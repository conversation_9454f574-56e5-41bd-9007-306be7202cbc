+-------------------------------+
|        User Interface         |
| Write, Brainstorm, plot, etc.  |
+---------------+---------------+
                |
                v
+---------------+---------------+
|        Application Logic      |
| Determines context & task     |
+---------------+---------------+
                |
                v
+---------------+---------------+
|        Model Management Layer |
+---------------+---------------+
|  +-------------------------+  |
|  |      Model Router       |  | <-- Decides which model to use
|  +-------------------------+  |
|  +-------------------------+  |
|  |   <PERSON><PERSON> Adapter Manager  |  | <-- Swaps task-specific LoRAs
|  +-------------------------+  |
+---------------+---------------+
                |
        +-------+--------+
        |                |
        v                v
+---------------+   +-----------------------+
|   Mistral 24B |   |  Lightweight Models   |
|  (Quantized)  |   | (Phi-3, TinyBERT, etc)|
+---------------+   +-----------------------+
| - Writing LoRA    | - NER / Extraction    |
| - Genre LoRA      | - Timeline Checks     |
| - Character LoRA  | - Embedding Gen       |
| - Plot LoRA       | - Classification      |
+-------------------+-----------------------+

        |                 |
        +--------+--------+
                 |
                 v
     +-----------+-----------+
     |       Data Layer       |
     +-----------+-----------+
     |  SQL DB (structured)   | <--- Characters, Chapters, Templates
     |  Vector DB (semantic)  | <--- Embeddings, Search, Clustering
     +------------------------+

                 |
                 v
      +----------+----------+
      |   External Services  |
      +----------+----------+
      | - TTS (Coqui, etc)   |
      | - Publishing Export  |
      +----------------------+


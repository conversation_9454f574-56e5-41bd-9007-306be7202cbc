
# 🧠 AI Models Used in the Application

This document outlines all AI models used in the authoring application, their specific roles, and the potential for data monetization.

---

## 📦 Models and Their Roles

| **Model**                        | **Used For**                                                                 |
|----------------------------------|------------------------------------------------------------------------------|
| **Mistral 24B (Quantized)**      | - Text generation (writing assistant)                                       |
|                                  | - Idea generation (brainstorming)                                           |
|                                  | - Dialogue generation (character voices)                                    |
|                                  | - Plot/arc suggestions                                                      |
| **LoRA Adapters (Mistral 24B)**  | - Style adaptation (author-specific)                                        |
|                                  | - Genre-specific generation                                                 |
|                                  | - Character voice tuning                                                    |
|                                  | - Plot structure alignment                                                  |
| **Instructor-XL or E5-base**     | - Embedding generation for world elements, characters, and scenes          |
| **TinyBERT or Flair**            | - Named Entity Recognition (characters, places, items)                      |
| **Sentence-BERT (MiniLM/MPNet)** | - Similarity matching (elements, characters, events)                        |
|                                  | - Relationship mapping                                                      |
| **Phi-2 or Phi-3-mini**          | - Classification (sentiment, story arc type, plot phase)                    |
|                                  | - Coherence checks for timeline and narrative flow                          |
| **Coqui XTTS (or XTTSv2)**       | - Text-to-speech synthesis of chapters                                      |
| **RVC (Retrieval Voice Conversion)** | - Voice personalization (convert narration to your own voice)              |

---

## 💰 Data for Monetization

To create value or sell data products (with user consent):

| **Data Type**                  | **Monetization Use**                                                   |
|--------------------------------|------------------------------------------------------------------------|
| Structured Worldbuilding Data  | Sell/export to game devs, RPG writers, interactive fiction platforms   |
| Character Arcs & Profiles      | Market as reusable narrative templates                                 |
| Embedded Semantic Chunks       | Power search/ranking tools, or license to LLM training services        |
| Narrative Patterns/Styles      | Use to train personalized writing assistants                           |
| User-Generated Text + Metadata | License anonymized corpora for fine-tuning genre-specific models       |

---

## 🧠 Coherence / Style Detection

- **Mistral 24B (with fine-tuned LoRA)** can be trained to **match and reproduce writing style** by:
  - Collecting text samples per user
  - Fine-tuning a LoRA for stylistic control
    LoRA Type	Data Required
      lora-style	Chapters written by the target author (yourself or others)
      lora-genre	Genre-specific prose from books/scripts
      lora-dialogue	Dialogues from specific character types
      lora-plotarc	Outlines, story arcs, structured summaries

- **Phi-2 / Phi-3-mini** can act as **structure/coherence checkers**, identifying:
  - Inconsistent tone
  - Broken arcs
  - Unresolved narrative threads

- **Sentence-BERT** can detect semantic drift across chapters, improving long-form consistency.

---

## ✅ Summary

You have a hybrid AI system composed of generative, analytic, and voice models. With proper tracking of user-generated content and embeddings, your app becomes a data-rich platform that can:
- Assist writers more deeply
- Export universes to other mediums (games, audiobooks)
- Feed anonymized data back into commercial model training pipelines (if users opt-in)


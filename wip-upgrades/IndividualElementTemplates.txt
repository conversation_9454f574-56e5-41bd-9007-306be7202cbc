Cosmology & Physical Environment

Star Template

  *   id: Unique identifier
  *   name: Name of the star
  *   type: Spectral classification (O, B, A, F, G, K, M, etc.)
  *   mass: Mass relative to solar mass
  *   radius: Size relative to solar radius
  *   temperature: Surface temperature in Kelvin
  *   luminosity: Brightness relative to solar luminosity
  *   age: Age in millions/billions of years
  *   color: Visible color
  *   habitable_zone: Distance range for potentially habitable planets
  *   lifecycle_stage: Main sequence, red giant, white dwarf, etc.
  *   galaxy_id: Parent galaxy
  *   star_system_id: Star system it belongs to (for binary/multiple systems)
  *   number_of_planets: How many planets orbit it
  *   stellar_phenomena: Flares, stellar winds, etc.
  *   discovery_date: When discovered (in-world)
  *   discovered_by: Who discovered it (in-world)
  *   cultural_significance: Importance in local cultures
  *   image_reference: Visual reference
  *   description: Detailed description

Planet Template

  *   id: Unique identifier
  *   name: Name of the planet
  *   parent_star_id: Star it orbits
  *   type: Terrestrial, gas giant, ice giant, etc.
  *   orbital_position: Order from star (1st, 2nd, etc.)
  *   mass: Mass measurement
  *   radius: Size measurement
  *   gravity: Surface gravity relative to Earth
  *   day_length: Rotation period
  *   year_length: Orbital period
  *   axial_tilt: Degree of tilt
  *   atmosphere_composition: Gases present
  *   surface_temperature_range: Min/max temperatures
  *   surface_water_percentage: Amount of water coverage
  *   habitable: Boolean (yes/no)
  *   life_present: Boolean (yes/no)
  *   notable_features: Rings, extreme weather, etc.
  *   number_of_moons: Quantity of satellites
  *   dominant_species_id: Main intelligent life form
  *   discovery_date: When discovered (in-world)
  *   colonization_status: Unexplored, outpost, colony, etc.
  *   controlling_faction_id: Who claims/controls it
  *   image_reference: Visual reference
  *   description: Detailed description

Moon Template

  *   id: Unique identifier
  *   name: Name of the moon
  *   parent_planet_id: Planet it orbits
  *   size: Diameter/radius
  *   mass: Mass measurement
  *   orbit_distance: Distance from planet
  *   orbit_period: Time to orbit planet once
  *   rotation_period: Time to rotate once
  *   atmosphere: Atmospheric composition if any
  *   surface_composition: What the surface is made of
  *   surface_features: Notable geography
  *   gravity: Surface gravity relative to Earth
  *   habitable: Boolean (yes/no)
  *   life_present: Boolean (yes/no)
  *   settlements: Any colonies or bases
  *   controlling_faction_id: Who claims/controls it
  *   resources: Valuable materials present
  *   cultural_significance: Importance in local cultures
  *   image_reference: Visual reference
  *   description: Detailed description

Asteroid/Comet Template

  *   id: Unique identifier
  *   name: Name or designation
  *   type: Asteroid, comet, dwarf planet, etc.
  *   location: Current position/orbit
  *   parent_star_id: Star system it belongs to
  *   size: Diameter/dimensions
  *   mass: Mass measurement
  *   composition: Material makeup
  *   orbit_type: Stable, eccentric, etc.
  *   orbital_period: Time to complete orbit
  *   rotation_period: Time to rotate once
  *   surface_features: Notable characteristics
  *   resources: Valuable materials present
  *   exploration_status: Unexplored, mapped, mined, etc.
  *   controlling_entity_id: Who claims/controls it
  *   hazard_rating: Threat to inhabited worlds
  *   cultural_significance: Role in local cultures/history
  *   image_reference: Visual reference
  *   description: Detailed description

Black Hole/Exotic Object Template

  *   id: Unique identifier
  *   name: Designation
  *   type: Black hole, neutron star, quasar, etc.
  *   mass: Mass measurement
  *   size: Event horizon/relevant dimension
  *   formation_date: When it formed
  *   formation_cause: How it formed
  *   galaxy_id: Parent galaxy
  *   surrounding_phenomena: Accretion disk, jets, etc.
  *   gravitational_effects: Spacetime distortions
  *   radiation_emissions: Types of radiation produced
  *   research_status: Level of scientific understanding
  *   research_bases: Observation posts/stations
  *   associated_anomalies: Strange phenomena
  *   navigational_hazard_level: Danger to spacecraft
  *   cultural_perception: How civilizations view it
  *   image_reference: Visual representation
  *   description: Detailed description

Nebula Template

  *   id: Unique identifier
  *   name: Name of the nebula
  *   type: Emission, reflection, dark, planetary, etc.
  *   size: Dimensions in light years
  *   location: Position in the galaxy
  *   composition: Chemical makeup
  *   age: How long it has existed
  *   origin: How it formed
  *   stellar_objects_within: Stars, systems inside it
  *   color_appearance: Visual characteristics
  *   radiation_levels: Types/intensity of radiation
  *   navigation_difficulty: How hard to traverse
  *   resource_value: Valuable materials present
  *   research_value: Scientific importance
  *   cultural_significance: Importance to civilizations
  *   exploration_status: How well mapped/explored
  *   associated_phenomena: Related cosmic events
  *   image_reference: Visual representation
  *   description: Detailed description

Galaxy Template

  *   id: Unique identifier
  *   name: Name of the galaxy
  *   type: Spiral, elliptical, irregular, etc.
  *   size: Diameter in light years
  *   estimated_stars: Number of stars
  *   estimated_planets: Estimated planetary bodies
  *   age: Age in billions of years
  *   location: Position in universe/cluster
  *   dominant_species_id: Most widespread intelligent life
  *   major_civilizations: Advanced cultures
  *   technological_level: Overall advancement level
  *   notable_features: Unique characteristics
  *   major_regions: Named areas/sectors
  *   travel_routes: Major hyperlanes/wormholes
  *   political_structure: Governance (united, fragmented)
  *   exploration_status: How much is known/mapped
  *   points_of_interest: Important locations
  *   image_reference: Visual representation
  *   description: Detailed description

Star System Template

  *   id: Unique identifier
  *   name: Name of the star system
  *   galaxy_id: Parent galaxy
  *   type: Single star, binary, trinary, etc.
  *   number_of_stars: Quantity of stars in the system
  *   primary_star_id: Main/brightest star
  *   companion_star_ids: Secondary stars (if applicable)
  *   age: Age in billions of years
  *   size: Approximate diameter of the system
  *   number_of_planets: Total planets in the system
  *   habitable_planets: Number of life-supporting worlds
  *   asteroid_belts: Number and positions
  *   notable_features: Unique characteristics
  *   stability: Orbital stability assessment
  *   location: Position within the galaxy
  *   dominant_species_id: Most prevalent intelligent life
  *   controlling_faction_id: Political authority
  *   exploration_status: Level of exploration/mapping
  *   strategic_importance: Military/economic value
  *   travel_routes: Connected hyperlanes/jump points
  *   discovery_date: When discovered (in-world)
  *   discovered_by: Who discovered it (in-world)
  *   cultural_significance: Importance in local cultures
  *   image_reference: Visual representation
  *   description: Detailed description

Continent Template

  *   id: Unique identifier
  *   name: Name of the continent
  *   planet_id: Planet it's located on
  *   size: Area measurement
  *   location: Position on planet
  *   climate_zones: Types of climates present
  *   biome_types: Major ecosystems
  *   dominant_terrain: Primary landscape features
  *   major_mountain_ranges: Significant mountains
  *   major_water_bodies: Significant lakes/rivers
  *   natural_resources: Important resources
  *   indigenous_species: Native life forms
  *   population: Number of inhabitants
  *   dominant_species_id: Main intelligent species
  *   major_nations: Political entities
  *   cultural_regions: Distinct cultural areas
  *   discovery_date: When first explored (in-world)
  *   development_level: Civilization advancement
  *   image_reference: Map/visual reference
  *   description: Detailed description

Ocean/Sea Template

  *   id: Unique identifier
  *   name: Name of the water body
  *   planet_id: Planet it's located on
  *   size: Surface area/volume
  *   location: Position on planet
  *   max_depth: Deepest point
  *   average_depth: Mean depth
  *   water_composition: Chemical makeup
  *   salinity: Salt content
  *   temperature_range: Min/max temperatures
  *   currents: Major water movements
  *   indigenous_life: Native species
  *   coastal_nations: Bordering countries
  *   ports: Major harbors
  *   resources: Valuable materials/food sources
  *   weather_patterns: Common meteorological events
  *   navigational_hazards: Dangers to ships
  *   cultural_significance: Importance to civilizations
  *   image_reference: Map/visual reference
  *   description: Detailed description

Mountain Range Template

  *   id: Unique identifier
  *   name: Name of the range
  *   planet_id: Planet it's located on
  *   continent_id: Continent it's located on
  *   location: Geographic position
  *   length: Total length
  *   average_height: Mean elevation
  *   highest_peak: Tallest mountain name & height
  *   formation_type: How it formed (volcanic, tectonic, etc.)
  *   age: How old the formation is
  *   climate: Weather conditions
  *   vegetation: Plant life
  *   wildlife: Animal species
  *   resources: Valuable materials
  *   passes: Major routes through
  *   settlements: Towns/cities in the range
  *   cultural_significance: Importance to local cultures
  *   current_status: Active/dormant (if volcanic)
  *   image_reference: Visual representation
  *   description: Detailed description

Forest/Jungle Template

  *   id: Unique identifier
  *   name: Name of the forest
  *   planet_id: Planet it's located on
  *   continent_id: Continent it's located on
  *   location: Geographic position
  *   size: Area coverage
  *   type: Rainforest, deciduous, coniferous, etc.
  *   age: How long it has existed
  *   climate: Weather conditions
  *   dominant_vegetation: Primary plant species
  *   canopy_density: How thick the tree cover is
  *   undergrowth_density: How thick the low vegetation is
  *   wildlife: Notable animal species
  *   rare_species: Uncommon/endangered life forms
  *   magical_properties: Any supernatural aspects
  *   resources: Valuable materials/herbs
  *   inhabitants: Intelligent beings living there
  *   settlements: Villages/outposts within
  *   dangers: Hazards to travelers
  *   cultural_significance: Importance to local peoples
  *   protected_status: Conservation measures
  *   image_reference: Visual representation
  *   description: Detailed description

Desert Template

  *   id: Unique identifier
  *   name: Name of the desert
  *   planet_id: Planet it's located on
  *   continent_id: Continent it's located on
  *   location: Geographic position
  *   size: Area coverage
  *   type: Sandy, rocky, ice, etc.
  *   climate: Temperature ranges and precipitation
  *   formation_cause: How it formed
  *   terrain_features: Dunes, mesas, etc.
  *   water_sources: Oases, underground aquifers
  *   vegetation: Plant life
  *   wildlife: Animal species
  *   resources: Valuable materials
  *   inhabitants: Intelligent beings living there
  *   settlements: Towns/outposts
  *   travel_routes: Paths through desert
  *   dangers: Hazards to travelers
  *   cultural_significance: Importance to local cultures
  *   image_reference: Visual representation
  *   description: Detailed description

River/Lake Template

  *   id: Unique identifier
  *   name: Name of the water body
  *   type: River, lake, etc.
  *   planet_id: Planet it's located on
  *   continent_id: Continent it's located on
  *   location: Geographic position
  *   size: Length/area/volume
  *   source: Origin point(s)
  *   destination: Where it flows to/drainage
  *   depth: Average and maximum depth
  *   flow_rate: Water movement speed (for rivers)
  *   water_composition: Chemical makeup
  *   indigenous_life: Aquatic species
  *   surrounding_landscape: Adjacent terrain
  *   settlements: Towns/cities along it
  *   uses: How it's utilized (transport, power, etc.)
  *   flooding_pattern: Seasonal changes
  *   bridges_crossings: Major crossing points
  *   cultural_significance: Importance to civilizations
  *   pollution_level: Environmental condition
  *   image_reference: Visual representation
  *   description: Detailed description

Plant Species Template

  *   id: Unique identifier
  *   name: Common name
  *   scientific_name: Taxonomic classification
  *   planet_id: Home planet
  *   habitats: Where it typically grows
  *   size: Height/spread dimensions
  *   appearance: Visual characteristics
  *   life_cycle: Growth and reproduction patterns
  *   seasonality: Annual changes/dormancy
  *   nutritional_value: Edibility for consumers
  *   medicinal_properties: Health effects
  *   toxicity: Harmful effects if any
  *   magical_properties: Supernatural aspects if any
  *   cultivation_status: Wild, domesticated, etc.
  *   ecological_role: Function in ecosystem
  *   cultural_uses: How societies use it
  *   rarity: How common/endangered
  *   related_species: Evolutionary relatives
  *   image_reference: Visual representation
  *   description: Detailed description

Animal Species Template

  *   id: Unique identifier
  *   name: Common name
  *   scientific_name: Taxonomic classification
  *   planet_id: Home planet
  *   habitats: Where it lives
  *   size: Dimensions and weight range
  *   appearance: Visual characteristics
  *   diet_type: Herbivore, carnivore, omnivore, etc.
  *   lifespan: Average life duration
  *   reproduction: Breeding methods
  *   social_structure: Solitary, herds, packs, etc.
  *   intelligence_level: Cognitive capabilities
  *   special_abilities: Unique traits/skills
  *   domestication_status: Wild, tamed, domesticated
  *   ecological_role: Function in ecosystem
  *   magical_properties: Supernatural aspects if any
  *   cultural_significance: Importance to civilizations
  *   conservation_status: Endangered, thriving, etc.
  *   image_reference: Visual representation
  *   description: Detailed description

Sapient Species Template

  *   id: Unique identifier
  *   name: Name of the species
  *   scientific_name: Taxonomic classification if applicable
  *   planet_of_origin_id: Homeworld
  *   current_habitats: Where they now live
  *   physical_traits: Appearance and biology
  *   lifespan: Average life duration
  *   reproduction: Biological reproduction method
  *   diet: Food requirements
  *   special_abilities: Unique traits/skills
  *   average_intelligence: Cognitive capabilities
  *   psychological_traits: Mental characteristics
  *   social_structure: How they organize groups
  *   family_structure: Kinship systems
  *   communication: Language and methods
  *   technology_level: Advancement level
  *   cultural_values: Core beliefs and priorities
  *   spiritual_beliefs: Religious tendencies
  *   relationship_with_other_species: Interspecies dynamics
  *   population: Total numbers
  *   major_settlements: Important communities
  *   historical_development: Key evolution points
  *   image_reference: Visual representation
  *   description: Detailed description

Disease Template

  *   id: Unique identifier
  *   name: Common name
  *   scientific_name: Medical classification
  *   type: Viral, bacterial, fungal, magical, etc.
  *   origin_location_id: Where it first appeared
  *   discovery_date: When identified
  *   transmission_vectors: How it spreads
  *   affected_species: Who can contract it
  *   symptoms: Effects on the infected
  *   incubation_period: Time before symptoms
  *   duration: How long it typically lasts
  *   mortality_rate: Death percentage
  *   treatment_options: Known cures/treatments
  *   preventative_measures: How to avoid it
  *   epidemic_history: Major outbreaks
  *   current_status: Active, contained, eradicated
  *   social_impact: Effect on societies
  *   research_status: Scientific understanding
  *   cultural_significance: How it's viewed
  *   image_reference: Visual representation
  *   description: Detailed description

Climate Zone/Weather Pattern Template

  *   id: Unique identifier
  *   name: Name of the climate zone/weather pattern
  *   planet_id: Planet where it occurs
  *   region_ids: Specific regions affected
  *   game_system_id: Associated rule system (for game mechanics)
  *   world_scale_id: Associated world scale system (for games)
  *   type: Climate classification (tropical, temperate, arctic, etc.)
  *   temperature_range: Average min/max temperatures
  *   precipitation_pattern: Rainfall/snowfall amounts and timing
  *   seasonal_variations: How it changes throughout the year
  *   wind_patterns: Prevailing winds and storm systems
  *   humidity_levels: Moisture content in the air
  *   cloud_coverage: Typical cloud formations
  *   extreme_weather_events: Storms, hurricanes, etc.
  *   causes: What creates this climate/weather (mountains, ocean currents, etc.)
  *   stability: How consistent the pattern is
  *   climate_change_factors: Elements causing shifts over time
  *   impact_on_biomes: How it shapes ecosystems
  *   impact_on_agriculture: Effects on food production
  *   impact_on_settlements: How it influences habitation
  *   cultural_adaptations: How local cultures have adjusted
  *   weather_cycle: How conditions change over time (for games)
  *   regional_variations: Different areas, different weather
  *   day_night_cycle: Light and darkness patterns
  *   visual_effects: How weather appears
  *   audio_effects: Weather sounds
  *   gameplay_impacts: Mechanical effects of weather
  *   player_protection: Ways to mitigate weather
  *   npc_reactions: How characters respond to conditions
  *   forecast_system: Predicting upcoming weather
  *   weather_manipulation: Player control of conditions
  *   environmental_hazards: Dangerous conditions
  *   image_reference: Visual representation
  *   description: Detailed description

Weather Event Template

  *   id: Unique identifier
  *   name: Name of the weather event
  *   climate_zone_id: Parent climate zone/pattern
  *   planet_id: Planet where it occurs
  *   region_ids: Specific areas affected
  *   type: Storm, hurricane, drought, blizzard, etc.
  *   classification: Severity category or rating
  *   duration: How long it typically lasts
  *   frequency: How often it occurs
  *   seasonality: Time of year when it happens
  *   trigger_conditions: What causes it to form
  *   warning_signs: Preceding indicators
  *   physical_characteristics: Wind speed, precipitation, etc.
  *   visual_appearance: How it looks
  *   auditory_elements: Sounds produced
  *   progression_stages: How it develops over time
  *   dissipation_factors: What causes it to end
  *   prediction_accuracy: How well it can be forecast
  *   impact_on_environment: Ecological effects
  *   impact_on_infrastructure: Damage to buildings/roads
  *   impact_on_agriculture: Crop effects
  *   danger_level: Threat to life and safety
  *   historical_notable_occurrences: Famous instances
  *   cultural_significance: How societies view it
  *   adaptation_measures: How people prepare/respond
  *   game_mechanics: How it affects gameplay
  *   player_interaction: How characters can respond
  *   image_reference: Visual representation
  *   description: Detailed description

Tectonic Activity Template

  *   id: Unique identifier
  *   name: Name of the tectonic feature/system
  *   planet_id: Planet where it occurs
  *   type: Plate boundary type (convergent, divergent, transform)
  *   plates_involved: Names of tectonic plates
  *   movement_rate: Speed of plate movement
  *   age: How long it has been active
  *   associated_mountain_ranges_ids: Mountains formed by this activity
  *   associated_volcanic_regions: Volcanic areas created
  *   earthquake_frequency: How often tremors occur
  *   earthquake_intensity: Typical magnitude range
  *   tsunami_risk: Potential for generating tsunamis
  *   affected_regions_ids: Areas impacted by the activity
  *   stability: Current state and predictability
  *   monitoring_systems: How it's observed/measured
  *   historical_major_events: Significant past occurrences
  *   cultural_impact: How it has shaped local societies
  *   scientific_understanding: Level of knowledge about it
  *   image_reference: Visual representation
  *   description: Detailed description

Ecological Relationship Template

  *   id: Unique identifier
  *   name: Name of the relationship
  *   ecosystem_id: Ecosystem where it occurs
  *   planet_id: Planet where it occurs
  *   type: Predator-prey, symbiotic, parasitic, etc.
  *   species_involved_ids: Species participating in the relationship
  *   relationship_dynamics: How the interaction functions
  *   benefit_distribution: Who benefits and how
  *   dependency_level: How critical the relationship is
  *   seasonal_variations: Changes throughout the year
  *   evolutionary_history: How the relationship developed
  *   stability: How resilient the relationship is
  *   disruption_factors: What threatens the relationship
  *   ecological_importance: Role in the broader ecosystem
  *   cascade_effects: What happens if relationship breaks
  *   human/sapient_impact: How intelligent species affect it
  *   research_status: Scientific understanding level
  *   cultural_significance: How local cultures view it
  *   image_reference: Visual representation
  *   description: Detailed description

Species Template

  *   id: Unique identifier
  *   name: Scientific/common name
  *   ecological_relationship_id: Parent ecological relationship
  *   type: Plant, animal, fungus, etc.
  *   taxonomy: Classification hierarchy
  *   habitat: Where it lives
  *   geographic_distribution: Range map
  *   physical_characteristics: Appearance and structure
  *   life_cycle: Development stages
  *   reproduction_method: How it breeds
  *   diet: What it consumes
  *   predators: What hunts it
  *   ecological_role: Function in ecosystem
  *   adaptations: Survival features
  *   behavior_patterns: Typical actions
  *   social_structure: Group organization
  *   communication_methods: How it interacts
  *   intelligence_level: Cognitive capacity
  *   conservation_status: Endangerment level
  *   human_relationship: Interaction with people
  *   cultural_significance: Symbolic importance
  *   evolutionary_history: Development over time
  *   image_reference: Visual representation
  *   description: Detailed description

Evolutionary History Template

  *   id: Unique identifier
  *   name: Name of the evolutionary lineage/event
  *   planet_id: Planet where it occurred
  *   species_ids: Species involved
  *   timeframe: When it took place
  *   ancestor_species: Original life forms
  *   descendant_species: Resulting life forms
  *   evolutionary_mechanism: Natural selection, genetic drift, etc.
  *   environmental_pressures: Factors driving evolution
  *   key_adaptations: Important biological changes
  *   speciation_events: When new species formed
  *   extinction_events: When species died out
  *   rate_of_change: Speed of evolutionary process
  *   convergent_evolution: Similar traits in unrelated species
  *   divergent_evolution: Different traits from common ancestors
  *   current_status: Ongoing or completed
  *   fossil_evidence: Physical proof available
  *   genetic_evidence: DNA/molecular support
  *   scientific_understanding: Level of knowledge
  *   cultural_interpretation: How societies view it
  *   image_reference: Visual representation
  *   description: Detailed description

Cultural & Social Systems

Nation/State Template

  *   id: Unique identifier
  *   name: Official name
  *   type: Kingdom, republic, empire, etc.
  *   founding_date: When established
  *   location_ids: Territories controlled
  *   capital_city_id: Seat of government
  *   government_type: Political system
  *   head_of_state_title: Leader designation
  *   current_ruler_id: Current leader
  *   population: Number of citizens
  *   dominant_species_id: Primary species
  *   official_languages: Primary languages
  *   official_religion_id: State faith if any
  *   currency_id: Official money
  *   economic_system: Economic model
  *   technological_level: Advancement stage
  *   military_strength: Armed forces capability
  *   allies_ids: Friendly nations
  *   enemies_ids: Hostile nations
  *   major_exports: Key trade goods
  *   major_imports: Key needed goods
  *   social_stratification: Class system
  *   cultural_values: Core societal principles
  *   flag_description: National flag details
  *   anthem_description: National anthem
  *   image_reference: Visual representation
  *   description: Detailed description

Province/Region Template

  *   id: Unique identifier
  *   name: Official name of the province/region
  *   nation_id: Parent nation/state
  *   type: State, province, prefecture, duchy, etc.
  *   founding_date: When established
  *   location_coordinates: Geographic position
  *   area: Physical size
  *   capital_city_id: Regional administrative center
  *   governance_structure: Local government system
  *   governor_title: Regional leader designation
  *   current_governor_id: Current regional leader
  *   autonomy_level: Degree of self-governance
  *   population: Number of inhabitants
  *   dominant_species_id: Primary species
  *   dominant_culture_id: Primary cultural group
  *   regional_languages: Local dialects/languages
  *   economic_focus: Primary industries
  *   resource_wealth: Natural resources
  *   development_level: Infrastructure quality
  *   strategic_importance: Military/political value
  *   regional_conflicts: Local tensions
  *   relationship_with_central_government: Loyalty/independence
  *   neighboring_provinces_ids: Adjacent regions
  *   distinctive_features: Unique characteristics
  *   regional_symbols: Flags, emblems, etc.
  *   historical_significance: Important past events
  *   image_reference: Visual representation
  *   description: Detailed description

City/Settlement Template

  *   id: Unique identifier
  *   name: Name of the settlement
  *   type: City, town, village, outpost, etc.
  *   nation_id: Country it belongs to
  *   planet_id: Planet it's located on
  *   location_coordinates: Precise position
  *   founding_date: When established
  *   founder_id: Who established it
  *   population: Number of inhabitants
  *   dominant_species_id: Primary inhabitants
  *   size: Physical dimensions/area
  *   layout: Urban planning style
  *   architectural_style: Building design
  *   governance_structure: Local government
  *   economic_base: Main industries
  *   notable_districts: Important areas
  *   landmarks: Famous locations
  *   defenses: Walls, shields, etc.
  *   infrastructure_quality: Development level
  *   services: Available amenities
  *   culture_id: Local cultural identity
  *   image_reference: Visual representation
  *   description: Detailed description

Tribe/Clan Template

  *   id: Unique identifier
  *   name: Name of the tribe/clan
  *   species_id: Primary species
  *   territory_ids: Areas controlled/inhabited
  *   population: Number of members
  *   formation_date: When established
  *   origin_story: How they formed
  *   leadership_structure: How leaders are chosen
  *   current_leader_id: Present chief/elder
  *   social_organization: How society is structured
  *   family_structure: Kinship system
  *   cultural_practices: Traditions and customs
  *   religious_beliefs: Spiritual systems
  *   livelihood: How they survive/economic activity
  *   technology_level: Technological advancement
  *   dwellings: Housing types
  *   distinctive_traits: What makes them unique
  *   allies_ids: Friendly groups
  *   enemies_ids: Hostile groups
  *   relationship_with_nations: How they interact with states
  *   image_reference: Visual representation
  *   description: Detailed description

Social Class Template

  *   id: Unique identifier
  *   name: Name of the class
  *   society_ids: Which societies have this class
  *   position: Hierarchical standing (high, middle, low)
  *   formation_origin: How this class developed
  *   entry_methods: How one joins this class
  *   exit_methods: How one leaves this class
  *   mobility_level: Ease of changing class
  *   population_percentage: What portion of society
  *   economic_standing: Wealth/resources
  *   political_power: Influence level
  *   legal_rights: Special privileges/restrictions
  *   obligations: Expected duties
  *   occupations: Common professions
  *   education_access: Learning opportunities
  *   living_conditions: Quality of life
  *   cultural_markers: Identifying traits
  *   view_of_other_classes: Inter-class relations
  *   historical_changes: How it evolved
  *   image_reference: Visual representation
  *   description: Detailed description

Family Structure Template

  *   id: Unique identifier
  *   name: Name of the structure
  *   culture_ids: Cultures practicing this
  *   species_ids: Species practicing this
  *   composition: Who constitutes family
  *   hierarchy: Power/authority structure
  *   lineage_tracking: Matrilineal, patrilineal, etc.
  *   marriage_customs: Union practices
  *   parenting_approach: Child-rearing methods
  *   inheritance_system: How property passes on
  *   gender_roles: Sex-based expectations
  *   elder_care: Treatment of aged members
  *   typical_size: Average family members
  *   home_arrangement: Living situation
  *   decision_making: How choices are made
  *   conflict_resolution: Handling disagreements
  *   rituals_traditions: Family practices
  *   naming_conventions: How names are given
  *   obligations: Member responsibilities
  *   image_reference: Visual representation
  *   description: Detailed description

Government Type Template

  *   id: Unique identifier
  *   name: Name of the system
  *   category: Democracy, autocracy, oligarchy, etc.
  *   power_source: Where authority derives from
  *   leadership_structure: How leaders operate
  *   leadership_selection: How leaders are chosen
  *   term_limits: Duration of leadership
  *   power_distribution: Centralized vs. distributed
  *   legislative_process: How laws are made
  *   judicial_system: How justice is administered
  *   citizen_participation: Public involvement level
  *   rights_guaranteed: Protected freedoms
  *   taxation_approach: Resource collection
  *   corruption_level: Integrity of operations
  *   stability: How resistant to change
  *   societies_using: Where this is practiced
  *   historical_examples: Notable implementations
  *   strengths: Advantages of this system
  *   weaknesses: Problems with this system
  *   image_reference: Visual representation
  *   description: Detailed description

Government Position Template

  *   id: Unique identifier
  *   name: Title of the position
  *   government_type_id: Parent government system
  *   nation_id: Country where position exists
  *   branch: Executive, legislative, judicial, etc.
  *   level: National, regional, local, etc.
  *   classification: Elected, appointed, hereditary, etc.
  *   creation_date: When established
  *   authority_scope: Areas of control
  *   responsibilities: Official duties
  *   powers: Actions they can take
  *   limitations: Restrictions on power
  *   selection_process: How position is filled
  *   eligibility_requirements: Who can hold position
  *   term_length: Duration of service
  *   term_limits: Maximum service time
  *   removal_process: How incumbents are dismissed
  *   succession_procedure: Replacement method
  *   salary_benefits: Compensation
  *   staff_resources: Support personnel
  *   current_holder_id: Present occupant
  *   notable_former_holders: Famous past occupants
  *   cultural_significance: Symbolic importance
  *   historical_evolution: How role has changed
  *   image_reference: Visual representation
  *   description: Detailed description

Military Organization Template

  *   id: Unique identifier
  *   name: Name of the organization
  *   nation_id: Country it belongs to
  *   type: Army, navy, air force, space fleet, etc.
  *   founding_date: When established
  *   size: Number of personnel
  *   headquarters_location_id: Command center
  *   command_structure: Leadership hierarchy
  *   current_commander_id: Present leader
  *   recruitment_method: How members join
  *   training_approach: How soldiers are prepared
  *   equipment_quality: Arms and armor level
  *   technological_level: Advanced weaponry
  *   special_units: Elite forces
  *   doctrine: Strategic philosophy
  *   current_deployment: Active operations
  *   notable_victories: Famous successes
  *   notable_defeats: Significant losses
  *   current_threats: Active enemies
  *   morale_state: Troop satisfaction
  *   public_perception: How citizens view it
  *   image_reference: Visual representation
  *   description: Detailed description

Military Unit Template

  *   id: Unique identifier
  *   name: Name of the unit
  *   military_organization_id: Parent military organization
  *   type: Infantry, cavalry, artillery, naval, air, etc.
  *   classification: Regular, elite, reserve, etc.
  *   size_category: Squad, platoon, company, battalion, etc.
  *   personnel_count: Number of members
  *   formation_date: When established
  *   current_location_id: Where stationed
  *   commanding_officer_id: Current leader
  *   command_structure: Internal hierarchy
  *   recruitment_criteria: Selection standards
  *   training_regimen: Preparation methods
  *   specialization: Unique capabilities
  *   standard_equipment: Weapons and gear
  *   distinctive_insignia: Identifying symbols
  *   battle_honors: Recognized achievements
  *   operational_history: Past deployments
  *   current_mission: Active assignment
  *   combat_effectiveness: Fighting capability
  *   casualties_sustained: Losses suffered
  *   reputation: How they're perceived
  *   traditions: Unit customs
  *   notable_members_ids: Famous personnel
  *   image_reference: Visual representation
  *   description: Detailed description

Language Template

  *   id: Unique identifier
  *   name: Name of the language
  *   language_family: Related languages
  *   origin_date: When it developed
  *   origin_location_id: Where it developed
  *   speakers_population: How many use it
  *   official_status: Where it's recognized
  *   script: Writing system
  *   phonology: Sound patterns
  *   grammar_structure: Syntactic framework
  *   vocabulary_size: Word count
  *   notable_features: Unique characteristics
  *   dialects: Regional variations
  *   loan_words: Borrowed terminology
  *   register_variations: Formal vs. informal
  *   preservation_status: Endangered vs. thriving
  *   cultural_importance: Significance to users
  *   common_expressions: Typical phrases
  *   sample_text: Example writing
  *   game_system_id: Associated rule system (for game mechanics)
  *   npc_system_id: Associated NPC interaction system
  *   dialogue_structure: Tree, hub-and-spoke, etc. (for games)
  *   conversation_initiation: How dialogues begin
  *   player_response_types: Categories of replies available
  *   response_time_limits: Timed dialogue options
  *   dialogue_history: How past conversations are tracked
  *   branching_factors: What causes conversation paths
  *   dialogue_flags: Conversation state tracking
  *   tone_options: Emotional approaches available
  *   dialogue_skills: Character abilities affecting conversations
  *   voice_acting_integration: Audio implementation
  *   text_display_method: How dialogue appears on screen
  *   conversation_animations: Character movements during dialogue
  *   dialogue_camera: View changes during conversations
  *   interruption_handling: Breaking conversation flow
  *   translation_support: Localization considerations
  *   image_reference: Script sample
  *   description: Detailed description

Dialect Template

  *   id: Unique identifier
  *   name: Name of the dialect
  *   language_id: Parent language
  *   region_id: Geographic area where spoken
  *   speakers_population: Number of users
  *   origin_date: When it diverged from parent language
  *   origin_cause: Why it developed differently
  *   phonological_differences: Sound pattern variations
  *   grammatical_differences: Syntax variations
  *   vocabulary_differences: Unique or altered words
  *   mutual_intelligibility: How easily understood by standard language speakers
  *   social_status: Prestige or stigma
  *   official_recognition: Legal or institutional status
  *   cultural_associations: Identity connections
  *   media_presence: Use in entertainment/news
  *   literary_tradition: Written works in this dialect
  *   preservation_efforts: Maintenance activities
  *   modernization: Adaptation to new concepts
  *   influence_on_standard: Effects on parent language
  *   distinctive_expressions: Characteristic phrases
  *   sample_text: Example of dialect writing
  *   image_reference: Visual representation
  *   description: Detailed description

Art Form Template

  *   id: Unique identifier
  *   name: Name of the art form
  *   type: Visual, performance, literary, etc.
  *   origin_culture_id: Where it developed
  *   origin_date: When it emerged
  *   major_practitioners: Famous artists
  *   materials_used: Physical components
  *   techniques: Methods and skills
  *   themes: Common subjects
  *   cultural_significance: Importance to society
  *   audience: Who appreciates it
  *   venues: Where it's performed/displayed
  *   education_method: How it's taught
  *   evolution: How it changed over time
  *   sub_styles: Variations and schools
  *   associated_movements: Related art trends
  *   commercial_value: Economic worth
  *   patronage: Who funds/supports it
  *   critical_reception: How it's evaluated
  *   image_reference: Visual example
  *   description: Detailed description

Music Style Template

  *   id: Unique identifier
  *   name: Name of the style
  *   origin_culture_id: Where it developed
  *   origin_date: When it emerged
  *   instruments: Tools used
  *   vocal_characteristics: Singing style
  *   rhythm_patterns: Beat structures
  *   tonal_system: Scale/mode used
  *   typical_structure: Song organization
  *   lyrical_themes: Common subjects
  *   performance_context: Where/when performed
  *   cultural_significance: Role in society
  *   notable_composers: Famous creators
  *   notable_works: Famous pieces
  *   evolution: How it changed over time
  *   influenced_by: Predecessor styles
  *   influenced: Successor styles
  *   audience: Who listens to it
  *   modern_relevance: Current popularity
  *   audio_reference: Sound example
  *   description: Detailed description

Festival/Holiday Template

  *   id: Unique identifier
  *   name: Name of the celebration
  *   culture_ids: Who observes it
  *   date_or_timing: When it occurs
  *   duration: How long it lasts
  *   origin_date: When first celebrated
  *   historical_origin: How it began
  *   purpose: What it commemorates
  *   religious_significance: Spiritual meaning
  *   activities: Traditional practices
  *   foods: Special cuisine
  *   decorations: Visual elements
  *   music: Traditional songs
  *   attire: Special clothing
  *   rituals: Ceremonial acts
  *   gifts: Exchange traditions
  *   public_vs_private: Community or family
  *   evolution: How it changed over time
  *   modern_observance: Current practices
  *   image_reference: Visual representation
  *   description: Detailed description

Cuisine Template

  *   id: Unique identifier
  *   name: Name of the cuisine
  *   culture_ids: Associated cultures
  *   region_ids: Geographic areas
  *   staple_ingredients: Basic components
  *   flavor_profile: Taste characteristics
  *   cooking_methods: Preparation techniques
  *   meal_structure: Course organization
  *   special_dishes: Famous recipes
  *   dining_etiquette: Eating customs
  *   utensils: Tools for eating
  *   ceremonial_importance: Role in rituals
  *   foreign_influences: External inspirations
  *   adaptations: Regional variations
  *   health_aspects: Nutritional profile
  *   preservation_methods: Food storage
  *   beverages: Traditional drinks
  *   modern_evolution: Contemporary changes
  *   global_popularity: Widespread appeal
  *   image_reference: Visual representation
  *   description: Detailed description

Clothing Style Template

  *   id: Unique identifier
  *   name: Name of the style
  *   culture_ids: Who wears it
  *   era_id: Historical period
  *   climate_adaptation: Weather suitability
  *   materials: Fabrics/components
  *   construction_techniques: How it's made
  *   silhouette: General shape
  *   color_palette: Typical colors
  *   patterns_motifs: Decorative elements
  *   purpose: Function (daily, ceremonial, etc.)
  *   gender_association: Who typically wears it
  *   status_signifiers: Class indicators
  *   accessories: Additional items
  *   evolution: Changes over time
  *   modern_adaptations: Contemporary versions
  *   cultural_significance: Social importance
  *   taboos: Inappropriate usage
  *   foreign_influences: External inspirations
  *   image_reference: Visual representation
  *   description: Detailed description

Religion Template

  *   id: Unique identifier
  *   name: Name of the religion
  *   type: Monotheistic, polytheistic, animistic, etc.
  *   founding_date: When established
  *   founder_id: Who established it
  *   origin_location_id: Where it began
  *   holy_text_ids: Sacred writings
  *   cosmology: View of universe structure
  *   deities: Gods/higher powers
  *   afterlife_concept: Post-death beliefs
  *   moral_code: Ethical framework
  *   core_tenets: Fundamental beliefs
  *   rituals: Sacred practices
  *   prayer_formats: Communication with divine
  *   clergy_structure: Religious leaders
  *   worship_locations: Temples, churches, etc.
  *   holidays_id: Sacred days
  *   symbols: Visual representations
  *   sects: Major denominations
  *   population: Number of followers
  *   geographical_distribution: Where practiced
  *   historical_events: Key moments
  *   image_reference: Visual representation
  *   description: Detailed description

Religious Text Template

  *   id: Unique identifier
  *   name: Title of the text
  *   religion_id: Parent religion
  *   type: Scripture, commentary, prophecy, hymnal, etc.
  *   creation_date: When written/compiled
  *   author_ids: Writers/compilers
  *   origin_location_id: Where created
  *   original_language: First written language
  *   current_form_date: When finalized
  *   canonicity_status: Official standing in religion
  *   physical_form: Scroll, codex, oral tradition, etc.
  *   structure: Organization of content
  *   major_sections: Primary divisions
  *   key_narratives: Important stories
  *   theological_concepts: Core religious ideas
  *   moral_teachings: Ethical guidance
  *   ritual_instructions: Ceremonial directions
  *   prophecies: Future predictions
  *   interpretation_methods: How meaning is derived
  *   major_translations: Important versions
  *   scholarly_traditions: Study approaches
  *   liturgical_use: Role in worship
  *   cultural_influence: Impact beyond religion
  *   restricted_status: Access limitations
  *   preservation_methods: How maintained
  *   image_reference: Visual representation
  *   description: Detailed description

Deity Template

  *   id: Unique identifier
  *   name: Name of the deity
  *   religion_id: Associated faith
  *   type: Creator, nature, war, etc.
  *   rank: Position in pantheon
  *   domain: Areas of influence
  *   appearance: How depicted
  *   personality: Character traits
  *   sacred_symbols: Associated images
  *   sacred_animals: Associated creatures
  *   origin_story: How they came to be
  *   major_myths: Stories involving them
  *   worship_methods: How followers venerate
  *   clergy_title: Priests' designation
  *   temples: Places of worship
  *   holy_days: Celebrations
  *   offerings: Sacrifices/gifts
  *   miracles: Powers attributed
  *   relationships: Connections to other deities
  *   image_reference: Visual representation
  *   description: Detailed description

Philosophy Template

  *   id: Unique identifier
  *   name: Name of the philosophy
  *   founder_id: Who established it
  *   founding_date: When established
  *   origin_location_id: Where it began
  *   core_principles: Fundamental ideas
  *   epistemology: Theory of knowledge
  *   metaphysics: Nature of reality
  *   ethics: Moral framework
  *   political_views: Social organization ideas
  *   view_of_human_nature: Conception of people
  *   key_texts: Important writings
  *   methodology: Approach to inquiry
  *   schools: Major variations
  *   notable_adherents: Famous followers
  *   influence_on_society: Cultural impact
  *   conflicts_with: Opposing philosophies
  *   modern_relevance: Current significance
  *   criticisms: Major objections
  *   game_system_id: Associated rule system (for game mechanics)
  *   choice_system_id: Associated player choice system
  *   alignment_axes: Moral dimensions tracked (for games)
  *   measurement_scale: How alignment is quantified
  *   action_values: Moral weight of different choices
  *   visibility_to_player: How alignment is displayed
  *   alignment_shifts: How quickly values change
  *   alignment_decay: Return to neutral over time
  *   alignment_thresholds: Points where effects trigger
  *   gameplay_effects: Mechanical impacts of alignment
  *   npc_reactions: How characters respond to alignment
  *   alignment_requirements: Content gated by morality
  *   redemption_mechanics: Changing alignment intentionally
  *   alignment_abilities: Powers based on moral standing
  *   conflicting_values: Handling moral dilemmas
  *   cultural_relativity: Different standards in different regions
  *   image_reference: Visual representation
  *   description: Detailed description

Gender Role/Sexual Norm Template

  *   id: Unique identifier
  *   name: Name of the gender role/sexual norm system
  *   culture_ids: Cultures practicing this system
  *   species_ids: Species practicing this system
  *   gender_categories: How genders are defined/classified
  *   gender_expression: How gender is displayed/performed
  *   gender_assignment: How gender is determined/assigned
  *   gender_fluidity: Flexibility in gender identity
  *   power_dynamics: Authority distribution by gender
  *   occupational_divisions: Work roles by gender
  *   family_role_divisions: Domestic roles by gender
  *   sexual_orientation_norms: Accepted sexual preferences
  *   partnership_structures: Marriage/relationship forms
  *   reproductive_expectations: Childbearing norms
  *   coming_of_age_rituals: Gender-related ceremonies
  *   religious_aspects: Spiritual dimensions of gender
  *   legal_status: Rights and restrictions by gender
  *   historical_development: How norms evolved
  *   resistance_movements: Challenges to the system
  *   image_reference: Visual representation
  *   description: Detailed description

Age Group/Generation Template

  *   id: Unique identifier
  *   name: Name of the age group/generation
  *   culture_ids: Cultures recognizing this group
  *   species_ids: Species this applies to
  *   age_range: Typical years included
  *   defining_events: Historical moments that shaped them
  *   social_status: Standing in society
  *   rights_and_privileges: Legal entitlements
  *   responsibilities: Expected duties
  *   typical_roles: Common occupations/positions
  *   cultural_markers: Identifying traits/styles
  *   coming_of_age_rituals: Transition ceremonies
  *   relationship_with_other_age_groups: Intergenerational dynamics
  *   educational_expectations: Learning requirements
  *   economic_position: Financial situation
  *   political_influence: Power in governance
  *   cultural_contributions: Art, technology, ideas produced
  *   generational_conflicts: Tensions with other groups
  *   historical_development: How the concept evolved
  *   image_reference: Visual representation
  *   description: Detailed description

Social Mobility Mechanism Template

  *   id: Unique identifier
  *   name: Name of the mechanism
  *   society_ids: Societies where it operates
  *   type: Education, marriage, wealth, merit, etc.
  *   direction: Upward, downward, horizontal
  *   accessibility: Who can use this mechanism
  *   requirements: What's needed to utilize it
  *   success_rate: How often it works
  *   timeframe: How long the process takes
  *   historical_development: How it evolved
  *   institutional_support: Organizations that facilitate it
  *   cultural_attitudes: How it's viewed by society
  *   resistance_factors: What prevents mobility
  *   case_studies: Notable examples
  *   impact_on_social_stability: Effects on social order
  *   impact_on_economy: Economic consequences
  *   relationship_to_political_system: How governance affects it
  *   current_trends: Recent changes
  *   image_reference: Visual representation
  *   description: Detailed description

Legal Framework/Justice System Template

  *   id: Unique identifier
  *   name: Name of the legal system
  *   society_ids: Societies using this system
  *   type: Common law, civil law, religious law, etc.
  *   founding_principles: Core legal concepts
  *   historical_development: How it evolved
  *   legal_code_structure: Organization of laws
  *   enforcement_mechanisms: How laws are upheld
  *   court_hierarchy: Levels of judicial authority
  *   judicial_selection: How judges are chosen
  *   trial_procedures: How cases are decided
  *   punishment_philosophy: Approach to sentencing
  *   rehabilitation_approach: Reform methods
  *   rights_of_accused: Protections for defendants
  *   legal_representation: Attorney/advocate system
  *   appeal_process: Challenging verdicts
  *   relationship_to_government: Judicial independence
  *   corruption_level: System integrity
  *   reform_movements: Efforts to change the system
  *   cultural_perception: How society views it
  *   image_reference: Visual representation
  *   description: Detailed description

Law Element Template

  *   id: Unique identifier
  *   name: Name or title of the law
  *   legal_framework_id: Parent legal system
  *   type: Criminal, civil, constitutional, administrative, etc.
  *   enactment_date: When it became law
  *   enacting_authority_id: Who created/passed it
  *   jurisdiction: Geographic/social scope of application
  *   purpose: Intended goal or problem addressed
  *   text: Actual wording or summary of provisions
  *   enforcement_agency_id: Who upholds this law
  *   penalties: Consequences for violation
  *   exceptions: Situations where it doesn't apply
  *   precedent_cases: Important legal decisions related to it
  *   amendments: Changes made since enactment
  *   public_compliance: How widely followed
  *   controversy_level: How disputed it is
  *   cultural_impact: Effect on society
  *   historical_context: Events leading to its creation
  *   related_laws_ids: Connected legislation
  *   interpretation_changes: How understanding has evolved
  *   current_status: Active, repealed, superseded, etc.
  *   image_reference: Visual representation
  *   description: Detailed description

Diplomatic Relation Template

  *   id: Unique identifier
  *   name: Name of the diplomatic relationship
  *   entities_involved_ids: Nations/groups in the relationship
  *   game_system_id: Associated rule system (for game mechanics)
  *   type: Alliance, non-aggression pact, trade agreement, etc.
  *   formation_date: When established
  *   historical_context: Events leading to formation
  *   formal_agreements: Treaties and accords
  *   diplomatic_channels: Communication methods
  *   embassy_presence: Diplomatic missions
  *   trade_relations: Economic exchanges
  *   military_cooperation: Defense arrangements
  *   cultural_exchanges: Shared arts, education, etc.
  *   public_perception: How citizens view the relationship
  *   points_of_tension: Areas of disagreement
  *   recent_developments: Latest changes
  *   strategic_importance: Value to involved parties
  *   third_party_influences: External factors
  *   future_outlook: Projected developments
  *   faction_count: Number of tracked groups (for games)
  *   reputation_scale: Measurement system (numeric, tier-based)
  *   reputation_thresholds: Points where status changes
  *   reputation_gain_actions: How to improve standing
  *   reputation_loss_actions: How standing decreases
  *   faction_relationships: How factions view each other
  *   reputation_benefits: Rewards for high standing
  *   reputation_penalties: Consequences of low standing
  *   reputation_decay: Changes over time without action
  *   hidden_factors: Unknown influence elements
  *   reputation_quests: Special tasks to change standing
  *   reputation_items: Objects that affect standing
  *   reputation_display: How players see their standing
  *   mutually_exclusive: Whether some factions conflict
  *   reputation_recovery: How to rebuild after loss
  *   image_reference: Visual representation
  *   description: Detailed description

Faction System Template

  *   id: Unique identifier
  *   name: Name of the faction system
  *   game_system_id: Associated rule system
  *   world_id: Associated world/setting
  *   faction_count: Number of tracked groups
  *   faction_types: Categories of factions (political, religious, criminal, etc.)
  *   reputation_scale: Measurement system (numeric, tier-based)
  *   reputation_thresholds: Points where status changes
  *   reputation_gain_actions: How to improve standing
  *   reputation_loss_actions: How standing decreases
  *   faction_relationships: How factions view each other
  *   reputation_benefits: Rewards for high standing
  *   reputation_penalties: Consequences of low standing
  *   reputation_decay: Changes over time without action
  *   hidden_factors: Unknown influence elements
  *   reputation_quests: Special tasks to change standing
  *   reputation_items: Objects that affect standing
  *   reputation_display: How players see their standing
  *   mutually_exclusive: Whether some factions conflict
  *   reputation_recovery: How to rebuild after loss
  *   faction_visibility: How factions are revealed to players
  *   faction_evolution: How factions change over time
  *   faction_conflicts: How inter-faction disputes are handled
  *   faction_alliances: How cooperation between factions works
  *   faction_territory: How physical control is represented
  *   faction_resources: What assets factions control
  *   faction_leadership: How faction hierarchy is structured
  *   image_reference: Visual representation
  *   description: Detailed description

Faction Rank Template

  *   id: Unique identifier
  *   name: Name of the rank
  *   faction_system_id: Parent faction system
  *   faction_id: Specific faction this rank belongs to
  *   rank_level: Numerical position in hierarchy
  *   title: Official designation
  *   requirements: What's needed to attain this rank
  *   reputation_threshold: Standing needed with faction
  *   authority_scope: What the rank can control
  *   responsibilities: Expected duties
  *   privileges: Special rights and access
  *   symbols_of_rank: Visual/physical indicators
  *   quarters: Where rank members reside/work
  *   salary_benefits: Compensation provided
  *   equipment_provided: Items given to rank holders
  *   command_structure: Who reports to this rank
  *   reports_to: Higher rank in chain of command
  *   promotion_path: Next rank in progression
  *   demotion_conditions: What causes loss of rank
  *   expulsion_conditions: What causes removal from faction
  *   typical_npcs: Example characters at this rank
  *   player_accessibility: Whether players can attain this rank
  *   gameplay_impact: How rank affects mechanics
  *   narrative_significance: Role in storylines
  *   image_reference: Visual representation
  *   description: Detailed description

Colonial Relationship Template

  *   id: Unique identifier
  *   name: Name of the colonial relationship
  *   colonizer_id: Controlling entity
  *   colonized_territory_ids: Controlled territories
  *   type: Settlement, extraction, trading post, etc.
  *   establishment_date: When colonization began
  *   motivation: Reasons for colonization
  *   administrative_structure: Governance system
  *   economic_relationship: Resource flow and trade
  *   cultural_policy: Approach to local cultures
  *   resistance_movements: Opposition to colonial rule
  *   demographic_impact: Population changes
  *   environmental_impact: Ecological changes
  *   technological_transfers: Shared technologies
  *   legal_status: Official relationship
  *   colonial_legacy: Lasting effects
  *   decolonization_process: Path to independence
  *   current_status: Present relationship
  *   historical_perception: How it's remembered
  *   image_reference: Visual representation
  *   description: Detailed description

Resistance Movement Template

  *   id: Unique identifier
  *   name: Name of the movement
  *   society_ids: Societies where it operates
  *   opposed_to_id: Entity/system being resisted
  *   founding_date: When established
  *   founding_cause: What triggered formation
  *   ideology: Core beliefs and principles
  *   goals: What they aim to achieve
  *   leadership_structure: How decisions are made
  *   key_figures_ids: Important members
  *   size: Number of participants
  *   tactics: Methods of resistance
  *   resources: Funding and materials
  *   allies_ids: Supporting groups
  *   enemies_ids: Opposing groups
  *   public_perception: How they're viewed
  *   government_response: Official reaction
  *   achievements: Successful outcomes
  *   setbacks: Failures and challenges
  *   current_status: Active, dormant, disbanded
  *   image_reference: Visual representation
  *   description: Detailed description

International Organization Template

  *   id: Unique identifier
  *   name: Name of the organization
  *   type: Political, economic, military, cultural, etc.
  *   founding_date: When established
  *   founding_purpose: Reason for creation
  *   member_entities_ids: Nations/groups involved
  *   headquarters_location_id: Main office location
  *   organizational_structure: How it's governed
  *   leadership_selection: How leaders are chosen
  *   decision_making_process: How policies are determined
  *   funding_mechanism: How it's financed
  *   areas_of_activity: Fields of operation
  *   powers_and_limitations: What it can/cannot do
  *   major_initiatives: Important programs
  *   historical_achievements: Successful outcomes
  *   controversies: Disputed actions
  *   relationship_with_members: Internal dynamics
  *   public_perception: How it's viewed
  *   current_challenges: Ongoing issues
  *   image_reference: Visual representation
  *   description: Detailed description

Sport/Recreation Template

  *   id: Unique identifier
  *   name: Name of the sport/activity
  *   culture_ids: Cultures that practice it
  *   origin_location_id: Where it developed
  *   origin_date: When it began
  *   type: Team sport, individual sport, board game, etc.
  *   participants: Number of players/teams
  *   equipment: Tools/gear required
  *   playing_field: Where/how it's played
  *   rules: How it works
  *   scoring_system: How winners are determined
  *   duration: How long it takes to play
  *   skill_requirements: Abilities needed
  *   professional_level: Organized leagues/competitions
  *   cultural_significance: Importance to society
  *   major_events: Tournaments/championships
  *   famous_participants_ids: Notable players
  *   variations: Different versions
  *   evolution: How it's changed over time
  *   image_reference: Visual representation
  *   description: Detailed description

Economic & Material Systems

Currency Template

  *   id: Unique identifier
  *   name: Name of the currency
  *   issuing_authority_id: Who creates it
  *   regions_used: Where it circulates
  *   value_basis: What backs its worth
  *   physical_form: Coins, notes, digital, etc.
  *   denominations: Value divisions
  *   materials: What it's made from
  *   security_features: Anti-counterfeiting
  *   exchange_rates: Value vs. other currencies
  *   stability: Value consistency
  *   inflation_rate: Value depreciation
  *   history: How it developed
  *   cultural_significance: Beyond monetary value
  *   slang_terms: Colloquial names
  *   black_market_value: Illegal trade worth
  *   counterfeit_issues: Forgery problems
  *   image_reference: Visual representation
  *   description: Detailed description

Currency Denomination Template

  *   id: Unique identifier
  *   name: Name of the denomination
  *   currency_id: Parent currency
  *   type: Coin, note, digital unit, etc.
  *   value: Worth relative to base unit
  *   face_value: Printed/stamped amount
  *   size: Physical dimensions
  *   weight: Mass (for physical currency)
  *   materials: Composition
  *   color: Visual appearance
  *   design_obverse: Front face imagery
  *   design_reverse: Back face imagery
  *   inscriptions: Text elements
  *   security_features: Anti-counterfeiting measures
  *   minting_process: How it's created
  *   circulation_date: When introduced
  *   circulation_volume: How many exist
  *   replacement_rate: How often renewed
  *   special_editions: Commemorative versions
  *   historical_changes: Design evolution
  *   cultural_significance: Symbolic importance
  *   common_usage: Typical transactions
  *   slang_terms: Colloquial names
  *   counterfeiting_prevalence: Forgery frequency
  *   image_reference: Visual representation
  *   description: Detailed description

Trade Route Template

  *   id: Unique identifier
  *   name: Name of the route
  *   type: Land, sea, air, space, etc.
  *   endpoints: Start and finish locations
  *   intermediate_stops: Waypoints
  *   length: Distance covered
  *   typical_travel_time: Journey duration
  *   goods_transported: What's carried
  *   volume_of_trade: Economic activity
  *   controlling_entities: Who runs/taxes it
  *   dangers: Risks to travelers
  *   protective_measures: Security features
  *   transportation_methods: How goods move
  *   seasonal_variations: Weather effects
  *   historical_significance: Past importance
  *   current_status: Active, declining, etc.
  *   alternative_routes: Backup paths
  *   cultural_impact: Effect on connected regions
  *   image_reference: Map representation
  *   description: Detailed description

Trading Post Template

  *   id: Unique identifier
  *   name: Name of the trading post
  *   trade_route_id: Parent trade route
  *   location_id: Geographic position
  *   founding_date: When established
  *   founder_id: Who established it
  *   controlling_entity_id: Who currently owns/operates it
  *   size: Physical dimensions
  *   population: Number of permanent residents
  *   visitor_traffic: Temporary population
  *   primary_goods: Main items traded
  *   specialty_items: Unique merchandise
  *   services_offered: Non-goods offerings
  *   facilities: Buildings and infrastructure
  *   security_measures: Protection methods
  *   cultural_makeup: Ethnic/species diversity
  *   local_laws: Special regulations
  *   economic_importance: Regional significance
  *   seasonal_patterns: Busy/slow periods
  *   reputation: How it's viewed by travelers
  *   notable_features: Unique characteristics
  *   image_reference: Visual representation
  *   description: Detailed description

Market/Trading Center Template

  *   id: Unique identifier
  *   name: Name of the market
  *   location_id: Where it's situated
  *   controlling_entity_id: Who runs it
  *   game_system_id: Associated rule system (for game mechanics)
  *   economy_id: Associated economic system
  *   size: Physical dimensions
  *   establishment_date: When founded
  *   operating_hours: When active
  *   specialization: Particular goods focus
  *   trading_volume: Economic activity
  *   currencies_accepted: Money used
  *   regulation_level: Rules enforcement
  *   membership_requirements: Who can trade
  *   facilities: Available amenities
  *   security_measures: Protection systems
  *   reputation: Quality/fairness perception
  *   notable_merchants: Famous traders
  *   exotic_goods: Rare merchandise
  *   cultural_significance: Beyond commerce
  *   trade_interfaces: How exchanges are conducted (for games)
  *   tradeable_items: What can be exchanged
  *   value_assessment: How worth is determined
  *   haggling_mechanics: Price negotiation
  *   npc_merchant_behaviors: AI trading patterns
  *   player_to_player_trading: Direct exchanges
  *   trade_restrictions: Limitations on exchanges
  *   trade_taxation: Fees on transactions
  *   trade_reputation: History affecting future trades
  *   black_market_trading: Illegal exchanges
  *   auction_systems: Competitive bidding
  *   trade_quest_integration: Missions involving commerce
  *   trade_routes: Geographic commerce patterns
  *   trade_timing: When exchanges are available
  *   image_reference: Visual representation
  *   description: Detailed description


Banking/Financial Institution Template

  *   id: Unique identifier
  *   name: Name of the institution
  *   type: Bank, credit union, investment firm, etc.
  *   founding_date: When established
  *   founder_id: Who established it
  *   location_ids: Where branches exist
  *   headquarters_location_id: Main office
  *   controlling_entity_id: Who owns/runs it
  *   services: Financial offerings
  *   clientele: Who they serve
  *   assets_value: Total holdings
  *   currency_ids: Money they deal in
  *   interest_rates: Lending/saving rates
  *   security_measures: Protection systems
  *   regulation_status: Oversight level
  *   technological_level: Banking technology
  *   public_reputation: How they're viewed
  *   corruption_level: Ethical standing
  *   cultural_influence: Impact beyond finance
  *   historical_events: Major incidents
  *   image_reference: Visual representation
  *   description: Detailed description

Economic System Template

  *   id: Unique identifier
  *   name: Name of the system
  *   type: Capitalism, feudalism, socialism, etc.
  *   game_system_id: Associated rule system (for game mechanics)
  *   regions_practiced: Where it's used
  *   inception_date: When implemented
  *   founder_ideology_id: Philosophical basis
  *   ownership_structure: Who controls assets
  *   resource_allocation: How goods are distributed
  *   labor_relations: Worker/employer dynamics
  *   market_regulation: Freedom vs. control
  *   currency_type: Money system
  *   taxation_approach: Revenue collection
  *   wealth_distribution: Equality level
  *   class_mobility: Status change ease
  *   growth_patterns: Economic development
  *   crisis_handling: Recession management
  *   strengths: System advantages
  *   weaknesses: System flaws
  *   historical_evolution: How it's changed
  *   currency_types: Money and exchange mediums (for games)
  *   currency_sources: How money enters the system
  *   currency_sinks: How money leaves the system
  *   inflation_controls: Value stability mechanisms
  *   price_scaling: How costs change over time
  *   trading_systems: Exchange between players
  *   vendor_mechanics: NPC merchants
  *   auction_systems: Competitive purchasing
  *   wealth_distribution: Economic equality measures
  *   economic_loops: Circular resource flows
  *   black_market: Alternative economies
  *   resource_system_id: Associated resources
  *   crafting_system_id: Associated crafting
  *   reward_system_id: Associated loot distribution
  *   banking_features: Saving and interest
  *   image_reference: Visual representation
  *   description: Detailed description

Resource Template

  *   id: Unique identifier
  *   name: Name of the resource
  *   type: Mineral, plant, energy, etc.
  *   game_system_id: Associated rule system (for game mechanics)
  *   rarity: How common/scarce
  *   locations_found: Where it exists
  *   extraction_method: How it's obtained
  *   processing_requirements: Refinement needs
  *   properties: Physical characteristics
  *   uses: What it's good for
  *   value: Economic worth
  *   substitutes: Alternatives
  *   environmental_impact: Ecological effect
  *   controlling_entities: Who owns/manages it
  *   conflicts: Disputes over control
  *   cultural_significance: Importance beyond utility
  *   technological_dependencies: Tech needed to use
  *   magical_properties: Supernatural aspects if any
  *   stability: Permanence/degradation
  *   resource_types: Categories of resources tracked (for games)
  *   acquisition_methods: How resources are gained
  *   expenditure_options: How resources are used
  *   storage_limitations: Maximum amounts
  *   decay_mechanics: How resources diminish over time
  *   conversion_rates: Exchange between resource types
  *   resource_economy: How resources flow through game
  *   scarcity_level: Abundance vs. rarity
  *   resource_competition: How players compete for resources
  *   resource_sharing: How resources can be transferred
  *   resource_visibility: Public vs. hidden information
  *   strategic_considerations: Decision-making factors
  *   resource_ui: How resources are displayed to players
  *   balance_considerations: Economic stability factors
  *   economy_id: Associated economic system
  *   image_reference: Visual representation
  *   description: Detailed description

Resource Node Template

  *   id: Unique identifier
  *   name: Name of the resource node
  *   resource_id: Parent resource type
  *   resource_system_id: Associated resource system
  *   game_system_id: Associated rule system
  *   location_id: Where it's situated
  *   node_type: Mine, grove, spring, etc.
  *   visual_appearance: How it looks in the world
  *   discovery_method: How players find it
  *   visibility_conditions: When/how it can be seen
  *   extraction_tool_requirements: Equipment needed
  *   extraction_skill_requirements: Abilities needed
  *   extraction_time: How long to gather
  *   extraction_difficulty: Challenge rating
  *   yield_amount: Quantity provided
  *   yield_quality: Grade of resource
  *   yield_variation: Randomization factors
  *   respawn_mechanics: How/when it replenishes
  *   depletion_mechanics: How it's exhausted
  *   seasonal_availability: Time-based access
  *   environmental_conditions: Weather effects
  *   hazards: Dangers during extraction
  *   competing_gatherers: NPC resource collectors
  *   guarding_entities: Creatures protecting it
  *   ownership_status: Who controls it
  *   access_restrictions: Barriers to gathering
  *   map_markers: How it appears on maps
  *   special_properties: Unique characteristics
  *   image_reference: Visual representation
  *   description: Detailed description

Taxation System Template

  *   id: Unique identifier
  *   name: Name of the system
  *   governing_entity_id: Who collects taxes
  *   regions_applied: Where it's enforced
  *   tax_types: Income, property, sales, etc.
  *   tax_rates: Amount collected
  *   collection_methods: How gathered
  *   enforcement_mechanisms: Compliance tools
  *   exemptions: Who doesn't pay
  *   penalties: Consequences for evasion
  *   revenue_use: How money is spent
  *   taxpayer_rights: Protection from abuse
  *   historical_development: System evolution
  *   public_perception: How it's viewed
  *   evasion_rates: Compliance level
  *   reform_movements: Change efforts
  *   efficiency: Administrative costs
  *   corruption_level: System integrity
  *   image_reference: Visual representation
  *   description: Detailed description

Specific Tax Template

  *   id: Unique identifier
  *   name: Name of the tax
  *   taxation_system_id: Parent taxation system
  *   governing_entity_id: Authority imposing the tax
  *   type: Income, property, sales, inheritance, etc.
  *   implementation_date: When established
  *   legal_basis: Law authorizing the tax
  *   purpose: Intended use of revenue
  *   tax_base: What is being taxed
  *   rate_structure: Flat, progressive, regressive, etc.
  *   rate_amount: Percentage or fixed amount
  *   calculation_method: How tax is computed
  *   collection_frequency: When it's collected
  *   collection_method: How it's gathered
  *   filing_requirements: Documentation needed
  *   exemptions: Who/what is excluded
  *   deductions: Allowable reductions
  *   minimum_threshold: Level below which no tax applies
  *   maximum_cap: Upper limit if applicable
  *   penalties_for_evasion: Consequences for non-payment
  *   enforcement_agency_id: Who ensures compliance
  *   revenue_generated: Amount collected
  *   economic_impact: Effect on markets
  *   social_impact: Effect on population groups
  *   avoidance_strategies: Legal minimization methods
  *   reform_history: How it has changed
  *   public_perception: How it's viewed
  *   image_reference: Visual representation
  *   description: Detailed description

Road/Transport Network Template

  *   id: Unique identifier
  *   name: Name of the network
  *   type: Roads, rails, air routes, etc.
  *   regions_covered: Geographic reach
  *   controlling_entity_id: Who manages it
  *   construction_date: When built
  *   materials: What it's made of
  *   condition: State of repair
  *   extent: Total size/length
  *   connectivity: Places linked
  *   capacity: Traffic volume
  *   access_restrictions: Usage limits
  *   tolls: Usage fees
  *   maintenance_system: Upkeep method
  *   security_measures: Protection approach
  *   strategic_importance: Military/economic value
  *   weather_vulnerability: Environmental risks
  *   cultural_significance: Social impact
  *   image_reference: Map representation
  *   description: Detailed description

Communication System Template

  *   id: Unique identifier
  *   name: Name of the system
  *   type: Postal, telegraph, digital, telepathic, etc.
  *   regions_covered: Geographic reach
  *   controlling_entity_id: Who manages it
  *   establishment_date: When created
  *   technology_level: Advancement stage
  *   transmission_method: How messages travel
  *   transmission_speed: Delivery time
  *   bandwidth: Information capacity
  *   reliability: Failure rate
  *   security_level: Privacy protection
  *   access_restrictions: Who can use it
  *   infrastructure: Physical components
  *   cost_to_use: Expense for users
  *   monitoring: Surveillance level
  *   cultural_impact: Effect on society
  *   failure_modes: Common problems
  *   image_reference: Visual representation
  *   description: Detailed description

Utility System Template

  *   id: Unique identifier
  *   name: Name of the utility
  *   type: Water, power, waste, etc.
  *   regions_served: Geographic coverage
  *   controlling_entity_id: Who manages it
  *   establishment_date: When created
  *   source: Where resource comes from
  *   distribution_method: How delivered
  *   infrastructure: Physical components
  *   capacity: Volume handled
  *   reliability: Outage frequency
  *   access_equity: Distribution fairness
  *   environmental_impact: Ecological effect
  *   cost_structure: Pricing model
  *   maintenance_approach: Upkeep method
  *   technological_level: Advancement stage
  *   future_development: Planned improvements
  *   cultural_significance: Social impact
  *   image_reference: Visual representation
  *   description: Detailed description

Urban Plan Template

  *   id: Unique identifier
  *   name: Name of the plan/style
  *   regions_implemented: Where used
  *   historical_period: When developed
  *   designer_id: Who created it
  *   layout_pattern: Grid, radial, organic, etc.
  *   density: Population concentration
  *   zoning_approach: Space allocation
  *   transportation_focus: Movement priority
  *   green_space_ratio: Natural areas
  *   architectural_style: Building design
  *   defensive_features: Protection elements
  *   water_management: Hydrology handling
  *   waste_handling: Sanitation approach
  *   cultural_influences: Design inspirations
  *   functionality: How well it works
  *   adaptation_over_time: How it's changed
  *   modern_relevance: Current applications
  *   image_reference: Map/diagram
  *   description: Detailed description

Agricultural System Template

  *   id: Unique identifier
  *   name: Name of the system
  *   regions_practiced: Where used
  *   historical_period: When developed
  *   climate_adaptation: Weather suitability
  *   terrain_requirements: Land needs
  *   crops_raised: Plants grown
  *   livestock_raised: Animals kept
  *   water_management: Irrigation approach
  *   cultivation_techniques: Farming methods
  *   tools_used: Equipment employed
  *   seasonality: Annual cycle
  *   yield_efficiency: Production level
  *   labor_requirements: Workforce needs
  *   land_ownership: Property structure
  *   sustainability: Long-term viability
  *   cultural_practices: Associated traditions
  *   evolution: How it's changed
  *   image_reference: Visual representation
  *   description: Detailed description

Manufacturing Center Template

  *   id: Unique identifier
  *   name: Name of the center
  *   location_id: Where situated
  *   controlling_entity_id: Who owns/runs it
  *   establishment_date: When created
  *   products: What's produced
  *   production_capacity: Output volume
  *   workforce_size: Number of workers
  *   raw_materials: Input resources
  *   technology_level: Advancement stage
  *   energy_source: Power type
  *   quality_reputation: Product standards
  *   distribution_network: Where goods go
  *   environmental_impact: Ecological effect
  *   working_conditions: Labor treatment
  *   automation_level: Machine vs. human
  *   economic_importance: Market impact
  *   security_measures: Protection systems
  *   production_process: Manufacturing steps
  *   quality_control: Standards enforcement
  *   waste_management: Handling byproducts
  *   storage_facilities: Inventory management
  *   shipping_methods: Product distribution
  *   image_reference: Visual representation
  *   description: Detailed description

Tool/Implement Template

  *   id: Unique identifier
  *   name: Name of the tool
  *   type: Hand tool, machine, device, etc.
  *   creator_id: Who invented it
  *   creation_date: When invented
  *   cultures_using: Who uses it
  *   materials: What it's made from
  *   construction_method: How it's made
  *   function: What it does
  *   operation_method: How it works
  *   skill_required: Expertise needed
  *   power_source: Energy type if any
  *   durability: How long it lasts
  *   maintenance_needs: Upkeep required
  *   availability: How common
  *   cost: Price/value
  *   variants: Different versions
  *   evolution: How it's changed
  *   cultural_significance: Importance beyond function
  *   image_reference: Visual representation
  *   description: Detailed description

Weapon/Armor Template

  *   id: Unique identifier
  *   name: Name of the item
  *   type: Sword, gun, shield, etc.
  *   creator_culture_id: Who developed it
  *   creation_date: When invented
  *   materials: What it's made from
  *   construction_method: How it's made
  *   weight: How heavy
  *   size: Dimensions
  *   damage_type: How it harms
  *   effective_range: Distance effective
  *   accuracy: Targeting precision
  *   penetration: Armor defeat ability
  *   reload_time: Between uses (if applicable)
  *   ammunition_type: What it fires (if applicable)
  *   protection_rating: Defense provided (if armor)
  *   mobility_impact: Movement restriction
  *   training_required: Skill needed
  *   typical_users: Who wields it
  *   cultural_significance: Symbolic importance
  *   image_reference: Visual representation
  *   description: Detailed description

Clothing Item Template

  *   id: Unique identifier
  *   name: Name of the garment
  *   type: Hat, shirt, dress, etc.
  *   culture_origin_id: Where developed
  *   era_id: Historical period
  *   gender_association: Who wears it
  *   materials: What it's made from
  *   construction_method: How it's made
  *   design_elements: Visual features
  *   function: Primary purpose
  *   weather_suitability: Climate adaptation
  *   social_context: When/where worn
  *   status_indication: Class signifier
  *   variants: Style differences
  *   decoration: Embellishments
  *   symbolism: Cultural meaning
  *   evolution: How it's changed
  *   modern_equivalents: Contemporary versions
  *   image_reference: Visual representation
  *   description: Detailed description

Furniture Item Template

  *   id: Unique identifier
  *   name: Name of the item
  *   type: Chair, table, bed, etc.
  *   culture_origin_id: Where developed
  *   era_id: Historical period
  *   materials: What it's made from
  *   construction_method: How it's made
  *   style: Design aesthetic
  *   function: Primary purpose
  *   dimensions: Size measurements
  *   ergonomics: Comfort/usability
  *   decoration: Embellishments
  *   typical_settings: Where used
  *   status_indication: Wealth signifier
  *   durability: Lifespan
  *   portability: Ease of movement
  *   variants: Regional differences
  *   evolution: How it's changed
  *   image_reference: Visual representation
  *   description: Detailed description

Luxury Good Template

  *   id: Unique identifier
  *   name: Name of the item
  *   type: Jewelry, art, exotic food, etc.
  *   culture_origin_id: Where developed
  *   materials: What it's made from
  *   rarity: How scarce
  *   production_method: How created
  *   typical_cost: Price range
  *   value_stability: Investment worth
  *   ownership_restrictions: Who can possess
  *   social_significance: Status meaning
  *   cultural_associations: Symbolic value
  *   typical_owners: Who possesses it
  *   display_customs: How shown/used
  *   counterfeiting: Fake production issues
  *   black_market: Illegal trade
  *   historical_examples: Famous instances
  *   evolution: How it's changed
  *   image_reference: Visual representation
  *   description: Detailed description

Shelter/Housing Template

  *   id: Unique identifier
  *   name: Type name
  *   culture_ids: Associated peoples
  *   climate_adaptation: Weather suitability
  *   terrain_adaptation: Land compatibility
  *   materials: Construction components
  *   construction_method: Building technique
  *   size_range: Typical dimensions
  *   layout: Floor plan approach
  *   permanence: Temporary vs. permanent
  *   mobility: Fixed vs. portable
  *   occupancy: Inhabitant capacity
  *   amenities: Available comforts
  *   defensive_features: Protection elements
  *   symbolic_elements: Cultural meanings
  *   environmental_control: Climate management
  *   lifespan: How long it lasts
  *   evolution: How design changed
  *   image_reference: Visual representation
  *   description: Detailed description

Art/Decorative Object Template

  *   id: Unique identifier
  *   name: Name/type of object
  *   culture_origin_id: Where developed
  *   era_id: Historical period
  *   creator_id: Artist/artisan
  *   materials: What it's made from
  *   creation_technique: How it's made
  *   style: Artistic tradition
  *   dimensions: Size measurements
  *   colors: Color palette
  *   symbolism: Cultural meaning
  *   function: Purpose beyond aesthetic
  *   typical_settings: Where displayed
  *   ownership_pattern: Who possesses
  *   value: Economic worth
  *   preservation_state: Condition
  *   cultural_significance: Importance
  *   modern_reproductions: Copy availability
  *   image_reference: Visual representation
  *   description: Detailed description

Public Facility Template

  *   id: Unique identifier
  *   name: Name of the facility
  *   type: School, hospital, library, etc.
  *   location_id: Where it's situated
  *   controlling_entity_id: Who runs it
  *   founding_date: When established
  *   purpose: Function it serves
  *   size: Physical dimensions
  *   capacity: How many it can serve
  *   architectural_style: Building design
  *   services_provided: What it offers
  *   staff_size: Number of employees
  *   funding_source: How it's financed
  *   access_restrictions: Who can use it
  *   quality_level: Condition and effectiveness
  *   cultural_significance: Importance to community
  *   historical_events: Notable occurrences
  *   public_perception: How it's viewed
  *   future_plans: Upcoming changes
  *   image_reference: Visual representation
  *   description: Detailed description

Knowledge & Technology Section

Scientific/Technological Era Template

  *   id: Unique identifier
  *   name: Name of the era
  *   timeframe: Duration period
  *   regions_affected: Geographic reach
  *   defining_technologies: Key innovations
  *   knowledge_paradigm: Understanding framework
  *   scientific_method: Research approach
  *   education_system: Learning structures
  *   literacy_rate: Reading/writing prevalence
  *   information_storage: Record-keeping
  *   major_discoveries: Important findings
  *   major_inventions: Important creations
  *   key_figures: Notable individuals
  *   societal_impact: Cultural effects
  *   economic_impact: Market effects
  *   successor_causes: What led to next era
  *   legacy_elements: Lasting influences
  *   image_reference: Visual representation
  *   description: Detailed description

Invention/Innovation Template

  *   id: Unique identifier
  *   name: Name of the invention/innovation
  *   technological_era_id: Parent technological era
  *   type: Device, process, theory, material, etc.
  *   inventor_ids: Who created it
  *   invention_date: When developed
  *   development_location_id: Where created
  *   problem_addressed: Issue it solves
  *   predecessor_technologies: What it built upon
  *   scientific_principles: Underlying concepts
  *   materials_required: Components needed
  *   manufacturing_complexity: Difficulty to produce
  *   initial_applications: First uses
  *   adoption_rate: How quickly it spread
  *   economic_impact: Market effects
  *   societal_impact: Cultural changes
  *   technological_impact: Effect on other technologies
  *   patent_status: Intellectual property protection
  *   competing_innovations: Alternative solutions
  *   evolution: How it changed over time
  *   modern_relevance: Current importance
  *   image_reference: Visual representation
  *   description: Detailed description

Scientific Paradigm Template

  *   id: Unique identifier
  *   name: Name of the paradigm
  *   field: Area of knowledge
  *   timeframe: When dominant
  *   regions_practiced: Where accepted
  *   founder_ids: Who established it
  *   core_principles: Fundamental ideas
  *   methodology: Research approach
  *   tools_used: Equipment employed
  *   evidence_standards: Proof requirements
  *   competing_paradigms: Alternative views
  *   major_discoveries: Important findings
  *   limitations: What it can't explain
  *   successor_paradigm: What replaced it
  *   cultural_impact: Societal effects
  *   religious_interaction: Faith compatibility
  *   modern_relevance: Current validity
  *   image_reference: Visual representation
  *   description: Detailed description

Knowledge Preservation Method Template

  *   id: Unique identifier
  *   name: Name of the method
  *   type: Oral, written, digital, magical, etc.
  *   cultures_using: Who employs it
  *   materials_required: Physical components
  *   techniques: How it works
  *   durability: Information lifespan
  *   fidelity: Accuracy maintenance
  *   accessibility: Who can access
  *   transmission_method: How passed on
  *   vulnerabilities: Failure modes
  *   storage_capacity: Information volume
  *   retrieval_efficiency: Access ease
  *   specialization: Knowledge types suited for
  *   practitioners: Who performs preservation
  *   cultural_importance: Social value
  *   evolution: How it's changed
  *   image_reference: Visual representation
  *   description: Detailed description


Educational System Template

  *   id: Unique identifier
  *   name: Name of the system
  *   cultures_using: Who employs it
  *   historical_period: When developed
  *   philosophy_id: Educational approach
  *   access_level: Who can participate
  *   cost_structure: How funded
  *   duration: Length of education
  *   age_ranges: Student demographics
  *   subjects_taught: Knowledge covered
  *   teaching_methods: Instruction approach
  *   assessment_methods: Evaluation techniques
  *   teacher_requirements: Educator qualifications
  *   facility_types: Learning environments
  *   materials_used: Educational resources
  *   credential_system: Certification method
  *   social_purpose: Role in society
  *   prestige_level: Respect accorded
  *   evolution: How it's changed
  *   image_reference: Visual representation
  *   description: Detailed description

Educational Institution Template

  *   id: Unique identifier
  *   name: Name of the institution
  *   educational_system_id: Parent educational system
  *   type: School, university, academy, guild, etc.
  *   founding_date: When established
  *   founder_id: Who established it
  *   location_id: Where situated
  *   campus_size: Physical dimensions
  *   architecture_style: Building design
  *   facilities: Available resources (libraries, labs, etc.)
  *   education_level: Primary, secondary, higher, etc.
  *   specialization: Focus areas if any
  *   student_body_size: Number of students
  *   student_demographics: Who attends
  *   faculty_size: Number of teachers
  *   faculty_qualifications: Teacher credentials
  *   curriculum: Subjects taught
  *   teaching_philosophy: Educational approach
  *   pedagogical_methods: How teaching occurs
  *   assessment_system: How students are evaluated
  *   academic_calendar: Schedule structure
  *   degree_programs: Credentials offered
  *   admission_requirements: Entry criteria
  *   tuition_cost: Attendance expense
  *   financial_aid: Support for students
  *   governance_structure: How it's managed
  *   affiliation: Religious, governmental, private, etc.
  *   reputation: Academic standing
  *   alumni_network: Graduate community
  *   traditions: Unique customs
  *   image_reference: Visual representation
  *   description: Detailed description

Research Facility Template

  *   id: Unique identifier
  *   name: Name of the facility
  *   type: Laboratory, observatory, archive, etc.
  *   location_id: Where situated
  *   controlling_entity_id: Who owns/runs it
  *   founding_date: When established
  *   research_focus: Study areas
  *   technological_level: Equipment advancement
  *   staff_size: Number of researchers
  *   funding_source: Financial support
  *   discovery_record: Major findings
  *   secrecy_level: Information access
  *   ethics_standards: Research boundaries
  *   public_interaction: Community engagement
  *   reputation: How it's viewed
  *   specialization: Unique capabilities
  *   collaboration_network: Partner institutions
  *   future_goals: Development plans
  *   image_reference: Visual representation
  *   description: Detailed description

Energy Production Technology Template

  *   id: Unique identifier
  *   name: Name of the technology
  *   type: Fossil, renewable, nuclear, magical, etc.
  *   inventor_id: Who developed it
  *   invention_date: When created
  *   cultures_using: Who employs it
  *   physics_principle: How it works
  *   fuel_source: What powers it
  *   efficiency_rating: Output ratio
  *   output_capacity: Energy production
  *   emissions: Byproducts generated
  *   safety_profile: Hazard level
  *   infrastructure_needs: Support requirements
  *   cost_efficiency: Economic viability
  *   maintenance_requirements: Upkeep needs
  *   lifespan: Operational duration
  *   environmental_impact: Ecological effects
  *   societal_impact: Cultural influence
  *   image_reference: Visual representation
  *   description: Detailed description

Medical Technology Template

  *   id: Unique identifier
  *   name: Name of the technology
  *   type: Diagnostic, treatment, preventative, etc.
  *   inventor_id: Who developed it
  *   invention_date: When created
  *   cultures_using: Who employs it
  *   scientific_basis: How it works
  *   target_conditions: What it addresses
  *   effectiveness: Success rate
  *   side_effects: Negative outcomes
  *   availability: Access level
  *   cost: Affordability
  *   practitioner_requirements: User training
  *   equipment_needs: Physical components
  *   procedure_duration: Time required
  *   recovery_time: Healing period
  *   cultural_acceptance: Social attitudes
  *   ethical_considerations: Moral issues
  *   evolution: How it's changed
  *   image_reference: Visual representation
  *   description: Detailed description

Communication Technology Template

  *   id: Unique identifier
  *   name: Name of the technology
  *   type: Visual, audio, electronic, telepathic, etc.
  *   inventor_id: Who developed it
  *   invention_date: When created
  *   cultures_using: Who employs it
  *   scientific_basis: How it works
  *   range: Distance covered
  *   speed: Transmission time
  *   reliability: Failure rate
  *   infrastructure_needs: Support requirements
  *   power_requirements: Energy use
  *   user_interface: How people interact
  *   privacy_level: Security features
  *   bandwidth: Information capacity
  *   cost: Affordability
  *   accessibility: Who can use it
  *   cultural_impact: Social effects
  *   evolution: How it's changed
  *   image_reference: Visual representation
  *   description: Detailed description

Information Technology Template

  *   id: Unique identifier
  *   name: Name of the technology
  *   type: Storage, processing, networking, etc.
  *   inventor_id: Who developed it
  *   invention_date: When created
  *   cultures_using: Who employs it
  *   scientific_basis: How it works
  *   processing_power: Computational ability
  *   storage_capacity: Data volume
  *   interface_type: User interaction method
  *   form_factor: Physical characteristics
  *   power_requirements: Energy needs
  *   maintenance_needs: Upkeep required
  *   durability: Operational lifespan
  *   security_features: Protection measures
  *   applications: Common uses
  *   cost: Affordability
  *   accessibility: Who can use it
  *   cultural_impact: Social effects
  *   evolution: How it's changed
  *   image_reference: Visual representation
  *   description: Detailed description

Manufacturing Technology Template

  *   id: Unique identifier
  *   name: Name of the technology
  *   type: Production method or system
  *   inventor_id: Who developed it
  *   invention_date: When created
  *   cultures_using: Who employs it
  *   scientific_basis: How it works
  *   materials_handled: What it processes
  *   output_volume: Production capacity
  *   precision: Accuracy level
  *   automation_level: Human involvement
  *   energy_requirements: Power needs
  *   facility_needs: Space/infrastructure
  *   workforce_requirements: Labor needs
  *   byproducts: Waste generated
  *   maintenance_needs: Upkeep required
  *   cost_efficiency: Economic viability
  *   environmental_impact: Ecological effects
  *   societal_impact: Labor/market influence
  *   image_reference: Visual representation
  *   description: Detailed description

Weapons Technology Template

  *   id: Unique identifier
  *   name: Name of the technology
  *   type: Melee, ranged, explosive, energy, etc.
  *   inventor_id: Who developed it
  *   invention_date: When created
  *   cultures_using: Who employs it
  *   scientific_basis: How it works
  *   materials_required: Components needed
  *   damage_mechanism: How it harms
  *   effective_range: Distance effective
  *   accuracy: Targeting precision
  *   rate_of_fire: Use frequency
  *   ammunition_type: What it fires (if applicable)
  *   counter_measures: Defenses against it
  *   production_difficulty: Ease of manufacture
  *   maintenance_needs: Upkeep required
  *   availability: Access restrictions
  *   societal_impact: Cultural effects
  *   evolution: How it's changed
  *   image_reference: Visual representation
  *   description: Detailed description

Military Technology Template

  *   id: Unique identifier
  *   name: Name of the technology
  *   type: Offensive, defensive, logistics, etc.
  *   inventor_id: Who developed it
  *   invention_date: When created
  *   cultures_using: Who employs it
  *   scientific_basis: How it works
  *   strategic_impact: Warfare influence
  *   tactical_applications: Combat uses
  *   scale: Individual vs. mass deployment
  *   mobility: Transportation aspects
  *   durability: Resilience in combat
  *   crew_requirements: Personnel needs
  *   training_needs: Skill development
  *   maintenance_needs: Upkeep required
  *   production_cost: Resource demands
  *   counter_technology: What defeats it
  *   proliferation: Distribution control
  *   societal_impact: Cultural effects
  *   image_reference: Visual representation
  *   description: Detailed description

Biotechnology Template

  *   id: Unique identifier
  *   name: Name of the technology
  *   type: Genetic, pharmaceutical, agricultural, etc.
  *   inventor_id: Who developed it
  *   invention_date: When created
  *   cultures_using: Who employs it
  *   scientific_basis: How it works
  *   organisms_affected: Target life forms
  *   intended_effect: Desired outcome
  *   side_effects: Unintended consequences
  *   application_method: How it's used
  *   duration_of_effect: Timeframe active
  *   safety_profile: Risk assessment
  *   ethical_considerations: Moral issues
  *   regulatory_status: Legal standing
  *   development_cost: Research investment
  *   production_difficulty: Manufacturing ease
  *   availability: Access level
  *   societal_impact: Cultural effects
  *   image_reference: Visual representation
  *   description: Detailed description

Space Technology Template

  *   id: Unique identifier
  *   name: Name of the technology
  *   type: Propulsion, habitation, observation, etc.
  *   inventor_id: Who developed it
  *   invention_date: When created
  *   cultures_using: Who employs it
  *   scientific_basis: How it works
  *   capabilities: What it can do
  *   limitations: What it can't do
  *   range: Operational distance
  *   speed: Movement rate (if applicable)
  *   durability: Space environment resistance
  *   crew_requirements: Personnel needs
  *   life_support: Survival systems
  *   power_source: Energy generation
  *   resource_consumption: Supply needs
  *   failure_modes: How it can malfunction
  *   production_difficulty: Manufacturing complexity
  *   development_cost: Research investment
  *   image_reference: Visual representation
  *   description: Detailed description

Land Vehicle Template

  *   id: Unique identifier
  *   name: Name/model of vehicle
  *   type: Car, train, tank, beast-drawn, etc.
  *   creator_id: Who developed it
  *   creation_date: When invented
  *   cultures_using: Who employs it
  *   propulsion_method: How it moves
  *   power_source: Energy type
  *   speed_range: Movement rate
  *   operational_range: Distance capacity
  *   terrain_adaptation: Where it can go
  *   passenger_capacity: People carried
  *   cargo_capacity: Goods carried
  *   crew_requirements: Operators needed
  *   handling_characteristics: Control ease
  *   durability: Breakdown resistance
  *   maintenance_needs: Upkeep required
  *   production_cost: Manufacturing expense
  *   status_value: Prestige factor
  *   image_reference: Visual representation
  *   description: Detailed description

Water Vessel Template

  *   id: Unique identifier
  *   name: Name/class of vessel
  *   type: Boat, ship, submarine, etc.
  *   creator_id: Who developed it
  *   creation_date: When invented
  *   cultures_using: Who employs it
  *   propulsion_method: How it moves
  *   power_source: Energy type
  *   speed_range: Movement rate
  *   operational_range: Distance capacity
  *   water_type_adaptation: Sea, river, etc.
  *   passenger_capacity: People carried
  *   cargo_capacity: Goods carried
  *   crew_requirements: Operators needed
  *   handling_characteristics: Control ease
  *   durability: Weather resistance
  *   maintenance_needs: Upkeep required
  *   production_cost: Manufacturing expense
  *   special_features: Unique capabilities
  *   image_reference: Visual representation
  *   description: Detailed description

Aircraft Template

  *   id: Unique identifier
  *   name: Name/model of aircraft
  *   type: Balloon, airship, plane, etc.
  *   creator_id: Who developed it
  *   creation_date: When invented
  *   cultures_using: Who employs it
  *   propulsion_method: How it moves
  *   lift_mechanism: How it stays airborne
  *   power_source: Energy type
  *   speed_range: Movement rate
  *   altitude_range: Height capacity
  *   operational_range: Distance capacity
  *   passenger_capacity: People carried
  *   cargo_capacity: Goods carried
  *   crew_requirements: Operators needed
  *   handling_characteristics: Control ease
  *   weather_tolerance: Environmental limits
  *   maintenance_needs: Upkeep required
  *   production_cost: Manufacturing expense
  *   special_features: Unique capabilities
  *   image_reference: Visual representation
  *   description: Detailed description

Space Vessel Template

  *   id: Unique identifier
  *   name: Name/class of vessel
  *   type: Shuttle, ship, station, etc.
  *   creator_id: Who developed it
  *   creation_date: When invented
  *   cultures_using: Who employs it
  *   propulsion_method: How it moves
  *   power_source: Energy type
  *   speed_capability: Movement rate
  *   range_capability: Distance capacity
  *   artificial_gravity: Gravity system
  *   life_support_duration: Survival timeframe
  *   passenger_capacity: People carried
  *   cargo_capacity: Goods carried
  *   crew_requirements: Operators needed
  *   defensive_systems: Protection measures
  *   special_capabilities: Unique features
  *   maintenance_needs: Upkeep required
  *   construction_location: Where built
  *   production_cost: Manufacturing expense
  *   image_reference: Visual representation
  *   description: Detailed description

Technological Disparity Template

  *   id: Unique identifier
  *   name: Name of the disparity
  *   regions_involved_ids: Areas affected
  *   technology_types: Fields with gaps
  *   disparity_level: Degree of difference
  *   causes: Reasons for the gap
  *   historical_development: How it evolved
  *   advanced_side_characteristics: Features of leading group
  *   less_advanced_side_characteristics: Features of trailing group
  *   transfer_mechanisms: How tech spreads
  *   barriers_to_transfer: What prevents sharing
  *   economic_impacts: Effects on economies
  *   social_impacts: Effects on societies
  *   political_impacts: Effects on power dynamics
  *   exploitation_patterns: How gaps are leveraged
  *   reduction_efforts: Attempts to close gaps
  *   projected_trends: Future developments
  *   cultural_perceptions: How disparity is viewed
  *   image_reference: Visual representation
  *   description: Detailed description

Technological Advancement Rate Template

  *   id: Unique identifier
  *   name: Name of the advancement pattern
  *   society_ids: Societies experiencing this rate
  *   technology_field: Area of advancement
  *   rate_measurement: Speed of progress
  *   historical_pattern: Past progression
  *   acceleration_factors: What speeds advancement
  *   deceleration_factors: What slows advancement
  *   breakthrough_events: Major innovations
  *   stagnation_periods: Times of little progress
  *   resource_dependencies: Materials/knowledge needed
  *   institutional_support: Organizations driving progress
  *   cultural_factors: Social elements affecting rate
  *   competitive_pressures: External motivators
  *   adoption_rate: How quickly tech spreads
  *   social_impacts: Effects on society
  *   economic_impacts: Effects on economy
  *   projected_trajectory: Future outlook
  *   image_reference: Visual representation
  *   description: Detailed description

Animal-based Transportation Template

  *   id: Unique identifier
  *   name: Name of the transportation method
  *   animal_species_id: Species used
  *   cultures_using_ids: Societies that employ it
  *   regions_common_ids: Where it's prevalent
  *   historical_development: How it evolved
  *   domestication_process: How animals were trained
  *   typical_uses: Common applications
  *   range: Distance typically covered
  *   speed: How fast it travels
  *   capacity: Weight/passengers it can carry
  *   equipment_needed: Saddles, harnesses, etc.
  *   breeding_practices: How animals are produced
  *   care_requirements: Maintenance needed
  *   economic_importance: Value to society
  *   cultural_significance: Symbolic meaning
  *   social_status: Prestige associated with it
  *   military_applications: Combat uses
  *   modern_relevance: Current usage
  *   image_reference: Visual representation
  *   description: Detailed description

Personal Transportation Device Template

  *   id: Unique identifier
  *   name: Name of the device
  *   technology_level: Tech sophistication
  *   inventor_id: Who created it
  *   invention_date: When developed
  *   power_source: What makes it move
  *   range: Distance it can travel
  *   speed: How fast it moves
  *   size: Physical dimensions
  *   weight: How heavy it is
  *   passenger_capacity: How many it carries
  *   control_mechanism: How it's operated
  *   safety_features: Protective elements
  *   cost: Expense to acquire
  *   maintenance_requirements: Upkeep needed
  *   availability: Who has access to it
  *   social_status: Prestige associated with it
  *   environmental_impact: Effect on surroundings
  *   popularity: How widely used
  *   image_reference: Visual representation
  *   description: Detailed description

Magical & Supernatural Systems

Magic Source Template

  *   id: Unique identifier
  *   name: Name of the source
  *   type: Divine, natural, internal, external, etc.
  *   discovery_date: When first accessed
  *   discoverer_id: Who first used it
  *   access_method: How mages connect to it
  *   physical_manifestation: Visible signs
  *   geographical_distribution: Where available
  *   abundance_level: How plentiful
  *   regeneration_rate: Replenishment speed
  *   fluctuation_patterns: Variation cycles
  *   manipulation_difficulty: Use challenge
  *   compatibility_factors: Who can use it
  *   corruption_potential: Negative effects
  *   historical_events: Key moments
  *   cultural_perception: How it's viewed
  *   research_status: Understanding level
  *   control_conflicts: Power struggles
  *   image_reference: Visual representation
  *   description: Detailed description

Magic Type Template

  *   id: Unique identifier
  *   name: Name of the magic type
  *   category: Elemental, necromantic, illusion, etc.
  *   source_id: Where power comes from
  *   discovery_date: When first used
  *   discoverer_id: Who first used it
  *   energy_color: Visual manifestation
  *   primary_effects: What it typically does
  *   control_mechanism: How mages direct it
  *   learning_difficulty: Mastery challenge
  *   danger_level: Risk to user/others
  *   social_perception: How it's viewed
  *   legal_status: Allowed vs. forbidden
  *   counter_methods: How to block/resist
  *   rare_applications: Uncommon uses
  *   master_practitioners: Famous users
  *   related_types: Connected magics
  *   evolution: How it's changed
  *   image_reference: Visual representation
  *   description: Detailed description

Spell/Ability Template

  *   id: Unique identifier
  *   name: Name of the spell/ability
  *   type_id: Magic category or ability type
  *   magic_based: Whether it's magical or mundane
  *   game_system_id: Associated rule system (for game mechanics)
  *   class_id: Associated character class (if class-specific)
  *   creator_id: Who developed it
  *   creation_date: When developed
  *   effect: What it does
  *   range: Distance effective
  *   area_of_effect: Space affected
  *   duration: How long it lasts
  *   casting_time: Activation speed
  *   components_required: Physical needs
  *   verbal_components: Words needed
  *   somatic_components: Gestures needed
  *   energy_cost: Power required
  *   resource_costs: What's required to use ability
  *   cooldown_mechanics: Time between uses
  *   physical_toll: User exhaustion
  *   backfire_risk: Failure consequences
  *   counter_measures: How to block
  *   availability: Who knows it
  *   acquisition_method: How abilities are learned/gained
  *   progression_path: How abilities improve
  *   synergy_mechanics: How abilities interact
  *   counter_mechanics: How abilities oppose each other
  *   passive_vs_active: Balance between types
  *   skill_ceiling: Mastery potential
  *   skill_floor: Entry-level accessibility
  *   visual_effects: How abilities appear in-game
  *   sound_design: Audio cues for abilities
  *   balance_considerations: Power level notes
  *   variations: Different versions
  *   image_reference: Visual representation
  *   description: Detailed description

Magic Item Template

  *   id: Unique identifier
  *   name: Name of the item
  *   type: Wand, potion, artifact, etc.
  *   creator_id: Who made it
  *   creation_date: When made
  *   appearance: Visual description
  *   materials: What it's made from
  *   magical_properties: What it does
  *   activation_method: How to use it
  *   power_source: Where magic comes from
  *   power_limits: Usage restrictions
  *   recharge_method: How power renews
  *   attunement_requirements: User bond needs
  *   creation_difficulty: Manufacturing challenge
  *   rarity: How uncommon
  *   value: Economic worth
  *   durability: Physical resilience
  *   side_effects: Unintended consequences
  *   cultural_significance: Historical importance
  *   image_reference: Visual representation
  *   description: Detailed description

Enchantment Process Template

  *   id: Unique identifier
  *   name: Name of the process
  *   cultures_using: Who employs it
  *   discovery_date: When developed
  *   discoverer_id: Who developed it
  *   compatible_materials: What can be enchanted
  *   incompatible_materials: What resists enchantment
  *   energy_source: Power required
  *   ritual_components: Physical needs
  *   incantations: Words needed
  *   timeframe: How long it takes
  *   practitioner_requirements: Who can perform it
  *   success_rate: Failure frequency
  *   stability: How long it lasts
  *   limitations: What it can't do
  *   risks: Dangers involved
  *   countering_method: How to break enchantments
  *   societal_regulations: Legal controls
  *   evolution: How it's changed
  *   image_reference: Visual representation
  *   description: Detailed description

Magical Creature Template

  *   id: Unique identifier
  *   name: Species name
  *   type: Beast, construct, spirit, etc.
  *   origin_theory: How they came to be
  *   habitat: Where they live
  *   appearance: Visual description
  *   size_range: Physical dimensions
  *   diet: Food requirements
  *   lifespan: Longevity
  *   reproductive_method: How they multiply
  *   intelligence_level: Mental capacity
  *   magical_abilities: Special powers
  *   magical_components: Valuable parts
  *   threat_level: Danger rating
  *   social_structure: Group dynamics
  *   relationship_with_sapients: Human interaction
  *   cultural_significance: Role in society
  *   conservation_status: Population health
  *   image_reference: Visual representation
  *   description: Detailed description

Magical Location Template

  *   id: Unique identifier
  *   name: Name of the location
  *   type: Ley line, nexus, sanctum, etc.
  *   location_coordinates: Physical position
  *   discovery_date: When found
  *   discoverer_id: Who found it
  *   size: Area affected
  *   magical_properties: Effects produced
  *   visibility: Detection methods
  *   stability: Constancy vs. fluctuation
  *   origin_theory: How it formed
  *   activation_triggers: What starts effects
  *   dangers: Risks to visitors
  *   guardians: Protective entities
  *   access_restrictions: Who can enter
  *   cultural_significance: Historical importance
  *   research_status: Understanding level
  *   associated_entities: Connected beings
  *   image_reference: Visual representation
  *   description: Detailed description

Other Realm Template

  *   id: Unique identifier
  *   name: Name of the realm
  *   type: Heaven, hell, fey realm, etc.
  *   relation_to_material: Physical connection
  *   size: Dimensions/scope
  *   time_flow: Relative to material world
  *   physical_laws: Rules of reality
  *   access_points: Entry methods
  *   inhabitants: Who/what lives there
  *   ruling_entities: Power structures
  *   environment: Landscape/conditions
  *   magical_properties: Special features
  *   dangers: Risks to visitors
  *   resources: Valuable elements
  *   cultural_perception: How it's viewed
  *   historical_interactions: Contact events
  *   research_status: Understanding level
  *   image_reference: Visual representation
  *   description: Detailed description

Spirit/Entity Template

  *   id: Unique identifier
  *   name: Name or type
  *   category: Ghost, elemental, demon, etc.
  *   origin: How they come to exist
  *   appearance: Visual manifestation
  *   composition: Physical vs. ethereal
  *   intelligence_level: Mental capacity
  *   communication_methods: How they interact
  *   powers: Special abilities
  *   weaknesses: Vulnerabilities
  *   habitat: Where they exist
  *   motivation: What drives them
  *   interaction_with_mortals: Human relations
  *   lifespan: Temporal existence
  *   banishment_methods: How to remove
  *   binding_methods: How to control
  *   cultural_perception: How they're viewed
  *   research_status: Understanding level
  *   image_reference: Visual representation
  *   description: Detailed description

Afterlife System Template

  *   id: Unique identifier
  *   name: Name of the system
  *   religions_associated: Connected faiths
  *   entry_method: How souls arrive
  *   judgment_process: Evaluation system
  *   realms_within: Different areas
  *   soul_state: Form of existence
  *   time_experience: How time passes
  *   interaction_with_living: Contact possibility
  *   return_possibilities: Reincarnation/resurrection
  *   ruling_entities: Who governs
  *   purpose: Cosmic function
  *   evidence_level: Proof available
  *   cultural_perception: How it's viewed
  *   variations_by_culture: Different beliefs
  *   research_status: Understanding level
  *   image_reference: Visual representation
  *   description: Detailed description

Prophecy System Template

  *   id: Unique identifier
  *   name: Name of the system
  *   cultures_using: Who believes it
  *   mechanism: How it works
  *   accuracy_level: Success rate
  *   specificity_level: Detail precision
  *   interpretation_requirements: Reading method
  *   reception_methods: How prophecies come
  *   eligible_receivers: Who can prophesy
  *   timeframe_covered: Future span seen
  *   alteration_possibility: Can be changed
  *   famous_prophecies: Notable examples
  *   failed_prophecies: Notable misses
  *   verification_method: How confirmed
  *   cultural_impact: Social effects
  *   research_status: Understanding level
  *   image_reference: Visual representation
  *   description: Detailed description

Paranormal Ability Template

  *   id: Unique identifier
  *   name: Name of the ability
  *   type: Telepathy, telekinesis, etc.
  *   manifestation_trigger: How it develops
  *   power_source: Energy origin
  *   mechanism: How it functions
  *   effect_range: Distance effective
  *   power_level: Strength/impact
  *   control_difficulty: Mastery challenge
  *   side_effects: User consequences
  *   detection_methods: How identified
  *   training_techniques: Development approach
  *   genetic_factor: Hereditary aspect
  *   frequency_in_population: How common
  *   social_perception: How it's viewed
  *   research_status: Understanding level
  *   countermeasures: How to block
  *   image_reference: Visual representation
  *   description: Detailed description

Mythical Artifact Template

  *   id: Unique identifier
  *   name: Name of the artifact
  *   type: Weapon, tool, jewelry, etc.
  *   origin_culture_ids: Societies that created it
  *   creation_myth: Story of its making
  *   creator_id: Who made it (deity, hero, etc.)
  *   age: How old it is
  *   materials: What it's made from
  *   appearance: Physical description
  *   powers: Magical abilities
  *   limitations: Restrictions on use
  *   activation_method: How powers are triggered
  *   current_location: Where it is now
  *   previous_owners_ids: Who possessed it
  *   historical_impact: Effects on events
  *   cultural_significance: Importance in stories
  *   authenticity_debates: Questions about existence
  *   seekers_ids: Who's looking for it
  *   protective_measures: What guards it
  *   image_reference: Visual representation
  *   description: Detailed description

Temporal & Historical Elements

Calendar System Template

  *   id: Unique identifier
  *   name: Name of the system
  *   cultures_using: Who employs it
  *   creation_date: When established
  *   creator_id: Who established it
  *   day_structure: Hours/minutes system
  *   week_structure: Days per week
  *   month_structure: Days/weeks per month
  *   year_structure: Months per year
  *   era_divisions: Historical periods
  *   astronomical_basis: Celestial alignments
  *   seasonal_alignment: Weather patterns
  *   leap_adjustments: Correction methods
  *   dating_notation: Year numbering
  *   significant_dates: Important days
  *   timekeeping_tools: Clocks/calendars
  *   cultural_significance: Social importance
  *   accuracy: Astronomical precision
  *   image_reference: Visual representation
  *   description: Detailed description

Holiday/Observance Template

  *   id: Unique identifier
  *   name: Name of the holiday/observance
  *   calendar_system_id: Parent calendar system
  *   type: Religious, secular, seasonal, commemorative, etc.
  *   date_or_timing: When it occurs
  *   calculation_method: How date is determined (fixed, lunar, etc.)
  *   duration: Length of observance
  *   frequency: Annual, monthly, once-in-lifetime, etc.
  *   origin_date: When first established
  *   founder_id: Who established it
  *   historical_basis: Event it commemorates
  *   religious_significance: Spiritual meaning
  *   cultural_significance: Social importance
  *   official_status: Government recognition
  *   participation_level: Public vs. private
  *   traditional_activities: Customary practices
  *   ceremonial_elements: Rituals performed
  *   symbolic_elements: Meaningful imagery
  *   traditional_foods: Special cuisine
  *   traditional_attire: Special clothing
  *   decorations: Visual elements
  *   music_and_arts: Creative expressions
  *   gift_giving_customs: Exchange traditions
  *   regional_variations: Local differences
  *   evolution: How it's changed over time
  *   modern_observance: Current practices
  *   commercial_aspects: Economic impact
  *   image_reference: Visual representation
  *   description: Detailed description

Historical Era Template

  *   id: Unique identifier
  *   name: Name of the era
  *   timeframe: Start and end dates
  *   preceding_era_id: What came before
  *   following_era_id: What came after
  *   defining_characteristics: Key features
  *   major_powers: Dominant nations
  *   political_landscape: Government systems
  *   social_structure: Class organization
  *   economic_systems: Trade/production
  *   technological_level: Advancement stage
  *   cultural_trends: Artistic movements
  *   religious_developments: Faith evolution
  *   conflicts: Major wars
  *   scientific_discoveries: Important findings
  *   ending_causes: Why it concluded
  *   historical_figures: Important people
  *   legacy: Lasting impact
  *   image_reference: Visual representation
  *   description: Detailed description

Historical Event Template

  *   id: Unique identifier
  *   name: Name of the event
  *   type: War, discovery, disaster, etc.
  *   date_timeframe: When it occurred
  *   location_ids: Where it happened
  *   primary_causes: What led to it
  *   key_participants: People involved
  *   immediate_effects: Short-term impact
  *   long_term_consequences: Lasting effects
  *   cultural_significance: Social importance
  *   modern_perception: Current views
  *   commemoration: How remembered
  *   controversies: Disputed aspects
  *   alternate_interpretations: Different views
  *   dramatizations: Creative depictions
  *   research_status: Historical understanding
  *   image_reference: Visual representation
  *   description: Detailed description

Creation Myth Template

  *   id: Unique identifier
  *   name: Name of the myth
  *   cultures_believing: Who accepts it
  *   deities_involved: Divine participants
  *   cosmic_elements: Universe components
  *   creation_process: How things formed
  *   timeframe: Duration described
  *   first_beings: Initial life forms
  *   world_features_explained: Natural elements
  *   moral_lessons: Ethical teachings
  *   variants: Different versions
  *   contradictions: Logical issues
  *   scientific_compatibility: Fact alignment
  *   cultural_influence: Social impact
  *   modern_relevance: Current importance
  *   image_reference: Visual representation
  *   description: Detailed description

Apocalyptic Event Template

  *   id: Unique identifier
  *   name: Name of the event
  *   type: Past or prophesied
  *   cultures_believing: Who accepts it
  *   timeframe: When it occurs/occurred
  *   warning_signs: Precursor events
  *   triggering_causes: What starts it
  *   divine_involvement: God participation
  *   mortals_role: Human contribution
  *   survival_possibilities: Who lives
  *   aftermath_description: What remains
  *   post-apocalyptic_world: New reality
  *   prevention_methods: How to stop it
  *   cultural_influence: Social impact
  *   scientific_basis: Factual elements
  *   modern_relevance: Current importance
  *   image_reference: Visual representation
  *   description: Detailed description

Historical Pattern Template

  *   id: Unique identifier
  *   name: Name of the pattern
  *   type: Political, economic, cultural, etc.
  *   cycle_length: Time between repetitions
  *   first_observed: When identified
  *   observer_id: Who identified it
  *   typical_phases: Stages of cycle
  *   triggering_factors: What starts it
  *   cultures_affected: Who experiences it
  *   current_phase: Present position
  *   historical_examples: Past occurrences
  *   exceptions: When pattern broke
  *   predictive_value: Future forecasts
  *   scientific_validity: Evidence level
  *   cultural_awareness: Public knowledge
  *   counter-strategies: Breaking the cycle
  *   image_reference: Visual representation
  *   description: Detailed description

Migration Event Template

  *   id: Unique identifier
  *   name: Name of the migration
  *   timeframe: When it occurred
  *   origin_location_id: Starting point
  *   destination_location_ids: Ending points
  *   population_size: Number of migrants
  *   species_ids: Who migrated
  *   primary_causes: Why they moved
  *   journey_challenges: Travel difficulties
  *   reception_at_destination: How received
  *   cultural_exchange: Idea transfers
  *   genetic_impact: Population effects
  *   technologies_transferred: Skills spread
  *   settlement_patterns: Living arrangements
  *   long-term_consequences: Lasting impact
  *   historical_evidence: Proof sources
  *   image_reference: Map/visualization
  *   description: Detailed description

Rise/Fall of Civilization Template

  *   id: Unique identifier
  *   name: Name of civilization
  *   species_id: Primary species
  *   timeframe: Duration of existence
  *   territory_ids: Geographic reach
  *   cultural_highlights: Notable achievements
  *   technological_innovations: Important advances
  *   societal_structure: Organization system
  *   rise_factors: Why it grew
  *   peak_description: Height of power
  *   decline_causes: Why it fell
  *   collapse_speed: How quickly it ended
  *   successor_states: What followed
  *   rediscovery: When/how found
  *   modern_relevance: Current importance
  *   archaeological_evidence: Physical proof
  *   image_reference: Visual representation
  *   description: Detailed description

Technological Revolution Template

  *   id: Unique identifier
  *   name: Name of the revolution
  *   timeframe: When it occurred
  *   preceding_paradigm_id: Previous system
  *   new_paradigm_id: New system
  *   key_innovations: Critical advances
  *   key_innovators: Important people
  *   adoption_rate: Spread speed
  *   resistance_factors: Opposition sources
  *   economic_impact: Market effects
  *   social_impact: Cultural changes
  *   power_shifts: Political consequences
  *   environmental_impact: Ecological effects
  *   geographical_origin: Where it started
  *   geographical_spread: How it spread
  *   modern_relevance: Current importance
  *   image_reference: Visual representation
  *   description: Detailed description

War/Conflict Template

  *   id: Unique identifier
  *   name: Name of the conflict
  *   type: Civil war, world war, etc.
  *   timeframe: Duration
  *   location_ids: Where it occurred
  *   combatants: Opposing sides
  *   causes: Why it started
  *   major_battles: Important engagements
  *   casualty_count: Deaths/injuries
  *   technologies_used: Weapons/tactics
  *   resolution_method: How it ended
  *   peace_terms: Settlement conditions
  *   territorial_changes: Border effects
  *   political_consequences: Power shifts
  *   cultural_impact: Social effects
  *   commemorations: How remembered
  *   image_reference: Visual representation
  *   description: Detailed description

Military Operation Template

  *   id: Unique identifier
  *   name: Operation codename/designation
  *   war_conflict_id: Parent conflict
  *   operation_type: Offensive, defensive, intelligence, etc.
  *   timeframe: Start and end dates
  *   location_id: Geographic area
  *   commanding_officer_id: Operation leader
  *   forces_involved: Units participating
  *   opposing_forces: Enemy units
  *   objective: Primary goal
  *   secondary_objectives: Additional goals
  *   strategic_context: Larger military situation
  *   planning_phase: Preparation details
  *   execution_phase: How operation unfolded
  *   phases: Distinct stages of operation
  *   tactics_employed: Combat methods used
  *   equipment_utilized: Key weapons/technology
  *   intelligence_factors: Information gathering
  *   logistical_elements: Supply considerations
  *   civilian_considerations: Non-combatant factors
  *   outcome: Success, failure, or partial success
  *   casualties: Losses sustained
  *   strategic_impact: Effect on larger conflict
  *   historical_significance: Long-term importance
  *   image_reference: Visual representation
  *   description: Detailed description

Battle/Campaign Template

  *   id: Unique identifier
  *   name: Name of the battle/campaign
  *   war_conflict_id: Parent conflict
  *   type: Battle, siege, naval engagement, campaign, etc.
  *   scale: Skirmish, major battle, decisive engagement, etc.
  *   start_date: When it began
  *   end_date: When it concluded
  *   location_id: Where it took place
  *   terrain_features: Geographical elements
  *   weather_conditions: Environmental factors
  *   combatant_sides: Opposing forces
  *   commander_ids: Leaders of each side
  *   force_size_side_a: Number of troops (side A)
  *   force_size_side_b: Number of troops (side B)
  *   force_composition_side_a: Types of units (side A)
  *   force_composition_side_b: Types of units (side B)
  *   initial_deployment: Starting positions
  *   battle_phases: Stages of the engagement
  *   key_maneuvers: Critical tactical moves
  *   technological_advantages: Superior weapons/equipment
  *   casualties_side_a: Losses suffered (side A)
  *   casualties_side_b: Losses suffered (side B)
  *   outcome: Victor and immediate result
  *   strategic_significance: Impact on the larger conflict
  *   historical_importance: Long-term significance
  *   controversies: Disputed aspects
  *   commemorations: Memorials and observances
  *   image_reference: Maps/visual representation
  *   description: Detailed description

Natural Disaster Template

  *   id: Unique identifier
  *   name: Name of the disaster
  *   type: Earthquake, flood, etc.
  *   date: When it occurred
  *   location_ids: Where it happened
  *   magnitude: Strength/size
  *   duration: How long it lasted
  *   warning_signs: Precursor events
  *   immediate_effects: Direct impact
  *   casualty_count: Deaths/injuries
  *   property_damage: Destruction level
  *   environmental_impact: Ecological effects
  *   response_actions: Aid efforts
  *   recovery_process: Rebuilding phase
  *   prevention_measures: Future safeguards
  *   cultural_impact: Social effects
  *   image_reference: Visual representation
  *   description: Detailed description

Historical Cultural Exchange Template

  *   id: Unique identifier
  *   name: Name of the exchange
  *   timeframe: When it occurred
  *   participating_cultures: Who involved
  *   catalyzing_event: What started it
  *   contact_method: How they met
  *   power_dynamic: Relationship balance
  *   ideas_transferred: Concepts shared
  *   technologies_shared: Skills spread
  *   artistic_influences: Creative impacts
  *   linguistic_borrowings: Language effects
  *   religious_syncretism: Faith blending
  *   resistance_elements: Opposition forms
  *   long-term_consequences: Lasting effects
  *   historical_documentation: Evidence sources
  *   modern_relevance: Current importance
  *   image_reference: Visual representation
  *   description: Detailed description

Historical Figure Template

  *   id: Unique identifier
  *   name: Full name
  *   birth_date: When born
  *   death_date: When died
  *   birth_location_id: Where born
  *   species_id: Species/race
  *   culture_ids: Cultural background
  *   titles_held: Official positions
  *   occupation: Primary activities
  *   major_achievements: Notable accomplishments
  *   personality_traits: Character aspects
  *   ideological_positions: Beliefs held
  *   allies_ids: Friends/supporters
  *   enemies_ids: Opponents/rivals
  *   historical_impact: Lasting influence
  *   controversies: Disputed actions
  *   cultural_legacy: How remembered
  *   fictional_portrayals: Creative depictions
  *   image_reference: Visual representation
  *   description: Detailed description
Cult/Fringe Belief Template

  *   id: Unique identifier
  *   name: Name of the cult or belief
  *   parent_religion_id: Related mainstream belief (if any)
  *   founder_id: Creator or prophet
  *   founding_date: Date of origin
  *   core_beliefs: Main tenets
  *   rituals: Practices and ceremonies
  *   sacred_texts: Important writings
  *   membership_requirements: Entry conditions
  *   organizational_structure: Leadership hierarchy
  *   sacred_sites: Associated places
  *   relationship_to_government: Legal status
  *   relationship_to_mainstream_religion: Doctrinal ties or conflicts
  *   known_conflicts: Historical or ongoing disputes
  *   symbols: Visual identifiers
  *   image_reference: Visual representation
  *   description: Detailed description

Supernatural Event Template

  *   id: Unique identifier
  *   name: Name of the phenomenon
  *   event_type: Haunting, dimensional rift, miracle, etc.
  *   location_id: Where it occurred
  *   first_occurrence_date: Earliest recorded event
  *   recurrence_pattern: Frequency (if any)
  *   witness_accounts: Descriptions by observers
  *   measurable_effects: Physical, emotional, or environmental changes
  *   entities_involved: Spirits, gods, otherworldly beings
  *   investigating_authorities: Who documented or investigated it
  *   explanations: Scientific, religious, or magical theories
  *   associated_myths: Stories tied to the event
  *   image_reference: Visual representation
  *   description: Detailed description

Sacred Site Template

  *   id: Unique identifier
  *   name: Site name
  *   location_id: Geographic location
  *   associated_religion_id: Related belief system
  *   deity_or_figure_id: Honored entity
  *   established_date: When it became sacred
  *   rituals_performed: Key ceremonies held there
  *   pilgrimage_patterns: Who visits and how often
  *   physical_features: Notable geography or structures
  *   protection_status: Legal/religious safeguards
  *   conflicts: Disputes or wars over control
  *   annual_visitation: Number of visitors
  *   cultural_significance: Symbolic role
  *   image_reference: Visual representation
  *   description: Detailed description

Mythology Entry Template

  *   id: Unique identifier
  *   name: Title or subject
  *   culture_id: Originating culture
  *   associated_religion_id: Related religion
  *   story_type: Creation myth, hero tale, moral fable, etc.
  *   main_characters: Gods, heroes, monsters
  *   core_themes: Values or lessons
  *   cosmological_elements: Origins of universe, time, life
  *   influence_on_law: Legal or moral rules derived from it
  *   festivals_linked: Celebrations based on story
  *   story_variants: Regional or sectarian versions
  *   first_recorded: When it was documented
  *   image_reference: Visual representation
  *   description: Detailed description

Status Symbol Template

  *   id: Unique identifier
  *   name: Item or behavior
  *   type: Material (jewelry) or behavioral (duel rights, accent)
  *   class_association: Linked social tier
  *   cultural_context_id: Society where it's valued
  *   accessibility: Who can attain it
  *   symbolic_meaning: What it conveys
  *   historical_evolution: How it changed over time
  *   associated_risks: Theft, envy, legal issues
  *   counterfeits_exist: True/false
  *   cultural_significance: Representation in art/media
  *   image_reference: Visual representation
  *   description: Detailed description

Cultural Exchange Template

  *   id: Unique identifier
  *   name: Title of the exchange instance
  *   origin_culture_id: Source society
  *   recipient_culture_id: Receiving society
  *   exchange_type: Language, food, clothing, tech, etc.
  *   time_period: When it occurred
  *   mechanism: Trade, conquest, migration, etc.
  *   resistance_or_adaptation: How it was received
  *   long_term_effects: Lingering cultural shifts
  *   notable_figures: People who enabled or resisted
  *   conflicts_arisen: Cultural clashes
  *   related_artifacts: Objects from the exchange
  *   image_reference: Visual representation
  *   description: Detailed description

Public Transit System Template

  *   id: Unique identifier
  *   name: System name
  *   urban_area_id: City or area served
  *   vehicle_types: Bus, rail, airship, magical gates, etc.
  *   operator_entity_id: Who runs it
  *   construction_date: When it was built
  *   coverage_area: Range or route map
  *   ridership_statistics: Usage metrics
  *   fare_structure: Pricing tiers
  *   accessibility_rating: Accommodations for disabilities
  *   safety_record: Incident history
  *   maintenance_system: Upkeep practices
  *   economic_impact: Job creation, real estate, etc.
  *   image_reference: Visual representation
  *   description: Detailed description

Freight Transport Template

  *   id: Unique identifier
  *   name: Network or service name
  *   transport_modes: Land, sea, air, space, magical
  *   operating_entity_id: Carrier or guild
  *   cargo_types: Materials commonly moved
  *   origin_nodes: Major hubs of origin
  *   destination_nodes: Typical delivery points
  *   route_efficiency: Time and cost metrics
  *   infrastructure_used: Roads, rails, portals, docks
  *   trade_volume: Throughput or weight/year
  *   security_measures: Guarding or warding
  *   accident_rate: Risk and incidents
  *   legal_regulations: Transport laws
  *   strategic_value: Economic or military importance
  *   image_reference: Visual representation
  *   description: Detailed description


Interactive Systems & Game Mechanics

Game Rule System Template

  *   id: Unique identifier
  *   name: Name of the rule system
  *   game_type: Board game, TTRPG, video game, etc.
  *   core_mechanics: Fundamental resolution systems
  *   turn_structure: How play progresses between participants
  *   action_economy: Available actions per turn/round
  *   randomization_method: Dice, cards, RNG, etc.
  *   success_determination: How outcomes are decided
  *   failure_consequences: Results of unsuccessful actions
  *   resource_management: How game resources are tracked
  *   player_count: Supported number of participants
  *   play_time: Expected duration of gameplay
  *   complexity_level: Learning curve difficulty
  *   balance_mechanisms: How fairness is maintained
  *   exception_handling: Rules for edge cases
  *   variant_rules: Optional or situational mechanics
  *   compatibility: Works with other systems/editions
  *   designer_notes: Creator's commentary on intent
  *   playtesting_feedback: Results of gameplay testing
  *   image_reference: Visual representation
  *   description: Detailed description

Game Mechanic Template

  *   id: Unique identifier
  *   name: Name of the game mechanic
  *   game_system_id: Parent rule system
  *   mechanic_type: Core, supplemental, variant, etc.
  *   interaction_point: When/how it comes into play
  *   player_action: What the player does
  *   system_response: What the game does
  *   input_method: How players engage with it
  *   feedback_method: How results are communicated
  *   resource_costs: What players spend to use it
  *   cooldown/limitations: Restrictions on usage
  *   strategic_depth: Complexity of decisions involved
  *   skill_vs_chance: Balance between ability and randomness
  *   learning_curve: Difficulty to master
  *   player_satisfaction: Emotional response generated
  *   synergy_mechanics: How it interacts with other systems
  *   counter_mechanics: What opposes or balances it
  *   edge_cases: Unusual interaction scenarios
  *   balance_history: Changes made during development
  *   player_exploits: Potential abuse vectors
  *   accessibility_considerations: Usability factors
  *   visual_representation: How it appears in game
  *   audio_cues: Sound design elements
  *   image_reference: Visual representation
  *   description: Detailed description

Character Class/Archetype Template

  *   id: Unique identifier
  *   name: Name of the class/archetype
  *   game_system_id: Associated rule system
  *   primary_role: Main function in gameplay
  *   secondary_roles: Alternative functions
  *   starting_attributes: Base statistics
  *   starting_equipment: Initial items/gear
  *   special_abilities: Unique powers/features
  *   restrictions: Limitations and weaknesses
  *   advancement_path: How it develops over time
  *   resource_pools: Class-specific currencies/points
  *   playstyle: How it feels to play this class
  *   difficulty_level: Complexity for players
  *   synergies: Works well with these other classes
  *   counters: Weak against these opponents/situations
  *   flavor_elements: Thematic aspects
  *   iconic_representatives: Famous examples
  *   balance_considerations: Power level notes
  *   variants: Subclasses or alternative versions
  *   image_reference: Visual representation
  *   description: Detailed description

Ability/Skill System Template

  *   id: Unique identifier
  *   name: Name of the ability/skill system
  *   game_system_id: Associated rule system
  *   skill_categories: Groupings of related abilities
  *   skill_acquisition: How abilities are gained
  *   skill_progression: How abilities improve
  *   skill_caps: Maximum proficiency levels
  *   skill_decay: Whether/how abilities diminish
  *   skill_synergies: How abilities complement each other
  *   skill_conflicts: Mutually exclusive abilities
  *   skill_checks: How proficiency is tested
  *   skill_specialization: Focused expertise options
  *   skill_display: How abilities appear to players
  *   passive_vs_active: Automatic vs. triggered abilities
  *   skill_costs: Resources required to use
  *   skill_cooldowns: Time between uses
  *   skill_prerequisites: Requirements to learn
  *   skill_balance: Relative power considerations
  *   image_reference: Visual representation
  *   description: Detailed description

Ability/Skill Tree Template

  *   id: Unique identifier
  *   name: Name of the ability/skill tree
  *   ability_system_id: Parent ability/skill system
  *   game_system_id: Associated rule system
  *   tree_structure: Branching pattern
  *   node_count: Number of abilities in tree
  *   entry_points: Initial accessible abilities
  *   progression_path: Typical advancement route
  *   tier_organization: Ability power levels
  *   node_connections: Prerequisites between abilities
  *   specialization_branches: Focused sub-paths
  *   mutually_exclusive_paths: Cannot have both
  *   point_allocation: How advancement is spent
  *   respec_options: Changing previous choices
  *   capstone_abilities: Ultimate powers at end
  *   synergy_bonuses: Benefits for specific combinations
  *   visual_presentation: How tree appears to players
  *   balance_considerations: Power distribution
  *   thematic_elements: Flavor and story connections
  *   image_reference: Visual representation
  *   description: Detailed description







Quest/Mission Structure Template

  *   id: Unique identifier
  *   name: Name of the quest structure
  *   game_system_id: Associated rule system
  *   quest_types: Categories of objectives
  *   quest_givers: Who/what assigns quests
  *   objective_variety: Different goals within quests
  *   quest_markers: How objectives are indicated
  *   completion_conditions: Requirements to finish
  *   failure_conditions: Ways quests can be failed
  *   time_constraints: Deadlines if applicable
  *   quest_chains: Series of connected missions
  *   branching_paths: Decision points and consequences
  *   quest_rewards_id: Associated reward distribution
  *   quest_difficulty_scaling: How challenge adjusts
  *   quest_tracking: How progress is monitored
  *   repeatable_quests: Reusable mission types
  *   unique_quests: One-time special missions
  *   narrative_integration: Connection to story
  *   quest_availability: Requirements to access
  *   image_reference: Visual representation
  *   description: Detailed description

Individual Quest Template

  *   id: Unique identifier
  *   name: Name of the quest
  *   quest_structure_id: Parent quest system
  *   game_system_id: Associated rule system
  *   type: Main, side, faction, daily, etc.
  *   category: Fetch, escort, kill, explore, etc.
  *   quest_giver_id: NPC or object that assigns it
  *   quest_giver_dialogue: Initial conversation text
  *   prerequisite_quests_ids: Required prior quests
  *   level_requirement: Minimum player level
  *   other_requirements: Skills, items, reputation needed
  *   location_ids: Where quest takes place
  *   primary_objective: Main goal
  *   secondary_objectives: Optional goals
  *   objective_markers: How goals are indicated
  *   step_by_step_progression: Sequential tasks
  *   time_limit: Deadline if applicable
  *   involved_npcs_ids: Characters in the quest
  *   involved_items_ids: Objects in the quest
  *   quest_enemies_ids: Opponents to overcome
  *   failure_conditions: Ways to fail the quest
  *   failure_consequences: Results of failing
  *   rewards_xp: Experience gained
  *   rewards_items: Items received
  *   rewards_currency: Money earned
  *   rewards_reputation: Standing changes
  *   rewards_other: Special benefits
  *   narrative_text: Story elements
  *   player_choices: Decision points
  *   choice_consequences: Results of decisions
  *   quest_journal_text: How it appears in log
  *   completion_dialogue: Final conversation text
  *   repeatable: Can be done multiple times
  *   cooldown_period: Time before repeatable
  *   image_reference: Visual representation
  *   description: Detailed description

Achievement System Template

  *   id: Unique identifier
  *   name: Name of the achievement system
  *   game_system_id: Associated rule system
  *   achievement_categories: Types of accomplishments
  *   difficulty_tiers: Levels of challenge
  *   visibility_rules: Hidden vs. visible achievements
  *   unlock_conditions: Requirements for completion
  *   reward_types: Benefits for achievement completion
  *   achievement_points: Value/scoring system
  *   achievement_ui: How displayed to players
  *   completion_tracking: How progress is monitored
  *   achievement_notifications: How players are informed
  *   meta_achievements: Rewards for multiple completions
  *   achievement_statistics: Player completion rates
  *   social_features: Sharing and comparison tools
  *   achievement_artwork: Visual rewards
  *   achievement_sound: Audio cues for completion
  *   platform_integration: Connection to external systems
  *   image_reference: Visual representation
  *   description: Detailed description

Combat System Template

  *   id: Unique identifier
  *   name: Name of the combat system
  *   game_system_id: Associated rule system
  *   initiative_mechanics: Turn order determination
  *   action_types: Categories of combat moves
  *   targeting_system: How targets are selected
  *   damage_types: Categories of harm
  *   defense_mechanisms: Protection methods
  *   hit_determination: Accuracy calculation
  *   critical_mechanics: Special success/failure rules
  *   status_effects: Temporary conditions
  *   positioning_rules: Movement and location importance
  *   terrain_effects: Environmental factors
  *   combat_pacing: Flow and rhythm of encounters
  *   recovery_mechanics: Healing and restoration
  *   death/defeat_consequences: Results of losing
  *   combat_rewards_id: Associated loot system
  *   encounter_scaling: Difficulty adjustment
  *   combat_ui: Information display during fights
  *   image_reference: Visual representation
  *   description: Detailed description

Combat Move Template

  *   id: Unique identifier
  *   name: Name of the combat move
  *   combat_system_id: Parent combat system
  *   game_system_id: Associated rule system
  *   type: Attack, defense, utility, movement, etc.
  *   action_cost: Resources required to use
  *   cooldown: Time before reuse
  *   range: Distance effective
  *   area_of_effect: Space affected
  *   targeting_requirements: What can be targeted
  *   execution_time: How long to perform
  *   damage_type: Kind of harm inflicted
  *   damage_calculation: How harm is determined
  *   status_effects_applied: Conditions imposed
  *   status_effect_duration: How long conditions last
  *   status_effect_potency: Condition strength
  *   defensive_properties: Protection provided
  *   movement_properties: Positioning changes
  *   prerequisites: Requirements to use
  *   class_restrictions: Who can use it
  *   equipment_requirements: Gear needed
  *   synergies: Combos with other moves
  *   counters: Moves that defeat it
  *   tactical_applications: Strategic uses
  *   animation: Visual representation
  *   sound_effects: Audio cues
  *   player_feedback: How results are shown
  *   ai_usage_parameters: How NPCs employ it
  *   balance_considerations: Power level notes
  *   image_reference: Visual representation
  *   description: Detailed description

Crafting System Template

  *   id: Unique identifier
  *   name: Name of the crafting system
  *   game_system_id: Associated rule system
  *   resource_system_id: Associated resources
  *   economy_id: Associated economic system
  *   crafting_methods: Techniques for creation
  *   recipe_discovery: How crafting options are learned
  *   ingredient_types: Categories of materials
  *   tool_requirements: Equipment needed
  *   skill_requirements: Abilities needed
  *   quality_variations: Different result tiers
  *   crafting_time: Duration of creation process
  *   critical_success/failure: Special results
  *   durability_mechanics: Item longevity
  *   repair_systems: Fixing damaged items
  *   customization_options: Personalization features
  *   economic_impact: Effect on game economy
  *   crafting_locations: Where crafting occurs
  *   crafting_animations: Visual process representation
  *   image_reference: Visual representation
  *   description: Detailed description

Item Crafting Template

  *   id: Unique identifier
  *   name: Name of the craftable item
  *   game_system_id: Associated rule system
  *   crafting_system_id: Associated crafting system
  *   item_type: Weapon, armor, consumable, etc.
  *   rarity_tier: Common, uncommon, rare, etc.
  *   required_ingredients: Materials needed
  *   ingredient_quantities: Amount of each material
  *   crafting_station: Where it can be crafted
  *   required_tools: Equipment needed
  *   required_skill_level: Proficiency needed
  *   crafting_time: Duration to complete
  *   crafting_difficulty: Challenge rating
  *   success_chance: Probability of completion
  *   critical_success_effects: Special positive outcomes
  *   failure_consequences: Results of failed crafting
  *   item_properties: Stats and attributes
  *   durability: Item lifespan
  *   repair_options: Maintenance methods
  *   appearance_variations: Visual options
  *   stat_variations: Randomized properties
  *   upgrade_paths: Enhancement options
  *   recipe_source: How players learn to craft it
  *   market_value: Economic worth
  *   crafting_animation: Visual process
  *   crafting_sound_effects: Audio during creation
  *   image_reference: Visual representation
  *   description: Detailed description

Progression System Template

  *   id: Unique identifier
  *   name: Name of the progression system
  *   game_system_id: Associated rule system
  *   experience_sources: How advancement points are earned
  *   level_structure: Tiers of advancement
  *   milestone_system: Key achievement points
  *   ability_unlocks: New capabilities gained
  *   stat_improvements: Numerical enhancements
  *   progression_pacing: Rate of advancement
  *   power_curve: Growth rate over time
  *   level_caps: Maximum advancement limits
  *   prestige_mechanics: Resetting for benefits
  *   specialization_paths: Advancement choices
  *   respec_options: Changing previous choices
  *   progression_display: How advancement is shown
  *   catch-up_mechanics: Helping behind players
  *   endgame_content: Activities at maximum level
  *   progression_rewards: Benefits of advancement
  *   image_reference: Visual representation
  *   description: Detailed description



NPC Interaction System Template

  *   id: Unique identifier
  *   name: Name of the NPC system
  *   game_system_id: Associated rule system
  *   interaction_types: Ways to engage with NPCs
  *   dialogue_system: Conversation mechanics
  *   relationship_tracking: How NPC attitudes are measured
  *   influence_mechanics: Persuasion and manipulation
  *   faction_system_id: Associated reputation system
  *   npc_memory: How NPCs recall player actions
  *   npc_schedules: Time-based behavior patterns
  *   npc_needs: Motivations and requirements
  *   service_offerings: What NPCs provide to players
  *   npc_reactions: Responses to player actions
  *   companion_mechanics: Follower systems
  *   npc_advancement: How NPCs develop over time
  *   npc_customization: Changing NPC appearance/behavior
  *   npc_creation_system: How new NPCs are generated
  *   npc_ai_complexity: Behavioral sophistication
  *   image_reference: Visual representation
  *   description: Detailed description

Random Encounter Template

  *   id: Unique identifier
  *   name: Name of the encounter system
  *   game_system_id: Associated rule system
  *   encounter_types: Categories of random events
  *   trigger_conditions: What causes encounters
  *   frequency_controls: How often encounters occur
  *   location_factors: Where encounters happen
  *   time_factors: When encounters happen
  *   player_level_scaling: Difficulty adjustment
  *   encounter_tables: Probability distributions
  *   special_encounters: Rare or unique events
  *   encounter_chains: Series of connected events
  *   avoidance_mechanics: How to prevent encounters
  *   preparation_options: How to ready for encounters
  *   encounter_rewards_id: Associated loot system
  *   quest_integration_id: Connection to quest system
  *   encounter_variety: Diversity of experiences
  *   encounter_signals: Warning indicators
  *   image_reference: Visual representation
  *   description: Detailed description

Loot/Reward Distribution Template

  *   id: Unique identifier
  *   name: Name of the reward system
  *   game_system_id: Associated rule system
  *   reward_types: Categories of prizes
  *   distribution_methods: How rewards are assigned
  *   rarity_tiers: Levels of item scarcity
  *   drop_tables: Probability distributions
  *   conditional_rewards: Situation-specific prizes
  *   scaling_mechanics: How rewards adjust to players
  *   bad_luck_protection: Preventing repeated failures
  *   jackpot_mechanics: Exceptional reward moments
  *   reward_limits: Caps on acquisition
  *   reward_cooldowns: Time between rewards
  *   collection_incentives: Set completion bonuses
  *   economy_impact: Effect on game economy
  *   economy_id: Associated economic system
  *   reward_presentation: How prizes are revealed
  *   reward_storage: Where items are kept
  *   image_reference: Visual representation
  *   description: Detailed description

Loot Table Template

  *   id: Unique identifier
  *   name: Name of the loot table
  *   reward_distribution_id: Parent reward system
  *   game_system_id: Associated rule system
  *   table_type: Enemy drops, chest contents, quest rewards, etc.
  *   context_id: Specific enemy, chest, or quest this applies to
  *   level_range: Character levels this table is appropriate for
  *   difficulty_tier: Easy, normal, hard, etc.
  *   entry_count: Number of items in the table
  *   guaranteed_items_ids: Always-included rewards
  *   common_items_ids: High probability rewards
  *   uncommon_items_ids: Medium probability rewards
  *   rare_items_ids: Low probability rewards
  *   legendary_items_ids: Very low probability rewards
  *   currency_range: Money/gold amount range
  *   roll_mechanics: How drops are determined
  *   quantity_range: How many items are given
  *   unique_drop_rules: One-time only items
  *   conditional_entries: Context-specific additions
  *   table_variations: Seasonal or event-based changes
  *   drop_rate_modifiers: Factors affecting probabilities
  *   special_rules: Unique table behaviors
  *   image_reference: Visual representation
  *   description: Detailed description



Difficulty Scaling Template

  *   id: Unique identifier
  *   name: Name of the difficulty system
  *   game_system_id: Associated rule system
  *   difficulty_levels: Preset challenge settings
  *   adjustable_parameters: What changes with difficulty
  *   scaling_formula: How challenge increases
  *   dynamic_adjustment: Real-time difficulty changes
  *   player_skill_assessment: How player ability is measured
  *   challenge_curve: Difficulty progression over time
  *   difficulty_spikes: Intentional challenge increases
  *   recovery_mechanics: Easier sections after challenges
  *   accessibility_options: Features for various abilities
  *   hardcore_modes: Extreme challenge options
  *   easy_modes: Reduced challenge options
  *   tutorial_difficulty: New player experience
  *   endgame_difficulty: Maximum challenge content
  *   difficulty_indicators: How challenge is communicated
  *   difficulty_rewards: Benefits of higher challenge
  *   image_reference: Visual representation
  *   description: Detailed description

Player Choice Consequence Template

  *   id: Unique identifier
  *   name: Name of the consequence system
  *   game_system_id: Associated rule system
  *   choice_types: Categories of decisions
  *   immediate_consequences: Short-term effects
  *   delayed_consequences: Long-term effects
  *   visible_consequences: Player-apparent results
  *   hidden_consequences: Concealed results
  *   branching_paths: How choices create divergence
  *   convergence_points: How paths reconnect
  *   choice_tracking: How decisions are recorded
  *   moral_system: Ethical frameworks
  *   faction_impacts: Effects on group relationships
  *   world_state_changes: Environmental alterations
  *   npc_reactions: Character responses to choices
  *   choice_presentation: How options are displayed
  *   choice_limitations: Constraints on decisions
  *   choice_reversibility: Whether decisions can be undone
  *   image_reference: Visual representation
  *   description: Detailed description

Game Session Structure Template

  *   id: Unique identifier
  *   name: Name of the session structure
  *   game_system_id: Associated rule system
  *   session_length: Typical duration
  *   pacing_elements: Rhythm and flow control
  *   save_points: Progress preservation
  *   checkpoints: Restart locations
  *   session_goals: Objectives within one play period
  *   between-session_activities: Offline progression
  *   session_start_rituals: Opening procedures
  *   session_end_rituals: Closing procedures
  *   interruption_handling: Dealing with pauses
  *   player_absence_rules: Handling missing participants
  *   session_rewards: Benefits for completion
  *   session_limits: Caps on play time/rewards
  *   session_recovery: Returning after long breaks
  *   session_summary: Record of events/achievements
  *   meta-session_progression: Campaign advancement
  *   image_reference: Visual representation
  *   description: Detailed description

Puzzle/Challenge Design Template

  *   id: Unique identifier
  *   name: Name of the puzzle system
  *   game_system_id: Associated rule system
  *   puzzle_types: Categories of challenges
  *   solution_mechanics: How puzzles are solved
  *   difficulty_progression: Challenge escalation
  *   hint_systems: Player assistance methods
  *   failure_consequences: Results of incorrect solutions
  *   time_pressure: Urgency elements
  *   environmental_integration: World context
  *   reward_structures: Benefits for completion
  *   puzzle_presentation: How challenges are introduced
  *   accessibility_considerations: Alternate solutions
  *   replayability: Variation between attempts
  *   cooperative_elements: Multi-player interaction
  *   competitive_elements: Player vs. player aspects
  *   puzzle_reset: How challenges return to start state
  *   puzzle_skip_options: Ways to bypass difficult challenges
  *   image_reference: Visual representation
  *   description: Detailed description

Tutorial System Template

  *   id: Unique identifier
  *   name: Name of the tutorial system
  *   game_system_id: Associated rule system
  *   teaching_methodology: Educational approach
  *   information_pacing: Knowledge distribution rate
  *   interactive_elements: Learning by doing
  *   passive_elements: Learning by observing
  *   mandatory_sections: Required training
  *   optional_sections: Supplemental training
  *   skill_verification: Proficiency testing
  *   contextual_help: Situation-specific guidance
  *   reference_materials: Documentation access
  *   tutorial_integration: Connection to main game
  *   tutorial_characters: Teaching guides/mentors
  *   tutorial_rewards: Benefits for completion
  *   tutorial_skipping: Options to bypass
  *   advanced_tutorials: Expert-level training
  *   refresher_tutorials: Returning player assistance
  *   accessibility_features: Learning accommodations
  *   image_reference: Visual representation
  *   description: Detailed description

Dialogue System Template

  *   id: Unique identifier
  *   name: Name of the dialogue system
  *   game_system_id: Associated rule system
  *   npc_system_id: Associated NPC interaction system
  *   dialogue_structure: Tree, hub-and-spoke, etc.
  *   conversation_initiation: How dialogues begin
  *   player_response_types: Categories of replies available
  *   response_time_limits: Timed dialogue options
  *   dialogue_history: How past conversations are tracked
  *   branching_factors: What causes conversation paths
  *   dialogue_flags: Conversation state tracking
  *   tone_options: Emotional approaches available
  *   dialogue_skills: Character abilities affecting conversations
  *   voice_acting_integration: Audio implementation
  *   text_display_method: How dialogue appears on screen
  *   conversation_animations: Character movements during dialogue
  *   dialogue_camera: View changes during conversations
  *   interruption_handling: Breaking conversation flow
  *   translation_support: Localization considerations
  *   image_reference: Visual representation
  *   description: Detailed description

Dialogue Option Template

  *   id: Unique identifier
  *   name: Label for the dialogue option
  *   dialogue_system_id: Parent dialogue system
  *   dialogue_tree_id: Parent dialogue tree
  *   game_system_id: Associated rule system
  *   option_text: What the player can select
  *   display_conditions: When this option appears
  *   skill_requirements: Abilities needed to use
  *   attribute_requirements: Stats needed to use
  *   item_requirements: Objects needed to use
  *   reputation_requirements: Standing needed to use
  *   previous_choice_requirements: Prior decisions needed
  *   success_chance: Probability of intended outcome
  *   success_response: NPC reaction if successful
  *   failure_response: NPC reaction if failed
  *   player_character_animation: PC movement during option
  *   tone_category: Emotional approach (friendly, hostile, etc.)
  *   consequence_flags: Game state changes from this choice
  *   follow_up_options: Next available choices
  *   xp_reward: Experience for selecting this option
  *   item_rewards: Objects gained from this choice
  *   reputation_changes: Standing adjustments from choice
  *   quest_impacts: Effects on active missions
  *   voice_acting_direction: Performance notes
  *   image_reference: Visual representation
  *   description: Detailed description

Dialogue Tree Template

  *   id: Unique identifier
  *   name: Name of the dialogue tree
  *   dialogue_system_id: Parent dialogue system
  *   game_system_id: Associated rule system
  *   npc_id: Character speaking
  *   conversation_context: Situation/location
  *   trigger_conditions: What initiates this dialogue
  *   prerequisite_flags: Required game states
  *   entry_node: Starting dialogue line
  *   player_response_nodes: Available player replies
  *   npc_response_nodes: Character reactions
  *   branching_conditions: What affects dialogue paths
  *   skill_check_nodes: Ability-based options
  *   item_requirement_nodes: Object-dependent options
  *   emotional_tone_variations: Different delivery styles
  *   dialogue_animations: Character movements
  *   camera_directions: View changes during conversation
  *   special_effects: Visual/audio enhancements
  *   consequence_flags: Game state changes from dialogue
  *   quest_triggers: Missions started by conversation
  *   item_exchanges: Objects given/received
  *   relationship_impacts: How NPC feelings change
  *   follow_up_dialogues: Future conversations unlocked
  *   replay_availability: Whether repeatable
  *   voice_acting_notes: Performance direction
  *   image_reference: Visual representation
  *   description: Detailed description

Inventory System Template

  *   id: Unique identifier
  *   name: Name of the inventory system
  *   game_system_id: Associated rule system
  *   storage_capacity: How much can be carried
  *   capacity_calculation: Weight, slots, volume, etc.
  *   organization_method: How items are arranged
  *   category_system: Item type classification
  *   sorting_options: Ways to arrange inventory
  *   stacking_rules: How similar items combine
  *   equipment_slots: Worn/equipped item positions
  *   quick_access_slots: Hotbar/favorite items
  *   visual_representation: How inventory appears to player
  *   restricted_items: What cannot be carried
  *   item_degradation: Durability mechanics
  *   container_items: Bags, chests, etc.
  *   shared_inventories: Multi-character access
  *   currency_storage: How money is tracked
  *   item_comparison: How equipment differences are shown
  *   image_reference: Visual representation
  *   description: Detailed description



AI Behavior Template

  *   id: Unique identifier
  *   name: Name of the AI system
  *   game_system_id: Associated rule system
  *   npc_system_id: Associated NPC system
  *   decision_architecture: State machines, behavior trees, etc.
  *   sensory_simulation: Sight, hearing, etc.
  *   memory_systems: How AI retains information
  *   goal_prioritization: Decision-making hierarchy
  *   pathfinding_algorithm: Navigation method
  *   obstacle_handling: Dealing with blocked paths
  *   group_coordination: Multi-entity tactics
  *   combat_behaviors: Fighting strategies
  *   non-combat_behaviors: Peaceful activities
  *   emotional_simulation: AI moods and reactions
  *   learning_mechanisms: Adaptation to player
  *   resource_awareness: Knowledge of game world
  *   performance_optimization: CPU usage considerations
  *   difficulty_scaling: Adjusting AI challenge
  *   image_reference: Visual representation
  *   description: Detailed description

AI State Template

  *   id: Unique identifier
  *   name: Name of the AI state
  *   ai_behavior_id: Parent AI system
  *   game_system_id: Associated rule system
  *   state_type: Idle, alert, combat, flee, etc.
  *   entry_conditions: When AI enters this state
  *   exit_conditions: When AI leaves this state
  *   priority_level: Importance in decision hierarchy
  *   duration_parameters: How long state typically lasts
  *   animation_set: Visual representations
  *   sound_set: Audio cues
  *   movement_pattern: How entity navigates
  *   target_selection: What the AI focuses on
  *   action_repertoire: Available behaviors
  *   action_weights: Probability of each behavior
  *   resource_requirements: What AI needs in this state
  *   transition_states: Possible next states
  *   interruption_handling: Priority overrides
  *   cooldown_period: Time before re-entering
  *   state_variations: Contextual differences
  *   debug_indicators: Development visualization
  *   image_reference: Visual representation
  *   description: Detailed description

Morality/Alignment System Template

  *   id: Unique identifier
  *   name: Name of the morality system
  *   game_system_id: Associated rule system
  *   choice_system_id: Associated player choice system
  *   alignment_axes: Moral dimensions tracked
  *   measurement_scale: How alignment is quantified
  *   action_values: Moral weight of different choices
  *   visibility_to_player: How alignment is displayed
  *   alignment_shifts: How quickly values change
  *   alignment_decay: Return to neutral over time
  *   alignment_thresholds: Points where effects trigger
  *   gameplay_effects: Mechanical impacts of alignment
  *   npc_reactions: How characters respond to alignment
  *   alignment_requirements: Content gated by morality
  *   redemption_mechanics: Changing alignment intentionally
  *   alignment_abilities: Powers based on moral standing
  *   conflicting_values: Handling moral dilemmas
  *   cultural_relativity: Different standards in different regions
  *   image_reference: Visual representation
  *   description: Detailed description

Moral Choice Template

  *   id: Unique identifier
  *   name: Name of the moral choice
  *   morality_system_id: Parent morality system
  *   game_system_id: Associated rule system
  *   choice_context: Situation where decision occurs
  *   choice_options: Available decisions
  *   moral_dimensions: Ethical aspects involved
  *   alignment_impacts: How each option affects standing
  *   presentation_method: How choice is offered to player
  *   time_pressure: Deadline for decision
  *   information_availability: What player knows when deciding
  *   npc_influences: Characters affecting the choice
  *   consequence_visibility: How outcomes are shown
  *   short_term_consequences: Immediate results
  *   long_term_consequences: Delayed results
  *   narrative_significance: Story importance
  *   gameplay_rewards: Mechanical benefits by option
  *   choice_complexity: Moral ambiguity level
  *   recurring_theme: Connection to larger patterns
  *   player_preparation: How players can ready for choice
  *   image_reference: Visual representation
  *   description: Detailed description

Procedural Generation Template

  *   id: Unique identifier
  *   name: Name of the generation system
  *   game_system_id: Associated rule system
  *   world_scale_id: Associated world scale system
  *   generation_targets: What is being created
  *   seed_system: Random number generation
  *   constraint_parameters: Limits on generation
  *   template_usage: Pre-designed elements
  *   validation_rules: Ensuring playable results
  *   difficulty_scaling: Challenge adjustment in generation
  *   content_density: Distribution of elements
  *   theme_coherence: Stylistic consistency
  *   landmark_placement: Significant locations
  *   connectivity_guarantees: Ensuring navigable spaces
  *   generation_timing: When content is created
  *   persistence: What remains after generation
  *   player_customization: User input into generation
  *   regeneration_triggers: When content is recreated
  *   image_reference: Visual representation
  *   description: Detailed description

Generation Algorithm Template

  *   id: Unique identifier
  *   name: Name of the algorithm
  *   procedural_generation_id: Parent generation system
  *   game_system_id: Associated rule system
  *   algorithm_type: Noise-based, grammar-based, agent-based, etc.
  *   input_parameters: Configuration variables
  *   output_format: Data structure produced
  *   computational_complexity: Processing requirements
  *   generation_scale: Size of content created
  *   hierarchical_level: Macro to micro generation order
  *   randomization_method: How variation is introduced
  *   seed_handling: How random seeds are processed
  *   constraint_enforcement: How rules are applied
  *   post-processing: Cleanup and refinement steps
  *   fallback_mechanisms: Handling generation failures
  *   optimization_techniques: Performance improvements
  *   quality_metrics: How output is evaluated
  *   designer_overrides: Manual intervention points
  *   version_history: Algorithm evolution
  *   reference_implementations: Code examples
  *   mathematical_foundation: Underlying principles
  *   image_reference: Visual representation
  *   description: Detailed description

Multiplayer Interaction Template

  *   id: Unique identifier
  *   name: Name of the multiplayer system
  *   game_system_id: Associated rule system
  *   player_count: Supported number of participants
  *   interaction_types: Cooperative, competitive, etc.
  *   synchronization_method: How game state is shared
  *   latency_handling: Dealing with connection delays
  *   player_matching: How groups are formed
  *   communication_tools: Chat, voice, emotes, etc.
  *   shared_resources: What players use together
  *   individual_progression: Personal advancement
  *   group_progression: Team advancement
  *   anti-griefing_measures: Preventing harassment
  *   drop_in_out_handling: Players joining/leaving
  *   scaling_mechanics: Adjusting for player count
  *   pvp_balancing: Fair competitive play
  *   social_features: Friend lists, guilds, etc.
  *   cross-platform_support: Multiple device types
  *   image_reference: Visual representation
  *   description: Detailed description

Player Role Template

  *   id: Unique identifier
  *   name: Name of the player role
  *   multiplayer_system_id: Parent multiplayer system
  *   game_system_id: Associated rule system
  *   role_function: Primary purpose in team
  *   ability_set: Special capabilities
  *   responsibility_areas: Expected duties
  *   resource_access: What this role controls
  *   communication_privileges: Special channels
  *   visibility_permissions: What this role can see
  *   command_authority: Leadership capabilities
  *   role_restrictions: Limitations
  *   role_progression: How the role develops
  *   role_prerequisites: Requirements to obtain
  *   role_indicators: Visual/audio identifiers
  *   role_synergies: Complementary roles
  *   role_conflicts: Opposing roles
  *   role_rotation: Changing between players
  *   role_balance: Power level considerations
  *   player_count_scaling: Adjustments based on team size
  *   image_reference: Visual representation
  *   description: Detailed description



Permadeath/Roguelike Mechanics Template

  *   id: Unique identifier
  *   name: Name of the permadeath system
  *   game_system_id: Associated rule system
  *   death_finality: Extent of permanent consequences
  *   character_persistence: What carries between runs
  *   meta_progression: Advancement across multiple plays
  *   run_randomization: What changes each attempt
  *   legacy_systems: How past characters affect future ones
  *   unlockable_content: Features gained through play
  *   difficulty_scaling: Challenge changes across runs
  *   starting_variations: Different beginning conditions
  *   death_alternatives: Near-death options
  *   resource_persistence: What items carry over
  *   knowledge_persistence: Information retained
  *   run_statistics: Performance tracking
  *   leaderboard_systems: Competitive comparisons
  *   run_seeding: Sharing specific game states
  *   permadeath_exceptions: Rare second chances
  *   image_reference: Visual representation
  *   description: Detailed description

Stealth Mechanics Template

  *   id: Unique identifier
  *   name: Name of the stealth system
  *   game_system_id: Associated rule system
  *   ai_system_id: Associated AI behavior system
  *   visibility_metrics: How detection is calculated
  *   sound_propagation: Noise detection mechanics
  *   alertness_states: NPC awareness levels
  *   detection_indicators: How player knows stealth status
  *   hiding_mechanics: Concealment methods
  *   cover_system: Using environment for stealth
  *   distraction_tools: Methods to divert attention
  *   stealth_takedowns: Eliminating unaware targets
  *   body_concealment: Hiding evidence
  *   alert_level_escalation: Increasing enemy awareness
  *   search_behaviors: How NPCs look for player
  *   return_to_normal: De-escalation of alerts
  *   stealth_equipment: Specialized gear
  *   environmental_factors: Lighting, surfaces, etc.
  *   image_reference: Visual representation
  *   description: Detailed description
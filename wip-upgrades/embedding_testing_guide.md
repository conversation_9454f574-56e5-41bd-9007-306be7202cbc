# Embedding Testing Guide

This guide provides detailed instructions for using the Embedding Testing tab in the Admin Panel to test, evaluate, and optimize the embedding pipeline.

## Overview

The Embedding Testing tab allows you to:

1. Generate embeddings for different types of input
2. Customize field weights for different template types
3. Evaluate embedding quality with various metrics
4. Compare embeddings to different template types
5. Visualize embedding vectors and their properties

## Accessing the Embedding Testing Tab

1. Log in to the application
2. Click the "A" button in the top navigation bar to access the Admin Panel
3. Click on the "Embedding Testing" tab in the left sidebar

## Testing Embeddings

### Basic Text Embedding

To test a simple text embedding:

1. Select "Text Input" from the Test Type dropdown
2. Enter the text you want to embed in the Input Text field
3. Select a Template Type (e.g., "Character", "Location")
4. Adjust field weights if desired (see Field Weighting section)
5. Click "Generate Embedding"

The system will process your text and display the resulting embedding vector along with quality metrics.

### Template Field Embedding

To test embedding generation for template fields:

1. Select "Template Fields" from the Test Type dropdown
2. Fill in the template fields that appear
3. Select a Template Type
4. Adjust field weights if desired
5. Click "Generate Embedding"

### Entity ID Embedding

To test embedding for an existing entity:

1. Select "Entity ID" from the Test Type dropdown
2. Enter the ID of the entity (character, element, etc.)
3. Click "Generate Embedding"

## Field Weighting

Field weighting allows you to control the importance of different fields in the embedding:

1. Each template type has different available fields
2. Each field has a default weight (0.0 to 2.0)
3. Higher weights give that field more importance in the final embedding
4. Adjust weights by changing the number input next to each field

### Default Field Weights

#### Character Template
- Name: 1.5
- Full Name: 1.2
- Personality: 1.8
- Background: 1.5
- Appearance: 1.0
- Motivations: 1.6
- Gender Identity: 0.8
- Sexual Orientation: 0.8

#### Location Template
- Name: 1.5
- Geography: 1.8
- Climate: 1.5
- Description: 1.2
- History: 1.0
- Inhabitants: 1.0

#### Item Template
- Name: 1.5
- Type: 1.2
- Description: 1.2
- Properties: 1.8
- History: 1.0
- Usage: 1.5

## Understanding Results

After generating an embedding, you'll see several sections:

### Embedding Vector

This shows the first 10 dimensions of the embedding vector. Each dimension is a floating-point number between -1 and 1.

### Metrics

- **Vector Norm**: The length of the embedding vector. Should be close to 1.0 for normalized vectors.
- **Variance**: Measures how spread out the values are. Higher variance can indicate more information content.

### Similarity to Template Types

This section shows how similar the generated embedding is to different template types:
- Higher percentages indicate greater similarity
- This helps identify if the embedding is capturing the intended entity type
- Useful for debugging template-specific embedding strategies

## Advanced Testing

### Testing Field Weights

To find optimal field weights:

1. Start with default weights
2. Generate an embedding
3. Note the similarity scores
4. Adjust weights for key fields (try increasing by 0.2-0.3)
5. Generate a new embedding
6. Compare similarity scores
7. Repeat until you find weights that produce desired results

### Testing Preprocessing

The embedding pipeline includes text preprocessing. To test its effects:

1. Generate an embedding with normal text
2. Note the results
3. Try variations of the text (different formatting, punctuation, etc.)
4. Compare results to see how robust the preprocessing is

### Comparing Related Entities

To test how well the embedding captures relationships:

1. Generate embeddings for related entities (e.g., a character and their hometown)
2. Compare the similarity between these embeddings
3. Related entities should have higher similarity than unrelated ones

## Integration with Other Systems

The embedding testing tab works with:

1. **Template Management**: Test embeddings for templates you create
2. **System Monitoring**: Check embedding generation performance
3. **Vector Search**: Test how embeddings affect search results

## Best Practices

1. **Start Simple**: Begin with basic text before testing complex templates
2. **Consistent Testing**: Use the same input when comparing different weights
3. **Document Results**: Keep track of which weights work best for different template types
4. **Batch Testing**: Test multiple variations before making final decisions
5. **Check Extremes**: Test both very short and very long inputs

## Troubleshooting

### Low Quality Embeddings

If embeddings have poor quality metrics:

1. Check if the input text is too short or generic
2. Try increasing weights for more important fields
3. Ensure the template type matches the content

### Inconsistent Results

If similar inputs produce very different embeddings:

1. Check for special characters or formatting issues
2. Ensure preprocessing is handling the text consistently
3. Try normalizing text manually before input

### Performance Issues

If embedding generation is slow:

1. Reduce the length of input text
2. Check system monitoring for resource constraints
3. Consider batching embedding requests

## Future Enhancements

The embedding testing tab will be enhanced with:

1. Batch testing capabilities
2. Export/import of test results
3. Visual comparison of multiple embeddings
4. Integration with real-time search testing
5. Custom embedding strategy creation

## Technical Details

The embedding pipeline uses:

1. InstructorXL for embedding generation
2. Custom text preprocessing for different entity types
3. Field-weighted embedding combination
4. Template-specific embedding strategies
5. Embedding validation and quality metrics

This testing interface allows you to fine-tune each component of the pipeline for optimal results.

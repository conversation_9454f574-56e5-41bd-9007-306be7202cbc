# /home/<USER>/Documents/Programming/Projects/BookAppPostgres/bookapp_postgres.py
from mcp.server.fastmcp import FastMCP
import json
import signal
import sys
import psycopg2
from psycopg2.extras import RealDictCursor

# PostgreSQL connection for bookapp (read-only)
DB_PARAMS = {
    "dbname": "bookapp1",  # Replace with your actual DB name
    "user": "project1_user",
    "password": "rHEWX*.V.Adaline..",  # Set during setup
    "host": "localhost"
}

# Connect with RealDictCursor for named columns
conn = psycopg2.connect(**DB_PARAMS, cursor_factory=RealDictCursor)
cursor = conn.cursor()

# Initialize MCP server
mcp_server = FastMCP(
    name="BookAppPostgresMCPServer",
    description="MCP server for full access to bookapp PostgreSQL database with schema discovery"
)

def shutdown_handler(signum, frame):
    """Handle shutdown signals (e.g., Ctrl+C)."""
    print("\nShutting down BookAppPostgresMCPServer gracefully...")
    conn.close()  # Close PostgreSQL connection
    sys.exit(0)

signal.signal(signal.SIGINT, shutdown_handler)

@mcp_server.tool()
def bookapp_query(query: str) -> str:
    """Executes any SQL query on the bookapp database."""
    try:
        cursor.execute(query)

        # For SELECT queries, return the results
        if query.strip().upper().startswith("SELECT"):
            rows = cursor.fetchall()
            return json.dumps({"results": [dict(row) for row in rows], "query": query})
        # For other queries, commit the changes and return success
        else:
            conn.commit()
            return json.dumps({"success": True, "message": "Query executed successfully", "query": query})
    except Exception as e:
        conn.rollback()  # Rollback on error
        return json.dumps({"error": f"Query failed: {str(e)}"})

@mcp_server.tool()
def bookapp_list_tables() -> str:
    """Lists all tables in the bookapp database."""
    try:
        cursor.execute("""
            SELECT table_name
            FROM information_schema.tables
            WHERE table_schema = 'public'
            ORDER BY table_name;
        """)
        tables = [row["table_name"] for row in cursor.fetchall()]
        return json.dumps({"tables": tables})
    except Exception as e:
        return json.dumps({"error": f"List tables failed: {str(e)}"})

@mcp_server.tool()
def bookapp_describe_table(table_name: str) -> str:
    """Describes the schema of a specific table in the bookapp database."""
    try:
        # Check if table exists
        cursor.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables
                WHERE table_schema = 'public' AND table_name = %s
            );
        """, (table_name,))
        if not cursor.fetchone()["exists"]:
            return json.dumps({"error": f"Table '{table_name}' not found"})

        # Get column details
        cursor.execute("""
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns
            WHERE table_schema = 'public' AND table_name = %s
            ORDER BY ordinal_position;
        """, (table_name,))
        columns = [dict(row) for row in cursor.fetchall()]
        return json.dumps({"table": table_name, "columns": columns})
    except Exception as e:
        return json.dumps({"error": f"Describe table failed: {str(e)}"})

@mcp_server.tool()
def bookapp_test_connection() -> str:
    """Tests the connection to the bookapp database."""
    try:
        cursor.execute("SELECT 1 AS test;")
        return json.dumps({"message": "Bookapp database connection is alive!"})
    except Exception as e:
        return json.dumps({"error": f"Connection test failed: {str(e)}"})

@mcp_server.tool()
def bookapp_execute_transaction(sql_statements: str) -> str:
    """Executes multiple SQL statements as a single transaction."""
    try:
        # Start a transaction
        conn.autocommit = False

        # Split the input into individual statements
        statements = [stmt.strip() for stmt in sql_statements.split(';') if stmt.strip()]

        # Execute each statement
        results = []
        for stmt in statements:
            cursor.execute(stmt)
            if stmt.strip().upper().startswith("SELECT"):
                rows = cursor.fetchall()
                results.append({"statement": stmt, "results": [dict(row) for row in rows]})
            else:
                results.append({"statement": stmt, "affected_rows": cursor.rowcount})

        # Commit the transaction
        conn.commit()
        return json.dumps({"success": True, "results": results})
    except Exception as e:
        conn.rollback()  # Rollback on error
        return json.dumps({"error": f"Transaction failed: {str(e)}"})
    finally:
        conn.autocommit = True  # Reset to default

if __name__ == "__main__":
    print("Starting MCP Server: BookAppPostgresMCPServer")
    print(f"Connected to database: {DB_PARAMS['dbname']}")
    print("Press Ctrl+C to stop the server.")
    mcp_server.run()
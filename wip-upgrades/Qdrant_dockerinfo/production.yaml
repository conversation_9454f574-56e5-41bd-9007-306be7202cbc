storage:
  # Storage persistence configuration
  storage_path: /Qdrant/storage
  
  # Snapshot configuration for backups
  snapshots_path: /Qdrant/snapshots
  snapshots_interval_sec: 3600  # Take snapshots hourly
  
service:
  # API configuration
  http_port: 6333
  grpc_port: 6334
  
  # API key environment variable (should use QDRANT__SERVICE__ prefix in environment variable)
  api_key: "${QDRANT__SERVICE__API_KEY}"

telemetry:
  disabled: false

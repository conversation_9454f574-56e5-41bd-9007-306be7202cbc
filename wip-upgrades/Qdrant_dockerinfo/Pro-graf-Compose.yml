# Pro-graf-Compose.yml
services:
  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
    restart: always
    networks:
      - monitoring_network
      - qdrant_qdrant_net  # Updated to match the actual network name
  node_exporter:
    image: prom/node-exporter:latest
    container_name: node_exporter
    ports:
      - "9100:9100"
    command:
      - '--path.rootfs=/host'
    networks:
      - monitoring_network
    pid: host  # Important for seeing all processes
    restart: unless-stopped
    volumes:
      - '/:/host:ro,rslave'  # Mount the host filesystem as read-only
  grafana:
    image: grafana/grafana:latest
    container_name: grafana
    ports:
      - "3051:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/provisioning:/etc/grafana/provisioning
    environment:
      - GF_SECURITY_ADMIN_USER=admon
      - GF_SECURITY_ADMIN_PASSWORD=Cry.1.Dev.1  # Change this!
      - GF_USERS_ALLOW_SIGN_UP=false
    restart: always
    depends_on:
      - prometheus
    networks:
      - monitoring_network

volumes:
  prometheus_data:
  grafana_data:

networks:
  monitoring_network:
    driver: bridge
  qdrant_qdrant_net:  # Updated to match the actual network name
    external: true
#!/bin/bash
# backup_qdrant.sh - Script to backup Qdrant vector database

# Set variables
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_ROOT="$HOME/Documents/Programming/Projects/Qdrant_Backups"
BACKUP_DIR="$BACKUP_ROOT/qdrant_$TIMESTAMP"
CONTAINER_NAME="qdrant"
RETENTION_DAYS=7

# Create backup directories
mkdir -p "$BACKUP_ROOT"
mkdir -p "$BACKUP_DIR"

# First, create a snapshot via API
echo "Creating a new snapshot..."
curl -s -X POST 'http://localhost:6333/snapshots' \
  -H 'api-key: Qf1wtuJVTItWgMg4GhKh4Khpm/dmmfbnhgIj2U5Qxck=' \
  -o /dev/null

# Wait for snapshot to complete
sleep 5

# Copy snapshots from Docker volume to external storage
echo "Copying snapshots from container..."
docker cp "$CONTAINER_NAME:/qdrant/snapshots" "$BACKUP_DIR"

# Also backup the storage directory for complete recovery
echo "Copying storage data from container..."
docker cp "$CONTAINER_NAME:/qdrant/storage" "$BACKUP_DIR"

# Compress the backup
echo "Compressing backup..."
tar -czf "${BACKUP_DIR}.tar.gz" -C "$BACKUP_ROOT" "qdrant_$TIMESTAMP"
rm -rf "$BACKUP_DIR"

# Rotate old backups
echo "Cleaning up old backups..."
find "$BACKUP_ROOT" -name "qdrant_*.tar.gz" -type f -mtime +$RETENTION_DAYS -delete

echo "Backup completed: ${BACKUP_DIR}.tar.gz"
echo "Backup contains both snapshots and storage data for complete recovery."
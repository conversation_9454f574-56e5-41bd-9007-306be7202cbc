#!/bin/bash
# restore_qdrant.sh - <PERSON><PERSON><PERSON> to restore Qdrant vector database from backup

# Check if backup file is provided
if [ $# -ne 1 ]; then
    echo "Usage: $0 <backup_file.tar.gz>"
    echo "Example: $0 ~/Documents/Programming/Projects/Qdrant_Backups/qdrant_20250515_123045.tar.gz"
    exit 1
fi

BACKUP_FILE="$1"
CONTAINER_NAME="qdrant"
TEMP_DIR="/tmp/qdrant_restore_$(date +%s)"

# Check if backup file exists
if [ ! -f "$BACKUP_FILE" ]; then
    echo "Error: Backup file $BACKUP_FILE does not exist."
    exit 1
fi

# Check if container is running
if ! docker ps | grep -q "$CONTAINER_NAME"; then
    echo "Error: Container $CONTAINER_NAME is not running."
    echo "Please start the container first with: docker start $CONTAINER_NAME"
    exit 1
fi

# Create temporary directory
mkdir -p "$TEMP_DIR"
echo "Extracting backup to temporary directory..."
tar -xzf "$BACKUP_FILE" -C "$TEMP_DIR"

# Check if extraction was successful
if [ $? -ne 0 ]; then
    echo "Error: Failed to extract backup file."
    rm -rf "$TEMP_DIR"
    exit 1
fi

# Confirm before proceeding
echo "WARNING: This will replace all data in the Qdrant container with data from the backup."
echo "Make sure you have stopped any applications using Qdrant before proceeding."
read -p "Do you want to continue? (y/n): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "Restoration cancelled."
    rm -rf "$TEMP_DIR"
    exit 1
fi

# Stop the container
echo "Stopping Qdrant container..."
docker stop "$CONTAINER_NAME"

# Copy data to container
echo "Restoring data to container..."
EXTRACTED_DIR=$(find "$TEMP_DIR" -type d -name "qdrant_*" | head -n 1)

# Remove existing data
echo "Removing existing data from container..."
docker run --rm --volumes-from "$CONTAINER_NAME" alpine:latest sh -c "rm -rf /qdrant/storage/* /qdrant/snapshots/*"

# Copy backup data to container
echo "Copying storage data to container..."
docker cp "$EXTRACTED_DIR/storage/." "$CONTAINER_NAME:/qdrant/storage/"

echo "Copying snapshots to container..."
docker cp "$EXTRACTED_DIR/snapshots/." "$CONTAINER_NAME:/qdrant/snapshots/"

# Start the container
echo "Starting Qdrant container..."
docker start "$CONTAINER_NAME"

# Clean up
echo "Cleaning up temporary files..."
rm -rf "$TEMP_DIR"

echo "Restoration completed successfully."
echo "Qdrant container has been restored from backup: $BACKUP_FILE"
echo "Please verify that your data is accessible."

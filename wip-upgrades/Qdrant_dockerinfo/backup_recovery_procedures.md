# Qdrant Backup and Recovery Procedures

This document outlines the backup and recovery procedures for the Qdrant vector database used in the book authoring application.

## Backup Procedure

### Automated Backups

The `backup_qdrant.sh` script creates a complete backup of the Qdrant database, including both snapshots and the storage directory. This ensures that all vector data and configurations are preserved.

#### Setup Automated Backups

1. Edit the backup script if needed to customize paths or retention period:
   ```bash
   nano wip-upgrades/Qdrant_dockerinfo/backup_qdrant.sh
   ```

2. Add to crontab to run daily at 2 AM:
   ```bash
   crontab -e
   ```
   
   Add this line:
   ```
   0 2 * * * /path/to/wip-upgrades/Qdrant_dockerinfo/backup_qdrant.sh >> $HOME/qdrant_backup.log 2>&1
   ```

3. Verify the cron job is set up:
   ```bash
   crontab -l | grep backup
   ```

### Manual Backups

Run the backup script manually before making significant changes:

```bash
./wip-upgrades/Qdrant_dockerinfo/backup_qdrant.sh
```

The script will:
1. Create a new snapshot via the Qdrant API
2. Copy both snapshots and storage data from the container
3. Compress everything into a single tarball
4. Clean up backups older than 7 days (configurable)

Backups are stored in `$HOME/Documents/Programming/Projects/Qdrant_Backups/` by default.

## Recovery Procedure

The `restore_qdrant.sh` script restores Qdrant from a backup tarball.

### Recovery Steps

1. Ensure the Qdrant container exists (but it can be stopped):
   ```bash
   docker ps -a | grep qdrant
   ```

2. Run the restore script with the path to the backup file:
   ```bash
   ./wip-upgrades/Qdrant_dockerinfo/restore_qdrant.sh /path/to/backup/qdrant_20250515_123045.tar.gz
   ```

3. Follow the prompts to confirm the restoration.

4. The script will:
   - Stop the Qdrant container
   - Remove existing data
   - Copy backup data to the container
   - Restart the container

5. Verify the restoration by checking if your collections are accessible:
   ```bash
   curl -H "api-key: Qf1wtuJVTItWgMg4GhKh4Khpm/dmmfbnhgIj2U5Qxck=" http://localhost:6333/collections
   ```

## Disaster Recovery Scenarios

### Container Failure

If the Qdrant container fails but the Docker volumes are intact:

1. Simply restart the container:
   ```bash
   docker start qdrant
   ```

2. If that doesn't work, recreate the container with the same volume mounts:
   ```bash
   docker run -d --name qdrant \
       -p 6333:6333 \
       -p 6334:6334 \
       -v qdrant_storage:/qdrant/storage \
       -v qdrant_snapshots:/qdrant/snapshots \
       -e QDRANT__SERVICE__API_KEY=Qf1wtuJVTItWgMg4GhKh4Khpm/dmmfbnhgIj2U5Qxck= \
       qdrant/qdrant
   ```

### Volume Data Loss

If the Docker volumes are corrupted or lost:

1. Use the restore script to recover from the most recent backup:
   ```bash
   ./wip-upgrades/Qdrant_dockerinfo/restore_qdrant.sh /path/to/latest/backup.tar.gz
   ```

### Complete System Failure

In case of complete system failure:

1. Install Docker on the new system
2. Create a new Qdrant container
3. Use the restore script to recover from backup

## Best Practices

1. **Regular Testing**: Test the recovery procedure quarterly to ensure it works
2. **Offsite Backups**: Copy backups to an offsite location or cloud storage
3. **Backup Verification**: Periodically verify backup integrity
4. **Documentation**: Keep this document updated with any changes to procedures
5. **Security**: Ensure backup files are secured with appropriate permissions

## Monitoring

The backup and recovery processes can be monitored through:

1. Logs in `$HOME/qdrant_backup.log`
2. Prometheus metrics for Qdrant health
3. Grafana dashboards for visualization

## Future Enhancements

For production deployment, consider enhancing these procedures with:

1. Multi-environment support (dev/staging/prod)
2. Cloud storage integration for backups
3. Automated backup verification
4. Notification system for backup success/failure

services:
  qdrant:
    image: qdrant/qdrant:latest
    container_name: qdrant
    restart: always
    networks:
      - qdrant_net
    ports:
      - "6333:6333"  # REST API
      - "6334:6334"  # gRPC API
    configs:
      - source: qdrant_config
        target: /qdrant/config/production.yaml
    volumes:
      - qdrant_storage:/qdrant/storage  # Persists vector data
      - qdrant_snapshots:/qdrant/snapshots  # Persists snapshots for backup
    environment:
      - QDRANT_CONFIG_PATH=/qdrant/config/production.yaml
      - QDRANT__LOG_LEVEL=INFO
    healthcheck:
      test: ["CMD", "curl", "-H", "API Key: Qf1wtuJVTItWgMg4GhKh4Khpm/dmmfbnhgIj2U5Qxck=", "http://localhost:6333/readiness"]
      interval: 10s
      timeout: 10s
      retries: 5
      start_period: 5s
volumes:
  qdrant_storage:
    driver: local
  qdrant_snapshots:
    driver: local

networks:
  qdrant_net:
    driver: bridge

configs:
  qdrant_config:
    content: |
      log_level: INFO
      service:
        http_port: 6333
        grpc_port: 6334
        api_key: "Qf1wtuJVTItWgMg4GhKh4Khpm/dmmfbnhgIj2U5Qxck="
      storage:
        storage_path: /qdrant/storage
        snapshots_path: /qdrant/snapshots
        snapshots_interval_sec: 3600
        performance:
          max_search_threads: 4
      telemetry:
        disabled: false
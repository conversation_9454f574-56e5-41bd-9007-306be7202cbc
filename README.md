# Book Writing Application

A comprehensive application for writers to plan, organize, and write their books.

## Features

- World building
- Character management
- Plot planning
- Brainstorming
- Writing with rich text editor
- AI assistance

## Documentation

- [State Map](STATE_MAP.md): Comprehensive overview of the Redux state structure
- [Redux Status](REDUX_STATUS.md): Current status of the Redux migration
- [Project Status](PROJECT_STATUS.md): Project status and future plans

## Architecture

The application follows a clean architecture with:

- **Frontend**: React-based SPA with Redux Toolkit for state management
- **Backend**: FastAPI-based REST API
- **Database**: PostgreSQL

### State Management

The application uses Redux Toolkit with feature-based slices for state management. The application follows the container/presentation pattern, with container components connecting to Redux and presentation components receiving props.

See the [State Map](STATE_MAP.md) documentation for more details.

## Development

### Prerequisites

- Node.js
- Python 3.8+
- PostgreSQL

### Setup

1. Clone the repository
2. Install frontend dependencies: `cd frontend && npm install`
3. Install backend dependencies: `cd backend && pip install -r requirements.txt`
4. Set up the database: `cd backend && python setup_db.py`
5. Start the backend: `cd backend && uvicorn main:app --reload`
6. Start the frontend: `cd frontend && npm start`

 Action 
Object { type: "brainstorm/cleanupOldDeletedCardIds/pending", payload: undefined, meta: {…} }
bundle.js:33488:19
 Next State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
[DEBUG Brainstorm] cleanupOldDeletedCardIds is deprecated - using backend deleted_nodes table bundle.js:33488:19
 Action: brainstorm/cleanupOldDeletedCardIds/fulfilled bundle.js:211494:13
 Previous State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
 Action 
Object { type: "brainstorm/cleanupOldDeletedCardIds/fulfilled", payload: {…}, meta: {…} }
bundle.js:33488:19
 Next State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
 Action: brainstorm/cleanupOldDeletedCardIds/fulfilled bundle.js:211494:13
 Previous State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
 Action 
Object { type: "brainstorm/cleanupOldDeletedCardIds/fulfilled", payload: {…}, meta: {…} }
bundle.js:33488:19
 Next State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
[PHANTOM] error getting provider injection options contentScript.js:9:172250
Error: Could not establish connection. Receiving end does not exist.
    xc moz-extension://c1253c5a-a2d9-4076-8b4b-cdcadfa09fd7/solanaActionsContentScript.js:27

[PHANTOM] error getting provider injection options contentScript.js:9:172250
[PHANTOM] error updating cache Error: Could not establish connection. Receiving end does not exist.
    Jc moz-extension://c1253c5a-a2d9-4076-8b4b-cdcadfa09fd7/contentScript.js:9
contentScript.js:9:169988
[PHANTOM] error updating cache Error: Could not establish connection. Receiving end does not exist.
    Jc moz-extension://c1253c5a-a2d9-4076-8b4b-cdcadfa09fd7/contentScript.js:9
contentScript.js:9:169988
API response status for http://localhost:8000/books: 200 bundle.js:33488:19
 Action: books/fetchAll/fulfilled bundle.js:211494:13
 Previous State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
 Action 
Object { type: "books/fetchAll/fulfilled", payload: (2) […], meta: {…} }
bundle.js:33488:19
[DEBUG] selectCurrentChapter - chapters: 
Array []
bundle.js:33488:19
[DEBUG] selectCurrentChapter - currentChapterId: null bundle.js:33488:19
[DEBUG] selectCurrentChapter - No current chapter ID bundle.js:33488:19
bookSelectionMiddleware: Books exist but no book is selected, selecting a book bundle.js:33488:19
bookSelectionMiddleware: Selecting book: NewBook1 bundle.js:33488:19
 Action: books/selectBook bundle.js:211494:13
 Previous State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
 Action 
Object { type: "books/selectBook", payload: {…} }
bundle.js:33488:19
selectBook action with payload: 
Object { book_id: "f61a4fd1-7ca1-419a-8a45-a155d65d0d96", title: "NewBook1", author: "Matt Griffin", genre: "romance", description: "This is a Sci-Fi Romance novel based around the galaxy of Andromeda ", created_at: "2025-04-24T10:28:58.475849", updated_at: "2025-04-24T21:59:24.414649" }
bundle.js:33488:19
Found existing book in books array: 
Proxy { <target>: null, <handler>: null }
bundle.js:33488:19
[DEBUG] selectCurrentChapter - chapters: 
Array []
bundle.js:33488:19
[DEBUG] selectCurrentChapter - currentChapterId: null bundle.js:33488:19
[DEBUG] selectCurrentChapter - No current chapter ID bundle.js:33488:19
bookSelectionMiddleware: Book changed from none to f61a4fd1-7ca1-419a-8a45-a155d65d0d96 bundle.js:33488:19
bookSelectionMiddleware: Resetting brainstorm state for new book bundle.js:33488:19
 Action: brainstorm/resetBrainstormState bundle.js:211494:13
 Previous State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
 Action 
Object { type: "brainstorm/resetBrainstormState", payload: undefined }
bundle.js:33488:19
[DEBUG Brainstorm Reducer] State reset for book change bundle.js:33488:19
 Next State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
bookSelectionMiddleware: Setting current book ID in worldBuildingSlice bundle.js:33488:19
 Action: worldBuilding/setCurrentBookId bundle.js:211494:13
 Previous State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
 Action 
Object { type: "worldBuilding/setCurrentBookId", payload: "f61a4fd1-7ca1-419a-8a45-a155d65d0d96" }
bundle.js:33488:19
 Next State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
 Next State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
 Action: worldBuilding/setCurrentBookId bundle.js:211494:13
 Previous State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
 Action 
Object { type: "worldBuilding/setCurrentBookId", payload: "f61a4fd1-7ca1-419a-8a45-a155d65d0d96" }
bundle.js:33488:19
 Next State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
bookSelectionMiddleware: Fetching full book details for: f61a4fd1-7ca1-419a-8a45-a155d65d0d96 bundle.js:33488:19
 Action: books/fetchById/pending bundle.js:211494:13
 Previous State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
 Action 
Object { type: "books/fetchById/pending", payload: undefined, meta: {…} }
bundle.js:33488:19
[DEBUG] selectCurrentChapter - chapters: 
Array []
bundle.js:33488:19
[DEBUG] selectCurrentChapter - currentChapterId: null bundle.js:33488:19
[DEBUG] selectCurrentChapter - No current chapter ID bundle.js:33488:19
 Next State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
Token found in localStorage: eyJhbGciOi... bundle.js:33488:19
 Next State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
App.js: Books fetched successfully: 
Object { count: 2, books: (2) […] }
bundle.js:33488:19
App.js: Last selected book ID from localStorage: f61a4fd1-7ca1-419a-8a45-a155d65d0d96 bundle.js:33488:19
App.js: Setting selected book to: 
Object { book_id: "f61a4fd1-7ca1-419a-8a45-a155d65d0d96", title: "NewBook1", isLastSelected: true }
bundle.js:33488:19
App.js: Saved book ID to localStorage: f61a4fd1-7ca1-419a-8a45-a155d65d0d96 bundle.js:33488:19
 Action: books/selectBook bundle.js:211494:13
 Previous State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
 Action 
Object { type: "books/selectBook", payload: {…} }
bundle.js:33488:19
selectBook action with payload: 
Object { book_id: "f61a4fd1-7ca1-419a-8a45-a155d65d0d96", title: "NewBook1", author: "Matt Griffin", genre: "romance", description: "This is a Sci-Fi Romance novel based around the galaxy of Andromeda ", created_at: "2025-04-24T10:28:58.475849", updated_at: "2025-04-24T21:59:24.414649" }
bundle.js:33488:19
Found existing book in books array: 
Proxy { <target>: null, <handler>: null }
bundle.js:33488:19
[DEBUG] selectCurrentChapter - chapters: 
Array []
bundle.js:33488:19
[DEBUG] selectCurrentChapter - currentChapterId: null bundle.js:33488:19
[DEBUG] selectCurrentChapter - No current chapter ID bundle.js:33488:19
 Next State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
App.js: Fetching book details for: f61a4fd1-7ca1-419a-8a45-a155d65d0d96 bundle.js:33488:19
 Action: books/fetchById/pending bundle.js:211494:13
 Previous State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
 Action 
Object { type: "books/fetchById/pending", payload: undefined, meta: {…} }
bundle.js:33488:19
 Next State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
Token found in localStorage: eyJhbGciOi... bundle.js:33488:19
 Action: books/fetchById/fulfilled bundle.js:211494:13
 Previous State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
 Action 
Object { type: "books/fetchById/fulfilled", payload: {…}, meta: {…} }
bundle.js:33488:19
fetchBookById.fulfilled with payload: 
Object { book_id: "f61a4fd1-7ca1-419a-8a45-a155d65d0d96", user_id: "610f8dfc-d3c5-48f6-b7c8-693e6d7a6695", title: "NewBook1", author: "Matt Griffin", genre: "romance", description: "This is a Sci-Fi Romance novel based around the galaxy of Andromeda ", created_at: "2025-04-24T10:28:58.475849", updated_at: "2025-04-24T21:59:24.414649" }
bundle.js:33488:19
Updating book in books array. Before: 
Proxy { <target>: null, <handler>: null }
bundle.js:33488:19
After update: 
Object { book_id: "f61a4fd1-7ca1-419a-8a45-a155d65d0d96", user_id: "610f8dfc-d3c5-48f6-b7c8-693e6d7a6695", title: "NewBook1", author: "Matt Griffin", genre: "romance", description: "This is a Sci-Fi Romance novel based around the galaxy of Andromeda ", created_at: "2025-04-24T10:28:58.475849", updated_at: "2025-04-24T21:59:24.414649" }
bundle.js:33488:19
[DEBUG] selectCurrentChapter - chapters: 
Array []
bundle.js:33488:19
[DEBUG] selectCurrentChapter - currentChapterId: null bundle.js:33488:19
[DEBUG] selectCurrentChapter - No current chapter ID bundle.js:33488:19
 Next State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
selectAllElementsForRelationshipView - elementsById: 0 bundle.js:33488:19
selectAllElementsForRelationshipView - allIds: 0 bundle.js:33488:19
selectAllElementsForRelationshipView - No elements found bundle.js:33488:19
selectAllRelationships - relationshipsById: 
Object {  }
bundle.js:33488:19
selectAllRelationships - allIds: 
Array []
bundle.js:33488:19
selectAllRelationships - returning relationships: 
Array []
bundle.js:33488:19
Selector unknown returned a different result when called with the same parameters. This can lead to unnecessary rerenders.
Selectors that return a new reference (such as an object or an array) should be memoized: https://redux.js.org/usage/deriving-data-selectors#optimizing-selectors-with-memoization 
Object { state: {…}, selected: [], selected2: [], stack: "@http://localhost:3000/static/js/bundle.js:125536:23\nmemoizedSelector@http://localhost:3000/static/js/bundle.js:171544:26\n./node_modules/use-sync-external-store/cjs/use-sync-external-store-with-selector.development.js/exports.useSyncExternalStoreWithSelector/instRef</<@http://localhost:3000/static/js/bundle.js:171563:16\nmountSyncExternalStore@http://localhost:3000/static/js/bundle.js:112589:24\nuseSyncExternalStore@http://localhost:3000/static/js/bundle.js:113381:18\nuseSyncExternalStore@http://localhost:3000/static/js/bundle.js:131977:25\n./node_modules/use-sync-external-store/cjs/use-sync-external-store-with-selector.development.js/exports.useSyncExternalStoreWithSelector@http://localhost:3000/static/js/bundle.js:171568:37\nuseSelector2@http://localhost:3000/static/js/bundle.js:125572:133\nWorldBuildingPageContainer@http://localhost:3000/static/js/bundle.js:210054:81\nrenderWithHooks@http://localhost:3000/static/js/bundle.js:112169:31\nmountIndeterminateComponent@http://localhost:3000/static/js/bundle.js:116140:17\nbeginWork@http://localhost:3000/static/js/bundle.js:117443:20\nbeginWork$1@http://localhost:3000/static/js/bundle.js:122402:18\nperformUnitOfWork@http://localhost:3000/static/js/bundle.js:121672:16\nworkLoopSync@http://localhost:3000/static/js/bundle.js:121595:26\nrenderRootSync@http://localhost:3000/static/js/bundle.js:121568:11\nperformConcurrentWorkOnRoot@http://localhost:3000/static/js/bundle.js:120963:78\nworkLoop@http://localhost:3000/static/js/bundle.js:134449:46\nflushWork@http://localhost:3000/static/js/bundle.js:134427:18\nperformWorkUntilDeadline@http://localhost:3000/static/js/bundle.js:134664:25\n" }
 Component Stack: 
    WorldBuildingPageContainer WorldBuildingPageContainer.js:45
    BookRequiredWrapper BookRequiredWrapper.js:13
    RenderedRoute hooks.tsx:665
    Routes components.tsx:514
    div unknown:0
    div unknown:0
    div unknown:0
    AppContent App.js:62
    ErrorBoundary ErrorBoundary.js:8
    ThemeProvider ThemeProvider.js:13
    App App.js:32
    Router components.tsx:428
    BrowserRouter index.tsx:793
    Provider Redux
<anonymous code>:1:145535
Selector unknown returned a different result when called with the same parameters. This can lead to unnecessary rerenders.
Selectors that return a new reference (such as an object or an array) should be memoized: https://redux.js.org/usage/deriving-data-selectors#optimizing-selectors-with-memoization 
Object { state: {…}, selected: [], selected2: [], stack: "@http://localhost:3000/static/js/bundle.js:125536:23\nmemoizedSelector@http://localhost:3000/static/js/bundle.js:171544:26\n./node_modules/use-sync-external-store/cjs/use-sync-external-store-with-selector.development.js/exports.useSyncExternalStoreWithSelector/instRef</<@http://localhost:3000/static/js/bundle.js:171563:16\nmountSyncExternalStore@http://localhost:3000/static/js/bundle.js:112589:24\nuseSyncExternalStore@http://localhost:3000/static/js/bundle.js:113381:18\nuseSyncExternalStore@http://localhost:3000/static/js/bundle.js:131977:25\n./node_modules/use-sync-external-store/cjs/use-sync-external-store-with-selector.development.js/exports.useSyncExternalStoreWithSelector@http://localhost:3000/static/js/bundle.js:171568:37\nuseSelector2@http://localhost:3000/static/js/bundle.js:125572:133\nWorldBuildingPageContainer@http://localhost:3000/static/js/bundle.js:210057:88\nrenderWithHooks@http://localhost:3000/static/js/bundle.js:112169:31\nmountIndeterminateComponent@http://localhost:3000/static/js/bundle.js:116140:17\nbeginWork@http://localhost:3000/static/js/bundle.js:117443:20\nbeginWork$1@http://localhost:3000/static/js/bundle.js:122402:18\nperformUnitOfWork@http://localhost:3000/static/js/bundle.js:121672:16\nworkLoopSync@http://localhost:3000/static/js/bundle.js:121595:26\nrenderRootSync@http://localhost:3000/static/js/bundle.js:121568:11\nperformConcurrentWorkOnRoot@http://localhost:3000/static/js/bundle.js:120963:78\nworkLoop@http://localhost:3000/static/js/bundle.js:134449:46\nflushWork@http://localhost:3000/static/js/bundle.js:134427:18\nperformWorkUntilDeadline@http://localhost:3000/static/js/bundle.js:134664:25\n" }
 Component Stack: 
    WorldBuildingPageContainer WorldBuildingPageContainer.js:45
    BookRequiredWrapper BookRequiredWrapper.js:13
    RenderedRoute hooks.tsx:665
    Routes components.tsx:514
    div unknown:0
    div unknown:0
    div unknown:0
    AppContent App.js:62
    ErrorBoundary ErrorBoundary.js:8
    ThemeProvider ThemeProvider.js:13
    App App.js:32
    Router components.tsx:428
    BrowserRouter index.tsx:793
    Provider Redux
<anonymous code>:1:145535
Selector unknown returned a different result when called with the same parameters. This can lead to unnecessary rerenders.
Selectors that return a new reference (such as an object or an array) should be memoized: https://redux.js.org/usage/deriving-data-selectors#optimizing-selectors-with-memoization  
Object { state: {…}, selected: [], selected2: [], stack: "@http://localhost:3000/static/js/bundle.js:125536:23\nmemoizedSelector@http://localhost:3000/static/js/bundle.js:171544:26\n./node_modules/use-sync-external-store/cjs/use-sync-external-store-with-selector.development.js/exports.useSyncExternalStoreWithSelector/instRef</<@http://localhost:3000/static/js/bundle.js:171563:16\nmountSyncExternalStore@http://localhost:3000/static/js/bundle.js:112589:24\nuseSyncExternalStore@http://localhost:3000/static/js/bundle.js:113381:18\nuseSyncExternalStore@http://localhost:3000/static/js/bundle.js:131977:25\n./node_modules/use-sync-external-store/cjs/use-sync-external-store-with-selector.development.js/exports.useSyncExternalStoreWithSelector@http://localhost:3000/static/js/bundle.js:171568:37\nuseSelector2@http://localhost:3000/static/js/bundle.js:125572:133\nWorldBuildingPageContainer@http://localhost:3000/static/js/bundle.js:210054:81\nrenderWithHooks@http://localhost:3000/static/js/bundle.js:112169:31\nmountIndeterminateComponent@http://localhost:3000/static/js/bundle.js:116198:23\nbeginWork@http://localhost:3000/static/js/bundle.js:117443:20\nbeginWork$1@http://localhost:3000/static/js/bundle.js:122402:18\nperformUnitOfWork@http://localhost:3000/static/js/bundle.js:121672:16\nworkLoopSync@http://localhost:3000/static/js/bundle.js:121595:26\nrenderRootSync@http://localhost:3000/static/js/bundle.js:121568:11\nperformConcurrentWorkOnRoot@http://localhost:3000/static/js/bundle.js:120963:78\nworkLoop@http://localhost:3000/static/js/bundle.js:134449:46\nflushWork@http://localhost:3000/static/js/bundle.js:134427:18\nperformWorkUntilDeadline@http://localhost:3000/static/js/bundle.js:134664:25\n" }
   Component Stack: 
    WorldBuildingPageContainer WorldBuildingPageContainer.js:45
    BookRequiredWrapper BookRequiredWrapper.js:13
    RenderedRoute hooks.tsx:665
    Routes components.tsx:514
    div unknown:0
    div unknown:0
    div unknown:0
    AppContent App.js:62
    ErrorBoundary ErrorBoundary.js:8
    ThemeProvider ThemeProvider.js:13
    App App.js:32
    Router components.tsx:428
    BrowserRouter index.tsx:793
    Provider Redux
   undefined <anonymous code>:1:145521
Selector unknown returned a different result when called with the same parameters. This can lead to unnecessary rerenders.
Selectors that return a new reference (such as an object or an array) should be memoized: https://redux.js.org/usage/deriving-data-selectors#optimizing-selectors-with-memoization  
Object { state: {…}, selected: [], selected2: [], stack: "@http://localhost:3000/static/js/bundle.js:125536:23\nmemoizedSelector@http://localhost:3000/static/js/bundle.js:171544:26\n./node_modules/use-sync-external-store/cjs/use-sync-external-store-with-selector.development.js/exports.useSyncExternalStoreWithSelector/instRef</<@http://localhost:3000/static/js/bundle.js:171563:16\nmountSyncExternalStore@http://localhost:3000/static/js/bundle.js:112589:24\nuseSyncExternalStore@http://localhost:3000/static/js/bundle.js:113381:18\nuseSyncExternalStore@http://localhost:3000/static/js/bundle.js:131977:25\n./node_modules/use-sync-external-store/cjs/use-sync-external-store-with-selector.development.js/exports.useSyncExternalStoreWithSelector@http://localhost:3000/static/js/bundle.js:171568:37\nuseSelector2@http://localhost:3000/static/js/bundle.js:125572:133\nWorldBuildingPageContainer@http://localhost:3000/static/js/bundle.js:210057:88\nrenderWithHooks@http://localhost:3000/static/js/bundle.js:112169:31\nmountIndeterminateComponent@http://localhost:3000/static/js/bundle.js:116198:23\nbeginWork@http://localhost:3000/static/js/bundle.js:117443:20\nbeginWork$1@http://localhost:3000/static/js/bundle.js:122402:18\nperformUnitOfWork@http://localhost:3000/static/js/bundle.js:121672:16\nworkLoopSync@http://localhost:3000/static/js/bundle.js:121595:26\nrenderRootSync@http://localhost:3000/static/js/bundle.js:121568:11\nperformConcurrentWorkOnRoot@http://localhost:3000/static/js/bundle.js:120963:78\nworkLoop@http://localhost:3000/static/js/bundle.js:134449:46\nflushWork@http://localhost:3000/static/js/bundle.js:134427:18\nperformWorkUntilDeadline@http://localhost:3000/static/js/bundle.js:134664:25\n" }
   Component Stack: 
    WorldBuildingPageContainer WorldBuildingPageContainer.js:45
    BookRequiredWrapper BookRequiredWrapper.js:13
    RenderedRoute hooks.tsx:665
    Routes components.tsx:514
    div unknown:0
    div unknown:0
    div unknown:0
    AppContent App.js:62
    ErrorBoundary ErrorBoundary.js:8
    ThemeProvider ThemeProvider.js:13
    App App.js:32
    Router components.tsx:428
    BrowserRouter index.tsx:793
    Provider Redux
   undefined <anonymous code>:1:145521
WorldBuildingPage - allRelationships: 
Array []
bundle.js:33488:19
WorldBuildingPage - elementRelationships: 
Array []
bundle.js:33488:19
WorldBuildingPage - allRelationships:  
Array []
bundle.js:33488:19
WorldBuildingPage - elementRelationships:  
Array []
bundle.js:33488:19
 Action: worldBuilding/setActiveTab bundle.js:211494:13
 Previous State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
 Action 
Object { type: "worldBuilding/setActiveTab", payload: "all" }
bundle.js:33488:19
[DEBUG] selectCurrentChapter - chapters: 
Array []
bundle.js:33488:19
[DEBUG] selectCurrentChapter - currentChapterId: null bundle.js:33488:19
[DEBUG] selectCurrentChapter - No current chapter ID bundle.js:33488:19
 Next State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
 Action: worldBuilding/fetchData/pending bundle.js:211494:13
 Previous State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
 Action 
Object { type: "worldBuilding/fetchData/pending", payload: undefined, meta: {…} }
bundle.js:33488:19
[DEBUG] selectCurrentChapter - chapters: 
Array []
bundle.js:33488:19
[DEBUG] selectCurrentChapter - currentChapterId: null bundle.js:33488:19
[DEBUG] selectCurrentChapter - No current chapter ID bundle.js:33488:19
 Next State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
Fetching world building data for book: f61a4fd1-7ca1-419a-8a45-a155d65d0d96 bundle.js:33488:19
API Request to http://localhost:8000/books/f61a4fd1-7ca1-419a-8a45-a155d65d0d96/world-building/categories bundle.js:33488:19
Token found in localStorage: eyJhbGciOi... bundle.js:33488:19
Request headers: 
Object { Authorization: "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI2MTBmOGRmYy1kM2M1LTQ4ZjYtYjdjOC02OTNlNmQ3YTY2OTUiLCJleHAiOjE3NDc0Mzg2MjZ9.krop37f_-mPIZzn-X5nu2xslpeliL6pjS6HVcNWPWT4", "Content-Type": "application/json" }
bundle.js:33488:19
API Request to http://localhost:8000/books/f61a4fd1-7ca1-419a-8a45-a155d65d0d96/world-building/customizations bundle.js:33488:19
Token found in localStorage: eyJhbGciOi... bundle.js:33488:19
Request headers: 
Object { Authorization: "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI2MTBmOGRmYy1kM2M1LTQ4ZjYtYjdjOC02OTNlNmQ3YTY2OTUiLCJleHAiOjE3NDc0Mzg2MjZ9.krop37f_-mPIZzn-X5nu2xslpeliL6pjS6HVcNWPWT4", "Content-Type": "application/json" }
bundle.js:33488:19
API Request to http://localhost:8000/books/f61a4fd1-7ca1-419a-8a45-a155d65d0d96/world-building/relationships bundle.js:33488:19
Token found in localStorage: eyJhbGciOi... bundle.js:33488:19
Request headers: 
Object { Authorization: "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI2MTBmOGRmYy1kM2M1LTQ4ZjYtYjdjOC02OTNlNmQ3YTY2OTUiLCJleHAiOjE3NDc0Mzg2MjZ9.krop37f_-mPIZzn-X5nu2xslpeliL6pjS6HVcNWPWT4", "Content-Type": "application/json" }
bundle.js:33488:19
API Request to http://localhost:8000/books/f61a4fd1-7ca1-419a-8a45-a155d65d0d96/world-building/tables/schemas bundle.js:33488:19
Token found in localStorage: eyJhbGciOi... bundle.js:33488:19
Request headers: 
Object { Authorization: "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI2MTBmOGRmYy1kM2M1LTQ4ZjYtYjdjOC02OTNlNmQ3YTY2OTUiLCJleHAiOjE3NDc0Mzg2MjZ9.krop37f_-mPIZzn-X5nu2xslpeliL6pjS6HVcNWPWT4", "Content-Type": "application/json" }
bundle.js:33488:19
 Action: worldBuilding/fetchAllElementsWithChildren/pending bundle.js:211494:13
 Previous State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
 Action 
Object { type: "worldBuilding/fetchAllElementsWithChildren/pending", payload: undefined, meta: {…} }
bundle.js:33488:19
 Next State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
Redux - Fetching all elements with children for relationship view, bookId: f61a4fd1-7ca1-419a-8a45-a155d65d0d96 bundle.js:33488:19
Redux - Categories found: 0 bundle.js:33488:19
Redux - No categories found, fetching world building data first bundle.js:33488:19
 Action: worldBuilding/fetchData/pending bundle.js:211494:13
 Previous State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
 Action 
Object { type: "worldBuilding/fetchData/pending", payload: undefined, meta: {…} }
bundle.js:33488:19
 Next State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
Fetching world building data for book: f61a4fd1-7ca1-419a-8a45-a155d65d0d96 bundle.js:33488:19
API Request to http://localhost:8000/books/f61a4fd1-7ca1-419a-8a45-a155d65d0d96/world-building/categories bundle.js:33488:19
Token found in localStorage: eyJhbGciOi... bundle.js:33488:19
Request headers: 
Object { Authorization: "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI2MTBmOGRmYy1kM2M1LTQ4ZjYtYjdjOC02OTNlNmQ3YTY2OTUiLCJleHAiOjE3NDc0Mzg2MjZ9.krop37f_-mPIZzn-X5nu2xslpeliL6pjS6HVcNWPWT4", "Content-Type": "application/json" }
bundle.js:33488:19
API Request to http://localhost:8000/books/f61a4fd1-7ca1-419a-8a45-a155d65d0d96/world-building/customizations bundle.js:33488:19
Token found in localStorage: eyJhbGciOi... bundle.js:33488:19
Request headers: 
Object { Authorization: "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI2MTBmOGRmYy1kM2M1LTQ4ZjYtYjdjOC02OTNlNmQ3YTY2OTUiLCJleHAiOjE3NDc0Mzg2MjZ9.krop37f_-mPIZzn-X5nu2xslpeliL6pjS6HVcNWPWT4", "Content-Type": "application/json" }
bundle.js:33488:19
API Request to http://localhost:8000/books/f61a4fd1-7ca1-419a-8a45-a155d65d0d96/world-building/relationships bundle.js:33488:19
Token found in localStorage: eyJhbGciOi... bundle.js:33488:19
Request headers: 
Object { Authorization: "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI2MTBmOGRmYy1kM2M1LTQ4ZjYtYjdjOC02OTNlNmQ3YTY2OTUiLCJleHAiOjE3NDc0Mzg2MjZ9.krop37f_-mPIZzn-X5nu2xslpeliL6pjS6HVcNWPWT4", "Content-Type": "application/json" }
bundle.js:33488:19
API Request to http://localhost:8000/books/f61a4fd1-7ca1-419a-8a45-a155d65d0d96/world-building/tables/schemas bundle.js:33488:19
Token found in localStorage: eyJhbGciOi... bundle.js:33488:19
Request headers: 
Object { Authorization: "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI2MTBmOGRmYy1kM2M1LTQ4ZjYtYjdjOC02OTNlNmQ3YTY2OTUiLCJleHAiOjE3NDc0Mzg2MjZ9.krop37f_-mPIZzn-X5nu2xslpeliL6pjS6HVcNWPWT4", "Content-Type": "application/json" }
bundle.js:33488:19
 Action: worldBuilding/setActiveTab bundle.js:211494:13
 Previous State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
 Action 
Object { type: "worldBuilding/setActiveTab", payload: "all" }
bundle.js:33488:19
 Next State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
 Action: worldBuilding/fetchData/pending bundle.js:211494:13
 Previous State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
 Action 
Object { type: "worldBuilding/fetchData/pending", payload: undefined, meta: {…} }
bundle.js:33488:19
 Next State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
Fetching world building data for book: f61a4fd1-7ca1-419a-8a45-a155d65d0d96 bundle.js:33488:19
API Request to http://localhost:8000/books/f61a4fd1-7ca1-419a-8a45-a155d65d0d96/world-building/categories bundle.js:33488:19
Token found in localStorage: eyJhbGciOi... bundle.js:33488:19
Request headers: 
Object { Authorization: "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI2MTBmOGRmYy1kM2M1LTQ4ZjYtYjdjOC02OTNlNmQ3YTY2OTUiLCJleHAiOjE3NDc0Mzg2MjZ9.krop37f_-mPIZzn-X5nu2xslpeliL6pjS6HVcNWPWT4", "Content-Type": "application/json" }
bundle.js:33488:19
API Request to http://localhost:8000/books/f61a4fd1-7ca1-419a-8a45-a155d65d0d96/world-building/customizations bundle.js:33488:19
Token found in localStorage: eyJhbGciOi... bundle.js:33488:19
Request headers: 
Object { Authorization: "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI2MTBmOGRmYy1kM2M1LTQ4ZjYtYjdjOC02OTNlNmQ3YTY2OTUiLCJleHAiOjE3NDc0Mzg2MjZ9.krop37f_-mPIZzn-X5nu2xslpeliL6pjS6HVcNWPWT4", "Content-Type": "application/json" }
bundle.js:33488:19
API Request to http://localhost:8000/books/f61a4fd1-7ca1-419a-8a45-a155d65d0d96/world-building/relationships bundle.js:33488:19
Token found in localStorage: eyJhbGciOi... bundle.js:33488:19
Request headers: 
Object { Authorization: "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI2MTBmOGRmYy1kM2M1LTQ4ZjYtYjdjOC02OTNlNmQ3YTY2OTUiLCJleHAiOjE3NDc0Mzg2MjZ9.krop37f_-mPIZzn-X5nu2xslpeliL6pjS6HVcNWPWT4", "Content-Type": "application/json" }
bundle.js:33488:19
API Request to http://localhost:8000/books/f61a4fd1-7ca1-419a-8a45-a155d65d0d96/world-building/tables/schemas bundle.js:33488:19
Token found in localStorage: eyJhbGciOi... bundle.js:33488:19
Request headers: 
Object { Authorization: "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI2MTBmOGRmYy1kM2M1LTQ4ZjYtYjdjOC02OTNlNmQ3YTY2OTUiLCJleHAiOjE3NDc0Mzg2MjZ9.krop37f_-mPIZzn-X5nu2xslpeliL6pjS6HVcNWPWT4", "Content-Type": "application/json" }
bundle.js:33488:19
 Action: worldBuilding/fetchAllElementsWithChildren/pending bundle.js:211494:13
 Previous State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
 Action 
Object { type: "worldBuilding/fetchAllElementsWithChildren/pending", payload: undefined, meta: {…} }
bundle.js:33488:19
 Next State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
Redux - Fetching all elements with children for relationship view, bookId: f61a4fd1-7ca1-419a-8a45-a155d65d0d96 bundle.js:33488:19
Redux - Categories found: 0 bundle.js:33488:19
Redux - No categories found, fetching world building data first bundle.js:33488:19
 Action: worldBuilding/fetchData/pending bundle.js:211494:13
 Previous State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
 Action 
Object { type: "worldBuilding/fetchData/pending", payload: undefined, meta: {…} }
bundle.js:33488:19
 Next State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
Fetching world building data for book: f61a4fd1-7ca1-419a-8a45-a155d65d0d96 bundle.js:33488:19
API Request to http://localhost:8000/books/f61a4fd1-7ca1-419a-8a45-a155d65d0d96/world-building/categories bundle.js:33488:19
Token found in localStorage: eyJhbGciOi... bundle.js:33488:19
Request headers: 
Object { Authorization: "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI2MTBmOGRmYy1kM2M1LTQ4ZjYtYjdjOC02OTNlNmQ3YTY2OTUiLCJleHAiOjE3NDc0Mzg2MjZ9.krop37f_-mPIZzn-X5nu2xslpeliL6pjS6HVcNWPWT4", "Content-Type": "application/json" }
bundle.js:33488:19
API Request to http://localhost:8000/books/f61a4fd1-7ca1-419a-8a45-a155d65d0d96/world-building/customizations bundle.js:33488:19
Token found in localStorage: eyJhbGciOi... bundle.js:33488:19
Request headers: 
Object { Authorization: "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI2MTBmOGRmYy1kM2M1LTQ4ZjYtYjdjOC02OTNlNmQ3YTY2OTUiLCJleHAiOjE3NDc0Mzg2MjZ9.krop37f_-mPIZzn-X5nu2xslpeliL6pjS6HVcNWPWT4", "Content-Type": "application/json" }
bundle.js:33488:19
API Request to http://localhost:8000/books/f61a4fd1-7ca1-419a-8a45-a155d65d0d96/world-building/relationships bundle.js:33488:19
Token found in localStorage: eyJhbGciOi... bundle.js:33488:19
Request headers: 
Object { Authorization: "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI2MTBmOGRmYy1kM2M1LTQ4ZjYtYjdjOC02OTNlNmQ3YTY2OTUiLCJleHAiOjE3NDc0Mzg2MjZ9.krop37f_-mPIZzn-X5nu2xslpeliL6pjS6HVcNWPWT4", "Content-Type": "application/json" }
bundle.js:33488:19
API Request to http://localhost:8000/books/f61a4fd1-7ca1-419a-8a45-a155d65d0d96/world-building/tables/schemas bundle.js:33488:19
Token found in localStorage: eyJhbGciOi... bundle.js:33488:19
Request headers: 
Object { Authorization: "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI2MTBmOGRmYy1kM2M1LTQ4ZjYtYjdjOC02OTNlNmQ3YTY2OTUiLCJleHAiOjE3NDc0Mzg2MjZ9.krop37f_-mPIZzn-X5nu2xslpeliL6pjS6HVcNWPWT4", "Content-Type": "application/json" }
bundle.js:33488:19
 Action: books/fetchById/fulfilled bundle.js:211494:13
 Previous State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
 Action 
Object { type: "books/fetchById/fulfilled", payload: {…}, meta: {…} }
bundle.js:33488:19
fetchBookById.fulfilled with payload: 
Object { book_id: "f61a4fd1-7ca1-419a-8a45-a155d65d0d96", user_id: "610f8dfc-d3c5-48f6-b7c8-693e6d7a6695", title: "NewBook1", author: "Matt Griffin", genre: "romance", description: "This is a Sci-Fi Romance novel based around the galaxy of Andromeda ", created_at: "2025-04-24T10:28:58.475849", updated_at: "2025-04-24T21:59:24.414649" }
bundle.js:33488:19
Updating book in books array. Before: 
Proxy { <target>: null, <handler>: null }
bundle.js:33488:19
After update: 
Object { book_id: "f61a4fd1-7ca1-419a-8a45-a155d65d0d96", user_id: "610f8dfc-d3c5-48f6-b7c8-693e6d7a6695", title: "NewBook1", author: "Matt Griffin", genre: "romance", description: "This is a Sci-Fi Romance novel based around the galaxy of Andromeda ", created_at: "2025-04-24T10:28:58.475849", updated_at: "2025-04-24T21:59:24.414649" }
bundle.js:33488:19
[DEBUG] selectCurrentChapter - chapters: 
Array []
bundle.js:33488:19
[DEBUG] selectCurrentChapter - currentChapterId: null bundle.js:33488:19
[DEBUG] selectCurrentChapter - No current chapter ID bundle.js:33488:19
 Next State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
App.js: Book details fetched successfully bundle.js:33488:19
API response status for http://localhost:8000/books/f61a4fd1-7ca1-419a-8a45-a155d65d0d96/world-building/categories: 200 bundle.js:33488:19
API response status for http://localhost:8000/books/f61a4fd1-7ca1-419a-8a45-a155d65d0d96/world-building/customizations: 200 bundle.js:33488:19
API response status for http://localhost:8000/books/f61a4fd1-7ca1-419a-8a45-a155d65d0d96/world-building/relationships: 200 bundle.js:33488:19
API response status for http://localhost:8000/books/f61a4fd1-7ca1-419a-8a45-a155d65d0d96/world-building/tables/schemas: 200 bundle.js:33488:19
Successfully fetched world building data: 
Object { categories: (7) […], customizations: (7) […], relationships: [], tableSchemas: [] }
bundle.js:33488:19
Fetched relationships: 
Array []
bundle.js:33488:19
 Action: worldBuilding/fetchData/fulfilled bundle.js:211494:13
 Previous State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
 Action 
Object { type: "worldBuilding/fetchData/fulfilled", payload: {…}, meta: {…} }
bundle.js:33488:19
Processing relationships in reducer: 
Array []
bundle.js:33488:19
[DEBUG] selectCurrentChapter - chapters: 
Array []
bundle.js:33488:19
[DEBUG] selectCurrentChapter - currentChapterId: null bundle.js:33488:19
[DEBUG] selectCurrentChapter - No current chapter ID bundle.js:33488:19
selectAllElementsForRelationshipView - elementsById: 0 bundle.js:33488:19
selectAllElementsForRelationshipView - allIds: 0 bundle.js:33488:19
selectAllElementsForRelationshipView - No elements found bundle.js:33488:19
selectAllRelationships - relationshipsById: 
Object {  }
bundle.js:33488:19
selectAllRelationships - allIds: 
Array []
bundle.js:33488:19
selectAllRelationships - returning relationships: 
Array []
bundle.js:33488:19
 Next State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
WorldBuildingPage - allRelationships: 
Array []
bundle.js:33488:19
WorldBuildingPage - elementRelationships: 
Array []
bundle.js:33488:19
WorldBuildingPage - allRelationships:  
Array []
bundle.js:33488:19
WorldBuildingPage - elementRelationships:  
Array []
bundle.js:33488:19
Token found in localStorage: eyJhbGciOi... bundle.js:33488:19
Using headers: 
Object { Authorization: "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI2MTBmOGRmYy1kM2M1LTQ4ZjYtYjdjOC02OTNlNmQ3YTY2OTUiLCJleHAiOjE3NDc0Mzg2MjZ9.krop37f_-mPIZzn-X5nu2xslpeliL6pjS6HVcNWPWT4", "Content-Type": "application/json" }
bundle.js:33488:19
WorldBuildingPageContainer - Active category changed, fetching elements: cat_cosmology_physical bundle.js:33488:19
 Action: worldBuilding/fetchElementsForCategory/pending bundle.js:211494:13
 Previous State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
 Action 
Object { type: "worldBuilding/fetchElementsForCategory/pending", payload: undefined, meta: {…} }
bundle.js:33488:19
[DEBUG] selectCurrentChapter - chapters: 
Array []
bundle.js:33488:19
[DEBUG] selectCurrentChapter - currentChapterId: null bundle.js:33488:19
[DEBUG] selectCurrentChapter - No current chapter ID bundle.js:33488:19
 Next State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
Redux - Fetching elements for category: 
Object { bookId: "f61a4fd1-7ca1-419a-8a45-a155d65d0d96", categoryId: "cat_cosmology_physical" }
bundle.js:33488:19
API Service - Fetching world elements: 
Object { bookId: "f61a4fd1-7ca1-419a-8a45-a155d65d0d96", category: "cat_cosmology_physical" }
bundle.js:33488:19
API Request to http://localhost:8000/books/f61a4fd1-7ca1-419a-8a45-a155d65d0d96/world-building/elements?category=cat_cosmology_physical bundle.js:33488:19
Token found in localStorage: eyJhbGciOi... bundle.js:33488:19
Request headers: 
Object { Authorization: "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI2MTBmOGRmYy1kM2M1LTQ4ZjYtYjdjOC02OTNlNmQ3YTY2OTUiLCJleHAiOjE3NDc0Mzg2MjZ9.krop37f_-mPIZzn-X5nu2xslpeliL6pjS6HVcNWPWT4", "Content-Type": "application/json" }
bundle.js:33488:19
Token found in localStorage: eyJhbGciOi... bundle.js:33488:19
Using headers: 
Object { Authorization: "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI2MTBmOGRmYy1kM2M1LTQ4ZjYtYjdjOC02OTNlNmQ3YTY2OTUiLCJleHAiOjE3NDc0Mzg2MjZ9.krop37f_-mPIZzn-X5nu2xslpeliL6pjS6HVcNWPWT4", "Content-Type": "application/json" }
bundle.js:33488:19
WorldBuildingPage - allRelationships: 
Array []
bundle.js:33488:19
WorldBuildingPage - elementRelationships: 
Array []
bundle.js:33488:19
WorldBuildingPage - allRelationships:  
Array []
bundle.js:33488:19
WorldBuildingPage - elementRelationships:  
Array []
bundle.js:33488:19
API response status for http://localhost:8000/books/f61a4fd1-7ca1-419a-8a45-a155d65d0d96/world-building/relationships: 200 bundle.js:33488:19
Raw response: <!DOCTYPE html>
<html lang="en" class="theme-light">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta
      name="description"
      content="Web site created using create-react-app"
    />
    <link rel="apple-touch-icon" href="/logo192.png" />
    <!--
      manifest.json provides metadata used when your web app is installed on a
      user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
    -->
    <link rel="manifest" href="/manifest.json" />
    <!--
      Notice the use of  in the tags above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.

      Unlike "/favicon.ico" or "favicon.ico", "/favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.
    -->
    <title>React App</title>
  <script defer src="/static/js/bundle.js"></script></head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->
  </body>
</html>
bundle.js:33488:19
Error parsing JSON: SyntaxError: JSON.parse: unexpected character at line 1 column 1 of the JSON data
    fetchTemplates TemplateBrowser.js:50
<anonymous code>:1:145535
Error fetching templates: SyntaxError: JSON.parse: unexpected character at line 1 column 1 of the JSON data <anonymous code>:1:145535
Raw response: <!DOCTYPE html>
<html lang="en" class="theme-light">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta
      name="description"
      content="Web site created using create-react-app"
    />
    <link rel="apple-touch-icon" href="/logo192.png" />
    <!--
      manifest.json provides metadata used when your web app is installed on a
      user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
    -->
    <link rel="manifest" href="/manifest.json" />
    <!--
      Notice the use of  in the tags above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.

      Unlike "/favicon.ico" or "favicon.ico", "/favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.
    -->
    <title>React App</title>
  <script defer src="/static/js/bundle.js"></script></head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->
  </body>
</html>
bundle.js:33488:19
Error parsing JSON: SyntaxError: JSON.parse: unexpected character at line 1 column 1 of the JSON data
    fetchTemplates TemplateBrowser.js:50
<anonymous code>:1:145535
Error fetching templates: SyntaxError: JSON.parse: unexpected character at line 1 column 1 of the JSON data <anonymous code>:1:145535
API response status for http://localhost:8000/books/f61a4fd1-7ca1-419a-8a45-a155d65d0d96/world-building/categories: 200 bundle.js:33488:19
API response status for http://localhost:8000/books/f61a4fd1-7ca1-419a-8a45-a155d65d0d96/world-building/customizations: 200 bundle.js:33488:19
API response status for http://localhost:8000/books/f61a4fd1-7ca1-419a-8a45-a155d65d0d96/world-building/tables/schemas: 200 bundle.js:33488:19
Successfully fetched world building data: 
Object { categories: (7) […], customizations: (7) […], relationships: [], tableSchemas: [] }
bundle.js:33488:19
Fetched relationships: 
Array []
bundle.js:33488:19
 Action: worldBuilding/fetchData/fulfilled bundle.js:211494:13
 Previous State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
 Action 
Object { type: "worldBuilding/fetchData/fulfilled", payload: {…}, meta: {…} }
bundle.js:33488:19
Processing relationships in reducer: 
Array []
bundle.js:33488:19
[DEBUG] selectCurrentChapter - chapters: 
Array []
bundle.js:33488:19
[DEBUG] selectCurrentChapter - currentChapterId: null bundle.js:33488:19
[DEBUG] selectCurrentChapter - No current chapter ID bundle.js:33488:19
selectAllElementsForRelationshipView - elementsById: 0 bundle.js:33488:19
selectAllElementsForRelationshipView - allIds: 0 bundle.js:33488:19
selectAllElementsForRelationshipView - No elements found bundle.js:33488:19
selectAllRelationships - relationshipsById: 
Object {  }
bundle.js:33488:19
selectAllRelationships - allIds: 
Array []
bundle.js:33488:19
selectAllRelationships - returning relationships: 
Array []
bundle.js:33488:19
 Next State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
WorldBuildingPage - allRelationships: 
Array []
bundle.js:33488:19
WorldBuildingPage - elementRelationships: 
Array []
bundle.js:33488:19
WorldBuildingPage - allRelationships:  
Array []
bundle.js:33488:19
WorldBuildingPage - elementRelationships:  
Array []
bundle.js:33488:19
Token found in localStorage: eyJhbGciOi... bundle.js:33488:19
Using headers: 
Object { Authorization: "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI2MTBmOGRmYy1kM2M1LTQ4ZjYtYjdjOC02OTNlNmQ3YTY2OTUiLCJleHAiOjE3NDc0Mzg2MjZ9.krop37f_-mPIZzn-X5nu2xslpeliL6pjS6HVcNWPWT4", "Content-Type": "application/json" }
bundle.js:33488:19
Redux - Updated categories after fetch: 7 bundle.js:33488:19
 Action: worldBuilding/fetchElementsForCategory/pending bundle.js:211494:13
 Previous State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
 Action 
Object { type: "worldBuilding/fetchElementsForCategory/pending", payload: undefined, meta: {…} }
bundle.js:33488:19
[DEBUG] selectCurrentChapter - chapters: 
Array []
bundle.js:33488:19
[DEBUG] selectCurrentChapter - currentChapterId: null bundle.js:33488:19
[DEBUG] selectCurrentChapter - No current chapter ID bundle.js:33488:19
 Next State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
Redux - Fetching elements for category: 
Object { bookId: "f61a4fd1-7ca1-419a-8a45-a155d65d0d96", categoryId: "cat_cosmology_physical" }
bundle.js:33488:19
API Service - Fetching world elements: 
Object { bookId: "f61a4fd1-7ca1-419a-8a45-a155d65d0d96", category: "cat_cosmology_physical" }
bundle.js:33488:19
API Request to http://localhost:8000/books/f61a4fd1-7ca1-419a-8a45-a155d65d0d96/world-building/elements?category=cat_cosmology_physical bundle.js:33488:19
Token found in localStorage: eyJhbGciOi... bundle.js:33488:19
Request headers: 
Object { Authorization: "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI2MTBmOGRmYy1kM2M1LTQ4ZjYtYjdjOC02OTNlNmQ3YTY2OTUiLCJleHAiOjE3NDc0Mzg2MjZ9.krop37f_-mPIZzn-X5nu2xslpeliL6pjS6HVcNWPWT4", "Content-Type": "application/json" }
bundle.js:33488:19
 Action: worldBuilding/fetchElementsForCategory/pending bundle.js:211494:13
 Previous State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
 Action 
Object { type: "worldBuilding/fetchElementsForCategory/pending", payload: undefined, meta: {…} }
bundle.js:33488:19
 Next State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
Redux - Fetching elements for category: 
Object { bookId: "f61a4fd1-7ca1-419a-8a45-a155d65d0d96", categoryId: "cat_cultural_social" }
bundle.js:33488:19
API Service - Fetching world elements: 
Object { bookId: "f61a4fd1-7ca1-419a-8a45-a155d65d0d96", category: "cat_cultural_social" }
bundle.js:33488:19
API Request to http://localhost:8000/books/f61a4fd1-7ca1-419a-8a45-a155d65d0d96/world-building/elements?category=cat_cultural_social bundle.js:33488:19
Token found in localStorage: eyJhbGciOi... bundle.js:33488:19
Request headers: 
Object { Authorization: "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI2MTBmOGRmYy1kM2M1LTQ4ZjYtYjdjOC02OTNlNmQ3YTY2OTUiLCJleHAiOjE3NDc0Mzg2MjZ9.krop37f_-mPIZzn-X5nu2xslpeliL6pjS6HVcNWPWT4", "Content-Type": "application/json" }
bundle.js:33488:19
 Action: worldBuilding/fetchElementsForCategory/pending bundle.js:211494:13
 Previous State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
 Action 
Object { type: "worldBuilding/fetchElementsForCategory/pending", payload: undefined, meta: {…} }
bundle.js:33488:19
 Next State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
Redux - Fetching elements for category: 
Object { bookId: "f61a4fd1-7ca1-419a-8a45-a155d65d0d96", categoryId: "cat_magical_supernatural" }
bundle.js:33488:19
API Service - Fetching world elements: 
Object { bookId: "f61a4fd1-7ca1-419a-8a45-a155d65d0d96", category: "cat_magical_supernatural" }
bundle.js:33488:19
API Request to http://localhost:8000/books/f61a4fd1-7ca1-419a-8a45-a155d65d0d96/world-building/elements?category=cat_magical_supernatural bundle.js:33488:19
Token found in localStorage: eyJhbGciOi... bundle.js:33488:19
Request headers: 
Object { Authorization: "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI2MTBmOGRmYy1kM2M1LTQ4ZjYtYjdjOC02OTNlNmQ3YTY2OTUiLCJleHAiOjE3NDc0Mzg2MjZ9.krop37f_-mPIZzn-X5nu2xslpeliL6pjS6HVcNWPWT4", "Content-Type": "application/json" }
bundle.js:33488:19
 Action: worldBuilding/fetchElementsForCategory/pending bundle.js:211494:13
 Previous State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
 Action 
Object { type: "worldBuilding/fetchElementsForCategory/pending", payload: undefined, meta: {…} }
bundle.js:33488:19
 Next State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
Redux - Fetching elements for category: 
Object { bookId: "f61a4fd1-7ca1-419a-8a45-a155d65d0d96", categoryId: "cat_knowledge_technology" }
bundle.js:33488:19
API Service - Fetching world elements: 
Object { bookId: "f61a4fd1-7ca1-419a-8a45-a155d65d0d96", category: "cat_knowledge_technology" }
bundle.js:33488:19
API Request to http://localhost:8000/books/f61a4fd1-7ca1-419a-8a45-a155d65d0d96/world-building/elements?category=cat_knowledge_technology bundle.js:33488:19
Token found in localStorage: eyJhbGciOi... bundle.js:33488:19
Request headers: 
Object { Authorization: "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI2MTBmOGRmYy1kM2M1LTQ4ZjYtYjdjOC02OTNlNmQ3YTY2OTUiLCJleHAiOjE3NDc0Mzg2MjZ9.krop37f_-mPIZzn-X5nu2xslpeliL6pjS6HVcNWPWT4", "Content-Type": "application/json" }
bundle.js:33488:19
 Action: worldBuilding/fetchElementsForCategory/pending bundle.js:211494:13
 Previous State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
 Action 
Object { type: "worldBuilding/fetchElementsForCategory/pending", payload: undefined, meta: {…} }
bundle.js:33488:19
 Next State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
Redux - Fetching elements for category: 
Object { bookId: "f61a4fd1-7ca1-419a-8a45-a155d65d0d96", categoryId: "cat_economic_material" }
bundle.js:33488:19
API Service - Fetching world elements: 
Object { bookId: "f61a4fd1-7ca1-419a-8a45-a155d65d0d96", category: "cat_economic_material" }
bundle.js:33488:19
API Request to http://localhost:8000/books/f61a4fd1-7ca1-419a-8a45-a155d65d0d96/world-building/elements?category=cat_economic_material bundle.js:33488:19
Token found in localStorage: eyJhbGciOi... bundle.js:33488:19
Request headers: 
Object { Authorization: "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI2MTBmOGRmYy1kM2M1LTQ4ZjYtYjdjOC02OTNlNmQ3YTY2OTUiLCJleHAiOjE3NDc0Mzg2MjZ9.krop37f_-mPIZzn-X5nu2xslpeliL6pjS6HVcNWPWT4", "Content-Type": "application/json" }
bundle.js:33488:19
 Action: worldBuilding/fetchElementsForCategory/pending bundle.js:211494:13
 Previous State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
 Action 
Object { type: "worldBuilding/fetchElementsForCategory/pending", payload: undefined, meta: {…} }
bundle.js:33488:19
 Next State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
Redux - Fetching elements for category: 
Object { bookId: "f61a4fd1-7ca1-419a-8a45-a155d65d0d96", categoryId: "cat_temporal_historical" }
bundle.js:33488:19
API Service - Fetching world elements: 
Object { bookId: "f61a4fd1-7ca1-419a-8a45-a155d65d0d96", category: "cat_temporal_historical" }
bundle.js:33488:19
API Request to http://localhost:8000/books/f61a4fd1-7ca1-419a-8a45-a155d65d0d96/world-building/elements?category=cat_temporal_historical bundle.js:33488:19
Token found in localStorage: eyJhbGciOi... bundle.js:33488:19
Request headers: 
Object { Authorization: "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI2MTBmOGRmYy1kM2M1LTQ4ZjYtYjdjOC02OTNlNmQ3YTY2OTUiLCJleHAiOjE3NDc0Mzg2MjZ9.krop37f_-mPIZzn-X5nu2xslpeliL6pjS6HVcNWPWT4", "Content-Type": "application/json" }
bundle.js:33488:19
 Action: worldBuilding/fetchElementsForCategory/pending bundle.js:211494:13
 Previous State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
 Action 
Object { type: "worldBuilding/fetchElementsForCategory/pending", payload: undefined, meta: {…} }
bundle.js:33488:19
 Next State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
Redux - Fetching elements for category: 
Object { bookId: "f61a4fd1-7ca1-419a-8a45-a155d65d0d96", categoryId: "cat_interactive_game" }
bundle.js:33488:19
API Service - Fetching world elements: 
Object { bookId: "f61a4fd1-7ca1-419a-8a45-a155d65d0d96", category: "cat_interactive_game" }
bundle.js:33488:19
API Request to http://localhost:8000/books/f61a4fd1-7ca1-419a-8a45-a155d65d0d96/world-building/elements?category=cat_interactive_game bundle.js:33488:19
Token found in localStorage: eyJhbGciOi... bundle.js:33488:19
Request headers: 
Object { Authorization: "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI2MTBmOGRmYy1kM2M1LTQ4ZjYtYjdjOC02OTNlNmQ3YTY2OTUiLCJleHAiOjE3NDc0Mzg2MjZ9.krop37f_-mPIZzn-X5nu2xslpeliL6pjS6HVcNWPWT4", "Content-Type": "application/json" }
bundle.js:33488:19
WorldBuildingPage - allRelationships: 
Array []
bundle.js:33488:19
WorldBuildingPage - elementRelationships: 
Array []
bundle.js:33488:19
WorldBuildingPage - allRelationships:  
Array []
bundle.js:33488:19
WorldBuildingPage - elementRelationships:  
Array []
bundle.js:33488:19
API response status for http://localhost:8000/books/f61a4fd1-7ca1-419a-8a45-a155d65d0d96/world-building/relationships: 200 bundle.js:33488:19
Raw response: <!DOCTYPE html>
<html lang="en" class="theme-light">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta
      name="description"
      content="Web site created using create-react-app"
    />
    <link rel="apple-touch-icon" href="/logo192.png" />
    <!--
      manifest.json provides metadata used when your web app is installed on a
      user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
    -->
    <link rel="manifest" href="/manifest.json" />
    <!--
      Notice the use of  in the tags above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.

      Unlike "/favicon.ico" or "favicon.ico", "/favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.
    -->
    <title>React App</title>
  <script defer src="/static/js/bundle.js"></script></head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->
  </body>
</html>
bundle.js:33488:19
Error parsing JSON: SyntaxError: JSON.parse: unexpected character at line 1 column 1 of the JSON data
    fetchTemplates TemplateBrowser.js:50
<anonymous code>:1:145535
Error fetching templates: SyntaxError: JSON.parse: unexpected character at line 1 column 1 of the JSON data <anonymous code>:1:145535
API response status for http://localhost:8000/books/f61a4fd1-7ca1-419a-8a45-a155d65d0d96/world-building/categories: 200 bundle.js:33488:19
API response status for http://localhost:8000/books/f61a4fd1-7ca1-419a-8a45-a155d65d0d96/world-building/customizations: 200 bundle.js:33488:19
API response status for http://localhost:8000/books/f61a4fd1-7ca1-419a-8a45-a155d65d0d96/world-building/tables/schemas: 200 bundle.js:33488:19
Successfully fetched world building data: 
Object { categories: (7) […], customizations: (7) […], relationships: [], tableSchemas: [] }
bundle.js:33488:19
Fetched relationships: 
Array []
bundle.js:33488:19
 Action: worldBuilding/fetchData/fulfilled bundle.js:211494:13
 Previous State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
 Action 
Object { type: "worldBuilding/fetchData/fulfilled", payload: {…}, meta: {…} }
bundle.js:33488:19
Processing relationships in reducer: 
Array []
bundle.js:33488:19
[DEBUG] selectCurrentChapter - chapters: 
Array []
bundle.js:33488:19
[DEBUG] selectCurrentChapter - currentChapterId: null bundle.js:33488:19
[DEBUG] selectCurrentChapter - No current chapter ID bundle.js:33488:19
selectAllElementsForRelationshipView - elementsById: 0 bundle.js:33488:19
selectAllElementsForRelationshipView - allIds: 0 bundle.js:33488:19
selectAllElementsForRelationshipView - No elements found bundle.js:33488:19
selectAllRelationships - relationshipsById: 
Object {  }
bundle.js:33488:19
selectAllRelationships - allIds: 
Array []
bundle.js:33488:19
selectAllRelationships - returning relationships: 
Array []
bundle.js:33488:19
 Next State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
WorldBuildingPage - allRelationships: 
Array []
bundle.js:33488:19
WorldBuildingPage - elementRelationships: 
Array []
bundle.js:33488:19
WorldBuildingPage - allRelationships:  
Array []
bundle.js:33488:19
WorldBuildingPage - elementRelationships:  
Array []
bundle.js:33488:19
Token found in localStorage: eyJhbGciOi... bundle.js:33488:19
Using headers: 
Object { Authorization: "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI2MTBmOGRmYy1kM2M1LTQ4ZjYtYjdjOC02OTNlNmQ3YTY2OTUiLCJleHAiOjE3NDc0Mzg2MjZ9.krop37f_-mPIZzn-X5nu2xslpeliL6pjS6HVcNWPWT4", "Content-Type": "application/json" }
bundle.js:33488:19
API response status for http://localhost:8000/books/f61a4fd1-7ca1-419a-8a45-a155d65d0d96/world-building/elements?category=cat_cosmology_physical: 200 bundle.js:33488:19
API Service - Fetched world elements: 
Array []
bundle.js:33488:19
Redux - Fetched elements for category: 
Array []
bundle.js:33488:19
 Action: worldBuilding/fetchElementsForCategory/fulfilled bundle.js:211494:13
 Previous State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
 Action 
Object { type: "worldBuilding/fetchElementsForCategory/fulfilled", payload: {…}, meta: {…} }
bundle.js:33488:19
[DEBUG] selectCurrentChapter - chapters: 
Array []
bundle.js:33488:19
[DEBUG] selectCurrentChapter - currentChapterId: null bundle.js:33488:19
[DEBUG] selectCurrentChapter - No current chapter ID bundle.js:33488:19
 Next State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
WorldBuildingPage - allRelationships: 
Array []
bundle.js:33488:19
WorldBuildingPage - elementRelationships: 
Array []
bundle.js:33488:19
WorldBuildingPage - allRelationships:  
Array []
bundle.js:33488:19
WorldBuildingPage - elementRelationships:  
Array []
bundle.js:33488:19
API response status for http://localhost:8000/books/f61a4fd1-7ca1-419a-8a45-a155d65d0d96/world-building/relationships: 200 bundle.js:33488:19
Raw response: <!DOCTYPE html>
<html lang="en" class="theme-light">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta
      name="description"
      content="Web site created using create-react-app"
    />
    <link rel="apple-touch-icon" href="/logo192.png" />
    <!--
      manifest.json provides metadata used when your web app is installed on a
      user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
    -->
    <link rel="manifest" href="/manifest.json" />
    <!--
      Notice the use of  in the tags above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.

      Unlike "/favicon.ico" or "favicon.ico", "/favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.
    -->
    <title>React App</title>
  <script defer src="/static/js/bundle.js"></script></head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->
  </body>
</html>
bundle.js:33488:19
Error parsing JSON: SyntaxError: JSON.parse: unexpected character at line 1 column 1 of the JSON data
    fetchTemplates TemplateBrowser.js:50
<anonymous code>:1:145535
Error fetching templates: SyntaxError: JSON.parse: unexpected character at line 1 column 1 of the JSON data <anonymous code>:1:145535
API response status for http://localhost:8000/books/f61a4fd1-7ca1-419a-8a45-a155d65d0d96/world-building/elements?category=cat_cultural_social: 200 bundle.js:33488:19
API Service - Fetched world elements: 
Array []
bundle.js:33488:19
Redux - Fetched elements for category: 
Array []
bundle.js:33488:19
 Action: worldBuilding/fetchElementsForCategory/fulfilled bundle.js:211494:13
 Previous State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
 Action 
Object { type: "worldBuilding/fetchElementsForCategory/fulfilled", payload: {…}, meta: {…} }
bundle.js:33488:19
[DEBUG] selectCurrentChapter - chapters: 
Array []
bundle.js:33488:19
[DEBUG] selectCurrentChapter - currentChapterId: null bundle.js:33488:19
[DEBUG] selectCurrentChapter - No current chapter ID bundle.js:33488:19
 Next State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
WorldBuildingPage - allRelationships: 
Array []
bundle.js:33488:19
WorldBuildingPage - elementRelationships: 
Array []
bundle.js:33488:19
WorldBuildingPage - allRelationships:  
Array []
bundle.js:33488:19
WorldBuildingPage - elementRelationships:  
Array []
bundle.js:33488:19
API response status for http://localhost:8000/books/f61a4fd1-7ca1-419a-8a45-a155d65d0d96/world-building/categories: 200 bundle.js:33488:19
API response status for http://localhost:8000/books/f61a4fd1-7ca1-419a-8a45-a155d65d0d96/world-building/customizations: 200 bundle.js:33488:19
API response status for http://localhost:8000/books/f61a4fd1-7ca1-419a-8a45-a155d65d0d96/world-building/tables/schemas: 200 bundle.js:33488:19
Successfully fetched world building data: 
Object { categories: (7) […], customizations: (7) […], relationships: [], tableSchemas: [] }
bundle.js:33488:19
Fetched relationships: 
Array []
bundle.js:33488:19
 Action: worldBuilding/fetchData/fulfilled bundle.js:211494:13
 Previous State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
 Action 
Object { type: "worldBuilding/fetchData/fulfilled", payload: {…}, meta: {…} }
bundle.js:33488:19
Processing relationships in reducer: 
Array []
bundle.js:33488:19
[DEBUG] selectCurrentChapter - chapters: 
Array []
bundle.js:33488:19
[DEBUG] selectCurrentChapter - currentChapterId: null bundle.js:33488:19
[DEBUG] selectCurrentChapter - No current chapter ID bundle.js:33488:19
selectAllElementsForRelationshipView - elementsById: 0 bundle.js:33488:19
selectAllElementsForRelationshipView - allIds: 0 bundle.js:33488:19
selectAllElementsForRelationshipView - No elements found bundle.js:33488:19
selectAllRelationships - relationshipsById: 
Object {  }
bundle.js:33488:19
selectAllRelationships - allIds: 
Array []
bundle.js:33488:19
selectAllRelationships - returning relationships: 
Array []
bundle.js:33488:19
 Next State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
WorldBuildingPage - allRelationships: 
Array []
bundle.js:33488:19
WorldBuildingPage - elementRelationships: 
Array []
bundle.js:33488:19
WorldBuildingPage - allRelationships:  
Array []
bundle.js:33488:19
WorldBuildingPage - elementRelationships:  
Array []
bundle.js:33488:19
Token found in localStorage: eyJhbGciOi... bundle.js:33488:19
Using headers: 
Object { Authorization: "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI2MTBmOGRmYy1kM2M1LTQ4ZjYtYjdjOC02OTNlNmQ3YTY2OTUiLCJleHAiOjE3NDc0Mzg2MjZ9.krop37f_-mPIZzn-X5nu2xslpeliL6pjS6HVcNWPWT4", "Content-Type": "application/json" }
bundle.js:33488:19
Redux - Updated categories after fetch: 7 bundle.js:33488:19
 Action: worldBuilding/fetchElementsForCategory/pending bundle.js:211494:13
 Previous State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
 Action 
Object { type: "worldBuilding/fetchElementsForCategory/pending", payload: undefined, meta: {…} }
bundle.js:33488:19
[DEBUG] selectCurrentChapter - chapters: 
Array []
bundle.js:33488:19
[DEBUG] selectCurrentChapter - currentChapterId: null bundle.js:33488:19
[DEBUG] selectCurrentChapter - No current chapter ID bundle.js:33488:19
 Next State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
Redux - Fetching elements for category: 
Object { bookId: "f61a4fd1-7ca1-419a-8a45-a155d65d0d96", categoryId: "cat_cosmology_physical" }
bundle.js:33488:19
API Service - Fetching world elements: 
Object { bookId: "f61a4fd1-7ca1-419a-8a45-a155d65d0d96", category: "cat_cosmology_physical" }
bundle.js:33488:19
API Request to http://localhost:8000/books/f61a4fd1-7ca1-419a-8a45-a155d65d0d96/world-building/elements?category=cat_cosmology_physical bundle.js:33488:19
Token found in localStorage: eyJhbGciOi... bundle.js:33488:19
Request headers: 
Object { Authorization: "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI2MTBmOGRmYy1kM2M1LTQ4ZjYtYjdjOC02OTNlNmQ3YTY2OTUiLCJleHAiOjE3NDc0Mzg2MjZ9.krop37f_-mPIZzn-X5nu2xslpeliL6pjS6HVcNWPWT4", "Content-Type": "application/json" }
bundle.js:33488:19
 Action: worldBuilding/fetchElementsForCategory/pending bundle.js:211494:13
 Previous State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
 Action 
Object { type: "worldBuilding/fetchElementsForCategory/pending", payload: undefined, meta: {…} }
bundle.js:33488:19
 Next State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
Redux - Fetching elements for category: 
Object { bookId: "f61a4fd1-7ca1-419a-8a45-a155d65d0d96", categoryId: "cat_cultural_social" }
bundle.js:33488:19
API Service - Fetching world elements: 
Object { bookId: "f61a4fd1-7ca1-419a-8a45-a155d65d0d96", category: "cat_cultural_social" }
bundle.js:33488:19
API Request to http://localhost:8000/books/f61a4fd1-7ca1-419a-8a45-a155d65d0d96/world-building/elements?category=cat_cultural_social bundle.js:33488:19
Token found in localStorage: eyJhbGciOi... bundle.js:33488:19
Request headers: 
Object { Authorization: "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI2MTBmOGRmYy1kM2M1LTQ4ZjYtYjdjOC02OTNlNmQ3YTY2OTUiLCJleHAiOjE3NDc0Mzg2MjZ9.krop37f_-mPIZzn-X5nu2xslpeliL6pjS6HVcNWPWT4", "Content-Type": "application/json" }
bundle.js:33488:19
 Action: worldBuilding/fetchElementsForCategory/pending bundle.js:211494:13
 Previous State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
 Action 
Object { type: "worldBuilding/fetchElementsForCategory/pending", payload: undefined, meta: {…} }
bundle.js:33488:19
 Next State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
Redux - Fetching elements for category: 
Object { bookId: "f61a4fd1-7ca1-419a-8a45-a155d65d0d96", categoryId: "cat_magical_supernatural" }
bundle.js:33488:19
API Service - Fetching world elements: 
Object { bookId: "f61a4fd1-7ca1-419a-8a45-a155d65d0d96", category: "cat_magical_supernatural" }
bundle.js:33488:19
API Request to http://localhost:8000/books/f61a4fd1-7ca1-419a-8a45-a155d65d0d96/world-building/elements?category=cat_magical_supernatural bundle.js:33488:19
Token found in localStorage: eyJhbGciOi... bundle.js:33488:19
Request headers: 
Object { Authorization: "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI2MTBmOGRmYy1kM2M1LTQ4ZjYtYjdjOC02OTNlNmQ3YTY2OTUiLCJleHAiOjE3NDc0Mzg2MjZ9.krop37f_-mPIZzn-X5nu2xslpeliL6pjS6HVcNWPWT4", "Content-Type": "application/json" }
bundle.js:33488:19
 Action: worldBuilding/fetchElementsForCategory/pending bundle.js:211494:13
 Previous State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
 Action 
Object { type: "worldBuilding/fetchElementsForCategory/pending", payload: undefined, meta: {…} }
bundle.js:33488:19
 Next State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
Redux - Fetching elements for category: 
Object { bookId: "f61a4fd1-7ca1-419a-8a45-a155d65d0d96", categoryId: "cat_knowledge_technology" }
bundle.js:33488:19
API Service - Fetching world elements: 
Object { bookId: "f61a4fd1-7ca1-419a-8a45-a155d65d0d96", category: "cat_knowledge_technology" }
bundle.js:33488:19
API Request to http://localhost:8000/books/f61a4fd1-7ca1-419a-8a45-a155d65d0d96/world-building/elements?category=cat_knowledge_technology bundle.js:33488:19
Token found in localStorage: eyJhbGciOi... bundle.js:33488:19
Request headers: 
Object { Authorization: "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI2MTBmOGRmYy1kM2M1LTQ4ZjYtYjdjOC02OTNlNmQ3YTY2OTUiLCJleHAiOjE3NDc0Mzg2MjZ9.krop37f_-mPIZzn-X5nu2xslpeliL6pjS6HVcNWPWT4", "Content-Type": "application/json" }
bundle.js:33488:19
 Action: worldBuilding/fetchElementsForCategory/pending bundle.js:211494:13
 Previous State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
 Action 
Object { type: "worldBuilding/fetchElementsForCategory/pending", payload: undefined, meta: {…} }
bundle.js:33488:19
 Next State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
Redux - Fetching elements for category: 
Object { bookId: "f61a4fd1-7ca1-419a-8a45-a155d65d0d96", categoryId: "cat_economic_material" }
bundle.js:33488:19
API Service - Fetching world elements: 
Object { bookId: "f61a4fd1-7ca1-419a-8a45-a155d65d0d96", category: "cat_economic_material" }
bundle.js:33488:19
API Request to http://localhost:8000/books/f61a4fd1-7ca1-419a-8a45-a155d65d0d96/world-building/elements?category=cat_economic_material bundle.js:33488:19
Token found in localStorage: eyJhbGciOi... bundle.js:33488:19
Request headers: 
Object { Authorization: "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI2MTBmOGRmYy1kM2M1LTQ4ZjYtYjdjOC02OTNlNmQ3YTY2OTUiLCJleHAiOjE3NDc0Mzg2MjZ9.krop37f_-mPIZzn-X5nu2xslpeliL6pjS6HVcNWPWT4", "Content-Type": "application/json" }
bundle.js:33488:19
 Action: worldBuilding/fetchElementsForCategory/pending bundle.js:211494:13
 Previous State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
 Action 
Object { type: "worldBuilding/fetchElementsForCategory/pending", payload: undefined, meta: {…} }
bundle.js:33488:19
 Next State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
Redux - Fetching elements for category: 
Object { bookId: "f61a4fd1-7ca1-419a-8a45-a155d65d0d96", categoryId: "cat_temporal_historical" }
bundle.js:33488:19
API Service - Fetching world elements: 
Object { bookId: "f61a4fd1-7ca1-419a-8a45-a155d65d0d96", category: "cat_temporal_historical" }
bundle.js:33488:19
API Request to http://localhost:8000/books/f61a4fd1-7ca1-419a-8a45-a155d65d0d96/world-building/elements?category=cat_temporal_historical bundle.js:33488:19
Token found in localStorage: eyJhbGciOi... bundle.js:33488:19
Request headers: 
Object { Authorization: "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI2MTBmOGRmYy1kM2M1LTQ4ZjYtYjdjOC02OTNlNmQ3YTY2OTUiLCJleHAiOjE3NDc0Mzg2MjZ9.krop37f_-mPIZzn-X5nu2xslpeliL6pjS6HVcNWPWT4", "Content-Type": "application/json" }
bundle.js:33488:19
 Action: worldBuilding/fetchElementsForCategory/pending bundle.js:211494:13
 Previous State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
 Action 
Object { type: "worldBuilding/fetchElementsForCategory/pending", payload: undefined, meta: {…} }
bundle.js:33488:19
 Next State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
Redux - Fetching elements for category: 
Object { bookId: "f61a4fd1-7ca1-419a-8a45-a155d65d0d96", categoryId: "cat_interactive_game" }
bundle.js:33488:19
API Service - Fetching world elements: 
Object { bookId: "f61a4fd1-7ca1-419a-8a45-a155d65d0d96", category: "cat_interactive_game" }
bundle.js:33488:19
API Request to http://localhost:8000/books/f61a4fd1-7ca1-419a-8a45-a155d65d0d96/world-building/elements?category=cat_interactive_game bundle.js:33488:19
Token found in localStorage: eyJhbGciOi... bundle.js:33488:19
Request headers: 
Object { Authorization: "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI2MTBmOGRmYy1kM2M1LTQ4ZjYtYjdjOC02OTNlNmQ3YTY2OTUiLCJleHAiOjE3NDc0Mzg2MjZ9.krop37f_-mPIZzn-X5nu2xslpeliL6pjS6HVcNWPWT4", "Content-Type": "application/json" }
bundle.js:33488:19
WorldBuildingPage - allRelationships: 
Array []
bundle.js:33488:19
WorldBuildingPage - elementRelationships: 
Array []
bundle.js:33488:19
WorldBuildingPage - allRelationships:  
Array []
bundle.js:33488:19
WorldBuildingPage - elementRelationships:  
Array []
bundle.js:33488:19
API response status for http://localhost:8000/books/f61a4fd1-7ca1-419a-8a45-a155d65d0d96/world-building/elements?category=cat_cosmology_physical: 200 bundle.js:33488:19
API response status for http://localhost:8000/books/f61a4fd1-7ca1-419a-8a45-a155d65d0d96/world-building/elements?category=cat_magical_supernatural: 200 bundle.js:33488:19
API Service - Fetched world elements: 
Array []
bundle.js:33488:19
Redux - Fetched elements for category: 
Array []
bundle.js:33488:19
 Action: worldBuilding/fetchElementsForCategory/fulfilled bundle.js:211494:13
 Previous State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
 Action 
Object { type: "worldBuilding/fetchElementsForCategory/fulfilled", payload: {…}, meta: {…} }
bundle.js:33488:19
[DEBUG] selectCurrentChapter - chapters: 
Array []
bundle.js:33488:19
[DEBUG] selectCurrentChapter - currentChapterId: null bundle.js:33488:19
[DEBUG] selectCurrentChapter - No current chapter ID bundle.js:33488:19
 Next State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
WorldBuildingPage - allRelationships: 
Array []
bundle.js:33488:19
WorldBuildingPage - elementRelationships: 
Array []
bundle.js:33488:19
WorldBuildingPage - allRelationships:  
Array []
bundle.js:33488:19
WorldBuildingPage - elementRelationships:  
Array []
bundle.js:33488:19
Raw response: <!DOCTYPE html>
<html lang="en" class="theme-light">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta
      name="description"
      content="Web site created using create-react-app"
    />
    <link rel="apple-touch-icon" href="/logo192.png" />
    <!--
      manifest.json provides metadata used when your web app is installed on a
      user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
    -->
    <link rel="manifest" href="/manifest.json" />
    <!--
      Notice the use of  in the tags above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.

      Unlike "/favicon.ico" or "favicon.ico", "/favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.
    -->
    <title>React App</title>
  <script defer src="/static/js/bundle.js"></script></head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->
  </body>
</html>
bundle.js:33488:19
Error parsing JSON: SyntaxError: JSON.parse: unexpected character at line 1 column 1 of the JSON data
    fetchTemplates TemplateBrowser.js:50
<anonymous code>:1:145535
API Service - Fetched world elements: 
Array []
bundle.js:33488:19
Redux - Fetched elements for category: 
Array []
bundle.js:33488:19
 Action: worldBuilding/fetchElementsForCategory/fulfilled bundle.js:211494:13
 Previous State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
 Action 
Object { type: "worldBuilding/fetchElementsForCategory/fulfilled", payload: {…}, meta: {…} }
bundle.js:33488:19
[DEBUG] selectCurrentChapter - chapters: 
Array []
bundle.js:33488:19
[DEBUG] selectCurrentChapter - currentChapterId: null bundle.js:33488:19
[DEBUG] selectCurrentChapter - No current chapter ID bundle.js:33488:19
 Next State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
WorldBuildingPage - allRelationships: 
Array []
bundle.js:33488:19
WorldBuildingPage - elementRelationships: 
Array []
bundle.js:33488:19
WorldBuildingPage - allRelationships:  
Array []
bundle.js:33488:19
WorldBuildingPage - elementRelationships:  
Array []
bundle.js:33488:19
Error fetching templates: SyntaxError: JSON.parse: unexpected character at line 1 column 1 of the JSON data <anonymous code>:1:145535
API response status for http://localhost:8000/books/f61a4fd1-7ca1-419a-8a45-a155d65d0d96/world-building/elements?category=cat_knowledge_technology: 200 bundle.js:33488:19
API response status for http://localhost:8000/books/f61a4fd1-7ca1-419a-8a45-a155d65d0d96/world-building/elements?category=cat_economic_material: 200 bundle.js:33488:19
API Service - Fetched world elements: 
Array []
bundle.js:33488:19
Redux - Fetched elements for category: 
Array []
bundle.js:33488:19
 Action: worldBuilding/fetchElementsForCategory/fulfilled bundle.js:211494:13
 Previous State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
 Action 
Object { type: "worldBuilding/fetchElementsForCategory/fulfilled", payload: {…}, meta: {…} }
bundle.js:33488:19
[DEBUG] selectCurrentChapter - chapters: 
Array []
bundle.js:33488:19
[DEBUG] selectCurrentChapter - currentChapterId: null bundle.js:33488:19
[DEBUG] selectCurrentChapter - No current chapter ID bundle.js:33488:19
 Next State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
WorldBuildingPage - allRelationships: 
Array []
bundle.js:33488:19
WorldBuildingPage - elementRelationships: 
Array []
bundle.js:33488:19
WorldBuildingPage - allRelationships:  
Array []
bundle.js:33488:19
WorldBuildingPage - elementRelationships:  
Array []
bundle.js:33488:19
API Service - Fetched world elements: 
Array []
bundle.js:33488:19
Redux - Fetched elements for category: 
Array []
bundle.js:33488:19
 Action: worldBuilding/fetchElementsForCategory/fulfilled bundle.js:211494:13
 Previous State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
 Action 
Object { type: "worldBuilding/fetchElementsForCategory/fulfilled", payload: {…}, meta: {…} }
bundle.js:33488:19
[DEBUG] selectCurrentChapter - chapters: 
Array []
bundle.js:33488:19
[DEBUG] selectCurrentChapter - currentChapterId: null bundle.js:33488:19
[DEBUG] selectCurrentChapter - No current chapter ID bundle.js:33488:19
 Next State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
WorldBuildingPage - allRelationships: 
Array []
bundle.js:33488:19
WorldBuildingPage - elementRelationships: 
Array []
bundle.js:33488:19
WorldBuildingPage - allRelationships:  
Array []
bundle.js:33488:19
WorldBuildingPage - elementRelationships:  
Array []
bundle.js:33488:19
API response status for http://localhost:8000/books/f61a4fd1-7ca1-419a-8a45-a155d65d0d96/world-building/elements?category=cat_temporal_historical: 200 bundle.js:33488:19
API response status for http://localhost:8000/books/f61a4fd1-7ca1-419a-8a45-a155d65d0d96/world-building/elements?category=cat_interactive_game: 200 bundle.js:33488:19
API Service - Fetched world elements: 
Array []
bundle.js:33488:19
Redux - Fetched elements for category: 
Array []
bundle.js:33488:19
 Action: worldBuilding/fetchElementsForCategory/fulfilled bundle.js:211494:13
 Previous State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
 Action 
Object { type: "worldBuilding/fetchElementsForCategory/fulfilled", payload: {…}, meta: {…} }
bundle.js:33488:19
[DEBUG] selectCurrentChapter - chapters: 
Array []
bundle.js:33488:19
[DEBUG] selectCurrentChapter - currentChapterId: null bundle.js:33488:19
[DEBUG] selectCurrentChapter - No current chapter ID bundle.js:33488:19
 Next State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
WorldBuildingPage - allRelationships: 
Array []
bundle.js:33488:19
WorldBuildingPage - elementRelationships: 
Array []
bundle.js:33488:19
WorldBuildingPage - allRelationships:  
Array []
bundle.js:33488:19
WorldBuildingPage - elementRelationships:  
Array []
bundle.js:33488:19
API Service - Fetched world elements: 
Array []
bundle.js:33488:19
Redux - Fetched elements for category: 
Array []
bundle.js:33488:19
 Action: worldBuilding/fetchElementsForCategory/fulfilled bundle.js:211494:13
 Previous State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
 Action 
Object { type: "worldBuilding/fetchElementsForCategory/fulfilled", payload: {…}, meta: {…} }
bundle.js:33488:19
[DEBUG] selectCurrentChapter - chapters: 
Array []
bundle.js:33488:19
[DEBUG] selectCurrentChapter - currentChapterId: null bundle.js:33488:19
[DEBUG] selectCurrentChapter - No current chapter ID bundle.js:33488:19
 Next State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
WorldBuildingPage - allRelationships: 
Array []
bundle.js:33488:19
WorldBuildingPage - elementRelationships: 
Array []
bundle.js:33488:19
WorldBuildingPage - allRelationships:  
Array []
bundle.js:33488:19
WorldBuildingPage - elementRelationships:  
Array []
bundle.js:33488:19
Redux - Root elements with children: 0 bundle.js:33488:19
Redux - Total elements after fetch: 0 bundle.js:33488:19
 Action: worldBuilding/fetchAllElementsWithChildren/fulfilled bundle.js:211494:13
 Previous State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
 Action 
Object { type: "worldBuilding/fetchAllElementsWithChildren/fulfilled", payload: {…}, meta: {…} }
bundle.js:33488:19
 Next State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
API response status for http://localhost:8000/books/f61a4fd1-7ca1-419a-8a45-a155d65d0d96/world-building/elements?category=cat_cosmology_physical: 200 bundle.js:33488:19
API response status for http://localhost:8000/books/f61a4fd1-7ca1-419a-8a45-a155d65d0d96/world-building/elements?category=cat_cultural_social: 200 bundle.js:33488:19
API Service - Fetched world elements: 
Array []
bundle.js:33488:19
Redux - Fetched elements for category: 
Array []
bundle.js:33488:19
 Action: worldBuilding/fetchElementsForCategory/fulfilled bundle.js:211494:13
 Previous State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
 Action 
Object { type: "worldBuilding/fetchElementsForCategory/fulfilled", payload: {…}, meta: {…} }
bundle.js:33488:19
 Next State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
API Service - Fetched world elements: 
Array []
bundle.js:33488:19
Redux - Fetched elements for category: 
Array []
bundle.js:33488:19
 Action: worldBuilding/fetchElementsForCategory/fulfilled bundle.js:211494:13
 Previous State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
 Action 
Object { type: "worldBuilding/fetchElementsForCategory/fulfilled", payload: {…}, meta: {…} }
bundle.js:33488:19
[DEBUG] selectCurrentChapter - chapters: 
Array []
bundle.js:33488:19
[DEBUG] selectCurrentChapter - currentChapterId: null bundle.js:33488:19
[DEBUG] selectCurrentChapter - No current chapter ID bundle.js:33488:19
 Next State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
WorldBuildingPage - allRelationships: 
Array []
bundle.js:33488:19
WorldBuildingPage - elementRelationships: 
Array []
bundle.js:33488:19
WorldBuildingPage - allRelationships:  
Array []
bundle.js:33488:19
WorldBuildingPage - elementRelationships:  
Array []
bundle.js:33488:19
API response status for http://localhost:8000/books/f61a4fd1-7ca1-419a-8a45-a155d65d0d96/world-building/elements?category=cat_magical_supernatural: 200 bundle.js:33488:19
API response status for http://localhost:8000/books/f61a4fd1-7ca1-419a-8a45-a155d65d0d96/world-building/elements?category=cat_knowledge_technology: 200 bundle.js:33488:19
API Service - Fetched world elements: 
Array []
bundle.js:33488:19
Redux - Fetched elements for category: 
Array []
bundle.js:33488:19
 Action: worldBuilding/fetchElementsForCategory/fulfilled bundle.js:211494:13
 Previous State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
 Action 
Object { type: "worldBuilding/fetchElementsForCategory/fulfilled", payload: {…}, meta: {…} }
bundle.js:33488:19
 Next State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
API Service - Fetched world elements: 
Array []
bundle.js:33488:19
Redux - Fetched elements for category: 
Array []
bundle.js:33488:19
 Action: worldBuilding/fetchElementsForCategory/fulfilled bundle.js:211494:13
 Previous State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
 Action 
Object { type: "worldBuilding/fetchElementsForCategory/fulfilled", payload: {…}, meta: {…} }
bundle.js:33488:19
 Next State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
API response status for http://localhost:8000/books/f61a4fd1-7ca1-419a-8a45-a155d65d0d96/world-building/elements?category=cat_economic_material: 200 bundle.js:33488:19
API response status for http://localhost:8000/books/f61a4fd1-7ca1-419a-8a45-a155d65d0d96/world-building/elements?category=cat_temporal_historical: 200 bundle.js:33488:19
API Service - Fetched world elements: 
Array []
bundle.js:33488:19
Redux - Fetched elements for category: 
Array []
bundle.js:33488:19
 Action: worldBuilding/fetchElementsForCategory/fulfilled bundle.js:211494:13
 Previous State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
 Action 
Object { type: "worldBuilding/fetchElementsForCategory/fulfilled", payload: {…}, meta: {…} }
bundle.js:33488:19
 Next State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
API Service - Fetched world elements: 
Array []
bundle.js:33488:19
Redux - Fetched elements for category: 
Array []
bundle.js:33488:19
 Action: worldBuilding/fetchElementsForCategory/fulfilled bundle.js:211494:13
 Previous State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
 Action 
Object { type: "worldBuilding/fetchElementsForCategory/fulfilled", payload: {…}, meta: {…} }
bundle.js:33488:19
 Next State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
API response status for http://localhost:8000/books/f61a4fd1-7ca1-419a-8a45-a155d65d0d96/world-building/elements?category=cat_interactive_game: 200 bundle.js:33488:19
API Service - Fetched world elements: 
Array []
bundle.js:33488:19
Redux - Fetched elements for category: 
Array []
bundle.js:33488:19
 Action: worldBuilding/fetchElementsForCategory/fulfilled bundle.js:211494:13
 Previous State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
 Action 
Object { type: "worldBuilding/fetchElementsForCategory/fulfilled", payload: {…}, meta: {…} }
bundle.js:33488:19
 Next State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
Redux - Root elements with children: 0 bundle.js:33488:19
Redux - Total elements after fetch: 0 bundle.js:33488:19
 Action: worldBuilding/fetchAllElementsWithChildren/fulfilled bundle.js:211494:13
 Previous State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
 Action 
Object { type: "worldBuilding/fetchAllElementsWithChildren/fulfilled", payload: {…}, meta: {…} }
bundle.js:33488:19
 Next State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
 Action: worldBuilding/setColumnTab bundle.js:211494:13
 Previous State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
 Action 
Object { type: "worldBuilding/setColumnTab", payload: "elements" }
bundle.js:33488:19
[DEBUG] selectCurrentChapter - chapters: 
Array []
bundle.js:33488:19
[DEBUG] selectCurrentChapter - currentChapterId: null bundle.js:33488:19
[DEBUG] selectCurrentChapter - No current chapter ID bundle.js:33488:19
 Next State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
WorldBuildingPage - allRelationships: 
Array []
bundle.js:33488:19
WorldBuildingPage - elementRelationships: 
Array []
bundle.js:33488:19
WorldBuildingPage - allRelationships:  
Array []
bundle.js:33488:19
WorldBuildingPage - elementRelationships:  
Array []
bundle.js:33488:19
Error fetching templates: SyntaxError: JSON.parse: unexpected character at line 1 column 1 of the JSON data <anonymous code>:1:145535
Error fetching popular templates: SyntaxError: JSON.parse: unexpected character at line 1 column 1 of the JSON data <anonymous code>:1:145535
Error fetching templates: SyntaxError: JSON.parse: unexpected character at line 1 column 1 of the JSON data <anonymous code>:1:145535
Error fetching popular templates: SyntaxError: JSON.parse: unexpected character at line 1 column 1 of the JSON data <anonymous code>:1:145535
 Action: worldBuilding/resetCustomizations/pending bundle.js:211494:13
 Previous State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
 Action 
Object { type: "worldBuilding/resetCustomizations/pending", payload: undefined, meta: {…} }
bundle.js:33488:19
[DEBUG] selectCurrentChapter - chapters: 
Array []
bundle.js:33488:19
[DEBUG] selectCurrentChapter - currentChapterId: null bundle.js:33488:19
[DEBUG] selectCurrentChapter - No current chapter ID bundle.js:33488:19
 Next State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
API Request to http://localhost:8000/books/f61a4fd1-7ca1-419a-8a45-a155d65d0d96/world-building/customizations/reset bundle.js:33488:19
Token found in localStorage: eyJhbGciOi... bundle.js:33488:19
Request headers: 
Object { Authorization: "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI2MTBmOGRmYy1kM2M1LTQ4ZjYtYjdjOC02OTNlNmQ3YTY2OTUiLCJleHAiOjE3NDc0Mzg2MjZ9.krop37f_-mPIZzn-X5nu2xslpeliL6pjS6HVcNWPWT4", "Content-Type": "application/json" }
bundle.js:33488:19
WorldBuildingPage - allRelationships: 
Array []
bundle.js:33488:19
WorldBuildingPage - elementRelationships: 
Array []
bundle.js:33488:19
WorldBuildingPage - allRelationships:  
Array []
bundle.js:33488:19
WorldBuildingPage - elementRelationships:  
Array []
bundle.js:33488:19
API response status for http://localhost:8000/books/f61a4fd1-7ca1-419a-8a45-a155d65d0d96/world-building/customizations/reset: 200 bundle.js:33488:19
 Action: worldBuilding/fetchData/pending bundle.js:211494:13
 Previous State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
 Action 
Object { type: "worldBuilding/fetchData/pending", payload: undefined, meta: {…} }
bundle.js:33488:19
 Next State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
Fetching world building data for book: f61a4fd1-7ca1-419a-8a45-a155d65d0d96 bundle.js:33488:19
API Request to http://localhost:8000/books/f61a4fd1-7ca1-419a-8a45-a155d65d0d96/world-building/categories bundle.js:33488:19
Token found in localStorage: eyJhbGciOi... bundle.js:33488:19
Request headers: 
Object { Authorization: "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI2MTBmOGRmYy1kM2M1LTQ4ZjYtYjdjOC02OTNlNmQ3YTY2OTUiLCJleHAiOjE3NDc0Mzg2MjZ9.krop37f_-mPIZzn-X5nu2xslpeliL6pjS6HVcNWPWT4", "Content-Type": "application/json" }
bundle.js:33488:19
API Request to http://localhost:8000/books/f61a4fd1-7ca1-419a-8a45-a155d65d0d96/world-building/customizations bundle.js:33488:19
Token found in localStorage: eyJhbGciOi... bundle.js:33488:19
Request headers: 
Object { Authorization: "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI2MTBmOGRmYy1kM2M1LTQ4ZjYtYjdjOC02OTNlNmQ3YTY2OTUiLCJleHAiOjE3NDc0Mzg2MjZ9.krop37f_-mPIZzn-X5nu2xslpeliL6pjS6HVcNWPWT4", "Content-Type": "application/json" }
bundle.js:33488:19
API Request to http://localhost:8000/books/f61a4fd1-7ca1-419a-8a45-a155d65d0d96/world-building/relationships bundle.js:33488:19
Token found in localStorage: eyJhbGciOi... bundle.js:33488:19
Request headers: 
Object { Authorization: "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI2MTBmOGRmYy1kM2M1LTQ4ZjYtYjdjOC02OTNlNmQ3YTY2OTUiLCJleHAiOjE3NDc0Mzg2MjZ9.krop37f_-mPIZzn-X5nu2xslpeliL6pjS6HVcNWPWT4", "Content-Type": "application/json" }
bundle.js:33488:19
API Request to http://localhost:8000/books/f61a4fd1-7ca1-419a-8a45-a155d65d0d96/world-building/tables/schemas bundle.js:33488:19
Token found in localStorage: eyJhbGciOi... bundle.js:33488:19
Request headers: 
Object { Authorization: "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI2MTBmOGRmYy1kM2M1LTQ4ZjYtYjdjOC02OTNlNmQ3YTY2OTUiLCJleHAiOjE3NDc0Mzg2MjZ9.krop37f_-mPIZzn-X5nu2xslpeliL6pjS6HVcNWPWT4", "Content-Type": "application/json" }
bundle.js:33488:19
 Action: worldBuilding/resetCustomizations/fulfilled bundle.js:211494:13
 Previous State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
 Action 
Object { type: "worldBuilding/resetCustomizations/fulfilled", payload: {…}, meta: {…} }
bundle.js:33488:19
[DEBUG] selectCurrentChapter - chapters: 
Array []
bundle.js:33488:19
[DEBUG] selectCurrentChapter - currentChapterId: null bundle.js:33488:19
[DEBUG] selectCurrentChapter - No current chapter ID bundle.js:33488:19
 Next State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
WorldBuildingPage - allRelationships: 
Array []
bundle.js:33488:19
WorldBuildingPage - elementRelationships: 
Array []
bundle.js:33488:19
WorldBuildingPage - allRelationships:  
Array []
bundle.js:33488:19
WorldBuildingPage - elementRelationships:  
Array []
bundle.js:33488:19
API response status for http://localhost:8000/books/f61a4fd1-7ca1-419a-8a45-a155d65d0d96/world-building/customizations: 200 bundle.js:33488:19
API response status for http://localhost:8000/books/f61a4fd1-7ca1-419a-8a45-a155d65d0d96/world-building/relationships: 200 bundle.js:33488:19
API response status for http://localhost:8000/books/f61a4fd1-7ca1-419a-8a45-a155d65d0d96/world-building/tables/schemas: 200 bundle.js:33488:19
API response status for http://localhost:8000/books/f61a4fd1-7ca1-419a-8a45-a155d65d0d96/world-building/categories: 200 bundle.js:33488:19
Successfully fetched world building data: 
Object { categories: (7) […], customizations: (7) […], relationships: [], tableSchemas: [] }
bundle.js:33488:19
Fetched relationships: 
Array []
bundle.js:33488:19
 Action: worldBuilding/fetchData/fulfilled bundle.js:211494:13
 Previous State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
 Action 
Object { type: "worldBuilding/fetchData/fulfilled", payload: {…}, meta: {…} }
bundle.js:33488:19
Processing relationships in reducer: 
Array []
bundle.js:33488:19
[DEBUG] selectCurrentChapter - chapters: 
Array []
bundle.js:33488:19
[DEBUG] selectCurrentChapter - currentChapterId: null bundle.js:33488:19
[DEBUG] selectCurrentChapter - No current chapter ID bundle.js:33488:19
selectAllElementsForRelationshipView - elementsById: 0 bundle.js:33488:19
selectAllElementsForRelationshipView - allIds: 0 bundle.js:33488:19
selectAllElementsForRelationshipView - No elements found bundle.js:33488:19
selectAllRelationships - relationshipsById: 
Object {  }
bundle.js:33488:19
selectAllRelationships - allIds: 
Array []
bundle.js:33488:19
selectAllRelationships - returning relationships: 
Array []
bundle.js:33488:19
 Next State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
WorldBuildingPage - allRelationships: 
Array []
bundle.js:33488:19
WorldBuildingPage - elementRelationships: 
Array []
bundle.js:33488:19
WorldBuildingPage - allRelationships:  
Array []
bundle.js:33488:19
WorldBuildingPage - elementRelationships:  
Array []
bundle.js:33488:19
Error fetching templates: SyntaxError: JSON.parse: unexpected character at line 1 column 1 of the JSON data <anonymous code>:1:145535
 Action: worldBuilding/setColumnTab bundle.js:211494:13
 Previous State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
 Action 
Object { type: "worldBuilding/setColumnTab", payload: "templates" }
bundle.js:33488:19
[DEBUG] selectCurrentChapter - chapters: 
Array []
bundle.js:33488:19
[DEBUG] selectCurrentChapter - currentChapterId: null bundle.js:33488:19
[DEBUG] selectCurrentChapter - No current chapter ID bundle.js:33488:19
 Next State 
Object { auth: {…}, books: {…}, worldBuilding: {…}, characters: {…}, plot: {…}, brainstorm: {…}, ai: {…}, write: {…}, reference: {…} }
bundle.js:33488:19
WorldBuildingPage - allRelationships: 
Array []
bundle.js:33488:19
WorldBuildingPage - elementRelationships: 
Array []
bundle.js:33488:19
WorldBuildingPage - allRelationships:  
Array []
bundle.js:33488:19
WorldBuildingPage - elementRelationships:  
Array []
bundle.js:33488:19
Token found in localStorage: eyJhbGciOi... bundle.js:33488:19
Using headers: 
Object { Authorization: "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI2MTBmOGRmYy1kM2M1LTQ4ZjYtYjdjOC02OTNlNmQ3YTY2OTUiLCJleHAiOjE3NDc0Mzg2MjZ9.krop37f_-mPIZzn-X5nu2xslpeliL6pjS6HVcNWPWT4", "Content-Type": "application/json" }
bundle.js:33488:19
Token found in localStorage: eyJhbGciOi... bundle.js:33488:19
Using headers: 
Object { Authorization: "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI2MTBmOGRmYy1kM2M1LTQ4ZjYtYjdjOC02OTNlNmQ3YTY2OTUiLCJleHAiOjE3NDc0Mzg2MjZ9.krop37f_-mPIZzn-X5nu2xslpeliL6pjS6HVcNWPWT4", "Content-Type": "application/json" }
bundle.js:33488:19
Raw response: <!DOCTYPE html>
<html lang="en" class="theme-light">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta
      name="description"
      content="Web site created using create-react-app"
    />
    <link rel="apple-touch-icon" href="/logo192.png" />
    <!--
      manifest.json provides metadata used when your web app is installed on a
      user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
    -->
    <link rel="manifest" href="/manifest.json" />
    <!--
      Notice the use of  in the tags above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.

      Unlike "/favicon.ico" or "favicon.ico", "/favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.
    -->
    <title>React App</title>
  <script defer src="/static/js/bundle.js"></script></head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->
  </body>
</html>
bundle.js:33488:19
Error parsing JSON: SyntaxError: JSON.parse: unexpected character at line 1 column 1 of the JSON data
    fetchTemplates TemplateBrowser.js:50
<anonymous code>:1:145535
    overrideMethod <anonymous code>:1
    error index.js:22
    instrumentConsole console.ts:40
    fetchTemplates TemplateBrowser.js:76
Raw response: <!DOCTYPE html>
<html lang="en" class="theme-light">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta
      name="description"
      content="Web site created using create-react-app"
    />
    <link rel="apple-touch-icon" href="/logo192.png" />
    <!--
      manifest.json provides metadata used when your web app is installed on a
      user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
    -->
    <link rel="manifest" href="/manifest.json" />
    <!--
      Notice the use of  in the tags above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.

      Unlike "/favicon.ico" or "favicon.ico", "/favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.
    -->
    <title>React App</title>
  <script defer src="/static/js/bundle.js"></script></head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->
  </body>
</html>
bundle.js:33488:19
Error parsing JSON: SyntaxError: JSON.parse: unexpected character at line 1 column 1 of the JSON data
    fetchTemplates TemplateBrowser.js:50
<anonymous code>:1:145535
Source map error: Error: JSON.parse: unexpected character at line 1 column 1 of the JSON data
Stack in the worker:parseSourceMapInput@resource://devtools/client/shared/vendor/source-map/lib/util.js:163:15
_factory@resource://devtools/client/shared/vendor/source-map/lib/source-map-consumer.js:1066:22
SourceMapConsumer@resource://devtools/client/shared/vendor/source-map/lib/source-map-consumer.js:26:12
_fetch@resource://devtools/client/shared/source-map-loader/utils/fetchSourceMap.js:83:19

Resource URL: http://localhost:3000/%3Canonymous%20code%3E
Source Map URL: installHook.js.map

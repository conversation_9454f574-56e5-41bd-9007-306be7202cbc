#!/usr/bin/env python3
"""
Template <PERSON>rse<PERSON> for World Building Templates

This script parses the IndividualElementTemplates.txt file and generates
SQL INSERT statements for the element_templates table.
"""

import re
import json
import uuid
import os
from typing import Dict, List, Tuple, Optional

# Category mapping - maps template categories to category_ids
CATEGORY_MAPPING = {
    "Cosmology & Physical Environment": {
        "Star": "cat_metaphysical_cosmology",
        "Planet": "cat_metaphysical_cosmology",
        "Moon": "cat_metaphysical_cosmology",
        "Asteroid/Comet": "cat_metaphysical_cosmology",
        "Black Hole/Exotic Object": "cat_metaphysical_cosmology",
        "Nebula": "cat_metaphysical_cosmology",
        "Galaxy": "cat_metaphysical_cosmology",
        "Star System": "cat_metaphysical_cosmology",
        "Continent": "cat_physical_geography",
        "Ocean/Sea": "cat_physical_geography",
        "Mountain Range": "cat_physical_geography",
        "Forest/Jungle": "cat_physical_geography",
        "Desert": "cat_physical_geography",
        "River/Lake": "cat_physical_geography",
        "Plant Species": "cat_physical_flora_fauna",
        "Animal Species": "cat_physical_flora_fauna",
        "Sapient Species": "cat_physical_flora_fauna",
        "Disease": "cat_physical_flora_fauna",
        "Climate Zone/Weather Pattern": "cat_physical_climate",
        "Weather Event": "cat_physical_climate",
        "Tectonic Activity": "cat_physical_geography",
        "Ecological Relationship": "cat_physical_flora_fauna",
        "Species": "cat_physical_flora_fauna",
        "Evolutionary History": "cat_physical_flora_fauna"
    },
    "Cultural & Social Systems": {
        "Nation/State": "cat_social_cultures",
        "Province/Region": "cat_social_cultures",
        "City/Settlement": "cat_physical_locations",
        "Tribe/Clan": "cat_social_cultures",
        "Social Class": "cat_social_cultures",
        "Family Structure": "cat_social_cultures",
        "Government Type": "cat_social_laws",
        "Government Position": "cat_social_laws",
        "Military Organization": "cat_social_organizations",
        "Military Unit": "cat_social_organizations",
        "Language": "cat_social_cultures",
        "Dialect": "cat_social_cultures",
        "Art Form": "cat_social_cultures",
        "Music Style": "cat_social_cultures",
        "Festival/Holiday": "cat_social_cultures",
        "Cuisine": "cat_social_cultures",
        "Clothing Style": "cat_social_cultures",
        "Religion": "cat_metaphysical_religion",
        "Religious Text": "cat_metaphysical_religion",
        "Deity": "cat_metaphysical_religion",
        "Philosophy": "cat_metaphysical_religion",
        "Gender Role/Sexual Norm": "cat_social_cultures",
        "Age Group/Generation": "cat_social_cultures",
        "Social Mobility Mechanism": "cat_social_cultures",
        "Legal Framework/Justice System": "cat_social_laws"
    }
    # Add more categories as needed
}

# Parent-child relationships between templates
TEMPLATE_HIERARCHY = {
    "Star": "Star System",
    "Planet": "Star System",
    "Moon": "Planet",
    "Continent": "Planet",
    "Ocean/Sea": "Planet",
    "Province/Region": "Nation/State",
    "City/Settlement": "Province/Region",
    "Military Unit": "Military Organization",
    "Dialect": "Language",
    "Religious Text": "Religion",
    "Deity": "Religion"
    # Add more relationships as needed
}

def parse_template_file(filepath: str) -> List[Dict]:
    """Parse the template file and extract template definitions."""
    templates = []
    current_template = None
    current_category = None
    current_fields = []

    # Known category headers in the file
    known_categories = [
        "Cosmology & Physical Environment",
        "Cultural & Social Systems",
        "Technological Systems",
        "Metaphysical Systems",
        "Historical & Temporal Systems",
        "Game Design Systems"
    ]

    with open(filepath, 'r') as f:
        lines = f.readlines()

    for line in lines:
        line = line.strip()

        # Skip empty lines
        if not line:
            continue

        # Check for known category headers
        if line in known_categories:
            current_category = line
            print(f"Found category: {current_category}")
            continue

        # Check for template headers
        if "Template" in line and not line.startswith('*'):
            # Save previous template if exists
            if current_template:
                templates.append({
                    'name': current_template,
                    'category': current_category,
                    'fields': current_fields
                })

            # Start new template
            current_template = line.replace(" Template", "")
            current_fields = []
            continue

        # Process field definitions
        if line.startswith('*'):
            field_def = line[1:].strip()
            if ':' in field_def:
                field_name, field_desc = field_def.split(':', 1)
                current_fields.append({
                    'name': field_name.strip(),
                    'description': field_desc.strip()
                })

    # Add the last template
    if current_template:
        templates.append({
            'name': current_template,
            'category': current_category if current_category else "Uncategorized",
            'fields': current_fields
        })

    return templates

def generate_template_id(template_name: str) -> str:
    """Generate a template ID from the template name."""
    return template_name.lower().replace('/', '_').replace(' ', '_').replace('-', '_')

def generate_field_definitions(fields: List[Dict]) -> Dict:
    """Generate field definitions JSON structure."""
    field_defs = {"fields": []}

    for field in fields:
        # Skip id field as it's handled by the system
        if field['name'] == 'id':
            continue

        field_id = field['name'].lower().replace(' ', '_').replace('/', '_')
        field_type = "text"  # Default type

        # Determine field type based on name or description
        if any(type_hint in field['name'].lower() for type_hint in ['date', 'when']):
            field_type = "date"
        elif any(type_hint in field['name'].lower() for type_hint in ['boolean', 'is_']):
            field_type = "boolean"
        elif any(type_hint in field['name'].lower() for type_hint in ['type', 'category']):
            field_type = "select"

        field_def = {
            "id": field_id,
            "label": field['name'],
            "type": field_type,
            "required": False,
            "description": field['description']
        }

        # Add options for select fields
        if field_type == "select":
            # Extract potential options from description
            options_match = re.search(r'\((.*?)\)', field['description'])
            if options_match:
                options = [opt.strip() for opt in options_match.group(1).split(',')]
                field_def["options"] = options

        field_defs["fields"].append(field_def)

    return field_defs

def generate_relationship_definitions(template_name: str) -> Dict:
    """Generate relationship definitions JSON structure."""
    relationships = {"valid_relationships": []}

    # Add parent relationship if applicable
    if template_name in TEMPLATE_HIERARCHY:
        parent = TEMPLATE_HIERARCHY[template_name]
        parent_id = generate_template_id(parent)
        relationships["valid_relationships"].append({
            "target_template": parent_id,
            "relationship_type": "belongs_to",
            "inverse_relationship": "contains",
            "label": f"Belongs To {parent}",
            "description": f"{parent} this {template_name} belongs to"
        })

    # Add child relationships
    for child, parent in TEMPLATE_HIERARCHY.items():
        if parent == template_name:
            child_id = generate_template_id(child)
            relationships["valid_relationships"].append({
                "target_template": child_id,
                "relationship_type": "contains",
                "inverse_relationship": "belongs_to",
                "label": f"Contains {child}",
                "description": f"{child} contained in this {template_name}"
            })

    return relationships

def generate_sql_inserts(templates: List[Dict]) -> List[str]:
    """Generate SQL INSERT statements for the templates."""
    sql_statements = []

    for template in templates:
        template_name = template['name']
        template_id = generate_template_id(template_name)

        # Determine category_id
        category = template['category']
        category_id = None
        for cat_group, mappings in CATEGORY_MAPPING.items():
            if template_name in mappings:
                category_id = mappings[template_name]
                break

        # Use default category if not found
        if not category_id:
            if category and "Physical" in category:
                category_id = "cat_physical_locations"
            elif category and ("Cultural" in category or "Social" in category):
                category_id = "cat_social_cultures"
            elif category and "Metaphysical" in category:
                category_id = "cat_metaphysical_religion"
            else:
                category_id = "cat_physical_locations"

        # Determine parent template
        parent_template_id = None
        if template_name in TEMPLATE_HIERARCHY:
            parent = TEMPLATE_HIERARCHY[template_name]
            parent_template_id = generate_template_id(parent)

        # Generate field definitions
        field_defs = generate_field_definitions(template['fields'])

        # Generate relationship definitions
        relationship_defs = generate_relationship_definitions(template_name)

        # Escape single quotes in JSON strings
        field_defs_json = json.dumps(field_defs).replace("'", "''")
        relationship_defs_json = json.dumps(relationship_defs).replace("'", "''")

        # Create SQL statement
        sql = f"""
INSERT INTO element_templates (
    template_id,
    template_name,
    display_name,
    category_id,
    parent_template_id,
    description,
    field_definitions,
    relationship_definitions
) VALUES (
    '{template_id}',
    '{template_name}Template',
    '{template_name}',
    '{category_id}',
    {f"'{parent_template_id}'" if parent_template_id else 'NULL'},
    'Template for {template_name} elements',
    '{field_defs_json}',
    '{relationship_defs_json}'
);
"""
        sql_statements.append(sql)

    return sql_statements

def main():
    """Main function to parse templates and generate SQL."""
    filepath = "wip-upgrades/IndividualElementTemplates.txt"

    # Parse templates
    templates = parse_template_file(filepath)
    print(f"Found {len(templates)} templates")

    # Check for templates without categories
    templates_without_category = [t for t in templates if not t['category'] or t['category'] == "Uncategorized"]
    if templates_without_category:
        print(f"WARNING: Found {len(templates_without_category)} templates without a category:")
        for t in templates_without_category:
            print(f"  - {t['name']}")

        # Assign default categories based on template name
        for t in templates_without_category:
            name = t['name'].lower()
            if any(term in name for term in ['star', 'planet', 'moon', 'galaxy', 'nebula', 'asteroid']):
                t['category'] = "Cosmology & Physical Environment"
            elif any(term in name for term in ['continent', 'mountain', 'forest', 'river', 'ocean', 'desert']):
                t['category'] = "Cosmology & Physical Environment"
            elif any(term in name for term in ['species', 'animal', 'plant', 'creature']):
                t['category'] = "Cosmology & Physical Environment"
            elif any(term in name for term in ['nation', 'city', 'settlement', 'government', 'law']):
                t['category'] = "Cultural & Social Systems"
            elif any(term in name for term in ['religion', 'deity', 'magic', 'supernatural']):
                t['category'] = "Metaphysical Systems"
            elif any(term in name for term in ['technology', 'weapon', 'vehicle', 'tool']):
                t['category'] = "Technological Systems"
            else:
                t['category'] = "Miscellaneous"

        print("Assigned default categories to templates without categories")

    # Sort templates to ensure parent templates are inserted first
    sorted_templates = []
    remaining_templates = templates.copy()

    # First, add templates without parents
    no_parent_templates = [t for t in remaining_templates if t['name'] not in TEMPLATE_HIERARCHY.values()]
    sorted_templates.extend(no_parent_templates)
    for t in no_parent_templates:
        if t in remaining_templates:
            remaining_templates.remove(t)

    # Then add templates in hierarchy order
    while remaining_templates:
        added_this_round = False
        for t in remaining_templates[:]:  # Create a copy to safely remove during iteration
            parent_name = TEMPLATE_HIERARCHY.get(t['name'])
            if not parent_name or any(st['name'] == parent_name for st in sorted_templates):
                sorted_templates.append(t)
                remaining_templates.remove(t)
                added_this_round = True

        # If we couldn't add any templates this round, there might be a circular dependency
        if not added_this_round:
            print("WARNING: Possible circular dependency in template hierarchy")
            sorted_templates.extend(remaining_templates)
            break

    # Generate SQL statements
    sql_statements = generate_sql_inserts(sorted_templates)

    # Write SQL to file
    with open("backend/app/db/migrations/element_templates_inserts.sql", "w") as f:
        f.write("-- Auto-generated SQL inserts for element_templates\n\n")
        for sql in sql_statements:
            f.write(sql + "\n")

    # Print category distribution
    category_counts = {}
    for t in templates:
        category = t['category']
        if category not in category_counts:
            category_counts[category] = 0
        category_counts[category] += 1

    print("\nCategory distribution:")
    for category, count in sorted(category_counts.items(), key=lambda x: x[1], reverse=True):
        print(f"  - {category}: {count} templates")

    print(f"\nGenerated SQL inserts for {len(sql_statements)} templates")
    print("SQL file written to backend/app/db/migrations/element_templates_inserts.sql")

if __name__ == "__main__":
    main()

"""
Service for validating templates.
"""
import logging
import re
from typing import Dict, List, Any, <PERSON><PERSON>, Optional

logger = logging.getLogger(__name__)

class TemplateValidationService:
    """Service for validating templates."""

    def __init__(self):
        """Initialize the template validation service."""
        # Define validation rules
        self.required_fields = [
            "template_name",
            "display_name",
            "category_id",
            "field_definitions"
        ]

        self.field_types = [
            "text", "textarea", "number", "date", "select",
            "multiselect", "checkbox", "radio", "color", "image"
        ]

        self.field_definition_required = [
            "field_type", "label"
        ]

        # Regex patterns for validation
        self.template_name_pattern = re.compile(r'^[a-z0-9_]+$')

    def validate_template(self, template_data: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """
        Validates a template against the defined rules.

        Args:
            template_data: The template data to validate

        Returns:
            A tuple containing (is_valid, error_messages)
        """
        errors = []

        # Check required fields
        for field in self.required_fields:
            if field not in template_data:
                errors.append(f"Missing required field: {field}")

        # If missing required fields, return early
        if errors:
            return False, errors

        # Validate template_name format (lowercase, underscores, alphanumeric)
        if not self.template_name_pattern.match(template_data["template_name"]):
            errors.append("template_name must contain only lowercase letters, numbers, and underscores")

        # Validate field_definitions
        field_errors = self._validate_field_definitions(template_data["field_definitions"])
        errors.extend(field_errors)

        # Validate relationship_definitions if present
        if "relationship_definitions" in template_data and template_data["relationship_definitions"]:
            relationship_errors = self._validate_relationship_definitions(template_data["relationship_definitions"])
            errors.extend(relationship_errors)

        return len(errors) == 0, errors

    def _validate_field_definitions(self, field_definitions: Dict[str, List[Dict[str, Any]]]) -> List[str]:
        """
        Validates the field definitions of a template.

        Args:
            field_definitions: The field definitions to validate

        Returns:
            A list of error messages
        """
        errors = []

        if not isinstance(field_definitions, dict):
            errors.append("field_definitions must be an object")
            return errors

        if not field_definitions:
            errors.append("field_definitions cannot be empty")
            return errors

        # Check each section
        for section_name, fields in field_definitions.items():
            if not isinstance(fields, list):
                errors.append(f"Section '{section_name}' must contain an array of fields")
                continue

            # Check each field in the section
            for i, field in enumerate(fields):
                field_errors = self._validate_field(field, section_name, i)
                errors.extend(field_errors)

        return errors

    def _validate_field(self, field: Dict[str, Any], section_name: str, index: int) -> List[str]:
        """
        Validates a single field definition.

        Args:
            field: The field to validate
            section_name: The name of the section containing the field
            index: The index of the field in the section

        Returns:
            A list of error messages
        """
        errors = []

        # Check required field properties
        for prop in self.field_definition_required:
            # Handle both field_type and type properties
            if prop == "field_type":
                if "field_type" not in field and "type" not in field:
                    errors.append(f"Field at index {index} in section '{section_name}' is missing required property: field_type or type")
            elif prop not in field:
                errors.append(f"Field at index {index} in section '{section_name}' is missing required property: {prop}")

        # If missing required properties, return early
        if errors:
            return errors

        # Get the field type (handle both field_type and type properties)
        field_type = field.get("field_type") or field.get("type")

        # Validate field_type
        if field_type not in self.field_types:
            errors.append(f"Field at index {index} in section '{section_name}' has invalid field type: {field_type}")

        # Validate options for select, multiselect, radio fields
        if field_type in ["select", "multiselect", "radio"]:
            if "options" not in field or not isinstance(field["options"], list) or not field["options"]:
                errors.append(f"Field at index {index} in section '{section_name}' of type {field_type} must have non-empty options array")

        return errors

    def _validate_relationship_definitions(self, relationship_definitions: Dict[str, List[Dict[str, Any]]]) -> List[str]:
        """
        Validates the relationship definitions of a template.

        Args:
            relationship_definitions: The relationship definitions to validate

        Returns:
            A list of error messages
        """
        errors = []

        if not isinstance(relationship_definitions, dict):
            errors.append("relationship_definitions must be an object")
            return errors

        # Check valid_relationships if present
        if "valid_relationships" in relationship_definitions:
            valid_relationships = relationship_definitions["valid_relationships"]

            if not isinstance(valid_relationships, list):
                errors.append("valid_relationships must be an array")
                return errors

            # Check each relationship
            for i, relationship in enumerate(valid_relationships):
                if not isinstance(relationship, dict):
                    errors.append(f"Relationship at index {i} must be an object")
                    continue

                # Check required relationship properties
                if "target_template" not in relationship:
                    errors.append(f"Relationship at index {i} is missing required property: target_template")

                if "relationship_type" not in relationship:
                    errors.append(f"Relationship at index {i} is missing required property: relationship_type")

                if "inverse_relationship" not in relationship:
                    errors.append(f"Relationship at index {i} is missing required property: inverse_relationship")

        return errors

    def generate_sample_data(self, template_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generates sample data for a template.

        Args:
            template_data: The template to generate sample data for

        Returns:
            A dictionary containing sample data for the template
        """
        sample_data = {
            "name": f"Sample {template_data['display_name']}",
            "template_id": template_data["template_id"] if "template_id" in template_data else template_data["template_name"],
            "data": {}
        }

        # Generate sample data for each field
        for section_name, fields in template_data["field_definitions"].items():
            sample_data["data"][section_name] = {}

            for field in fields:
                # Get the field type (handle both field_type and type properties)
                field_type = field.get("field_type") or field.get("type")
                if not field_type:
                    continue  # Skip fields without a type

                field_name = field.get("field_name", "").strip() or field["label"].lower().replace(" ", "_")

                # Generate sample value based on field type
                sample_value = self._generate_sample_value(field_type, field)
                sample_data["data"][section_name][field_name] = sample_value

        return sample_data

    def _generate_sample_value(self, field_type: str, field: Dict[str, Any]) -> Any:
        """
        Generates a sample value for a field based on its type.

        Args:
            field_type: The type of the field
            field: The field definition

        Returns:
            A sample value for the field
        """
        if field_type == "text":
            return f"Sample {field['label']}"

        elif field_type == "textarea":
            return f"This is a sample description for {field['label']}."

        elif field_type == "number":
            return 42

        elif field_type == "date":
            return "2023-01-01"

        elif field_type in ["select", "radio"]:
            if "options" in field and field["options"]:
                return field["options"][0]["value"]
            return ""

        elif field_type == "multiselect":
            if "options" in field and field["options"]:
                return [field["options"][0]["value"]]
            return []

        elif field_type == "checkbox":
            return True

        elif field_type == "color":
            return "#3498db"

        elif field_type == "image":
            return "https://example.com/sample-image.jpg"

        return "Sample value"

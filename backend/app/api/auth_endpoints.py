# backend/app/api/auth_endpoints.py
from fastapi import APIRouter, HTTPException, status
from pydantic import BaseModel
from app.auth import get_password_hash, create_access_token, verify_password
from app.db.user_repository import UserRepository

router = APIRouter(prefix="/auth", tags=["auth"])
user_repo = UserRepository()

class UserCreate(BaseModel):
    """Schema for user registration data."""
    user_id: str = ""
    email: str
    password: str

@router.post("/register")
def register_user(user: UserCreate):
    """Registers a new user with hashed password."""
    # Check if email already exists
    existing_user = user_repo.get_by_email(user.email)
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email already registered"
        )

    # Generate a user_id if not provided
    import uuid
    user_id = user.user_id if user.user_id else str(uuid.uuid4())

    # Hash the password and create the user
    password_hash = get_password_hash(user.password)
    user_repo.create(user_id, user.email, password_hash)

    # Return user info (without password)
    return {"user_id": user_id, "email": user.email}

@router.post("/token")
def login(user: UserCreate):
    """Authenticates a user and returns a JWT token."""
    db_user = user_repo.get_by_email(user.email)
    if not db_user or not verify_password(user.password, db_user["password_hash"]):
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid credentials")
    token = create_access_token({"sub": db_user["user_id"]})
    return {"access_token": token, "token_type": "bearer"}
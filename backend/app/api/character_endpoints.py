# backend/app/api/character_endpoints.py
from fastapi import APIRouter, Depends, HTTPException, Request
import json
import logging
import httpx
import traceback
import os
import uuid
import re
from app.auth import get_current_user
from app.db.character_repository import CharacterRepository
from app.db.base import get_db_connection
from app.ai.provider import get_text_provider, get_image_provider, get_user_image_style, get_user_image_model, get_user_image_quality
from app.config import XAI_API_KEY, XAI_IMG_API_KEY, API_BASE_URL

# Directory to save character headshots
IMAGE_DIR = "static/images"
os.makedirs(IMAGE_DIR, exist_ok=True)

# Helper function to get user-specific image directory
def get_user_image_dir(user_id):
    """Get the user-specific image directory path"""
    if not user_id:
        return IMAGE_DIR

    user_dir = os.path.join(IMAGE_DIR, f"user_{user_id}")
    os.makedirs(user_dir, exist_ok=True)
    return user_dir

# Helper function to construct a detailed headshot prompt
def construct_headshot_prompt(character_name, character_data, style=None):
    """Construct a detailed prompt for character headshot generation with optional style"""
    # Start with style prefix based on the provided style
    if style == "realistic":
        prompt = "Generate a photorealistic style headshot portrait with detailed facial features, professional photography lighting, clean, singular person of "
    elif style == "anime":
        prompt = "Generate an anime style headshot portrait with vibrant colors, expressive features, anime art style of "
    elif style == "fantasy":
        prompt = "Generate a fantasy art style headshot portrait with dramatic lighting, detailed features, fantasy elements of "
    elif style == "oil_painting":
        prompt = "Generate an oil painting style headshot portrait with rich colors, textured brush strokes, classical painting technique of "
    elif style == "watercolor":
        prompt = "Generate a watercolor style headshot portrait with soft edges, translucent colors, watercolor painting technique of "
    else:
        # Default style
        prompt = "Generate a high quality headshot portrait with detailed facial features, professional lighting, neutral background of "

    # Add character name
    prompt += f"{character_name}"

    # Add description if available
    description = character_data.get("description", "")
    if description:
        prompt += f", who is {description}"

    # Add race if available
    race = character_data.get("race", "")
    if race:
        prompt += f". Their race is {race}"

    # Add age if available
    age = character_data.get("age", "")
    if age:
        prompt += f". They are {age} years old"

    # Add gender identity if available
    gender_identity = character_data.get("gender_identity", "")
    if gender_identity:
        prompt += f". Their gender identity is {gender_identity}"

    # Add style reinforcement at the end
    if style == "realistic":
        prompt += ". Ensure photorealistic style with professional photography lighting."
    elif style == "anime":
        prompt += ". Ensure anime style with vibrant colors and expressive features."
    elif style == "fantasy":
        prompt += ". Ensure fantasy art style with dramatic lighting."
    elif style == "oil_painting":
        prompt += ". Ensure oil painting style with visible brush strokes and rich colors."
    elif style == "watercolor":
        prompt += ". Ensure watercolor style with soft edges and translucent colors."
    else:
        # Default style reinforcement
        prompt += ". Ensure high quality with professional lighting and neutral background."

    return prompt

logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

router = APIRouter(prefix="/books/{book_id}/characters", tags=["characters"])
char_repo = CharacterRepository()

@router.post("")
async def add_character(
    book_id: str,
    character_data: dict,
    user_id: str = Depends(get_current_user)
):
    """Add or update a character for a book"""
    try:
        # Check if this is a bulk update
        if 'update_all' in character_data:
            # Handle bulk update
            char_repo = CharacterRepository()
            result = char_repo.update_all_characters(book_id, character_data['update_all'])

            # Handle memory updates if provided
            if 'memory' in character_data:
                try:
                    memory_data = json.loads(character_data['memory'])
                    if 'deletedCharacters' in memory_data:
                        # Update deleted characters tracking
                        char_repo.update_deleted_characters(book_id, memory_data['deletedCharacters'])
                except Exception as e:
                    logger.error(f"Error processing memory data: {str(e)}")

            return {"message": "Characters updated successfully", "count": len(character_data['update_all'])}

        # Handle single character update
        char_repo = CharacterRepository()

        # Ensure character has required fields
        if 'name' not in character_data:
            raise HTTPException(status_code=400, detail="Character name is required")

        # Ensure relationships is a list
        if 'relationships' in character_data and not isinstance(character_data['relationships'], list):
            try:
                character_data['relationships'] = json.loads(character_data['relationships'])
            except:
                character_data['relationships'] = []

        # Ensure traits is a list
        if 'traits' in character_data and not isinstance(character_data['traits'], list):
            try:
                if isinstance(character_data['traits'], str) and character_data['traits']:
                    character_data['traits'] = character_data['traits'].split(',')
                else:
                    character_data['traits'] = []
            except:
                character_data['traits'] = []

        # Add or update the character
        character_id = char_repo.add_character(book_id, character_data['name'], character_data)
        return {
            "message": "Character added successfully",
            "name": character_data['name'],
            "character_id": character_id,
            "id": character_id  # Include both id and character_id for compatibility
        }

    except Exception as e:
        logger.error(f"Error adding character: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error adding character: {str(e)}")

@router.get("")
def get_characters(book_id: str, user_id: str = Depends(get_current_user)):
    """Fetches all characters for a given book."""
    characters = char_repo.get_characters(book_id)
    return {"book_id": book_id, "characters": characters}

@router.get("/deleted")
def get_deleted_characters(book_id: str, user_id: str = Depends(get_current_user)):
    """Fetches all deleted character names for a given book."""
    conn = get_db_connection()
    try:
        with conn.cursor() as cursor:
            cursor.execute(
                "SELECT name FROM deleted_characters WHERE book_id = %s",
                (book_id,)
            )
            deleted_names = [row['name'] for row in cursor.fetchall()]
        return {"book_id": book_id, "deleted_characters": deleted_names}
    except Exception as e:
        logger.error(f"Error fetching deleted characters: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error fetching deleted characters: {str(e)}")
    finally:
        conn.close()

@router.put("/{character_id}")
async def update_character(
    book_id: str,
    character_id: str,
    character_data: dict,
    user_id: str = Depends(get_current_user)
):
    """Update a specific character by ID"""
    try:
        logger.info(f"Updating character {character_id} in book {book_id}")

        # Ensure character has required fields
        if 'name' not in character_data:
            raise HTTPException(status_code=400, detail="Character name is required")

        # Ensure relationships is a list
        if 'relationships' in character_data and not isinstance(character_data['relationships'], list):
            try:
                character_data['relationships'] = json.loads(character_data['relationships'])
            except:
                character_data['relationships'] = []

        # Ensure traits is a list
        if 'traits' in character_data and not isinstance(character_data['traits'], list):
            try:
                if isinstance(character_data['traits'], str) and character_data['traits']:
                    character_data['traits'] = character_data['traits'].split(',')
                else:
                    character_data['traits'] = []
            except:
                character_data['traits'] = []

        # Check if character exists
        conn = get_db_connection()
        try:
            with conn.cursor() as cursor:
                cursor.execute(
                    "SELECT * FROM characters WHERE book_id = %s AND character_id = %s",
                    (book_id, character_id)
                )
                existing_character = cursor.fetchone()

                if not existing_character:
                    raise HTTPException(status_code=404, detail=f"Character with ID {character_id} not found")

                # Log the character data for debugging
                logger.info(f"Updating character with data: {character_data}")

                # Log the character data for debugging
                logger.info(f"Character data keys: {character_data.keys()}")

                # Update the character with explicit handling of each field
                try:
                    cursor.execute(
                        """UPDATE characters
                        SET name = %s, description = %s, age = %s, role = %s, race = %s, headshot = %s,
                        backstory = %s, arc = %s, gender_identity = %s, sexual_orientation = %s
                        WHERE book_id = %s AND character_id = %s""",
                        (
                            character_data['name'],
                            character_data.get('description', ''),
                            character_data.get('age', ''),
                            character_data.get('role', ''),
                            character_data.get('race', ''),
                            character_data.get('headshot', ''),
                            character_data.get('backstory', ''),
                            character_data.get('arc', ''),
                            character_data.get('gender_identity', ''),
                            character_data.get('sexual_orientation', ''),
                            book_id,
                            character_id
                        )
                    )
                    logger.info(f"Character update SQL executed successfully")
                except Exception as e:
                    logger.error(f"Error executing character update SQL: {str(e)}")
                    raise

                # Delete existing traits
                cursor.execute(
                    "DELETE FROM character_traits WHERE character_id = %s",
                    (character_id,)
                )

                # Add new traits
                if 'formatted_traits' in character_data and character_data['formatted_traits']:
                    # Use formatted traits with trait_ids
                    for trait_obj in character_data['formatted_traits']:
                        cursor.execute(
                            "INSERT INTO character_traits (trait_id, character_id, trait) VALUES (%s, %s, %s)",
                            (trait_obj['trait_id'], character_id, trait_obj['trait'])
                        )
                elif 'traits' in character_data and character_data['traits']:
                    # For backward compatibility, generate trait_ids if not provided
                    for i, trait in enumerate(character_data['traits']):
                        trait_id = f"trait_{uuid.uuid4().hex}_{i}"
                        cursor.execute(
                            "INSERT INTO character_traits (trait_id, character_id, trait) VALUES (%s, %s, %s)",
                            (trait_id, character_id, trait)
                        )

                # Only delete and update relationships if they're included in the request
                if 'relationships' in character_data:
                    # Delete existing relationships
                    cursor.execute(
                        "DELETE FROM character_relationships WHERE character_id = %s",
                        (character_id,)
                    )

                    # Add new relationships if provided
                    if character_data['relationships'] and isinstance(character_data['relationships'], list):
                        for rel in character_data['relationships']:
                            if isinstance(rel, dict):
                                # Handle both formats: new format with relatedCharacterId or old format with name/type
                                related_character_id = None
                                relationship_type = None

                                # Get the related character ID
                                if 'relatedCharacterId' in rel:
                                    related_character_id = rel['relatedCharacterId']
                                elif 'name' in rel:
                                    # Try to find the character ID by name
                                    cursor.execute(
                                        "SELECT character_id FROM characters WHERE book_id = %s AND name = %s",
                                        (book_id, rel['name'])
                                    )
                                    result = cursor.fetchone()
                                    if result:
                                        related_character_id = result['character_id']

                                # Get the relationship type
                                if 'relationshipType' in rel:
                                    relationship_type = rel['relationshipType']
                                elif 'type' in rel:
                                    relationship_type = rel['type']

                                # Skip if we don't have both required fields
                                if not related_character_id or not relationship_type:
                                    logger.warning(f"Skipping relationship due to missing required fields: {rel}")
                                    continue

                                relationship_id = f"rel_{character_id}_{related_character_id}_{uuid.uuid4().hex[:8]}"

                                # Get description from various possible fields
                                description = rel.get('description', '')

                                # Check all possible keys that might contain the relationship description
                                if not description:
                                    possible_keys = [
                                        "Description of relationship with this character",
                                        "description of relationship with this character",
                                        "Description",
                                        "Relationship description",
                                        "relationship description"
                                    ]

                                    for key in possible_keys:
                                        if rel.get(key):
                                            description = rel.get(key)
                                            break

                                logger.info(f"Adding relationship: {character_id} -> {related_character_id} ({relationship_type})")

                                # Get strength from the relationship object, default to 3 if not specified
                                strength = rel.get('strength', 3)

                                # Ensure strength is an integer between 0 and 5
                                try:
                                    strength = int(strength)
                                    strength = max(0, min(5, strength))
                                except (ValueError, TypeError):
                                    strength = 3  # Default to 3 if invalid

                                cursor.execute(
                                    """INSERT INTO character_relationships
                                    (relationship_id, character_id, related_character_id, relationship_type, description, strength)
                                    VALUES (%s, %s, %s, %s, %s, %s)""",
                                    (
                                        relationship_id,
                                        character_id,
                                        related_character_id,
                                        relationship_type,
                                        description,
                                        strength
                                    )
                                )

                conn.commit()

                # Log success before returning
                logger.info(f"Character {character_id} updated successfully")

                # Return the updated character
                response_data = {
                    "id": character_id,
                    "name": character_data['name'],
                    "description": character_data.get('description', ''),
                    "age": character_data.get('age', ''),
                    "role": character_data.get('role', ''),
                    "race": character_data.get('race', ''),
                    "headshot": character_data.get('headshot', ''),
                    "traits": character_data.get('traits', []),
                    "relationships": character_data.get('relationships', []),
                    "backstory": character_data.get('backstory', ''),
                    "arc": character_data.get('arc', ''),
                    "gender_identity": character_data.get('gender_identity', ''),
                    "sexual_orientation": character_data.get('sexual_orientation', ''),
                    "message": "Character updated successfully"
                }

                logger.info(f"Returning response: {response_data}")
                return response_data
        finally:
            conn.close()
    except HTTPException as e:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error updating character: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"Error updating character: {str(e)}")

@router.delete("/{character_id}")
async def delete_character(
    book_id: str,
    character_id: str,
    user_id: str = Depends(get_current_user)
):
    """Delete a specific character by ID"""
    try:
        logger.info(f"Deleting character {character_id} from book {book_id}")

        # Check if character exists
        conn = get_db_connection()
        try:
            with conn.cursor() as cursor:
                # Check if the character exists
                cursor.execute(
                    "SELECT * FROM characters WHERE book_id = %s AND character_id = %s",
                    (book_id, character_id)
                )
                existing_character = cursor.fetchone()

                if not existing_character:
                    raise HTTPException(status_code=404, detail=f"Character with ID {character_id} not found")

                # Get the character name and headshot for tracking deleted characters
                character_name = existing_character['name']
                character_headshot = existing_character.get('headshot', '')

                # Delete the character's headshot file if it exists
                if character_headshot and not character_headshot.startswith('http'):
                    try:
                        # Remove the leading slash if present
                        if character_headshot.startswith('/'):
                            character_headshot = character_headshot[1:]

                        # Construct the full path to the headshot file
                        headshot_path = os.path.join(os.getcwd(), character_headshot)

                        # Check if the file exists before attempting to delete
                        if os.path.exists(headshot_path):
                            os.remove(headshot_path)
                            logger.info(f"Deleted headshot file: {headshot_path}")
                        else:
                            logger.warning(f"Headshot file not found: {headshot_path}")
                    except Exception as e:
                        logger.error(f"Error deleting headshot file: {str(e)}")

                # Delete character relationships
                cursor.execute(
                    "DELETE FROM character_relationships WHERE character_id = %s",
                    (character_id,)
                )

                # Delete character traits
                cursor.execute(
                    "DELETE FROM character_traits WHERE character_id = %s",
                    (character_id,)
                )

                # Delete the character
                cursor.execute(
                    "DELETE FROM characters WHERE book_id = %s AND character_id = %s",
                    (book_id, character_id)
                )

                # Try to add to deleted_characters table for tracking
                try:
                    cursor.execute(
                        "INSERT INTO deleted_characters (book_id, name) VALUES (%s, %s) ON CONFLICT DO NOTHING",
                        (book_id, character_name)
                    )
                except Exception as e:
                    # If the table doesn't exist, just log the error and continue
                    logger.warning(f"Could not add to deleted_characters table: {str(e)}")

                conn.commit()

                return {"message": f"Character {character_name} deleted successfully", "id": character_id}
        finally:
            conn.close()
    except HTTPException as e:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error deleting character: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"Error deleting character: {str(e)}")

# Helper function to call AI service for text generation
async def call_ai_service(prompt, context="", system_message=None, user_id=None):
    """Call the AI service with the given prompt"""
    try:
        # Get the text provider for the user
        text_provider = await get_text_provider(user_id)

        # Use the provided system message or the default one
        if system_message is None:
            system_message = f"Context: {context}\n" + \
                            "You are a creative writing assistant that helps create detailed characters for stories.\n" + \
                            "Respond with a single JSON object containing the character details as specified in the user's prompt."

        # Create the full prompt with system message
        full_prompt = f"{system_message}\n\n{prompt}"

        # Call the text provider
        response = await text_provider.generate_text(full_prompt)
        return response
    except Exception as e:
        logger.error(f"Error calling AI service: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error calling AI service: {str(e)}")

# Helper function to generate character headshot using AI
async def generate_character_headshot(character_name, character_description, user_id=None, style=None):
    """Generate a character headshot using AI image provider"""
    try:
        # Get the user-specific image directory
        user_image_dir = get_user_image_dir(user_id)

        # Create a safe filename
        safe_name = character_name.replace(" ", "_")
        image_filename = f"{safe_name}_{uuid.uuid4().hex}.jpg"
        image_path = os.path.join(user_image_dir, image_filename)

        # If no style is specified, use the user's preferred style
        if style is None:
            style = get_user_image_style(user_id)

        # Get the user's preferred model and quality
        model = get_user_image_model(user_id)
        quality = get_user_image_quality(user_id)

        # Create the prompt for the image generation using the helper function
        prompt = construct_headshot_prompt(character_name, {"description": character_description}, style)

        # Log the preferences being used
        logger.debug(f"Generating headshot with style: {style}, model: {model}, quality: {quality}")

        # Log a truncated version of the prompt for debugging
        truncated_prompt = prompt[:200] + "..." if len(prompt) > 200 else prompt
        logger.debug(f"Headshot prompt (truncated): {truncated_prompt}")

        # Get the image provider for the user
        image_provider = await get_image_provider(user_id)

        try:
            # Call the image provider
            image_url = await image_provider.generate_image(prompt, image_path)

            # If the provider already saved the image to the path (which happens with base64 data),
            # just return the path
            if image_url and image_url.startswith('/'):
                return image_url

            # If we got a URL, download the image
            elif image_url and image_url.startswith('http'):
                try:
                    # Download the image
                    async with httpx.AsyncClient(timeout=60.0) as client:
                        image_response = await client.get(image_url)
                        image_response.raise_for_status()

                        with open(image_path, "wb") as f:
                            f.write(image_response.content)

                        # Return the URL to access the saved image
                        # Extract the relative path from the full path
                        relative_path = os.path.relpath(image_path, start="static")
                        return f"/static/{relative_path}"
                except Exception as download_error:
                    logger.error(f"Error downloading image from URL: {str(download_error)}")
                    raise
            # If we got a data URL, decode and save it
            elif image_url and image_url.startswith('data:image'):
                try:
                    # Extract the base64 data
                    import base64
                    # Format is data:image/png;base64,<data>
                    base64_data = image_url.split(',')[1]
                    image_bytes = base64.b64decode(base64_data)

                    # Save the image
                    with open(image_path, "wb") as f:
                        f.write(image_bytes)

                    # Return the URL to access the saved image
                    relative_path = os.path.relpath(image_path, start="static")
                    return f"/static/{relative_path}"
                except Exception as decode_error:
                    logger.error(f"Error decoding data URL: {str(decode_error)}")
                    raise
            else:
                # No valid image URL returned
                logger.error(f"No valid image URL returned: {image_url}")
                raise Exception("Failed to generate image: No valid image URL returned")
        except Exception as e:
            # Check for content moderation rejection or prompt length error
            error_text = str(e).lower()
            reason = "unknown-error"

            # Handle content moderation rejection
            if "content moderation" in error_text:
                logger.warning(f"Image rejected by content moderation for character: {character_name}")
                reason = "moderation-rejected"
            # Handle prompt length error
            elif "prompt len is larger than the maximum allowed length" in error_text or "too long" in error_text:
                logger.warning(f"Prompt too long for character: {character_name}. Prompt length: {len(prompt)} characters")
                reason = "prompt-too-long"
            else:
                logger.error(f"Error generating image: {str(e)}")

            # Create a simple placeholder image locally instead of downloading
            initial = character_name[0].upper() if character_name else "?"

            try:
                # Try to use PIL to create a simple placeholder image
                from PIL import Image, ImageDraw, ImageFont

                # Create a new image with a gray background
                img = Image.new('RGB', (400, 400), color=(108, 117, 125))
                draw = ImageDraw.Draw(img)

                # Try to use a system font, fall back to default if not available
                try:
                    font = ImageFont.truetype("Arial", 150)
                except IOError:
                    font = ImageFont.load_default()

                # Calculate text position to center it
                text_width, text_height = draw.textbbox((0, 0), initial, font=font)[2:4]
                position = ((400 - text_width) // 2, (400 - text_height) // 2)

                # Draw the initial in white
                draw.text(position, initial, font=font, fill=(255, 255, 255))

                # Save the image
                img.save(image_path, 'JPEG')

                logger.info(f"Created local placeholder image at {image_path}")
            except Exception as img_error:
                # If PIL fails, create an extremely simple placeholder file
                logger.warning(f"Failed to create PIL image: {str(img_error)}. Creating simple file instead.")
                with open(image_path, "w") as f:
                    f.write(f"Placeholder for {character_name}")
                logger.info(f"Created simple text placeholder at {image_path}")

            logger.info(f"Created placeholder image for {reason}: {image_path}")
            # Extract the relative path from the full path
            relative_path = os.path.relpath(image_path, start="static")
            return f"/static/{relative_path}"
    except Exception as e:
        logger.error(f"Error generating character headshot: {str(e)}")
        return None

# Helper function to extract JSON from text
def extract_json_from_text(text):
    """Extract JSON object from text"""
    logger.info("=== EXTRACT JSON DEBUG ===")
    logger.info(f"Attempting to extract JSON from text of length: {len(text)}")

    try:
        # First, try to parse the entire text as JSON (for clean responses)
        try:
            logger.info("Trying to parse entire text as JSON")
            result = json.loads(text)
            logger.info("Successfully parsed entire text as JSON")
            logger.info(f"Extracted JSON: {json.dumps(result)[:1000]}...")
            logger.info("=== END EXTRACT JSON DEBUG ===")
            return result
        except json.JSONDecodeError as e:
            logger.info(f"Failed to parse entire text as JSON: {str(e)}")
            pass

        # Next, try to find JSON object in text
        start_idx = text.find('{')
        end_idx = text.rfind('}')

        logger.info(f"JSON object markers: start_idx={start_idx}, end_idx={end_idx}")

        if start_idx == -1 or end_idx == -1:
            # Try to find JSON array instead
            start_idx = text.find('[')
            end_idx = text.rfind(']')
            logger.info(f"JSON array markers: start_idx={start_idx}, end_idx={end_idx}")

            if start_idx == -1 or end_idx == -1:
                logger.info("No JSON markers found in text")
                logger.info("=== END EXTRACT JSON DEBUG ===")
                return None

        json_str = text[start_idx:end_idx+1]
        logger.info(f"Extracted JSON string of length: {len(json_str)}")
        logger.info(f"JSON string preview: {json_str[:500]}...")

        # Try to parse the extracted JSON
        try:
            logger.info("Trying to parse extracted JSON string")
            result = json.loads(json_str)
            logger.info("Successfully parsed extracted JSON string")
            logger.info(f"Extracted JSON: {json.dumps(result)[:1000]}...")
            logger.info("=== END EXTRACT JSON DEBUG ===")
            return result
        except json.JSONDecodeError as e:
            logger.info(f"Failed to parse extracted JSON string: {str(e)}")

            # Try to clean up the JSON string
            # Remove any markdown code block markers
            logger.info("Cleaning up JSON string by removing markdown code block markers")
            json_str = re.sub(r'```json|```', '', json_str).strip()
            logger.info(f"Cleaned JSON string preview: {json_str[:500]}...")

            # Try again with the cleaned string
            try:
                logger.info("Trying to parse cleaned JSON string")
                result = json.loads(json_str)
                logger.info("Successfully parsed cleaned JSON string")
                logger.info(f"Extracted JSON: {json.dumps(result)[:1000]}...")
                logger.info("=== END EXTRACT JSON DEBUG ===")
                return result
            except json.JSONDecodeError as e:
                logger.info(f"Failed to parse cleaned JSON string: {str(e)}")

                # Try to fix incomplete JSON by adding missing closing braces/brackets
                logger.info("Attempting to fix incomplete JSON")
                try:
                    # Count opening and closing braces/brackets
                    open_braces = json_str.count('{')
                    close_braces = json_str.count('}')
                    open_brackets = json_str.count('[')
                    close_brackets = json_str.count(']')

                    # Add missing closing braces/brackets
                    fixed_json = json_str
                    for _ in range(open_braces - close_braces):
                        fixed_json += '}'
                    for _ in range(open_brackets - close_brackets):
                        fixed_json += ']'

                    logger.info(f"Fixed JSON preview: {fixed_json[:500]}...")

                    # Try to parse the fixed JSON
                    result = json.loads(fixed_json)
                    logger.info("Successfully parsed fixed JSON")
                    logger.info(f"Extracted JSON: {json.dumps(result)[:1000]}...")
                    logger.info("=== END EXTRACT JSON DEBUG ===")
                    return result
                except json.JSONDecodeError as e:
                    logger.info(f"Failed to parse fixed JSON: {str(e)}")

                    # Try to extract a partial JSON object with required fields
                    logger.info("Attempting to extract partial JSON with required fields")
                    try:
                        # Use regex to extract key fields
                        name_match = re.search(r'"name"\s*:\s*"([^"]+)"', json_str)
                        age_match = re.search(r'"age"\s*:\s*"([^"]+)"', json_str)
                        race_match = re.search(r'"race"\s*:\s*"([^"]+)"', json_str)
                        gender_match = re.search(r'"gender_identity"\s*:\s*"([^"]+)"', json_str)
                        orientation_match = re.search(r'"sexual_orientation"\s*:\s*"([^"]+)"', json_str)
                        description_match = re.search(r'"(?:Physical description|description)"\s*:\s*"([^"]+)"', json_str)
                        role_match = re.search(r'"role"\s*:\s*"([^"]+)"', json_str)
                        backstory_match = re.search(r'"backstory"\s*:\s*"([^"]+)"', json_str)
                        arc_match = re.search(r'"arc"\s*:\s*"([^"]+)"', json_str)

                        # Extract traits array if possible
                        traits_match = re.search(r'"traits"\s*:\s*\[(.*?)\]', json_str, re.DOTALL)
                        traits = []
                        if traits_match:
                            traits_str = traits_match.group(1)
                            trait_matches = re.findall(r'"([^"]+)"', traits_str)
                            traits = trait_matches

                        # Try to extract relationships
                        relationships = []
                        relationships_section = re.search(r'"relationships"\s*:\s*\[(.*?)(?:\]\s*}|\Z)', json_str, re.DOTALL)
                        if relationships_section:
                            rel_text = relationships_section.group(1)
                            logger.info(f"Found relationships section in extract_json_from_text: {rel_text[:500]}...")

                            # Find all complete relationship objects
                            rel_objects = re.findall(r'\{(.*?)\}', rel_text, re.DOTALL)
                            logger.info(f"Found {len(rel_objects)} relationship objects in extract_json_from_text")

                            for rel_obj in rel_objects:
                                rel_char_match = re.search(r'"character"\s*:\s*"([^"]+)"', rel_obj)
                                rel_type_match = re.search(r'"(?:relationship|type)"\s*:\s*"([^"]+)"', rel_obj)
                                rel_desc_match = re.search(r'"description"\s*:\s*"([^"]+)"', rel_obj)
                                rel_strength_match = re.search(r'"strength"\s*:\s*(\d+)', rel_obj)

                                if rel_char_match:
                                    rel = {
                                        "character": rel_char_match.group(1),
                                        "relationship": rel_type_match.group(1) if rel_type_match else "Friend",
                                        "description": rel_desc_match.group(1) if rel_desc_match else "",
                                        "strength": int(rel_strength_match.group(1)) if rel_strength_match else 3
                                    }
                                    relationships.append(rel)
                                    logger.info(f"Extracted relationship in extract_json_from_text: {json.dumps(rel)}")

                        # If no relationships found, try a more aggressive approach
                        if not relationships:
                            logger.info("No relationships found with first method in extract_json_from_text, trying alternative approach")
                            # Look for all relationship objects in the entire string
                            rel_pattern = r'"character"\s*:\s*"([^"]+)"[^}]*"(?:relationship|type)"\s*:\s*"([^"]+)"[^}]*"description"\s*:\s*"([^"]+)"[^}]*"strength"\s*:\s*(\d+)'
                            alt_rel_matches = re.finditer(rel_pattern, json_str, re.DOTALL)

                            for match in alt_rel_matches:
                                rel = {
                                    "character": match.group(1),
                                    "relationship": match.group(2),
                                    "description": match.group(3),
                                    "strength": int(match.group(4))
                                }
                                relationships.append(rel)
                                logger.info(f"Extracted relationship with alternative method in extract_json_from_text: {json.dumps(rel)}")

                        # Create a partial result with available fields
                        partial_result = {
                            "name": name_match.group(1) if name_match else "",
                            "age": age_match.group(1) if age_match else "",
                            "race": race_match.group(1) if race_match else "",
                            "gender_identity": gender_match.group(1) if gender_match else "",
                            "sexual_orientation": orientation_match.group(1) if orientation_match else "",
                            "description": description_match.group(1) if description_match else "",
                            "role": role_match.group(1) if role_match else "",
                            "backstory": backstory_match.group(1) if backstory_match else "",
                            "arc": arc_match.group(1) if arc_match else "",
                            "traits": traits,
                            "relationships": relationships
                        }

                        logger.info("Successfully extracted partial JSON")
                        logger.info(f"Partial JSON: {json.dumps(partial_result)}")
                        logger.info("=== END EXTRACT JSON DEBUG ===")
                        return partial_result
                    except Exception as e:
                        logger.info(f"Failed to extract partial JSON: {str(e)}")
                        logger.info("=== END EXTRACT JSON DEBUG ===")
                        return None
    except Exception as e:
        logger.error(f"Error extracting JSON from text: {str(e)}")
        logger.error(f"Raw text preview: {text[:1000]}...")
        logger.info("=== END EXTRACT JSON DEBUG ===")
        return None

@router.post("/generate")
async def generate_character(book_id: str, request: Request, user_id: str = Depends(get_current_user)):
    """Generate a character using AI and generate a headshot"""
    try:
        data = await request.json()
        prompt_data = data.get("prompt", "")

        # Check if the prompt is a JSON string (structured prompt)
        try:
            structured_prompt = json.loads(prompt_data) if isinstance(prompt_data, str) and prompt_data.startswith('{') else prompt_data
            user_prompt = structured_prompt.get("userPrompt", "")
            book_genre = structured_prompt.get("bookGenre", "fiction")
            existing_characters = structured_prompt.get("existingCharacters", [])
        except (json.JSONDecodeError, AttributeError):
            # If not a valid JSON, treat it as a simple string prompt
            user_prompt = prompt_data
            book_genre = "fiction"
            existing_characters = []

        # Create context for the AI
        context = f"Book genre: {book_genre}"

        # Prepare the prompt for the AI
        ai_prompt = f"Generate a detailed character for a {book_genre} story based on this description: {user_prompt}\n\n"

        # Add information about existing characters for relationships
        if existing_characters and len(existing_characters) > 0:
            ai_prompt += "\nExisting characters in the story that this character might have relationships with or be related to:\n"
            for i, char in enumerate(existing_characters):
                char_name = char.get('name', 'Unknown')
                char_role = char.get('role', 'Unknown role')
                char_age = char.get('age', 'Unknown age')
                char_gender = char.get('gender_identity', '')
                char_orientation = char.get('sexual_orientation', '')
                char_backstory = char.get('backstory', 'No backstory')

                ai_prompt += f"{i+1}. {char_name} - {char_role} - {char_age}"
                if char_gender:
                    ai_prompt += f", {char_gender}"
                if char_orientation:
                    ai_prompt += f", {char_orientation}"
                ai_prompt += f": {char_backstory}\n"
            context += f"\nThe story has {len(existing_characters)} existing characters."

        ai_prompt += "\nInclude the following information:\n"
        ai_prompt += "- Name\n"
        ai_prompt += "- Age\n"
        ai_prompt += "- Race or ethnicity\n"
        ai_prompt += "- Gender identity (male, female, non-binary, genderfluid, etc.)\n"
        ai_prompt += "- Sexual orientation (heterosexual, homosexual, bisexual, pansexual, asexual, etc.)\n"
        ai_prompt += "- Physical description (be detailed about appearance for image generation)\n"
        ai_prompt += "- Personality traits\n"
        ai_prompt += "- Background/history\n"
        ai_prompt += "- Role in the story (protagonist, antagonist, etc.)\n"
        ai_prompt += "- Special abilities or skills (if applicable)\n"
        ai_prompt += "- Character arc (how the character changes throughout the story)\n"

        # Add relationships section if there are existing characters
        if existing_characters and len(existing_characters) > 0:
            ai_prompt += "- Relationships with existing characters (be specific about the nature of each relationship)\n"

        ai_prompt += "\nFormat your response as a JSON object with the following structure:\n"
        ai_prompt += "{\n"
        ai_prompt += '  "name": "Character Name",\n'
        ai_prompt += '  "age": "Character Age",\n'
        ai_prompt += '  "race": "Character Race or Ethnicity",\n'
        ai_prompt += '  "gender_identity": "Character Gender Identity",\n'
        ai_prompt += '  "sexual_orientation": "Character Sexual Orientation",\n'
        ai_prompt += '  "Physical description": "Detailed physical description",\n'
        ai_prompt += '  "role": "Role in the story",\n'
        ai_prompt += '  "backstory": "Character\'s personality, history and background",\n'
        ai_prompt += '  "arc": "How the character changes throughout the story",\n'
        ai_prompt += '  "traits": ["Trait 1", "Trait 2", "Trait 3"]'

        # Add relationships field if there are existing characters
        if existing_characters and len(existing_characters) > 0:
            ai_prompt += ',\n  "relationships": ['
            for i, char in enumerate(existing_characters):
                if i > 0:
                    ai_prompt += ', '
                char_name = char.get('name', 'Unknown')
                ai_prompt += '{'
                ai_prompt += f'"character": "{char_name}", '
                ai_prompt += '"relationship": "type", '
                ai_prompt += '"description": "Detailed description of the relationship", '
                ai_prompt += '"strength": 3'  # Default strength value as example
                ai_prompt += '}'
            ai_prompt += ']'

            # Add explanation of relationship strength levels
            ai_prompt += '\n\nRelationship strength levels (you MUST include a strength value for each relationship):'
            ai_prompt += '\n0: Enemy, hostile connection'
            ai_prompt += '\n1: Acquaintance, minimal interaction'
            ai_prompt += '\n2: Casual friend, occasional contact'
            ai_prompt += '\n3: Close friend, regular interaction'
            ai_prompt += '\n4: Deep bond, trust, frequent support'
            ai_prompt += '\n5: Intimate, profound connection, constant reliance'

        ai_prompt += "\n}\n"

        # Log the entire prompt for debugging
        logger.info("=== GENERATE CHARACTER DEBUG ===")
        logger.info(f"User Prompt: {user_prompt}")
        logger.info(f"Genre: {book_genre}")
        logger.info(f"Number of existing characters: {len(existing_characters)}")
        logger.info("=== FULL PROMPT ===")
        logger.info(ai_prompt)
        logger.info("=== CONTEXT ===")
        logger.info(context)

        # Call the AI service for text generation
        logger.info(f"Generating character with AI: {ai_prompt[:100000]}...")
        response = await call_ai_service(ai_prompt, context, user_id=user_id)

        # Log the raw response
        logger.info("=== RAW RESPONSE ===")
        logger.info(response)
        logger.info("=== END DEBUG ===")

        # Extract the JSON from the response
        character_data = extract_json_from_text(response)

        if not character_data:
            logger.error(f"Failed to parse AI response: {response}")
            # Check if this might be an API key issue
            if "API key" in response or "api key" in response.lower():
                return {"error": "AI provider API key issue. Please check your API keys in the backend/.env file.", "raw_response": response}

            # Try to extract partial data using regex
            logger.info("Attempting to extract partial character data using regex")
            try:
                # Extract basic character information
                name_match = re.search(r'"name"\s*:\s*"([^"]+)"', response)
                age_match = re.search(r'"age"\s*:\s*"([^"]+)"', response)
                race_match = re.search(r'"race"\s*:\s*"([^"]+)"', response)
                gender_match = re.search(r'"gender_identity"\s*:\s*"([^"]+)"', response)
                orientation_match = re.search(r'"sexual_orientation"\s*:\s*"([^"]+)"', response)
                description_match = re.search(r'"(?:Physical description|description)"\s*:\s*"([^"]+)"', response)
                role_match = re.search(r'"role"\s*:\s*"([^"]+)"', response)
                backstory_match = re.search(r'"backstory"\s*:\s*"([^"]+)"', response)
                arc_match = re.search(r'"arc"\s*:\s*"([^"]+)"', response)

                # Extract traits array if possible
                traits = []
                traits_match = re.search(r'"traits"\s*:\s*\[(.*?)\]', response, re.DOTALL)
                if traits_match:
                    traits_str = traits_match.group(1)
                    trait_matches = re.findall(r'"([^"]+)"', traits_str)
                    traits = trait_matches

                # Try to extract relationships if possible
                relationships = []

                # First, try to find the relationships section
                relationships_section = re.search(r'"relationships"\s*:\s*\[(.*?)(?:\]\s*}|\Z)', response, re.DOTALL)
                if relationships_section:
                    rel_text = relationships_section.group(1)
                    logger.info(f"Found relationships section: {rel_text[:500]}...")

                    # Find all complete relationship objects
                    rel_objects = re.findall(r'\{(.*?)\}', rel_text, re.DOTALL)
                    logger.info(f"Found {len(rel_objects)} relationship objects")

                    for rel_obj in rel_objects:
                        rel_char_match = re.search(r'"character"\s*:\s*"([^"]+)"', rel_obj)
                        rel_type_match = re.search(r'"(?:relationship|type)"\s*:\s*"([^"]+)"', rel_obj)
                        rel_desc_match = re.search(r'"description"\s*:\s*"([^"]+)"', rel_obj)
                        rel_strength_match = re.search(r'"strength"\s*:\s*(\d+)', rel_obj)

                        if rel_char_match:
                            rel = {
                                "character": rel_char_match.group(1),
                                "relationship": rel_type_match.group(1) if rel_type_match else "Friend",
                                "description": rel_desc_match.group(1) if rel_desc_match else "",
                                "strength": int(rel_strength_match.group(1)) if rel_strength_match else 3
                            }
                            relationships.append(rel)
                            logger.info(f"Extracted relationship: {json.dumps(rel)}")

                # If no relationships found with the first method, try a more aggressive approach
                if not relationships:
                    logger.info("No relationships found with first method, trying alternative approach")
                    # Look for all relationship objects in the entire response
                    rel_pattern = r'"character"\s*:\s*"([^"]+)"[^}]*"(?:relationship|type)"\s*:\s*"([^"]+)"[^}]*"description"\s*:\s*"([^"]+)"[^}]*"strength"\s*:\s*(\d+)'
                    alt_rel_matches = re.finditer(rel_pattern, response, re.DOTALL)

                    for match in alt_rel_matches:
                        rel = {
                            "character": match.group(1),
                            "relationship": match.group(2),
                            "description": match.group(3),
                            "strength": int(match.group(4))
                        }
                        relationships.append(rel)
                        logger.info(f"Extracted relationship with alternative method: {json.dumps(rel)}")

                # If still no relationships, try an even more lenient approach
                if not relationships:
                    logger.info("No relationships found with second method, trying final approach")
                    # Just look for character names and relationship types
                    char_matches = re.finditer(r'"character"\s*:\s*"([^"]+)"', response)
                    for i, match in enumerate(char_matches):
                        char_name = match.group(1)
                        # Try to find relationship type near this character name
                        rel_type_match = re.search(r'"(?:relationship|type)"\s*:\s*"([^"]+)"', response[match.start():match.start()+500])
                        rel_type = rel_type_match.group(1) if rel_type_match else "Friend"

                        # Try to find strength near this character name
                        strength_match = re.search(r'"strength"\s*:\s*(\d+)', response[match.start():match.start()+500])
                        strength = int(strength_match.group(1)) if strength_match else 3

                        rel = {
                            "character": char_name,
                            "relationship": rel_type,
                            "description": f"Relationship with {char_name}",
                            "strength": strength
                        }
                        relationships.append(rel)
                        logger.info(f"Extracted minimal relationship: {json.dumps(rel)}")

                # Create a partial character object
                partial_character = {
                    "name": name_match.group(1) if name_match else "",
                    "age": age_match.group(1) if age_match else "",
                    "race": race_match.group(1) if race_match else "",
                    "gender_identity": gender_match.group(1) if gender_match else "",
                    "sexual_orientation": orientation_match.group(1) if orientation_match else "",
                    "description": description_match.group(1) if description_match else "",
                    "role": role_match.group(1) if role_match else "",
                    "backstory": backstory_match.group(1) if backstory_match else "",
                    "arc": arc_match.group(1) if arc_match else "",
                    "traits": traits,
                    "relationships": relationships
                }

                # Check if we have enough data to proceed
                if partial_character["name"] and (partial_character["description"] or partial_character["backstory"]):
                    logger.info(f"Successfully extracted partial character data: {json.dumps(partial_character)[:1000]}...")
                    character_data = partial_character
                else:
                    logger.error("Insufficient data extracted from AI response")
                    return {"error": "Failed to parse AI response", "raw_response": response}
            except Exception as e:
                logger.error(f"Error extracting partial character data: {str(e)}")
                return {"error": "Failed to parse AI response", "raw_response": response}

        # Handle renamed fields from the AI response
        if "Physical description" in character_data and not "description" in character_data:
            character_data["description"] = character_data["Physical description"]
            del character_data["Physical description"]

        logger.debug(f"Processed character data: {character_data}")

        # Format relationships as needed by the frontend
        if "relationships" in character_data and existing_characters:
            formatted_relationships = []
            for rel in character_data.get("relationships", []):
                # Log the raw relationship data for debugging
                logger.info(f"Processing character relationship: {json.dumps(rel)}")

                char_name = rel.get("character")
                rel_desc = rel.get("relationship")
                # Check for description in various formats
                rel_description = rel.get("description", "")

                # Check all possible keys that might contain the relationship description
                if not rel_description:
                    possible_keys = [
                        "Description of relationship with this character",
                        "description of relationship with this character",
                        "Description",
                        "Relationship description",
                        "relationship description"
                    ]

                    for key in possible_keys:
                        if rel.get(key):
                            rel_description = rel.get(key)
                            logger.info(f"Found description in key '{key}': {rel_description[:100]}...")
                            break

                # Process relationship format if it's in the format "type <description>"
                if not rel_description and isinstance(rel_desc, str) and "<" in rel_desc and ">" in rel_desc:
                    # Extract type and description
                    match = re.search(r'([^<]+)<([^>]+)>', rel_desc)
                    if match:
                        rel_desc = match.group(1).strip()
                        rel_description = match.group(2).strip()
                        logger.info(f"Extracted description from format 'type <description>': {rel_description[:100]}...")

                # Get strength with default of 3 (close friend)
                rel_strength = rel.get("strength", 3)
                logger.info(f"Initial strength value: {rel_strength}")

                # Ensure strength is an integer between 0 and 5
                try:
                    rel_strength = int(rel_strength)
                    rel_strength = max(0, min(5, rel_strength))
                    logger.info(f"Parsed strength as integer: {rel_strength}")
                except (ValueError, TypeError):
                    # If strength is not a valid integer, try to infer it from the relationship type
                    logger.info(f"Failed to parse strength as integer, trying to infer from type: {rel_desc}")
                    if isinstance(rel_desc, str):
                        rel_desc_lower = rel_desc.lower()
                        if "enemy" in rel_desc_lower or "hostile" in rel_desc_lower or "rival" in rel_desc_lower:
                            rel_strength = 0
                        elif "acquaintance" in rel_desc_lower or "minimal" in rel_desc_lower:
                            rel_strength = 1
                        elif "casual" in rel_desc_lower:
                            rel_strength = 2
                        elif "close" in rel_desc_lower:
                            rel_strength = 3
                        elif "deep" in rel_desc_lower or "strong" in rel_desc_lower:
                            rel_strength = 4
                        elif "intimate" in rel_desc_lower or "profound" in rel_desc_lower or "best" in rel_desc_lower:
                            rel_strength = 5
                        else:
                            rel_strength = 3  # Default to 3 if we can't infer
                        logger.info(f"Inferred strength from type: {rel_strength}")
                    else:
                        rel_strength = 3  # Default to 3 if invalid
                        logger.info(f"Using default strength: {rel_strength}")

                # Find the character ID from existing characters
                char_id = None
                for existing_char in existing_characters:
                    if existing_char.get("name") == char_name:
                        char_id = existing_char.get("id")
                        break

                # Log the final relationship data
                logger.info(f"Final relationship data: name={char_name}, type={rel_desc}, strength={rel_strength}, has_id={char_id is not None}")

                if char_id:
                    formatted_relationships.append({
                        "relatedCharacterId": char_id,
                        "relatedCharacterName": char_name,
                        "relationshipType": rel_desc,
                        "description": rel_description,
                        "strength": rel_strength
                    })

            character_data["relationships"] = formatted_relationships

        # Generate a headshot for the character
        if "name" in character_data:
            # Use Physical description if available, otherwise use description
            description = character_data.get("description", "")
            logger.info(f"Generating headshot for character: {character_data['name']}")
            headshot_url = await generate_character_headshot(
                character_data["name"],
                description,
                user_id=user_id
            )

            if headshot_url:
                character_data["headshot"] = headshot_url
                logger.info(f"Generated headshot: {headshot_url}")
            else:
                logger.warning("Failed to generate character headshot")

        return character_data
    except Exception as e:
        logger.error(f"Error generating character: {str(e)}")
        return {"error": str(e), "traceback": traceback.format_exc()}

@router.post("/generate-name")
async def generate_character_name(book_id: str, request: Request, user_id: str = Depends(get_current_user)):
    """Generate a character name using AI"""
    try:
        data = await request.json()
        genre = data.get("genre", "fantasy")
        race = data.get("race", "")
        gender = data.get("gender", "")

        # Create the prompt
        prompt = f"Generate a character name for a {genre} story."
        if race:
            prompt += f" The character is a {race}."
        if gender:
            prompt += f" The character's gender identity is {gender}."

        logger.info(f"Generating name with prompt: {prompt}")

        # Add instructions for the response format
        prompt += " Return only a JSON object with a single field 'name' containing the generated name."

        # Set up system message
        system_message = " max_completion_tokens=10, You are a creative writing assistant that helps generate character names. Respond with a single JSON object containing only the name field."

        # Call the AI service
        response = await call_ai_service(prompt, context=f"Book genre: {genre}", system_message=system_message, user_id=user_id)

        # Extract the JSON from the response
        name_data = extract_json_from_text(response)

        if not name_data or "name" not in name_data:
            logger.error(f"Failed to parse AI response for name generation: {response}")
            return {"error": "Failed to generate name", "raw_response": response}

        return {"name": name_data["name"]}
    except Exception as e:
        logger.error(f"Error generating character name: {str(e)}")
        return {"error": str(e), "traceback": traceback.format_exc()}

@router.post("/generate-age")
async def generate_character_age(book_id: str, request: Request, user_id: str = Depends(get_current_user)):
    """Generate a character age using AI"""
    try:
        data = await request.json()
        genre = data.get("genre", "fantasy")
        race = data.get("race", "")
        role = data.get("role", "")
        backstory = data.get("backstory", "")

        # Create the prompt
        prompt = f"Generate an appropriate age for a character in a {genre} story."
        if race:
            prompt += f" The character is a {race}."
        if role:
            prompt += f" The character's role is {role}."
        if backstory:
            prompt += f" The character's backstory is {backstory}."

        # Add instructions to consider backstory
        prompt += " Consider what age would be most appropriate for this character's potential backstory and role in the story."
        prompt += " Think about what life experiences they might have had and how their age affects their character development."

        # Add instructions for the response format
        prompt += " Return only a JSON object with a single field 'age' containing the generated age as a string."

        # Set up system message
        system_message = " max_completion_tokens=5, You are a creative writing assistant that helps generate character details. Respond with a single JSON object containing only the age field."

        # Call the AI service
        response = await call_ai_service(prompt, context=f"Book genre: {genre}", system_message=system_message, user_id=user_id)

        # Extract the JSON from the response
        age_data = extract_json_from_text(response)

        if not age_data or "age" not in age_data:
            logger.error(f"Failed to parse AI response for age generation: {response}")
            return {"error": "Failed to generate age", "raw_response": response}

        return {"age": age_data["age"]}
    except Exception as e:
        logger.error(f"Error generating character age: {str(e)}")
        return {"error": str(e), "traceback": traceback.format_exc()}

@router.post("/generate-race")
async def generate_character_race(book_id: str, request: Request, user_id: str = Depends(get_current_user)):
    """Generate a character race using AI"""
    try:
        data = await request.json()
        genre = data.get("genre", "fantasy")

        # Create the prompt
        prompt = f"Generate an ethnicity for a character in a {genre} story."

        # Add instructions for the response format
        prompt += " Return only a JSON object with a single field 'race' containing the generated race or ethnicity."

        # Set up system message
        system_message = " max_completion_tokens=10, You are a creative writing assistant that helps generate character details. Respond with a single JSON object containing only the race field."

        # Call the AI service
        response = await call_ai_service(prompt, context=f"Book genre: {genre}", system_message=system_message, user_id=user_id)

        # Extract the JSON from the response
        race_data = extract_json_from_text(response)

        if not race_data or "race" not in race_data:
            logger.error(f"Failed to parse AI response for race generation: {response}")
            return {"error": "Failed to generate race", "raw_response": response}

        return {"race": race_data["race"]}
    except Exception as e:
        logger.error(f"Error generating character race: {str(e)}")
        return {"error": str(e), "traceback": traceback.format_exc()}

@router.post("/generate-gender-identity")
async def generate_character_gender_identity(book_id: str, request: Request, user_id: str = Depends(get_current_user)):
    """Generate a character gender identity using AI"""
    try:
        data = await request.json()
        genre = data.get("genre", "fantasy")
        name = data.get("name", "")
        race = data.get("race", "")
        description = data.get("description", "")
        backstory = data.get("backstory", "")

        # Create the prompt
        prompt = f"Generate an appropriate gender identity for a character in a {genre} story."
        if name:
            prompt += f" The character's name is {name}."
        if race:
            prompt += f" The character is a {race}."
        if description:
            prompt += f" The character is described as: {description}."
        if backstory:
            # Truncate backstory if it's too long to keep the prompt manageable
            truncated_backstory = backstory[:300] + "..." if len(backstory) > 300 else backstory
            prompt += f" The character's backstory: {truncated_backstory}"

        # Add instructions for the response format
        prompt += " Return only a JSON object with a single field 'gender_identity' containing the generated gender identity."

        # Set up system message
        system_message = " max_completion_tokens=10, You are a creative writing assistant that helps generate character details. Respond with a single JSON object containing only the gender_identity field."

        # Call the AI service
        response = await call_ai_service(prompt, context=f"Book genre: {genre}", system_message=system_message, user_id=user_id)

        # Extract the JSON from the response
        gender_identity_data = extract_json_from_text(response)

        if not gender_identity_data or "gender_identity" not in gender_identity_data:
            logger.error(f"Failed to parse AI response for gender identity generation: {response}")
            return {"error": "Failed to generate gender identity", "raw_response": response}

        return {"gender_identity": gender_identity_data["gender_identity"]}
    except Exception as e:
        logger.error(f"Error generating character gender identity: {str(e)}")
        return {"error": str(e), "traceback": traceback.format_exc()}

@router.post("/generate-sexual-orientation")
async def generate_character_sexual_orientation(book_id: str, request: Request, user_id: str = Depends(get_current_user)):
    """Generate a character sexual orientation using AI"""
    try:
        data = await request.json()
        genre = data.get("genre", "fantasy")
        name = data.get("name", "")
        gender_identity = data.get("gender_identity", "")

        # Create the prompt
        prompt = f"Generate an appropriate sexual orientation for a character in a {genre} story."
        if name:
            prompt += f" The character's name is {name}."
        if gender_identity:
            prompt += f" The character's gender identity is {gender_identity}."

        # Add instructions for the response format
        prompt += " Return only a JSON object with a single field 'sexual_orientation' containing the generated sexual orientation."

        # Set up system message
        system_message = " max_completion_tokens=10, You are a creative writing assistant that helps generate character details. Respond with a single JSON object containing only the sexual_orientation field."

        # Call the AI service
        response = await call_ai_service(prompt, context=f"Book genre: {genre}", system_message=system_message, user_id=user_id)

        # Extract the JSON from the response
        sexual_orientation_data = extract_json_from_text(response)

        if not sexual_orientation_data or "sexual_orientation" not in sexual_orientation_data:
            logger.error(f"Failed to parse AI response for sexual orientation generation: {response}")
            return {"error": "Failed to generate sexual orientation", "raw_response": response}

        return {"sexual_orientation": sexual_orientation_data["sexual_orientation"]}
    except Exception as e:
        logger.error(f"Error generating character sexual orientation: {str(e)}")
        return {"error": str(e), "traceback": traceback.format_exc()}

@router.post("/generate-description")
async def generate_character_description(book_id: str, request: Request, user_id: str = Depends(get_current_user)):
    """Generate a character physical description using AI"""
    try:
        data = await request.json()
        genre = data.get("genre", "fantasy")
        name = data.get("name", "")
        race = data.get("race", "")
        age = data.get("age", "")

        # Create the prompt
        prompt = f"Generate a detailed physical descriptionfor a character in a {genre} story."
        if name:
            prompt += f" The character's name is {name}."
        if race:
            prompt += f" The character is a {race}."
        if age:
            prompt += f" The character is {age} years old."

        # Add instructions for the response format
        prompt += " Focus only on physical appearance, including details about face, body, clothing, and distinctive features do not include their name or age in your return."
        prompt += " Return only a JSON object with a single field 'description' containing the generated physical description."

        # Set up system message
        system_message = " max_completion_tokens=200 ,You are a creative writing assistant that helps generate character details. Respond with a single JSON object containing only the description field with a detailed physical description."

        # Call the AI service
        response = await call_ai_service(prompt, context=f"Book genre: {genre}", system_message=system_message, user_id=user_id)

        # Extract the JSON from the response
        description_data = extract_json_from_text(response)

        if not description_data or "description" not in description_data:
            logger.error(f"Failed to parse AI response for description generation: {response}")
            return {"error": "Failed to generate description", "raw_response": response}

        # Handle the case where description is an object instead of a string
        description_value = description_data["description"]
        if isinstance(description_value, dict):
            # Try to convert the object to a string
            try:
                # If it's a nested object, try to extract text content
                if "content" in description_value:
                    description_value = description_value["content"]
                elif "text" in description_value:
                    description_value = description_value["text"]
                elif "value" in description_value:
                    description_value = description_value["value"]
                else:
                    # Convert the entire object to a formatted string
                    description_value = json.dumps(description_value, indent=2)
            except Exception as e:
                logger.error(f"Error converting description object to string: {str(e)}")
                # Fall back to string representation
                description_value = str(description_value)

        return {"description": description_value}
    except Exception as e:
        logger.error(f"Error generating character description: {str(e)}")
        return {"error": str(e), "traceback": traceback.format_exc()}

@router.post("/generate-traits")
async def generate_character_traits(book_id: str, request: Request, user_id: str = Depends(get_current_user)):
    """Generate character traits using AI"""
    try:
        data = await request.json()
        genre = data.get("genre", "fantasy")
        name = data.get("name", "")
        age = data.get("age", "")
        role = data.get("role", "")
        description = data.get("description", "")

        # Create the prompt
        prompt = f"Generate 3-5 personality traits for a character in a {genre} story."
        if name:
            prompt += f" The character's name is {name}."
        if age:
            prompt += f" The character's age is {age}."
        if role:
            prompt += f" The character's role is {role}."
        if description:
            prompt += f" The character's physical description: {description}."

        # Add instructions for the response format
        prompt += " Return only a JSON object with a single field 'traits' containing an array of the generated traits."

        # Set up system message
        system_message = " max_completion_tokens=50,You are a creative writing assistant that helps generate character details. Respond with a single JSON object containing only the traits field with an array of 3-5 personality traits."

        # Call the AI service
        response = await call_ai_service(prompt, context=f"Book genre: {genre}", system_message=system_message, user_id=user_id)

        # Extract the JSON from the response
        traits_data = extract_json_from_text(response)

        if not traits_data or "traits" not in traits_data or not isinstance(traits_data["traits"], list):
            logger.error(f"Failed to parse AI response for traits generation: {response}")
            return {"error": "Failed to generate traits", "raw_response": response}

        return {"traits": traits_data["traits"]}
    except Exception as e:
        logger.error(f"Error generating character traits: {str(e)}")
        return {"error": str(e), "traceback": traceback.format_exc()}

@router.post("/generate-backstory")
async def generate_character_backstory(book_id: str, request: Request, user_id: str = Depends(get_current_user)):
    """Generate a character backstory using AI"""
    try:
        data = await request.json()
        genre = data.get("genre", "fantasy")
        name = data.get("name", "")
        race = data.get("race", "")
        age = data.get("age", "")
        traits = data.get("traits", [])

        # Create the prompt
        prompt = f"limit response to less than 1000 characters,  Generate a detailed backstory for a character in a {genre} story."
        if name:
            prompt += f" The character's name is {name}."
        if race:
            prompt += f" The character is a {race}."
        if age:
            prompt += f" The character is {age} years old."
        if traits and len(traits) > 0:
            prompt += f" The character has the following traits: {', '.join(traits)}."

        # Add instructions for the response format
        prompt += " Include details about their upbringing, significant life events, and what shaped them into who they are now."
        prompt += " Consider important relationships that influenced their development, such as family members, mentors, friends, or enemies."
        prompt += " Describe how these relationships affected their worldview and personality."
        prompt += " Return only a JSON object with a single field 'backstory' containing the generated backstory."

        # Set up system message
        system_message = " limit response to less than 1000 characters, You are a creative writing assistant that helps generate character details. Respond with a single JSON object containing only the backstory field with a detailed character history."

        # Call the AI service
        response = await call_ai_service(prompt, context=f"Book genre: {genre}", system_message=system_message, user_id=user_id)

        # Extract the JSON from the response
        backstory_data = extract_json_from_text(response)

        if not backstory_data or "backstory" not in backstory_data:
            logger.error(f"Failed to parse AI response for backstory generation: {response}")
            return {"error": "Failed to generate backstory", "raw_response": response}

        # Handle the case where backstory is an object instead of a string
        backstory_value = backstory_data["backstory"]
        if isinstance(backstory_value, dict):
            # Try to convert the object to a string
            try:
                # If it's a nested object, try to extract text content
                if "content" in backstory_value:
                    backstory_value = backstory_value["content"]
                elif "text" in backstory_value:
                    backstory_value = backstory_value["text"]
                elif "description" in backstory_value:
                    backstory_value = backstory_value["description"]
                else:
                    # Convert the entire object to a formatted string
                    backstory_value = json.dumps(backstory_value, indent=2)
            except Exception as e:
                logger.error(f"Error converting backstory object to string: {str(e)}")
                # Fall back to string representation
                backstory_value = str(backstory_value)

        return {"backstory": backstory_value}
    except Exception as e:
        logger.error(f"Error generating character backstory: {str(e)}")
        return {"error": str(e), "traceback": traceback.format_exc()}

@router.post("/generate-arc")
async def generate_character_arc(book_id: str, request: Request, user_id: str = Depends(get_current_user)):
    """Generate a character arc using AI"""
    try:
        data = await request.json()
        genre = data.get("genre", "fantasy")
        name = data.get("name", "")
        role = data.get("role", "")
        backstory = data.get("backstory", "")
        traits = data.get("traits", [])

        # Create the prompt
        prompt = f"Generate a character arc for a character in a {genre} story."
        if name:
            prompt += f" The character's name is {name}."
        if role:
            prompt += f" The character's role is {role}."
        if backstory:
            prompt += f" The character's backstory: {backstory}."
        if traits and len(traits) > 0:
            prompt += f" The character has the following traits: {', '.join(traits)}."

        # Add instructions for the response format
        prompt += " Describe how the character changes and develops throughout the story, including challenges they face and how they grow."
        prompt += " Include how their relationships with other characters evolve and impact their development."
        prompt += " Consider how key relationships might be tested, strengthened, broken, or formed as part of their character arc."
        prompt += " Describe how these relationship changes contribute to the character's overall growth and transformation."
        prompt += " Return only a JSON object with a single field 'arc' containing the generated character arc."

        # Set up system message
        system_message = "respond with less than 1000 characters, You are a creative writing assistant that helps generate character details. Return only a JSON object with a single field 'arc' containing the generated character arc."

        # Call the AI service
        response = await call_ai_service(prompt, context=f"Book genre: {genre}", system_message=system_message, user_id=user_id)

        # Extract the JSON from the response
        arc_data = extract_json_from_text(response)

        if not arc_data or "arc" not in arc_data:
            logger.error(f"Failed to parse AI response for arc generation: {response}")
            return {"error": "Failed to generate character arc", "raw_response": response}

        # Handle the case where arc is an object instead of a string
        arc_value = arc_data["arc"]
        if isinstance(arc_value, dict):
            # Try to convert the object to a string
            try:
                # If it's a nested object, try to extract text content
                if "content" in arc_value:
                    arc_value = arc_value["content"]
                elif "text" in arc_value:
                    arc_value = arc_value["text"]
                elif "description" in arc_value:
                    arc_value = arc_value["description"]
                else:
                    # Convert the entire object to a formatted string
                    arc_value = json.dumps(arc_value, indent=2)
            except Exception as e:
                logger.error(f"Error converting arc object to string: {str(e)}")
                # Fall back to string representation
                arc_value = str(arc_value)

        return {"arc": arc_value}
    except Exception as e:
        logger.error(f"Error generating character arc: {str(e)}")
        return {"error": str(e), "traceback": traceback.format_exc()}

@router.post("/generate-gender-identity")
async def generate_character_gender_identity(book_id: str, request: Request, user_id: str = Depends(get_current_user)):
    """Generate a character gender identity using AI"""
    try:
        data = await request.json()
        genre = data.get("genre", "fantasy")
        name = data.get("name", "")
        race = data.get("race", "")
        description = data.get("description", "")
        backstory = data.get("backstory", "")

        # Create the prompt
        prompt = f"Generate an appropriate gender identity for a character in a {genre} story."
        if name:
            prompt += f" The character's name is {name}."
        if race:
            prompt += f" The character is a {race}."
        if description:
            prompt += f" The character's physical description: {description}."
        if backstory:
            # Include the full backstory without truncation
            prompt += f" The character's backstory: {backstory}"

        # Add instructions for the response format
        prompt += " Be inclusive and consider a range of gender identities including but not limited to male, female, non-binary, genderfluid, etc."
        prompt += " Return only a JSON object with a single field 'gender_identity' containing the generated gender identity."

        # Set up system message
        system_message = "You are a creative writing assistant that helps generate character details. Respond with a single JSON object containing only the gender_identity field."

        # Call the AI service
        response = await call_ai_service(prompt, context=f"Book genre: {genre}", system_message=system_message, user_id=user_id)

        # Extract the JSON from the response
        gender_data = extract_json_from_text(response)

        if not gender_data or "gender_identity" not in gender_data:
            logger.error(f"Failed to parse AI response for gender identity generation: {response}")
            return {"error": "Failed to generate gender identity", "raw_response": response}

        return {"gender_identity": gender_data["gender_identity"]}
    except Exception as e:
        logger.error(f"Error generating character gender identity: {str(e)}")
        return {"error": str(e), "traceback": traceback.format_exc()}

@router.post("/generate-sexual-orientation")
async def generate_character_sexual_orientation(book_id: str, request: Request, user_id: str = Depends(get_current_user)):
    """Generate a character sexual orientation using AI"""
    try:
        data = await request.json()
        genre = data.get("genre", "fantasy")
        name = data.get("name", "")
        gender_identity = data.get("gender_identity", "")
        backstory = data.get("backstory", "")

        # Create the prompt
        prompt = f"Generate an appropriate sexual orientation for a character in a {genre} story."
        if name:
            prompt += f" The character's name is {name}."
        if gender_identity:
            prompt += f" The character's gender identity is {gender_identity}."
        if backstory:
            # Include the full backstory without truncation
            prompt += f" The character's backstory: {backstory}"

        # Add instructions for the response format
        prompt += " Be inclusive and consider a range of sexual orientations including but not limited to heterosexual, homosexual, bisexual, pansexual, asexual, etc."
        prompt += " Return only a JSON object with a single field 'sexual_orientation' containing the generated sexual orientation."

        # Set up system message
        system_message = "You are a creative writing assistant that helps generate character details. Respond with a single JSON object containing only the sexual_orientation field."

        # Call the AI service
        response = await call_ai_service(prompt, context=f"Book genre: {genre}", system_message=system_message, user_id=user_id)

        # Extract the JSON from the response
        orientation_data = extract_json_from_text(response)

        if not orientation_data or "sexual_orientation" not in orientation_data:
            logger.error(f"Failed to parse AI response for sexual orientation generation: {response}")
            return {"error": "Failed to generate sexual orientation", "raw_response": response}

        return {"sexual_orientation": orientation_data["sexual_orientation"]}
    except Exception as e:
        logger.error(f"Error generating character sexual orientation: {str(e)}")
        return {"error": str(e), "traceback": traceback.format_exc()}

@router.post("/{character_id}/regenerate-headshot")
async def regenerate_character_headshot(
    book_id: str,
    character_id: str,
    request: Request,
    user_id: str = Depends(get_current_user)
):
    """Regenerate a character headshot with optional style parameter"""
    try:
        data = await request.json()
        style = data.get("style", None)  # Get the optional style parameter

        # Fetch the character from the database
        conn = get_db_connection()
        try:
            with conn.cursor() as cursor:
                cursor.execute(
                    "SELECT * FROM characters WHERE book_id = %s AND character_id = %s",
                    (book_id, character_id)
                )
                character = cursor.fetchone()

                if not character:
                    raise HTTPException(status_code=404, detail=f"Character with ID {character_id} not found")

                # Get the character's current headshot path
                current_headshot = character.get('headshot', '')

                # Delete the old headshot file if it exists and is not an external URL
                if current_headshot and not current_headshot.startswith('http'):
                    try:
                        # Remove the leading slash if present
                        if current_headshot.startswith('/'):
                            current_headshot = current_headshot[1:]

                        # Construct the full path to the headshot file
                        headshot_path = os.path.join(os.getcwd(), current_headshot)

                        # Check if the file exists before attempting to delete
                        if os.path.exists(headshot_path):
                            os.remove(headshot_path)
                            logger.info(f"Deleted old headshot file: {headshot_path}")
                        else:
                            logger.warning(f"Old headshot file not found: {headshot_path}")
                    except Exception as e:
                        logger.error(f"Error deleting old headshot file: {str(e)}")

                # Construct character data for the prompt
                character_data = {
                    "name": character.get('name', ''),
                    "description": character.get('description', ''),
                    "age": character.get('age', ''),
                    "race": character.get('race', ''),
                    "gender_identity": character.get('gender_identity', ''),
                    "sexual_orientation": character.get('sexual_orientation', '')
                }

                # Generate a new headshot
                logger.info(f"Regenerating headshot for character: {character_data['name']} with style: {style}")

                # Log the style parameter for debugging
                logger.debug(f"STYLE PARAMETER: {style}")

                headshot_url = await generate_character_headshot(
                    character_data["name"],
                    character_data["description"],
                    user_id=user_id,
                    style=style
                )

                if not headshot_url:
                    raise HTTPException(status_code=500, detail="Failed to generate headshot")

                # Update the character with the new headshot URL
                cursor.execute(
                    "UPDATE characters SET headshot = %s WHERE book_id = %s AND character_id = %s",
                    (headshot_url, book_id, character_id)
                )
                conn.commit()

                return {"headshot_url": headshot_url}
        finally:
            conn.close()
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error regenerating character headshot: {str(e)}")
        return {"error": str(e), "traceback": traceback.format_exc()}

@router.post("/generate-relationships")
async def generate_character_relationships(book_id: str, request: Request, user_id: str = Depends(get_current_user)):
    """Generate character relationships using AI"""
    try:
        data = await request.json()
        genre = data.get("genre", "fantasy")
        name = data.get("name", "")
        existing_characters = data.get("existingCharacters", [])

        if not existing_characters or len(existing_characters) == 0:
            return {"relationships": [], "message": "No existing characters to create relationships with"}

        # Create the prompt
        prompt = f"Generate relationships in the format name:type:description:strength for {name or 'the new character'} within a story set in {genre} with the following existing characters:\n"

        for i, char in enumerate(existing_characters):
            char_name = char.get("name", f"Character {i+1}")
            char_role = char.get("role", "")
            char_age = char.get("age", "")
            char_desc = char.get("description", "")
            char_backstory = char.get("backstory", "")
            char_gender_identity = char.get("gender_identity", "")
            char_sexual_orientation = char.get("sexual_orientation", "")

            prompt += f"{i+1}. {char_name}"
            if char_role:
                prompt += f" ({char_role})"
            if char_age:
                prompt += f" ({char_age})"
            if char_gender_identity:
                prompt += f", {char_gender_identity}"
            if char_sexual_orientation:
                prompt += f", {char_sexual_orientation}"
            if char_desc:
                prompt += f": {char_desc}"
            if char_backstory:
                prompt += f"\n   Backstory: {char_backstory[:200]}..." if len(char_backstory) > 200 else f"\n   Backstory: {char_backstory}"
            prompt += "\n"

        # Add instructions for the response format
        prompt += "\nFor each existing character, create a relationship with the new character."
        prompt += " Return a JSON object with a single field 'relationships' containing an array of relationship objects."
        prompt += " Each relationship object MUST have these fields: 'character' (name of the existing character), 'type' (relationship type like friend, enemy, mentor, etc.), 'description' (details about the relationship), and 'strength' (a number from (enemy) 0-5 (intimate)) indicating relationship strength)."
        prompt += "\n\nRelationship strength levels (you MUST include a strength value for each relationship):"
        prompt += "\n0: Enemy, hostile connection"
        prompt += "\n1: Acquaintance, minimal interaction"
        prompt += "\n2: Casual friend, occasional contact"
        prompt += "\n3: Close friend, regular interaction"
        prompt += "\n4: Deep bond, trust, frequent support"
        prompt += "\n5: Intimate, profound connection, constant reliance"
        prompt += "\n\nMake sure to assign an appropriate strength value to each relationship based on the descriptions above."

        # Set up system message
        system_message = "You are a creative writing assistant that helps generate character relationships. Respond with a single JSON object containing only the 'relationships' field with an array of relationship objects. Each object must have 'character', 'type', 'description', and 'strength' fields. The strength should be an integer from 0 to 5 indicating relationship strength."

        # Log the entire prompt for debugging
        logger.info("=== GENERATE RELATIONSHIPS DEBUG ===")
        logger.info(f"Character Name: {name}")
        logger.info(f"Genre: {genre}")
        logger.info(f"Number of existing characters: {len(existing_characters)}")
        logger.info("=== FULL PROMPT ===")
        logger.info(prompt)
        logger.info("=== SYSTEM MESSAGE ===")
        logger.info(system_message)

        # Call the AI service
        response = await call_ai_service(prompt, context=f"Book genre: {genre}", system_message=system_message, user_id=user_id)

        # Log the raw response
        logger.info("=== RAW RESPONSE ===")
        logger.info(response)
        logger.info("=== END DEBUG ===")

        # Extract the JSON from the response
        relationships_data = extract_json_from_text(response)

        if not relationships_data or "relationships" not in relationships_data or not isinstance(relationships_data["relationships"], list):
            logger.error(f"Failed to parse AI response for relationships generation: {response}")
            return {"error": "Failed to generate relationships", "raw_response": response}

        # Format relationships for the frontend
        formatted_relationships = []
        for rel in relationships_data["relationships"]:
            char_name = rel.get("character", "")
            rel_type = rel.get("type", "")
            rel_desc = rel.get("description", "")

            # Get strength with default of 3 (close friend)
            rel_strength = rel.get("strength", 3)

            # Log the raw relationship data for debugging
            logger.info(f"Processing relationship: {json.dumps(rel)}")

            # Ensure strength is an integer between 0 and 5
            try:
                rel_strength = int(rel_strength)
                rel_strength = max(0, min(5, rel_strength))
                logger.info(f"Parsed strength as integer: {rel_strength}")
            except (ValueError, TypeError):
                # If strength is not a valid integer, try to infer it from the relationship type
                logger.info(f"Failed to parse strength as integer, trying to infer from type: {rel_type}")
                if isinstance(rel_type, str):
                    rel_type_lower = rel_type.lower()
                    if "enemy" in rel_type_lower or "hostile" in rel_type_lower:
                        rel_strength = 0
                    elif "acquaintance" in rel_type_lower or "minimal" in rel_type_lower:
                        rel_strength = 1
                    elif "casual" in rel_type_lower:
                        rel_strength = 2
                    elif "close" in rel_type_lower:
                        rel_strength = 3
                    elif "deep" in rel_type_lower or "strong" in rel_type_lower:
                        rel_strength = 4
                    elif "intimate" in rel_type_lower or "profound" in rel_type_lower:
                        rel_strength = 5
                    else:
                        rel_strength = 3  # Default to 3 if we can't infer
                    logger.info(f"Inferred strength from type: {rel_strength}")
                else:
                    rel_strength = 3  # Default to 3 if invalid
                    logger.info(f"Using default strength: {rel_strength}")

            # Find the character ID from existing characters
            char_id = None
            for existing_char in existing_characters:
                if existing_char.get("name") == char_name:
                    char_id = existing_char.get("id") or existing_char.get("character_id")
                    break

            # Log the final relationship data
            logger.info(f"Final relationship data: name={char_name}, type={rel_type}, strength={rel_strength}, has_id={char_id is not None}")

            if char_id:
                formatted_relationships.append({
                    "relatedCharacterId": char_id,
                    "relatedCharacterName": char_name,
                    "relationshipType": rel_type,
                    "description": rel_desc,
                    "strength": rel_strength
                })
            else:
                # If we can't find the character ID, still include the relationship with just the name
                formatted_relationships.append({
                    "relatedCharacterName": char_name,
                    "relationshipType": rel_type,
                    "description": rel_desc,
                    "strength": rel_strength
                })

        return {"relationships": formatted_relationships}
    except Exception as e:
        logger.error(f"Error generating character relationships: {str(e)}")
        return {"error": str(e), "traceback": traceback.format_exc()}

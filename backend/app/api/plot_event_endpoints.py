# backend/app/api/plot_event_endpoints.py
from fastapi import APIRouter, Depends, HTTPException, Body
from pydantic import BaseModel
from typing import Optional, List
from app.auth import get_current_user
from app.db.base import get_db_connection
import logging
from uuid import uuid4

logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

router = APIRouter(prefix="/books", tags=["plot_events"])

class PlotEventCreate(BaseModel):
    """Schema for creating a new plot event."""
    title: str
    description: Optional[str] = None
    chapter_id: Optional[str] = None
    sequence_number: Optional[int] = None
    characters: Optional[List[str]] = None
    locations: Optional[List[str]] = None
    event_type: Optional[str] = None

class PlotEventUpdate(BaseModel):
    """Schema for updating a plot event."""
    title: Optional[str] = None
    description: Optional[str] = None
    chapter_id: Optional[str] = None
    sequence_number: Optional[int] = None
    is_in_bank: Optional[bool] = None
    characters: Optional[List[str]] = None
    locations: Optional[List[str]] = None
    event_type: Optional[str] = None

@router.get("/{book_id}/plot/events")
def get_all_plot_events(
    book_id: str,
    user_id: str = Depends(get_current_user)
):
    """Gets all plot events for a book."""
    logger.debug(f"Getting all plot events for book {book_id}")

    # Initialize result as a list at the beginning of the function
    result = []

    try:
        conn = get_db_connection()
        with conn.cursor() as cursor:
            # Verify the book exists and belongs to the user
            cursor.execute(
                "SELECT book_id FROM books WHERE book_id = %s AND user_id = %s",
                (book_id, user_id)
            )
            book = cursor.fetchone()

            if not book:
                raise HTTPException(status_code=404, detail="Book not found or not authorized")

            # Get all plot events for the book
            cursor.execute(
                "SELECT event_id, book_id, chapter_id, title, description, sequence_number, is_in_bank, "
                "x, y, width, height, created_at, updated_at, event_type, source_node_id "
                "FROM plot_events WHERE book_id = %s",
                (book_id,)
            )
            events = cursor.fetchall()

            # Convert to list of dictionaries
            # result is already initialized at the beginning of the function
            for event in events:
                try:
                    # Print each key and value for debugging
                    logger.debug(f"[DEBUG] Event keys: {event.keys()}")
                    for key in event.keys():
                        logger.debug(f"[DEBUG] Event[{key}] = {event[key]}")

                    # Extract metadata from description if it exists
                    description = event['description'] or ''
                    characters = []
                    locations = []

                    # Use event_type from the database if available
                    event_type = event.get('event_type')

                    # Try to get characters and locations from the event_characters and event_locations tables
                    try:
                        # List all tables in the database
                        try:
                            cursor.execute(
                                """SELECT schemaname, tablename FROM pg_tables ORDER BY schemaname, tablename"""
                            )
                            all_tables = cursor.fetchall()
                            logger.debug(f"[DEBUG] All tables in database: {[f'{t['schemaname']}.{t['tablename']}' for t in all_tables]}")
                        except Exception as e:
                            logger.error(f"[DEBUG] Error listing all tables: {str(e)}")

                        # Direct query to check if the event_characters table has data for this event
                        try:
                            cursor.execute(
                                """SELECT * FROM event_characters WHERE event_id = %s""",
                                (event['event_id'],)
                            )
                            direct_characters = cursor.fetchall()
                            logger.debug(f"[DEBUG] Direct query for characters: {direct_characters}")
                        except Exception as direct_error:
                            logger.error(f"[DEBUG] Error with direct character query: {str(direct_error)}")
                        # Get characters
                        cursor.execute(
                            """
                            SELECT EXISTS (
                                SELECT FROM pg_tables
                                WHERE LOWER(tablename) = LOWER('event_characters')
                            )
                            AS table_exists
                            """
                        )
                        table_result = cursor.fetchone()
                        table_exists = table_result['table_exists'] if table_result else False
                        logger.debug(f"[DEBUG] event_characters table exists: {table_exists}")

                        if table_exists:
                            logger.debug(f"[DEBUG] Querying characters for event {event['event_id']}")
                            # Try both lowercase and as-is table names
                            try:
                                cursor.execute(
                                    "SELECT character_name FROM event_characters WHERE event_id = %s",
                                    (event['event_id'],)
                                )
                            except Exception as e:
                                logger.debug(f"[DEBUG] Error with lowercase table name: {str(e)}")
                                # Try with the exact case from information_schema
                                cursor.execute(
                                    """
                                    SELECT tablename, schemaname FROM pg_tables
                                    WHERE LOWER(tablename) = LOWER('event_characters')
                                    """
                                )
                                actual_table_info = cursor.fetchone()
                                if actual_table_info:
                                    logger.debug(f"[DEBUG] Found actual table: {actual_table_info['tablename']} in schema {actual_table_info['schemaname']}")
                                    cursor.execute(
                                        f"SELECT character_name FROM \"{actual_table_info['schemaname']}\".\"{actual_table_info['tablename']}\" WHERE event_id = %s",
                                        (event['event_id'],)
                                    )
                                else:
                                    logger.error(f"[DEBUG] Could not find actual table name for event_characters")
                                    raise
                            character_rows = cursor.fetchall()
                            logger.debug(f"[DEBUG] Raw character rows for event {event['event_id']}: {character_rows}")
                            characters = [row['character_name'] for row in character_rows]
                            logger.debug(f"[DEBUG] Found characters for event {event['event_id']}: {characters}")
                        else:
                            logger.warning(f"[DEBUG] event_characters table does not exist!")

                        # Direct query to check if the event_locations table has data for this event
                        try:
                            cursor.execute(
                                """SELECT * FROM event_locations WHERE event_id = %s""",
                                (event['event_id'],)
                            )
                            direct_locations = cursor.fetchall()
                            logger.debug(f"[DEBUG] Direct query for locations: {direct_locations}")
                        except Exception as direct_error:
                            logger.error(f"[DEBUG] Error with direct location query: {str(direct_error)}")

                        # Get locations
                        cursor.execute(
                            """
                            SELECT EXISTS (
                                SELECT FROM pg_tables
                                WHERE LOWER(tablename) = LOWER('event_locations')
                            )
                            AS table_exists
                            """
                        )
                        table_result = cursor.fetchone()
                        table_exists = table_result['table_exists'] if table_result else False
                        logger.debug(f"[DEBUG] event_locations table exists: {table_exists}")

                        if table_exists:
                            logger.debug(f"[DEBUG] Querying locations for event {event['event_id']}")
                            # Try both lowercase and as-is table names
                            try:
                                cursor.execute(
                                    "SELECT location_name FROM event_locations WHERE event_id = %s",
                                    (event['event_id'],)
                                )
                            except Exception as e:
                                logger.debug(f"[DEBUG] Error with lowercase table name: {str(e)}")
                                # Try with the exact case from information_schema
                                cursor.execute(
                                    """
                                    SELECT tablename, schemaname FROM pg_tables
                                    WHERE LOWER(tablename) = LOWER('event_locations')
                                    """
                                )
                                actual_table_info = cursor.fetchone()
                                if actual_table_info:
                                    logger.debug(f"[DEBUG] Found actual table: {actual_table_info['tablename']} in schema {actual_table_info['schemaname']}")
                                    cursor.execute(
                                        f"SELECT location_name FROM \"{actual_table_info['schemaname']}\".\"{actual_table_info['tablename']}\" WHERE event_id = %s",
                                        (event['event_id'],)
                                    )
                                else:
                                    logger.error(f"[DEBUG] Could not find actual table name for event_locations")
                                    raise
                            location_rows = cursor.fetchall()
                            logger.debug(f"[DEBUG] Raw location rows for event {event['event_id']}: {location_rows}")
                            locations = [row['location_name'] for row in location_rows]
                            logger.debug(f"[DEBUG] Found locations for event {event['event_id']}: {locations}")
                        else:
                            logger.warning(f"[DEBUG] event_locations table does not exist!")
                    except Exception as e:
                        logger.error(f"[DEBUG] Error getting characters/locations: {str(e)}")
                        logger.error(f"[DEBUG] Error type: {type(e).__name__}")
                        import traceback
                        logger.error(f"[DEBUG] Traceback: {traceback.format_exc()}")

                    # Check if description contains metadata (for backward compatibility)
                    if description and 'METADATA:' in description:
                        try:
                            import json
                            # Extract the JSON metadata
                            metadata_match = description.split('METADATA: ', 1)
                            if len(metadata_match) > 1:
                                metadata_json = metadata_match[1]
                                metadata = json.loads(metadata_json)
                                logger.debug(f"[DEBUG] Extracted metadata: {metadata}")

                                # Extract metadata fields only if not already set
                                if not characters and 'characters' in metadata:
                                    characters = metadata['characters']
                                    logger.debug(f"[DEBUG] Extracted characters from metadata: {characters}")
                                if not locations and 'locations' in metadata:
                                    locations = metadata['locations']
                                    logger.debug(f"[DEBUG] Extracted locations from metadata: {locations}")
                                if not event_type and 'event_type' in metadata:
                                    event_type = metadata['event_type']
                                    logger.debug(f"[DEBUG] Extracted event_type from metadata: {event_type}")

                                # Clean up description
                                if '---\n' in description:
                                    description = description.split('---\n')[0].strip()
                        except Exception as e:
                            logger.error(f"[DEBUG] Error parsing metadata: {str(e)}")

                    # Create the event dictionary
                    event_dict = {
                        "id": event['event_id'],
                        "book_id": event['book_id'],
                        "chapter_id": event['chapter_id'],
                        "title": event['title'],
                        "description": description,
                        "sequence_number": event['sequence_number'],
                        "is_in_bank": event['is_in_bank'],
                        "x": event['x'],
                        "y": event['y'],
                        "width": event['width'],
                        "height": event['height'],
                        "characters": characters,
                        "locations": locations,
                        "type": event_type,
                        "source_node_id": event.get('source_node_id')
                    }

                    # Log the event dictionary with detailed character and location info
                    logger.debug(f"[DEBUG] FINAL EVENT DICTIONARY FOR {event['event_id']}:")
                    logger.debug(f"[DEBUG] - Title: {event_dict['title']}")
                    logger.debug(f"[DEBUG] - Characters: {event_dict['characters']} (type: {type(event_dict['characters']).__name__}, length: {len(event_dict['characters']) if isinstance(event_dict['characters'], list) else 'N/A'})")
                    logger.debug(f"[DEBUG] - Locations: {event_dict['locations']} (type: {type(event_dict['locations']).__name__}, length: {len(event_dict['locations']) if isinstance(event_dict['locations'], list) else 'N/A'})")
                    logger.debug(f"[DEBUG] - Type: {event_dict['type']}")
                    logger.debug(f"[DEBUG] - Source Node ID: {event_dict['source_node_id']}")

                    # Log detailed information about characters and locations
                    logger.debug(f"[DEBUG] Event {event['event_id']} characters: {characters}")
                    logger.debug(f"[DEBUG] Event {event['event_id']} locations: {locations}")
                    logger.debug(f"[DEBUG] Event {event['event_id']} type: {event_type}")

                    # Log the event dictionary with metadata
                    logger.debug(f"[DEBUG] Created event dictionary with metadata: id={event_dict['id']}, title={event_dict['title']}, characters={event_dict['characters']}, locations={event_dict['locations']}, type={event_dict['type']}")

                    # Handle datetime fields separately
                    if 'created_at' in event and event['created_at']:
                        event_dict['created_at'] = event['created_at'].isoformat()
                    else:
                        event_dict['created_at'] = None

                    if 'updated_at' in event and event['updated_at']:
                        event_dict['updated_at'] = event['updated_at'].isoformat()
                    else:
                        event_dict['updated_at'] = None

                    # Check if result is a list before appending
                    if isinstance(result, list):
                        result.append(event_dict)
                        logger.debug(f"[DEBUG] Processed event: {event_dict['id']}")
                    else:
                        logger.error(f"[DEBUG] result is not a list, it's a {type(result).__name__}")
                        # Initialize result as a list if it's not already
                        result = [event_dict]
                        logger.debug(f"[DEBUG] Reinitialized result as a list with event: {event_dict['id']}")
                except Exception as event_error:
                    logger.error(f"[DEBUG] Error processing event: {repr(event_error)}, event data: {event}")
                    logger.error(f"[DEBUG] Error type: {type(event_error)}")
                    import traceback
                    logger.error(f"[DEBUG] Traceback: {traceback.format_exc()}")

            return result
    except Exception as e:
        logger.error(f"Error getting plot events: {str(e)}")
        logger.error(f"Error type: {type(e)}")
        logger.error(f"Error traceback: {e.__traceback__}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"Error getting plot events: {str(e)}")
    finally:
        if conn:
            conn.close()

@router.post("/{book_id}/plot/events")
def create_plot_event(
    book_id: str,
    event_data: PlotEventCreate,
    user_id: str = Depends(get_current_user)
):
    """Creates a new plot event."""
    logger.debug(f"Creating new plot event in book {book_id}")

    try:
        conn = get_db_connection()
        with conn.cursor() as cursor:
            # Verify the book exists and belongs to the user
            cursor.execute(
                "SELECT book_id FROM books WHERE book_id = %s AND user_id = %s",
                (book_id, user_id)
            )
            book = cursor.fetchone()

            if not book:
                raise HTTPException(status_code=404, detail="Book not found or not authorized")

            # If chapter_id is provided, verify it exists and belongs to the book
            if event_data.chapter_id:
                cursor.execute(
                    "SELECT chapter_id FROM chapters WHERE chapter_id = %s AND book_id = %s",
                    (event_data.chapter_id, book_id)
                )
                chapter = cursor.fetchone()

                if not chapter:
                    raise HTTPException(status_code=404, detail="Chapter not found or not authorized")

            # Generate a unique event ID
            event_id = f"evt_{str(uuid4()).replace('-', '_')}"

            # If sequence_number is not provided, find the highest sequence number and add 1
            sequence_number = event_data.sequence_number
            if sequence_number is None:
                try:
                    cursor.execute(
                        "SELECT MAX(sequence_number) FROM plot_events WHERE book_id = %s",
                        (book_id,)
                    )
                    result = cursor.fetchone()
                    # Safely handle the case where result might be None or not have index 0
                    if result and result[0] is not None:
                        max_seq = result[0]
                        sequence_number = max_seq + 1
                    else:
                        sequence_number = 1  # First event in this book
                except Exception as seq_error:
                    logger.error(f"[DEBUG] Error getting max sequence number: {str(seq_error)}")
                    sequence_number = 1  # Default to 1 if there's an error

            # Insert the new plot event
            # Add more detailed logging
            logger.debug(f"[DEBUG] Inserting new plot event with data: event_id={event_id}, book_id={book_id}, "
                       f"chapter_id={event_data.chapter_id}, title={event_data.title}, "
                       f"description={event_data.description}, sequence_number={sequence_number}, "
                       f"is_in_bank={event_data.chapter_id is None}")

            # Always insert with chapter_id as NULL to avoid foreign key constraint issues
            logger.debug(f"[DEBUG] Inserting with chapter_id=NULL to avoid foreign key constraint issues")

            # Print all parameters for debugging
            logger.debug(f"[DEBUG] event_id: {event_id}")
            logger.debug(f"[DEBUG] book_id: {book_id}")
            logger.debug(f"[DEBUG] title: {event_data.title}")
            logger.debug(f"[DEBUG] description: {event_data.description}")
            logger.debug(f"[DEBUG] sequence_number: {sequence_number}")

            try:
                # Store characters, locations, and event_type in the description field as JSON if they exist
                description = event_data.description or ''
                metadata = {}

                # Log all fields in event_data for debugging
                logger.debug(f"[DEBUG] Event data fields: {dir(event_data)}")
                try:
                    # Use model_dump if available (newer Pydantic), fall back to dict for older versions
                    if hasattr(event_data, 'model_dump'):
                        logger.debug(f"[DEBUG] Event data dict: {event_data.model_dump()}")
                    elif hasattr(event_data, 'dict'):
                        # Using dict method for backward compatibility
                        logger.debug(f"[DEBUG] Event data dict (using deprecated dict method): {event_data.dict()}")
                    else:
                        logger.debug(f"[DEBUG] Event data: {event_data}")
                except Exception as e:
                    logger.error(f"[DEBUG] Error dumping event data: {str(e)}")

                if hasattr(event_data, 'characters') and event_data.characters:
                    metadata['characters'] = event_data.characters
                    logger.debug(f"[DEBUG] Found characters in event data: {event_data.characters}")

                if hasattr(event_data, 'locations') and event_data.locations:
                    metadata['locations'] = event_data.locations
                    logger.debug(f"[DEBUG] Found locations in event data: {event_data.locations}")

                if hasattr(event_data, 'event_type') and event_data.event_type:
                    metadata['event_type'] = event_data.event_type
                    logger.debug(f"[DEBUG] Found event_type in event data: {event_data.event_type}")

                # If we have metadata, append it to the description
                if metadata:
                    import json
                    if description:
                        description += '\n\n---\n'
                    description += f"METADATA: {json.dumps(metadata)}"
                    logger.debug(f"[DEBUG] Added metadata to description: {metadata}")

                # Use the event_type column directly
                cursor.execute(
                    "INSERT INTO plot_events (event_id, book_id, title, description, is_in_bank, event_type) "
                    "VALUES (%s, %s, %s, %s, TRUE, %s)",
                    (event_id, book_id, event_data.title, description, event_data.event_type)
                )
                logger.debug(f"[DEBUG] Insert successful")
            except Exception as insert_error:
                logger.error(f"[DEBUG] Error during INSERT: {str(insert_error)}")
                logger.error(f"[DEBUG] Error type: {type(insert_error)}")
                import traceback
                logger.error(f"[DEBUG] Traceback: {traceback.format_exc()}")

                # Try an even simpler INSERT as a last resort
                try:
                    logger.debug(f"[DEBUG] Trying simpler INSERT as last resort")
                    cursor.execute(
                        "INSERT INTO plot_events (event_id, book_id, title, event_type) "
                        "VALUES (%s, %s, %s, %s)",
                        (event_id, book_id, event_data.title or 'Untitled Event', event_data.event_type)
                    )
                    logger.debug(f"[DEBUG] Simpler INSERT successful")
                except Exception as last_error:
                    logger.error(f"[DEBUG] Error during simpler INSERT: {str(last_error)}")
                    logger.error(f"[DEBUG] Error type: {type(last_error)}")
                    logger.error(f"[DEBUG] Traceback: {traceback.format_exc()}")
                    raise

            conn.commit()

            # Return the new plot event data
            return {
                "id": event_id,
                "book_id": book_id,
                "chapter_id": None,  # Always null for new events
                "title": event_data.title or 'Untitled Event',
                "description": event_data.description or '',
                "sequence_number": sequence_number,
                "is_in_bank": True,  # Always in bank for new events
                "x": 0,
                "y": 0,
                "width": 200,
                "height": 100,
                "characters": event_data.characters or [],
                "locations": event_data.locations or [],
                "type": event_data.event_type  # Use 'type' to match frontend expectations
            }
    except Exception as e:
        logger.error(f"Error creating plot event: {str(e)}")
        logger.error(f"Error type: {type(e)}")
        logger.error(f"Error traceback: {e.__traceback__}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        if conn:
            conn.rollback()
        raise HTTPException(status_code=500, detail=f"Error creating plot event: {str(e)}")
    finally:
        if conn:
            conn.close()

@router.put("/{book_id}/plot/events/{event_id}")
def update_plot_event(
    book_id: str,
    event_id: str,
    event_data: PlotEventUpdate,
    user_id: str = Depends(get_current_user)
):
    """Updates a plot event."""
    logger.debug(f"Updating plot event {event_id} in book {book_id}")

    try:
        conn = get_db_connection()
        with conn.cursor() as cursor:
            # Verify the book exists and belongs to the user
            cursor.execute(
                "SELECT book_id FROM books WHERE book_id = %s AND user_id = %s",
                (book_id, user_id)
            )
            book = cursor.fetchone()

            if not book:
                raise HTTPException(status_code=404, detail="Book not found or not authorized")

            # Verify the plot event exists and belongs to the book
            cursor.execute(
                "SELECT event_id FROM plot_events WHERE event_id = %s AND book_id = %s",
                (event_id, book_id)
            )
            event = cursor.fetchone()

            if not event:
                raise HTTPException(status_code=404, detail="Plot event not found or not authorized")

            # If chapter_id is provided, verify it exists and belongs to the book
            if event_data.chapter_id:
                cursor.execute(
                    "SELECT chapter_id FROM chapters WHERE chapter_id = %s AND book_id = %s",
                    (event_data.chapter_id, book_id)
                )
                chapter = cursor.fetchone()

                if not chapter:
                    raise HTTPException(status_code=404, detail="Chapter not found or not authorized")

            # Build the update query dynamically based on provided fields
            update_fields = []
            params = []

            if event_data.title is not None:
                update_fields.append("title = %s")
                params.append(event_data.title)

            if event_data.description is not None:
                update_fields.append("description = %s")
                params.append(event_data.description)

            if event_data.chapter_id is not None:
                update_fields.append("chapter_id = %s")
                params.append(event_data.chapter_id)

            if event_data.sequence_number is not None:
                update_fields.append("sequence_number = %s")
                params.append(event_data.sequence_number)

            if event_data.is_in_bank is not None:
                update_fields.append("is_in_bank = %s")
                params.append(event_data.is_in_bank)

            if event_data.event_type is not None:
                update_fields.append("event_type = %s")
                params.append(event_data.event_type)

            # Add updated_at timestamp
            update_fields.append("updated_at = CURRENT_TIMESTAMP")

            # If no fields to update, return early
            if not update_fields:
                return {"status": "success", "message": "No fields to update"}

            # Build and execute the update query
            query = f"UPDATE plot_events SET {', '.join(update_fields)} WHERE event_id = %s AND book_id = %s"
            params.extend([event_id, book_id])

            cursor.execute(query, params)

            if cursor.rowcount == 0:
                raise HTTPException(status_code=500, detail="Failed to update plot event")

            # Update character and location relationships if provided
            if hasattr(event_data, 'characters') and event_data.characters is not None:
                try:
                    # Check if event_characters table exists
                    cursor.execute(
                        """
                        SELECT EXISTS (
                            SELECT FROM information_schema.tables
                            WHERE table_name = 'event_characters'
                        )
                        """
                    )
                    table_exists = cursor.fetchone()[0]

                    if table_exists:
                        # Delete existing character relationships
                        cursor.execute(
                            "DELETE FROM event_characters WHERE event_id = %s",
                            (event_id,)
                        )

                        # Add new character relationships
                        for character in event_data.characters:
                            cursor.execute(
                                "INSERT INTO event_characters (event_id, character_name) VALUES (%s, %s)",
                                (event_id, character)
                            )
                except Exception as e:
                    logger.error(f"[DEBUG] Error updating character relationships: {str(e)}")

            if hasattr(event_data, 'locations') and event_data.locations is not None:
                try:
                    # Check if event_locations table exists
                    cursor.execute(
                        """
                        SELECT EXISTS (
                            SELECT FROM information_schema.tables
                            WHERE table_name = 'event_locations'
                        )
                        """
                    )
                    table_exists = cursor.fetchone()[0]

                    if table_exists:
                        # Delete existing location relationships
                        cursor.execute(
                            "DELETE FROM event_locations WHERE event_id = %s",
                            (event_id,)
                        )

                        # Add new location relationships
                        for location in event_data.locations:
                            cursor.execute(
                                "INSERT INTO event_locations (event_id, location_name) VALUES (%s, %s)",
                                (event_id, location)
                            )
                except Exception as e:
                    logger.error(f"[DEBUG] Error updating location relationships: {str(e)}")

            conn.commit()

            # Get the updated plot event
            cursor.execute(
                "SELECT event_id, book_id, chapter_id, title, description, sequence_number, is_in_bank, "
                "x, y, width, height, created_at, updated_at, event_type "
                "FROM plot_events WHERE event_id = %s",
                (event_id,)
            )
            updated_event = cursor.fetchone()

            # Try to get characters and locations from the event_characters and event_locations tables
            characters = []
            locations = []
            try:
                # Get characters
                cursor.execute(
                    """
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables
                        WHERE table_name = 'event_characters'
                    )
                    """
                )
                table_exists = cursor.fetchone()[0]

                if table_exists:
                    cursor.execute(
                        "SELECT character_name FROM event_characters WHERE event_id = %s",
                        (event_id,)
                    )
                    characters = [row['character_name'] for row in cursor.fetchall()]

                # Get locations
                cursor.execute(
                    """
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables
                        WHERE table_name = 'event_locations'
                    )
                    """
                )
                table_exists = cursor.fetchone()[0]

                if table_exists:
                    cursor.execute(
                        "SELECT location_name FROM event_locations WHERE event_id = %s",
                        (event_id,)
                    )
                    locations = [row['location_name'] for row in cursor.fetchall()]
            except Exception as e:
                logger.error(f"[DEBUG] Error getting characters/locations: {str(e)}")

            return {
                "id": updated_event[0],
                "book_id": updated_event[1],
                "chapter_id": updated_event[2],
                "title": updated_event[3],
                "description": updated_event[4],
                "sequence_number": updated_event[5],
                "is_in_bank": updated_event[6],
                "x": updated_event[7],
                "y": updated_event[8],
                "width": updated_event[9],
                "height": updated_event[10],
                "created_at": updated_event[11].isoformat() if updated_event[11] else None,
                "updated_at": updated_event[12].isoformat() if updated_event[12] else None,
                "type": updated_event[13],  # event_type column
                "characters": characters,
                "locations": locations
            }
    except Exception as e:
        logger.error(f"Error updating plot event: {str(e)}")
        if conn:
            conn.rollback()
        raise HTTPException(status_code=500, detail=f"Error updating plot event: {str(e)}")
    finally:
        if conn:
            conn.close()

@router.delete("/{book_id}/plot/events/{event_id}")
def delete_plot_event(
    book_id: str,
    event_id: str,
    user_id: str = Depends(get_current_user)
):
    """Deletes a plot event."""
    logger.debug(f"Deleting plot event {event_id} from book {book_id}")

    try:
        conn = get_db_connection()
        with conn.cursor() as cursor:
            # Verify the book exists and belongs to the user
            cursor.execute(
                "SELECT book_id FROM books WHERE book_id = %s AND user_id = %s",
                (book_id, user_id)
            )
            book = cursor.fetchone()

            if not book:
                raise HTTPException(status_code=404, detail="Book not found or not authorized")

            # Verify the plot event exists and belongs to the book
            cursor.execute(
                "SELECT event_id FROM plot_events WHERE event_id = %s AND book_id = %s",
                (event_id, book_id)
            )
            event = cursor.fetchone()

            if not event:
                raise HTTPException(status_code=404, detail="Plot event not found or not authorized")

            # Delete the plot event
            cursor.execute(
                "DELETE FROM plot_events WHERE event_id = %s AND book_id = %s",
                (event_id, book_id)
            )

            if cursor.rowcount == 0:
                raise HTTPException(status_code=500, detail="Failed to delete plot event")

            conn.commit()
            return {"status": "success", "message": "Plot event deleted successfully"}
    except Exception as e:
        logger.error(f"Error deleting plot event: {str(e)}")
        if conn:
            conn.rollback()
        raise HTTPException(status_code=500, detail=f"Error deleting plot event: {str(e)}")
    finally:
        if conn:
            conn.close()

@router.post("/{book_id}/plot/events/{event_id}/assign")
def assign_event_to_chapter(
    book_id: str,
    event_id: str,
    chapter_id: str = Body(None, embed=True),
    user_id: str = Depends(get_current_user)
):
    """Assigns a plot event to a chapter or unassigns it (null chapter_id)."""
    logger.debug(f"[DEBUG] Assigning plot event {event_id} to chapter {chapter_id} in book {book_id}")
    logger.debug(f"[DEBUG] User ID: {user_id}")

    try:
        conn = get_db_connection()
        with conn.cursor() as cursor:
            # Verify the book exists and belongs to the user
            cursor.execute(
                "SELECT book_id FROM books WHERE book_id = %s AND user_id = %s",
                (book_id, user_id)
            )
            book = cursor.fetchone()
            logger.debug(f"[DEBUG] Book query result: {book}")

            if not book:
                logger.warning(f"[DEBUG] Book not found or not authorized: {book_id}, user: {user_id}")
                raise HTTPException(status_code=404, detail="Book not found or not authorized")

            # Verify the plot event exists and belongs to the book
            cursor.execute(
                "SELECT event_id FROM plot_events WHERE event_id = %s AND book_id = %s",
                (event_id, book_id)
            )
            event = cursor.fetchone()
            logger.debug(f"[DEBUG] Event query result: {event}")

            if not event:
                logger.warning(f"[DEBUG] Plot event not found or not authorized: {event_id}")
                raise HTTPException(status_code=404, detail="Plot event not found or not authorized")

            # If chapter_id is provided and not null/empty, verify it exists and belongs to the book
            if chapter_id and chapter_id.strip():
                cursor.execute(
                    "SELECT chapter_id FROM chapters WHERE chapter_id = %s AND book_id = %s",
                    (chapter_id, book_id)
                )
                chapter = cursor.fetchone()
                logger.debug(f"[DEBUG] Chapter query result: {chapter}")

                if not chapter:
                    logger.warning(f"[DEBUG] Chapter not found or not authorized: {chapter_id}")
                    raise HTTPException(status_code=404, detail="Chapter not found or not authorized")

                # Update the plot event to assign to chapter
                logger.debug(f"[DEBUG] Assigning event {event_id} to chapter {chapter_id}")
                cursor.execute(
                    "UPDATE plot_events SET chapter_id = %s, is_in_bank = FALSE, updated_at = CURRENT_TIMESTAMP "
                    "WHERE event_id = %s AND book_id = %s",
                    (chapter_id, event_id, book_id)
                )
            else:
                # If chapter_id is null or empty, unassign the event (move to bank)
                logger.debug(f"[DEBUG] Unassigning event {event_id} (moving to bank)")
                logger.debug(f"[DEBUG] chapter_id value: '{chapter_id}'")
                cursor.execute(
                    "UPDATE plot_events SET chapter_id = NULL, is_in_bank = TRUE, updated_at = CURRENT_TIMESTAMP "
                    "WHERE event_id = %s AND book_id = %s",
                    (event_id, book_id)
                )

            if cursor.rowcount == 0:
                logger.error(f"[DEBUG] Failed to update plot event: {event_id}")
                raise HTTPException(status_code=500, detail="Failed to update plot event")

            # Fetch the updated event to return
            cursor.execute(
                "SELECT event_id, book_id, chapter_id, title, description, sequence_number, is_in_bank, "
                "x, y, width, height, created_at, updated_at, event_type, source_node_id "
                "FROM plot_events WHERE event_id = %s AND book_id = %s",
                (event_id, book_id)
            )
            updated_event = cursor.fetchone()
            logger.debug(f"[DEBUG] Updated event: {updated_event}")

            conn.commit()
            logger.debug(f"[DEBUG] Transaction committed")

            # Convert to dictionary
            try:
                # Print each key and value for debugging
                logger.debug(f"[DEBUG] Updated event keys: {updated_event.keys()}")
                for key in updated_event.keys():
                    logger.debug(f"[DEBUG] Updated event[{key}] = {updated_event[key]}")

                # Get characters and locations from the event_characters and event_locations tables
                characters = []
                locations = []
                try:
                    # Get characters - use case-insensitive query to be safe
                    cursor.execute(
                        """
                        SELECT EXISTS (
                            SELECT FROM pg_tables
                            WHERE LOWER(tablename) = LOWER('event_characters')
                        )
                        AS table_exists
                        """
                    )
                    table_result = cursor.fetchone()
                    table_exists = table_result['table_exists'] if table_result else False
                    logger.debug(f"[DEBUG] event_characters table exists: {table_exists}")

                    if table_exists:
                        cursor.execute(
                            "SELECT character_name FROM event_characters WHERE event_id = %s",
                            (event_id,)
                        )
                        character_rows = cursor.fetchall()
                        logger.debug(f"[DEBUG] Raw character rows for event {event_id}: {character_rows}")
                        characters = [row['character_name'] for row in character_rows]
                        logger.debug(f"[DEBUG] Found characters for event {event_id}: {characters}")

                    # Get locations - use case-insensitive query to be safe
                    cursor.execute(
                        """
                        SELECT EXISTS (
                            SELECT FROM pg_tables
                            WHERE LOWER(tablename) = LOWER('event_locations')
                        )
                        AS table_exists
                        """
                    )
                    table_result = cursor.fetchone()
                    table_exists = table_result['table_exists'] if table_result else False
                    logger.debug(f"[DEBUG] event_locations table exists: {table_exists}")

                    if table_exists:
                        cursor.execute(
                            "SELECT location_name FROM event_locations WHERE event_id = %s",
                            (event_id,)
                        )
                        location_rows = cursor.fetchall()
                        logger.debug(f"[DEBUG] Raw location rows for event {event_id}: {location_rows}")
                        locations = [row['location_name'] for row in location_rows]
                        logger.debug(f"[DEBUG] Found locations for event {event_id}: {locations}")
                except Exception as e:
                    logger.error(f"[DEBUG] Error getting characters/locations: {str(e)}")
                    logger.error(f"[DEBUG] Exception type: {type(e)}")
                    import traceback
                    logger.error(f"[DEBUG] Traceback: {traceback.format_exc()}")

                # Create the event dictionary
                result = {
                    "id": updated_event['event_id'],
                    "book_id": updated_event['book_id'],
                    "chapter_id": updated_event['chapter_id'],
                    "title": updated_event['title'],
                    "description": updated_event['description'],
                    "sequence_number": updated_event['sequence_number'],
                    "is_in_bank": updated_event['is_in_bank'],
                    "x": updated_event['x'],
                    "y": updated_event['y'],
                    "width": updated_event['width'],
                    "height": updated_event['height'],
                    "type": updated_event['event_type'],  # Include event_type
                    "characters": characters,  # Include characters
                    "locations": locations  # Include locations
                }

                # Handle datetime fields separately
                if 'created_at' in updated_event and updated_event['created_at']:
                    result['created_at'] = updated_event['created_at'].isoformat()
                else:
                    result['created_at'] = None

                if 'updated_at' in updated_event and updated_event['updated_at']:
                    result['updated_at'] = updated_event['updated_at'].isoformat()
                else:
                    result['updated_at'] = None

                logger.debug(f"[DEBUG] Processed updated event: {result['id']}")
            except Exception as event_error:
                logger.error(f"[DEBUG] Error processing updated event: {repr(event_error)}, event data: {updated_event}")
                logger.error(f"[DEBUG] Error type: {type(event_error)}")
                import traceback
                logger.error(f"[DEBUG] Traceback: {traceback.format_exc()}")
                raise HTTPException(status_code=500, detail=f"Error processing updated event: {repr(event_error)}")

            return result
    except Exception as e:
        logger.error(f"Error assigning plot event to chapter: {str(e)}")
        if conn:
            conn.rollback()
        raise HTTPException(status_code=500, detail=f"Error assigning plot event to chapter: {str(e)}")
    finally:
        if conn:
            conn.close()

@router.get("/{book_id}/chapters/{chapter_id}/events")
def get_chapter_events(
    book_id: str,
    chapter_id: str,
    user_id: str = Depends(get_current_user)
):
    """Gets all plot events for a chapter."""
    logger.debug(f"Getting plot events for chapter {chapter_id} in book {book_id}")

    try:
        conn = get_db_connection()
        with conn.cursor() as cursor:
            # Verify the book exists and belongs to the user
            cursor.execute(
                "SELECT book_id FROM books WHERE book_id = %s AND user_id = %s",
                (book_id, user_id)
            )
            book = cursor.fetchone()

            if not book:
                raise HTTPException(status_code=404, detail="Book not found or not authorized")

            # Verify the chapter exists and belongs to the book
            cursor.execute(
                "SELECT chapter_id FROM chapters WHERE chapter_id = %s AND book_id = %s",
                (chapter_id, book_id)
            )
            chapter = cursor.fetchone()

            if not chapter:
                raise HTTPException(status_code=404, detail="Chapter not found or not authorized")

            # Get all plot events for the chapter
            cursor.execute(
                "SELECT event_id, book_id, chapter_id, title, description, sequence_number, is_in_bank, "
                "x, y, width, height, created_at, updated_at "
                "FROM plot_events WHERE book_id = %s AND chapter_id = %s",
                (book_id, chapter_id)
            )
            events = cursor.fetchall()

            # Convert to list of dictionaries
            result = []
            for event in events:
                try:
                    result.append({
                        "id": event['event_id'],
                        "book_id": event['book_id'],
                        "chapter_id": event['chapter_id'],
                        "title": event['title'],
                        "description": event['description'],
                        "sequence_number": event['sequence_number'],
                        "is_in_bank": event['is_in_bank'],
                        "x": event['x'],
                        "y": event['y'],
                        "width": event['width'],
                        "height": event['height'],
                        "created_at": event['created_at'].isoformat() if event['created_at'] else None,
                        "updated_at": event['updated_at'].isoformat() if event['updated_at'] else None
                    })
                    logger.debug(f"[DEBUG] Processed chapter event: {event['event_id']}")
                except Exception as event_error:
                    logger.error(f"[DEBUG] Error processing chapter event: {str(event_error)}, event data: {event}")

            return result
    except Exception as e:
        logger.error(f"Error getting chapter events: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting chapter events: {str(e)}")
    finally:
        if conn:
            conn.close()

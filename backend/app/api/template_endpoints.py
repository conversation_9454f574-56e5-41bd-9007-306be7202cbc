"""
API endpoints for template management.
"""
from fastapi import API<PERSON><PERSON><PERSON>, Depends, HTTPException, Query, Request, Header
from pydantic import BaseModel, <PERSON>
from typing import List, Dict, Optional, Any, Union
import logging
import asyncio
import sys
import os
from app.auth import get_current_user
from app.db.template_repository import TemplateRepository
from app.services.template_validation_service import TemplateValidationService

# Set up logger
logger = logging.getLogger(__name__)

# Import Qdrant integration
try:
    # Use relative import path
    sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
    from qdrant_integration.sync_service import SyncService
    from qdrant_integration.embedding_service import get_template_embedding
    QDRANT_AVAILABLE = True
    logger.info("Qdrant integration available. Template embeddings will be synchronized.")
except ImportError as e:
    logger.warning(f"Qdrant integration not available. Template embeddings will not be synchronized. Error: {str(e)}")
    QDRANT_AVAILABLE = False

router = APIRouter(prefix="/api/templates", tags=["templates"])

# Initialize repository and services
template_repo = TemplateRepository()
template_validation_service = TemplateValidationService()

# Initialize sync service if available
sync_service = None
if QDRANT_AVAILABLE:
    # Database configuration for sync service
    db_config = {
        "host": "localhost",
        "port": 5432,
        "user": "project1_user",
        "password": "rHEWX*.V.Adaline..",
        "database": "bookapp1"
    }
    sync_service = SyncService(db_config)

# Models
class FieldDefinition(BaseModel):
    id: str
    label: str
    type: str
    required: bool = False
    description: Optional[str] = None
    options: Optional[List[str]] = None
    default_value: Optional[Any] = None

class RelationshipDefinition(BaseModel):
    target_template_id: str
    relationship_type_id: str
    description: Optional[str] = None

    # For backward compatibility with existing data
    target_template: Optional[str] = None
    relationship_type: Optional[str] = None
    inverse_relationship: Optional[str] = None
    label: Optional[str] = None

class TemplateCreate(BaseModel):
    template_name: str
    display_name: str
    category_id: str
    parent_template_id: Optional[str] = None
    description: Optional[str] = None
    field_definitions: Dict[str, List[FieldDefinition]]
    relationship_definitions: Optional[Dict[str, List[RelationshipDefinition]]] = None
    icon: Optional[str] = None
    source_template_id: Optional[str] = None

class TemplateUpdate(BaseModel):
    template_name: Optional[str] = None
    display_name: Optional[str] = None
    category_id: Optional[str] = None
    parent_template_id: Optional[str] = None
    description: Optional[str] = None
    field_definitions: Optional[Dict[str, List[FieldDefinition]]] = None
    relationship_definitions: Optional[Dict[str, List[RelationshipDefinition]]] = None
    icon: Optional[str] = None

class TemplateResponse(BaseModel):
    template_id: str
    template_name: str
    display_name: str
    category_id: str
    category_name: Optional[str] = None
    category_aspect_type: Optional[str] = None
    parent_template_id: Optional[str] = None
    parent_template_name: Optional[str] = None
    description: Optional[str] = None
    field_definitions: Dict[str, List[FieldDefinition]]
    relationship_definitions: Optional[Dict[str, List[RelationshipDefinition]]] = None
    icon: Optional[str] = None
    version: str
    source_template_id: Optional[str] = None
    creator_id: Optional[str] = None
    is_system_template: bool
    created_at: str
    updated_at: str

class TemplateHierarchyItem(BaseModel):
    template_id: str
    display_name: str
    description: Optional[str] = None
    version: str
    is_system_template: bool
    children: List['TemplateHierarchyItem'] = []

class CategoryHierarchy(BaseModel):
    category_id: str
    category_name: str
    category_aspect_type: str
    templates: List[TemplateHierarchyItem]

class TemplateUsageStats(BaseModel):
    id: int
    template_id: str
    template_version: str
    book_id: str
    user_id: str
    usage_count: int
    last_used_at: str
    created_at: str
    updated_at: str
    template_name: Optional[str] = None
    book_title: Optional[str] = None

class TemplateUsageTrend(BaseModel):
    time_period: str
    total_usage: int
    unique_users: int
    unique_books: int

class PopularTemplate(BaseModel):
    template_id: str
    display_name: str
    category_id: str
    category_name: Optional[str] = None
    category_aspect_type: Optional[str] = None
    description: Optional[str] = None
    version: str
    is_system_template: bool
    total_usage_count: int
    user_count: int

class TemplateExport(BaseModel):
    template_name: str
    display_name: str
    category_id: str
    description: Optional[str] = None
    field_definitions: Dict[str, List[FieldDefinition]]
    relationship_definitions: Optional[Dict[str, List[RelationshipDefinition]]] = None
    icon: Optional[str] = None
    version: str
    export_date: str
    metadata: Dict[str, Any]

class TemplateImport(BaseModel):
    template_data: Dict[str, Any]

class TemplateValidationRequest(BaseModel):
    template_data: Dict[str, Any]

class TemplateValidationResponse(BaseModel):
    is_valid: bool
    errors: List[str]

class TemplateSampleDataResponse(BaseModel):
    sample_data: Dict[str, Any]

class TemplateVersion(BaseModel):
    version_id: str
    template_id: str
    version: str
    created_at: str
    created_by: str

class TemplateVersionComparisonResult(BaseModel):
    version1: Dict[str, Any]
    version2: Dict[str, Any]
    differences: List[Dict[str, Any]]

# Endpoints
@router.get("", response_model=List[TemplateResponse])
def get_templates(
    category_id: Optional[str] = None,
    creator_id: Optional[str] = None,
    is_system_template: Optional[bool] = None,
    user_id: str = Depends(get_current_user)
):
    """
    Fetches templates, optionally filtered by category, creator, or system status.
    """
    try:
        templates = template_repo.get_templates(
            category_id=category_id,
            creator_id=creator_id,
            is_system_template=is_system_template
        )
        return templates
    except Exception as e:
        logger.error(f"Error fetching templates: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch templates: {str(e)}")

@router.get("/hierarchy", response_model=Dict[str, CategoryHierarchy])
def get_template_hierarchy(user_id: str = Depends(get_current_user)):
    """
    Fetches the template hierarchy organized by category.
    """
    try:
        hierarchy = template_repo.get_template_hierarchy()
        return hierarchy
    except Exception as e:
        logger.error(f"Error fetching template hierarchy: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch template hierarchy: {str(e)}")

@router.get("/popular", response_model=List[PopularTemplate])
def get_popular_templates(
    limit: int = Query(10, ge=1, le=100),
    user_id: str = Depends(get_current_user)
):
    """
    Fetches the most popular templates based on usage count.
    """
    try:
        templates = template_repo.get_popular_templates(limit=limit)
        return templates
    except Exception as e:
        logger.error(f"Error fetching popular templates: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch popular templates: {str(e)}")

@router.get("/usage", response_model=List[TemplateUsageStats])
def get_template_usage_stats(
    template_id: Optional[str] = None,
    book_id: Optional[str] = None,
    user_id: str = Depends(get_current_user)
):
    """
    Fetches template usage statistics.
    """
    try:
        stats = template_repo.get_template_usage_stats(
            template_id=template_id,
            book_id=book_id,
            user_id=user_id
        )
        return stats
    except Exception as e:
        logger.error(f"Error fetching template usage stats: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch template usage stats: {str(e)}")

@router.get("/usage/trends", response_model=List[TemplateUsageTrend])
def get_template_usage_trends(
    template_id: Optional[str] = None,
    period: str = Query("monthly", regex="^(daily|weekly|monthly)$"),
    limit: int = Query(12, ge=1, le=100),
    user_id: str = Depends(get_current_user)
):
    """
    Fetches template usage trends over time.

    - period: Time period for grouping ('daily', 'weekly', 'monthly')
    - limit: Maximum number of time periods to return
    """
    try:
        trends = template_repo.get_template_usage_trends(
            template_id=template_id,
            period=period,
            limit=limit
        )
        return trends
    except Exception as e:
        logger.error(f"Error fetching template usage trends: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch template usage trends: {str(e)}")

@router.get("/{template_id}", response_model=TemplateResponse)
def get_template(
    template_id: str,
    version: Optional[str] = None,
    user_id: str = Depends(get_current_user)
):
    """
    Fetches a specific template by ID and optionally version.
    """
    try:
        template = template_repo.get_template(template_id=template_id, version=version)
        return template
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Error fetching template: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch template: {str(e)}")

@router.post("", response_model=TemplateResponse)
async def create_template(
    template: TemplateCreate,
    user_id: str = Depends(get_current_user)
):
    """
    Creates a new template.
    """
    try:
        # Add the creator ID to the template data
        template_data = template.dict()
        template_data['creator_id'] = user_id

        result = template_repo.create_template(template_data)

        # Synchronize with Qdrant if available
        if QDRANT_AVAILABLE and sync_service:
            # Run synchronization in the background
            asyncio.create_task(sync_service.sync_template(result["template_id"]))
            logger.info(f"Template {result['template_id']} scheduled for synchronization with Qdrant")

        return result
    except Exception as e:
        logger.error(f"Error creating template: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to create template: {str(e)}")

@router.put("/{template_id}", response_model=TemplateResponse)
async def update_template(
    template_id: str,
    template: TemplateUpdate,
    user_id: str = Depends(get_current_user)
):
    """
    Updates an existing template.
    """
    try:
        # Add the creator ID to the template data
        template_data = template.dict(exclude_unset=True)
        template_data['creator_id'] = user_id

        result = template_repo.update_template(template_id, template_data)

        # Synchronize with Qdrant if available
        if QDRANT_AVAILABLE and sync_service:
            # Run synchronization in the background
            asyncio.create_task(sync_service.sync_template(template_id))
            logger.info(f"Template {template_id} scheduled for synchronization with Qdrant")

        return result
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Error updating template: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to update template: {str(e)}")

@router.delete("/{template_id}")
async def delete_template(
    template_id: str,
    user_id: str = Depends(get_current_user)
):
    """
    Deletes a template if it's not a system template.
    """
    try:
        template_repo.delete_template(template_id)

        # Delete from Qdrant if available
        if QDRANT_AVAILABLE and sync_service:
            # Run deletion in the background
            asyncio.create_task(sync_service.delete_template(template_id))
            logger.info(f"Template {template_id} scheduled for deletion from Qdrant")

        return {"status": "success", "message": f"Template {template_id} deleted"}
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error deleting template: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to delete template: {str(e)}")

@router.post("/{template_id}/usage")
def increment_template_usage(
    template_id: str,
    book_id: str,
    template_version: str = "1.0",
    user_id: str = Depends(get_current_user)
):
    """
    Increments the usage count for a template.
    """
    try:
        result = template_repo.increment_template_usage(
            template_id=template_id,
            template_version=template_version,
            book_id=book_id,
            user_id=user_id
        )
        return result
    except Exception as e:
        logger.error(f"Error incrementing template usage: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to increment template usage: {str(e)}")

@router.get("/{template_id}/children", response_model=List[TemplateResponse])
def get_template_children(
    template_id: str,
    user_id: str = Depends(get_current_user)
):
    """
    Fetches all child templates for a specific template.
    """
    try:
        templates = template_repo.get_templates()
        children = [t for t in templates if t.get('parent_template_id') == template_id]
        return children
    except Exception as e:
        logger.error(f"Error fetching template children: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch template children: {str(e)}")

@router.get("/{template_id}/export", response_model=TemplateExport)
def export_template(
    template_id: str,
    user_id: str = Depends(get_current_user)
):
    """
    Exports a template in a format suitable for sharing or backup.
    """
    try:
        export_data = template_repo.export_template(template_id)
        return export_data
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Error exporting template: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to export template: {str(e)}")

@router.post("/import", response_model=TemplateResponse)
def import_template(
    template_import: TemplateImport,
    user_id: str = Depends(get_current_user)
):
    """
    Imports a template from an export format.
    """
    try:
        imported_template = template_repo.import_template(template_import.template_data, user_id)
        return imported_template
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error importing template: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to import template: {str(e)}")

@router.post("/validate", response_model=TemplateValidationResponse)
def validate_template(
    validation_request: TemplateValidationRequest,
    user_id: str = Depends(get_current_user)
):
    """
    Validates a template against the defined rules.
    """
    try:
        is_valid, errors = template_validation_service.validate_template(validation_request.template_data)
        return {
            "is_valid": is_valid,
            "errors": errors
        }
    except Exception as e:
        logger.error(f"Error validating template: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to validate template: {str(e)}")

@router.post("/generate-sample", response_model=TemplateSampleDataResponse)
def generate_sample_data(
    validation_request: TemplateValidationRequest,
    user_id: str = Depends(get_current_user)
):
    """
    Generates sample data for a template.
    """
    try:
        # First validate the template
        is_valid, errors = template_validation_service.validate_template(validation_request.template_data)
        if not is_valid:
            raise ValueError(f"Cannot generate sample data for invalid template: {', '.join(errors)}")

        sample_data = template_validation_service.generate_sample_data(validation_request.template_data)
        return {
            "sample_data": sample_data
        }
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error generating sample data: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to generate sample data: {str(e)}")

@router.get("/{template_id}/versions", response_model=List[TemplateVersion])
def get_template_versions(
    template_id: str,
    user_id: str = Depends(get_current_user)
):
    """
    Fetches the version history of a template.
    """
    try:
        versions = template_repo.get_template_versions(template_id)
        return versions
    except Exception as e:
        logger.error(f"Error fetching template versions: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch template versions: {str(e)}")

@router.get("/compare-versions", response_model=TemplateVersionComparisonResult)
def compare_template_versions(
    version1: str,
    version2: str,
    user_id: str = Depends(get_current_user)
):
    """
    Compares two versions of a template.
    """
    try:
        comparison = template_repo.compare_template_versions(version1, version2)
        return comparison
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Error comparing template versions: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to compare template versions: {str(e)}")

@router.post("/versions/{version_id}/restore", response_model=TemplateResponse)
def restore_template_version(
    version_id: str,
    user_id: str = Depends(get_current_user)
):
    """
    Restores a template to a previous version.
    """
    try:
        restored_template = template_repo.restore_template_version(version_id, user_id)
        return restored_template
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Error restoring template version: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to restore template version: {str(e)}")
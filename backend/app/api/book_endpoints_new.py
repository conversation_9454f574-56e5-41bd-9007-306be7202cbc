# backend/app/api/book_endpoints_new.py
"""
Book endpoints that don't use the book_memory table.
This is a new version that uses dedicated tables for all data.
"""

from fastapi import APIRouter, Depends, HTTPException
import logging
from pydantic import BaseModel
from app.auth import get_current_user
from app.db.book_repository_new import BookRepository
from uuid import uuid4

logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

router = APIRouter(prefix="/books", tags=["books"])
book_repo = BookRepository()

class BookCreate(BaseModel):
    """Schema for creating a new book."""
    title: str  # Only title is required
    author: str = ""
    genre: str = ""
    description: str = ""

class BookUpdate(BaseModel):
    """Schema for updating a book."""
    title: str = None
    author: str = None
    genre: str = None
    description: str = None

@router.get("")
def get_books(user_id: str = Depends(get_current_user)):
    """Fetches all books for the authenticated user."""
    try:
        books = book_repo.get_user_books(user_id)
        return books
    except Exception as e:
        logger.error(f"Error fetching books: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error fetching books: {str(e)}")

@router.post("")
def create_book(book: BookCreate, user_id: str = Depends(get_current_user)):
    """Creates a new book."""
    try:
        # Generate a unique book_id
        book_id = str(uuid4())
        
        # Extract book data
        title = book.title
        author = book.author
        genre = book.genre
        description = book.description
        
        logger.debug(f"Creating book with title: {title}, author: {author}, genre: {genre}")
        
        # Use the BookRepository.create method to create the book and initialize tables
        book_repo.create(book_id, user_id, title, author, genre, description)
        
        # Get the book details
        book_details = book_repo.get_book_details(book_id)
        
        logger.debug(f"Book created successfully with book_id: {book_id}")
        return book_details
    except Exception as e:
        logger.error(f"Database error while creating book: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error creating book: {str(e)}")

@router.options("")
def options_books():
    """Handles OPTIONS request for CORS preflight."""
    return {}

@router.get("/{book_id}")
def get_book(book_id: str, user_id: str = Depends(get_current_user)):
    """Fetches a specific book's details."""
    try:
        # Get the book details
        book_details = book_repo.get_book_details(book_id)
        
        # Check if the book belongs to the user
        if book_details['user_id'] != user_id:
            logger.error(f"User {user_id} does not own book {book_id}")
            raise HTTPException(status_code=403, detail="You do not have permission to access this book")
        
        return book_details
    except HTTPException as e:
        # Re-raise HTTP exceptions
        raise e
    except Exception as e:
        logger.error(f"Error fetching book {book_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error fetching book: {str(e)}")

@router.put("/{book_id}")
def update_book(book_id: str, book_data: BookUpdate, user_id: str = Depends(get_current_user)):
    """Updates a book's details."""
    logger.info(f"Updating book with book_id: {book_id}, user_id: {user_id}")
    logger.info(f"Book data: {book_data}")
    
    try:
        # Update the book
        updated_book = book_repo.update(book_id, user_id, book_data.dict(exclude_unset=True))
        return updated_book
    except HTTPException as e:
        # Re-raise HTTP exceptions
        raise e
    except Exception as e:
        logger.error(f"Error updating book {book_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error updating book: {str(e)}")

@router.delete("/{book_id}")
def delete_book(book_id: str, user_id: str = Depends(get_current_user)):
    """Deletes a book."""
    try:
        # Delete the book
        book_repo.delete(book_id, user_id)
        return {"message": f"Book {book_id} deleted successfully"}
    except HTTPException as e:
        # Re-raise HTTP exceptions
        raise e
    except Exception as e:
        logger.error(f"Error deleting book {book_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error deleting book: {str(e)}")

@router.get("/{book_id}/metadata")
def get_book_metadata(book_id: str, user_id: str = Depends(get_current_user)):
    """Fetches metadata for a book."""
    try:
        # Get the book metadata
        metadata = book_repo.get_book_metadata(book_id)
        return metadata
    except HTTPException as e:
        # Re-raise HTTP exceptions
        raise e
    except Exception as e:
        logger.error(f"Error fetching book metadata for {book_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error fetching book metadata: {str(e)}")

@router.post("/{book_id}/carryover")
def carryover_book(
    book_id: str,
    new_book: BookCreate,
    user_id: str = Depends(get_current_user)
):
    """Creates a new book and carries over data from an existing book."""
    try:
        # This would need to be implemented to copy data from dedicated tables
        # instead of using the book_memory table
        raise HTTPException(status_code=501, detail="Not implemented yet")
    except HTTPException as e:
        # Re-raise HTTP exceptions
        raise e
    except Exception as e:
        logger.error(f"Error carrying over book {book_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error carrying over book: {str(e)}")

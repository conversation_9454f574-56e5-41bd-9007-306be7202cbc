"""
Semantic Search Endpoints

This module provides API endpoints for semantic search using vector embeddings
stored in the Qdrant vector database.
"""

from fastapi import APIRouter, Depends, HTTPException, Query
from pydantic import BaseModel, Field
from typing import List, Dict, Optional, Any, Union
import logging
import uuid
import sys
import os

from app.auth import get_current_user
from app.db.template_repository import TemplateRepository

# Set up path for Qdrant integration
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# Import Qdrant integration
try:
    from qdrant_integration.embedding_service import (
        embedding_service,
        get_content_embedding_for_template_suggestion
    )
    from qdrant_integration.query_utils import secure_search
    QDRANT_AVAILABLE = True
except ImportError as e:
    QDRANT_AVAILABLE = False

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/search", tags=["search"])

# Initialize repositories
template_repo = TemplateRepository()

# Log Qdrant availability
if QDRANT_AVAILABLE:
    logger.info("Qdrant integration available. Semantic search endpoints are active.")
else:
    logger.warning("Qdrant integration not available. Semantic search endpoints will return empty results.")

# Models
class SearchQuery(BaseModel):
    query: str
    limit: int = 10
    filters: Optional[Dict[str, Any]] = None

class TemplateSearchResult(BaseModel):
    template_id: str
    template_name: str
    display_name: str
    category_id: str
    category_name: Optional[str] = None
    description: Optional[str] = None
    score: float

class ContentSearchResult(BaseModel):
    id: str
    type: str  # "template", "element", "character", etc.
    name: str
    description: Optional[str] = None
    category: Optional[str] = None
    score: float
    metadata: Optional[Dict[str, Any]] = None

class TemplateSuggestion(BaseModel):
    template_id: str
    template_name: str
    display_name: str
    category_id: str
    category_name: Optional[str] = None
    description: Optional[str] = None
    score: float
    relevance_explanation: Optional[str] = None

# Endpoints
@router.post("/templates", response_model=List[TemplateSearchResult])
def search_templates(
    query: SearchQuery,
    user_id: str = Depends(get_current_user)
):
    """
    Search for templates using semantic similarity
    """
    # Check if Qdrant is available
    if not QDRANT_AVAILABLE:
        logger.warning("Qdrant integration not available. Returning empty results.")
        return []

    try:
        # Generate embedding for the query
        query_embedding = embedding_service.get_instructor_embedding(
            query.query,
            "Represent this query for finding relevant templates"
        )

        if not query_embedding:
            raise HTTPException(status_code=500, detail="Failed to generate query embedding")

        # Search Qdrant
        collection_name = "templates"
        results = secure_search(
            user_id=user_id,
            collection_name=collection_name,
            vector=query_embedding,
            limit=query.limit,
            filter_params=query.filters
        )

        # Format results
        search_results = []
        for result in results:
            payload = result.get("payload", {})
            search_results.append(
                TemplateSearchResult(
                    template_id=payload.get("template_id", ""),
                    template_name=payload.get("template_name", ""),
                    display_name=payload.get("display_name", ""),
                    category_id=payload.get("category_id", ""),
                    category_name=payload.get("category_name", ""),
                    description=payload.get("description", ""),
                    score=result.get("score", 0.0)
                )
            )

        return search_results
    except Exception as e:
        logger.error(f"Error searching templates: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error searching templates: {str(e)}")

@router.post("/hybrid", response_model=List[ContentSearchResult])
def hybrid_search(
    query: SearchQuery,
    book_id: str,
    content_types: List[str] = Query(["templates", "elements", "characters"]),
    user_id: str = Depends(get_current_user)
):
    """
    Perform a hybrid search across multiple content types
    """
    # Check if Qdrant is available
    if not QDRANT_AVAILABLE:
        logger.warning("Qdrant integration not available. Returning empty results.")
        return []

    try:
        # Generate embedding for the query
        query_embedding = embedding_service.get_instructor_embedding(
            query.query,
            "Represent this query for finding relevant content"
        )

        if not query_embedding:
            raise HTTPException(status_code=500, detail="Failed to generate query embedding")

        all_results = []

        # Search each content type
        for content_type in content_types:
            if content_type == "templates":
                collection_name = "templates"
            elif content_type == "elements":
                collection_name = f"user_{user_id}_elements"
            elif content_type == "characters":
                collection_name = f"user_{user_id}_characters"
            else:
                continue

            # Add book_id filter for elements and characters
            filter_params = query.filters or {}
            if content_type in ["elements", "characters"]:
                if "must" not in filter_params:
                    filter_params["must"] = []
                filter_params["must"].append({
                    "key": "book_id",
                    "match": {"value": book_id}
                })

            # Search Qdrant
            try:
                results = secure_search(
                    user_id=user_id,
                    collection_name=collection_name,
                    vector=query_embedding,
                    limit=query.limit,
                    filter_params=filter_params
                )

                # Format and add results
                for result in results:
                    payload = result.get("payload", {})

                    # Create a generic result object
                    content_result = ContentSearchResult(
                        id=str(result.get("id", "")),
                        type=content_type[:-1],  # Remove 's' from type
                        name=payload.get("name", payload.get("template_name", payload.get("display_name", ""))),
                        description=payload.get("description", ""),
                        category=payload.get("category", payload.get("category_name", "")),
                        score=result.get("score", 0.0),
                        metadata=payload
                    )

                    all_results.append(content_result)
            except Exception as e:
                logger.warning(f"Error searching {content_type}: {str(e)}")
                continue

        # Sort by score
        all_results.sort(key=lambda x: x.score, reverse=True)

        # Limit to requested number
        return all_results[:query.limit]
    except Exception as e:
        logger.error(f"Error performing hybrid search: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error performing hybrid search: {str(e)}")

@router.post("/suggest-templates", response_model=List[TemplateSuggestion])
def suggest_templates(
    content: str,
    limit: int = 5,
    user_id: str = Depends(get_current_user)
):
    """
    Suggest templates based on content analysis
    """
    # Check if Qdrant is available
    if not QDRANT_AVAILABLE:
        logger.warning("Qdrant integration not available. Returning empty results.")
        return []

    try:
        # Generate embedding for the content
        content_embedding = get_content_embedding_for_template_suggestion(content)

        if not content_embedding:
            raise HTTPException(status_code=500, detail="Failed to generate content embedding")

        # Search Qdrant
        collection_name = "templates"
        results = secure_search(
            user_id=user_id,
            collection_name=collection_name,
            vector=content_embedding,
            limit=limit
        )

        # Format results
        suggestions = []
        for result in results:
            payload = result.get("payload", {})

            # Generate a simple explanation of why this template is relevant
            relevance = "This template appears to be relevant to your content."
            if payload.get("description"):
                relevance = f"This template is relevant because it deals with {payload['description']}"

            suggestions.append(
                TemplateSuggestion(
                    template_id=payload.get("template_id", ""),
                    template_name=payload.get("template_name", ""),
                    display_name=payload.get("display_name", ""),
                    category_id=payload.get("category_id", ""),
                    category_name=payload.get("category_name", ""),
                    description=payload.get("description", ""),
                    score=result.get("score", 0.0),
                    relevance_explanation=relevance
                )
            )

        return suggestions
    except Exception as e:
        logger.error(f"Error suggesting templates: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error suggesting templates: {str(e)}")

"""
API endpoints for relationship type management.
"""
from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel
from typing import List, Optional
import logging
from app.auth import get_current_user
from app.db.base import get_db_connection

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/relationship-types", tags=["relationship-types"])

# Models
class RelationshipTypeCreate(BaseModel):
    relationship_type_id: str
    name: str
    inverse_relationship_type_id: Optional[str] = None
    description: Optional[str] = None

class RelationshipTypeUpdate(BaseModel):
    name: Optional[str] = None
    inverse_relationship_type_id: Optional[str] = None
    description: Optional[str] = None

class RelationshipTypeResponse(BaseModel):
    relationship_type_id: str
    name: str
    inverse_relationship_type_id: Optional[str] = None
    inverse_name: Optional[str] = None
    description: Optional[str] = None
    created_at: str
    updated_at: str

# Endpoints
@router.get("", response_model=List[RelationshipTypeResponse])
def get_relationship_types(user_id: str = Depends(get_current_user)):
    """
    Fetches all relationship types.
    """
    conn = get_db_connection()
    try:
        with conn.cursor() as cursor:
            cursor.execute(
                """
                SELECT
                    r.relationship_type_id,
                    r.name,
                    r.inverse_relationship_type_id,
                    i.name as inverse_name,
                    r.description,
                    r.created_at,
                    r.updated_at
                FROM relationship_types r
                LEFT JOIN relationship_types i ON r.inverse_relationship_type_id = i.relationship_type_id
                ORDER BY r.name
                """
            )
            relationship_types = cursor.fetchall()

            # Convert datetime objects to strings
            processed_types = []
            for rt in relationship_types:
                rt_dict = dict(rt)

                # Convert datetime objects to strings
                if 'created_at' in rt_dict and rt_dict['created_at']:
                    rt_dict['created_at'] = rt_dict['created_at'].isoformat()
                if 'updated_at' in rt_dict and rt_dict['updated_at']:
                    rt_dict['updated_at'] = rt_dict['updated_at'].isoformat()

                processed_types.append(rt_dict)

            return processed_types
    except Exception as e:
        logger.error(f"Error fetching relationship types: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch relationship types: {str(e)}")
    finally:
        conn.close()

@router.get("/{relationship_type_id}", response_model=RelationshipTypeResponse)
def get_relationship_type(relationship_type_id: str, user_id: str = Depends(get_current_user)):
    """
    Fetches a specific relationship type by ID.
    """
    conn = get_db_connection()
    try:
        with conn.cursor() as cursor:
            cursor.execute(
                """
                SELECT
                    r.relationship_type_id,
                    r.name,
                    r.inverse_relationship_type_id,
                    i.name as inverse_name,
                    r.description,
                    r.created_at,
                    r.updated_at
                FROM relationship_types r
                LEFT JOIN relationship_types i ON r.inverse_relationship_type_id = i.relationship_type_id
                WHERE r.relationship_type_id = %s
                """,
                (relationship_type_id,)
            )
            relationship_type = cursor.fetchone()

            if not relationship_type:
                raise HTTPException(status_code=404, detail=f"Relationship type {relationship_type_id} not found")

            rt_dict = dict(relationship_type)

            # Convert datetime objects to strings
            if 'created_at' in rt_dict and rt_dict['created_at']:
                rt_dict['created_at'] = rt_dict['created_at'].isoformat()
            if 'updated_at' in rt_dict and rt_dict['updated_at']:
                rt_dict['updated_at'] = rt_dict['updated_at'].isoformat()

            return rt_dict
    except Exception as e:
        logger.error(f"Error fetching relationship type: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch relationship type: {str(e)}")
    finally:
        conn.close()

@router.post("", response_model=RelationshipTypeResponse)
def create_relationship_type(relationship_type: RelationshipTypeCreate, user_id: str = Depends(get_current_user)):
    """
    Creates a new relationship type.
    """
    conn = get_db_connection()
    try:
        with conn.cursor() as cursor:
            # Check if the relationship type ID already exists
            cursor.execute(
                "SELECT relationship_type_id FROM relationship_types WHERE relationship_type_id = %s",
                (relationship_type.relationship_type_id,)
            )
            if cursor.fetchone():
                raise HTTPException(status_code=400, detail=f"Relationship type ID {relationship_type.relationship_type_id} already exists")

            # Insert the new relationship type
            cursor.execute(
                """
                INSERT INTO relationship_types (
                    relationship_type_id,
                    name,
                    inverse_relationship_type_id,
                    description
                )
                VALUES (%s, %s, %s, %s)
                RETURNING
                    relationship_type_id,
                    name,
                    inverse_relationship_type_id,
                    description,
                    created_at,
                    updated_at
                """,
                (
                    relationship_type.relationship_type_id,
                    relationship_type.name,
                    relationship_type.inverse_relationship_type_id,
                    relationship_type.description
                )
            )
            result = cursor.fetchone()

            # If an inverse relationship type ID was provided, update it to point back to this one
            if relationship_type.inverse_relationship_type_id:
                cursor.execute(
                    """
                    UPDATE relationship_types
                    SET inverse_relationship_type_id = %s
                    WHERE relationship_type_id = %s
                    """,
                    (relationship_type.relationship_type_id, relationship_type.inverse_relationship_type_id)
                )

            conn.commit()

            # Get the inverse name if applicable
            inverse_name = None
            if relationship_type.inverse_relationship_type_id:
                cursor.execute(
                    "SELECT name FROM relationship_types WHERE relationship_type_id = %s",
                    (relationship_type.inverse_relationship_type_id,)
                )
                inverse = cursor.fetchone()
                if inverse:
                    inverse_name = inverse['name']

            response = dict(result)
            response['inverse_name'] = inverse_name

            # Convert datetime objects to strings
            if 'created_at' in response and response['created_at']:
                response['created_at'] = response['created_at'].isoformat()
            if 'updated_at' in response and response['updated_at']:
                response['updated_at'] = response['updated_at'].isoformat()

            return response
    except Exception as e:
        conn.rollback()
        logger.error(f"Error creating relationship type: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to create relationship type: {str(e)}")
    finally:
        conn.close()

@router.put("/{relationship_type_id}", response_model=RelationshipTypeResponse)
def update_relationship_type(
    relationship_type_id: str,
    relationship_type: RelationshipTypeUpdate,
    user_id: str = Depends(get_current_user)
):
    """
    Updates an existing relationship type.
    """
    conn = get_db_connection()
    try:
        with conn.cursor() as cursor:
            # Check if the relationship type exists
            cursor.execute(
                "SELECT relationship_type_id FROM relationship_types WHERE relationship_type_id = %s",
                (relationship_type_id,)
            )
            if not cursor.fetchone():
                raise HTTPException(status_code=404, detail=f"Relationship type {relationship_type_id} not found")

            # Update the relationship type
            update_fields = []
            params = []

            if relationship_type.name is not None:
                update_fields.append("name = %s")
                params.append(relationship_type.name)

            if relationship_type.description is not None:
                update_fields.append("description = %s")
                params.append(relationship_type.description)

            if relationship_type.inverse_relationship_type_id is not None:
                update_fields.append("inverse_relationship_type_id = %s")
                params.append(relationship_type.inverse_relationship_type_id)

            if not update_fields:
                raise HTTPException(status_code=400, detail="No fields to update")

            update_fields.append("updated_at = CURRENT_TIMESTAMP")

            query = f"""
                UPDATE relationship_types
                SET {', '.join(update_fields)}
                WHERE relationship_type_id = %s
                RETURNING
                    relationship_type_id,
                    name,
                    inverse_relationship_type_id,
                    description,
                    created_at,
                    updated_at
            """

            params.append(relationship_type_id)

            cursor.execute(query, params)
            result = cursor.fetchone()

            # If an inverse relationship type ID was provided, update it to point back to this one
            if relationship_type.inverse_relationship_type_id:
                cursor.execute(
                    """
                    UPDATE relationship_types
                    SET inverse_relationship_type_id = %s
                    WHERE relationship_type_id = %s
                    """,
                    (relationship_type_id, relationship_type.inverse_relationship_type_id)
                )

            conn.commit()

            # Get the inverse name if applicable
            inverse_name = None
            if result['inverse_relationship_type_id']:
                cursor.execute(
                    "SELECT name FROM relationship_types WHERE relationship_type_id = %s",
                    (result['inverse_relationship_type_id'],)
                )
                inverse = cursor.fetchone()
                if inverse:
                    inverse_name = inverse['name']

            response = dict(result)
            response['inverse_name'] = inverse_name

            # Convert datetime objects to strings
            if 'created_at' in response and response['created_at']:
                response['created_at'] = response['created_at'].isoformat()
            if 'updated_at' in response and response['updated_at']:
                response['updated_at'] = response['updated_at'].isoformat()

            return response
    except Exception as e:
        conn.rollback()
        logger.error(f"Error updating relationship type: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to update relationship type: {str(e)}")
    finally:
        conn.close()

@router.delete("/{relationship_type_id}")
def delete_relationship_type(relationship_type_id: str, user_id: str = Depends(get_current_user)):
    """
    Deletes a relationship type.
    """
    conn = get_db_connection()
    try:
        with conn.cursor() as cursor:
            # Check if the relationship type exists
            cursor.execute(
                "SELECT relationship_type_id, inverse_relationship_type_id FROM relationship_types WHERE relationship_type_id = %s",
                (relationship_type_id,)
            )
            relationship_type = cursor.fetchone()

            if not relationship_type:
                raise HTTPException(status_code=404, detail=f"Relationship type {relationship_type_id} not found")

            # Check if this relationship type is used in any templates
            cursor.execute(
                """
                SELECT COUNT(*) as count
                FROM element_templates
                WHERE relationship_definitions::text LIKE %s
                """,
                (f'%{relationship_type_id}%',)
            )
            result = cursor.fetchone()

            if result['count'] > 0:
                raise HTTPException(
                    status_code=400,
                    detail=f"Cannot delete relationship type: it is used in {result['count']} templates"
                )

            # Check if this relationship type is used in any relationships
            cursor.execute(
                """
                SELECT COUNT(*) as count
                FROM world_element_relationships
                WHERE relationship_type = %s
                """,
                (relationship_type_id,)
            )
            result = cursor.fetchone()

            if result['count'] > 0:
                raise HTTPException(
                    status_code=400,
                    detail=f"Cannot delete relationship type: it is used in {result['count']} relationships"
                )

            # If this relationship type has an inverse, update the inverse to remove the reference
            if relationship_type['inverse_relationship_type_id']:
                cursor.execute(
                    """
                    UPDATE relationship_types
                    SET inverse_relationship_type_id = NULL
                    WHERE relationship_type_id = %s
                    """,
                    (relationship_type['inverse_relationship_type_id'],)
                )

            # Delete the relationship type
            cursor.execute(
                "DELETE FROM relationship_types WHERE relationship_type_id = %s",
                (relationship_type_id,)
            )

            conn.commit()
            return {"status": "success", "message": f"Relationship type {relationship_type_id} deleted"}
    except Exception as e:
        conn.rollback()
        logger.error(f"Error deleting relationship type: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to delete relationship type: {str(e)}")
    finally:
        conn.close()
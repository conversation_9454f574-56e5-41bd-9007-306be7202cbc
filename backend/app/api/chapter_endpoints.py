
# backend/app/api/chapter_endpoints.py
from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel
from typing import List, Optional
from app.auth import get_current_user
from app.db.base import get_db_connection
import logging
from uuid import uuid4

logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

router = APIRouter(prefix="/books", tags=["chapters"])

class ChapterContent(BaseModel):
    """Schema for updating chapter content."""
    content: str

# Define models for chapter reordering
class ChapterReorderItem(BaseModel):
    """Schema for a chapter in a reordering request."""
    id: str
    sequence_number: int

class ChapterReorderRequest(BaseModel):
    """Schema for a chapter reordering request."""
    chapters: List[ChapterReorderItem]

class ChapterCreate(BaseModel):
    """Schema for creating a new chapter."""
    title: str
    sequence_number: int = None

@router.get("/{book_id}/chapters/{chapter_id}/content")
def get_chapter_content(
    book_id: str,
    chapter_id: str,
    user_id: str = Depends(get_current_user)
):
    """Gets the content of a chapter with related events, characters, and locations."""
    logger.debug(f"[DEBUG Backend] Getting content for chapter {chapter_id} in book {book_id}")
    logger.debug(f"[DEBUG Backend] User ID: {user_id}")

    conn = None
    try:
        conn = get_db_connection()
        with conn.cursor() as cursor:
            # Verify the chapter exists and belongs to the book
            cursor.execute(
                "SELECT c.content FROM chapters c JOIN books b ON c.book_id = b.book_id "
                "WHERE c.chapter_id = %s AND c.book_id = %s AND b.user_id = %s",
                (chapter_id, book_id, user_id)
            )
            result = cursor.fetchone()

            if not result:
                logger.warning(f"[DEBUG Backend] Chapter {chapter_id} not found or not authorized")
                return {"content": "", "events": []}

            # Handle NULL content or any other issue
            try:
                content = result[0] if result[0] is not None else ""
                logger.debug(f"[DEBUG Backend] Retrieved content type: {type(content).__name__}")
                logger.debug(f"[DEBUG Backend] Content length: {len(str(content)) if content else 0}")
                if content:
                    logger.debug(f"[DEBUG Backend] Content preview: {str(content)[:100]}...")

                # Get events for this chapter
                cursor.execute(
                    """
                    SELECT event_id, title, description, sequence_number
                    FROM plot_events
                    WHERE book_id = %s AND chapter_id = %s
                    ORDER BY sequence_number
                    """,
                    (book_id, chapter_id)
                )
                events = cursor.fetchall()

                # Get characters and locations for each event
                events_with_details = []
                for event in events:
                    event_id = event['event_id']

                    # Get characters - safely check if table exists
                    try:
                        cursor.execute(
                            """
                            SELECT EXISTS (
                                SELECT FROM information_schema.tables
                                WHERE table_name = 'event_characters'
                            )
                            """
                        )
                        table_exists = cursor.fetchone()[0]

                        if table_exists:
                            cursor.execute(
                                "SELECT character_name FROM event_characters WHERE event_id = %s",
                                (event_id,)
                            )
                            characters = [row['character_name'] for row in cursor.fetchall()]
                        else:
                            characters = []
                    except Exception as e:
                        logger.error(f"Error checking event_characters table: {e}")
                        characters = []

                    # Get locations - safely check if table exists
                    try:
                        cursor.execute(
                            """
                            SELECT EXISTS (
                                SELECT FROM information_schema.tables
                                WHERE table_name = 'event_locations'
                            )
                            """
                        )
                        table_exists = cursor.fetchone()[0]

                        if table_exists:
                            cursor.execute(
                                "SELECT location_name FROM event_locations WHERE event_id = %s",
                                (event_id,)
                            )
                            locations = [row['location_name'] for row in cursor.fetchall()]
                        else:
                            locations = []
                    except Exception as e:
                        logger.error(f"Error checking event_locations table: {e}")
                        locations = []

                    # Add to events list
                    events_with_details.append({
                        "id": event_id,
                        "title": event['title'],
                        "description": event['description'],
                        "sequence_number": event['sequence_number'],
                        "characters": characters,
                        "locations": locations
                    })

                return {
                    "content": content,
                    "events": events_with_details
                }
            except Exception as e:
                logger.error(f"Error processing content: {str(e)}")
                return {"content": "", "events": []}

    except Exception as e:
        logger.error(f"Database error getting chapter content: {str(e)}")
        return {"content": "", "events": []}  # Return empty content instead of raising an exception
    finally:
        if conn:
            conn.close()

@router.put("/{book_id}/chapters/{chapter_id}/content")
def update_chapter_content(
    book_id: str,
    chapter_id: str,
    content_data: ChapterContent,
    user_id: str = Depends(get_current_user)
):
    """Updates the content of a chapter."""
    logger.debug(f"[DEBUG Backend] Updating content for chapter {chapter_id} in book {book_id}")
    logger.debug(f"[DEBUG Backend] User ID: {user_id}")
    logger.debug(f"[DEBUG Backend] Content type: {type(content_data.content).__name__}")
    logger.debug(f"[DEBUG Backend] Content length: {len(content_data.content) if content_data.content else 0}")
    if content_data.content:
        logger.debug(f"[DEBUG Backend] Content preview: {content_data.content[:100]}...")

    try:
        conn = get_db_connection()
        with conn.cursor() as cursor:
            # Verify the chapter exists and belongs to the book
            cursor.execute(
                "SELECT c.chapter_id FROM chapters c JOIN books b ON c.book_id = b.book_id "
                "WHERE c.chapter_id = %s AND c.book_id = %s AND b.user_id = %s",
                (chapter_id, book_id, user_id)
            )
            chapter = cursor.fetchone()

            if not chapter:
                raise HTTPException(status_code=404, detail="Chapter not found or not authorized")

            # Update the chapter content
            logger.debug(f"[DEBUG Backend] Executing update query for chapter {chapter_id}")
            cursor.execute(
                "UPDATE chapters SET content = %s WHERE chapter_id = %s",
                (content_data.content, chapter_id)
            )

            if cursor.rowcount == 0:
                logger.error(f"[DEBUG Backend] Failed to update chapter content, rowcount: {cursor.rowcount}")
                raise HTTPException(status_code=500, detail="Failed to update chapter content")

            logger.debug(f"[DEBUG Backend] Update query executed successfully, rows affected: {cursor.rowcount}")
            conn.commit()
            logger.debug(f"[DEBUG Backend] Transaction committed successfully")
            return {"status": "success", "message": "Chapter content updated successfully"}
    except Exception as e:
        logger.error(f"Error updating chapter content: {str(e)}")
        if conn:
            conn.rollback()
        raise HTTPException(status_code=500, detail=f"Error updating chapter content: {str(e)}")
    finally:
        if conn:
            conn.close()

@router.get("/{book_id}/chapters")
def get_chapters(
    book_id: str,
    user_id: str = Depends(get_current_user)
):
    """Gets all chapters for a book."""
    logger.debug(f"[DEBUG] Getting chapters for book {book_id}")
    logger.debug(f"[DEBUG] User ID: {user_id}")

    conn = None
    try:
        logger.debug(f"[DEBUG] Getting database connection")
        conn = get_db_connection()
        with conn.cursor() as cursor:
            # Verify the book exists and belongs to the user
            logger.debug(f"[DEBUG] Verifying book exists and belongs to user")
            cursor.execute(
                "SELECT book_id FROM books WHERE book_id = %s AND user_id = %s",
                (book_id, user_id)
            )
            book = cursor.fetchone()
            logger.debug(f"[DEBUG] Book query result: {book}")

            if not book:
                logger.warning(f"[DEBUG] Book not found or not authorized: {book_id}, user: {user_id}")
                raise HTTPException(status_code=404, detail="Book not found or not authorized")

            # Get all chapters for the book
            logger.debug(f"[DEBUG] Fetching chapters for book {book_id}")
            cursor.execute(
                "SELECT chapter_id, book_id, title, sequence_number, created_at, updated_at "
                "FROM chapters WHERE book_id = %s ORDER BY sequence_number",
                (book_id,)
            )
            chapters = cursor.fetchall()
            logger.debug(f"[DEBUG] Found {len(chapters)} chapters")

            # Convert to list of dictionaries
            result = []
            for chapter in chapters:
                try:
                    # Print each key and value for debugging
                    logger.debug(f"[DEBUG] Chapter keys: {chapter.keys()}")
                    for key in chapter.keys():
                        logger.debug(f"[DEBUG] Chapter[{key}] = {chapter[key]}")

                    # Create the chapter dictionary
                    chapter_dict = {
                        "id": chapter['chapter_id'],
                        "chapter_id": chapter['chapter_id'],
                        "book_id": chapter['book_id'],
                        "title": chapter['title'],
                        "sequence_number": chapter['sequence_number']
                    }

                    # Handle datetime fields separately
                    if 'created_at' in chapter and chapter['created_at']:
                        chapter_dict['created_at'] = chapter['created_at'].isoformat()
                    else:
                        chapter_dict['created_at'] = None

                    if 'updated_at' in chapter and chapter['updated_at']:
                        chapter_dict['updated_at'] = chapter['updated_at'].isoformat()
                    else:
                        chapter_dict['updated_at'] = None

                    result.append(chapter_dict)
                    logger.debug(f"[DEBUG] Processed chapter: {chapter_dict['id']}")
                except Exception as chapter_error:
                    logger.error(f"[DEBUG] Error processing chapter: {repr(chapter_error)}, chapter data: {chapter}")
                    logger.error(f"[DEBUG] Error type: {type(chapter_error)}")
                    import traceback
                    logger.error(f"[DEBUG] Traceback: {traceback.format_exc()}")

            logger.debug(f"[DEBUG] Returning {len(result)} chapters")
            return result
    except Exception as e:
        logger.error(f"[DEBUG] Error getting chapters: {str(e)}")
        logger.exception("[DEBUG] Exception details:")
        if conn:
            try:
                conn.rollback()
                logger.debug(f"[DEBUG] Transaction rolled back")
            except Exception as rollback_error:
                logger.error(f"[DEBUG] Error rolling back transaction: {str(rollback_error)}")
        raise HTTPException(status_code=500, detail=f"Error getting chapters: {str(e)}")
    finally:
        if conn:
            try:
                conn.close()
                logger.debug(f"[DEBUG] Database connection closed")
            except Exception as close_error:
                logger.error(f"[DEBUG] Error closing database connection: {str(close_error)}")

@router.post("/{book_id}/chapters")
def create_chapter(
    book_id: str,
    chapter_data: ChapterCreate,
    user_id: str = Depends(get_current_user)
):
    """Creates a new chapter in a book."""
    logger.debug(f"[DEBUG] Creating new chapter in book {book_id}")
    logger.debug(f"[DEBUG] Chapter data: {chapter_data}")
    logger.debug(f"[DEBUG] User ID: {user_id}")

    try:
        logger.debug(f"[DEBUG] Getting database connection")
        conn = get_db_connection()
        with conn.cursor() as cursor:
            # Verify the book exists and belongs to the user
            logger.debug(f"[DEBUG] Verifying book exists and belongs to user")
            cursor.execute(
                "SELECT book_id FROM books WHERE book_id = %s AND user_id = %s",
                (book_id, user_id)
            )
            book = cursor.fetchone()
            logger.debug(f"[DEBUG] Book query result: {book}")

            if not book:
                logger.error(f"[DEBUG] Book not found or not authorized: {book_id}, user: {user_id}")
                raise HTTPException(status_code=404, detail="Book not found or not authorized")

            # Generate a unique chapter ID
            chapter_id = f"ch_{str(uuid4()).replace('-', '_')}"
            logger.debug(f"[DEBUG] Generated chapter ID: {chapter_id}")

            # If sequence_number is not provided, find the highest sequence number and add 1
            sequence_number = chapter_data.sequence_number
            logger.debug(f"[DEBUG] Initial sequence_number: {sequence_number}")

            if sequence_number is None:
                logger.debug(f"[DEBUG] Finding highest sequence number")
                cursor.execute(
                    "SELECT MAX(sequence_number) FROM chapters WHERE book_id = %s",
                    (book_id,)
                )
                max_seq = cursor.fetchone()[0]
                sequence_number = (max_seq or 0) + 1
                logger.debug(f"[DEBUG] Calculated sequence_number: {sequence_number}")

            # Insert the new chapter
            logger.debug(f"[DEBUG] Inserting new chapter with sequence_number: {sequence_number}")
            cursor.execute(
                "INSERT INTO chapters (book_id, chapter_id, title, sequence_number, content) "
                "VALUES (%s, %s, %s, %s, %s)",
                (book_id, chapter_id, chapter_data.title, sequence_number, "")
            )
            logger.debug(f"[DEBUG] Insert result: {cursor.rowcount} rows affected")

            conn.commit()
            logger.debug(f"[DEBUG] Transaction committed")

            # Return the new chapter data
            response_data = {
                "id": chapter_id,  # Using 'id' to match frontend expectations
                "chapter_id": chapter_id,
                "book_id": book_id,
                "title": chapter_data.title,
                "sequence_number": sequence_number,
                "content": ""
            }
            logger.debug(f"[DEBUG] Returning response: {response_data}")
            return response_data
    except Exception as e:
        logger.error(f"Error creating chapter: {str(e)}")
        if conn:
            conn.rollback()
        raise HTTPException(status_code=500, detail=f"Error creating chapter: {str(e)}")
    finally:
        if conn:
            conn.close()

@router.put("/{book_id}/chapters/order")
def update_chapter_order(
    book_id: str,
    chapters: dict,
    user_id: str = Depends(get_current_user)
):
    """Updates the order of chapters in a book."""
    logger.debug(f"Updating chapter order in book {book_id}")

    try:
        conn = get_db_connection()
        with conn.cursor() as cursor:
            # Verify the book exists and belongs to the user
            cursor.execute(
                "SELECT book_id FROM books WHERE book_id = %s AND user_id = %s",
                (book_id, user_id)
            )
            book = cursor.fetchone()

            if not book:
                raise HTTPException(status_code=404, detail="Book not found or not authorized")

            # Update each chapter's sequence number
            for chapter in chapters.get("chapters", []):
                cursor.execute(
                    "UPDATE chapters SET sequence_number = %s WHERE chapter_id = %s AND book_id = %s",
                    (chapter.get("sequence_number"), chapter.get("id"), book_id)
                )

            conn.commit()
            return {"status": "success", "message": "Chapter order updated successfully"}
    except Exception as e:
        logger.error(f"Error updating chapter order: {str(e)}")
        if conn:
            conn.rollback()
        raise HTTPException(status_code=500, detail=f"Error updating chapter order: {str(e)}")
    finally:
        if conn:
            conn.close()

@router.delete("/{book_id}/chapters/{chapter_id}")
def delete_chapter(
    book_id: str,
    chapter_id: str,
    user_id: str = Depends(get_current_user)
):
    """Deletes a chapter from a book."""
    logger.debug(f"Deleting chapter {chapter_id} from book {book_id}")

    try:
        conn = get_db_connection()
        with conn.cursor() as cursor:
            # Verify the chapter exists and belongs to the book and user
            cursor.execute(
                "SELECT c.chapter_id FROM chapters c JOIN books b ON c.book_id = b.book_id "
                "WHERE c.chapter_id = %s AND c.book_id = %s AND b.user_id = %s",
                (chapter_id, book_id, user_id)
            )
            chapter = cursor.fetchone()

            if not chapter:
                raise HTTPException(status_code=404, detail="Chapter not found or not authorized")

            # First, update any plot events to move them to the bank
            cursor.execute(
                "UPDATE plot_events SET chapter_id = NULL, is_in_bank = TRUE "
                "WHERE book_id = %s AND chapter_id = %s",
                (book_id, chapter_id)
            )

            # Delete the chapter
            cursor.execute(
                "DELETE FROM chapters WHERE chapter_id = %s AND book_id = %s",
                (chapter_id, book_id)
            )

            if cursor.rowcount == 0:
                raise HTTPException(status_code=500, detail="Failed to delete chapter")

            conn.commit()
            return {"status": "success", "message": "Chapter deleted successfully"}
    except Exception as e:
        logger.error(f"Error deleting chapter: {str(e)}")
        if conn:
            conn.rollback()
        raise HTTPException(status_code=500, detail=f"Error deleting chapter: {str(e)}")
    finally:
        if conn:
            conn.close()

@router.put("/{book_id}/chapters/reorder")
def reorder_chapters(
    book_id: str,
    request: ChapterReorderRequest,
    user_id: str = Depends(get_current_user)
):
    """Reorders chapters for a book."""
    logger.debug(f"[DEBUG] Reordering chapters for book {book_id}")
    logger.debug(f"[DEBUG] User ID: {user_id}")
    logger.debug(f"[DEBUG] Chapters to reorder: {request.chapters}")

    conn = None
    try:
        logger.debug(f"[DEBUG] Getting database connection")
        conn = get_db_connection()
        with conn.cursor() as cursor:
            # Verify the book exists and belongs to the user
            logger.debug(f"[DEBUG] Verifying book exists and belongs to user")
            cursor.execute(
                "SELECT book_id FROM books WHERE book_id = %s AND user_id = %s",
                (book_id, user_id)
            )
            book = cursor.fetchone()
            logger.debug(f"[DEBUG] Book query result: {book}")

            if not book:
                logger.warning(f"[DEBUG] Book not found or not authorized: {book_id}, user: {user_id}")
                raise HTTPException(status_code=404, detail="Book not found or not authorized")

            # Update sequence numbers for each chapter
            for chapter in request.chapters:
                logger.debug(f"[DEBUG] Updating sequence number for chapter {chapter.id} to {chapter.sequence_number}")
                cursor.execute(
                    "UPDATE chapters SET sequence_number = %s, updated_at = NOW() "
                    "WHERE chapter_id = %s AND book_id = %s",
                    (chapter.sequence_number, chapter.id, book_id)
                )
                if cursor.rowcount == 0:
                    logger.warning(f"[DEBUG] Chapter not found or not updated: {chapter.id}")

            conn.commit()
            logger.debug(f"[DEBUG] Chapters reordered successfully")
            return {"status": "success", "message": "Chapters reordered successfully"}
    except Exception as e:
        logger.error(f"Error reordering chapters: {str(e)}")
        if conn:
            conn.rollback()
        raise HTTPException(status_code=500, detail=f"Error reordering chapters: {str(e)}")
    finally:
        if conn:
            conn.close()

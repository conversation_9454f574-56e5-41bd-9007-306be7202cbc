from pydantic import BaseModel
from typing import List, Dict, Any

class BookCreate(BaseModel):
    book_id: str
    title: str

class MemoryUpdate(BaseModel):
    memory: str

class AIPrompt(BaseModel):
    prompt: str

class CategoryData(BaseModel):
    description: str = ""
    items: List[dict] = []  # e.g., [{"name": "fireball", "effects": "Deals fire damage"}]

class World(BaseModel):
    setting_types: List[str] = ["fantasy"]  # Multi-select setting types
    custom_setting_types: List[str] = []    # User-defined setting types
    lists: Dict[str, CategoryData] = {}     # e.g., {"magic": {"description": "", "items": [...]}, "locations": {...}}

class PlotNode(BaseModel):
    id: str
    title: str
    events: List[dict]

class PlotChapter(BaseModel):
    id: str
    title: str
    events: List[dict] = []
    content: str = ""

class PlotData(BaseModel):
    bank: List[Dict[str, Any]] = []  # Events in "Unassigned"
    # Allow dynamic chapter IDs with lists of events
    additional_properties: Dict[str, List[Dict[str, Any]]] = {}
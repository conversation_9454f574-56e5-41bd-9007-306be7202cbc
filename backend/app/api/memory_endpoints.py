from fastapi import APIRouter, Depends, HTTPException
import json
import logging
from app.auth import get_current_user
from app.db.character_repository import CharacterRepository
from app.db.plot_repository import PlotRepository
from app.db.world_repository import WorldRepository

logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

router = APIRouter(prefix="/books/{book_id}/memory", tags=["memory"])
char_repo = CharacterRepository()
plot_repo = PlotRepository()
world_repo = WorldRepository()

@router.post("/generate")
async def generate_memory_blob(
    book_id: str,
    user_id: str = Depends(get_current_user)
):
    """Generates a memory blob from database tables for AI context."""
    try:
        # Fetch data from various repositories
        characters = char_repo.get_characters(book_id)
        plot = plot_repo.get_plot(book_id)
        world = world_repo.get_world_data_for_ai(book_id)
        
        # Construct memory blob
        memory_blob = {
            "characters": characters,
            "plot": plot,
            "world": world,
            # Add any other necessary data for AI context
        }
        
        return {"book_id": book_id, "memory": memory_blob}
    except Exception as e:
        logger.error(f"Error generating memory blob: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error generating memory blob: {str(e)}")

"""
API endpoints for admin functionality.
"""
from fastapi import APIRout<PERSON>, Depends, HTTPException, Query, Request, Header
from pydantic import BaseModel, Field
from typing import List, Dict, Optional, Any, Union
import logging
import numpy as np
import sys
import os
from app.auth import get_current_user
from app.db.world_aspect_repository import WorldAspectRepository
from app.db.template_repository import TemplateRepository

# Set up logger
logger = logging.getLogger(__name__)

# Import Qdrant integration
try:
    # Use relative import path
    sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
    from qdrant_integration.embedding_service import get_embedding
    QDRANT_AVAILABLE = True
    logger.info("Qdrant integration available for admin endpoints.")
except ImportError as e:
    logger.warning(f"Qdrant integration not available for admin endpoints. Error: {str(e)}")
    QDRANT_AVAILABLE = False

router = APIRouter(prefix="/api/admin", tags=["admin"])

# Initialize repositories
aspect_repo = WorldAspectRepository()
template_repo = TemplateRepository()

# Models
class EmbeddingTestRequest(BaseModel):
    text: str
    template_type: str
    test_type: str
    field_weights: Optional[Dict[str, float]] = None

class EmbeddingTestResponse(BaseModel):
    embedding: List[float]
    metrics: Dict[str, Any]
    preprocessed: Dict[str, Any]

# Endpoints
@router.post("/embedding-test", response_model=EmbeddingTestResponse)
async def test_embedding(
    request: EmbeddingTestRequest,
    user_id: str = Depends(get_current_user)
):
    """
    Tests the embedding generation for a given text and template type.
    """
    try:
        if not QDRANT_AVAILABLE:
            raise HTTPException(status_code=500, detail="Qdrant integration not available")

        # Get the embedding
        embedding = get_embedding(request.text)

        # Calculate metrics
        embedding_array = np.array(embedding)
        norm = float(np.linalg.norm(embedding_array))
        variance = float(np.var(embedding_array))

        # Mock similarity scores for now
        # In a real implementation, you would compare to actual template embeddings
        similarity = {
            "character": 0.85,
            "location": 0.32,
            "item": 0.41
        }

        # Return the results
        return {
            "embedding": embedding,
            "metrics": {
                "norm": norm,
                "variance": variance,
                "similarity": similarity
            },
            "preprocessed": {
                "text": request.text.strip().lower()
            }
        }
    except Exception as e:
        logger.error(f"Error testing embedding: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to test embedding: {str(e)}")

@router.get("/system-status")
async def get_system_status(
    user_id: str = Depends(get_current_user)
):
    """
    Gets the status of various system components.
    """
    try:
        # In a real implementation, you would check the actual status of these components
        return {
            "postgresql": {
                "status": "healthy",
                "metrics": {
                    "connections": 12,
                    "active_queries": 3,
                    "avg_query_time": 45,  # ms
                    "disk_usage": 1.2,  # GB
                    "uptime": "7d 14h 23m"
                }
            },
            "qdrant": {
                "status": "healthy",
                "metrics": {
                    "collections": 8,
                    "points": 25420,
                    "segments": 32,
                    "disk_usage": 0.8,  # GB
                    "avg_search_time": 12,  # ms
                    "uptime": "7d 14h 23m"
                }
            },
            "api": {
                "status": "healthy",
                "metrics": {
                    "requests_per_minute": 42,
                    "avg_response_time": 120,  # ms
                    "error_rate": 0.2,  # %
                    "uptime": "7d 14h 23m"
                }
            }
        }
    except Exception as e:
        logger.error(f"Error getting system status: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get system status: {str(e)}")

# Add endpoints for the admin panel to access categories and templates
@router.get("/world-building/categories")
def get_all_categories(user_id: str = Depends(get_current_user)):
    """
    Fetches all world aspect categories for the admin panel.
    This endpoint doesn't require a book_id.
    """
    try:
        categories = aspect_repo.get_aspect_categories()
        # Convert the categories to a list of dictionaries
        category_list = []
        for category in categories:
            category_dict = dict(category)
            category_list.append(category_dict)
        return category_list
    except Exception as e:
        logger.error(f"Error fetching categories for admin panel: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch categories: {str(e)}")

@router.get("/templates")
def get_all_templates(
    category_id: Optional[str] = None,
    creator_id: Optional[str] = None,
    is_system_template: Optional[bool] = None,
    user_id: str = Depends(get_current_user)
):
    """
    Fetches all templates for the admin panel.
    This endpoint is a convenience wrapper around the /api/templates endpoint.
    """
    try:
        templates = template_repo.get_templates(
            category_id=category_id,
            creator_id=creator_id,
            is_system_template=is_system_template
        )
        return templates
    except Exception as e:
        logger.error(f"Error fetching templates for admin panel: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch templates: {str(e)}")

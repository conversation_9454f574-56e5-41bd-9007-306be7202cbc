"""
API endpoints for the enhanced World Building page.
"""
from fastapi import APIRouter, Depends, HTTPException, Query
from pydantic import BaseModel, Field
from typing import List, Dict, Optional, Any
import json
import logging
import uuid
from app.auth import get_current_user, oauth2_scheme
from app.db.base import get_db_connection
from app.db.world_repository import WorldRepository
from app.db.world_aspect_repository import WorldAspectRepository
from app.db.world_relationship_repository import WorldRelationshipRepository
from app.db.world_table_repository import WorldTableRepository
from app.db.world_map_repository import WorldMapRepository

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/books/{book_id}/world-building", tags=["world-building"])

# Initialize repositories
world_repo = WorldRepository()
aspect_repo = WorldAspectRepository()
relationship_repo = WorldRelationshipRepository()
table_repo = WorldTableRepository()
map_repo = WorldMapRepository()

# Helper function to get the appropriate template structure for a given category
def get_template_for_category(category_id):
    """
    Returns the appropriate template structure for a given category ID.
    Includes information about proper nesting of elements and sub-elements.
    """
    # Map category IDs to their template structures
    templates = {
        # Physical categories

        # Locations
        'cat_physical_locations': """
        IMPORTANT - Use the following structure for the main location:
        ```json
        {
          "parent": {
            "name": "Location Name",
            "description": "Detailed description",
            "element_type": "location",
            "custom_fields": {
              "location_type": "city|town|village|landmark|building|region|other",
              "population": "Approximate number of inhabitants (if applicable)",
              "notable_features": "Distinctive characteristics of this location",
              "climate": "Weather patterns and climate of this location",
              "culture": "Cultural aspects of this location"
            }
          },
          "sub_elements": [
            {
              "name": "Landmark Name",
              "description": "Detailed description",
              "element_type": "landmark",
              "custom_fields": {
                "landmark_type": "monument|natural_feature|historical_site|other",
                "significance": "Cultural or historical significance",
                "age": "How old this landmark is",
                "condition": "Current state of the landmark",
                "accessibility": "How easy it is to visit or access"
              }
            },
            {
              "name": "Building Name",
              "description": "Detailed description",
              "element_type": "building",
              "custom_fields": {
                "building_type": "residential|commercial|governmental|religious|military|other",
                "architecture": "Architectural style and features",
                "size": "Size or dimensions",
                "purpose": "What this building is used for",
                "occupants": "Who lives or works here"
              }
            },
            {
              "name": "Sub-location Name",
              "description": "Detailed description",
              "element_type": "location",
              "custom_fields": {
                "location_type": "district|neighborhood|street|area|other",
                "population": "Approximate number of inhabitants (if applicable)",
                "notable_features": "Distinctive characteristics of this sub-location",
                "purpose": "Main function or purpose of this area",
                "relation": "How this area relates to the parent location"
              }
            }
          ]
        }
        ```
        """,

        # Geography
        'cat_physical_geography': """
        IMPORTANT - Use the following structure for the main geography feature:
        ```json
        {
          "parent": {
            "name": "Geography Feature Name",
            "description": "Detailed description",
            "element_type": "geography",
            "custom_fields": {
              "climate": "Climate of this geographical area",
              "terrain": "Description of the terrain",
              "resources": "Natural resources found in this area",
              "size": "Size or extent of this geographical feature"
            }
          },
          "sub_elements": [
            {
              "name": "Natural Feature Name",
              "description": "Detailed description",
              "element_type": "natural_feature",
              "custom_fields": {
                "feature_type": "mountain|river|lake|forest|canyon|desert|other",
                "size": "Size or dimensions of this feature",
                "ecology": "Plant and animal life associated with this feature",
                "significance": "Cultural or practical significance",
                "unique_properties": "Any unique or unusual properties"
              }
            },
            {
              "name": "Location Name",
              "description": "Detailed description",
              "element_type": "location",
              "custom_fields": {
                "location_type": "settlement|outpost|ruins|landmark|other",
                "population": "Approximate number of inhabitants (if applicable)",
                "purpose": "Main function or purpose of this location",
                "notable_features": "Distinctive characteristics of this location",
                "relation": "How this location relates to the geography"
              }
            }
          ]
        }
        ```
        """,

        # Plants
        'cat_physical_plants': """
        IMPORTANT - Use the following structure for the main plant:
        ```json
        {
          "parent": {
            "name": "Plant Name",
            "description": "Detailed description",
            "element_type": "plant",
            "custom_fields": {
              "plant_type": "tree|shrub|flower|grass|crop|fungus|aquatic|other",
              "appearance": "What the plant looks like",
              "habitat": "Where the plant typically grows",
              "properties": "Special properties (medicinal, magical, etc.)",
              "cultivation": "How it's cultivated or harvested"
            }
          },
          "sub_elements": [
            {
              "name": "Plant Variant Name",
              "description": "Detailed description",
              "element_type": "plant_variant",
              "custom_fields": {
                "variant_type": "subspecies|cultivar|mutation|regional_variant|other",
                "differences": "How this variant differs from the main plant",
                "habitat": "Where this variant typically grows",
                "rarity": "How common or rare this variant is",
                "unique_properties": "Special properties unique to this variant"
              }
            }
          ]
        }
        ```
        """,

        # Animals
        'cat_physical_animals': """
        IMPORTANT - Use the following structure for the main animal:
        ```json
        {
          "parent": {
            "name": "Animal Name",
            "description": "Detailed description",
            "element_type": "animal",
            "custom_fields": {
              "animal_type": "mammal|bird|reptile|amphibian|fish|insect|arachnid|other",
              "habitat": "Where this animal typically lives",
              "appearance": "What this animal looks like",
              "behavior": "How this animal behaves",
              "diet": "What this animal eats",
              "lifecycle": "Reproduction, lifespan, etc."
            }
          },
          "sub_elements": [
            {
              "name": "Animal Variant Name",
              "description": "Detailed description",
              "element_type": "animal_variant",
              "custom_fields": {
                "variant_type": "breed|subspecies|regional_variant|mutation|other",
                "differences": "How this variant differs from the main animal",
                "habitat": "Where this variant typically lives",
                "rarity": "How common or rare this variant is",
                "unique_traits": "Special traits unique to this variant"
              }
            }
          ]
        }
        ```
        """,

        # Monsters & Creatures
        'cat_physical_monsters': """
        IMPORTANT - Use the following structure for the main creature:
        ```json
        {
          "parent": {
            "name": "Creature Name",
            "description": "Detailed description",
            "element_type": "creature",
            "custom_fields": {
              "creature_type": "monster|mythical|sentient|undead|construct|other",
              "habitat": "Where this creature typically lives",
              "appearance": "What this creature looks like",
              "behavior": "How this creature behaves",
              "abilities": "Special abilities the creature has",
              "diet": "What this creature eats"
            }
          },
          "sub_elements": [
            {
              "name": "Creature Variant Name",
              "description": "Detailed description",
              "element_type": "creature_variant",
              "custom_fields": {
                "variant_type": "subspecies|regional_variant|mutation|evolution|other",
                "differences": "How this variant differs from the main creature",
                "habitat": "Where this variant typically lives",
                "unique_abilities": "Special abilities unique to this variant",
                "rarity": "How common or rare this variant is"
              }
            }
          ]
        }
        ```
        """,

        # Climate & Weather
        'cat_physical_climate': """
        IMPORTANT - Use the following structure for the main climate system:
        ```json
        {
          "parent": {
            "name": "Climate System Name",
            "description": "Detailed description",
            "element_type": "planetary_climate_system",
            "custom_fields": {
              "axial_tilt": "Axial tilt of the planet (affects seasons)",
              "orbital_characteristics": "Orbital path, year length, and unusual features",
              "global_patterns": "Major global climate patterns and zones",
              "atmospheric_composition": "What the atmosphere is composed of",
              "number_of_seasons": "How many seasons this world has",
              "global_phenomena": "Planet-wide weather events or cycles"
            }
          },
          "sub_elements": [
            {
              "name": "Regional Climate System Name",
              "description": "Detailed description",
              "element_type": "climate_system",
              "custom_fields": {
                "average_temperature": "Temperature range in this region",
                "precipitation": "Rainfall patterns in this region",
                "humidity": "Humidity levels in this region",
                "unique_weather_patterns": "Weather patterns specific to this region",
                "impact": "How this climate affects the region's inhabitants and environment"
              }
            }
          ]
        }
        ```

        You can also include weather phenomena and seasons as sub-elements of regional climate systems:

        Weather Phenomenon Example:
        ```json
        {
          "name": "Weather Phenomenon Name",
          "description": "Detailed description",
          "element_type": "weather_phenomenon",
          "custom_fields": {
            "frequency": "How often this phenomenon occurs",
            "intensity": "How severe this phenomenon is",
            "duration": "How long this phenomenon typically lasts",
            "effects": "Effects on the environment and inhabitants",
            "seasonal_variation": "How this phenomenon varies with seasons"
          }
        }
        ```

        Season Example:
        ```json
        {
          "name": "Season Name",
          "description": "Detailed description",
          "element_type": "season",
          "custom_fields": {
            "duration": "How long this season lasts",
            "temperature_range": "Temperature range during this season",
            "precipitation": "Rainfall patterns during this season",
            "cultural_significance": "How this season affects culture and society",
            "agricultural_impact": "Effects on agriculture and food production"
          }
        }
        ```
        """,

        # Social categories

        # Cultures
        'cat_social_cultures': """
        IMPORTANT - Use the following structure for the main culture:
        ```json
        {
          "parent": {
            "name": "Culture Name",
            "description": "Detailed description",
            "element_type": "culture",
            "custom_fields": {
              "values": "Core values important to this culture",
              "customs": "Important customs and traditions",
              "social_structure": "How society is organized in this culture",
              "language": "Languages spoken in this culture",
              "art_forms": "Important art forms to this culture"
            }
          },
          "sub_elements": [
            {
              "name": "Cultural Practice Name",
              "description": "Detailed description",
              "element_type": "cultural_practice",
              "custom_fields": {
                "practice_type": "tradition|ritual|custom|ceremony|festival|other",
                "significance": "Cultural or religious significance",
                "participants": "Who participates in this practice",
                "frequency": "How often this practice occurs",
                "origin": "How this practice originated"
              }
            }
          ]
        }
        ```
        """,

        # Organizations
        'cat_social_factions': """
        IMPORTANT - Use the following structure for the main organization:
        ```json
        {
          "parent": {
            "name": "Organization Name",
            "description": "Detailed description",
            "element_type": "organization",
            "custom_fields": {
              "structure": "Organizational structure",
              "leadership": "Who leads this organization",
              "purpose": "Purpose of this organization",
              "resources": "Resources available to this organization",
              "influence": "Extent of the organization's influence"
            }
          },
          "sub_elements": [
            {
              "name": "Department/Division Name",
              "description": "Detailed description",
              "element_type": "department",
              "custom_fields": {
                "function": "Primary function of this department",
                "leadership": "Who leads this department",
                "size": "Number of members or employees",
                "resources": "Resources available to this department",
                "parent_organization": "The main organization this department belongs to"
              }
            },
            {
              "name": "Law/Regulation Name",
              "description": "Detailed description",
              "element_type": "law",
              "custom_fields": {
                "scope": "Who this law applies to",
                "enforcement": "How this law is enforced",
                "penalties": "Consequences for breaking this law",
                "exceptions": "Exceptions or loopholes in this law",
                "creation_date": "When this law was established"
              }
            }
          ]
        }
        ```
        """,

        # Governments
        'cat_social_governments': """
        IMPORTANT - Use the following structure for the main government:
        ```json
        {
          "parent": {
            "name": "Government Name",
            "description": "Detailed description",
            "element_type": "organization",
            "custom_fields": {
              "government_type": "monarchy|democracy|oligarchy|republic|dictatorship|other",
              "leadership": "Who leads this government",
              "territory": "Territory governed",
              "military": "Military strength and organization",
              "policies": "Notable policies or laws"
            }
          },
          "sub_elements": [
            {
              "name": "Department/Division Name",
              "description": "Detailed description",
              "element_type": "department",
              "custom_fields": {
                "function": "Primary function of this department",
                "leadership": "Who leads this department",
                "size": "Number of members or employees",
                "resources": "Resources available to this department",
                "parent_organization": "The main government this department belongs to"
              }
            },
            {
              "name": "Law/Regulation Name",
              "description": "Detailed description",
              "element_type": "law",
              "custom_fields": {
                "scope": "Who this law applies to",
                "enforcement": "How this law is enforced",
                "penalties": "Consequences for breaking this law",
                "exceptions": "Exceptions or loopholes in this law",
                "creation_date": "When this law was established"
              }
            }
          ]
        }
        ```
        """,

        # Laws
        'cat_social_laws': """
        IMPORTANT - Use the following structure for the main law:
        ```json
        {
          "parent": {
            "name": "Law Name",
            "description": "Detailed description",
            "element_type": "law",
            "custom_fields": {
              "jurisdiction": "Where this law applies",
              "enforcement": "How this law is enforced",
              "penalties": "Penalties for breaking this law",
              "exceptions": "Exceptions to this law"
            }
          },
          "sub_elements": [
            {
              "name": "Law Clause Name",
              "description": "Detailed description",
              "element_type": "law_clause",
              "custom_fields": {
                "purpose": "Purpose of this clause",
                "conditions": "Conditions under which this clause applies",
                "penalties": "Specific penalties for violating this clause",
                "exceptions": "Exceptions to this clause",
                "precedents": "Legal precedents related to this clause"
              }
            }
          ]
        }
        ```
        """,

        # Economics & Trade
        'cat_social_economics': """
        IMPORTANT - Use the following structure for the main economic system:
        ```json
        {
          "parent": {
            "name": "Economic System Name",
            "description": "Detailed description",
            "element_type": "economic_system",
            "custom_fields": {
              "system_type": "feudal|mercantile|capitalist|socialist|communist|barter|gift|mixed|other",
              "currency": "What currency is used",
              "major_industries": "Primary industries in this economy",
              "trade_patterns": "How trade works in this system",
              "wealth_distribution": "How wealth is distributed"
            }
          },
          "sub_elements": [
            {
              "name": "Trade Route Name",
              "description": "Detailed description",
              "element_type": "trade_route",
              "custom_fields": {
                "start_point": "Where this trade route begins",
                "end_point": "Where this trade route ends",
                "goods": "Goods transported along this route",
                "dangers": "Dangers or challenges along this route",
                "importance": "Economic importance of this route"
              }
            },
            {
              "name": "Market Name",
              "description": "Detailed description",
              "element_type": "market",
              "custom_fields": {
                "location": "Where this market is located",
                "specialties": "What this market is known for",
                "schedule": "When this market operates",
                "size": "Size and scope of this market",
                "regulations": "Rules governing this market"
              }
            },
            {
              "name": "Currency Name",
              "description": "Detailed description",
              "element_type": "currency",
              "custom_fields": {
                "value": "Relative value of this currency",
                "materials": "What this currency is made from",
                "denominations": "Different denominations of this currency",
                "circulation": "How widely this currency is used",
                "stability": "How stable this currency's value is"
              }
            }
          ]
        }
        ```
        """,

        # Metaphysical categories

        # Magic Systems
        'cat_metaphysical_magic': """
        IMPORTANT - Use the following structure for the main magic system:
        ```json
        {
          "parent": {
            "name": "Magic System Name",
            "description": "Detailed description",
            "element_type": "magic_system",
            "custom_fields": {
              "source": "Where the magic comes from",
              "energy_type": "mana|life_force|elemental|divine|cosmic|soul|blood|nature|other",
              "access": "Who can use this magic and how they gain access",
              "limitations": "Limitations and costs of using this magic",
              "learning_process": "How magic users learn and improve",
              "cultural_impact": "How this magic affects society and culture"
            }
          },
          "sub_elements": [
            {
              "name": "Spell Name",
              "description": "Detailed description",
              "element_type": "spell",
              "custom_fields": {
                "effect": "What this spell does",
                "casting_method": "How this spell is cast",
                "difficulty": "How difficult this spell is to learn/cast",
                "components": "Physical components required (if any)",
                "duration": "How long the effects last"
              }
            },
            {
              "name": "Magical Power Name",
              "description": "Detailed description",
              "element_type": "magical_power",
              "custom_fields": {
                "manifestation": "How this power manifests",
                "activation": "How this power is activated",
                "limitations": "Limitations or drawbacks",
                "rarity": "How common or rare this power is",
                "mastery_levels": "Different levels of mastery possible"
              }
            },
            {
              "name": "Magical Artifact Name",
              "description": "Detailed description",
              "element_type": "magical_artifact",
              "custom_fields": {
                "appearance": "What this artifact looks like",
                "powers": "Magical abilities this artifact grants",
                "creation": "How this artifact was created",
                "limitations": "Limitations or drawbacks",
                "history": "Historical significance of this artifact"
              }
            }
          ]
        }
        ```
        """,

        # Religion
        'cat_metaphysical_religion': """
        IMPORTANT - Use the following structure for the main religion:
        ```json
        {
          "parent": {
            "name": "Religion Name",
            "description": "Detailed description",
            "element_type": "religion",
            "custom_fields": {
              "beliefs": "Core beliefs and tenets",
              "practices": "Religious practices and rituals",
              "organization": "How the religion is organized",
              "holy_texts": "Sacred writings or oral traditions",
              "history": "Origin and development of the religion"
            }
          },
          "sub_elements": [
            {
              "name": "Deity Name",
              "description": "Detailed description",
              "element_type": "deity",
              "custom_fields": {
                "domain": "Areas of influence or power",
                "appearance": "How this deity is depicted",
                "symbols": "Sacred symbols associated with this deity",
                "worship": "How this deity is worshipped",
                "relationships": "Relationships with other deities"
              }
            }
          ]
        }
        ```
        """,

        # Superpowers
        'cat_metaphysical_superpowers': """
        IMPORTANT - Use the following structure for the main power:
        ```json
        {
          "parent": {
            "name": "Power Name",
            "description": "Detailed description",
            "element_type": "magical_power",
            "custom_fields": {
              "power_type": "physical|mental|elemental|reality-altering|other",
              "activation": "How the power is activated or controlled",
              "limitations": "Limitations and weaknesses of the power",
              "side_effects": "Side effects or consequences of using the power",
              "origin": "How people obtain this power"
            }
          },
          "sub_elements": [
            {
              "name": "Power Variant Name",
              "description": "Detailed description",
              "element_type": "power_variant",
              "custom_fields": {
                "manifestation": "How this variant manifests",
                "requirements": "Special requirements for this variant",
                "power_level": "Relative strength of this variant",
                "rarity": "How common this variant is",
                "unique_effects": "Effects specific to this variant"
              }
            }
          ]
        }
        ```
        """,

        # Cosmology
        'cat_metaphysical_cosmology': """
        IMPORTANT - Use the following structure for the main cosmic structure:
        ```json
        {
          "parent": {
            "name": "Cosmic Structure Name",
            "description": "Detailed description",
            "element_type": "cosmic_structure",
            "custom_fields": {
              "structure_type": "universe|dimension|galaxy|solar_system|celestial_body|void|other",
              "physical_laws": "Laws of physics that govern this structure",
              "origin": "How it was created or formed",
              "age": "Age or timeline of this structure",
              "notable_features": "Distinctive characteristics",
              "inhabitants": "Beings or entities that inhabit this structure"
            }
          },
          "sub_elements": [
            {
              "name": "Celestial Body Name",
              "description": "Detailed description",
              "element_type": "celestial_body",
              "custom_fields": {
                "body_type": "star|planet|moon|asteroid|comet|black_hole|other",
                "size": "Size or dimensions of this celestial body",
                "composition": "What this celestial body is made of",
                "orbit": "Orbital characteristics (if applicable)",
                "atmosphere": "Atmospheric composition (if applicable)",
                "surface_features": "Notable features on the surface (if applicable)"
              }
            },
            {
              "name": "Cosmic Phenomenon Name",
              "description": "Detailed description",
              "element_type": "cosmic_phenomenon",
              "custom_fields": {
                "phenomenon_type": "energy_field|anomaly|wormhole|nebula|radiation|other",
                "effects": "Effects this phenomenon has on surrounding space",
                "origin": "How this phenomenon formed",
                "stability": "How stable or predictable this phenomenon is",
                "detection": "How this phenomenon can be detected or observed"
              }
            }
          ]
        }
        ```
        """,

        # Technological categories

        # Tools & Technology
        'cat_technological_tools': """
        IMPORTANT - Use the following structure for the main technology:
        ```json
        {
          "parent": {
            "name": "Technology Name",
            "description": "Detailed description",
            "element_type": "technology",
            "custom_fields": {
              "tech_type": "weapon|transportation|communication|medical|industrial|computing|energy|other",
              "function": "What this technology does",
              "development": "How this technology was developed",
              "availability": "How common or rare this technology is",
              "impact": "Impact on society and culture"
            }
          },
          "sub_elements": [
            {
              "name": "Device Name",
              "description": "Detailed description",
              "element_type": "device",
              "custom_fields": {
                "purpose": "Specific purpose of this device",
                "operation": "How this device operates",
                "materials": "Materials used in construction",
                "maintenance": "Maintenance requirements",
                "limitations": "Limitations or drawbacks"
              }
            },
            {
              "name": "Related Technology Name",
              "description": "Detailed description",
              "element_type": "related_technology",
              "custom_fields": {
                "relationship": "How this technology relates to the parent",
                "differences": "Key differences from the parent technology",
                "advantages": "Advantages over the parent technology",
                "disadvantages": "Disadvantages compared to the parent",
                "development_stage": "Current stage of development"
              }
            }
          ]
        }
        ```
        """,

        # Sci-Fi Technology
        'cat_technological_scifi': """
        IMPORTANT - Use the following structure for the main sci-fi technology:
        ```json
        {
          "parent": {
            "name": "Sci-Fi Technology Name",
            "description": "Detailed description",
            "element_type": "technology",
            "custom_fields": {
              "tech_type": "weapon|transportation|communication|medical|industrial|computing|energy|other",
              "scientific_basis": "Scientific principles this technology is based on",
              "development_level": "prototype|limited_use|widespread|theoretical",
              "limitations": "Limitations or drawbacks of this technology",
              "future_potential": "Possible future developments of this technology"
            }
          },
          "sub_elements": [
            {
              "name": "Device Name",
              "description": "Detailed description",
              "element_type": "device",
              "custom_fields": {
                "purpose": "Specific purpose of this device",
                "operation": "How this device operates",
                "materials": "Materials used in construction",
                "power_source": "What powers this device",
                "limitations": "Limitations or drawbacks"
              }
            },
            {
              "name": "Related Technology Name",
              "description": "Detailed description",
              "element_type": "related_technology",
              "custom_fields": {
                "relationship": "How this technology relates to the parent",
                "differences": "Key differences from the parent technology",
                "advantages": "Advantages over the parent technology",
                "disadvantages": "Disadvantages compared to the parent",
                "development_stage": "Current stage of development"
              }
            }
          ]
        }
        ```
        """,

        # Weapons
        'cat_technological_weapons': """
        IMPORTANT - Use the following structure for the main weapon:
        ```json
        {
          "parent": {
            "name": "Weapon Name",
            "description": "Detailed description",
            "element_type": "technology",
            "custom_fields": {
              "weapon_type": "melee|ranged|explosive|energy|biological|chemical|other",
              "damage_type": "Type of damage this weapon inflicts",
              "range": "Effective range of this weapon",
              "materials": "Materials used in construction",
              "availability": "How common or rare this weapon is"
            }
          },
          "sub_elements": [
            {
              "name": "Weapon Variant Name",
              "description": "Detailed description",
              "element_type": "weapon_variant",
              "custom_fields": {
                "differences": "How this variant differs from the main weapon",
                "advantages": "Advantages over the standard model",
                "disadvantages": "Disadvantages compared to the standard model",
                "user_group": "Who typically uses this variant",
                "production": "How this variant is produced"
              }
            },
            {
              "name": "Related Technology Name",
              "description": "Detailed description",
              "element_type": "related_technology",
              "custom_fields": {
                "relationship": "How this technology relates to the weapon",
                "purpose": "Purpose of this related technology",
                "development": "How this technology was developed",
                "integration": "How it integrates with the weapon",
                "limitations": "Limitations or drawbacks"
              }
            }
          ]
        }
        ```
        """,

        # Infrastructure
        'cat_technological_infrastructure': """
        IMPORTANT - Use the following structure for the main infrastructure system:
        ```json
        {
          "parent": {
            "name": "Infrastructure System Name",
            "description": "Detailed description",
            "element_type": "infrastructure_system",
            "custom_fields": {
              "system_type": "transportation|energy|water|communication|waste|defense|public|magical|other",
              "coverage": "Areas covered by this infrastructure",
              "technology_level": "Level of technology used in this system",
              "maintenance": "How this system is maintained",
              "vulnerabilities": "Weaknesses or vulnerabilities in the system"
            }
          },
          "sub_elements": [
            {
              "name": "Facility Name",
              "description": "Detailed description",
              "element_type": "facility",
              "custom_fields": {
                "facility_type": "production|distribution|storage|control|maintenance|other",
                "location": "Where this facility is located",
                "capacity": "Capacity or output of this facility",
                "staff": "Personnel required to operate this facility",
                "security": "Security measures protecting this facility"
              }
            },
            {
              "name": "Route Network Name",
              "description": "Detailed description",
              "element_type": "route_network",
              "custom_fields": {
                "network_type": "roads|railways|waterways|airways|pipelines|power_lines|data_lines|other",
                "coverage": "Areas connected by this network",
                "capacity": "Capacity of this network",
                "condition": "Current condition of this network",
                "key_junctions": "Important connection points in this network"
              }
            }
          ]
        }
        ```
        """,

        # Sciences & Knowledge
        'cat_technological_sciences': """
        IMPORTANT - Use the following structure for the main field of study:
        ```json
        {
          "parent": {
            "name": "Field of Study Name",
            "description": "Detailed description",
            "element_type": "field_of_study",
            "custom_fields": {
              "field_type": "natural_science|social_science|formal_science|applied_science|humanities|magical_study|forbidden_knowledge|other",
              "key_concepts": "Core concepts or principles of this field",
              "methodologies": "Research methods used in this field",
              "notable_discoveries": "Important discoveries or advancements",
              "institutions": "Organizations that study or teach this field"
            }
          },
          "sub_elements": [
            {
              "name": "Technology Name",
              "description": "Detailed description",
              "element_type": "technology",
              "custom_fields": {
                "purpose": "Purpose of this technology",
                "development": "How this technology was developed",
                "applications": "Practical applications of this technology",
                "limitations": "Limitations or drawbacks",
                "impact": "Impact on society or the field"
              }
            },
            {
              "name": "Knowledge Repository Name",
              "description": "Detailed description",
              "element_type": "knowledge_repository",
              "custom_fields": {
                "repository_type": "library|archive|database|collection|other",
                "contents": "What knowledge is stored here",
                "access": "Who can access this repository and how",
                "organization": "How the knowledge is organized",
                "preservation": "Methods used to preserve the knowledge"
              }
            }
          ]
        }
        ```
        """
    }

    # Handle Flora & Fauna category specially - it could be plants or animals
    if category_id == 'cat_physical_flora_fauna':
        return """
        IMPORTANT - For Flora (plants), use the following structure:
        ```json
        {
          "parent": {
            "name": "Plant Name",
            "description": "Detailed description",
            "element_type": "plant",
            "custom_fields": {
              "plant_type": "tree|shrub|flower|grass|crop|fungus|aquatic|other",
              "appearance": "What the plant looks like",
              "habitat": "Where the plant typically grows",
              "properties": "Special properties (medicinal, magical, etc.)",
              "cultivation": "How it's cultivated or harvested"
            }
          },
          "sub_elements": [
            {
              "name": "Plant Variant Name",
              "description": "Detailed description",
              "element_type": "plant_variant",
              "custom_fields": {
                "variant_type": "subspecies|cultivar|mutation|regional_variant|other",
                "differences": "How this variant differs from the main plant",
                "habitat": "Where this variant typically grows",
                "rarity": "How common or rare this variant is",
                "unique_properties": "Special properties unique to this variant"
              }
            }
          ]
        }
        ```

        IMPORTANT - For Fauna (animals), use the following structure:
        ```json
        {
          "parent": {
            "name": "Animal Name",
            "description": "Detailed description",
            "element_type": "animal",
            "custom_fields": {
              "animal_type": "mammal|bird|reptile|amphibian|fish|insect|arachnid|other",
              "habitat": "Where this animal typically lives",
              "appearance": "What this animal looks like",
              "behavior": "How this animal behaves",
              "diet": "What this animal eats",
              "lifecycle": "Reproduction, lifespan, etc."
            }
          },
          "sub_elements": [
            {
              "name": "Animal Variant Name",
              "description": "Detailed description",
              "element_type": "animal_variant",
              "custom_fields": {
                "variant_type": "breed|subspecies|regional_variant|mutation|other",
                "differences": "How this variant differs from the main animal",
                "habitat": "Where this variant typically lives",
                "rarity": "How common or rare this variant is",
                "unique_traits": "Special traits unique to this variant"
              }
            }
          ]
        }
        ```
        """

    # Return the template for the category, or a generic template if not found
    return templates.get(category_id, """
    IMPORTANT - Use the following structure for the main element:
    ```json
    {
      "parent": {
        "name": "Element Name",
        "description": "Detailed description",
        "element_type": "appropriate_type",
        "custom_fields": {
          "field1": "Value for field1",
          "field2": "Value for field2",
          "field3": "Value for field3"
        }
      },
      "sub_elements": [
        {
          "name": "Sub-Element Name",
          "description": "Detailed description",
          "element_type": "sub_element_type",
          "custom_fields": {
            "field1": "Value for field1",
            "field2": "Value for field2",
            "field3": "Value for field3"
          }
        }
      ]
    }
    ```

    Make sure to use appropriate element_type values and custom_fields that make sense for the category.
    """)

# ============= Pydantic Models =============

class CategoryCustomization(BaseModel):
    category_id: str
    is_enabled: bool
    display_order: Optional[int] = None

class Relationship(BaseModel):
    relationship_id: Optional[str] = None
    source_element_id: str
    target_element_id: str
    relationship_type: str
    description: Optional[str] = ""

class TableSchema(BaseModel):
    schema_id: Optional[str] = None
    book_id: str
    category_id: str
    name: str
    columns: List[str]
    column_types: List[str]

class TableRow(BaseModel):
    row_id: Optional[str] = None
    element_id: str
    schema_id: str
    row_values: List[str]
    display_order: Optional[int] = None

class MapData(BaseModel):
    map_id: Optional[str] = None
    book_id: str
    name: str
    description: Optional[str] = ""
    base_image_path: Optional[str] = ""
    width: Optional[int] = None
    height: Optional[int] = None

class MapElement(BaseModel):
    map_element_id: Optional[str] = None
    map_id: str
    world_element_id: str
    element_type: str  # 'point', 'area', 'path'
    coordinates: str   # Serialized coordinate data
    style_data: Optional[str] = ""
    label: Optional[str] = ""

# ============= Aspect Categories Endpoints =============

@router.get("/categories")
def get_all_categories(user_id: str = Depends(get_current_user)):
    """Fetches all world aspect categories."""
    try:
        categories = aspect_repo.get_aspect_categories()
        return categories
    except Exception as e:
        logger.error(f"Error fetching categories: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch categories: {str(e)}")

@router.get("/categories/{category_id}")
def get_category(category_id: str, user_id: str = Depends(get_current_user)):
    """Fetches a specific world aspect category by ID."""
    try:
        category = aspect_repo.get_category_by_id(category_id)
        return category
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Error fetching category: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch category: {str(e)}")

@router.post("/categories")
def create_category(category_data: dict, user_id: str = Depends(get_current_user)):
    """Creates a new world aspect category."""
    try:
        category = aspect_repo.create_category(category_data)
        return category
    except Exception as e:
        logger.error(f"Error creating category: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to create category: {str(e)}")

@router.put("/categories/{category_id}")
def update_category(category_id: str, category_data: dict, user_id: str = Depends(get_current_user)):
    """Updates an existing world aspect category."""
    try:
        category = aspect_repo.update_category(category_id, category_data)
        return category
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Error updating category: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to update category: {str(e)}")

@router.delete("/categories/{category_id}")
def delete_category(category_id: str, user_id: str = Depends(get_current_user)):
    """Deletes a world aspect category."""
    try:
        success = aspect_repo.delete_category(category_id)
        if not success:
            raise HTTPException(status_code=404, detail=f"Category with ID {category_id} not found")
        return {"status": "success", "message": f"Category {category_id} deleted"}
    except Exception as e:
        logger.error(f"Error deleting category: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to delete category: {str(e)}")

# ============= Category Customization Endpoints =============

@router.get("/customizations")
def get_category_customizations(book_id: str, user_id: str = Depends(get_current_user)):
    """Fetches category customizations for a book."""
    try:
        customizations = aspect_repo.get_category_customizations(book_id)
        return customizations
    except Exception as e:
        logger.error(f"Error fetching customizations: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch customizations: {str(e)}")

@router.put("/customizations/{category_id}")
def update_category_customization(
    book_id: str,
    category_id: str,
    customization: CategoryCustomization,
    user_id: str = Depends(get_current_user)
):
    """Updates a category customization for a book."""
    try:
        result = aspect_repo.update_category_customization(
            book_id,
            category_id,
            customization.is_enabled,
            customization.display_order
        )
        return result
    except Exception as e:
        logger.error(f"Error updating customization: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to update customization: {str(e)}")

@router.post("/customizations/reset")
def reset_category_customizations(book_id: str, user_id: str = Depends(get_current_user)):
    """Resets category customizations to default based on book genre."""
    try:
        success = aspect_repo.reset_category_customizations(book_id)
        return {"status": "success", "message": "Category customizations reset to defaults"}
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Error resetting customizations: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to reset customizations: {str(e)}")

@router.get("/visible-categories")
def get_visible_categories(book_id: str, user_id: str = Depends(get_current_user)):
    """Fetches all visible (enabled) categories for a book."""
    try:
        categories = aspect_repo.get_visible_categories(book_id)
        return categories
    except Exception as e:
        logger.error(f"Error fetching visible categories: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch visible categories: {str(e)}")

# ============= Element Endpoints =============

@router.get("/elements")
def get_elements(
    book_id: str,
    category: str = None,
    template_id: str = None,
    user_id: str = Depends(get_current_user)
):
    """Fetches all world elements for a book, optionally filtered by category or template_id."""
    try:
        conn = get_db_connection()
        try:
            with conn.cursor() as cursor:
                # Build the query based on filters provided
                query = """
                SELECT * FROM world_elements
                WHERE book_id = %(book_id)s
                """
                params = {'book_id': book_id}

                # Add category filter if provided
                if category:
                    query += " AND category = %(category)s"
                    params['category'] = category

                # Add template_id filter if provided
                if template_id:
                    query += " AND template_id = %(template_id)s"
                    params['template_id'] = template_id

                cursor.execute(query, params)
                elements = cursor.fetchall()

                # Process each element
                result = []
                for element in elements:
                    # Convert attributes to custom_fields
                    if 'attributes' in element and element['attributes']:
                        try:
                            if isinstance(element['attributes'], str):
                                element['custom_fields'] = json.loads(element['attributes'])
                            else:
                                element['custom_fields'] = element['attributes']
                        except json.JSONDecodeError:
                            element['custom_fields'] = {}
                            logger.error(f"Error parsing attributes for element {element['element_id']}")
                    else:
                        element['custom_fields'] = {}

                    # Remove attributes field
                    if 'attributes' in element:
                        del element['attributes']

                    # Check if this element has children
                    cursor.execute(
                        "SELECT COUNT(*) FROM world_elements WHERE parent_id = %s",
                        (element['element_id'],)
                    )
                    count = cursor.fetchone()['count']
                    element['has_children'] = count > 0

                    result.append(element)

                return result
        finally:
            conn.close()
    except Exception as e:
        logger.error(f"Error fetching elements: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch elements: {str(e)}")

@router.get("/elements/{element_id}")
def get_element(book_id: str, element_id: str, user_id: str = Depends(get_current_user)):
    """Fetches a specific world element by ID."""
    try:
        # Verify the element belongs to the book
        conn = get_db_connection()
        with conn.cursor() as cursor:
            cursor.execute(
                "SELECT * FROM world_elements WHERE element_id = %s AND book_id = %s",
                (element_id, book_id)
            )
            element = cursor.fetchone()

        if not element:
            raise HTTPException(status_code=404, detail=f"Element with ID {element_id} not found in book {book_id}")

        # Convert attributes JSON to dict if it exists
        if 'attributes' in element and element['attributes']:
            try:
                if isinstance(element['attributes'], str):
                    element['custom_fields'] = json.loads(element['attributes'])
                else:
                    element['custom_fields'] = element['attributes']
            except json.JSONDecodeError:
                element['custom_fields'] = {}
                logger.error(f"Error parsing attributes for element {element_id}")
        else:
            element['custom_fields'] = {}

        # Remove attributes field and add custom_fields
        if 'attributes' in element:
            del element['attributes']

        return element
    except Exception as e:
        logger.error(f"Error fetching element: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch element: {str(e)}")

@router.post("/elements")
def create_element(book_id: str, element_data: dict, user_id: str = Depends(get_current_user)):
    """Creates a new world element."""
    try:
        # Validate required fields
        required_fields = ['name', 'element_type']
        for field in required_fields:
            if field not in element_data:
                raise HTTPException(status_code=400, detail=f"Missing required field: {field}")

        # Handle category_id -> category conversion
        if 'category_id' in element_data and 'category' not in element_data:
            element_data['category'] = element_data.pop('category_id')

        # Extract custom fields
        custom_fields = element_data.pop('custom_fields', {})

        # Add book_id to element data
        element_data['book_id'] = book_id

        # Create the element
        conn = get_db_connection()
        try:
            with conn.cursor() as cursor:
                # Generate a unique ID
                element_id = f"elem_{uuid.uuid4()}"
                element_data['element_id'] = element_id

                # Convert custom fields to JSON
                attributes_json = json.dumps(custom_fields)

                # Build the SQL query dynamically based on the fields provided
                fields = list(element_data.keys())
                placeholders = [f"%({field})s" for field in fields]

                # Add attributes field if it's not already in the fields
                if 'attributes' not in fields:
                    fields.append('attributes')
                    placeholders.append('%(attributes)s')

                query = f"""
                INSERT INTO world_elements ({', '.join(fields)})
                VALUES ({', '.join(placeholders)})
                RETURNING *
                """

                # Add attributes to the parameters if it's not already there
                params = {**element_data}
                if 'attributes' not in params:
                    params['attributes'] = attributes_json

                cursor.execute(query, params)
                result = cursor.fetchone()
                conn.commit()

                # Convert attributes back to custom_fields
                if 'attributes' in result and result['attributes']:
                    try:
                        if isinstance(result['attributes'], str):
                            result['custom_fields'] = json.loads(result['attributes'])
                        else:
                            result['custom_fields'] = result['attributes']
                    except json.JSONDecodeError:
                        result['custom_fields'] = {}
                else:
                    result['custom_fields'] = {}

                # Remove attributes field
                if 'attributes' in result:
                    del result['attributes']

                return result
        except Exception as e:
            conn.rollback()
            logger.error(f"Error creating element: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to create element: {str(e)}")
        finally:
            conn.close()
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating element: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to create element: {str(e)}")

@router.put("/elements/{element_id}")
def update_element(book_id: str, element_id: str, element_data: dict, user_id: str = Depends(get_current_user)):
    """Updates an existing world element."""
    try:
        # Verify the element belongs to the book
        conn = get_db_connection()
        with conn.cursor() as cursor:
            cursor.execute(
                "SELECT * FROM world_elements WHERE element_id = %s AND book_id = %s",
                (element_id, book_id)
            )
            existing_element = cursor.fetchone()

        if not existing_element:
            raise HTTPException(status_code=404, detail=f"Element with ID {element_id} not found in book {book_id}")

        # Extract custom fields
        custom_fields = element_data.pop('custom_fields', None)

        # Update the element
        try:
            with conn.cursor() as cursor:
                # Build the SET clause dynamically
                set_clauses = []
                params = {'element_id': element_id, 'book_id': book_id}

                for key, value in element_data.items():
                    if key not in ['element_id', 'book_id']:  # Skip these fields
                        set_clauses.append(f"{key} = %({key})s")
                        params[key] = value

                # Handle custom fields separately
                if custom_fields is not None:
                    # Get existing attributes
                    cursor.execute(
                        "SELECT attributes FROM world_elements WHERE element_id = %s",
                        (element_id,)
                    )
                    result = cursor.fetchone()
                    existing_attributes = {}

                    if result and result['attributes']:
                        try:
                            if isinstance(result['attributes'], str):
                                existing_attributes = json.loads(result['attributes'])
                            else:
                                existing_attributes = result['attributes']
                        except json.JSONDecodeError:
                            pass

                    # Merge with new custom fields
                    merged_attributes = {**existing_attributes, **custom_fields}
                    set_clauses.append("attributes = %(attributes)s")
                    params['attributes'] = json.dumps(merged_attributes)

                # Add updated_at
                set_clauses.append("updated_at = CURRENT_TIMESTAMP")

                if not set_clauses:
                    return existing_element  # Nothing to update

                query = f"""
                UPDATE world_elements
                SET {', '.join(set_clauses)}
                WHERE element_id = %(element_id)s AND book_id = %(book_id)s
                RETURNING *
                """

                cursor.execute(query, params)
                result = cursor.fetchone()
                conn.commit()

                # Convert attributes back to custom_fields
                if 'attributes' in result and result['attributes']:
                    try:
                        if isinstance(result['attributes'], str):
                            result['custom_fields'] = json.loads(result['attributes'])
                        else:
                            result['custom_fields'] = result['attributes']
                    except json.JSONDecodeError:
                        result['custom_fields'] = {}
                else:
                    result['custom_fields'] = {}

                # Remove attributes field
                if 'attributes' in result:
                    del result['attributes']

                return result
        except Exception as e:
            conn.rollback()
            logger.error(f"Error updating element: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to update element: {str(e)}")
        finally:
            conn.close()
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating element: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to update element: {str(e)}")

@router.delete("/elements/{element_id}")
def delete_element(book_id: str, element_id: str, user_id: str = Depends(get_current_user)):
    """Deletes a world element and its children."""
    try:
        # Verify the element belongs to the book
        conn = get_db_connection()
        with conn.cursor() as cursor:
            cursor.execute(
                "SELECT * FROM world_elements WHERE element_id = %s AND book_id = %s",
                (element_id, book_id)
            )
            element = cursor.fetchone()

        if not element:
            raise HTTPException(status_code=404, detail=f"Element with ID {element_id} not found in book {book_id}")

        # Delete the element and its children recursively
        try:
            with conn.cursor() as cursor:
                # First get all child elements
                cursor.execute(
                    """WITH RECURSIVE element_tree AS (
                        SELECT element_id FROM world_elements WHERE element_id = %s
                        UNION ALL
                        SELECT e.element_id FROM world_elements e
                        JOIN element_tree et ON e.parent_id = et.element_id
                    )
                    SELECT element_id FROM element_tree
                    """,
                    (element_id,)
                )
                elements_to_delete = [row['element_id'] for row in cursor.fetchall()]

                # Delete relationships involving these elements
                cursor.execute(
                    """DELETE FROM world_element_relationships
                    WHERE source_element_id = ANY(%s) OR target_element_id = ANY(%s)
                    """,
                    (elements_to_delete, elements_to_delete)
                )

                # Delete the elements
                cursor.execute(
                    "DELETE FROM world_elements WHERE element_id = ANY(%s)",
                    (elements_to_delete,)
                )

                conn.commit()

                return {"status": "success", "message": f"Element {element_id} and {len(elements_to_delete) - 1} child elements deleted"}
        except Exception as e:
            conn.rollback()
            logger.error(f"Error deleting element: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to delete element: {str(e)}")
        finally:
            conn.close()
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting element: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to delete element: {str(e)}")

@router.get("/elements/{parent_id}/children")
def get_child_elements(book_id: str, parent_id: str, user_id: str = Depends(get_current_user)):
    """Fetches all child elements for a specific parent element."""
    try:
        # Verify the parent element belongs to the book
        conn = get_db_connection()
        with conn.cursor() as cursor:
            cursor.execute(
                "SELECT * FROM world_elements WHERE element_id = %s AND book_id = %s",
                (parent_id, book_id)
            )
            parent = cursor.fetchone()

        if not parent:
            raise HTTPException(status_code=404, detail=f"Parent element with ID {parent_id} not found in book {book_id}")

        # Get all child elements
        with conn.cursor() as cursor:
            cursor.execute(
                "SELECT * FROM world_elements WHERE parent_id = %s AND book_id = %s",
                (parent_id, book_id)
            )
            children = cursor.fetchall()

            # Process each child element
            result = []
            for child in children:
                # Convert attributes to custom_fields
                if 'attributes' in child and child['attributes']:
                    try:
                        if isinstance(child['attributes'], str):
                            child['custom_fields'] = json.loads(child['attributes'])
                        else:
                            child['custom_fields'] = child['attributes']
                    except json.JSONDecodeError:
                        child['custom_fields'] = {}
                        logger.error(f"Error parsing attributes for element {child['element_id']}")
                else:
                    child['custom_fields'] = {}

                # Remove attributes field
                if 'attributes' in child:
                    del child['attributes']

                # Check if this child has children
                cursor.execute(
                    "SELECT COUNT(*) FROM world_elements WHERE parent_id = %s",
                    (child['element_id'],)
                )
                count = cursor.fetchone()['count']
                child['has_children'] = count > 0

                result.append(child)

        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching child elements: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch child elements: {str(e)}")

# ============= Relationship Endpoints =============

@router.get("/relationships")
def get_relationships(book_id: str, user_id: str = Depends(get_current_user)):
    """Fetches all relationships for elements in a book."""
    try:
        relationships = relationship_repo.get_relationships(book_id)
        return relationships
    except Exception as e:
        logger.error(f"Error fetching relationships: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch relationships: {str(e)}")

@router.get("/elements/{element_id}/relationships")
def get_element_relationships(book_id: str, element_id: str, user_id: str = Depends(get_current_user)):
    """Fetches all relationships for a specific element."""
    try:
        relationships = relationship_repo.get_element_relationships(element_id)
        return relationships
    except Exception as e:
        logger.error(f"Error fetching element relationships: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch element relationships: {str(e)}")

@router.post("/relationships")
def create_relationship(book_id: str, relationship: Relationship, user_id: str = Depends(get_current_user)):
    """Creates a new relationship between world elements."""
    try:
        result = relationship_repo.create_relationship(relationship.dict(exclude_unset=True))
        return result
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error creating relationship: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to create relationship: {str(e)}")

@router.put("/relationships/{relationship_id}")
def update_relationship(
    book_id: str,
    relationship_id: str,
    relationship: Relationship,
    user_id: str = Depends(get_current_user)
):
    """Updates an existing relationship."""
    try:
        result = relationship_repo.update_relationship(relationship_id, relationship.dict(exclude_unset=True))
        return result
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Error updating relationship: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to update relationship: {str(e)}")

@router.delete("/relationships/{relationship_id}")
def delete_relationship(book_id: str, relationship_id: str, user_id: str = Depends(get_current_user)):
    """Deletes a relationship."""
    try:
        success = relationship_repo.delete_relationship(relationship_id)
        if not success:
            raise HTTPException(status_code=404, detail=f"Relationship with ID {relationship_id} not found")
        return {"status": "success", "message": f"Relationship {relationship_id} deleted"}
    except Exception as e:
        logger.error(f"Error deleting relationship: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to delete relationship: {str(e)}")

@router.get("/relationship-types")
def get_relationship_types(user_id: str = Depends(get_current_user)):
    """Returns a list of common relationship types."""
    try:
        types = relationship_repo.get_relationship_types()
        return types
    except Exception as e:
        logger.error(f"Error fetching relationship types: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch relationship types: {str(e)}")

# ============= Table Schema Endpoints =============

@router.get("/tables/schemas")
def get_table_schemas(book_id: str, user_id: str = Depends(get_current_user)):
    """Fetches all table schemas for a book."""
    try:
        schemas = table_repo.get_table_schemas(book_id)
        return schemas
    except Exception as e:
        logger.error(f"Error fetching table schemas: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch table schemas: {str(e)}")

@router.get("/tables/schemas/{schema_id}")
def get_table_schema(book_id: str, schema_id: str, user_id: str = Depends(get_current_user)):
    """Fetches a specific table schema by ID."""
    try:
        schema = table_repo.get_table_schema(schema_id)
        return schema
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Error fetching table schema: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch table schema: {str(e)}")

@router.post("/tables/schemas")
def create_table_schema(book_id: str, schema: TableSchema, user_id: str = Depends(get_current_user)):
    """Creates a new table schema."""
    try:
        # Ensure the book_id in the path matches the one in the schema
        if schema.book_id != book_id:
            raise HTTPException(status_code=400, detail="Book ID in path does not match book ID in schema")

        result = table_repo.create_table_schema(schema.dict(exclude_unset=True))
        return result
    except Exception as e:
        logger.error(f"Error creating table schema: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to create table schema: {str(e)}")

@router.put("/tables/schemas/{schema_id}")
def update_table_schema(
    book_id: str,
    schema_id: str,
    schema: TableSchema,
    user_id: str = Depends(get_current_user)
):
    """Updates an existing table schema."""
    try:
        result = table_repo.update_table_schema(schema_id, schema.dict(exclude_unset=True))
        return result
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Error updating table schema: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to update table schema: {str(e)}")

@router.delete("/tables/schemas/{schema_id}")
def delete_table_schema(book_id: str, schema_id: str, user_id: str = Depends(get_current_user)):
    """Deletes a table schema and all its rows."""
    try:
        success = table_repo.delete_table_schema(schema_id)
        if not success:
            raise HTTPException(status_code=404, detail=f"Table schema with ID {schema_id} not found")
        return {"status": "success", "message": f"Table schema {schema_id} deleted"}
    except Exception as e:
        logger.error(f"Error deleting table schema: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to delete table schema: {str(e)}")

# ============= Table Row Endpoints =============

@router.get("/elements/{element_id}/table-rows")
def get_table_rows(book_id: str, element_id: str, user_id: str = Depends(get_current_user)):
    """Fetches all table rows for a specific element."""
    try:
        rows = table_repo.get_table_rows(element_id)
        return rows
    except Exception as e:
        logger.error(f"Error fetching table rows: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch table rows: {str(e)}")

@router.post("/tables/rows")
def create_table_row(book_id: str, row: TableRow, user_id: str = Depends(get_current_user)):
    """Creates a new table row."""
    try:
        result = table_repo.create_table_row(row.dict(exclude_unset=True))
        return result
    except Exception as e:
        logger.error(f"Error creating table row: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to create table row: {str(e)}")

@router.put("/tables/rows/{row_id}")
def update_table_row(
    book_id: str,
    row_id: str,
    row: TableRow,
    user_id: str = Depends(get_current_user)
):
    """Updates an existing table row."""
    try:
        result = table_repo.update_table_row(row_id, row.dict(exclude_unset=True))
        return result
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Error updating table row: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to update table row: {str(e)}")

@router.delete("/tables/rows/{row_id}")
def delete_table_row(book_id: str, row_id: str, user_id: str = Depends(get_current_user)):
    """Deletes a table row."""
    try:
        success = table_repo.delete_table_row(row_id)
        if not success:
            raise HTTPException(status_code=404, detail=f"Table row with ID {row_id} not found")
        return {"status": "success", "message": f"Table row {row_id} deleted"}
    except Exception as e:
        logger.error(f"Error deleting table row: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to delete table row: {str(e)}")

@router.post("/elements/{element_id}/table-rows/reorder")
def reorder_table_rows(
    book_id: str,
    element_id: str,
    row_order: List[str],
    user_id: str = Depends(get_current_user)
):
    """Updates the display order of table rows for an element."""
    try:
        success = table_repo.reorder_table_rows(element_id, row_order)
        return {"status": "success", "message": "Table rows reordered"}
    except Exception as e:
        logger.error(f"Error reordering table rows: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to reorder table rows: {str(e)}")

# ============= Map Endpoints =============

@router.get("/maps")
def get_maps(book_id: str, user_id: str = Depends(get_current_user)):
    """Fetches all maps for a book."""
    try:
        maps = map_repo.get_maps(book_id)
        return maps
    except Exception as e:
        logger.error(f"Error fetching maps: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch maps: {str(e)}")

@router.get("/maps/{map_id}")
def get_map(book_id: str, map_id: str, user_id: str = Depends(get_current_user)):
    """Fetches a specific map by ID."""
    try:
        map_data = map_repo.get_map(map_id)
        return map_data
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Error fetching map: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch map: {str(e)}")

@router.post("/maps")
def create_map(book_id: str, map_data: MapData, user_id: str = Depends(get_current_user)):
    """Creates a new map."""
    try:
        # Ensure the book_id in the path matches the one in the map data
        if map_data.book_id != book_id:
            raise HTTPException(status_code=400, detail="Book ID in path does not match book ID in map data")

        result = map_repo.create_map(map_data.dict(exclude_unset=True))
        return result
    except Exception as e:
        logger.error(f"Error creating map: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to create map: {str(e)}")

@router.put("/maps/{map_id}")
def update_map(
    book_id: str,
    map_id: str,
    map_data: MapData,
    user_id: str = Depends(get_current_user)
):
    """Updates an existing map."""
    try:
        result = map_repo.update_map(map_id, map_data.dict(exclude_unset=True))
        return result
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Error updating map: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to update map: {str(e)}")

@router.delete("/maps/{map_id}")
def delete_map(book_id: str, map_id: str, user_id: str = Depends(get_current_user)):
    """Deletes a map and all its elements."""
    try:
        success = map_repo.delete_map(map_id)
        if not success:
            raise HTTPException(status_code=404, detail=f"Map with ID {map_id} not found")
        return {"status": "success", "message": f"Map {map_id} deleted"}
    except Exception as e:
        logger.error(f"Error deleting map: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to delete map: {str(e)}")

# ============= Map Element Endpoints =============

@router.get("/maps/{map_id}/elements")
def get_map_elements(book_id: str, map_id: str, user_id: str = Depends(get_current_user)):
    """Fetches all elements for a specific map."""
    try:
        elements = map_repo.get_map_elements(map_id)
        return elements
    except Exception as e:
        logger.error(f"Error fetching map elements: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch map elements: {str(e)}")

@router.post("/maps/{map_id}/elements")
def create_map_element(book_id: str, map_id: str, element: MapElement, user_id: str = Depends(get_current_user)):
    """Creates a new map element."""
    try:
        # Ensure the map_id in the path matches the one in the element
        if element.map_id != map_id:
            raise HTTPException(status_code=400, detail="Map ID in path does not match map ID in element")

        result = map_repo.create_map_element(element.dict(exclude_unset=True))
        return result
    except Exception as e:
        logger.error(f"Error creating map element: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to create map element: {str(e)}")

@router.put("/maps/{map_id}/elements/{map_element_id}")
def update_map_element(
    book_id: str,
    map_id: str,
    map_element_id: str,
    element: MapElement,
    user_id: str = Depends(get_current_user)
):
    """Updates an existing map element."""
    try:
        result = map_repo.update_map_element(map_element_id, element.dict(exclude_unset=True))
        return result
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Error updating map element: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to update map element: {str(e)}")

@router.delete("/maps/{map_id}/elements/{map_element_id}")
def delete_map_element(book_id: str, map_id: str, map_element_id: str, user_id: str = Depends(get_current_user)):
    """Deletes a map element."""
    try:
        success = map_repo.delete_map_element(map_element_id)
        if not success:
            raise HTTPException(status_code=404, detail=f"Map element with ID {map_element_id} not found")
        return {"status": "success", "message": f"Map element {map_element_id} deleted"}
    except Exception as e:
        logger.error(f"Error deleting map element: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to delete map element: {str(e)}")

# ============= AI Generation Endpoints =============

class GenerateElementRequest(BaseModel):
    prompt: str
    count: int = Field(1, ge=1, le=20)  # Increased max from 5 to 20
    category_id: str
    parent_id: Optional[str] = None
    # New fields for structured generation modes
    nested_mode: bool = False
    multi_level_mode: bool = False
    batch_mode: bool = False
    # Additional parameters for nested mode
    sub_element_count: Optional[int] = 3
    # Additional parameters for multi-level mode
    nesting_depth: Optional[int] = 1
    level_counts: Optional[List[int]] = None

def parse_user_requirements(prompt: str) -> dict:
    """
    Parses the user's prompt to extract specific requirements like numbers and types of elements.
    Returns a dictionary with the extracted requirements.
    """
    import re

    requirements = {
        "has_specific_requirements": False,
        "element_counts": {},
        "raw_text": prompt
    }

    # Look for patterns like "2 locations and 1 natural feature"
    # This is a simple implementation - could be enhanced with NLP in the future
    number_words = {
        "one": 1, "two": 2, "three": 3, "four": 4, "five": 5,
        "six": 6, "seven": 7, "eight": 8, "nine": 9, "ten": 10
    }

    # Convert number words to digits
    for word, number in number_words.items():
        prompt = prompt.replace(f" {word} ", f" {number} ")

    # Common element types to look for
    element_types = [
        "location", "locations", "natural feature", "natural features",
        "region", "regions", "city", "cities", "town", "towns",
        "village", "villages", "mountain", "mountains", "river", "rivers",
        "lake", "lakes", "forest", "forests", "desert", "deserts",
        "canyon", "canyons", "ocean", "oceans", "sea", "seas",
        "island", "islands", "continent", "continents", "kingdom", "kingdoms",
        "empire", "empires", "settlement", "settlements", "outpost", "outposts",
        "ruin", "ruins", "landmark", "landmarks", "geography", "geographies"
    ]

    # Look for patterns like "3 cities" or "2 mountains"
    for element_type in element_types:
        # Look for digit followed by space followed by element type
        pattern = r'(\d+)\s+' + element_type
        matches = re.findall(pattern, prompt)
        if matches:
            requirements["has_specific_requirements"] = True
            singular_type = element_type.rstrip('s')  # Convert plural to singular
            requirements["element_counts"][singular_type] = int(matches[0])

    return requirements


@router.post("/generate")
async def generate_world_elements(
    book_id: str,
    request: GenerateElementRequest,
    user_id: str = Depends(get_current_user)
):
    """
    Generate AI world elements based on a prompt.
    Returns structured elements that can be added to the world.
    """
    try:
        from app.ai.provider import get_text_provider
        import uuid
        import json
        import re

        # Initialize temp_id_map at the beginning of the function
        temp_id_map = {}

        prompt = request.prompt
        count = request.count
        category_id = request.category_id
        parent_id = request.parent_id

        logger.debug(f"Generating {count} world elements for book {book_id}, category {category_id}, parent_id: {parent_id or 'none'}")

        # Get book information and context
        conn = get_db_connection()
        book_info = {}
        characters = []
        world_elements = []
        parent_element = None

        try:
            # Get book details
            with conn.cursor() as cursor:
                cursor.execute("SELECT * FROM books WHERE book_id = %s", (book_id,))
                book = cursor.fetchone()
                if not book:
                    raise HTTPException(status_code=404, detail="Book not found")

                book_info = {
                    "title": book.get('title', ''),
                    "genre": book.get('genre', ''),
                    "description": book.get('description', '')
                }

                # Get characters
                cursor.execute("SELECT * FROM characters WHERE book_id = %s", (book_id,))
                characters = cursor.fetchall()

                # Get category information from the category_id
                # Since we don't have a world_building_categories table, we'll extract info from the ID
                # Format is typically cat_physical_locations, cat_cultures, etc.
                category_name = category_id.replace('cat_', '').replace('_', ' ').title()
                category = {
                    'category_id': category_id,
                    'name': category_name,
                    'description': f"Elements related to {category_name}"
                }
                logger.debug(f"Using category: {category}")

                # Get ALL existing elements from ALL categories
                cursor.execute(
                    "SELECT * FROM world_elements WHERE book_id = %s",
                    (book_id,)
                )
                world_elements = cursor.fetchall()
                logger.debug(f"Fetched {len(world_elements)} world elements from ALL categories for AI context")

                # If parent_id is provided, get the parent element
                if parent_id:
                    cursor.execute(
                        "SELECT * FROM world_elements WHERE element_id = %s AND book_id = %s",
                        (parent_id, book_id)
                    )
                    parent_element = cursor.fetchone()

                    if not parent_element:
                        raise HTTPException(status_code=404, detail=f"Parent element with ID {parent_id} not found")
        finally:
            conn.close()

        # Prepare character data for the prompt - include ALL characters with full profiles
        character_data = [{
            "name": char.get('name', ''),
            "role": char.get('role', ''),
            "description": char.get('description', ''),
            "backstory": char.get('backstory', ''),
            "arc": char.get('arc', ''),
            "gender_identity": char.get('gender_identity', ''),
            "sexual_orientation": char.get('sexual_orientation', ''),
            "traits": char.get('traits', []),
            "goals": char.get('goals', []),
            "fears": char.get('fears', [])
        } for char in characters]  # No limit on characters

        # Prepare ALL existing elements data for context - no limits
        element_data = [{
            "name": elem.get('name', ''),
            "description": elem.get('description', ''),
            "element_type": elem.get('element_type', ''),
            "custom_fields": elem.get('custom_fields', {}),
            "attributes": elem.get('attributes', {}),
            "importance": elem.get('importance', ''),
            "tags": elem.get('tags', []),
            "has_children": elem.get('has_children', False),
            "parent_id": elem.get('parent_id', None),
            "category_id": elem.get('category_id', '')
        } for elem in world_elements]  # Include ALL elements, not just from the same category

        # Prepare parent element data if available
        parent_data = None
        if parent_element:
            parent_data = {
                "element_id": parent_element.get('element_id', ''),
                "name": parent_element.get('name', ''),
                "description": parent_element.get('description', ''),
                "element_type": parent_element.get('element_type', '')
            }

        # Get the appropriate template for this category
        template_structure = get_template_for_category(category_id)

        # Determine the generation mode and format instructions
        generation_mode = "standard"
        if request.nested_mode:
            generation_mode = "nested"
            count = 1  # Override count for nested mode
            sub_element_count = request.sub_element_count or 3
        elif request.multi_level_mode:
            generation_mode = "multi_level"
            count = 1  # Override count for multi-level mode
            nesting_depth = request.nesting_depth or 1
            level_counts = request.level_counts or [3, 2, 2]
        elif request.batch_mode:
            generation_mode = "batch"

        # Parse the user's prompt to extract specific requirements
        user_requirements = parse_user_requirements(prompt)

        # Log the extracted requirements
        if user_requirements["has_specific_requirements"]:
            logger.debug(f"Detected specific requirements in prompt: {user_requirements['element_counts']}")

        # Create the prompt for the AI with user's creative input first and emphasized
        ai_prompt = f"""
        {prompt}

        Based on the above prompt, generate {count} detailed {category.get('name', 'world elements')} for a {book_info.get('genre', '')} book titled '{book_info.get('title', 'Untitled')}'.

        IMPORTANT: Follow the user's specific requirements exactly as stated in their prompt. Pay careful attention to:
        - The number of elements requested
        - The types of elements requested
        - Any specific relationships or structures mentioned
        """

        # Add specific requirements if detected
        if user_requirements["has_specific_requirements"]:
            ai_prompt += """

        SPECIFIC REQUIREMENTS DETECTED:
        """
            for element_type, count in user_requirements["element_counts"].items():
                ai_prompt += f"- Create exactly {count} {element_type}{'s' if count > 1 else ''}\n"

            ai_prompt += """
        You MUST fulfill these specific requirements exactly as stated.
        """

        # Add mode-specific instructions
        if generation_mode == "nested":
            # Calculate total sub-elements from user requirements if available
            total_sub_elements = sub_element_count
            if user_requirements["has_specific_requirements"]:
                total_sub_elements = sum(user_requirements["element_counts"].values())
                logger.debug(f"Adjusting sub-element count from {sub_element_count} to {total_sub_elements} based on user requirements")

            ai_prompt += f"""
        Create a complete hierarchical structure consisting of 1 parent {category.get('name', 'element')} and {total_sub_elements} sub-elements that belong to it.

        The parent element should be detailed and substantial enough to serve as a container or category for the sub-elements.

        The sub-elements should:
        1. Be clearly related to and logically fit under the parent element
        2. Form a coherent set that makes sense together
        3. Each have a distinct purpose or role within the parent structure
        4. Include appropriate details that connect them to the parent

        IMPORTANT: If the user's prompt specifies a certain number or type of sub-elements (e.g., "2 locations and 1 natural feature"),
        you MUST create exactly that number and type, regardless of the default settings. The user's specific requirements always take precedence.
        """
        elif generation_mode == "multi_level":
            # Adjust level counts based on user requirements if available
            adjusted_level_counts = level_counts.copy() if level_counts else [3, 2, 2]
            if user_requirements["has_specific_requirements"]:
                # For simplicity, we'll just use the total count for the top level
                # A more sophisticated implementation would parse hierarchical requirements
                total_elements = sum(user_requirements["element_counts"].values())
                adjusted_level_counts[0] = total_elements
                logger.debug(f"Adjusting top-level count from {level_counts[0]} to {total_elements} based on user requirements")

            ai_prompt += f"""
        Create a hierarchical structure with {nesting_depth} levels of depth:
        - {adjusted_level_counts[0]} top-level elements
        """
            if nesting_depth >= 2 and len(adjusted_level_counts) >= 2:
                ai_prompt += f"- Each top-level element should have {adjusted_level_counts[1]} sub-elements\n"
            if nesting_depth >= 3 and len(adjusted_level_counts) >= 3:
                ai_prompt += f"- Each sub-element should have {adjusted_level_counts[2]} sub-sub-elements\n"

            ai_prompt += """
        IMPORTANT: If the user's prompt specifies a certain number or type of elements at any level (e.g., "3 regions each with 2 locations"),
        you MUST create exactly that number and type, regardless of the default settings. The user's specific requirements always take precedence.

        Pay special attention to the exact wording of the user's prompt and create elements that match their description precisely.
        """

        # Add format instructions based on the template
        ai_prompt += f"""

        {template_structure}

        Use the following details to inform your response:
        - Book description: {book_info.get('description', '')}
        - Category description: {category.get('description', '')}
        - Characters in the book: {json.dumps(character_data)}
        - Existing world elements: {json.dumps(element_data)}
        """

        # Add parent element context if applicable
        if parent_data:
            ai_prompt += f"""
        IMPORTANT - These elements should be created as sub-elements or children of the following parent element:
        Parent element: {json.dumps(parent_data)}

        The generated elements should be logically related to this parent and make sense as its sub-components, parts, or children.
        They should have a clear hierarchical relationship to the parent element.

        These elements should be designed to work well in a hierarchical structure. Each element should have a clear relationship to its parent and potentially to other elements at the same level.
        """

        # Add final instructions for the return format
        if generation_mode == "nested":
            ai_prompt += """
        Return the results as a JSON object with a "parent" object and a "sub_elements" array.

        IMPORTANT: Make sure the number and types of sub-elements match EXACTLY what the user requested in their prompt.
        """
        elif generation_mode == "multi_level":
            ai_prompt += """
        Return the results as a JSON object with an "elements" array containing the hierarchical structure.

        IMPORTANT: Make sure the number and types of elements at each level match EXACTLY what the user requested in their prompt.
        """
        else:
            # Adjust count based on user requirements if available
            adjusted_count = count
            if user_requirements["has_specific_requirements"]:
                total_elements = sum(user_requirements["element_counts"].values())
                adjusted_count = total_elements
                logger.debug(f"Adjusting element count from {count} to {total_elements} based on user requirements")

            ai_prompt += f"""
        Return the results as a JSON array with {adjusted_count} objects, each with these fields:
        - name: string
        - description: string (detailed description)
        - element_type: string (appropriate subcategory)
        - custom_fields: object with any additional properties

        IMPORTANT: Make sure the number and types of elements match EXACTLY what the user requested in their prompt.
        If the user asked for specific types (e.g., "2 mountains and 1 river"), create exactly those types in those quantities.
        """

        ai_prompt += """
        Make sure each element is unique, detailed, and fits the world of the book.

        FINAL CHECK: Before returning your response, verify that you have created EXACTLY the number and types of elements specified in the user's prompt.
        If the user asked for "2 locations and 1 natural feature", your response must contain exactly 2 locations and 1 natural feature - no more, no less.
        """

        # Get the AI provider
        text_provider = await get_text_provider(user_id)

        # Log the full prompt for debugging
        logger.debug(f"Full AI prompt: {ai_prompt}")

        # Call the AI API
        ai_response = await text_provider.generate_text(ai_prompt)

        # Log the full response for debugging
        logger.debug(f"Full AI response: {ai_response}")

        # Extract JSON from the response - try multiple patterns
        # First, try to find JSON in code blocks
        json_match = re.search(r'```(?:json)?\s*(.+?)\s*```', ai_response, re.DOTALL)

        if json_match:
            cleaned_json = json_match.group(1)
            logger.debug("Found JSON in code block")
        else:
            # If no code block, look for JSON-like structures
            # Try to find anything that looks like a complete JSON object or array
            json_object_match = re.search(r'({[\s\S]*})', ai_response, re.DOTALL)
            json_array_match = re.search(r'(\[[\s\S]*\])', ai_response, re.DOTALL)

            if json_object_match:
                cleaned_json = json_object_match.group(1)
                logger.debug("Found JSON-like object structure")
            elif json_array_match:
                cleaned_json = json_array_match.group(1)
                logger.debug("Found JSON-like array structure")
            else:
                # If no JSON-like structure found, use the entire response
                cleaned_json = ai_response
                logger.debug("Using entire response as JSON")

        # Clean up the JSON string - remove any markdown artifacts or extra text
        # This helps with responses that might have explanatory text mixed with JSON
        cleaned_json = re.sub(r'^[^{[\n]*', '', cleaned_json)  # Remove text before the first { or [
        cleaned_json = re.sub(r'[^}\]]*$', '', cleaned_json)   # Remove text after the last } or ]

        # Special handling for magic systems - they often have complex nested structures
        is_magic_category = category_id and 'magic' in category_id.lower()
        if is_magic_category:
            logger.debug("Detected magic category, applying special JSON handling")
            # Fix common JSON formatting issues in magic system responses
            cleaned_json = cleaned_json.replace('```', '')
            cleaned_json = re.sub(r'//.*?\n', '\n', cleaned_json)  # Remove comments
            cleaned_json = re.sub(r'/\*.*?\*/', '', cleaned_json, flags=re.DOTALL)  # Remove block comments

        try:
            # Parse the JSON response
            parsed_json = json.loads(cleaned_json)
            logger.debug(f"Successfully parsed JSON response")

            # Check if this is a multi-level structure
            if isinstance(parsed_json, dict) and 'elements' in parsed_json:
                # This is a multi-level structure, we'll handle it differently
                logger.debug("Detected multi-level structure with 'elements' key")

                # We'll flatten the hierarchical structure for now
                # In a future update, we could process this recursively to maintain the hierarchy
                flattened_elements = []

                # We'll use a two-pass approach to maintain the hierarchy
                # First pass: Create all elements and store their IDs
                # Second pass: Update parent-child relationships

                # Create a mapping to store temporary IDs to real IDs
                temp_id_map = {}

                # First, add all elements to the flattened list with proper metadata
                for top_element in parsed_json['elements']:
                    # Check if this is a nested structure with parent and sub_elements
                    if isinstance(top_element, dict) and 'parent' in top_element and 'sub_elements' in top_element:
                        # Extract the parent element
                        parent = top_element['parent']
                        parent['_is_top_level'] = True

                        # Generate a temporary ID for this parent
                        top_temp_id = f"temp_{uuid.uuid4().hex}"
                        parent['_temp_id'] = top_temp_id

                        # Add to flattened list
                        flattened_elements.append(parent)

                        # Process sub-elements
                        for sub_element in top_element['sub_elements']:
                            # Add parent reference
                            sub_element['_parent_temp_id'] = top_temp_id
                            sub_element['parent_name'] = parent['name']
                            sub_element['_is_second_level'] = True

                            # Add to flattened list
                            flattened_elements.append(sub_element)

                        continue  # Skip the rest of the loop for this element
                    # If we have a parent_id from the request, these top-level elements
                    # should be children of that parent
                    if parent_id:
                        logger.debug(f"Adding parent_id {parent_id} to top-level elements in multi-level structure")
                        # Get the parent name for reference
                        conn = get_db_connection()
                        parent_name = None
                        try:
                            with conn.cursor() as cursor:
                                cursor.execute(
                                    "SELECT name FROM world_elements WHERE element_id = %s",
                                    (parent_id,)
                                )
                                parent_result = cursor.fetchone()
                                if parent_result:
                                    parent_name = parent_result['name']
                        finally:
                            conn.close()

                        # Add parent reference to top-level elements
                        top_element['parent_id'] = parent_id
                        if parent_name:
                            top_element['parent_name'] = parent_name

                    # Generate a temporary ID for this element
                    top_temp_id = f"temp_{uuid.uuid4().hex}"
                    top_element['_temp_id'] = top_temp_id
                    top_element['_is_top_level'] = True

                    # Add the top-level element
                    flattened_elements.append(top_element)

                    # If it has children, add them too with references to their parent
                    if 'children' in top_element and isinstance(top_element['children'], list):
                        for child in top_element['children']:
                            # Generate a temporary ID for this child
                            child_temp_id = f"temp_{uuid.uuid4().hex}"
                            child['_temp_id'] = child_temp_id

                            # Add parent reference using the temporary ID
                            child['_parent_temp_id'] = top_temp_id
                            child['parent_name'] = top_element['name']
                            child['_is_second_level'] = True

                            # Add to flattened list
                            flattened_elements.append(child)

                            # If the child has children, add them too
                            if 'children' in child and isinstance(child['children'], list):
                                for grandchild in child['children']:
                                    # Generate a temporary ID for this grandchild
                                    grandchild_temp_id = f"temp_{uuid.uuid4().hex}"
                                    grandchild['_temp_id'] = grandchild_temp_id

                                    # Add parent reference using the temporary ID
                                    grandchild['_parent_temp_id'] = child_temp_id
                                    grandchild['parent_name'] = child['name']
                                    grandchild['_is_third_level'] = True

                                    # Add to flattened list
                                    flattened_elements.append(grandchild)

                # Add metadata to indicate this is a hierarchical structure
                for element in flattened_elements:
                    element['_is_hierarchical'] = True

                generated_elements = flattened_elements
            # Check if this is a nested structure
            elif isinstance(parsed_json, dict) and 'parent' in parsed_json and 'sub_elements' in parsed_json:
                # This is a nested structure with parent and sub-elements
                logger.debug("Detected nested structure with 'parent' and 'sub_elements' keys")

                # Get the parent element
                parent_element = parsed_json['parent']
                parent_element['_is_parent'] = True

                # Mark all sub-elements with a reference to their parent
                for sub_element in parsed_json['sub_elements']:
                    sub_element['_is_sub_element'] = True
                    sub_element['parent_name'] = parent_element.get('name', 'Parent Element')
                    # Generate a temporary ID for the parent that we'll use later
                    if '_temp_parent_id' not in sub_element:
                        parent_temp_id = f"temp_parent_{uuid.uuid4().hex}"
                        sub_element['_temp_parent_id'] = parent_temp_id
                        # Also add this ID to the parent for reference
                        if '_temp_id' not in parent_element:
                            parent_element['_temp_id'] = parent_temp_id

                # Combine parent and sub-elements into a flat list
                generated_elements = [parent_element] + parsed_json['sub_elements']
            # Otherwise, assume it's a simple array of elements
            elif isinstance(parsed_json, list):
                generated_elements = parsed_json
            else:
                # If it's not a recognized format, try to extract an array
                logger.warning(f"Unrecognized response format: {type(parsed_json)}")
                if isinstance(parsed_json, dict):
                    # Try to find an array in the dictionary
                    for _, value in parsed_json.items():
                        if isinstance(value, list) and len(value) > 0:
                            generated_elements = value
                            break
                    else:
                        # If no array found, convert the dict to a single-element array
                        generated_elements = [parsed_json]
                else:
                    # Convert to a single-element array
                    generated_elements = [parsed_json]
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse AI response as JSON: {e}")
            # Try to extract JSON with a more lenient approach
            try:
                # Log the full response for debugging
                logger.debug(f"Full AI response: {ai_response}")

                # Try multiple approaches to extract valid JSON

                # Approach 1: Find anything that looks like a JSON array
                array_match = re.search(r'\[\s*{.+}\s*\]', ai_response, re.DOTALL)
                if array_match:
                    logger.debug("Found JSON array pattern")
                    array_text = array_match.group(0)
                    # Clean up the array text
                    array_text = re.sub(r'//.*?\n', '\n', array_text)  # Remove comments
                    array_text = re.sub(r'/\*.*?\*/', '', array_text, flags=re.DOTALL)  # Remove block comments
                    try:
                        generated_elements = json.loads(array_text)
                        logger.debug("Successfully parsed JSON array")
                    except json.JSONDecodeError:
                        logger.debug("Failed to parse JSON array, trying to fix common issues")
                        # Try to fix common JSON issues
                        fixed_array = re.sub(r',\s*}', '}', array_text)  # Remove trailing commas in objects
                        fixed_array = re.sub(r',\s*\]', ']', fixed_array)  # Remove trailing commas in arrays
                        generated_elements = json.loads(fixed_array)
                        logger.debug("Successfully parsed fixed JSON array")
                else:
                    # Approach 2: Try to find a JSON object
                    object_match = re.search(r'{\s*"[^"]+"\s*:.+}', ai_response, re.DOTALL)
                    if object_match:
                        logger.debug("Found JSON object pattern")
                        object_text = object_match.group(0)
                        # Clean up the object text
                        object_text = re.sub(r'//.*?\n', '\n', object_text)  # Remove comments
                        object_text = re.sub(r'/\*.*?\*/', '', object_text, flags=re.DOTALL)  # Remove block comments
                        try:
                            parsed_json = json.loads(object_text)
                            logger.debug("Successfully parsed JSON object")
                        except json.JSONDecodeError:
                            logger.debug("Failed to parse JSON object, trying to fix common issues")
                            # Try to fix common JSON issues
                            fixed_object = re.sub(r',\s*}', '}', object_text)  # Remove trailing commas in objects
                            fixed_object = re.sub(r',\s*\]', ']', fixed_object)  # Remove trailing commas in arrays
                            parsed_json = json.loads(fixed_object)
                            logger.debug("Successfully parsed fixed JSON object")

                        # Process the object based on its structure
                        if 'elements' in parsed_json:
                            generated_elements = parsed_json['elements']
                        elif 'parent' in parsed_json and 'sub_elements' in parsed_json:
                            generated_elements = [parsed_json['parent']] + parsed_json['sub_elements']
                        else:
                            generated_elements = [parsed_json]
                    else:
                        # Approach 3: For magic systems, try to extract custom fields
                        if is_magic_category:
                            logger.debug("Trying magic-specific JSON extraction")

                            # First, try to extract parent and sub_elements structure
                            parent_match = re.search(r'{\s*"parent"\s*:\s*({[^{}]*"name"[^{}]*})', ai_response, re.DOTALL)
                            sub_elements_match = re.search(r'"sub_elements"\s*:\s*\[(.*?)\](?=\s*})', ai_response, re.DOTALL)

                            if parent_match and sub_elements_match:
                                logger.debug("Found parent and sub_elements structure")
                                try:
                                    # Extract parent object
                                    parent_text = parent_match.group(1)
                                    parent_text = re.sub(r',\s*}', '}', parent_text)  # Remove trailing commas

                                    # Extract and clean sub_elements array
                                    sub_elements_text = sub_elements_match.group(1)

                                    # Split sub_elements into individual elements
                                    element_texts = []
                                    bracket_count = 0
                                    current_element = ""

                                    for char in sub_elements_text:
                                        current_element += char
                                        if char == '{':
                                            bracket_count += 1
                                        elif char == '}':
                                            bracket_count -= 1
                                            if bracket_count == 0 and current_element.strip():
                                                element_texts.append(current_element.strip())
                                                current_element = ""

                                    # Parse parent
                                    try:
                                        parent_json = json.loads(parent_text)
                                        logger.debug("Successfully parsed parent JSON")

                                        # Parse each complete sub-element
                                        sub_elements = []
                                        for element_text in element_texts:
                                            # Make sure it's a complete JSON object
                                            if element_text.startswith('{') and element_text.endswith('}'):
                                                try:
                                                    # Clean up the element text
                                                    element_text = re.sub(r',\s*}', '}', element_text)  # Remove trailing commas
                                                    element_json = json.loads(element_text)
                                                    sub_elements.append(element_json)
                                                except json.JSONDecodeError:
                                                    logger.debug(f"Skipping invalid sub-element: {element_text[:100]}...")

                                        # Combine parent and valid sub-elements
                                        generated_elements = [parent_json] + sub_elements
                                        logger.debug(f"Successfully extracted {len(generated_elements)} elements (1 parent + {len(sub_elements)} sub-elements)")
                                    except json.JSONDecodeError:
                                        logger.debug("Failed to parse parent JSON, falling back to individual element extraction")
                                except Exception as e:
                                    logger.debug(f"Error processing parent/sub-elements structure: {str(e)}")

                            # If parent/sub-elements approach failed, try individual element extraction
                            if 'generated_elements' not in locals():
                                # Look for individual complete JSON objects that might be magic elements
                                element_matches = re.finditer(r'{\s*"name":.+?"element_type"\s*:\s*"[^"]+".+?}', ai_response, re.DOTALL)
                                elements = []

                                for match in element_matches:
                                    element_text = match.group(0)
                                    # Clean and fix the element JSON
                                    element_text = re.sub(r'//.*?\n', '\n', element_text)  # Remove comments
                                    element_text = re.sub(r'/\*.*?\*/', '', element_text, flags=re.DOTALL)  # Remove block comments
                                    element_text = re.sub(r',\s*}', '}', element_text)  # Remove trailing commas in objects
                                    element_text = re.sub(r',\s*\]', ']', element_text)  # Remove trailing commas in arrays

                                    try:
                                        element_json = json.loads(element_text)
                                        elements.append(element_json)
                                        logger.debug(f"Successfully parsed element: {element_json.get('name', 'unnamed')}")
                                    except json.JSONDecodeError:
                                        logger.debug("Failed to parse element JSON, trying more aggressive fixes")
                                        try:
                                            # Replace any invalid escape sequences
                                            element_text = re.sub(r'\\([^"\\/bfnrtu])', r'\1', element_text)
                                            # Ensure all property names are quoted
                                            element_text = re.sub(r'([{,]\s*)([a-zA-Z0-9_]+)(\s*:)', r'\1"\2"\3', element_text)
                                            element_json = json.loads(element_text)
                                            elements.append(element_json)
                                            logger.debug(f"Successfully parsed element with fixes: {element_json.get('name', 'unnamed')}")
                                        except json.JSONDecodeError:
                                            logger.debug("Failed to parse element despite aggressive fixes")

                                if elements:
                                    generated_elements = elements
                                    logger.debug(f"Successfully extracted {len(elements)} individual elements")
                                else:
                                    # If no complete elements found, try to extract at least one magic system
                                    magic_match = re.search(r'{\s*"name":.+?"custom_fields":.+?}', ai_response, re.DOTALL)
                                    if magic_match:
                                        logger.debug("Found potential magic system JSON")
                                        magic_text = magic_match.group(0)
                                        # Clean and fix the magic system JSON
                                        magic_text = re.sub(r'//.*?\n', '\n', magic_text)  # Remove comments
                                        magic_text = re.sub(r'/\*.*?\*/', '', magic_text, flags=re.DOTALL)  # Remove block comments
                                        magic_text = re.sub(r',\s*}', '}', magic_text)  # Remove trailing commas in objects
                                        magic_text = re.sub(r',\s*\]', ']', magic_text)  # Remove trailing commas in arrays

                                        try:
                                            magic_json = json.loads(magic_text)
                                            logger.debug("Successfully parsed magic system JSON")
                                            generated_elements = [magic_json]
                                        except json.JSONDecodeError:
                                            raise ValueError("Could not parse magic system JSON despite fixes")
                                    else:
                                        raise ValueError("Could not find any valid magic system structures")
                        else:
                            raise ValueError("Could not extract JSON from response")
            except Exception as inner_e:
                logger.error(f"Second attempt to parse JSON failed: {inner_e}")

                # Last resort: Try to create a minimal valid element from the response
                if is_magic_category:
                    logger.debug("Attempting to create a minimal valid magic element from the response")
                    try:
                        # Extract potential name and description
                        name_match = re.search(r'"name"\s*:\s*"([^"]+)"', ai_response)
                        desc_match = re.search(r'"description"\s*:\s*"([^"]+)"', ai_response)

                        name = name_match.group(1) if name_match else "Magic Element"
                        description = desc_match.group(1) if desc_match else "Generated magic element with parsing issues"

                        # Create a minimal valid element
                        generated_elements = [{
                            "name": name,
                            "description": description,
                            "element_type": "magic_system",
                            "custom_fields": {
                                "source": "Unknown (parsing error)",
                                "energy_type": "unknown",
                                "acquisition": "unknown",
                                "rules": "Unknown (parsing error)",
                                "limitations": "Unknown (parsing error)",
                                "cost": "Unknown (parsing error)",
                                "practitioners": "Unknown (parsing error)",
                                "rarity": "unknown"
                            }
                        }]
                        logger.debug("Created minimal valid magic element as fallback")
                    except Exception as fallback_e:
                        logger.error(f"Fallback element creation failed: {fallback_e}")
                        raise HTTPException(status_code=500, detail=f"Failed to parse AI response: {str(e)}")
                else:
                    raise HTTPException(status_code=500, detail=f"Failed to parse AI response: {str(e)}")

        # Create the elements in the database
        result = []
        conn = get_db_connection()
        try:
            with conn.cursor() as cursor:
                # Sort elements to ensure parents are created before children
                if any(e.get('_is_hierarchical', False) for e in generated_elements):
                    # Sort by level: top-level first, then second level, then third level
                    sorted_elements = []

                    # First add all top-level elements
                    sorted_elements.extend([e for e in generated_elements if e.get('_is_top_level', False)])

                    # Then add all second-level elements
                    sorted_elements.extend([e for e in generated_elements if e.get('_is_second_level', False)])

                    # Finally add all third-level elements
                    sorted_elements.extend([e for e in generated_elements if e.get('_is_third_level', False)])

                    # Add any remaining elements
                    remaining = [e for e in generated_elements if not any(
                        e.get(k, False) for k in ['_is_top_level', '_is_second_level', '_is_third_level']
                    )]
                    sorted_elements.extend(remaining)

                    logger.debug(f"Sorted {len(generated_elements)} elements by hierarchy level")
                    generated_elements = sorted_elements

                # Process each element
                for element in generated_elements:
                    # Skip if element is not a dictionary
                    if not isinstance(element, dict):
                        logger.warning(f"Skipping non-dictionary element: {element}")
                        continue

                    # Generate a unique ID
                    element_id = f"elem_{uuid.uuid4().hex}"

                    # Extract fields with proper error handling
                    try:
                        name = element.get("name", "Unnamed Element")
                        description = element.get("description", "")
                        element_type = element.get("element_type", "general")

                        # Map Flora/Fauna to plant/creature
                        if element_type == 'Flora':
                            element_type = 'plant'
                            logger.debug(f"Mapped Flora to plant element type for '{name}'")
                        elif element_type == 'Fauna':
                            element_type = 'creature'
                            logger.debug(f"Mapped Fauna to creature element type for '{name}'")

                        custom_fields = element.get("custom_fields", {})

                        # Check if this element has a parent_name from the hierarchical structure
                        # but no parent_id (meaning it's a child in the flattened structure)
                        current_parent_id = parent_id

                        # Check if this is a sub-element from a nested structure
                        if element.get('_is_sub_element', False):
                            logger.debug(f"Processing sub-element: {name}")
                            # This is a sub-element, so we need to find its parent
                            # First, check if we have a temp_parent_id
                            if '_temp_parent_id' in element:
                                # Look up the parent by temp_id in our temp_id_map
                                parent_temp_id = element.get('_temp_parent_id')
                                if parent_temp_id in temp_id_map:
                                    current_parent_id = temp_id_map[parent_temp_id]
                                    logger.debug(f"Found parent ID {current_parent_id} from temp_id {parent_temp_id}")
                                else:
                                    # If we don't have the parent ID yet, look it up by name
                                    parent_name = element.get("parent_name")
                                    logger.debug(f"Looking for parent with name: {parent_name}")

                                    # Query to find the parent element by name
                                    cursor.execute(
                                        """SELECT element_id FROM world_elements
                                        WHERE book_id = %s AND name = %s
                                        ORDER BY created_at DESC LIMIT 1""",
                                        (book_id, parent_name)
                                    )
                                    parent_result = cursor.fetchone()

                                    if parent_result:
                                        current_parent_id = parent_result["element_id"]
                                        logger.debug(f"Found parent ID: {current_parent_id}")
                            elif "parent_name" in element:
                                # Look up the parent by name
                                parent_name = element.get("parent_name")
                                logger.debug(f"Looking for parent with name: {parent_name}")

                                # Query to find the parent element by name
                                cursor.execute(
                                    """SELECT element_id FROM world_elements
                                    WHERE book_id = %s AND name = %s
                                    ORDER BY created_at DESC LIMIT 1""",
                                    (book_id, parent_name)
                                )
                                parent_result = cursor.fetchone()

                                if parent_result:
                                    current_parent_id = parent_result["element_id"]
                                    logger.debug(f"Found parent ID: {current_parent_id}")
                        elif not current_parent_id and "parent_name" in element:
                            # This is a regular element with a parent_name
                            # Look up the parent by name
                            parent_name = element.get("parent_name")
                            logger.debug(f"Looking for parent with name: {parent_name}")

                            # Query to find the parent element by name
                            cursor.execute(
                                """SELECT element_id FROM world_elements
                                WHERE book_id = %s AND name = %s
                                ORDER BY created_at DESC LIMIT 1""",
                                (book_id, parent_name)
                            )
                            parent_result = cursor.fetchone()

                            if parent_result:
                                current_parent_id = parent_result["element_id"]
                                logger.debug(f"Found parent ID: {current_parent_id}")
                    except Exception as field_error:
                        logger.error(f"Error extracting fields from element: {str(field_error)}")
                        logger.error(f"Problematic element: {element}")
                        # Use default values
                        name = "Unnamed Element"
                        description = ""
                        element_type = "general"
                        custom_fields = {}

                    # Convert custom fields to JSON
                    attributes_json = json.dumps(custom_fields)

                    # Check if this is a hierarchical element
                    is_hierarchical = element.get('_is_hierarchical', False)

                    # Determine the parent ID based on the element's metadata
                    if is_hierarchical:
                        # For hierarchical elements, we need to handle parent-child relationships
                        if element.get('_is_top_level', False):
                            # Top-level elements use the parent_id from the request (if any)
                            effective_parent_id = current_parent_id
                        elif '_parent_temp_id' in element:
                            # Child elements need to look up their parent's real ID
                            parent_temp_id = element['_parent_temp_id']

                            # Look up the real parent ID from our mapping
                            if parent_temp_id in temp_id_map:
                                effective_parent_id = temp_id_map[parent_temp_id]
                                logger.debug(f"Using mapped parent ID {effective_parent_id} for element '{name}'")
                            else:
                                # If we can't find the parent ID, fall back to the original parent_id
                                effective_parent_id = current_parent_id
                                logger.debug(f"Could not find mapped parent ID for {parent_temp_id}, using {effective_parent_id}")
                        else:
                            # Default to the current_parent_id
                            effective_parent_id = current_parent_id
                    else:
                        # For non-hierarchical elements, use the current_parent_id
                        effective_parent_id = current_parent_id

                    # Prepare SQL query based on whether we have a parent_id
                    if effective_parent_id:
                        # Insert the element with parent_id
                        cursor.execute(
                            """INSERT INTO world_elements
                            (element_id, book_id, category, name, description, element_type, attributes, parent_id)
                            VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                            RETURNING *""",
                            (element_id, book_id, category_id, name, description, element_type, attributes_json, effective_parent_id)
                        )

                        # Fetch the result of the INSERT query
                        new_element = cursor.fetchone()

                        # Update the parent element to indicate it has children
                        cursor.execute(
                            """UPDATE world_elements
                            SET has_children = true
                            WHERE element_id = %s""",
                            (effective_parent_id,)
                        )

                        logger.debug(f"Created element '{name}' as child of '{effective_parent_id}'")
                    else:
                        # Insert the element without parent_id
                        cursor.execute(
                            """INSERT INTO world_elements
                            (element_id, book_id, category, name, description, element_type, attributes)
                            VALUES (%s, %s, %s, %s, %s, %s, %s)
                            RETURNING *""",
                            (element_id, book_id, category_id, name, description, element_type, attributes_json)
                        )

                        # Fetch the result of the INSERT query
                        new_element = cursor.fetchone()

                        logger.debug(f"Created top-level element '{name}'")

                    # Store the mapping from temporary ID to real ID
                    if '_temp_id' in element:
                        temp_id = element['_temp_id']
                        temp_id_map[temp_id] = element_id
                        logger.debug(f"Mapped temporary ID {temp_id} to real ID {element_id}")

                    # Store the element ID in a mapping for potential children
                    # This will be used if we process this in multiple passes
                    if "children" in element and isinstance(element["children"], list):
                        logger.debug(f"Element '{name}' has {len(element['children'])} children that will be processed separately")

                    # Add custom_fields to the result
                    if new_element:
                        if 'attributes' in new_element and new_element['attributes']:
                            try:
                                if isinstance(new_element['attributes'], str):
                                    new_element['custom_fields'] = json.loads(new_element['attributes'])
                                else:
                                    new_element['custom_fields'] = new_element['attributes']
                            except json.JSONDecodeError:
                                new_element['custom_fields'] = {}
                        else:
                            new_element['custom_fields'] = {}

                        # Remove attributes field
                        if 'attributes' in new_element:
                            del new_element['attributes']

                        result.append(new_element)

                conn.commit()
        except Exception as e:
            conn.rollback()
            logger.error(f"Error creating world elements: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to create world elements: {str(e)}")
        finally:
            conn.close()

        return result
    except Exception as e:
        logger.error(f"World element generation failed: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Failed to generate world elements: {str(e)}")

@router.post("/analyze")
async def analyze_world_consistency(
    book_id: str,
    user_id: str = Depends(get_current_user)
):
    """
    Analyzes the consistency of world building elements using AI.
    Returns a structured analysis with strengths, inconsistencies, and suggestions.
    """
    try:
        from app.ai.provider import get_text_provider
        import json
        import re

        logger.debug(f"Analyzing world consistency for book {book_id}")

        # Get book information
        conn = get_db_connection()
        book_info = {}
        try:
            with conn.cursor() as cursor:
                cursor.execute(
                    "SELECT title, genre, description FROM books WHERE book_id = %s",
                    (book_id,)
                )
                book_info = cursor.fetchone() or {}
        finally:
            conn.close()

        # Get all world elements
        elements_by_category = world_repo.get_world_elements(book_id)

        # Get all relationships between elements
        relationships = relationship_repo.get_relationships(book_id)

        # Format relationships for analysis
        formatted_relationships = []
        for rel in relationships:
            formatted_relationships.append({
                "source": rel.get("source_name", ""),
                "source_category": rel.get("source_category", ""),
                "target": rel.get("target_name", ""),
                "target_category": rel.get("target_category", ""),
                "type": rel.get("relationship_type", ""),
                "description": rel.get("description", "")
            })

        # Format elements for analysis
        formatted_elements = []
        for category, elements in elements_by_category.items():
            for element in elements:
                formatted_elements.append({
                    "name": element.get("name", ""),
                    "category": category,
                    "description": element.get("description", ""),
                    "attributes": element.get("attributes", {})
                })

        # Create the prompt for the AI
        ai_prompt = f"""
        Analyze the consistency of the world building elements for a {book_info.get('genre', '')} book titled '{book_info.get('title', 'Untitled')}'.

        Book description: {book_info.get('description', '')}

        World elements:
        {json.dumps(formatted_elements, indent=2)}

        Relationships between elements:
        {json.dumps(formatted_relationships, indent=2)}

        Please analyze the world building for consistency, coherence, and believability. Consider:
        1. Internal consistency - Do elements contradict each other?
        2. Logical relationships - Do the relationships between elements make sense?
        3. Completeness - Are there any major gaps in the world building?
        4. Genre appropriateness - Do the elements fit the genre of the book?
        5. Originality - Is the world building unique and interesting?

        Return your analysis as a JSON object with the following structure:
        {{
            "overall_score": number (0-100, representing consistency percentage),
            "strengths": [string, string, ...] (list of strengths in the world building),
            "inconsistencies": [string, string, ...] (list of potential inconsistencies or contradictions),
            "suggestions": [string, string, ...] (list of suggestions for improvement)
        }}

        Be specific in your analysis, referencing actual elements and relationships by name.
        """

        # Get the AI provider
        text_provider = await get_text_provider(user_id)

        # Call the AI API
        ai_response = await text_provider.generate_text(ai_prompt)

        # Extract JSON from the response
        json_match = re.search(r'```(?:json)?\s*(.+?)\s*```', ai_response, re.DOTALL)
        cleaned_json = json_match.group(1) if json_match else ai_response

        try:
            analysis = json.loads(cleaned_json)
            logger.debug(f"World consistency analysis: {analysis}")

            # Ensure the response has the expected structure
            if not isinstance(analysis, dict):
                raise ValueError("Analysis response is not a dictionary")

            # Ensure required fields are present
            required_fields = ["overall_score", "strengths", "inconsistencies", "suggestions"]
            for field in required_fields:
                if field not in analysis:
                    analysis[field] = [] if field != "overall_score" else 50

            return analysis

        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse AI response as JSON: {e}")
            # Try to extract JSON with a more lenient approach
            try:
                # Find anything that looks like a JSON object
                object_match = re.search(r'{\s*"[^"]+"\s*:.+}', ai_response, re.DOTALL)
                if object_match:
                    analysis = json.loads(object_match.group(0))
                    return analysis
                else:
                    # If we can't parse JSON, create a structured response from the text
                    return {
                        "overall_score": 50,  # Default score
                        "strengths": ["The AI provided analysis but it couldn't be parsed as JSON."],
                        "inconsistencies": ["Unable to extract specific inconsistencies from AI response."],
                        "suggestions": ["Please try again or refine your world building elements."]
                    }
            except Exception as inner_e:
                logger.error(f"Second attempt to parse JSON failed: {inner_e}")
                raise HTTPException(status_code=500, detail=f"Failed to parse AI response: {str(e)}")
    except Exception as e:
        logger.error(f"World consistency analysis failed: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Failed to analyze world consistency: {str(e)}")

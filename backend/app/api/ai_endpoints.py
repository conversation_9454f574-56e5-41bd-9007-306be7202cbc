# backend/app/api/ai_endpoints.py
from fastapi import APIRouter, Depends, HTTPException, Body
from ..auth import oauth2_scheme
from ..db.base import get_db_connection
import json
import logging
import re
import uuid
import os
from ..ai.provider import get_text_provider, get_image_provider

router = APIRouter()
logger = logging.getLogger(__name__)
# Directory to save images
IMAGE_DIR = "static/images"
os.makedirs(IMAGE_DIR, exist_ok=True)

# Helper function to get user-specific image directory
def get_user_image_dir(user_id):
    """Get the user-specific image directory path"""
    if not user_id:
        return IMAGE_DIR

    user_dir = os.path.join(IMAGE_DIR, f"user_{user_id}")
    os.makedirs(user_dir, exist_ok=True)
    return user_dir

@router.post("/books/{book_id}/ai/save_image")
async def save_ai_image(book_id: str, request: dict, token: str = Depends(oauth2_scheme)):
    # Note: book_id is not used in this function but is part of the URL path for consistency
    """
    Generate and save an AI-generated image for a character.
    Request body: {"prompt": str, "character_name": str, "style": str}
    Returns: {"headshot_url": str}
    """
    # Extract user_id from token
    from ..auth import get_current_user
    from ..api.character_endpoints import construct_headshot_prompt
    from ..ai.provider import set_user_image_style
    user_id = get_current_user(token)

    prompt = request.get("prompt")
    character_name = request.get("character_name")
    style = request.get("style")  # Get the optional style parameter
    if not prompt or not character_name:
        raise HTTPException(status_code=400, detail="Prompt and character_name are required")

    logger.debug(f"Generating image for character: {character_name}, prompt: {prompt}, style: {style}")

    # If style is provided, use it for this request and store it for the user
    if style:
        # Store the style preference for the user
        set_user_image_style(user_id, style)
        logger.debug(f"Set user image style preference to: {style}")

        # Extract description from the prompt
        description = prompt.replace(f"Generate a headshot portrait for {character_name}", "").strip()
        if description.startswith(", who is "):
            description = description[9:]  # Remove ", who is " prefix

        # Use construct_headshot_prompt to create a styled prompt
        prompt = construct_headshot_prompt(character_name, {"description": description}, style)
        logger.debug(f"Styled prompt: {prompt}")
        logger.info(f"STYLED PROMPT FOR {style.upper()} STYLE: {prompt}")

    # Get the image provider for the user
    image_provider = await get_image_provider(user_id)
    logger.debug(f"Using image provider: {image_provider.provider_name}")

    try:
        # Get the user-specific image directory
        user_image_dir = get_user_image_dir(user_id)

        # Generate a unique filename for the image
        image_filename = f"{character_name}_{uuid.uuid4().hex}.jpg"
        image_path = os.path.join(user_image_dir, image_filename)

        # Create the directory if it doesn't exist
        os.makedirs(os.path.dirname(image_path), exist_ok=True)

        try:
            # Generate the image
            image_url = await image_provider.generate_image(prompt, image_path)
            headshot_url = image_url
            logger.debug(f"Image saved: {headshot_url}")
            return {"headshot_url": headshot_url}
        except Exception as img_error:
            # If image generation fails, create a placeholder image
            logger.warning(f"Image generation failed for character: {character_name}. Error: {str(img_error)}")

            # Create a placeholder image with the character's initial
            initial = character_name[0].upper() if character_name else "?"

            try:
                # Try to use PIL to create a simple placeholder image
                from PIL import Image, ImageDraw, ImageFont

                # Create a new image with a gray background
                img = Image.new('RGB', (400, 400), color=(108, 117, 125))
                draw = ImageDraw.Draw(img)

                # Try to use a system font, fall back to default if not available
                try:
                    font = ImageFont.truetype("Arial", 150)
                except IOError:
                    font = ImageFont.load_default()

                # Calculate text position to center it
                text_width, text_height = draw.textbbox((0, 0), initial, font=font)[2:4]
                position = ((400 - text_width) // 2, (400 - text_height) // 2)

                # Draw the initial in white
                draw.text(position, initial, font=font, fill=(255, 255, 255))

                # Save the image
                img.save(image_path, 'JPEG')

                logger.info(f"Created local placeholder image at {image_path}")
            except Exception as pil_error:
                # If PIL fails, create an extremely simple placeholder file
                logger.warning(f"Failed to create PIL image: {str(pil_error)}. Creating simple file instead.")
                with open(image_path, "w") as f:
                    f.write(f"Placeholder for {character_name}")
                logger.info(f"Created simple text placeholder at {image_path}")

            # Extract the relative path from the full path
            relative_path = os.path.relpath(image_path, start="static")
            headshot_url = f"/static/{relative_path}"
            logger.info(f"Created placeholder image: {headshot_url}")

            # Return success with the placeholder and a special flag
            return {
                "headshot_url": headshot_url,
                "generation_failed": True,
                "message": f"Image generation failed: {str(img_error)}. A placeholder has been provided instead."
            }
    except Exception as e:
        logger.error(f"Image generation/save failed: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Failed to generate/save image: {str(e)}")

@router.post("/generate-image")
async def generate_image(request: dict, token: str = Depends(oauth2_scheme)):
    """
    Generate an AI image based on a prompt.
    Request body: {"prompt": str}
    Returns: {"image_url": str}
    """
    # Extract user_id from token
    from ..auth import get_current_user
    user_id = get_current_user(token)

    prompt = request.get("prompt")
    if not prompt:
        raise HTTPException(status_code=400, detail="Prompt is required")

    logger.debug(f"Generating image with prompt: {prompt}")

    try:
        # Get the image provider for the user
        image_provider = await get_image_provider(user_id)
        logger.debug(f"Using image provider: {image_provider.provider_name}")

        # Get the user-specific image directory
        user_image_dir = get_user_image_dir(user_id)

        # Generate a unique filename for the image
        image_filename = f"image_{uuid.uuid4().hex}.jpg"
        image_path = os.path.join(user_image_dir, image_filename)

        # Create the directory if it doesn't exist
        os.makedirs(os.path.dirname(image_path), exist_ok=True)

        # Generate the image
        image_url = await image_provider.generate_image(prompt, image_path)

        return {"image_url": image_url}
    except Exception as e:
        logger.error(f"Image generation failed: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Failed to generate image: {str(e)}")

@router.post("/books/{book_id}/world/generate-image")
async def generate_world_image(book_id: str, request: dict, token: str = Depends(oauth2_scheme)):
    """
    Generate an AI image for a world based on its name and description.
    Request body: {"name": str, "description": str}
    Returns: {"image_url": str}
    """
    # Extract user_id from token
    from ..auth import get_current_user
    user_id = get_current_user(token)

    world_name = request.get("name", "")
    world_description = request.get("description", "")

    if not world_name and not world_description:
        raise HTTPException(status_code=400, detail="Either world name or description is required")

    # Construct a prompt for the world image
    prompt = f"Fantasy world map of {world_name}" if world_name else "Fantasy world map"
    if world_description:
        prompt += f". {world_description}"

    logger.debug(f"Generating world image with prompt: {prompt}")

    try:
        # Get the image provider for the user
        image_provider = await get_image_provider(user_id)
        logger.debug(f"Using image provider: {image_provider.provider_name}")

        # Get the user-specific image directory
        user_image_dir = get_user_image_dir(user_id)

        # Generate a unique filename for the image
        image_filename = f"world_{uuid.uuid4().hex}.jpg"
        image_path = os.path.join(user_image_dir, image_filename)

        # Create the directory if it doesn't exist
        os.makedirs(os.path.dirname(image_path), exist_ok=True)

        # Generate the image
        image_url = await image_provider.generate_image(prompt, image_path)

        # Save the image URL to the world settings
        conn = get_db_connection()
        try:
            with conn.cursor() as cursor:
                # Check if there's already a world image element
                cursor.execute(
                    "SELECT element_id FROM world_elements WHERE book_id = %s AND category = 'world_settings' AND name = 'World Image'",
                    (book_id,)
                )
                result = cursor.fetchone()

                if result:
                    # Update existing element
                    element_id = result['element_id']
                    cursor.execute(
                        "UPDATE world_elements SET attributes = %s WHERE element_id = %s",
                        (json.dumps({"url": image_url}), element_id)
                    )
                else:
                    # Create new element
                    element_id = str(uuid.uuid4())
                    cursor.execute(
                        "INSERT INTO world_elements (element_id, book_id, category, name, description, attributes) VALUES (%s, %s, %s, %s, %s, %s)",
                        (element_id, book_id, "world_settings", "World Image", "World Image URL", json.dumps({"url": image_url}))
                    )

                conn.commit()
        finally:
            conn.close()

        return {"image_url": image_url}
    except Exception as e:
        logger.error(f"World image generation failed: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Failed to generate world image: {str(e)}")

@router.post("/books/{book_id}/brainstorm/generate")
async def generate_brainstorm_ideas(book_id: str, request: dict = Body(...), token: str = Depends(oauth2_scheme)):
    """
    Generate AI brainstorm ideas for a given book.
    Returns 3 structured event cards that can be added to the brainstorm board.
    """
    # Extract user_id from token
    from ..auth import get_current_user
    user_id = get_current_user(token)
    try:
        # Extract page from request
        current_page = request.get('page', 'page1')
        logger.debug(f"Generating ideas for book {book_id} on page {current_page}")

        # Get book information and context
        conn = get_db_connection()
        book_info = {}
        characters = []
        genre = ""
        description = ""

        try:
            # Get book details
            with conn.cursor() as cursor:
                cursor.execute("SELECT * FROM books WHERE book_id = %s", (book_id,))
                book = cursor.fetchone()
                if not book:
                    raise HTTPException(status_code=404, detail="Book not found")

                book_info = {
                    "title": book.get('title', ''),
                    "genre": book.get('genre', ''),
                    "description": book.get('description', '')
                }
                genre = book_info["genre"]
                description = book_info["description"]

                # Get characters
                cursor.execute("SELECT * FROM characters WHERE book_id = %s", (book_id,))
                characters = cursor.fetchall()

                # Get all world elements, grouped by category
                cursor.execute(
                    "SELECT * FROM world_elements WHERE book_id = %s",
                    (book_id,)
                )
                all_world_elements = cursor.fetchall()

                # Organize world elements by category
                world_elements_by_category = {}
                for element in all_world_elements:
                    category = element.get('category')
                    if category not in world_elements_by_category:
                        world_elements_by_category[category] = []
                    world_elements_by_category[category].append(element)

                # We used to get locations here for backward compatibility
                # but it's no longer needed with the new AI provider abstraction
        finally:
            conn.close()

        # Prepare character data for the prompt
        character_data = [{
            "name": char.get('name', ''),
            "role": char.get('role', ''),
            "description": char.get('description', '')
        } for char in characters[:5]]  # Limit to 5 characters for prompt size

        # Prepare world element data by category for the prompt
        world_data = {}
        for category, elements in world_elements_by_category.items():
            # Limit to 5 elements per category to keep prompt size reasonable
            world_data[category] = [{
                "name": elem.get('name', ''),
                "description": elem.get('description', ''),
                "element_type": elem.get('element_type', ''),
                "attributes": elem.get('attributes', {})
            } for elem in elements[:5]]

        # Note: We used to create location_data here for backward compatibility,
        # but it's no longer needed with the new AI provider abstraction

        # Create the prompt for the AI
        prompt = f"""
        Generate 3 interesting ideas for a {genre} book titled '{book_info.get('title', 'Untitled')}'.
        Book description: {description}

        Characters: {json.dumps(character_data)}
        World Elements: {json.dumps(world_data)}
        Ensure you generate at least two different event types, such as 'event', 'character'(introducing a new character), 'location' (introducing a new location), or 'idea'.
        For each event, provide:
        1. A title (short and catchy)
        2. A detailed description of the event
        3. Which characters are involved
        4. Where the event takes place (location)
        5. The type of event (choose from: 'event', 'character', 'location', 'idea')

        Return the results as a JSON array with 3 objects, each with these fields:
        - title: string
        - content: string (detailed description)
        - characters: array of character names that are involved
        - locations: array of location names where it takes place
        - type: string (one of 'event', 'character', 'location', 'idea')

        Make sure each event is unique and interesting, and fits the genre and characters.
        Use the world elements to make the events more immersive and connected to the world.
        """

        # Get the text provider for the user
        text_provider = await get_text_provider(user_id)
        logger.debug(f"Using text provider: {text_provider.provider_name}")

        # Add system message to the prompt
        system_message = "You are a creative writing assistant that generates structured event ideas for authors. Return only valid JSON as specified in the prompt."
        full_prompt = f"{system_message}\n\n{prompt}"

        # Call the AI provider
        logger.debug(f"Sending request to AI provider for brainstorm ideas")
        ai_text = await text_provider.generate_text(full_prompt)
        logger.debug(f"Raw AI response: {ai_text}")

        # Extract JSON from markdown code blocks if present
        json_match = re.search(r'```(?:json)?\s*(.+?)\s*```', ai_text, re.DOTALL)
        cleaned_json = json_match.group(1) if json_match else ai_text

        # Parse the JSON
        try:
            event_ideas = json.loads(cleaned_json)
            logger.debug(f"Parsed event ideas: {event_ideas}")
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse AI response as JSON: {e}")
            # Try to extract JSON with a more lenient approach
            try:
                # Find anything that looks like a JSON array
                array_match = re.search(r'\[\s*{.+}\s*\]', ai_text, re.DOTALL)
                if array_match:
                    event_ideas = json.loads(array_match.group(0))
                else:
                    raise ValueError("Could not extract JSON array from response")
            except Exception as inner_e:
                logger.error(f"Second attempt to parse JSON failed: {inner_e}")
                raise HTTPException(status_code=500, detail=f"Failed to parse AI response: {str(e)}")

        # Generate node IDs and prepare the response
        result = []

        # Create brainstorm pages if they don't exist
        conn = get_db_connection()
        try:
            with conn.cursor() as cursor:
                # Import the standardize_page_id function from brainstorm_endpoints
                from app.api.brainstorm_endpoints import standardize_page_id

                # Use the standardized function to get a consistent page ID
                page_id = standardize_page_id(book_id, current_page)

                # Check if the page exists with the standardized ID
                cursor.execute(
                    "SELECT page_id FROM brainstorm_pages WHERE page_id = %s",
                    (page_id,)
                )
                page = cursor.fetchone()
                logger.debug(f"Looking for page {current_page} (ID: {page_id}) for book {book_id}, found: {page is not None}")

                # Check for duplicate pages and clean them up
                cursor.execute(
                    """WITH ranked_pages AS (
                        SELECT page_id, book_id, title, sequence_number,
                               ROW_NUMBER() OVER (PARTITION BY book_id, title ORDER BY page_id) as rn
                        FROM brainstorm_pages
                        WHERE book_id = %s
                    )
                    DELETE FROM brainstorm_pages
                    WHERE page_id IN (
                        SELECT page_id FROM ranked_pages WHERE rn > 1
                    )
                    RETURNING page_id""",
                    (book_id,)
                )
                deleted_pages = cursor.fetchall()
                if deleted_pages:
                    logger.debug(f"Cleaned up {len(deleted_pages)} duplicate pages for book {book_id}")

                if not page:
                    # Import the standardize_page_id function from brainstorm_endpoints
                    from app.api.brainstorm_endpoints import standardize_page_id

                    # Use the standardized function to get a consistent page ID
                    page_id = standardize_page_id(book_id, current_page)

                    # Extract page number for sequence_number
                    page_number = '1'  # Default
                    if current_page.startswith('page') and len(current_page) > 4:
                        try:
                            page_number = current_page[4:]
                        except:
                            page_number = '1'

                    # Check if the page already exists (double-check to avoid race conditions)
                    cursor.execute(
                        """SELECT page_id FROM brainstorm_pages WHERE page_id = %s""",
                        (page_id,)
                    )
                    existing_page = cursor.fetchone()

                    if not existing_page:
                        # Create the page with the standardized ID
                        cursor.execute(
                            """INSERT INTO brainstorm_pages (page_id, book_id, title, sequence_number)
                            VALUES (%s, %s, %s, %s)""",
                            (page_id, book_id, current_page, int(page_number))
                        )
                        logger.debug(f"Created new page {current_page} with ID {page_id}")
                    else:
                        logger.debug(f"Page {page_id} already exists, using existing page")
                else:
                    # Handle both tuple and dict return types from the database
                    if isinstance(page, tuple):
                        page_id = page[0]
                    else:
                        page_id = page.get('page_id')
                    logger.debug(f"Using existing page with ID {page_id}")

                # We no longer delete existing AI-generated nodes
                # Instead, we'll just add new nodes to the page
                logger.debug(f"Adding new AI-generated nodes to page {current_page} without removing existing ones")

                # Create new nodes in the database
                for i, idea in enumerate(event_ideas):
                    # Use consistent node_id format with underscore
                    node_id = f"node_{uuid.uuid4().hex}"
                    title = idea.get("title", "Untitled Event")
                    content = json.dumps({
                        "title": title,
                        "content": idea.get("content", ""),
                        "characters": idea.get("characters", []),
                        "locations": idea.get("locations", [])
                    })
                    node_type = idea.get("type", "event")
                    # Get the count of existing nodes to position new ones appropriately
                    cursor.execute(
                        "SELECT COUNT(*) FROM brainstorm_nodes WHERE page_id = %s",
                        (page_id,)
                    )
                    count_result = cursor.fetchone()
                    existing_count = 0
                    if count_result:
                        existing_count = count_result[0] if isinstance(count_result, tuple) else count_result.get('count', 0)

                    # Position new nodes in a grid pattern
                    # Each row can have 3 nodes, then start a new row
                    row = (existing_count + i) // 3
                    col = (existing_count + i) % 3
                    position_x = 100 + (col * 250)  # Space them out horizontally
                    position_y = 100 + (row * 200)  # Space them out vertically

                    # Insert the node
                    cursor.execute(
                        """INSERT INTO brainstorm_nodes
                        (node_id, page_id, content, position_x, position_y, node_type)
                        VALUES (%s, %s, %s, %s, %s, %s)
                        RETURNING node_id""",
                        (node_id, page_id, content, position_x, position_y, node_type)
                    )
                    logger.debug(f"Created new AI node {node_id} on page {current_page}")

                    # Prepare the response
                    node = {
                        "id": node_id,
                        "title": title,
                        "content": idea.get("content", ""),
                        "type": node_type,
                        "characters": idea.get("characters", []),
                        "locations": idea.get("locations", []),
                        "page": current_page,  # Use the current page from the request
                        "page_id": page_id,  # Include the actual page_id for proper association
                        "position": {
                            "x": position_x,
                            "y": position_y
                        }
                    }
                    result.append(node)

                conn.commit()
        except Exception as e:
            conn.rollback()
            logger.error(f"Error creating brainstorm nodes: {str(e)}")
            raise
        finally:
            conn.close()

        return result
    except Exception as e:
        logger.error(f"Brainstorm idea generation failed: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Failed to generate brainstorm ideas: {str(e)}")

@router.post("/books/{book_id}/brainstorm/connect")
async def connect_brainstorm_ideas(book_id: str, request: dict = Body(...), token: str = Depends(oauth2_scheme)):
    """
    Generate AI brainstorm ideas that connect selected nodes for a given book.
    Returns (number of nodes selected)-1 structured event cards that can be added to the brainstorm board.
    """
    # Extract user_id from token
    from ..auth import get_current_user
    user_id = get_current_user(token)
    try:
        # Extract page and selected nodes from request
        current_page = request.get('page', 'page1')
        selected_nodes = request.get('selectedNodes', [])
        logger.debug(f"Connecting ideas for book {book_id} on page {current_page}")
        logger.debug(f"Selected nodes: {selected_nodes}")

        if not selected_nodes or len(selected_nodes) < 2 or len(selected_nodes) > 5:
            raise HTTPException(status_code=400, detail="Please select between 2 and 5 nodes to connect")

        # Get book information and context
        conn = get_db_connection()
        book_info = {}
        characters = []
        genre = ""
        description = ""

        try:
            # Get book details
            with conn.cursor() as cursor:
                cursor.execute("SELECT * FROM books WHERE book_id = %s", (book_id,))
                book = cursor.fetchone()
                if not book:
                    raise HTTPException(status_code=404, detail="Book not found")

                book_info = {
                    "title": book.get('title', ''),
                    "genre": book.get('genre', ''),
                    "description": book.get('description', '')
                }
                genre = book_info["genre"]
                description = book_info["description"]

                # Get characters
                cursor.execute("SELECT * FROM characters WHERE book_id = %s", (book_id,))
                characters = cursor.fetchall()

                # Get all world elements, grouped by category
                cursor.execute(
                    "SELECT * FROM world_elements WHERE book_id = %s",
                    (book_id,)
                )
                all_world_elements = cursor.fetchall()

                # Organize world elements by category
                world_elements_by_category = {}
                for element in all_world_elements:
                    category = element.get('category')
                    if category not in world_elements_by_category:
                        world_elements_by_category[category] = []
                    world_elements_by_category[category].append(element)

                # We used to get locations here for backward compatibility
                # but it's no longer needed with the new AI provider abstraction
        finally:
            conn.close()

        # Prepare character data for the prompt
        character_data = [{
            "name": char.get('name', ''),
            "role": char.get('role', ''),
            "description": char.get('description', '')
        } for char in characters[:5]]  # Limit to 5 characters for prompt size

        # Prepare world element data by category for the prompt
        world_data = {}
        for category, elements in world_elements_by_category.items():
            # Limit to 5 elements per category to keep prompt size reasonable
            world_data[category] = [{
                "name": elem.get('name', ''),
                "description": elem.get('description', ''),
                "element_type": elem.get('element_type', ''),
                "attributes": elem.get('attributes', {})
            } for elem in elements[:5]]

        # Note: We used to create location_data here for backward compatibility,
        # but it's no longer needed with the new AI provider abstraction

        # Prepare selected nodes text for the prompt
        selected_nodes_text = "\n\n".join([f"Idea {i+1}: {node.get('title', 'Untitled')} - {node.get('content', '')}"
                                         for i, node in enumerate(selected_nodes)])

        # Create the prompt for the AI
        # Calculate the number of ideas to generate (one less than the number of selected nodes)
        num_ideas_to_generate = len(selected_nodes) - 1

        prompt = f"""
        I have {len(selected_nodes)} ideas for my {genre} book titled '{book_info.get('title', 'Untitled')}'.
        Generate exactly {num_ideas_to_generate} new idea(s) that connect these concepts together:

        {selected_nodes_text}

        Book description: {description}

        Characters: {json.dumps(character_data)}
        World Elements: {json.dumps(world_data)}

        For each event idea, provide:
        1. A title (short and catchy)
        2. A detailed description of the event that connects the ideas above
        3. Which characters are involved (use characters from the provided list)
        4. Where the event takes place (use locations from the world elements)
        5. The type of event (choose from: 'event', 'character', 'location', 'idea')

        Return the results as a JSON array with exactly {num_ideas_to_generate} objects, each with these fields:
        - title: string
        - content: string (detailed description)
        - characters: array of character names that are involved
        - locations: array of location names where it takes place
        - type: string (one of 'event', 'character', 'location', 'idea')

        Make sure each event is unique and interesting, and connects the ideas in a meaningful way.
        Use the world elements to make the events more immersive and connected to the world.
        """

        # Get the text provider for the user
        text_provider = await get_text_provider(user_id)
        logger.debug(f"Using text provider: {text_provider.provider_name}")

        # Add system message to the prompt
        system_message = "You are a creative writing assistant that generates structured event ideas for authors. Return only valid JSON as specified in the prompt."
        full_prompt = f"{system_message}\n\n{prompt}"

        # Call the AI provider
        logger.debug(f"Sending request to AI provider for connected ideas")
        ai_text = await text_provider.generate_text(full_prompt)
        logger.debug(f"Raw AI response: {ai_text}")

        # Extract JSON from markdown code blocks if present
        json_match = re.search(r'```(?:json)?\s*(.+?)\s*```', ai_text, re.DOTALL)
        cleaned_json = json_match.group(1) if json_match else ai_text

        # Parse the JSON
        try:
            event_ideas = json.loads(cleaned_json)
            logger.debug(f"Parsed event ideas: {event_ideas}")
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse AI response as JSON: {e}")
            # Try to extract JSON with a more lenient approach
            try:
                # Find anything that looks like a JSON array
                array_match = re.search(r'\[\s*{.+}\s*\]', ai_text, re.DOTALL)
                if array_match:
                    event_ideas = json.loads(array_match.group(0))
                else:
                    raise ValueError("Could not extract JSON array from response")
            except Exception as inner_e:
                logger.error(f"Second attempt to parse JSON failed: {inner_e}")
                raise HTTPException(status_code=500, detail=f"Failed to parse AI response: {str(e)}")

        # Generate node IDs and prepare the response
        result = []

        # Create brainstorm pages if they don't exist
        conn = get_db_connection()
        try:
            with conn.cursor() as cursor:
                # Check if the current page exists for this book
                cursor.execute(
                    "SELECT page_id FROM brainstorm_pages WHERE book_id = %s AND title = %s LIMIT 1",
                    (book_id, current_page)
                )
                page = cursor.fetchone()
                logger.debug(f"Looking for page {current_page} for book {book_id}, found: {page is not None}")

                # Check for duplicate pages and clean them up
                cursor.execute(
                    """WITH ranked_pages AS (
                        SELECT page_id, book_id, title, sequence_number,
                               ROW_NUMBER() OVER (PARTITION BY book_id, title ORDER BY page_id) as rn
                        FROM brainstorm_pages
                        WHERE book_id = %s
                    )
                    DELETE FROM brainstorm_pages
                    WHERE page_id IN (
                        SELECT page_id FROM ranked_pages WHERE rn > 1
                    )
                    RETURNING page_id""",
                    (book_id,)
                )
                deleted_pages = cursor.fetchall()
                if deleted_pages:
                    logger.debug(f"Cleaned up {len(deleted_pages)} duplicate pages for book {book_id}")

                    if not page:
                        # Import the standardize_page_id function from brainstorm_endpoints
                        from app.api.brainstorm_endpoints import standardize_page_id

                        # Use the standardized function to get a consistent page ID
                        page_id = standardize_page_id(book_id, current_page)

                        # Extract page number for sequence_number
                        page_number = '1'  # Default
                        if current_page.startswith('page') and len(current_page) > 4:
                            try:
                                page_number = current_page[4:]
                            except:
                                page_number = '1'

                        # Check if the page already exists (double-check to avoid race conditions)
                        cursor.execute(
                            """SELECT page_id FROM brainstorm_pages WHERE page_id = %s""",
                            (page_id,)
                        )
                        existing_page = cursor.fetchone()

                        if not existing_page:
                            # Create the page with the standardized ID
                            cursor.execute(
                                """INSERT INTO brainstorm_pages (page_id, book_id, title, sequence_number)
                                VALUES (%s, %s, %s, %s)""",
                                (page_id, book_id, current_page, int(page_number))
                            )
                            logger.debug(f"Created new page {current_page} with ID {page_id}")
                        else:
                            logger.debug(f"Page {page_id} already exists, using existing page")
                    else:
                        # Handle both tuple and dict return types from the database
                        if isinstance(page, tuple):
                            page_id = page[0]
                        else:
                            page_id = page.get('page_id')
                        logger.debug(f"Using existing page with ID {page_id}")

                    # We no longer delete existing AI-generated nodes
                    # Instead, we'll just add new nodes to the page
                    logger.debug(f"Adding new AI-generated nodes to page {current_page} without removing existing ones")

                    # Create new nodes in the database
                    for i, idea in enumerate(event_ideas):
                        # Use consistent node_id format with underscore
                        node_id = f"node_{uuid.uuid4().hex}"
                        title = idea.get("title", "Untitled Event")
                        content = json.dumps({
                            "title": title,
                            "content": idea.get("content", ""),
                            "characters": idea.get("characters", []),
                            "locations": idea.get("locations", [])
                        })
                        node_type = idea.get("type", "event")
                        # Get the count of existing nodes to position new ones appropriately
                        cursor.execute(
                            "SELECT COUNT(*) FROM brainstorm_nodes WHERE page_id = %s",
                            (page_id,)
                        )
                        count_result = cursor.fetchone()
                        existing_count = 0
                        if count_result:
                            existing_count = count_result[0] if isinstance(count_result, tuple) else count_result.get('count', 0)

                        # Position new nodes in a grid pattern
                        # Each row can have 3 nodes, then start a new row
                        row = (existing_count + i) // 3
                        col = (existing_count + i) % 3
                        position_x = 100 + (col * 250)  # Space them out horizontally
                        position_y = 100 + (row * 200)  # Space them out vertically

                        # Insert the node
                        cursor.execute(
                            """INSERT INTO brainstorm_nodes
                            (node_id, page_id, content, position_x, position_y, node_type)
                            VALUES (%s, %s, %s, %s, %s, %s)
                            RETURNING node_id""",
                            (node_id, page_id, content, position_x, position_y, node_type)
                        )
                        logger.debug(f"Created new AI node {node_id} on page {current_page}")

                        # Prepare the response
                        node = {
                            "id": node_id,
                            "title": title,
                            "content": idea.get("content", ""),
                            "type": node_type,
                            "characters": idea.get("characters", []),
                            "locations": idea.get("locations", []),
                            "page": current_page,  # Use the current page from the request
                            "page_id": page_id,  # Include the actual page_id for proper association
                            "position": {
                                "x": position_x,
                                "y": position_y
                            }
                        }
                        result.append(node)

                conn.commit()
        except Exception as e:
            conn.rollback()
            logger.error(f"Error creating brainstorm nodes: {str(e)}")
            raise
        finally:
            conn.close()

        return result
    except Exception as e:
        logger.error(f"Connect brainstorm ideas failed: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Failed to connect brainstorm ideas: {str(e)}")

@router.post("/books/{book_id}/ai/generate")
async def generate_ai_response(book_id: str, request: dict, token: str = Depends(oauth2_scheme)):
    """
    Generate an AI response for a given book. Supports text-only or text+image responses.
    Request body: {"prompt": str, "response_type": "text" | "text_and_image"}
    """
    # Extract user_id from token
    from ..auth import get_current_user
    user_id = get_current_user(token)

    prompt = request.get("prompt")
    response_type = request.get("response_type", "text_and_image")  # Default to original behavior
    if not prompt:
        raise HTTPException(status_code=400, detail="Prompt is required")

    logger.debug(f"Received request: book_id={book_id}, prompt={prompt}, response_type={response_type}")

    # Fetch world data for context directly from the database
    conn = get_db_connection()
    try:
        with conn.cursor() as cursor:
            # Get locations from world elements
            cursor.execute(
                "SELECT * FROM world_elements WHERE book_id = %s AND category = 'places'",
                (book_id,)
            )
            locations = cursor.fetchall()
            location_names = ", ".join([loc.get('name', '') for loc in locations]) if locations else "None"
    except Exception as e:
        logger.error(f"Failed to fetch world data: {str(e)}")
        location_names = "None"
    finally:
        conn.close()

    # Step 1: Generate text response
    text_prompt = (
        f"{prompt} Existing locations: {location_names}. "
        "Return a single JSON object or array as specified by the prompt."
    )

    try:
        # Get the text provider for the user
        text_provider = await get_text_provider(user_id)
        logger.debug(f"Using text provider: {text_provider.provider_name}")

        # Add system message to the prompt
        system_message = "Respond with JSON as requested in the prompt."
        full_prompt = f"{system_message}\n\n{text_prompt}"

        # Call the AI provider
        logger.debug(f"Sending request to AI provider for text generation")
        character_text = await text_provider.generate_text(full_prompt)

        logger.debug(f"Raw character text: {character_text}")
        json_match = re.search(r'```json\s*(.*?)\s*```', character_text, re.DOTALL)
        cleaned_json = json_match.group(1) if json_match else character_text
        logger.debug(f"Cleaned JSON: {cleaned_json}")
        text_result = json.loads(cleaned_json)
    except Exception as e:
        logger.error(f"Text API call failed: {str(e)}", exc_info=True)
        text_result = {"error": f"Text generation failed: {str(e)}"}

    # If text-only, return early
    if response_type == "text":
        return {"response": json.dumps(text_result)}

    # Step 2: Generate image (only for text_and_image)
    if isinstance(text_result, dict) and "description" in text_result:
        image_prompt = (
            f"A portrait of {text_result.get('name', 'an unknown character')}, "
            f"{text_result.get('description', 'a mysterious figure')}, "
            f"with traits {', '.join(text_result.get('traits', ['mysterious']))}, "
            f"aged {text_result.get('age', 'unknown')}, "
            f"role as {text_result.get('role', 'adventurer')}, "
            f"race {text_result.get('race', 'human')}"
        )

        try:
            # Get the image provider for the user
            image_provider = await get_image_provider(user_id)
            logger.debug(f"Using image provider: {image_provider.provider_name}")

            # Call the AI provider
            logger.debug(f"Sending request to AI provider for image generation")
            headshot = await image_provider.generate_image(image_prompt)
            text_result["headshot"] = headshot
        except Exception as e:
            logger.error(f"Image API call failed: {str(e)}")
            text_result["headshot"] = "https://via.placeholder.com/150"

    return {"response": json.dumps(text_result)}

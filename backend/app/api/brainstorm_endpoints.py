# backend/app/api/brainstorm_endpoints.py
from fastapi import APIRouter, Depends, HTTPException, Body
from typing import Dict, List, Optional, Any
import json
import logging
import time
import uuid
from app.auth import get_current_user  # Changed from app.auth.auth_handler
from app.db.base import get_db_connection


router = APIRouter(prefix="/books/{book_id}/brainstorm", tags=["brainstorm"])
logger = logging.getLogger(__name__)


@router.post("/nodes/{node_id}/send-to-plot")
async def send_node_to_plot(
    book_id: str,
    node_id: str,
    event_data: Dict[str, Any] = Body(...),
    user_id: str = Depends(get_current_user)
):
    """Sends a brainstorm node to the plot as an event."""
    try:
        conn = get_db_connection()
        with conn.cursor() as cursor:
            # Verify the node exists and belongs to the book
            cursor.execute(
                """
                SELECT n.content, n.node_type, p.page_id
                FROM brainstorm_nodes n
                JOIN brainstorm_pages p ON n.page_id = p.page_id
                WHERE n.node_id = %s AND p.book_id = %s
                """,
                (node_id, book_id)
            )
            node = cursor.fetchone()
            if not node:
                raise HTTPException(status_code=404, detail="Node not found or not authorized")

            # Extract data from the node and request
            content = node['content']
            node_type = node.get('node_type') or 'default'

            # Try to parse content as JSON if it looks like JSON
            description = content
            if content and (content.strip().startswith('{') or content.strip().startswith('[')):
                try:
                    content_obj = json.loads(content)
                    # If content is a JSON object with a 'content' field, use that
                    if isinstance(content_obj, dict) and 'content' in content_obj:
                        description = content_obj['content']
                except Exception as e:
                    logger.warning(f"Failed to parse content as JSON: {e}")

            # Use provided title or extract from content
            title = event_data.get('title', description[:50] + ('...' if len(description) > 50 else ''))

            # Get characters and locations from event_data
            characters = event_data.get('characters', [])
            locations = event_data.get('locations', [])
            event_type = event_data.get('event_type', node_type)

            # Create a new plot event
            event_id = f"event_{uuid.uuid4().hex[:8]}"

            # Use the description from event_data if available, otherwise use the processed description
            final_description = event_data.get('description', description)

            cursor.execute(
                """
                INSERT INTO plot_events (
                    event_id, book_id, title, description, is_in_bank, source_node_id, event_type
                ) VALUES (%s, %s, %s, %s, %s, %s, %s)
                RETURNING event_id
                """,
                (event_id, book_id, title, final_description, True, node_id, event_type)
            )

            # Add character associations
            for character in characters:
                cursor.execute(
                    """
                    INSERT INTO event_characters (event_id, character_name)
                    VALUES (%s, %s)
                    """,
                    (event_id, character)
                )

            # Add location associations
            for location in locations:
                cursor.execute(
                    """
                    INSERT INTO event_locations (event_id, location_name)
                    VALUES (%s, %s)
                    """,
                    (event_id, location)
                )

            conn.commit()

            # Return the created event
            return {
                "event_id": event_id,
                "title": title,
                "description": final_description,
                "is_in_bank": True,
                "characters": characters,
                "locations": locations,
                "type": event_type,  # Use 'type' to match frontend expectations
                "source_node_id": node_id
            }
    except Exception as e:
        conn.rollback()
        logger.error(f"Error sending node to plot: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error sending node to plot: {str(e)}")


def standardize_page_id(book_id: str, frontend_page_id: str) -> str:
    """
    Create a consistent page ID format.

    Args:
        book_id: The book ID
        frontend_page_id: The page ID from the frontend

    Returns:
        A standardized book-specific page ID
    """
    # Extract page number if frontend_page_id is in format 'page1', 'page2', etc.
    page_number = '1'  # Default
    if frontend_page_id.startswith('page') and len(frontend_page_id) > 4:
        try:
            page_number = frontend_page_id[4:]
        except:
            page_number = '1'

    # Create a consistent format: page_book_id_number
    # This is the canonical format we'll use going forward
    canonical_format = f"page_{book_id}_{page_number}"

    # Check if this page already exists in the database
    conn = get_db_connection()
    try:
        with conn.cursor() as cursor:
            # First check the canonical format
            cursor.execute(
                "SELECT page_id FROM brainstorm_pages WHERE page_id = %s",
                (canonical_format,)
            )
            result = cursor.fetchone()
            if result:
                logger.debug(f"Found page with canonical format: {canonical_format}")
                return canonical_format

            # Try alternative formats that might exist in the database
            alternative_formats = [
                f"{book_id}_{frontend_page_id}",  # Format: book_id_page1
                f"page_{book_id.replace('-', '_')}_{page_number}",  # Format with underscores
                f"{book_id}_page{page_number}",  # Another possible format
            ]

            for alt_format in alternative_formats:
                cursor.execute(
                    "SELECT page_id FROM brainstorm_pages WHERE page_id = %s",
                    (alt_format,)
                )
                result = cursor.fetchone()
                if result:
                    logger.debug(f"Found page with alternative format: {alt_format}")

                    # Instead of trying to update the page_id (which can cause foreign key violations),
                    # just return the existing page_id
                    logger.debug(f"Using existing page_id: {alt_format} instead of canonical format")
                    return alt_format

            # If we get here, no existing page was found
            logger.debug(f"No existing page found, using canonical format: {canonical_format}")
            return canonical_format
    finally:
        conn.close()

@router.post("")
def update_brainstorm(
    book_id: str,
    brainstorm: dict,
    user_id: str = Depends(get_current_user)
):
    logger.debug(f"Received brainstorm update for book: {book_id}")
    logger.debug(f"Brainstorm data structure: Pages count: {len(brainstorm.get('pages', {}))}")

    # Log page IDs
    page_ids = list(brainstorm.get('pages', {}).keys())
    logger.debug(f"Page IDs in request: {page_ids}")

    # Log sample node data for first page if available
    if page_ids and 'pages' in brainstorm:
        first_page = brainstorm['pages'][page_ids[0]]
        nodes_count = len(first_page.get('nodes', []))
        shapes_count = len(first_page.get('shapes', []))
        logger.debug(f"First page '{page_ids[0]}' has {nodes_count} nodes and {shapes_count} shapes")

        # Log first node details if available
        if nodes_count > 0:
            first_node = first_page['nodes'][0]
            logger.debug(f"Sample node data: ID={first_node.get('id')}, Type={first_node.get('type')}, Text={first_node.get('text')[:30]}...")

    try:
        conn = get_db_connection()

        # Process pages
        for frontend_page_id, page_data in brainstorm.get('pages', {}).items():
            # Generate a standardized page_id for this book
            book_specific_page_id = standardize_page_id(book_id, frontend_page_id)
            logger.debug(f"Processing page: {frontend_page_id} (DB ID: {book_specific_page_id})")

            with conn.cursor() as cursor:
                # Check if page exists
                cursor.execute(
                    "SELECT page_id FROM brainstorm_pages WHERE book_id = %s AND page_id = %s",
                    (book_id, book_specific_page_id)
                )
                page_exists = cursor.fetchone()
                logger.debug(f"Page {book_specific_page_id} exists: {page_exists is not None}")

                # Get the highest sequence number for this book
                cursor.execute(
                    "SELECT MAX(sequence_number) FROM brainstorm_pages WHERE book_id = %s",
                    (book_id,)
                )
                result = cursor.fetchone()
                max_sequence = None
                if result:
                    # Try to access as tuple first, then as dict if that fails
                    try:
                        max_sequence = result[0]
                    except (IndexError, KeyError):
                        # If it's a dictionary, try to get the value by column name
                        max_sequence = result.get('max') or result.get('MAX(sequence_number)')
                sequence_number = 1 if max_sequence is None else max_sequence + 1

                # Create or update page
                if not page_exists:
                    cursor.execute(
                        "INSERT INTO brainstorm_pages (book_id, page_id, title, sequence_number) VALUES (%s, %s, %s, %s)",
                        (book_id, book_specific_page_id, page_data.get('title', f'Page {frontend_page_id}'), sequence_number)
                    )
                    logger.debug(f"Created new page: {book_specific_page_id}")
                else:
                    cursor.execute(
                        "UPDATE brainstorm_pages SET title = %s WHERE book_id = %s AND page_id = %s",
                        (page_data.get('title', f'Page {frontend_page_id}'), book_id, book_specific_page_id)
                    )
                    logger.debug(f"Updated existing page: {book_specific_page_id}")

                # Process nodes
                if 'nodes' in page_data:
                    logger.debug(f"Processing {len(page_data['nodes'])} nodes for page {frontend_page_id}")

                    # Check if the brainstorm_nodes table exists
                    cursor.execute(
                        "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'brainstorm_nodes')"
                    )
                    result = cursor.fetchone()
                    table_exists = False
                    if result:
                        # Try to access as tuple first, then as dict if that fails
                        try:
                            table_exists = result[0]
                        except (IndexError, KeyError):
                            # If it's a dictionary, try to get the value by column name
                            table_exists = result.get('exists') or result.get('EXISTS')

                    if not table_exists:
                        logger.warning("brainstorm_nodes table does not exist, skipping node processing")
                        continue

                    # First, get existing nodes for this page
                    cursor.execute(
                        "SELECT node_id FROM brainstorm_nodes WHERE page_id = %s",
                        (book_specific_page_id,)
                    )
                    # Process query results safely
                    existing_nodes = set()
                    for row in cursor.fetchall():
                        try:
                            node_id = row[0]
                        except (IndexError, KeyError):
                            # If it's a dictionary, try to get the value by column name
                            node_id = row.get('node_id')
                        if node_id:
                            existing_nodes.add(node_id)

                    # Update or insert nodes
                    for node in page_data['nodes']:
                        node_id = node.get('id')
                        if not node_id:
                            continue

                        # Extract node properties
                        position_x = None
                        position_y = None
                        node_type = 'Custom'

                        # Handle different node formats
                        if 'position' in node:
                            # ReactFlow format
                            position_x = node['position'].get('x', 0) if isinstance(node['position'], dict) else 0
                            position_y = node['position'].get('y', 0) if isinstance(node['position'], dict) else 0
                            node_type = node.get('type', 'cardNode')
                        else:
                            # Direct format
                            position_x = node.get('x', 0)
                            position_y = node.get('y', 0)
                            node_type = node.get('type', 'Custom')

                        # Store the complete node data as JSON
                        content = json.dumps(node)

                        logger.debug(f"Processing node {node_id} of type {node_type} at position ({position_x}, {position_y})")

                        if node_id in existing_nodes:
                            cursor.execute(
                                "UPDATE brainstorm_nodes SET content = %s, position_x = %s, position_y = %s, node_type = %s WHERE page_id = %s AND node_id = %s",
                                (content, position_x, position_y, node_type, book_specific_page_id, node_id)
                            )
                            existing_nodes.remove(node_id)
                        else:
                            cursor.execute(
                                "INSERT INTO brainstorm_nodes (page_id, node_id, content, position_x, position_y, node_type) VALUES (%s, %s, %s, %s, %s, %s)",
                                (book_specific_page_id, node_id, content, position_x, position_y, node_type)
                            )

                    # Delete nodes that no longer exist
                    for node_id in existing_nodes:
                        cursor.execute(
                            "DELETE FROM brainstorm_nodes WHERE page_id = %s AND node_id = %s",
                            (book_specific_page_id, node_id)
                        )

                # Process shapes
                if 'shapes' in page_data:
                    # Check if the brainstorm_shapes table exists
                    cursor.execute(
                        "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'brainstorm_shapes')"
                    )
                    result = cursor.fetchone()
                    table_exists = False
                    if result:
                        # Try to access as tuple first, then as dict if that fails
                        try:
                            table_exists = result[0]
                        except (IndexError, KeyError):
                            # If it's a dictionary, try to get the value by column name
                            table_exists = result.get('exists') or result.get('EXISTS')

                    if not table_exists:
                        logger.warning("brainstorm_shapes table does not exist, creating it")
                        # Create the brainstorm_shapes table
                        cursor.execute("""
                            CREATE TABLE IF NOT EXISTS brainstorm_shapes (
                                shape_id TEXT NOT NULL,
                                page_id TEXT NOT NULL,
                                shape_type TEXT NOT NULL,
                                position_x DOUBLE PRECISION,
                                position_y DOUBLE PRECISION,
                                width DOUBLE PRECISION,
                                height DOUBLE PRECISION,
                                properties JSONB,
                                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                PRIMARY KEY (shape_id, page_id),
                                FOREIGN KEY (page_id) REFERENCES brainstorm_pages(page_id) ON DELETE CASCADE
                            )
                        """)
                        conn.commit()
                        logger.info("Created brainstorm_shapes table")

                    # First, get existing shapes for this page
                    cursor.execute(
                        "SELECT shape_id FROM brainstorm_shapes WHERE page_id = %s",
                        (book_specific_page_id,)
                    )
                    # Process query results safely
                    existing_shapes = set()
                    for row in cursor.fetchall():
                        try:
                            shape_id = row[0]
                        except (IndexError, KeyError):
                            # If it's a dictionary, try to get the value by column name
                            shape_id = row.get('shape_id')
                        if shape_id:
                            existing_shapes.add(shape_id)

                    # Update or insert shapes
                    for shape in page_data['shapes']:
                        shape_id = shape.get('id')
                        if not shape_id:
                            continue

                        shape_data = json.dumps(shape)

                        if shape_id in existing_shapes:
                            cursor.execute(
                                "UPDATE brainstorm_shapes SET data = %s WHERE page_id = %s AND shape_id = %s",
                                (shape_data, book_specific_page_id, shape_id)
                            )
                            existing_shapes.remove(shape_id)
                        else:
                            cursor.execute(
                                "INSERT INTO brainstorm_shapes (page_id, shape_id, data) VALUES (%s, %s, %s)",
                                (book_specific_page_id, shape_id, shape_data)
                            )

                    # Delete shapes that no longer exist
                    for shape_id in existing_shapes:
                        cursor.execute(
                            "DELETE FROM brainstorm_shapes WHERE page_id = %s AND shape_id = %s",
                            (book_specific_page_id, shape_id)
                        )

        conn.commit()
        logger.debug(f"Successfully saved brainstorm data for book {book_id}")
        return {"book_id": book_id, "status": "success"}
    except Exception as e:
        conn.rollback()
        error_type = type(e).__name__
        error_msg = str(e)
        error_detail = getattr(e, '__dict__', {})
        logger.error(f"Error updating brainstorm: {error_type} - {error_msg}")
        logger.error(f"Error details: {error_detail}")
        logger.exception("Full traceback:")
        raise HTTPException(status_code=500, detail=f"Error updating brainstorm: {error_type} - {error_msg}")
    finally:
        conn.close()

@router.get("")
def get_brainstorm(book_id: str, user_id: str = Depends(get_current_user)):
    try:
        conn = get_db_connection()
        # Get all pages for this book
        with conn.cursor() as cursor:
            cursor.execute(
                "SELECT page_id, title FROM brainstorm_pages WHERE book_id = %s ORDER BY sequence_number",
                (book_id,)
            )
            # Process query results safely
            pages = {}
            processed_page_ids = set()  # Track processed page IDs to avoid duplicates

            for row in cursor.fetchall():
                # Try to access as tuple first, then as dict if that fails
                try:
                    db_page_id = row[0]
                    title = row[1]
                except (IndexError, KeyError):
                    # If it's a dictionary, try to get the values by column name
                    db_page_id = row.get('page_id')
                    title = row.get('title')

                if db_page_id:
                    # Extract the frontend page_id from the book-specific page_id
                    # We want to convert all page IDs to the simple format 'page1', 'page2', etc.
                    frontend_page_id = "page1"  # Default

                    # Handle the canonical format: page_book_id_number
                    if db_page_id.startswith(f"page_{book_id}_"):
                        # Extract the page number at the end
                        page_number = db_page_id.split('_')[-1]
                        frontend_page_id = f"page{page_number}"
                        logger.debug(f"Extracted frontend_page_id '{frontend_page_id}' from canonical format '{db_page_id}'")

                    # Handle legacy format: page_book_id_with_underscores_number
                    elif db_page_id.startswith('page_'):
                        # Extract the page number at the end
                        parts = db_page_id.split('_')
                        if len(parts) > 1:
                            frontend_page_id = f"page{parts[-1]}"
                        logger.debug(f"Extracted frontend_page_id '{frontend_page_id}' from legacy format '{db_page_id}'")

                    # Handle book_id_page1 format
                    elif db_page_id.startswith(f"{book_id}_page"):
                        # Extract the page number after 'page'
                        page_part = db_page_id.split('_')[-1]
                        if page_part.startswith('page'):
                            frontend_page_id = page_part
                        logger.debug(f"Extracted frontend_page_id '{frontend_page_id}' from book_id_page format '{db_page_id}'")

                    # Any other format, just use as is but log it
                    else:
                        if db_page_id.startswith(book_id):
                            # If it starts with book_id, try to extract the page part
                            parts = db_page_id.split('_')
                            if len(parts) > 1 and parts[-1].startswith('page'):
                                frontend_page_id = parts[-1]
                            else:
                                frontend_page_id = "page1"
                        else:
                            frontend_page_id = db_page_id
                        logger.debug(f"Using '{frontend_page_id}' for unknown format '{db_page_id}'")

                    # Skip if we've already processed this frontend page ID
                    if frontend_page_id in processed_page_ids:
                        logger.debug(f"Skipping duplicate page ID: {frontend_page_id} from db_page_id: {db_page_id}")
                        continue

                    processed_page_ids.add(frontend_page_id)
                    logger.debug(f"Found page in database: {db_page_id}, extracted frontend ID: {frontend_page_id}")
                    pages[frontend_page_id] = {'title': title or f'Page {frontend_page_id}', 'nodes': [], 'shapes': []}

            # If no pages exist, create a default page
            if not pages:
                pages = {'page1': {'title': 'Page 1', 'nodes': [], 'shapes': []}}

            # Check if the brainstorm_nodes table exists
            cursor.execute(
                "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'brainstorm_nodes')"
            )
            result = cursor.fetchone()
            table_exists = False
            if result:
                # Try to access as tuple first, then as dict if that fails
                try:
                    table_exists = result[0]
                except (IndexError, KeyError):
                    # If it's a dictionary, try to get the value by column name
                    table_exists = result.get('exists') or result.get('EXISTS')

            if table_exists:
                # Get all nodes for this book
                try:
                    # Get all nodes for this book, excluding those in the deleted_nodes table
                    logger.debug(f"Fetching nodes for book {book_id}, excluding deleted nodes")

                    # First, let's log all the page IDs for this book to help with debugging
                    cursor.execute(
                        "SELECT page_id FROM brainstorm_pages WHERE book_id = %s",
                        (book_id,)
                    )
                    book_pages = cursor.fetchall()
                    page_ids = [row[0] if isinstance(row, tuple) else row.get('page_id') for row in book_pages]
                    logger.debug(f"Found {len(page_ids)} pages for book {book_id}: {page_ids}")

                    # Now get all nodes, with a more flexible join condition to handle inconsistent page_id formats
                    cursor.execute(
                        """
                        SELECT n.page_id, n.node_id, n.content, n.position_x, n.position_y, n.node_type
                        FROM brainstorm_nodes n
                        JOIN brainstorm_pages p ON n.page_id = p.page_id
                        WHERE p.book_id = %s
                        AND NOT EXISTS (
                            SELECT 1 FROM deleted_nodes d WHERE d.node_id = n.node_id
                        )
                        """,
                        (book_id,)
                    )

                    # Count how many nodes we're fetching
                    all_nodes = cursor.fetchall()
                    logger.debug(f"Fetched {len(all_nodes)} non-deleted nodes for book {book_id}")

                    # Also check how many nodes are in the deleted_nodes table
                    # Put this in a separate try/except block to prevent it from affecting the main flow
                    try:
                        cursor.execute(
                            """SELECT COUNT(*) FROM deleted_nodes d
                               WHERE EXISTS (SELECT 1 FROM brainstorm_nodes n
                                            JOIN brainstorm_pages p ON n.page_id = p.page_id
                                            WHERE p.book_id = %s AND n.node_id = d.node_id)""",
                            (book_id,)
                        )

                        result = cursor.fetchone()
                        if result is not None and hasattr(result, "__getitem__"):
                            try:
                                deleted_count = result[0]
                            except (IndexError, KeyError):
                                logger.warning(f"Could not access index 0 of result: {result}")
                                deleted_count = 0
                        else:
                            logger.warning(f"Unexpected result from deleted nodes count query: {result}")
                            deleted_count = 0

                        logger.debug(f"Found {deleted_count} deleted nodes for book {book_id}")
                    except Exception as e:
                        logger.error(f"Error counting deleted nodes: {e}")
                        deleted_count = 0

                    # Process each non-deleted node
                    logger.debug(f"Starting to process {len(all_nodes)} nodes for book {book_id}")
                    processed_node_count = 0
                    for row in all_nodes:
                        try:
                            # Try to access as tuple first
                            db_page_id = row[0]
                            node_id = row[1]
                            content = row[2]
                            position_x = row[3] if len(row) > 3 else 0
                            position_y = row[4] if len(row) > 4 else 0
                            node_type = row[5] if len(row) > 5 else 'idea'
                        except (IndexError, KeyError):
                            # If it's a dictionary, try to get the values by column name
                            db_page_id = row.get('page_id')
                            node_id = row.get('node_id')
                            content = row.get('content')
                            position_x = row.get('position_x', 0)
                            position_y = row.get('position_y', 0)
                            node_type = row.get('node_type', 'idea')

                        if not db_page_id or not content:
                            logger.warning(f"Skipping node with missing data: {row}")
                            continue

                        # Extract the frontend page_id from the book-specific page_id
                        # We want to convert all page IDs to the simple format 'page1', 'page2', etc.
                        frontend_page_id = "page1"  # Default

                        # Handle the canonical format: page_book_id_number
                        if db_page_id.startswith(f"page_{book_id}_"):
                            # Extract the page number at the end
                            page_number = db_page_id.split('_')[-1]
                            frontend_page_id = f"page{page_number}"
                            logger.debug(f"Extracted frontend_page_id '{frontend_page_id}' from canonical format '{db_page_id}'")

                        # Handle legacy format: page_book_id_with_underscores_number
                        elif db_page_id.startswith('page_'):
                            # Extract the page number at the end
                            parts = db_page_id.split('_')
                            if len(parts) > 1:
                                frontend_page_id = f"page{parts[-1]}"
                            logger.debug(f"Extracted frontend_page_id '{frontend_page_id}' from legacy format '{db_page_id}'")

                        # Handle book_id_page1 format
                        elif db_page_id.startswith(f"{book_id}_page"):
                            # Extract the page number after 'page'
                            page_part = db_page_id.split('_')[-1]
                            if page_part.startswith('page'):
                                frontend_page_id = page_part
                            logger.debug(f"Extracted frontend_page_id '{frontend_page_id}' from book_id_page format '{db_page_id}'")

                        # Any other format, just use as is but log it
                        else:
                            if db_page_id.startswith(book_id):
                                # If it starts with book_id, try to extract the page part
                                parts = db_page_id.split('_')
                                if len(parts) > 1 and parts[-1].startswith('page'):
                                    frontend_page_id = parts[-1]
                                else:
                                    frontend_page_id = "page1"
                            else:
                                frontend_page_id = db_page_id
                            logger.debug(f"Using '{frontend_page_id}' for unknown format '{db_page_id}'")
                        if frontend_page_id in pages:
                            try:
                                # Try to parse the content as JSON
                                node_content = json.loads(content)
                                pages[frontend_page_id]['nodes'].append(node_content)
                                processed_node_count += 1
                                logger.debug(f"Added node {node_id} to page {frontend_page_id}")
                            except json.JSONDecodeError:
                                # If content is not valid JSON, create a node object from the database fields
                                logger.debug(f"Creating node object from database fields for node {node_id}")
                                node_obj = {
                                    'id': node_id,
                                    'type': 'cardNode',
                                    'data': {
                                        'label': 'Untitled',
                                        'content': content,
                                        'type': node_type
                                    },
                                    'position': {
                                        'x': float(position_x),
                                        'y': float(position_y)
                                    }
                                }
                                pages[frontend_page_id]['nodes'].append(node_obj)
                                processed_node_count += 1
                                logger.debug(f"Added node {node_id} to page {frontend_page_id} (from database fields)")
                except Exception as e:
                    error_type = type(e).__name__
                    error_msg = str(e)
                    error_detail = getattr(e, '__dict__', {})
                    logger.error(f"Error fetching nodes: {error_type} - {error_msg}")
                    logger.error(f"Error details: {error_detail}")
                    logger.exception("Full traceback:")
                    # Continue with the rest of the function instead of returning empty data

                # Log summary of node processing
                logger.debug(f"Successfully processed {processed_node_count} out of {len(all_nodes)} nodes for book {book_id}")

                # Check if we have any nodes in the pages
                total_nodes_in_pages = sum(len(page.get('nodes', [])) for page in pages.values())
                logger.debug(f"Total nodes added to pages: {total_nodes_in_pages}")

            # Check if the brainstorm_shapes table exists
            cursor.execute(
                "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'brainstorm_shapes')"
            )
            result = cursor.fetchone()
            table_exists = False
            if result:
                # Try to access as tuple first, then as dict if that fails
                try:
                    table_exists = result[0]
                except (IndexError, KeyError):
                    # If it's a dictionary, try to get the value by column name
                    table_exists = result.get('exists') or result.get('EXISTS')

            if table_exists:
                # Get all shapes for this book
                try:
                    cursor.execute(
                        "SELECT s.page_id, s.shape_id, s.shape_type, s.position_x, s.position_y, s.width, s.height, s.properties FROM brainstorm_shapes s JOIN brainstorm_pages p ON s.page_id = p.page_id WHERE p.book_id = %s",
                        (book_id,)
                    )
                    for row in cursor.fetchall():
                        try:
                            # Try to access as tuple first
                            db_page_id = row[0]
                            shape_id = row[1]
                            shape_type = row[2]
                            position_x = row[3]
                            position_y = row[4]
                            width = row[5]
                            height = row[6]
                            properties = row[7]
                        except (IndexError, KeyError):
                            # If it's a dictionary, try to get the values by column name
                            db_page_id = row.get('page_id')
                            shape_id = row.get('shape_id')
                            shape_type = row.get('shape_type')
                            position_x = row.get('position_x')
                            position_y = row.get('position_y')
                            width = row.get('width')
                            height = row.get('height')
                            properties = row.get('properties')

                        if not db_page_id or not shape_id:
                            logger.warning(f"Skipping shape with missing data: {row}")
                            continue

                        # Extract the frontend page_id from the book-specific page_id
                        frontend_page_id = db_page_id.split('_', 1)[1] if '_' in db_page_id else db_page_id
                        if frontend_page_id in pages:
                            # Create a shape object from the database fields
                            shape_data = {
                                'id': shape_id,
                                'type': shape_type,
                                'x': position_x,
                                'y': position_y,
                                'width': width,
                                'height': height
                            }

                            # Add properties if available
                            if properties:
                                try:
                                    if isinstance(properties, str):
                                        props = json.loads(properties)
                                    else:
                                        props = properties
                                    shape_data.update(props)
                                except json.JSONDecodeError:
                                    logger.error(f"Error decoding shape properties: {properties}")

                            pages[frontend_page_id]['shapes'].append(shape_data)
                            logger.debug(f"Added shape to page {frontend_page_id}")
                except Exception as e:
                    error_type = type(e).__name__
                    error_msg = str(e)
                    logger.error(f"Error fetching shapes: {error_type} - {error_msg}")
                    logger.exception("Full traceback:")
            else:
                logger.warning("brainstorm_shapes table does not exist, no shapes will be returned")
                # Create the brainstorm_shapes table
                try:
                    cursor.execute("""
                        CREATE TABLE IF NOT EXISTS brainstorm_shapes (
                            shape_id TEXT NOT NULL,
                            page_id TEXT NOT NULL,
                            shape_type TEXT NOT NULL,
                            position_x DOUBLE PRECISION,
                            position_y DOUBLE PRECISION,
                            width DOUBLE PRECISION,
                            height DOUBLE PRECISION,
                            properties JSONB,
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            PRIMARY KEY (shape_id, page_id),
                            FOREIGN KEY (page_id) REFERENCES brainstorm_pages(page_id) ON DELETE CASCADE
                        )
                    """)
                    conn.commit()
                    logger.info("Created brainstorm_shapes table")
                except Exception as e:
                    error_type = type(e).__name__
                    error_msg = str(e)
                    logger.error(f"Error creating brainstorm_shapes table: {error_type} - {error_msg}")
                    logger.exception("Full traceback:")

        # We no longer need to filter out deleted nodes from book_memory as we're migrating away from it
        # Instead, we'll ensure all deleted nodes are properly tracked in the deleted_nodes table
        try:
            # Get all deleted node IDs
            with conn.cursor() as cursor:
                cursor.execute("SELECT node_id FROM deleted_nodes")
                deleted_node_ids = set(row[0] if isinstance(row, tuple) else row.get('node_id')
                                     for row in cursor.fetchall())

                if deleted_node_ids:
                    logger.debug(f"Found {len(deleted_node_ids)} deleted node IDs to filter from response")

                    # Filter out deleted nodes from the response
                    for page_id, page_data in pages.items():
                        if 'nodes' in page_data:
                            original_length = len(page_data['nodes'])
                            page_data['nodes'] = [node for node in page_data['nodes']
                                                 if node.get('id') not in deleted_node_ids]

                            # Log if we removed any nodes
                            if len(page_data['nodes']) < original_length:
                                logger.debug(f"Removed {original_length - len(page_data['nodes'])} deleted nodes from response for page {page_id}")
        except Exception as filter_error:
            logger.error(f"Error filtering deleted nodes from response: {str(filter_error)}")
            # Continue with the return process even if filtering fails

        return {"book_id": book_id, "brainstorm": {"pages": pages}}
    except Exception as e:
        error_type = type(e).__name__
        error_msg = str(e)
        error_detail = getattr(e, '__dict__', {})
        logger.error(f"Error fetching brainstorm: {error_type} - {error_msg}")
        logger.error(f"Error details: {error_detail}")
        logger.exception("Full traceback:")

        # Return a default brainstorm structure on error
        return {
            "book_id": book_id,
            "brainstorm": {
                "pages": {
                    "page1": {
                        "title": "Page 1",
                        "nodes": [],
                        "shapes": []
                    }
                }
            }
        }
    finally:
        if 'conn' in locals():
            conn.close()


@router.post("/cards")
def create_brainstorm_card(
    book_id: str,
    card_data: dict,
    user_id: str = Depends(get_current_user)
):
    """
    Create a new brainstorm card.

    Args:
        book_id: The book ID
        card_data: The card data
        user_id: The user ID

    Returns:
        A success message
    """
    try:
        conn = get_db_connection()
        with conn.cursor() as cursor:
            # Get or create the page
            page_id = card_data.get('page', 'page1')
            book_specific_page_id = standardize_page_id(book_id, page_id)

            # Check if page exists
            cursor.execute(
                "SELECT 1 FROM brainstorm_pages WHERE page_id = %s",
                (book_specific_page_id,)
            )
            page_exists = cursor.fetchone() is not None

            if not page_exists:
                # Create the page
                cursor.execute(
                    "SELECT MAX(sequence_number) FROM brainstorm_pages WHERE book_id = %s",
                    (book_id,)
                )
                result = cursor.fetchone()
                sequence_number = 1
                if result and result[0]:
                    sequence_number = result[0] + 1

                cursor.execute(
                    "INSERT INTO brainstorm_pages (book_id, page_id, title, sequence_number) VALUES (%s, %s, %s, %s)",
                    (book_id, book_specific_page_id, f"Page {page_id}", sequence_number)
                )
                logger.debug(f"Created new page: {book_specific_page_id}")

            # Create the card
            card_id = card_data.get('id')

            # All node IDs should use node_ format
            if not card_id:
                # Generate a new ID if none provided - use UUID for guaranteed uniqueness
                card_id = f"node_{uuid.uuid4().hex}"
                logger.debug(f"Generated new UUID-based node ID: {card_id}")

            # Extract position
            position_x = 0
            position_y = 0
            if 'position' in card_data:
                position_x = card_data['position'].get('x', 0)
                position_y = card_data['position'].get('y', 0)

            # Extract node type
            node_type = card_data.get('type', 'idea')

            # Store the complete card data as JSON
            content = json.dumps(card_data)

            logger.debug(f"Creating node {card_id} of type {node_type} at position ({position_x}, {position_y})")

            cursor.execute(
                "INSERT INTO brainstorm_nodes (page_id, node_id, content, position_x, position_y, node_type) VALUES (%s, %s, %s, %s, %s, %s)",
                (book_specific_page_id, card_id, content, position_x, position_y, node_type)
            )

            conn.commit()
            logger.debug(f"Successfully created brainstorm card {card_id} for book {book_id}")
            return {"status": "success", "card_id": card_id}
    except Exception as e:
        conn.rollback()
        error_type = type(e).__name__
        error_msg = str(e)
        logger.error(f"Error creating brainstorm card: {error_type} - {error_msg}")
        logger.exception("Full traceback:")
        raise HTTPException(status_code=500, detail=f"Error creating brainstorm card: {error_type} - {error_msg}")
    finally:
        conn.close()


@router.put("/cards/{card_id}")
def update_brainstorm_card(
    book_id: str,
    card_id: str,
    card_data: dict,
    user_id: str = Depends(get_current_user)
):
    """
    Update a brainstorm card.

    Args:
        book_id: The book ID
        card_id: The card ID
        card_data: The updated card data
        user_id: The user ID

    Returns:
        A success message
    """
    try:
        conn = get_db_connection()
        with conn.cursor() as cursor:
            # Get the page
            page_id = card_data.get('page', 'page1')
            book_specific_page_id = standardize_page_id(book_id, page_id)

            # All node IDs should already be in node_ format
            standardized_card_id = card_id

            # Extract position
            position_x = 0
            position_y = 0
            if 'position' in card_data:
                position_x = card_data['position'].get('x', 0)
                position_y = card_data['position'].get('y', 0)

            # Extract node type
            node_type = card_data.get('type', 'idea')

            # Store the complete card data as JSON
            content = json.dumps(card_data)

            logger.debug(f"Updating node {card_id} of type {node_type} at position ({position_x}, {position_y})")

            # First check if this node has ever been deleted
            cursor.execute(
                "SELECT 1 FROM deleted_nodes WHERE node_id = %s OR node_id = %s",
                (card_id, standardized_card_id)
            )
            ever_deleted = cursor.fetchone() is not None

            if ever_deleted:
                logger.debug(f"Node {card_id} was previously deleted, removing from deleted_nodes to allow update")

                # Remove from deleted_nodes to allow the node to be recreated
                cursor.execute(
                    "DELETE FROM deleted_nodes WHERE node_id = %s",
                    (card_id,)
                )
                logger.debug(f"Removed node {card_id} from deleted_nodes table")

            # Check if the card exists in the nodes table
            cursor.execute(
                "SELECT 1 FROM brainstorm_nodes WHERE node_id = %s",
                (card_id,)
            )
            card_exists = cursor.fetchone() is not None

            if card_exists:
                cursor.execute(
                    "UPDATE brainstorm_nodes SET page_id = %s, content = %s, position_x = %s, position_y = %s, node_type = %s WHERE node_id = %s",
                    (book_specific_page_id, content, position_x, position_y, node_type, card_id)
                )
                logger.debug(f"Updated existing card: {card_id}")
            else:
                # Only create a new card if it wasn't recently deleted
                cursor.execute(
                    "INSERT INTO brainstorm_nodes (page_id, node_id, content, position_x, position_y, node_type) VALUES (%s, %s, %s, %s, %s, %s)",
                    (book_specific_page_id, card_id, content, position_x, position_y, node_type)
                )
                logger.debug(f"Created new card: {card_id}")

            conn.commit()
            logger.debug(f"Successfully updated brainstorm card {card_id} for book {book_id}")
            return {"status": "success", "card_id": card_id}
    except Exception as e:
        conn.rollback()
        error_type = type(e).__name__
        error_msg = str(e)
        logger.error(f"Error updating brainstorm card: {error_type} - {error_msg}")
        logger.exception("Full traceback:")
        raise HTTPException(status_code=500, detail=f"Error updating brainstorm card: {error_type} - {error_msg}")
    finally:
        conn.close()


@router.delete("/cards/{card_id}")
def delete_brainstorm_card(
    book_id: str,
    card_id: str,
    user_id: str = Depends(get_current_user)
):
    """
    Delete a brainstorm card.

    Args:
        book_id: The book ID
        card_id: The card ID
        user_id: The user ID

    Returns:
        A success message
    """
    try:
        conn = get_db_connection()
        with conn.cursor() as cursor:
            # First, check if the deleted_nodes table exists, create it if not
            cursor.execute(
                """CREATE TABLE IF NOT EXISTS deleted_nodes (
                    node_id VARCHAR(255) PRIMARY KEY,
                    deleted_at TIMESTAMP DEFAULT NOW()
                )"""
            )

            # All node IDs should already be in node_ format
            standardized_card_id = card_id

            # First check if this node is already in the deleted_nodes table
            cursor.execute(
                "SELECT 1 FROM deleted_nodes WHERE node_id = %s OR node_id = %s",
                (card_id, standardized_card_id)
            )
            already_deleted = cursor.fetchone() is not None

            if already_deleted:
                logger.debug(f"Node {card_id} is already marked as deleted, skipping deletion")
                return {"status": "success", "card_id": card_id, "message": "Node was already deleted"}

            # First, verify the node exists and get its exact ID from the database
            cursor.execute(
                "SELECT node_id FROM brainstorm_nodes WHERE node_id = %s OR node_id = %s",
                (card_id, standardized_card_id)
            )
            result = cursor.fetchone()

            if not result:
                logger.warning(f"Node {card_id} not found in database, skipping deletion")
                return {"status": "success", "card_id": card_id, "message": "Node not found in database"}

            # Get the exact ID as stored in the database
            exact_node_id = result[0] if isinstance(result, tuple) else result.get('node_id')
            logger.debug(f"Found exact node ID in database: {exact_node_id}")

            # Delete the card using the exact ID from the database
            cursor.execute(
                "DELETE FROM brainstorm_nodes WHERE node_id = %s",
                (exact_node_id,)
            )

            # Log how many rows were affected
            deleted_count = cursor.rowcount
            logger.debug(f"Deleted {deleted_count} rows from brainstorm_nodes for node {exact_node_id}")

            # Also delete any associated data using the exact ID
            cursor.execute(
                "DELETE FROM event_characters WHERE event_id = %s",
                (exact_node_id,)
            )
            logger.debug(f"Deleted {cursor.rowcount} rows from event_characters for node {exact_node_id}")

            cursor.execute(
                "DELETE FROM event_locations WHERE event_id = %s",
                (exact_node_id,)
            )
            logger.debug(f"Deleted {cursor.rowcount} rows from event_locations for node {exact_node_id}")

            # Record the deletion in the deleted_nodes table for permanent tracking
            # Use the exact ID from the database for accuracy
            cursor.execute(
                "INSERT INTO deleted_nodes (node_id) VALUES (%s) ON CONFLICT (node_id) DO UPDATE SET deleted_at = NOW()",
                (exact_node_id,)
            )
            logger.debug(f"Added node {exact_node_id} to deleted_nodes table")

            # Also record the original ID if it's different, to ensure all variants are tracked
            if card_id != exact_node_id:
                cursor.execute(
                    "INSERT INTO deleted_nodes (node_id) VALUES (%s) ON CONFLICT (node_id) DO UPDATE SET deleted_at = NOW()",
                    (card_id,)
                )
                logger.debug(f"Also added original node ID {card_id} to deleted_nodes table")

            # The book_memory table has been removed, so we don't need to update it anymore
            # This code was previously used to remove the node from the book_memory table

            conn.commit()
            logger.debug(f"Successfully deleted brainstorm card {card_id} for book {book_id}")
            return {"status": "success", "card_id": card_id}
    except Exception as e:
        conn.rollback()
        error_type = type(e).__name__
        error_msg = str(e)
        logger.error(f"Error deleting brainstorm card: {error_type} - {error_msg}")
        logger.exception("Full traceback:")
        raise HTTPException(status_code=500, detail=f"Error deleting brainstorm card: {error_type} - {error_msg}")
    finally:
        conn.close()

@router.post("/pages")
def add_brainstorm_page(
    book_id: str,
    page_data: dict,
    user_id: str = Depends(get_current_user)
):
    try:
        conn = get_db_connection()
        try:
            with conn.cursor() as cursor:
                frontend_page_id = page_data.get('page_id')
                if not frontend_page_id:
                    # Generate a new page ID if not provided
                    cursor.execute(
                        "SELECT COUNT(*) FROM brainstorm_pages WHERE book_id = %s",
                        (book_id,)
                    )
                    result = cursor.fetchone()
                    page_count = 0
                    if result:
                        # Try to access as tuple first, then as dict if that fails
                        try:
                            page_count = result[0]
                        except (IndexError, KeyError):
                            # If it's a dictionary, try to get the value by column name
                            page_count = result.get('count') or result.get('COUNT(*)') or 0
                    frontend_page_id = f"page{page_count + 1}"

                # Generate a standardized book-specific page ID
                book_specific_page_id = standardize_page_id(book_id, frontend_page_id)
                logger.debug(f"Creating new page with frontend ID: {frontend_page_id}, DB ID: {book_specific_page_id}")

                title = page_data.get('title', f'Page {frontend_page_id}')

                # Get the highest sequence number for this book
                cursor.execute(
                    "SELECT MAX(sequence_number) FROM brainstorm_pages WHERE book_id = %s",
                    (book_id,)
                )
                result = cursor.fetchone()
                max_sequence = None
                if result:
                    # Try to access as tuple first, then as dict if that fails
                    try:
                        max_sequence = result[0]
                    except (IndexError, KeyError):
                        # If it's a dictionary, try to get the value by column name
                        max_sequence = result.get('max') or result.get('MAX(sequence_number)')
                sequence_number = 1 if max_sequence is None else max_sequence + 1

                # Check if page already exists
                cursor.execute(
                    "SELECT page_id FROM brainstorm_pages WHERE book_id = %s AND page_id = %s",
                    (book_id, book_specific_page_id)
                )
                result = cursor.fetchone()
                if result:
                    logger.warning(f"Page {book_specific_page_id} already exists for book {book_id}, skipping creation")
                    return {"book_id": book_id, "page_id": frontend_page_id, "status": "exists"}

                # Insert the new page
                cursor.execute(
                    "INSERT INTO brainstorm_pages (book_id, page_id, title, sequence_number) VALUES (%s, %s, %s, %s)",
                    (book_id, book_specific_page_id, title, sequence_number)
                )
                logger.info(f"Created new brainstorm page {book_specific_page_id} for book {book_id}")
            conn.commit()
            return {"book_id": book_id, "page_id": frontend_page_id, "status": "success"}
        except Exception as e:
            if conn:
                conn.rollback()
            error_type = type(e).__name__
            error_msg = str(e)
            logger.error(f"Database error adding brainstorm page: {error_type} - {error_msg}")
            logger.exception("Full traceback:")
            raise e
    except Exception as e:
        error_type = type(e).__name__
        error_msg = str(e)
        error_detail = getattr(e, '__dict__', {})
        logger.error(f"Error adding brainstorm page: {error_type} - {error_msg}")
        logger.error(f"Error details: {error_detail}")
        logger.exception("Full traceback:")
        raise HTTPException(status_code=500, detail=f"Error adding brainstorm page: {error_type} - {error_msg}")
    finally:
        if 'conn' in locals() and conn:
            conn.close()

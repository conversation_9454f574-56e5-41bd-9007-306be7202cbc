"""
Book Import API Endpoints

This module provides API endpoints for importing books into the system,
including synchronization between PostgreSQL and Qdrant.
"""

from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from pydantic import BaseModel
from typing import Dict, Any, Optional, List
import logging
import asyncio
import sys
import os

# Add the parent directory to the path to allow importing from qdrant_integration
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from app.auth import get_current_user
from app.db.book_repository import BookRepository

# Import Qdrant integration
try:
    from qdrant_integration.book_import_sync import BookImportSync
    from qdrant_integration.enhanced_collection_manager import EnhancedCollectionManager
    QDRANT_AVAILABLE = True
except ImportError as e:
    QDRANT_AVAILABLE = False
    print(f"Qdrant integration not available: {str(e)}")

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/books", tags=["books"])

# Initialize repositories
book_repo = BookRepository()

# Database configuration for sync service
DB_CONFIG = {
    "host": "localhost",
    "port": 5432,
    "user": "project1_user",
    "password": "rHEWX*.V.Adaline..",
    "database": "bookapp1"
}

class ImportResponse(BaseModel):
    """Response model for book import operations"""
    success: bool
    message: str
    book_id: int
    elements_count: Optional[int] = None
    characters_count: Optional[int] = None
    status: Optional[str] = None
    error: Optional[str] = None

class CollectionHealthResponse(BaseModel):
    """Response model for collection health information"""
    status: str
    collections: Dict[str, Any]
    user_id: str

async def import_book_task(book_id: int, user_id: str):
    """
    Background task for importing a book
    
    Args:
        book_id: ID of the book to import
        user_id: ID of the user importing the book
    """
    if not QDRANT_AVAILABLE:
        logger.error("Qdrant integration not available. Cannot import book.")
        return
        
    try:
        # Initialize book import sync
        book_import = BookImportSync(DB_CONFIG)
        
        # Import book
        result = await book_import.import_book(book_id, int(user_id))
        
        # Log result
        if result["success"]:
            logger.info(f"Book {book_id} imported successfully")
        else:
            logger.error(f"Failed to import book {book_id}: {result.get('message')}")
    except Exception as e:
        logger.error(f"Error in book import background task: {str(e)}")

@router.post("/{book_id}/import", response_model=ImportResponse)
async def import_book(
    book_id: int,
    background_tasks: BackgroundTasks,
    user_id: str = Depends(get_current_user)
):
    """
    Import a book's data into Qdrant for vector search
    
    This endpoint triggers the synchronization of book data between PostgreSQL and Qdrant.
    The import process runs in the background to avoid blocking the API response.
    
    Args:
        book_id: ID of the book to import
        background_tasks: FastAPI background tasks
        user_id: ID of the user (from authentication)
        
    Returns:
        ImportResponse: Status of the import operation
    """
    if not QDRANT_AVAILABLE:
        raise HTTPException(status_code=503, detail="Qdrant integration not available")
        
    try:
        # Check if book exists and belongs to user
        book = book_repo.get_book_by_id(book_id)
        if not book:
            raise HTTPException(status_code=404, detail=f"Book with ID {book_id} not found")
        
        if str(book["user_id"]) != user_id:
            raise HTTPException(status_code=403, detail="You don't have permission to import this book")
        
        # Add import task to background tasks
        background_tasks.add_task(import_book_task, book_id, user_id)
        
        return {
            "success": True,
            "message": f"Book import started for book {book_id}",
            "book_id": book_id
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error starting book import: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error starting book import: {str(e)}")

@router.get("/{book_id}/import/status", response_model=Dict[str, Any])
async def get_import_status(
    book_id: int,
    user_id: str = Depends(get_current_user)
):
    """
    Get the status of a book import operation
    
    Args:
        book_id: ID of the book
        user_id: ID of the user (from authentication)
        
    Returns:
        Dict: Status information about the import operation
    """
    if not QDRANT_AVAILABLE:
        raise HTTPException(status_code=503, detail="Qdrant integration not available")
        
    try:
        # Check if book exists and belongs to user
        book = book_repo.get_book_by_id(book_id)
        if not book:
            raise HTTPException(status_code=404, detail=f"Book with ID {book_id} not found")
        
        if str(book["user_id"]) != user_id:
            raise HTTPException(status_code=403, detail="You don't have permission to view this book's import status")
        
        # TODO: Implement status tracking for import operations
        # For now, we'll return a placeholder status
        return {
            "book_id": book_id,
            "status": "unknown",
            "message": "Import status tracking not yet implemented"
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting import status: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting import status: {str(e)}")

@router.get("/collections/health", response_model=CollectionHealthResponse)
async def get_collections_health(
    user_id: str = Depends(get_current_user)
):
    """
    Get health information about all collections
    
    Args:
        user_id: ID of the user (from authentication)
        
    Returns:
        CollectionHealthResponse: Health information about all collections
    """
    if not QDRANT_AVAILABLE:
        raise HTTPException(status_code=503, detail="Qdrant integration not available")
        
    try:
        # Initialize collection manager
        collection_manager = EnhancedCollectionManager()
        
        # Get health information
        health_info = collection_manager.get_all_collections_health()
        
        return {
            "status": "success",
            "collections": health_info,
            "user_id": user_id
        }
    except Exception as e:
        logger.error(f"Error getting collections health: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting collections health: {str(e)}")

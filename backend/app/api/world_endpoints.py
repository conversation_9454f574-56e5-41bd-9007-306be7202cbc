# In backend/app/api/world_endpoints.py (entire file)
from app.db.base import get_db_connection
from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel, Field
from typing import List, Dict, Optional, Any
import json
import httpx
import logging
import uuid
from app.auth import get_current_user, oauth2_scheme
from app.db.world_repository import WorldRepository
from app.config import XAI_API_KEY, API_BASE_URL

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/books/{book_id}/world", tags=["world"])

class CategoryData(BaseModel):
    description: str = ""
    items: List[dict] = []

class World(BaseModel):
    name: str = ""
    description: str = ""
    era: str = ""
    image_url: str = ""
    setting_types: List[str] = ["fantasy"]
    custom_setting_types: List[str] = []
    lists: Dict[str, CategoryData] = {}

@router.post("")
def update_world(
    book_id: str,
    world: World,
    user_id: str = Depends(get_current_user)
):
    """Updates the world settings for a book using the WorldRepository."""
    try:
        # Check if the book exists first
        conn = get_db_connection()
        try:
            with conn.cursor() as cursor:
                cursor.execute("SELECT book_id FROM books WHERE book_id = %s", (book_id,))
                book_exists = cursor.fetchone()

                if not book_exists:
                    # Create a new book entry if it doesn't exist
                    cursor.execute(
                        "INSERT INTO books (book_id, user_id, title) VALUES (%s, %s, %s)",
                        (book_id, user_id, "Test Book")
                    )
                    conn.commit()
        finally:
            conn.close()

        # Use WorldRepository to save world data
        world_repo = WorldRepository()

        # Convert the world model to a dictionary for AI memory data
        world_data = world.model_dump()

        # Save setting types to AI memory data
        ai_memory_data = {
            "setting_types": world_data.get("setting_types", ["fantasy"]),
            "lists": {
                "locations": {"items": []}
            }
        }

        # Save the AI memory data
        world_repo.save_ai_memory_data(book_id, ai_memory_data)

        # For each category in the world lists, create or update world elements
        for category, category_data in world_data.get("lists", {}).items():
            for item in category_data.get("items", []):
                if isinstance(item, dict) and "name" in item:
                    world_repo.create_update_world_element(
                        book_id=book_id,
                        element_id=item.get("id"),  # May be None for new elements
                        category=category,
                        name=item.get("name", ""),
                        description=item.get("description", ""),
                        attributes=item.get("attributes", {})
                    )

        # Return the updated world data
        return {
            "book_id": book_id,
            "world": world_data
        }
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error updating world: {str(e)}")

@router.get("")
def get_world(book_id: str, user_id: str = Depends(get_current_user)):
    """Fetches the world settings for a given book using the WorldRepository."""
    try:
        # Check if the book exists
        conn = get_db_connection()
        book_exists = False
        try:
            with conn.cursor() as cursor:
                cursor.execute("SELECT book_id FROM books WHERE book_id = %s", (book_id,))
                book_exists = cursor.fetchone() is not None

                if not book_exists:
                    # Create a new book entry if it doesn't exist
                    cursor.execute(
                        "INSERT INTO books (book_id, user_id, title) VALUES (%s, %s, %s)",
                        (book_id, user_id, "Test Book")
                    )
                    conn.commit()
        finally:
            conn.close()

        # Use WorldRepository to get world data
        world_repo = WorldRepository()

        # Get AI memory data (setting types, etc.)
        ai_memory_data = world_repo.get_ai_memory_data(book_id)

        # Get world elements organized by category
        elements_by_category = world_repo.get_world_elements(book_id)

        # Construct the world response
        world_data = {
            "name": "",  # Default values
            "description": "",
            "era": "",
            "image_url": "",
            "setting_types": ai_memory_data.get("setting_types", ["fantasy"]),
            "custom_setting_types": [],
            "lists": {}
        }

        # Convert elements to the expected format
        for category, items in elements_by_category.items():
            if category not in world_data["lists"]:
                world_data["lists"][category] = {
                    "description": "",
                    "items": []
                }

            # Add each element to the appropriate category
            for item in items:
                world_data["lists"][category]["items"].append({
                    "id": item["id"],
                    "name": item["name"],
                    "description": item["description"],
                    "attributes": item.get("attributes", {})
                })

        # Add locations from AI memory data if not already present
        if "locations" not in world_data["lists"] and "locations" in ai_memory_data.get("lists", {}):
            world_data["lists"]["locations"] = {
                "description": "",
                "items": ai_memory_data["lists"]["locations"].get("items", [])
            }

        return {"book_id": book_id, "world": world_data}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to fetch world: {str(e)}")

class WorldElement(BaseModel):
    category: str
    name: str
    description: str = ""
    element_id: Optional[str] = None
    attributes: Optional[Dict] = {}

@router.get("/elements")
def get_world_elements(book_id: str, category: str = None, user_id: str = Depends(get_current_user)):
    """Fetches structured world elements for a given book, optionally filtered by category."""
    try:
        world_repo = WorldRepository()
        elements_by_category = world_repo.get_world_elements(book_id, category)

        # Convert to a flat list for the frontend
        elements_list = []
        for cat, items in elements_by_category.items():
            for item in items:
                elements_list.append({
                    "element_id": item["id"],
                    "category": cat,
                    "name": item["name"],
                    "description": item["description"],
                    "attributes": item.get("attributes", {})
                })

        print(f"Returning {len(elements_list)} world elements for book {book_id}")
        return elements_list
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to fetch world elements: {str(e)}")

@router.post("/elements")
def create_update_world_element(
    book_id: str,
    element: WorldElement,
    user_id: str = Depends(get_current_user)
):
    """Creates or updates a world element."""
    try:
        world_repo = WorldRepository()
        result = world_repo.create_update_world_element(
            book_id=book_id,
            element_id=element.element_id,
            category=element.category,
            name=element.name,
            description=element.description,
            attributes=element.attributes
        )
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to update world element: {str(e)}")

@router.delete("/elements/{element_id}")
def delete_world_element(
    book_id: str,
    element_id: str,
    user_id: str = Depends(get_current_user)
):
    """Deletes a world element."""
    try:
        world_repo = WorldRepository()
        world_repo.delete_world_element(book_id, element_id)
        return {"status": "success", "message": f"Element {element_id} deleted"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to delete world element: {str(e)}")

class GenerateElementRequest(BaseModel):
    prompt: str
    category: str
    count: int = 1  # Default to 1 if not specified

@router.post("/generate")
async def generate_world_element(
    book_id: str,
    request: GenerateElementRequest,
    token: str = Depends(oauth2_scheme)
):
    """Generates one or more world elements using AI based on the prompt and category."""
    try:
        # Validate count parameter
        count = max(1, min(request.count, 5))  # Limit between 1 and 5 elements
        if count != request.count:
            logger.warning(f"Adjusted requested count from {request.count} to {count}")

        # Get book information for context
        conn = get_db_connection()
        book_info = {}
        try:
            with conn.cursor() as cursor:
                cursor.execute("SELECT * FROM books WHERE book_id = %s", (book_id,))
                book = cursor.fetchone()
                if not book:
                    raise HTTPException(status_code=404, detail="Book not found")

                book_info = {
                    "title": book.get('title', ''),
                    "genre": book.get('genre', ''),
                    "description": book.get('description', '')
                }
        finally:
            conn.close()

        # Construct a prompt for the AI based on the category
        category_label = {
            "places": "location",
            "character_types": "character type",
            "magic_systems": "magic system",
            "technology": "technology",
            "groups_societies": "group or society",
            "world_settings": "world setting"
        }.get(request.category, request.category)

        # Build context from book info
        context = f"Book title: {book_info.get('title', 'Unknown')}"
        if book_info.get('genre'):
            context += f"\nGenre: {book_info.get('genre')}"
        if book_info.get('description'):
            context += f"\nDescription: {book_info.get('description')}"

        # Create the system prompt
        system_prompt = f"""Context: {context}
        You are a creative writing assistant that helps create detailed world elements for stories.
        Generate {count} different {category_label}s based on the user's prompt.
        Respond with a JSON array containing {count} objects with the following structure:
        [
          {{
              "name": "Name of the {category_label}",
              "description": "Detailed description",
              "attributes": {{
                  // Include relevant attributes based on the category
              }}
          }},
          // Additional elements...
        ]
        """

        # Determine attributes based on category
        if request.category == "places":
            system_prompt += """For places, include these attributes:
            "location": "Where it's located",
            "climate": "Climate description",
            "population": "Population information",
            "importance": "Why this place is important"
            """
        elif request.category == "character_types":
            system_prompt += """For character types, include these attributes:
            "traits": "Common traits of this character type",
            "role": "Role in society",
            "abilities": "Special abilities if any"
            """
        elif request.category == "magic_systems":
            system_prompt += """For magic systems, include these attributes:
            "rules": "Rules of the magic system",
            "limitations": "Limitations of the magic",
            "source": "Source of the magical power"
            """
        elif request.category == "technology":
            system_prompt += """For technology, include these attributes:
            "function": "What the technology does",
            "availability": "How available or rare it is",
            "impact": "Impact on society"
            """
        elif request.category == "groups_societies":
            system_prompt += """For groups or societies, include these attributes:
            "structure": "Social structure",
            "values": "Core values",
            "conflicts": "Internal or external conflicts"
            """

        # Call the AI service
        logger.debug(f"Calling AI service with prompt: {request.prompt} to generate {count} elements")
        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {"Authorization": f"Bearer {XAI_API_KEY}", "Content-Type": "application/json"}
            payload = {
                "model": "grok-2-1212",
                "messages": [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": request.prompt}
                ],
                "max_tokens": 1500  # Increased for multiple elements
            }

            response = await client.post(f"{API_BASE_URL}/chat/completions", headers=headers, json=payload)

            if not response.is_success:
                logger.error(f"AI API error: {response.status_code} - {response.text}")
                raise HTTPException(status_code=response.status_code,
                                  detail=f"AI API error: {response.text}")

            result = response.json()
            ai_response = result.get("choices", [{}])[0].get("message", {}).get("content", "")

            # Extract JSON from the response
            try:
                # Try to parse the entire response as JSON
                elements_data = json.loads(ai_response)

                # Handle case where AI returns a single object instead of an array
                if not isinstance(elements_data, list):
                    elements_data = [elements_data]

            except json.JSONDecodeError:
                # If that fails, try to extract JSON from the text
                import re
                json_match = re.search(r'\[[\s\S]*\]', ai_response)
                if json_match:
                    try:
                        elements_data = json.loads(json_match.group(0))
                    except json.JSONDecodeError:
                        # Try to find a single JSON object if array parsing fails
                        json_match = re.search(r'\{[\s\S]*\}', ai_response)
                        if json_match:
                            try:
                                elements_data = [json.loads(json_match.group(0))]
                            except json.JSONDecodeError:
                                raise HTTPException(status_code=500,
                                                  detail="Failed to parse AI response as JSON")
                        else:
                            raise HTTPException(status_code=500,
                                              detail="AI response did not contain valid JSON")
                else:
                    raise HTTPException(status_code=500,
                                      detail="AI response did not contain valid JSON")

            # Validate the elements data
            if not elements_data or not isinstance(elements_data, list):
                raise HTTPException(status_code=500,
                                  detail="AI response did not return a valid list of elements")

            # Save each element to the database
            world_repo = WorldRepository()
            created_elements = []

            for element_data in elements_data:
                # Validate the element data
                if not element_data.get("name"):
                    logger.warning(f"Skipping element without name: {element_data}")
                    continue

                # Log the data we're about to save
                logger.debug(f"Creating new world element with data: book_id={book_id}, category={request.category}, name={element_data.get('name', '')}, attributes={element_data.get('attributes', {})}")

                try:
                    result = world_repo.create_update_world_element(
                        book_id=book_id,
                        element_id=None,  # Pass None to create a new element
                        category=request.category,
                        name=element_data.get("name", ""),
                        description=element_data.get("description", ""),
                        attributes=element_data.get("attributes", {})
                    )

                    # Log the result
                    logger.debug(f"Successfully created world element: {result}")
                    created_elements.append(result)
                except Exception as e:
                    logger.error(f"Error creating element {element_data.get('name', '')}: {str(e)}")

            # Return all created elements
            return created_elements
    except Exception as e:
        logger.error(f"World element generation failed: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Failed to generate world element: {str(e)}")

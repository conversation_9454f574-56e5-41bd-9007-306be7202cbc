"""
AI Context Endpoints

This module provides endpoints for generating AI context from dedicated tables
without relying on the book_memory table.
"""

from fastapi import APIRouter, Depends, HTTPException
import json
import logging
from app.auth import get_current_user
from app.db.base import get_db_connection
from app.db.character_repository import CharacterRepository
from app.db.plot_repository import PlotRepository
from app.db.world_repository import WorldRepository

logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

router = APIRouter(prefix="/books/{book_id}/ai/context", tags=["ai"])
char_repo = CharacterRepository()
plot_repo = PlotRepository()
world_repo = WorldRepository()

@router.post("")
async def generate_ai_context(
    book_id: str,
    user_id: str = Depends(get_current_user)
):
    """
    Generates AI context directly from dedicated tables without using book_memory.
    
    This endpoint replaces the old memory_endpoints.py generate_memory_blob endpoint.
    """
    try:
        # Fetch data from various repositories
        characters = char_repo.get_characters(book_id)
        plot = plot_repo.get_plot(book_id)
        world = world_repo.get_world_data_for_ai(book_id)
        
        # Fetch brainstorm data
        brainstorm = get_brainstorm_data(book_id)
        
        # Fetch chapter content
        chapters = get_chapter_content(book_id)
        
        # Construct context blob
        context_blob = {
            "characters": characters,
            "plot": plot,
            "world": world,
            "brainstorm": brainstorm,
            "chapters": chapters
        }
        
        return {"book_id": book_id, "context": context_blob}
    except Exception as e:
        logger.error(f"Error generating AI context: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error generating AI context: {str(e)}")

def get_brainstorm_data(book_id: str):
    """
    Fetches brainstorm data from dedicated tables.
    """
    conn = get_db_connection()
    try:
        with conn.cursor() as cursor:
            # Get all pages for this book
            cursor.execute(
                "SELECT page_id, title FROM brainstorm_pages WHERE book_id = %s ORDER BY sequence_number",
                (book_id,)
            )
            pages_data = {}
            
            for page_row in cursor.fetchall():
                page_id = page_row['page_id']
                title = page_row['title']
                
                # Get nodes for this page
                cursor.execute(
                    """
                    SELECT node_id, content, position_x, position_y, node_type
                    FROM brainstorm_nodes
                    WHERE page_id = %s
                    AND NOT EXISTS (
                        SELECT 1 FROM deleted_nodes d WHERE d.node_id = brainstorm_nodes.node_id
                    )
                    """,
                    (page_id,)
                )
                
                nodes = []
                for node_row in cursor.fetchall():
                    try:
                        # Parse content as JSON if possible
                        content = node_row['content']
                        if isinstance(content, str):
                            node_data = json.loads(content)
                        else:
                            node_data = content
                        
                        nodes.append(node_data)
                    except json.JSONDecodeError:
                        # If content is not valid JSON, create a node object from the database fields
                        nodes.append({
                            'id': node_row['node_id'],
                            'type': node_row['node_type'],
                            'position': {
                                'x': float(node_row['position_x']),
                                'y': float(node_row['position_y'])
                            },
                            'data': {
                                'content': node_row['content']
                            }
                        })
                
                pages_data[page_id] = {
                    'title': title,
                    'nodes': nodes
                }
            
            return {"pages": pages_data}
    except Exception as e:
        logger.error(f"Error fetching brainstorm data: {str(e)}")
        return {"pages": {}}
    finally:
        conn.close()

def get_chapter_content(book_id: str):
    """
    Fetches chapter content from dedicated tables.
    """
    conn = get_db_connection()
    try:
        with conn.cursor() as cursor:
            cursor.execute(
                """
                SELECT chapter_id, title, content, sequence_number
                FROM chapters
                WHERE book_id = %s
                ORDER BY sequence_number
                """,
                (book_id,)
            )
            
            chapters = {}
            for row in cursor.fetchall():
                chapter_id = row['chapter_id']
                chapters[chapter_id] = {
                    'title': row['title'],
                    'content': row['content'],
                    'sequence': row['sequence_number'],
                    'wordCount': len(row['content'].split()) if row['content'] else 0
                }
            
            return chapters
    except Exception as e:
        logger.error(f"Error fetching chapter content: {str(e)}")
        return {}
    finally:
        conn.close()

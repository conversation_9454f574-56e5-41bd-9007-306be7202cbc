# backend/app/api/user_preferences_endpoints.py
from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel
from typing import Optional
from app.auth import get_current_user
from app.db.base import get_db_connection
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/user/preferences", tags=["user_preferences"])

class AIProviderPreferences(BaseModel):
    text_provider: Optional[str] = None
    image_provider: Optional[str] = None
    image_style: Optional[str] = None
    image_model: Optional[str] = None
    image_quality: Optional[str] = None

@router.get("")
async def get_user_preferences(user_id: str = Depends(get_current_user)):
    """Get the user's AI provider preferences"""
    try:
        conn = get_db_connection()
        with conn.cursor() as cursor:
            try:
                # Try to get all preferences
                cursor.execute(
                    "SELECT text_provider, image_provider, image_style, image_model, image_quality FROM user_preferences WHERE user_id = %s",
                    (user_id,)
                )
                result = cursor.fetchone()

                if not result:
                    # Create default preferences if they don't exist
                    try:
                        cursor.execute(
                            "INSERT INTO user_preferences (user_id, text_provider, image_provider, image_style, image_model, image_quality) VALUES (%s, %s, %s, %s, %s, %s)",
                            (user_id, "grok", "grok", "default", "dall-e-3", "standard")
                        )
                        conn.commit()
                        return {
                            "text_provider": "grok",
                            "image_provider": "grok",
                            "image_style": "default",
                            "image_model": "dall-e-3",
                            "image_quality": "standard"
                        }
                    except Exception as insert_error:
                        logger.error(f"Error inserting preferences with all columns: {str(insert_error)}")
                        # Try inserting without the new columns
                        cursor.execute(
                            "INSERT INTO user_preferences (user_id, text_provider, image_provider) VALUES (%s, %s, %s)",
                            (user_id, "grok", "grok")
                        )
                        conn.commit()
                        return {
                            "text_provider": "grok",
                            "image_provider": "grok",
                            "image_style": "default",
                            "image_model": "dall-e-3",
                            "image_quality": "standard"
                        }
            except Exception as column_error:
                logger.error(f"Error getting all preferences, trying with just basic columns: {str(column_error)}")

                # Try to get just the basic preferences
                cursor.execute(
                    "SELECT text_provider, image_provider FROM user_preferences WHERE user_id = %s",
                    (user_id,)
                )
                result = cursor.fetchone()

                if not result:
                    # Create default preferences if they don't exist
                    cursor.execute(
                        "INSERT INTO user_preferences (user_id, text_provider, image_provider) VALUES (%s, %s, %s)",
                        (user_id, "grok", "grok")
                    )
                    conn.commit()
                    return {
                        "text_provider": "grok",
                        "image_provider": "grok",
                        "image_style": "default",
                        "image_model": "dall-e-3",
                        "image_quality": "standard"
                    }

            # Check if result has the new columns
            has_style = "image_style" in result if result else False
            has_model = "image_model" in result if result else False
            has_quality = "image_quality" in result if result else False

            return {
                "text_provider": result.get("text_provider", "grok") if result else "grok",
                "image_provider": result.get("image_provider", "grok") if result else "grok",
                "image_style": result.get("image_style", "default") if has_style else "default",
                "image_model": result.get("image_model", "dall-e-3") if has_model else "dall-e-3",
                "image_quality": result.get("image_quality", "standard") if has_quality else "standard"
            }
    except Exception as e:
        logger.error(f"Error getting user preferences: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get user preferences: {str(e)}")
    finally:
        conn.close()

@router.post("")
async def update_user_preferences(
    preferences: AIProviderPreferences,
    user_id: str = Depends(get_current_user)
):
    """Update the user's AI provider preferences"""
    try:
        # Validate provider names, style, model, and quality
        valid_text_providers = ["grok", "openai", "anthropic"]
        valid_image_providers = ["grok", "openai", "openai_gpt"]
        valid_image_styles = ["default", "realistic", "anime", "fantasy", "oil_painting", "watercolor"]
        valid_image_models = ["dall-e-3", "gpt-image-1"]
        valid_image_qualities = ["standard", "hd", "high", "medium", "low"]

        if preferences.text_provider and preferences.text_provider not in valid_text_providers:
            raise HTTPException(status_code=400, detail=f"Invalid text provider. Valid options are: {valid_text_providers}")

        if preferences.image_provider and preferences.image_provider not in valid_image_providers:
            raise HTTPException(status_code=400, detail=f"Invalid image provider. Valid options are: {valid_image_providers}")

        if preferences.image_style and preferences.image_style not in valid_image_styles:
            raise HTTPException(status_code=400, detail=f"Invalid image style. Valid options are: {valid_image_styles}")

        if preferences.image_model and preferences.image_model not in valid_image_models:
            raise HTTPException(status_code=400, detail=f"Invalid image model. Valid options are: {valid_image_models}")

        if preferences.image_quality and preferences.image_quality not in valid_image_qualities:
            raise HTTPException(status_code=400, detail=f"Invalid image quality. Valid options are: {valid_image_qualities}")

        # Check if API keys are set for the selected providers
        from ..config import OPENAI_API_KEY, ANTHROPIC_API_KEY

        if preferences.text_provider == "openai" and (not OPENAI_API_KEY or OPENAI_API_KEY == "sk-your-openai-api-key"):
            raise HTTPException(status_code=400, detail="OpenAI API key is not set. Please add your API key to the backend/.env file.")

        if preferences.text_provider == "anthropic" and (not ANTHROPIC_API_KEY or ANTHROPIC_API_KEY == "sk-ant-your-anthropic-api-key"):
            raise HTTPException(status_code=400, detail="Anthropic API key is not set. Please add your API key to the backend/.env file.")

        if preferences.image_provider == "openai" and (not OPENAI_API_KEY or OPENAI_API_KEY == "sk-your-openai-api-key"):
            raise HTTPException(status_code=400, detail="OpenAI API key is not set. Please add your API key to the backend/.env file.")

        # Build the update query dynamically based on which fields are provided
        update_fields = []
        params = []

        if preferences.text_provider:
            update_fields.append("text_provider = %s")
            params.append(preferences.text_provider)

        if preferences.image_provider:
            update_fields.append("image_provider = %s")
            params.append(preferences.image_provider)

        if preferences.image_style:
            update_fields.append("image_style = %s")
            params.append(preferences.image_style)

        if preferences.image_model:
            update_fields.append("image_model = %s")
            params.append(preferences.image_model)

        if preferences.image_quality:
            update_fields.append("image_quality = %s")
            params.append(preferences.image_quality)

        if not update_fields:
            return {"message": "No changes to update"}

        # Add user_id to params
        params.append(user_id)

        conn = get_db_connection()
        with conn.cursor() as cursor:
            # Check if the user has preferences
            cursor.execute(
                "SELECT user_id FROM user_preferences WHERE user_id = %s",
                (user_id,)
            )
            user_exists = cursor.fetchone() is not None

            if user_exists:
                # Update existing preferences
                query = f"UPDATE user_preferences SET {', '.join(update_fields)} WHERE user_id = %s"
                cursor.execute(query, params)
            else:
                # Create new preferences
                fields = ["user_id"]
                placeholders = ["%s"]
                values = [user_id]

                if preferences.text_provider:
                    fields.append("text_provider")
                    placeholders.append("%s")
                    values.append(preferences.text_provider)

                if preferences.image_provider:
                    fields.append("image_provider")
                    placeholders.append("%s")
                    values.append(preferences.image_provider)

                if preferences.image_style:
                    fields.append("image_style")
                    placeholders.append("%s")
                    values.append(preferences.image_style)

                if preferences.image_model:
                    fields.append("image_model")
                    placeholders.append("%s")
                    values.append(preferences.image_model)

                if preferences.image_quality:
                    fields.append("image_quality")
                    placeholders.append("%s")
                    values.append(preferences.image_quality)

                query = f"INSERT INTO user_preferences ({', '.join(fields)}) VALUES ({', '.join(placeholders)})"
                cursor.execute(query, values)

            conn.commit()

            # Get the updated preferences
            try:
                cursor.execute(
                    "SELECT text_provider, image_provider, image_style, image_model, image_quality FROM user_preferences WHERE user_id = %s",
                    (user_id,)
                )
                result = cursor.fetchone()

                # Check if result has the new columns
                has_style = "image_style" in result if result else False
                has_model = "image_model" in result if result else False
                has_quality = "image_quality" in result if result else False

                return {
                    "message": "Preferences updated successfully",
                    "preferences": {
                        "text_provider": result.get("text_provider", "grok") if result else "grok",
                        "image_provider": result.get("image_provider", "grok") if result else "grok",
                        "image_style": result.get("image_style", "default") if has_style else "default",
                        "image_model": result.get("image_model", "dall-e-3") if has_model else "dall-e-3",
                        "image_quality": result.get("image_quality", "standard") if has_quality else "standard"
                    }
                }
            except Exception as select_error:
                logger.error(f"Error getting updated preferences with all columns: {str(select_error)}")

                # Try to get just the basic preferences
                cursor.execute(
                    "SELECT text_provider, image_provider FROM user_preferences WHERE user_id = %s",
                    (user_id,)
                )
                result = cursor.fetchone()

                return {
                    "message": "Preferences updated successfully (note: some advanced preferences may not be available)",
                    "preferences": {
                        "text_provider": result.get("text_provider", "grok") if result else "grok",
                        "image_provider": result.get("image_provider", "grok") if result else "grok",
                        "image_style": "default",
                        "image_model": "dall-e-3",
                        "image_quality": "standard"
                    }
                }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating user preferences: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to update user preferences: {str(e)}")
    finally:
        if 'conn' in locals():
            conn.close()

@router.get("/providers")
async def get_available_providers():
    """Get the list of available AI providers, image styles, models, and qualities"""
    return {
        "text_providers": [
            {"id": "grok", "name": "Grok", "description": "Grok AI by xAI"},
            {"id": "openai", "name": "OpenAI", "description": "OpenAI's GPT models"},
            {"id": "anthropic", "name": "Anthropic", "description": "Anthropic's Claude models"}
        ],
        "image_providers": [
            {"id": "grok", "name": "Grok", "description": "Grok AI by xAI"},
            {"id": "openai", "name": "DALL-E 3", "description": "OpenAI's DALL-E 3 image generation"},
            {"id": "openai_gpt", "name": "GPT Image 1", "description": "OpenAI's GPT Image 1 image generation"}
        ],
        "image_styles": [
            {"id": "default", "name": "Default", "description": "Standard high-quality image style"},
            {"id": "realistic", "name": "Realistic", "description": "Photorealistic style with detailed features"},
            {"id": "anime", "name": "Anime", "description": "Anime/manga style with vibrant colors"},
            {"id": "fantasy", "name": "Fantasy", "description": "Fantasy art style with dramatic lighting"},
            {"id": "oil_painting", "name": "Oil Painting", "description": "Classical oil painting style"},
            {"id": "watercolor", "name": "Watercolor", "description": "Soft watercolor painting style"}
        ],
        "image_models": [
            {"id": "dall-e-3", "name": "DALL-E 3", "description": "OpenAI's DALL-E 3 model for high-quality image generation"},
            {"id": "gpt-image-1", "name": "GPT Image 1", "description": "OpenAI's GPT Image 1 model for image generation"}
        ],
        "image_qualities": [
            {"id": "standard", "name": "Standard", "description": "Standard quality for DALL-E 3 (1024x1024)"},
            {"id": "hd", "name": "HD", "description": "High definition quality for DALL-E 3 (1024x1024)"},
            {"id": "high", "name": "High", "description": "High quality for GPT Image 1 (1024x1024)"},
            {"id": "medium", "name": "Medium", "description": "Medium quality for GPT Image 1 (1024x1024)"},
            {"id": "low", "name": "Low", "description": "Low quality for GPT Image 1 (1024x1024)"}
        ]
    }

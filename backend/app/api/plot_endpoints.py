# backend/app/api/plot_endpoints.py
from fastapi import APIRouter, Depends, HTTPException, Body
from typing import List, Dict, Any
from ..db.plot_repository import PlotRepository
from ..auth import get_current_user, oauth2_scheme
import logging

router = APIRouter(prefix="/books/{book_id}/plot", tags=["plot"])
plot_repo = PlotRepository()
logger = logging.getLogger(__name__)

@router.get("")
def get_plot(book_id: str, user_id: str = Depends(get_current_user)):
    """Fetches the plot data for a book."""
    try:
        plot = plot_repo.get_plot(book_id)
        return {"book_id": book_id, "plot": plot}
    except Exception as e:
        logger.error(f"Error fetching plot data: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Failed to fetch plot data: {str(e)}")

@router.post("")
async def save_plot(book_id: str, request: Dict[str, Any] = Body(...), user_id: str = Depends(get_current_user)):
    """Save plot data for a book"""
    try:
        # Extract plot data from request
        plot_data = request.get("plotData", {})
        
        # Log the incoming data for debugging
        logger.info(f"Saving plot data for book: {book_id}")
        logger.debug(f"Incoming plot data: {plot_data}")
        
        # Ensure plot data has the expected structure
        if "chapters" not in plot_data:
            logger.warning(f"Missing 'chapters' in plot data for book: {book_id}")
            plot_data["chapters"] = []
        
        # Ensure at least one chapter exists
        if not plot_data["chapters"]:
            logger.warning(f"No chapters in plot data for book: {book_id}, adding default chapter")
            plot_data["chapters"] = [{"id": "chapter_1", "title": "Chapter 1", "events": []}]
        
        if "bank" not in plot_data:
            logger.warning(f"Missing 'bank' in plot data for book: {book_id}")
            plot_data["bank"] = []
        
        # Save the plot data using the repository
        result = plot_repo.add_plot_node(book_id, plot_data)
        
        return {"success": True, "message": "Plot data saved successfully"}
    except Exception as e:
        logger.error(f"Error saving plot data: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Failed to save plot data: {str(e)}")

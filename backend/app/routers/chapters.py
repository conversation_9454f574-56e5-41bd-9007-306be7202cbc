@router.put("/{chapter_id}/content")
async def update_chapter_content(
    chapter_id: str, 
    content_data: dict,
    token: str = Depends(get_token_from_header)
):
    """Updates the content of a chapter"""
    try:
        # Verify token and get user_id
        user_id = verify_token(token)
        if not user_id:
            raise HTTPException(status_code=401, detail="Invalid token")
        
        # Get the chapter
        chapter = await db.chapters.find_one({"_id": chapter_id})
        if not chapter:
            raise HTTPException(status_code=404, detail="Chapter not found")
        
        # Verify user has access to this chapter
        book = await db.books.find_one({"_id": chapter["book_id"]})
        if not book or book["user_id"] != user_id:
            raise HTTPException(status_code=403, detail="Not authorized to update this chapter")
        
        # Update the chapter content
        result = await db.chapters.update_one(
            {"_id": chapter_id},
            {"$set": {"content": content_data["content"]}}
        )
        
        if result.modified_count == 0:
            raise HTTPException(status_code=500, detail="Failed to update chapter content")
        
        return {"status": "success", "message": "Chapter content updated successfully"}
    except Exception as e:
        logger.error(f"Error updating chapter content: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Server error: {str(e)}")
"""
Repository for managing table schemas and rows for structured world data.
"""
from .base import get_db_connection
import json
import uuid
import logging

logger = logging.getLogger(__name__)

class WorldTableRepository:
    """Repository for managing table schemas and rows for structured world data."""

    def get_table_schemas(self, book_id: str) -> list:
        """Fetches all table schemas for a book."""
        conn = get_db_connection()
        try:
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    SELECT 
                        schema_id,
                        book_id,
                        category_id,
                        name,
                        columns,
                        column_types,
                        created_at,
                        updated_at
                    FROM world_table_schemas
                    WHERE book_id = %s
                    ORDER BY name
                    """,
                    (book_id,)
                )
                schemas = cursor.fetchall()
                return list(schemas)
        except Exception as e:
            logger.error(f"Error fetching table schemas: {str(e)}")
            return []
        finally:
            conn.close()

    def get_table_schema(self, schema_id: str) -> dict:
        """Fetches a specific table schema by ID."""
        conn = get_db_connection()
        try:
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    SELECT 
                        schema_id,
                        book_id,
                        category_id,
                        name,
                        columns,
                        column_types,
                        created_at,
                        updated_at
                    FROM world_table_schemas
                    WHERE schema_id = %s
                    """,
                    (schema_id,)
                )
                schema = cursor.fetchone()
                if not schema:
                    raise ValueError(f"Table schema with ID {schema_id} not found")
                return dict(schema)
        except Exception as e:
            logger.error(f"Error fetching table schema: {str(e)}")
            raise
        finally:
            conn.close()

    def create_table_schema(self, schema_data: dict) -> dict:
        """Creates a new table schema."""
        conn = get_db_connection()
        try:
            # Generate a schema ID if not provided
            if not schema_data.get('schema_id'):
                schema_data['schema_id'] = f"schema_{uuid.uuid4()}"

            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    INSERT INTO world_table_schemas (
                        schema_id,
                        book_id,
                        category_id,
                        name,
                        columns,
                        column_types
                    )
                    VALUES (%s, %s, %s, %s, %s, %s)
                    RETURNING 
                        schema_id,
                        book_id,
                        category_id,
                        name,
                        columns,
                        column_types,
                        created_at,
                        updated_at
                    """,
                    (
                        schema_data.get('schema_id'),
                        schema_data.get('book_id'),
                        schema_data.get('category_id'),
                        schema_data.get('name'),
                        schema_data.get('columns'),
                        schema_data.get('column_types')
                    )
                )
                result = cursor.fetchone()
                conn.commit()
                return dict(result)
        except Exception as e:
            conn.rollback()
            logger.error(f"Error creating table schema: {str(e)}")
            raise
        finally:
            conn.close()

    def update_table_schema(self, schema_id: str, schema_data: dict) -> dict:
        """Updates an existing table schema."""
        conn = get_db_connection()
        try:
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    UPDATE world_table_schemas
                    SET 
                        category_id = %s,
                        name = %s,
                        columns = %s,
                        column_types = %s,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE schema_id = %s
                    RETURNING 
                        schema_id,
                        book_id,
                        category_id,
                        name,
                        columns,
                        column_types,
                        created_at,
                        updated_at
                    """,
                    (
                        schema_data.get('category_id'),
                        schema_data.get('name'),
                        schema_data.get('columns'),
                        schema_data.get('column_types'),
                        schema_id
                    )
                )
                result = cursor.fetchone()
                if not result:
                    raise ValueError(f"Table schema with ID {schema_id} not found")
                conn.commit()
                return dict(result)
        except Exception as e:
            conn.rollback()
            logger.error(f"Error updating table schema: {str(e)}")
            raise
        finally:
            conn.close()

    def delete_table_schema(self, schema_id: str) -> bool:
        """Deletes a table schema and all its rows."""
        conn = get_db_connection()
        try:
            with conn.cursor() as cursor:
                # First delete all rows associated with this schema
                cursor.execute(
                    "DELETE FROM world_table_rows WHERE schema_id = %s",
                    (schema_id,)
                )
                
                # Then delete the schema itself
                cursor.execute(
                    "DELETE FROM world_table_schemas WHERE schema_id = %s",
                    (schema_id,)
                )
                conn.commit()
                return cursor.rowcount > 0
        except Exception as e:
            conn.rollback()
            logger.error(f"Error deleting table schema: {str(e)}")
            raise
        finally:
            conn.close()

    def get_table_rows(self, element_id: str) -> list:
        """Fetches all table rows for a specific element."""
        conn = get_db_connection()
        try:
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    SELECT 
                        r.row_id,
                        r.element_id,
                        r.schema_id,
                        r.row_values,
                        r.display_order,
                        r.created_at,
                        r.updated_at,
                        s.columns,
                        s.column_types,
                        s.name as schema_name
                    FROM world_table_rows r
                    JOIN world_table_schemas s ON r.schema_id = s.schema_id
                    WHERE r.element_id = %s
                    ORDER BY r.display_order
                    """,
                    (element_id,)
                )
                rows = cursor.fetchall()
                return list(rows)
        except Exception as e:
            logger.error(f"Error fetching table rows: {str(e)}")
            return []
        finally:
            conn.close()

    def create_table_row(self, row_data: dict) -> dict:
        """Creates a new table row."""
        conn = get_db_connection()
        try:
            # Generate a row ID if not provided
            if not row_data.get('row_id'):
                row_data['row_id'] = f"row_{uuid.uuid4()}"

            with conn.cursor() as cursor:
                # Get the current maximum display order for this element
                cursor.execute(
                    """
                    SELECT MAX(display_order) as max_order
                    FROM world_table_rows
                    WHERE element_id = %s
                    """,
                    (row_data.get('element_id'),)
                )
                result = cursor.fetchone()
                max_order = result['max_order'] if result and result['max_order'] is not None else 0
                
                # Set the display order to max + 1 if not provided
                display_order = row_data.get('display_order')
                if display_order is None:
                    display_order = max_order + 1

                cursor.execute(
                    """
                    INSERT INTO world_table_rows (
                        row_id,
                        element_id,
                        schema_id,
                        row_values,
                        display_order
                    )
                    VALUES (%s, %s, %s, %s, %s)
                    RETURNING 
                        row_id,
                        element_id,
                        schema_id,
                        row_values,
                        display_order,
                        created_at,
                        updated_at
                    """,
                    (
                        row_data.get('row_id'),
                        row_data.get('element_id'),
                        row_data.get('schema_id'),
                        row_data.get('row_values'),
                        display_order
                    )
                )
                result = cursor.fetchone()
                conn.commit()
                return dict(result)
        except Exception as e:
            conn.rollback()
            logger.error(f"Error creating table row: {str(e)}")
            raise
        finally:
            conn.close()

    def update_table_row(self, row_id: str, row_data: dict) -> dict:
        """Updates an existing table row."""
        conn = get_db_connection()
        try:
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    UPDATE world_table_rows
                    SET 
                        row_values = %s,
                        display_order = %s,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE row_id = %s
                    RETURNING 
                        row_id,
                        element_id,
                        schema_id,
                        row_values,
                        display_order,
                        created_at,
                        updated_at
                    """,
                    (
                        row_data.get('row_values'),
                        row_data.get('display_order'),
                        row_id
                    )
                )
                result = cursor.fetchone()
                if not result:
                    raise ValueError(f"Table row with ID {row_id} not found")
                conn.commit()
                return dict(result)
        except Exception as e:
            conn.rollback()
            logger.error(f"Error updating table row: {str(e)}")
            raise
        finally:
            conn.close()

    def delete_table_row(self, row_id: str) -> bool:
        """Deletes a table row."""
        conn = get_db_connection()
        try:
            with conn.cursor() as cursor:
                cursor.execute(
                    "DELETE FROM world_table_rows WHERE row_id = %s",
                    (row_id,)
                )
                conn.commit()
                return cursor.rowcount > 0
        except Exception as e:
            conn.rollback()
            logger.error(f"Error deleting table row: {str(e)}")
            raise
        finally:
            conn.close()

    def reorder_table_rows(self, element_id: str, row_order: list) -> bool:
        """Updates the display order of table rows for an element."""
        conn = get_db_connection()
        try:
            with conn.cursor() as cursor:
                for i, row_id in enumerate(row_order):
                    cursor.execute(
                        """
                        UPDATE world_table_rows
                        SET display_order = %s
                        WHERE row_id = %s AND element_id = %s
                        """,
                        (i + 1, row_id, element_id)
                    )
                conn.commit()
                return True
        except Exception as e:
            conn.rollback()
            logger.error(f"Error reordering table rows: {str(e)}")
            raise
        finally:
            conn.close()

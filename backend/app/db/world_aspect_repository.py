"""
Repository for managing world aspect categories and customizations.
"""
from .base import get_db_connection
import json
import uuid
import logging

logger = logging.getLogger(__name__)

class WorldAspectRepository:
    """Repository for managing world aspect categories and customizations."""

    def get_aspect_categories(self) -> list:
        """Fetches all world aspect categories."""
        conn = get_db_connection()
        try:
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    SELECT
                        category_id,
                        name,
                        description,
                        is_universal,
                        applicable_genres,
                        aspect_type,
                        display_order,
                        default_view_type,
                        created_at,
                        updated_at
                    FROM world_aspect_categories
                    ORDER BY
                        CASE
                            WHEN aspect_type = 'physical' THEN 1
                            WHEN aspect_type = 'social' THEN 2
                            WHEN aspect_type = 'metaphysical' THEN 3
                            WHEN aspect_type = 'technological' THEN 4
                            ELSE 5
                        END,
                        display_order
                    """
                )
                categories = cursor.fetchall()
                return list(categories)
        except Exception as e:
            logger.error(f"Error fetching world aspect categories: {str(e)}")
            return []
        finally:
            conn.close()

    def get_category_by_id(self, category_id: str) -> dict:
        """Fetches a specific world aspect category by ID."""
        conn = get_db_connection()
        try:
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    SELECT
                        category_id,
                        name,
                        description,
                        is_universal,
                        applicable_genres,
                        aspect_type,
                        display_order,
                        default_view_type,
                        created_at,
                        updated_at
                    FROM world_aspect_categories
                    WHERE category_id = %s
                    """,
                    (category_id,)
                )
                category = cursor.fetchone()
                if not category:
                    raise ValueError(f"Category with ID {category_id} not found")
                return dict(category)
        except Exception as e:
            logger.error(f"Error fetching world aspect category: {str(e)}")
            raise
        finally:
            conn.close()

    def create_category(self, category_data: dict) -> dict:
        """Creates a new world aspect category."""
        conn = get_db_connection()
        try:
            # Generate a category ID if not provided
            if not category_data.get('category_id'):
                # Create a slug from the name and aspect type
                name_slug = category_data.get('name', '').lower().replace(' ', '_')
                aspect_type = category_data.get('aspect_type', 'physical')
                category_data['category_id'] = f"cat_{aspect_type}_{name_slug}"

            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    INSERT INTO world_aspect_categories (
                        category_id,
                        name,
                        description,
                        is_universal,
                        applicable_genres,
                        aspect_type,
                        display_order,
                        default_view_type
                    )
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                    RETURNING
                        category_id,
                        name,
                        description,
                        is_universal,
                        applicable_genres,
                        aspect_type,
                        display_order,
                        default_view_type,
                        created_at,
                        updated_at
                    """,
                    (
                        category_data.get('category_id'),
                        category_data.get('name'),
                        category_data.get('description', ''),
                        category_data.get('is_universal', False),
                        category_data.get('applicable_genres'),
                        category_data.get('aspect_type', 'physical'),
                        category_data.get('display_order'),
                        category_data.get('default_view_type', 'card')
                    )
                )
                result = cursor.fetchone()
                conn.commit()
                return dict(result)
        except Exception as e:
            conn.rollback()
            logger.error(f"Error creating world aspect category: {str(e)}")
            raise
        finally:
            conn.close()

    def update_category(self, category_id: str, category_data: dict) -> dict:
        """Updates an existing world aspect category."""
        conn = get_db_connection()
        try:
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    UPDATE world_aspect_categories
                    SET
                        name = %s,
                        description = %s,
                        is_universal = %s,
                        applicable_genres = %s,
                        aspect_type = %s,
                        display_order = %s,
                        default_view_type = %s,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE category_id = %s
                    RETURNING
                        category_id,
                        name,
                        description,
                        is_universal,
                        applicable_genres,
                        aspect_type,
                        display_order,
                        default_view_type,
                        created_at,
                        updated_at
                    """,
                    (
                        category_data.get('name'),
                        category_data.get('description', ''),
                        category_data.get('is_universal', False),
                        category_data.get('applicable_genres'),
                        category_data.get('aspect_type', 'physical'),
                        category_data.get('display_order'),
                        category_data.get('default_view_type', 'card'),
                        category_id
                    )
                )
                result = cursor.fetchone()
                if not result:
                    raise ValueError(f"Category with ID {category_id} not found")
                conn.commit()
                return dict(result)
        except Exception as e:
            conn.rollback()
            logger.error(f"Error updating world aspect category: {str(e)}")
            raise
        finally:
            conn.close()

    def delete_category(self, category_id: str) -> bool:
        """Deletes a world aspect category."""
        conn = get_db_connection()
        try:
            with conn.cursor() as cursor:
                cursor.execute(
                    "DELETE FROM world_aspect_categories WHERE category_id = %s",
                    (category_id,)
                )
                conn.commit()
                return cursor.rowcount > 0
        except Exception as e:
            conn.rollback()
            logger.error(f"Error deleting world aspect category: {str(e)}")
            raise
        finally:
            conn.close()

    def get_category_customizations(self, book_id: str) -> list:
        """Fetches category customizations for a specific book."""
        conn = get_db_connection()
        try:
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    SELECT
                        wcc.book_id,
                        wcc.category_id,
                        wcc.is_enabled,
                        wcc.display_order,
                        wcc.created_at,
                        wcc.updated_at,
                        wac.name,
                        wac.description,
                        wac.is_universal,
                        wac.applicable_genres,
                        wac.aspect_type,
                        wac.default_view_type
                    FROM world_category_customizations wcc
                    JOIN world_aspect_categories wac ON wcc.category_id = wac.category_id
                    WHERE wcc.book_id = %s
                    ORDER BY
                        CASE
                            WHEN wac.aspect_type = 'physical' THEN 1
                            WHEN wac.aspect_type = 'social' THEN 2
                            WHEN wac.aspect_type = 'metaphysical' THEN 3
                            WHEN wac.aspect_type = 'technological' THEN 4
                            ELSE 5
                        END,
                        COALESCE(wcc.display_order, wac.display_order)
                    """,
                    (book_id,)
                )
                customizations = cursor.fetchall()
                return list(customizations)
        except Exception as e:
            logger.error(f"Error fetching category customizations: {str(e)}")
            return []
        finally:
            conn.close()

    def update_category_customization(self, book_id: str, category_id: str, is_enabled: bool, display_order: int = None) -> dict:
        """Updates a category customization for a specific book."""
        conn = get_db_connection()
        try:
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    UPDATE world_category_customizations
                    SET
                        is_enabled = %s,
                        display_order = %s,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE book_id = %s AND category_id = %s
                    RETURNING
                        book_id,
                        category_id,
                        is_enabled,
                        display_order,
                        created_at,
                        updated_at
                    """,
                    (is_enabled, display_order, book_id, category_id)
                )
                result = cursor.fetchone()
                if not result:
                    # If no row was updated, the customization might not exist yet
                    cursor.execute(
                        """
                        INSERT INTO world_category_customizations (
                            book_id,
                            category_id,
                            is_enabled,
                            display_order
                        )
                        VALUES (%s, %s, %s, %s)
                        RETURNING
                            book_id,
                            category_id,
                            is_enabled,
                            display_order,
                            created_at,
                            updated_at
                        """,
                        (book_id, category_id, is_enabled, display_order)
                    )
                    result = cursor.fetchone()
                conn.commit()
                return dict(result)
        except Exception as e:
            conn.rollback()
            logger.error(f"Error updating category customization: {str(e)}")
            raise
        finally:
            conn.close()

    def reset_category_customizations(self, book_id: str) -> bool:
        """Resets category customizations to default based on book genre."""
        conn = get_db_connection()
        try:
            with conn.cursor() as cursor:
                # Get the book's genre
                cursor.execute(
                    "SELECT genre FROM books WHERE book_id = %s",
                    (book_id,)
                )
                book = cursor.fetchone()
                if not book:
                    raise ValueError(f"Book with ID {book_id} not found")

                genre = book['genre']

                # Delete existing customizations
                cursor.execute(
                    "DELETE FROM world_category_customizations WHERE book_id = %s",
                    (book_id,)
                )

                # Re-initialize customizations based on genre
                cursor.execute(
                    """
                    INSERT INTO world_category_customizations (book_id, category_id, is_enabled)
                    SELECT %s, c.category_id,
                           CASE
                               WHEN c.is_universal THEN TRUE
                               WHEN c.applicable_genres IS NULL THEN FALSE
                               WHEN %s = ANY(c.applicable_genres) THEN TRUE
                               ELSE FALSE
                           END
                    FROM world_aspect_categories c
                    """,
                    (book_id, genre)
                )
                conn.commit()
                return True
        except Exception as e:
            conn.rollback()
            logger.error(f"Error resetting category customizations: {str(e)}")
            raise
        finally:
            conn.close()

    def get_visible_categories(self, book_id: str) -> list:
        """Fetches all visible (enabled) categories for a book."""
        conn = get_db_connection()
        try:
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    SELECT
                        wac.category_id,
                        wac.name,
                        wac.description,
                        wac.is_universal,
                        wac.applicable_genres,
                        wac.aspect_type,
                        COALESCE(wcc.display_order, wac.display_order) as display_order,
                        wac.default_view_type,
                        wcc.is_enabled
                    FROM world_aspect_categories wac
                    JOIN world_category_customizations wcc ON wac.category_id = wcc.category_id
                    WHERE wcc.book_id = %s AND wcc.is_enabled = TRUE
                    ORDER BY
                        CASE
                            WHEN wac.aspect_type = 'physical' THEN 1
                            WHEN wac.aspect_type = 'social' THEN 2
                            WHEN wac.aspect_type = 'metaphysical' THEN 3
                            WHEN wac.aspect_type = 'technological' THEN 4
                            ELSE 5
                        END,
                        COALESCE(wcc.display_order, wac.display_order)
                    """,
                    (book_id,)
                )
                categories = cursor.fetchall()
                return list(categories)
        except Exception as e:
            logger.error(f"Error fetching visible categories: {str(e)}")
            return []
        finally:
            conn.close()

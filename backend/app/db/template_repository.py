"""
Repository for managing element templates and template usage statistics.
"""
from .base import get_db_connection
import json
import uuid
import logging

logger = logging.getLogger(__name__)

class TemplateRepository:
    """Repository for managing element templates and template usage statistics."""

    def get_templates(self, category_id: str = None, creator_id: str = None,
                     is_system_template: bool = None) -> list:
        """
        Fetches templates, optionally filtered by category, creator, or system status.

        Args:
            category_id: Optional category filter
            creator_id: Optional creator filter
            is_system_template: Optional system template filter

        Returns:
            A list of template dictionaries
        """
        conn = get_db_connection()
        try:
            with conn.cursor() as cursor:
                query = """
                    SELECT
                        t.template_id,
                        t.template_name,
                        t.display_name,
                        t.category_id,
                        t.parent_template_id,
                        t.description,
                        t.field_definitions,
                        t.relationship_definitions,
                        t.icon,
                        t.version,
                        t.source_template_id,
                        t.creator_id,
                        t.is_system_template,
                        t.created_at,
                        t.updated_at,
                        c.name as category_name,
                        c.aspect_type as category_aspect_type,
                        p.display_name as parent_template_name
                    FROM element_templates t
                    LEFT JOIN world_aspect_categories c ON t.category_id = c.category_id
                    LEFT JOIN element_templates p ON t.parent_template_id = p.template_id
                    WHERE 1=1
                """

                params = []

                if category_id:
                    query += " AND t.category_id = %s"
                    params.append(category_id)

                if creator_id:
                    query += " AND t.creator_id = %s"
                    params.append(creator_id)

                if is_system_template is not None:
                    query += " AND t.is_system_template = %s"
                    params.append(is_system_template)

                query += " ORDER BY c.aspect_type, c.name, t.display_name"

                cursor.execute(query, params)
                templates = cursor.fetchall()

                # Process the templates to convert JSONB fields to Python dicts
                # and datetime objects to strings
                processed_templates = []
                for template in templates:
                    template_dict = dict(template)

                    # Convert datetime objects to strings
                    if 'created_at' in template_dict and template_dict['created_at']:
                        template_dict['created_at'] = template_dict['created_at'].isoformat()
                    if 'updated_at' in template_dict and template_dict['updated_at']:
                        template_dict['updated_at'] = template_dict['updated_at'].isoformat()

                    # Ensure field_definitions and relationship_definitions are properly formatted
                    template_dict['field_definitions'] = template_dict['field_definitions']

                    # Handle relationship_definitions
                    if template_dict['relationship_definitions']:
                        # Check if the relationship_definitions has the expected structure
                        if isinstance(template_dict['relationship_definitions'], dict) and 'valid_relationships' in template_dict['relationship_definitions']:
                            # Update the relationship structure to match the expected model
                            valid_relationships = template_dict['relationship_definitions']['valid_relationships']
                            updated_relationships = []

                            for rel in valid_relationships:
                                updated_rel = {
                                    'target_template_id': rel.get('target_template', ''),
                                    'relationship_type_id': rel.get('relationship_type', ''),
                                    'description': rel.get('description', '')
                                }
                                updated_relationships.append(updated_rel)

                            template_dict['relationship_definitions']['valid_relationships'] = updated_relationships

                    processed_templates.append(template_dict)

                return processed_templates
        except Exception as e:
            logger.error(f"Error fetching templates: {str(e)}")
            return []
        finally:
            conn.close()

    def get_template(self, template_id: str, version: str = None) -> dict:
        """
        Fetches a specific template by ID and optionally version.

        Args:
            template_id: The ID of the template
            version: Optional specific version to fetch

        Returns:
            A dictionary containing the template data
        """
        conn = get_db_connection()
        try:
            with conn.cursor() as cursor:
                query = """
                    SELECT
                        t.template_id,
                        t.template_name,
                        t.display_name,
                        t.category_id,
                        t.parent_template_id,
                        t.description,
                        t.field_definitions,
                        t.relationship_definitions,
                        t.icon,
                        t.version,
                        t.source_template_id,
                        t.creator_id,
                        t.is_system_template,
                        t.created_at,
                        t.updated_at,
                        c.name as category_name,
                        c.aspect_type as category_aspect_type,
                        p.display_name as parent_template_name
                    FROM element_templates t
                    LEFT JOIN world_aspect_categories c ON t.category_id = c.category_id
                    LEFT JOIN element_templates p ON t.parent_template_id = p.template_id
                    WHERE t.template_id = %s
                """

                params = [template_id]

                if version:
                    query += " AND t.version = %s"
                    params.append(version)

                cursor.execute(query, params)
                template = cursor.fetchone()

                if not template:
                    raise ValueError(f"Template with ID {template_id} not found")

                template_dict = dict(template)

                # Convert datetime objects to strings
                if 'created_at' in template_dict and template_dict['created_at']:
                    template_dict['created_at'] = template_dict['created_at'].isoformat()
                if 'updated_at' in template_dict and template_dict['updated_at']:
                    template_dict['updated_at'] = template_dict['updated_at'].isoformat()

                # Ensure field_definitions and relationship_definitions are properly formatted
                template_dict['field_definitions'] = template_dict['field_definitions']

                # Handle relationship_definitions
                if template_dict['relationship_definitions']:
                    # Check if the relationship_definitions has the expected structure
                    if isinstance(template_dict['relationship_definitions'], dict) and 'valid_relationships' in template_dict['relationship_definitions']:
                        # Update the relationship structure to match the expected model
                        valid_relationships = template_dict['relationship_definitions']['valid_relationships']
                        updated_relationships = []

                        for rel in valid_relationships:
                            updated_rel = {
                                'target_template_id': rel.get('target_template', ''),
                                'relationship_type_id': rel.get('relationship_type', ''),
                                'description': rel.get('description', '')
                            }
                            updated_relationships.append(updated_rel)

                        template_dict['relationship_definitions']['valid_relationships'] = updated_relationships

                return template_dict
        except Exception as e:
            logger.error(f"Error fetching template: {str(e)}")
            raise
        finally:
            conn.close()

    def create_template(self, template_data: dict) -> dict:
        """
        Creates a new template.

        Args:
            template_data: Dictionary containing template data

        Returns:
            A dictionary containing the created template
        """
        conn = get_db_connection()
        try:
            # Generate a template ID if not provided
            if not template_data.get('template_id'):
                # Create a slug from the name
                name_slug = template_data.get('display_name', '').lower().replace(' ', '_')
                template_data['template_id'] = f"{name_slug}_{uuid.uuid4().hex[:8]}"

            # Set default version if not provided
            if not template_data.get('version'):
                template_data['version'] = '1.0'

            # Set default is_system_template if not provided
            if 'is_system_template' not in template_data:
                template_data['is_system_template'] = False

            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    INSERT INTO element_templates (
                        template_id,
                        template_name,
                        display_name,
                        category_id,
                        parent_template_id,
                        description,
                        field_definitions,
                        relationship_definitions,
                        icon,
                        version,
                        source_template_id,
                        creator_id,
                        is_system_template
                    )
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    RETURNING
                        template_id,
                        template_name,
                        display_name,
                        category_id,
                        parent_template_id,
                        description,
                        field_definitions,
                        relationship_definitions,
                        icon,
                        version,
                        source_template_id,
                        creator_id,
                        is_system_template,
                        created_at,
                        updated_at
                    """,
                    (
                        template_data.get('template_id'),
                        template_data.get('template_name'),
                        template_data.get('display_name'),
                        template_data.get('category_id'),
                        template_data.get('parent_template_id'),
                        template_data.get('description'),
                        json.dumps(template_data.get('field_definitions', {})),
                        json.dumps(template_data.get('relationship_definitions', {})),
                        template_data.get('icon'),
                        template_data.get('version'),
                        template_data.get('source_template_id'),
                        template_data.get('creator_id'),
                        template_data.get('is_system_template')
                    )
                )
                result = cursor.fetchone()
                conn.commit()

                template_dict = dict(result)

                # Convert datetime objects to strings
                if 'created_at' in template_dict and template_dict['created_at']:
                    template_dict['created_at'] = template_dict['created_at'].isoformat()
                if 'updated_at' in template_dict and template_dict['updated_at']:
                    template_dict['updated_at'] = template_dict['updated_at'].isoformat()

                # Ensure field_definitions and relationship_definitions are properly formatted
                template_dict['field_definitions'] = template_dict['field_definitions']
                template_dict['relationship_definitions'] = template_dict['relationship_definitions']

                return template_dict
        except Exception as e:
            conn.rollback()
            logger.error(f"Error creating template: {str(e)}")
            raise
        finally:
            conn.close()

    def update_template(self, template_id: str, template_data: dict) -> dict:
        """
        Updates an existing template.

        Args:
            template_id: The ID of the template to update
            template_data: Dictionary containing template data

        Returns:
            A dictionary containing the updated template
        """
        conn = get_db_connection()
        try:
            # Check if this is a system template
            with conn.cursor() as cursor:
                cursor.execute(
                    "SELECT is_system_template FROM element_templates WHERE template_id = %s",
                    (template_id,)
                )
                result = cursor.fetchone()

                if not result:
                    raise ValueError(f"Template with ID {template_id} not found")

                is_system_template = result['is_system_template']

                # If this is a system template and we're trying to modify it,
                # create a new version instead
                if is_system_template and template_data.get('creator_id'):
                    # Create a copy with a new ID
                    new_template_data = self.get_template(template_id)

                    # Update with the new data
                    new_template_data.update({
                        'template_id': None,  # Will generate a new ID
                        'source_template_id': template_id,
                        'creator_id': template_data.get('creator_id'),
                        'is_system_template': False,
                        'version': '1.0'  # Start at version 1.0
                    })

                    # Apply the updates from template_data
                    for key, value in template_data.items():
                        if key not in ('template_id', 'source_template_id', 'is_system_template', 'version'):
                            new_template_data[key] = value

                    # Create the new template
                    return self.create_template(new_template_data)

                # For user templates, update the existing template
                # Increment the version number
                current_version = template_data.get('version') or '1.0'
                major, minor = current_version.split('.')
                new_version = f"{major}.{int(minor) + 1}"
                template_data['version'] = new_version

                # Update the template
                update_fields = []
                params = []

                for key, value in template_data.items():
                    if key in ('template_name', 'display_name', 'category_id', 'parent_template_id',
                              'description', 'icon', 'version'):
                        update_fields.append(f"{key} = %s")
                        params.append(value)
                    elif key == 'field_definitions':
                        update_fields.append("field_definitions = %s")
                        params.append(json.dumps(value))
                    elif key == 'relationship_definitions':
                        update_fields.append("relationship_definitions = %s")
                        params.append(json.dumps(value))

                if not update_fields:
                    raise ValueError("No fields to update")

                update_fields.append("updated_at = CURRENT_TIMESTAMP")

                query = f"""
                    UPDATE element_templates
                    SET {', '.join(update_fields)}
                    WHERE template_id = %s
                    RETURNING
                        template_id,
                        template_name,
                        display_name,
                        category_id,
                        parent_template_id,
                        description,
                        field_definitions,
                        relationship_definitions,
                        icon,
                        version,
                        source_template_id,
                        creator_id,
                        is_system_template,
                        created_at,
                        updated_at
                """

                params.append(template_id)

                cursor.execute(query, params)
                result = cursor.fetchone()

                if not result:
                    raise ValueError(f"Template with ID {template_id} not found")

                conn.commit()

                template_dict = dict(result)

                # Convert datetime objects to strings
                if 'created_at' in template_dict and template_dict['created_at']:
                    template_dict['created_at'] = template_dict['created_at'].isoformat()
                if 'updated_at' in template_dict and template_dict['updated_at']:
                    template_dict['updated_at'] = template_dict['updated_at'].isoformat()

                # Ensure field_definitions and relationship_definitions are properly formatted
                template_dict['field_definitions'] = template_dict['field_definitions']
                template_dict['relationship_definitions'] = template_dict['relationship_definitions']

                return template_dict
        except Exception as e:
            conn.rollback()
            logger.error(f"Error updating template: {str(e)}")
            raise
        finally:
            conn.close()

    def delete_template(self, template_id: str) -> bool:
        """
        Deletes a template if it's not a system template.

        Args:
            template_id: The ID of the template to delete

        Returns:
            True if the template was deleted, False otherwise
        """
        conn = get_db_connection()
        try:
            # Check if this is a system template
            with conn.cursor() as cursor:
                cursor.execute(
                    "SELECT is_system_template FROM element_templates WHERE template_id = %s",
                    (template_id,)
                )
                result = cursor.fetchone()

                if not result:
                    raise ValueError(f"Template with ID {template_id} not found")

                is_system_template = result['is_system_template']

                # Don't allow deletion of system templates
                if is_system_template:
                    raise ValueError("System templates cannot be deleted")

                # Check if any elements are using this template
                cursor.execute(
                    "SELECT COUNT(*) as count FROM world_elements WHERE template_id = %s",
                    (template_id,)
                )
                result = cursor.fetchone()

                if result['count'] > 0:
                    raise ValueError(f"Cannot delete template: {result['count']} elements are using it")

                # Delete the template
                cursor.execute(
                    "DELETE FROM element_templates WHERE template_id = %s",
                    (template_id,)
                )

                conn.commit()
                return True
        except Exception as e:
            conn.rollback()
            logger.error(f"Error deleting template: {str(e)}")
            raise
        finally:
            conn.close()

    def export_template(self, template_id: str) -> dict:
        """
        Exports a template in a format suitable for sharing or backup.

        Args:
            template_id: The ID of the template to export

        Returns:
            A dictionary containing the template data in export format
        """
        try:
            # Get the template
            template = self.get_template(template_id)

            # Create export format
            export_data = {
                "template_name": template["template_name"],
                "display_name": template["display_name"],
                "category_id": template["category_id"],
                "description": template["description"],
                "icon": template["icon"],
                "version": template["version"],
                "export_date": template["updated_at"],
                "metadata": {
                    "original_id": template["template_id"],
                    "category_name": template["category_name"],
                    "is_system_template": template["is_system_template"]
                }
            }

            # Process field definitions to ensure consistent field property names
            field_definitions = {}
            for section_name, fields in template["field_definitions"].items():
                field_definitions[section_name] = []
                for field in fields:
                    # Create a new field with standardized property names
                    new_field = {k: v for k, v in field.items()}

                    # Ensure field_type is used (not type)
                    if "type" in new_field and "field_type" not in new_field:
                        new_field["field_type"] = new_field.pop("type")

                    field_definitions[section_name].append(new_field)

            export_data["field_definitions"] = field_definitions

            # Include relationship definitions if they exist
            if template["relationship_definitions"]:
                export_data["relationship_definitions"] = template["relationship_definitions"]

            # Include parent template reference if it exists
            if template.get("parent_template_id"):
                export_data["metadata"]["parent_template_id"] = template["parent_template_id"]
                export_data["metadata"]["parent_template_name"] = template.get("parent_template_name", "")

            return export_data
        except Exception as e:
            logger.error(f"Error exporting template: {str(e)}")
            raise

    def get_template_versions(self, template_id: str) -> list:
        """
        Fetches the version history of a template.

        Args:
            template_id: The ID of the template

        Returns:
            A list of template versions
        """
        conn = get_db_connection()
        try:
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    SELECT
                        version_id,
                        template_id,
                        version,
                        created_at,
                        created_by
                    FROM template_versions
                    WHERE template_id = %s
                    ORDER BY version DESC
                    """,
                    (template_id,)
                )

                versions = cursor.fetchall()
                return versions
        except Exception as e:
            logger.error(f"Error fetching template versions: {str(e)}")
            return []
        finally:
            conn.close()

    def get_template_version(self, version_id: str) -> dict:
        """
        Fetches a specific version of a template.

        Args:
            version_id: The ID of the template version

        Returns:
            A dictionary containing the template version data
        """
        conn = get_db_connection()
        try:
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    SELECT
                        v.version_id,
                        v.template_id,
                        v.version,
                        v.created_at,
                        v.created_by,
                        v.template_data
                    FROM template_versions v
                    WHERE v.version_id = %s
                    """,
                    (version_id,)
                )

                version = cursor.fetchone()

                if not version:
                    raise ValueError(f"Template version with ID {version_id} not found")

                return version
        except Exception as e:
            logger.error(f"Error fetching template version: {str(e)}")
            raise
        finally:
            conn.close()

    def compare_template_versions(self, version1_id: str, version2_id: str) -> dict:
        """
        Compares two versions of a template.

        Args:
            version1_id: The ID of the first template version
            version2_id: The ID of the second template version

        Returns:
            A dictionary containing the comparison result
        """
        try:
            # Get both versions
            version1 = self.get_template_version(version1_id)
            version2 = self.get_template_version(version2_id)

            # Extract template data
            template1 = version1["template_data"]
            template2 = version2["template_data"]

            # Compare templates
            differences = []

            # Compare basic properties
            basic_props = ["template_name", "display_name", "description", "category_id", "icon"]
            for prop in basic_props:
                if prop in template1 and prop in template2:
                    if template1[prop] != template2[prop]:
                        differences.append({
                            "field": prop,
                            "type": "modified",
                            "oldValue": template1[prop],
                            "newValue": template2[prop]
                        })
                elif prop in template1:
                    differences.append({
                        "field": prop,
                        "type": "removed",
                        "value": template1[prop]
                    })
                elif prop in template2:
                    differences.append({
                        "field": prop,
                        "type": "added",
                        "value": template2[prop]
                    })

            # Compare field definitions
            if "field_definitions" in template1 and "field_definitions" in template2:
                # Compare sections
                all_sections = set(template1["field_definitions"].keys()) | set(template2["field_definitions"].keys())

                for section in all_sections:
                    if section in template1["field_definitions"] and section in template2["field_definitions"]:
                        # Compare fields in this section
                        fields1 = {f.get("field_name", f["label"]): f for f in template1["field_definitions"][section]}
                        fields2 = {f.get("field_name", f["label"]): f for f in template2["field_definitions"][section]}

                        all_fields = set(fields1.keys()) | set(fields2.keys())

                        for field_name in all_fields:
                            if field_name in fields1 and field_name in fields2:
                                if fields1[field_name] != fields2[field_name]:
                                    differences.append({
                                        "field": f"field_definitions.{section}.{field_name}",
                                        "type": "modified",
                                        "oldValue": fields1[field_name],
                                        "newValue": fields2[field_name]
                                    })
                            elif field_name in fields1:
                                differences.append({
                                    "field": f"field_definitions.{section}.{field_name}",
                                    "type": "removed",
                                    "value": fields1[field_name]
                                })
                            elif field_name in fields2:
                                differences.append({
                                    "field": f"field_definitions.{section}.{field_name}",
                                    "type": "added",
                                    "value": fields2[field_name]
                                })
                    elif section in template1["field_definitions"]:
                        differences.append({
                            "field": f"field_definitions.{section}",
                            "type": "removed",
                            "value": template1["field_definitions"][section]
                        })
                    elif section in template2["field_definitions"]:
                        differences.append({
                            "field": f"field_definitions.{section}",
                            "type": "added",
                            "value": template2["field_definitions"][section]
                        })

            # Compare relationship definitions
            if "relationship_definitions" in template1 and "relationship_definitions" in template2:
                if template1["relationship_definitions"] != template2["relationship_definitions"]:
                    differences.append({
                        "field": "relationship_definitions",
                        "type": "modified",
                        "oldValue": template1["relationship_definitions"],
                        "newValue": template2["relationship_definitions"]
                    })
            elif "relationship_definitions" in template1:
                differences.append({
                    "field": "relationship_definitions",
                    "type": "removed",
                    "value": template1["relationship_definitions"]
                })
            elif "relationship_definitions" in template2:
                differences.append({
                    "field": "relationship_definitions",
                    "type": "added",
                    "value": template2["relationship_definitions"]
                })

            return {
                "version1": {
                    "version_id": version1["version_id"],
                    "template_id": version1["template_id"],
                    "version": version1["version"],
                    "created_at": version1["created_at"],
                    "created_by": version1["created_by"]
                },
                "version2": {
                    "version_id": version2["version_id"],
                    "template_id": version2["template_id"],
                    "version": version2["version"],
                    "created_at": version2["created_at"],
                    "created_by": version2["created_by"]
                },
                "differences": differences
            }
        except Exception as e:
            logger.error(f"Error comparing template versions: {str(e)}")
            raise

    def restore_template_version(self, version_id: str, user_id: str) -> dict:
        """
        Restores a template to a previous version.

        Args:
            version_id: The ID of the template version to restore
            user_id: The ID of the user restoring the version

        Returns:
            A dictionary containing the restored template
        """
        conn = get_db_connection()
        try:
            # Get the version to restore
            version = self.get_template_version(version_id)

            # Get the current template
            template = self.get_template(version["template_id"])

            # Create a new version with the data from the old version
            template_data = version["template_data"]

            # Update the version number
            current_version = float(template["version"])
            new_version = str(current_version + 0.1)

            # Update the template
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    UPDATE templates
                    SET
                        template_name = %s,
                        display_name = %s,
                        category_id = %s,
                        description = %s,
                        field_definitions = %s,
                        relationship_definitions = %s,
                        icon = %s,
                        version = %s,
                        updated_at = NOW(),
                        updated_by = %s
                    WHERE template_id = %s
                    RETURNING *
                    """,
                    (
                        template_data["template_name"],
                        template_data["display_name"],
                        template_data["category_id"],
                        template_data.get("description", ""),
                        json.dumps(template_data["field_definitions"]),
                        json.dumps(template_data.get("relationship_definitions", {})),
                        template_data.get("icon"),
                        new_version,
                        user_id,
                        version["template_id"]
                    )
                )

                updated_template = cursor.fetchone()

                # Create a new version record
                cursor.execute(
                    """
                    INSERT INTO template_versions (
                        template_id,
                        version,
                        template_data,
                        created_by
                    )
                    VALUES (%s, %s, %s, %s)
                    RETURNING version_id
                    """,
                    (
                        version["template_id"],
                        new_version,
                        json.dumps(template_data),
                        user_id
                    )
                )

                conn.commit()

                # Get the updated template with category name
                return self.get_template(version["template_id"])
        except Exception as e:
            conn.rollback()
            logger.error(f"Error restoring template version: {str(e)}")
            raise
        finally:
            conn.close()

    def import_template(self, template_data: dict, user_id: str) -> dict:
        """
        Imports a template from an export format.

        Args:
            template_data: The template data in export format
            user_id: The ID of the user importing the template

        Returns:
            A dictionary containing the imported template
        """
        conn = get_db_connection()
        try:
            # Validate required fields
            required_fields = ["template_name", "display_name", "category_id", "field_definitions"]
            for field in required_fields:
                if field not in template_data:
                    raise ValueError(f"Missing required field: {field}")

            # Process field definitions to ensure consistent field property names
            field_definitions = {}
            for section_name, fields in template_data["field_definitions"].items():
                field_definitions[section_name] = []
                for field in fields:
                    # Create a new field with standardized property names
                    new_field = {k: v for k, v in field.items()}

                    # Ensure field_type is used (not type)
                    if "type" in new_field and "field_type" not in new_field:
                        new_field["field_type"] = new_field.pop("type")

                    field_definitions[section_name].append(new_field)

            # Create a new template from the import data
            new_template = {
                "template_name": template_data["template_name"],
                "display_name": template_data["display_name"],
                "category_id": template_data["category_id"],
                "description": template_data.get("description", ""),
                "field_definitions": field_definitions,
                "relationship_definitions": template_data.get("relationship_definitions", {}),
                "icon": template_data.get("icon"),
                "version": "1.0",  # Always start at version 1.0 for imported templates
                "creator_id": user_id,
                "is_system_template": False  # Imported templates are never system templates
            }

            # Check if the category exists
            with conn.cursor() as cursor:
                cursor.execute(
                    "SELECT category_id FROM world_aspect_categories WHERE category_id = %s",
                    (new_template["category_id"],)
                )
                category = cursor.fetchone()

                if not category:
                    # If the category doesn't exist, use the first available category
                    cursor.execute(
                        "SELECT category_id FROM world_aspect_categories LIMIT 1"
                    )
                    fallback_category = cursor.fetchone()

                    if fallback_category:
                        new_template["category_id"] = fallback_category["category_id"]
                    else:
                        raise ValueError("No valid category found for template import")

            # Create the template
            result = self.create_template(new_template)
            return result
        except Exception as e:
            conn.rollback()
            logger.error(f"Error importing template: {str(e)}")
            raise
        finally:
            conn.close()

    def get_template_hierarchy(self) -> dict:
        """
        Fetches the template hierarchy.

        Returns:
            A dictionary mapping category IDs to lists of templates with their children
        """
        conn = get_db_connection()
        try:
            with conn.cursor() as cursor:
                # Get all templates
                cursor.execute(
                    """
                    SELECT
                        t.template_id,
                        t.template_name,
                        t.display_name,
                        t.category_id,
                        t.parent_template_id,
                        t.description,
                        t.version,
                        t.is_system_template,
                        c.name as category_name,
                        c.aspect_type as category_aspect_type
                    FROM element_templates t
                    LEFT JOIN world_aspect_categories c ON t.category_id = c.category_id
                    ORDER BY c.aspect_type, c.name, t.display_name
                    """
                )
                templates = cursor.fetchall()

                # Organize templates by category
                templates_by_category = {}
                for template in templates:
                    category_id = template['category_id']
                    if category_id not in templates_by_category:
                        templates_by_category[category_id] = {
                            'category_id': category_id,
                            'category_name': template['category_name'],
                            'category_aspect_type': template['category_aspect_type'],
                            'templates': []
                        }

                    templates_by_category[category_id]['templates'].append({
                        'template_id': template['template_id'],
                        'display_name': template['display_name'],
                        'parent_template_id': template['parent_template_id'],
                        'description': template['description'],
                        'version': template['version'],
                        'is_system_template': template['is_system_template']
                    })

                # Build the hierarchy
                for category_data in templates_by_category.values():
                    templates = category_data['templates']

                    # Create a map of template IDs to templates
                    template_map = {t['template_id']: t for t in templates}

                    # Add children to each template
                    for template in templates:
                        template['children'] = []

                    # Populate children
                    for template in templates:
                        parent_id = template['parent_template_id']
                        if parent_id and parent_id in template_map:
                            template_map[parent_id]['children'].append(template)

                    # Filter out templates that have parents (they'll be included as children)
                    category_data['templates'] = [t for t in templates if not t['parent_template_id']]

                return templates_by_category
        except Exception as e:
            logger.error(f"Error fetching template hierarchy: {str(e)}")
            return {}
        finally:
            conn.close()

    def get_template_usage_stats(self, template_id: str = None, book_id: str = None,
                                user_id: str = None) -> list:
        """
        Fetches template usage statistics.

        Args:
            template_id: Optional template ID filter
            book_id: Optional book ID filter
            user_id: Optional user ID filter

        Returns:
            A list of usage statistics
        """
        conn = get_db_connection()
        try:
            with conn.cursor() as cursor:
                query = """
                    SELECT
                        s.id,
                        s.template_id,
                        s.template_version,
                        s.book_id,
                        s.user_id,
                        s.usage_count,
                        s.last_used_at,
                        s.created_at,
                        s.updated_at,
                        t.display_name as template_name,
                        b.title as book_title
                    FROM template_usage_stats s
                    JOIN element_templates t ON s.template_id = t.template_id
                    JOIN books b ON s.book_id = b.book_id
                    WHERE 1=1
                """

                params = []

                if template_id:
                    query += " AND s.template_id = %s"
                    params.append(template_id)

                if book_id:
                    query += " AND s.book_id = %s"
                    params.append(book_id)

                if user_id:
                    query += " AND s.user_id = %s"
                    params.append(user_id)

                query += " ORDER BY s.usage_count DESC, s.last_used_at DESC"

                cursor.execute(query, params)
                stats = cursor.fetchall()

                # Convert datetime objects to strings
                processed_stats = []
                for stat in stats:
                    stat_dict = dict(stat)

                    # Convert datetime objects to strings
                    if 'last_used_at' in stat_dict and stat_dict['last_used_at']:
                        stat_dict['last_used_at'] = stat_dict['last_used_at'].isoformat()
                    if 'created_at' in stat_dict and stat_dict['created_at']:
                        stat_dict['created_at'] = stat_dict['created_at'].isoformat()
                    if 'updated_at' in stat_dict and stat_dict['updated_at']:
                        stat_dict['updated_at'] = stat_dict['updated_at'].isoformat()

                    processed_stats.append(stat_dict)

                return processed_stats
        except Exception as e:
            logger.error(f"Error fetching template usage stats: {str(e)}")
            return []
        finally:
            conn.close()

    def get_popular_templates(self, limit: int = 10) -> list:
        """
        Fetches the most popular templates based on usage count.

        Args:
            limit: Maximum number of templates to return

        Returns:
            A list of templates with usage statistics
        """
        conn = get_db_connection()
        try:
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    SELECT
                        t.template_id,
                        t.display_name,
                        t.category_id,
                        t.description,
                        t.version,
                        t.is_system_template,
                        c.name as category_name,
                        c.aspect_type as category_aspect_type,
                        SUM(s.usage_count) as total_usage_count,
                        COUNT(DISTINCT s.user_id) as user_count
                    FROM element_templates t
                    JOIN template_usage_stats s ON t.template_id = s.template_id
                    LEFT JOIN world_aspect_categories c ON t.category_id = c.category_id
                    GROUP BY
                        t.template_id,
                        t.display_name,
                        t.category_id,
                        t.description,
                        t.version,
                        t.is_system_template,
                        c.name,
                        c.aspect_type
                    ORDER BY total_usage_count DESC, user_count DESC
                    LIMIT %s
                    """,
                    (limit,)
                )
                templates = cursor.fetchall()

                return [dict(template) for template in templates]
        except Exception as e:
            logger.error(f"Error fetching popular templates: {str(e)}")
            return []
        finally:
            conn.close()

    def get_template_usage_trends(self, template_id: str = None, period: str = 'monthly',
                                  limit: int = 12) -> list:
        """
        Fetches template usage trends over time.

        Args:
            template_id: Optional template ID to filter by
            period: Time period for grouping ('daily', 'weekly', 'monthly')
            limit: Maximum number of time periods to return

        Returns:
            A list of usage counts grouped by time period
        """
        conn = get_db_connection()
        try:
            with conn.cursor() as cursor:
                # Define the date trunc function based on the period
                if period == 'daily':
                    date_trunc = 'day'
                elif period == 'weekly':
                    date_trunc = 'week'
                else:  # default to monthly
                    date_trunc = 'month'

                query = """
                    SELECT
                        date_trunc(%s, last_used_at) as time_period,
                        SUM(usage_count) as total_usage,
                        COUNT(DISTINCT user_id) as unique_users,
                        COUNT(DISTINCT book_id) as unique_books
                    FROM template_usage_stats
                    WHERE 1=1
                """

                params = [date_trunc]

                if template_id:
                    query += " AND template_id = %s"
                    params.append(template_id)

                query += """
                    GROUP BY time_period
                    ORDER BY time_period DESC
                    LIMIT %s
                """

                params.append(limit)

                cursor.execute(query, params)
                trends = cursor.fetchall()

                # Convert datetime objects to strings and process results
                processed_trends = []
                for trend in trends:
                    trend_dict = dict(trend)

                    # Convert datetime to string
                    if 'time_period' in trend_dict and trend_dict['time_period']:
                        trend_dict['time_period'] = trend_dict['time_period'].isoformat()

                    processed_trends.append(trend_dict)

                # Reverse to get chronological order
                processed_trends.reverse()

                return processed_trends
        except Exception as e:
            logger.error(f"Error fetching template usage trends: {str(e)}")
            return []
        finally:
            conn.close()

    def increment_template_usage(self, template_id: str, template_version: str,
                                book_id: str, user_id: str) -> dict:
        """
        Increments the usage count for a template.

        Args:
            template_id: The ID of the template
            template_version: The version of the template
            book_id: The ID of the book
            user_id: The ID of the user

        Returns:
            A dictionary containing the updated usage statistics
        """
        conn = get_db_connection()
        try:
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    INSERT INTO template_usage_stats (
                        template_id,
                        template_version,
                        book_id,
                        user_id,
                        usage_count,
                        last_used_at
                    )
                    VALUES (%s, %s, %s, %s, 1, CURRENT_TIMESTAMP)
                    ON CONFLICT (template_id, template_version, book_id, user_id)
                    DO UPDATE SET
                        usage_count = template_usage_stats.usage_count + 1,
                        last_used_at = CURRENT_TIMESTAMP,
                        updated_at = CURRENT_TIMESTAMP
                    RETURNING
                        id,
                        template_id,
                        template_version,
                        book_id,
                        user_id,
                        usage_count,
                        last_used_at,
                        created_at,
                        updated_at
                    """,
                    (template_id, template_version, book_id, user_id)
                )
                result = cursor.fetchone()
                conn.commit()

                result_dict = dict(result)

                # Convert datetime objects to strings
                if 'last_used_at' in result_dict and result_dict['last_used_at']:
                    result_dict['last_used_at'] = result_dict['last_used_at'].isoformat()
                if 'created_at' in result_dict and result_dict['created_at']:
                    result_dict['created_at'] = result_dict['created_at'].isoformat()
                if 'updated_at' in result_dict and result_dict['updated_at']:
                    result_dict['updated_at'] = result_dict['updated_at'].isoformat()

                return result_dict
        except Exception as e:
            conn.rollback()
            logger.error(f"Error incrementing template usage: {str(e)}")
            raise
        finally:
            conn.close()
"""
Repository for managing relationships between world elements.
"""
from .base import get_db_connection
import json
import uuid
import logging

logger = logging.getLogger(__name__)

class WorldRelationshipRepository:
    """Repository for managing relationships between world elements."""

    def get_relationships(self, book_id: str) -> list:
        """Fetches all relationships for elements in a book."""
        conn = get_db_connection()
        try:
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    SELECT
                        r.relationship_id,
                        r.source_element_id,
                        r.target_element_id,
                        r.relationship_type,
                        r.description,
                        r.created_at,
                        r.updated_at,
                        s.name as source_name,
                        s.category as source_category,
                        t.name as target_name,
                        t.category as target_category
                    FROM world_element_relationships r
                    JOIN world_elements s ON r.source_element_id = s.element_id
                    JOIN world_elements t ON r.target_element_id = t.element_id
                    WHERE s.book_id = %s AND t.book_id = %s
                    """,
                    (book_id, book_id)
                )
                relationships = cursor.fetchall()
                return list(relationships)
        except Exception as e:
            logger.error(f"Error fetching world element relationships: {str(e)}")
            return []
        finally:
            conn.close()

    def get_element_relationships(self, element_id: str) -> list:
        """Fetches all relationships for a specific element (both as source and target)."""
        conn = get_db_connection()
        try:
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    SELECT
                        r.relationship_id,
                        r.source_element_id,
                        r.target_element_id,
                        r.relationship_type,
                        r.description,
                        r.created_at,
                        r.updated_at,
                        s.name as source_name,
                        s.category as source_category,
                        t.name as target_name,
                        t.category as target_category,
                        CASE WHEN r.source_element_id = %s THEN 'outgoing' ELSE 'incoming' END as direction
                    FROM world_element_relationships r
                    JOIN world_elements s ON r.source_element_id = s.element_id
                    JOIN world_elements t ON r.target_element_id = t.element_id
                    WHERE r.source_element_id = %s OR r.target_element_id = %s
                    """,
                    (element_id, element_id, element_id)
                )
                relationships = cursor.fetchall()
                return list(relationships)
        except Exception as e:
            logger.error(f"Error fetching element relationships: {str(e)}")
            return []
        finally:
            conn.close()

    def create_relationship(self, relationship_data: dict) -> dict:
        """Creates a new relationship between world elements."""
        conn = get_db_connection()
        try:
            # Generate a relationship ID if not provided
            if not relationship_data.get('relationship_id'):
                relationship_data['relationship_id'] = f"rel_{uuid.uuid4()}"

            with conn.cursor() as cursor:
                # First verify that both elements exist and belong to the same book
                cursor.execute(
                    """
                    SELECT e1.book_id AS source_book_id, e2.book_id AS target_book_id
                    FROM world_elements e1, world_elements e2
                    WHERE e1.element_id = %s AND e2.element_id = %s
                    """,
                    (relationship_data.get('source_element_id'), relationship_data.get('target_element_id'))
                )
                result = cursor.fetchone()
                if not result:
                    raise ValueError("One or both elements do not exist")

                if result['source_book_id'] != result['target_book_id']:
                    raise ValueError("Elements must belong to the same book")

                cursor.execute(
                    """
                    INSERT INTO world_element_relationships (
                        relationship_id,
                        source_element_id,
                        target_element_id,
                        relationship_type,
                        description
                    )
                    VALUES (%s, %s, %s, %s, %s)
                    RETURNING
                        relationship_id,
                        source_element_id,
                        target_element_id,
                        relationship_type,
                        description,
                        created_at,
                        updated_at
                    """,
                    (
                        relationship_data.get('relationship_id'),
                        relationship_data.get('source_element_id'),
                        relationship_data.get('target_element_id'),
                        relationship_data.get('relationship_type'),
                        relationship_data.get('description', '')
                    )
                )
                result = cursor.fetchone()
                conn.commit()
                return dict(result)
        except Exception as e:
            conn.rollback()
            logger.error(f"Error creating relationship: {str(e)}")
            raise
        finally:
            conn.close()

    def update_relationship(self, relationship_id: str, relationship_data: dict) -> dict:
        """Updates an existing relationship."""
        conn = get_db_connection()
        try:
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    UPDATE world_element_relationships
                    SET
                        relationship_type = %s,
                        description = %s,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE relationship_id = %s
                    RETURNING
                        relationship_id,
                        source_element_id,
                        target_element_id,
                        relationship_type,
                        description,
                        created_at,
                        updated_at
                    """,
                    (
                        relationship_data.get('relationship_type'),
                        relationship_data.get('description', ''),
                        relationship_id
                    )
                )
                result = cursor.fetchone()
                if not result:
                    raise ValueError(f"Relationship with ID {relationship_id} not found")
                conn.commit()
                return dict(result)
        except Exception as e:
            conn.rollback()
            logger.error(f"Error updating relationship: {str(e)}")
            raise
        finally:
            conn.close()

    def delete_relationship(self, relationship_id: str) -> bool:
        """Deletes a relationship."""
        conn = get_db_connection()
        try:
            with conn.cursor() as cursor:
                cursor.execute(
                    "DELETE FROM world_element_relationships WHERE relationship_id = %s",
                    (relationship_id,)
                )
                conn.commit()
                return cursor.rowcount > 0
        except Exception as e:
            conn.rollback()
            logger.error(f"Error deleting relationship: {str(e)}")
            raise
        finally:
            conn.close()

    def get_relationship_types(self) -> list:
        """Returns a list of common relationship types."""
        # This could be expanded to be stored in the database
        return [
            {"id": "contains", "name": "Contains", "description": "The source element contains the target element"},
            {"id": "located_in", "name": "Located In", "description": "The source element is located in the target element"},
            {"id": "part_of", "name": "Part Of", "description": "The source element is part of the target element"},
            {"id": "connected_to", "name": "Connected To", "description": "The source element is connected to the target element"},
            {"id": "opposes", "name": "Opposes", "description": "The source element opposes or conflicts with the target element"},
            {"id": "allies_with", "name": "Allies With", "description": "The source element is allied with the target element"},
            {"id": "created", "name": "Created", "description": "The source element created the target element"},
            {"id": "destroyed", "name": "Destroyed", "description": "The source element destroyed the target element"},
            {"id": "influences", "name": "Influences", "description": "The source element influences the target element"},
            {"id": "rules", "name": "Rules", "description": "The source element rules or governs the target element"},
            {"id": "depends_on", "name": "Depends On", "description": "The source element depends on the target element"},
            {"id": "transforms_into", "name": "Transforms Into", "description": "The source element transforms into the target element"}
        ]

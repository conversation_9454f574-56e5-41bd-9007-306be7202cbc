# backend/app/db/book_repository.py
from .base import get_db_connection
from fastapi import HTTPException
import json

class BookRepository:
    """Repository for managing book-related data in the database."""

    def create(self, book_id: str, user_id: str, title: str, author: str = '', genre: str = '', description: str = '') -> None:
        """Creates a new book and its associated memory entry with initial chapters."""
        conn = get_db_connection()
        try:
            with conn.cursor() as cursor:
                # Check if book_id already exists
                cursor.execute("SELECT book_id FROM books WHERE book_id = %s", (book_id,))
                if cursor.fetchone():
                    raise ValueError(f"Book with ID {book_id} already exists")

                # Check if the books table has the necessary columns
                cursor.execute("SELECT column_name FROM information_schema.columns WHERE table_name = 'books'")
                columns = [row['column_name'] for row in cursor.fetchall()]

                # If the columns don't exist, add them
                if 'author' not in columns:
                    cursor.execute("ALTER TABLE books ADD COLUMN author TEXT DEFAULT ''")
                if 'genre' not in columns:
                    cursor.execute("ALTER TABLE books ADD COLUMN genre TEXT DEFAULT ''")
                if 'description' not in columns:
                    cursor.execute("ALTER TABLE books ADD COLUMN description TEXT DEFAULT ''")

                # Insert into books table with all fields
                cursor.execute(
                    """INSERT INTO books (book_id, user_id, title, author, genre, description, created_at, updated_at)
                    VALUES (%s, %s, %s, %s, %s, %s, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)""",
                    (book_id, user_id, title, author, genre, description)
                )

                # Create default brainstorm page with a unique page_id
                page_id = f"page_{book_id.replace('-', '_')}_1"
                cursor.execute(
                    "INSERT INTO brainstorm_pages (book_id, page_id, title, sequence_number) VALUES (%s, %s, %s, %s)",
                    (book_id, page_id, 'Page 1', 1)
                )

                # Create first chapter with a unique chapter_id
                chapter_id = f"ch_{book_id.replace('-', '_')}_1"
                cursor.execute(
                    "INSERT INTO chapters (book_id, chapter_id, title, sequence_number, content) VALUES (%s, %s, %s, %s, %s)",
                    (book_id, chapter_id, 'Chapter 1', 1, '')
                )

                # Initialize memory with the same structure as in create_book endpoint
                initial_memory = json.dumps({
                    "world": {
                        "name": title,
                        "description": "",
                        "image_url": "",
                        "setting_types": [],
                        "lists": {
                            "Items": {"items": [], "description": ""},
                            "Places": {"items": [], "description": ""},
                            "Species": {"items": [], "description": ""},
                            "Systems": {"items": [], "description": ""},
                            "Concepts": {"items": [], "description": ""},
                            "Conflicts": {"items": [], "description": ""},
                            "Societies": {"items": [], "description": ""},
                            "Character Types": {"items": [], "description": ""}
                        }
                    },
                    "plot": {
                        "chapters": [
                            {"id": chapter_id, "title": "Chapter 1", "events": []}
                        ],
                        "bank": []
                    },
                    "characters": {},
                    "brainstorm": {
                        "pages": {
                            page_id: {"nodes": [], "connectors": []}
                        }
                    },
                    "content": {
                        "chapters": {
                            chapter_id: {
                                "title": "Chapter 1",
                                "content": "",
                                "wordCount": 0,
                                "pageCount": 0
                            }
                        }
                    }
                })
                # Check if title column exists in book_memory table
                cursor.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.columns
                        WHERE table_name = 'book_memory' AND column_name = 'title'
                    );
                """)
                title_exists = cursor.fetchone()['exists']

                if title_exists:
                    cursor.execute(
                        "INSERT INTO book_memory (book_id, title, memory) VALUES (%s, %s, %s)",
                        (book_id, title, initial_memory)
                    )
                else:
                    cursor.execute(
                        "INSERT INTO book_memory (book_id, memory) VALUES (%s, %s)",
                        (book_id, initial_memory)
                    )
            conn.commit()
            print(f"Book created with ID {book_id}, title: {title}, user_id: {user_id}")
        except ValueError as e:
            conn.rollback()
            print(f"Error creating book: {str(e)}")
            raise HTTPException(status_code=400, detail=str(e))
        except Exception as e:
            conn.rollback()
            print(f"Database error while creating book: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to create book: {str(e)}")
        finally:
            conn.close()

    def get_user_books(self, user_id: str) -> list[dict]:
        """Fetches all books owned by a user."""
        conn = get_db_connection()
        try:
            with conn.cursor() as cursor:
                cursor.execute(
                    "SELECT book_id, title FROM books WHERE user_id = %s",
                    (user_id,)
                )
                books = [dict(row) for row in cursor.fetchall()]
                print(f"Fetched {len(books)} books for user_id: {user_id}")
                return books
        except Exception as e:
            print(f"Error fetching books for user_id {user_id}: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")
        finally:
            conn.close()

    def load_memory(self, book_id: str) -> str:
        """Loads the memory JSON for a given book."""
        conn = get_db_connection()
        try:
            with conn.cursor() as cursor:
                cursor.execute(
                    "SELECT memory FROM book_memory WHERE book_id = %s",
                    (book_id,)
                )
                result = cursor.fetchone()

                if not result:
                    print(f"No memory found for book_id {book_id}, returning empty object")
                    return '{}'

                memory = result['memory']

                # If memory is already a string, return it directly
                # If it's a dict/object, convert it to a JSON string
                if isinstance(memory, str):
                    print(f"Loaded memory string for book_id {book_id}, length: {len(memory)}")
                    return memory
                else:
                    memory_str = json.dumps(memory)
                    print(f"Loaded and converted memory for book_id {book_id}, length: {len(memory_str)}")
                    return memory_str
        except Exception as e:
            print(f"Error loading memory for book_id {book_id}: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to load memory: {str(e)}")
        finally:
            conn.close()

    def save_memory(self, book_id: str, title: str, memory: str) -> None:
        """Saves or updates the memory JSON for a book."""
        conn = get_db_connection()
        try:
            with conn.cursor() as cursor:
                # Check if title column exists in book_memory table
                cursor.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.columns
                        WHERE table_name = 'book_memory' AND column_name = 'title'
                    );
                """)
                title_exists = cursor.fetchone()['exists']

                if title_exists:
                    cursor.execute(
                        "INSERT INTO book_memory (book_id, title, memory) VALUES (%s, %s, %s) "
                        "ON CONFLICT (book_id) DO UPDATE SET memory = EXCLUDED.memory",
                        (book_id, title, memory)
                    )
                else:
                    cursor.execute(
                        "INSERT INTO book_memory (book_id, memory) VALUES (%s, %s) "
                        "ON CONFLICT (book_id) DO UPDATE SET memory = EXCLUDED.memory",
                        (book_id, memory)
                    )
            conn.commit()
            print(f"Saved memory for book_id {book_id}: {memory}")
        except Exception as e:
            conn.rollback()
            print(f"Error saving memory for book_id {book_id}: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to save memory: {str(e)}")
        finally:
            conn.close()

    def merge_memory(self, book_id: str, new_memory: str) -> str:
        """Merges new memory JSON with existing memory and updates the database."""
        print(f"\n[MERGE_MEMORY] Starting merge for book_id: {book_id}")
        print(f"[MERGE_MEMORY] New memory length: {len(new_memory) if new_memory else 0}")
        print(f"[MERGE_MEMORY] New memory preview: {new_memory[:100] if new_memory else 'None'}...")

        conn = get_db_connection()
        try:
            with conn.cursor() as cursor:
                # Check if title column exists in book_memory table
                cursor.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.columns
                        WHERE table_name = 'book_memory' AND column_name = 'title'
                    );
                """)
                title_exists = cursor.fetchone()['exists']
                print(f"[MERGE_MEMORY] Title column exists: {title_exists}")

                # Check if book_memory entry exists
                cursor.execute(
                    "SELECT EXISTS (SELECT 1 FROM book_memory WHERE book_id = %s)",
                    (book_id,)
                )
                memory_exists = cursor.fetchone()['exists']
                print(f"[MERGE_MEMORY] Memory entry exists: {memory_exists}")

                if not memory_exists:
                    print(f"[MERGE_MEMORY] Creating new memory entry for book_id: {book_id}")
                    # Get book title
                    cursor.execute(
                        "SELECT title FROM books WHERE book_id = %s",
                        (book_id,)
                    )
                    book_result = cursor.fetchone()
                    if not book_result:
                        raise ValueError(f"Book {book_id} not found in books table")

                    book_title = book_result['title']

                    # Create new memory entry
                    if title_exists:
                        cursor.execute(
                            "INSERT INTO book_memory (book_id, title, memory) VALUES (%s, %s, %s)",
                            (book_id, book_title, '{}')
                        )
                    else:
                        cursor.execute(
                            "INSERT INTO book_memory (book_id, memory) VALUES (%s, %s)",
                            (book_id, '{}')
                        )
                    print(f"[MERGE_MEMORY] Created new memory entry for book_id: {book_id}")
                    existing_dict = {}
                else:
                    # Fetch existing memory
                    if title_exists:
                        cursor.execute(
                            "SELECT title, memory FROM book_memory WHERE book_id = %s",
                            (book_id,)
                        )
                    else:
                        cursor.execute(
                            "SELECT memory FROM book_memory WHERE book_id = %s",
                            (book_id,)
                        )

                    result = cursor.fetchone()
                    if not result:
                        raise ValueError(f"Book {book_id} not found in book_memory table")

                    existing_memory = result['memory']
                    print(f"[MERGE_MEMORY] Existing memory type: {type(existing_memory).__name__}")
                    print(f"[MERGE_MEMORY] Existing memory length: {len(str(existing_memory)) if existing_memory else 0}")
                    print(f"[MERGE_MEMORY] Existing memory preview: {str(existing_memory)[:100] if existing_memory else 'None'}...")

                    # Parse existing memory if it's a string
                    if isinstance(existing_memory, str):
                        try:
                            existing_dict = json.loads(existing_memory)
                            print(f"[MERGE_MEMORY] Successfully parsed existing memory as JSON")
                        except json.JSONDecodeError as e:
                            print(f"[MERGE_MEMORY] Error parsing existing memory: {e}")
                            print(f"[MERGE_MEMORY] Raw existing memory: {existing_memory}")
                            existing_dict = {}
                    else:
                        existing_dict = existing_memory
                        print(f"[MERGE_MEMORY] Using existing memory as-is (not a string)")

                # Parse new memory if it's a string
                if isinstance(new_memory, str):
                    try:
                        new_dict = json.loads(new_memory)
                        print(f"[MERGE_MEMORY] Successfully parsed new memory as JSON")
                    except json.JSONDecodeError as e:
                        print(f"[MERGE_MEMORY] Error parsing new memory: {e}")
                        print(f"[MERGE_MEMORY] Raw new memory: {new_memory}")
                        new_dict = {}
                else:
                    new_dict = new_memory
                    print(f"[MERGE_MEMORY] Using new memory as-is (not a string)")

                # Deep merge the dictionaries
                print(f"[MERGE_MEMORY] Performing deep merge")
                merged_dict = self._deep_merge(existing_dict, new_dict)
                merged_memory = json.dumps(merged_dict)
                print(f"[MERGE_MEMORY] Merged memory length: {len(merged_memory)}")
                print(f"[MERGE_MEMORY] Merged memory preview: {merged_memory[:100]}...")

                # Update the memory in the database
                cursor.execute(
                    "UPDATE book_memory SET memory = %s WHERE book_id = %s",
                    (merged_memory, book_id)
                )
                print(f"[MERGE_MEMORY] Updated book_memory table for book_id: {book_id}")

                # Update the books table's updated_at timestamp
                cursor.execute(
                    "UPDATE books SET updated_at = CURRENT_TIMESTAMP WHERE book_id = %s",
                    (book_id,)
                )
                print(f"[MERGE_MEMORY] Updated books table timestamp for book_id: {book_id}")

            conn.commit()
            print(f"[MERGE_MEMORY] Committed transaction successfully")
            return merged_memory
        except ValueError as e:
            conn.rollback()
            print(f"Error merging memory for book_id {book_id}: {str(e)}")
            raise HTTPException(status_code=404, detail=str(e))
        except Exception as e:
            conn.rollback()
            print(f"Database error while merging memory for book_id {book_id}: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to merge memory: {str(e)}")
        finally:
            conn.close()

    def update(self, book_id: str, user_id: str, book_data: dict) -> dict:
        """Updates an existing book with new data."""
        print(f"\n[BOOK_REPO] Updating book with ID: {book_id}, user_id: {user_id}")
        print(f"[BOOK_REPO] Book data: {book_data}")

        conn = get_db_connection()
        try:
            with conn.cursor() as cursor:
                # Check if book exists and belongs to the user
                cursor.execute(
                    "SELECT * FROM books WHERE book_id = %s AND user_id = %s",
                    (book_id, user_id)
                )
                existing_book = cursor.fetchone()

                if not existing_book:
                    print(f"[BOOK_REPO] Book with ID {book_id} not found or does not belong to user {user_id}")
                    raise ValueError(f"Book with ID {book_id} not found or does not belong to user {user_id}")

                print(f"[BOOK_REPO] Found existing book: {existing_book}")

                # Check if the books table has the necessary columns
                cursor.execute("SELECT column_name FROM information_schema.columns WHERE table_name = 'books'")
                columns = [row['column_name'] for row in cursor.fetchall()]

                # If the columns don't exist, add them
                if 'author' not in columns:
                    cursor.execute("ALTER TABLE books ADD COLUMN author TEXT DEFAULT ''")
                if 'genre' not in columns:
                    cursor.execute("ALTER TABLE books ADD COLUMN genre TEXT DEFAULT ''")
                if 'description' not in columns:
                    cursor.execute("ALTER TABLE books ADD COLUMN description TEXT DEFAULT ''")

                # Get values from book_data with defaults from existing book
                title = book_data.get('title', existing_book['title'])
                author = book_data.get('author', existing_book.get('author', ''))
                genre = book_data.get('genre', existing_book.get('genre', ''))
                description = book_data.get('description', existing_book.get('description', ''))

                # Update the book
                cursor.execute(
                    """UPDATE books
                    SET title = %s, author = %s, genre = %s, description = %s, updated_at = CURRENT_TIMESTAMP
                    WHERE book_id = %s""",
                    (title, author, genre, description, book_id)
                )

                # Get the updated book
                cursor.execute(
                    "SELECT * FROM books WHERE book_id = %s",
                    (book_id,)
                )
                updated_book = cursor.fetchone()
                print(f"[BOOK_REPO] Updated book from database: {updated_book}")

                # Load memory
                memory = self.load_memory(book_id)

                conn.commit()
                print(f"[BOOK_REPO] Transaction committed successfully")

                # Return the updated book with memory
                result = {
                    "book_id": updated_book['book_id'],
                    "title": updated_book['title'],
                    "author": updated_book.get('author', ''),
                    "genre": updated_book.get('genre', ''),
                    "description": updated_book.get('description', ''),
                    "memory": memory
                }

                print(f"[BOOK_REPO] Final result: {result}")
                return result

        except ValueError as e:
            conn.rollback()
            raise HTTPException(status_code=404, detail=str(e))
        except Exception as e:
            conn.rollback()
            raise HTTPException(status_code=500, detail=f"Failed to update book: {str(e)}")
        finally:
            conn.close()

    def _deep_merge(self, dict1, dict2):
        """Deep merge two dictionaries."""
        result = dict1.copy()
        for key, value in dict2.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._deep_merge(result[key], value)
            else:
                result[key] = value
        return result

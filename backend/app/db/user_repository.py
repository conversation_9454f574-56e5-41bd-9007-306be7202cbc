# backend/app/db/user_repository.py
from .base import get_db_connection
from fastapi import HTTPException

class UserRepository:
    """Repository for managing user data in the database."""

    def create(self, user_id: str, email: str, password_hash: str) -> None:
        """Creates a new user in the database."""
        conn = get_db_connection()
        try:
            with conn.cursor() as cursor:
                # Check for existing user_id or email to avoid duplicates
                cursor.execute(
                    "SELECT user_id FROM users WHERE user_id = %s OR email = %s",
                    (user_id, email)
                )
                if cursor.fetchone():
                    raise ValueError(f"User with ID {user_id} or email {email} already exists")
                
                # Insert new user
                cursor.execute(
                    "INSERT INTO users (user_id, email, password_hash) VALUES (%s, %s, %s)",
                    (user_id, email, password_hash)
                )
            conn.commit()
        except ValueError as e:
            conn.rollback()
            raise HTTPException(status_code=400, detail=str(e))
        except Exception as e:
            conn.rollback()
            raise HTTPException(status_code=500, detail=f"Failed to create user: {str(e)}")
        finally:
            conn.close()

    def get_by_email(self, email: str) -> dict | None:
        """Retrieves a user by their email address."""
        conn = get_db_connection()
        try:
            with conn.cursor() as cursor:
                cursor.execute(
                    "SELECT user_id, email, password_hash FROM users WHERE email = %s",
                    (email,)
                )
                result = cursor.fetchone()
                return dict(result) if result else None
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to fetch user: {str(e)}")
        finally:
            conn.close()
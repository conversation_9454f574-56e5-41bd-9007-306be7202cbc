# backend/app/db/book_repository_new.py
"""
Repository for managing book-related data in the database.
This is a new version that doesn't use the book_memory table.
"""

from .base import get_db_connection
from fastapi import HTTPException
import json
import logging

logger = logging.getLogger(__name__)

class BookRepository:
    """Repository for managing book-related data in the database."""

    def create(self, book_id: str, user_id: str, title: str, author: str = '', genre: str = '', description: str = '') -> None:
        """Creates a new book and initializes related tables."""
        conn = get_db_connection()
        try:
            with conn.cursor() as cursor:
                # Check if book_id already exists
                cursor.execute("SELECT book_id FROM books WHERE book_id = %s", (book_id,))
                if cursor.fetchone():
                    raise ValueError(f"Book with ID {book_id} already exists")

                # Check if the books table has the necessary columns
                cursor.execute("SELECT column_name FROM information_schema.columns WHERE table_name = 'books'")
                columns = [row['column_name'] for row in cursor.fetchall()]

                # If the columns don't exist, add them
                if 'author' not in columns:
                    cursor.execute("ALTER TABLE books ADD COLUMN author TEXT DEFAULT ''")
                if 'genre' not in columns:
                    cursor.execute("ALTER TABLE books ADD COLUMN genre TEXT DEFAULT ''")
                if 'description' not in columns:
                    cursor.execute("ALTER TABLE books ADD COLUMN description TEXT DEFAULT ''")

                # Insert into books table with all fields
                cursor.execute(
                    """INSERT INTO books (book_id, user_id, title, author, genre, description, created_at, updated_at)
                    VALUES (%s, %s, %s, %s, %s, %s, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)""",
                    (book_id, user_id, title, author, genre, description)
                )

                # Create default brainstorm page with a unique page_id
                page_id = f"page_{book_id.replace('-', '_')}_1"
                cursor.execute(
                    "INSERT INTO brainstorm_pages (book_id, page_id, title, sequence_number) VALUES (%s, %s, %s, %s)",
                    (book_id, page_id, 'Page 1', 1)
                )

                # Create first chapter with a unique chapter_id
                chapter_id = f"ch_{book_id.replace('-', '_')}_1"
                cursor.execute(
                    "INSERT INTO chapters (book_id, chapter_id, title, sequence_number, content) VALUES (%s, %s, %s, %s, %s)",
                    (book_id, chapter_id, 'Chapter 1', 1, '')
                )

                # Initialize world settings
                cursor.execute(
                    """INSERT INTO world_settings (book_id, name, description, image_url, setting_types)
                    VALUES (%s, %s, %s, %s, %s)""",
                    (book_id, title, '', '', '[]')
                )

            conn.commit()
            logger.info(f"Book created with ID {book_id}, title: {title}, user_id: {user_id}")
        except ValueError as e:
            conn.rollback()
            logger.error(f"Error creating book: {str(e)}")
            raise HTTPException(status_code=400, detail=str(e))
        except Exception as e:
            conn.rollback()
            logger.error(f"Database error while creating book: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to create book: {str(e)}")
        finally:
            conn.close()

    def get_user_books(self, user_id: str) -> list[dict]:
        """Fetches all books owned by a user."""
        conn = get_db_connection()
        try:
            with conn.cursor() as cursor:
                cursor.execute(
                    "SELECT book_id, title, author, genre, description, created_at, updated_at FROM books WHERE user_id = %s",
                    (user_id,)
                )
                books = [dict(row) for row in cursor.fetchall()]
                logger.info(f"Fetched {len(books)} books for user_id: {user_id}")
                return books
        except Exception as e:
            logger.error(f"Error fetching books for user_id {user_id}: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")
        finally:
            conn.close()

    def get_book_details(self, book_id: str) -> dict:
        """Fetches a book's details."""
        conn = get_db_connection()
        try:
            with conn.cursor() as cursor:
                cursor.execute(
                    "SELECT book_id, user_id, title, author, genre, description, created_at, updated_at FROM books WHERE book_id = %s",
                    (book_id,)
                )
                book = cursor.fetchone()
                if not book:
                    raise ValueError(f"Book with ID {book_id} not found")

                return dict(book)
        except ValueError as e:
            logger.error(f"Error fetching book details: {str(e)}")
            raise HTTPException(status_code=404, detail=str(e))
        except Exception as e:
            logger.error(f"Database error while fetching book details: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to fetch book details: {str(e)}")
        finally:
            conn.close()

    def update(self, book_id: str, user_id: str, book_data: dict) -> dict:
        """Updates an existing book with new data."""
        logger.info(f"Updating book with ID: {book_id}, user_id: {user_id}")
        logger.info(f"Book data: {book_data}")

        conn = get_db_connection()
        try:
            with conn.cursor() as cursor:
                # Check if book exists and belongs to the user
                cursor.execute(
                    "SELECT * FROM books WHERE book_id = %s AND user_id = %s",
                    (book_id, user_id)
                )
                existing_book = cursor.fetchone()

                if not existing_book:
                    logger.error(f"Book with ID {book_id} not found or does not belong to user {user_id}")
                    raise ValueError(f"Book with ID {book_id} not found or does not belong to user {user_id}")

                logger.info(f"Found existing book: {existing_book}")

                # Check if the books table has the necessary columns
                cursor.execute("SELECT column_name FROM information_schema.columns WHERE table_name = 'books'")
                columns = [row['column_name'] for row in cursor.fetchall()]

                # If the columns don't exist, add them
                if 'author' not in columns:
                    cursor.execute("ALTER TABLE books ADD COLUMN author TEXT DEFAULT ''")
                if 'genre' not in columns:
                    cursor.execute("ALTER TABLE books ADD COLUMN genre TEXT DEFAULT ''")
                if 'description' not in columns:
                    cursor.execute("ALTER TABLE books ADD COLUMN description TEXT DEFAULT ''")

                # Get values from book_data with defaults from existing book
                title = book_data.get('title', existing_book['title'])
                author = book_data.get('author', existing_book.get('author', ''))
                genre = book_data.get('genre', existing_book.get('genre', ''))
                description = book_data.get('description', existing_book.get('description', ''))

                # Update the book
                cursor.execute(
                    """UPDATE books
                    SET title = %s, author = %s, genre = %s, description = %s, updated_at = CURRENT_TIMESTAMP
                    WHERE book_id = %s""",
                    (title, author, genre, description, book_id)
                )

                # Get the updated book
                cursor.execute(
                    "SELECT * FROM books WHERE book_id = %s",
                    (book_id,)
                )
                updated_book = cursor.fetchone()
                logger.info(f"Updated book from database: {updated_book}")

                conn.commit()
                logger.info(f"Transaction committed successfully")

                # Return the updated book
                result = {
                    "book_id": updated_book['book_id'],
                    "title": updated_book['title'],
                    "author": updated_book.get('author', ''),
                    "genre": updated_book.get('genre', ''),
                    "description": updated_book.get('description', '')
                }

                logger.info(f"Final result: {result}")
                return result

        except ValueError as e:
            conn.rollback()
            raise HTTPException(status_code=404, detail=str(e))
        except Exception as e:
            conn.rollback()
            raise HTTPException(status_code=500, detail=f"Failed to update book: {str(e)}")
        finally:
            conn.close()

    def delete(self, book_id: str, user_id: str) -> None:
        """Deletes a book and all associated data."""
        conn = get_db_connection()
        try:
            with conn.cursor() as cursor:
                # Check if book exists and belongs to the user
                cursor.execute(
                    "SELECT 1 FROM books WHERE book_id = %s AND user_id = %s",
                    (book_id, user_id)
                )
                if not cursor.fetchone():
                    raise ValueError(f"Book with ID {book_id} not found or does not belong to user {user_id}")

                # First, handle world element relationships
                # Delete relationships involving elements from this book
                cursor.execute(
                    """DELETE FROM world_element_relationships
                    WHERE source_element_id IN (SELECT element_id FROM world_elements WHERE book_id = %s)
                    OR target_element_id IN (SELECT element_id FROM world_elements WHERE book_id = %s)""",
                    (book_id, book_id)
                )
                logger.info(f"Deleted world element relationships for book {book_id}")

                # Delete the book (cascade will delete all associated data)
                cursor.execute(
                    "DELETE FROM books WHERE book_id = %s",
                    (book_id,)
                )

            conn.commit()
            logger.info(f"Deleted book with ID {book_id}")
        except ValueError as e:
            conn.rollback()
            raise HTTPException(status_code=404, detail=str(e))
        except Exception as e:
            conn.rollback()
            logger.error(f"Failed to delete book: {str(e)}")

            # Try a more detailed approach if the first attempt failed
            try:
                with conn.cursor() as cursor:
                    # Check if book exists and belongs to the user
                    cursor.execute(
                        "SELECT 1 FROM books WHERE book_id = %s AND user_id = %s",
                        (book_id, user_id)
                    )
                    if not cursor.fetchone():
                        raise ValueError(f"Book with ID {book_id} not found or does not belong to user {user_id}")

                    # Delete in a specific order to handle dependencies

                    # 1. Delete world element relationships
                    cursor.execute(
                        """DELETE FROM world_element_relationships
                        WHERE source_element_id IN (SELECT element_id FROM world_elements WHERE book_id = %s)
                        OR target_element_id IN (SELECT element_id FROM world_elements WHERE book_id = %s)""",
                        (book_id, book_id)
                    )
                    logger.info(f"Deleted world element relationships for book {book_id}")

                    # 2. Delete world element tag assignments
                    cursor.execute(
                        """DELETE FROM world_element_tag_assignments
                        WHERE element_id IN (SELECT element_id FROM world_elements WHERE book_id = %s)""",
                        (book_id,)
                    )
                    logger.info(f"Deleted world element tag assignments for book {book_id}")

                    # 3. Delete world element tags
                    cursor.execute(
                        "DELETE FROM world_element_tags WHERE book_id = %s",
                        (book_id,)
                    )
                    logger.info(f"Deleted world element tags for book {book_id}")

                    # 4. Delete world table rows
                    cursor.execute(
                        """DELETE FROM world_table_rows
                        WHERE element_id IN (SELECT element_id FROM world_elements WHERE book_id = %s)""",
                        (book_id,)
                    )
                    logger.info(f"Deleted world table rows for book {book_id}")

                    # 5. Delete world table schemas
                    cursor.execute(
                        "DELETE FROM world_table_schemas WHERE book_id = %s",
                        (book_id,)
                    )
                    logger.info(f"Deleted world table schemas for book {book_id}")

                    # 6. Delete world map elements
                    cursor.execute(
                        """DELETE FROM world_map_elements
                        WHERE world_element_id IN (SELECT element_id FROM world_elements WHERE book_id = %s)""",
                        (book_id,)
                    )
                    logger.info(f"Deleted world map elements for book {book_id}")

                    # 7. Delete world maps
                    cursor.execute(
                        "DELETE FROM world_maps WHERE book_id = %s",
                        (book_id,)
                    )
                    logger.info(f"Deleted world maps for book {book_id}")

                    # 8. Delete world elements (handle parent-child relationships)
                    # First, set parent_id to NULL for all elements to avoid foreign key constraints
                    cursor.execute(
                        "UPDATE world_elements SET parent_id = NULL WHERE book_id = %s",
                        (book_id,)
                    )
                    logger.info(f"Reset parent_id for world elements in book {book_id}")

                    # Then delete all world elements
                    cursor.execute(
                        "DELETE FROM world_elements WHERE book_id = %s",
                        (book_id,)
                    )
                    logger.info(f"Deleted world elements for book {book_id}")

                    # 9. Delete event_characters and event_locations
                    cursor.execute(
                        """DELETE FROM event_characters
                        WHERE event_id IN (SELECT event_id FROM plot_events WHERE book_id = %s)""",
                        (book_id,)
                    )
                    cursor.execute(
                        """DELETE FROM event_locations
                        WHERE event_id IN (SELECT event_id FROM plot_events WHERE book_id = %s)""",
                        (book_id,)
                    )
                    logger.info(f"Deleted event characters and locations for book {book_id}")

                    # 10. Delete plot events
                    cursor.execute(
                        "DELETE FROM plot_events WHERE book_id = %s",
                        (book_id,)
                    )
                    logger.info(f"Deleted plot events for book {book_id}")

                    # 11. Delete character relationships and traits
                    cursor.execute(
                        """DELETE FROM character_relationships
                        WHERE character_id IN (SELECT character_id FROM characters WHERE book_id = %s)
                        OR related_character_id IN (SELECT character_id FROM characters WHERE book_id = %s)""",
                        (book_id, book_id)
                    )
                    cursor.execute(
                        """DELETE FROM character_traits
                        WHERE character_id IN (SELECT character_id FROM characters WHERE book_id = %s)""",
                        (book_id,)
                    )
                    logger.info(f"Deleted character relationships and traits for book {book_id}")

                    # 12. Delete characters
                    cursor.execute(
                        "DELETE FROM characters WHERE book_id = %s",
                        (book_id,)
                    )
                    logger.info(f"Deleted characters for book {book_id}")

                    # 13. Delete brainstorm connectors, nodes, and shapes
                    cursor.execute(
                        """DELETE FROM brainstorm_connectors
                        WHERE page_id IN (SELECT page_id FROM brainstorm_pages WHERE book_id = %s)""",
                        (book_id,)
                    )
                    cursor.execute(
                        """DELETE FROM brainstorm_nodes
                        WHERE page_id IN (SELECT page_id FROM brainstorm_pages WHERE book_id = %s)""",
                        (book_id,)
                    )
                    cursor.execute(
                        """DELETE FROM brainstorm_shapes
                        WHERE page_id IN (SELECT page_id FROM brainstorm_pages WHERE book_id = %s)""",
                        (book_id,)
                    )
                    logger.info(f"Deleted brainstorm connectors, nodes, and shapes for book {book_id}")

                    # 14. Delete brainstorm pages
                    cursor.execute(
                        "DELETE FROM brainstorm_pages WHERE book_id = %s",
                        (book_id,)
                    )
                    logger.info(f"Deleted brainstorm pages for book {book_id}")

                    # 15. Delete chapters
                    cursor.execute(
                        "DELETE FROM chapters WHERE book_id = %s",
                        (book_id,)
                    )
                    logger.info(f"Deleted chapters for book {book_id}")

                    # 16. Delete book_memory (if it exists)
                    cursor.execute(
                        "DELETE FROM book_memory WHERE book_id = %s",
                        (book_id,)
                    )
                    logger.info(f"Deleted book_memory for book {book_id}")

                    # 17. Finally delete the book itself
                    cursor.execute(
                        "DELETE FROM books WHERE book_id = %s",
                        (book_id,)
                    )
                    logger.info(f"Deleted book with ID {book_id}")

                conn.commit()
                logger.info(f"Successfully deleted book with ID {book_id} using detailed approach")
            except Exception as detailed_error:
                conn.rollback()
                logger.error(f"Detailed approach also failed: {str(detailed_error)}")
                raise HTTPException(status_code=500, detail=f"Failed to delete book: {str(e)}\nDetailed error: {str(detailed_error)}")
        finally:
            conn.close()

    def get_book_metadata(self, book_id: str) -> dict:
        """Fetches metadata for a book from dedicated tables."""
        conn = get_db_connection()
        try:
            metadata = {}

            with conn.cursor() as cursor:
                # Get chapter count
                cursor.execute(
                    "SELECT COUNT(*) FROM chapters WHERE book_id = %s",
                    (book_id,)
                )
                metadata['chapterCount'] = cursor.fetchone()['count']

                # Get character count
                cursor.execute(
                    "SELECT COUNT(*) FROM characters WHERE book_id = %s",
                    (book_id,)
                )
                metadata['characterCount'] = cursor.fetchone()['count']

                # Get world elements count (all categories)
                cursor.execute(
                    "SELECT COUNT(*) FROM world_elements WHERE book_id = %s",
                    (book_id,)
                )
                metadata['locationCount'] = cursor.fetchone()['count']

                # Get word count from chapters
                cursor.execute(
                    "SELECT SUM(array_length(regexp_split_to_array(content, '\\s+'), 1)) FROM chapters WHERE book_id = %s AND content IS NOT NULL AND content != ''",
                    (book_id,)
                )
                result = cursor.fetchone()
                metadata['wordCount'] = result['sum'] if result['sum'] else 0

                # Get last edited timestamp
                cursor.execute(
                    "SELECT updated_at FROM books WHERE book_id = %s",
                    (book_id,)
                )
                metadata['lastEdited'] = cursor.fetchone()['updated_at'].isoformat()

            return metadata
        except Exception as e:
            logger.error(f"Error fetching book metadata: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to fetch book metadata: {str(e)}")
        finally:
            conn.close()

    def carryover_book(self, source_book_id: str, user_id: str, new_title: str) -> dict:
        """Creates a new book based on an existing one, copying all relevant data."""
        # This method would need to be implemented to copy data from dedicated tables
        # instead of using the book_memory table
        raise NotImplementedError("This method needs to be implemented to use dedicated tables")

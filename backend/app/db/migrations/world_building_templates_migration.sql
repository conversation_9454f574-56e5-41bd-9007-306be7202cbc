-- World Building Templates Mi<PERSON> Script
-- This script adds support for 179 element templates and enhances the relationship system

-- Step 1: Create element_templates table
CREATE TABLE IF NOT EXISTS element_templates (
    template_id TEXT PRIMARY KEY,
    template_name TEXT NOT NULL,
    display_name TEXT NOT NULL,
    category_id TEXT NOT NULL,
    parent_template_id TEXT,
    description TEXT,
    field_definitions JSONB NOT NULL,
    relationship_definitions JSONB,
    icon TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES world_aspect_categories(category_id),
    FOREIGN KEY (parent_template_id) REFERENCES element_templates(template_id) ON DELETE SET NULL
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_element_templates_category ON element_templates(category_id);
CREATE INDEX IF NOT EXISTS idx_element_templates_parent ON element_templates(parent_template_id);

-- Step 2: Create relationship_types table
CREATE TABLE IF NOT EXISTS relationship_types (
    relationship_type_id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    inverse_relationship_type_id TEXT,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (inverse_relationship_type_id) REFERENCES relationship_types(relationship_type_id) ON DELETE SET NULL
);

-- Step 3: Add indexes to world_elements table
CREATE INDEX IF NOT EXISTS idx_world_elements_element_type ON world_elements(element_type);
CREATE INDEX IF NOT EXISTS idx_world_elements_parent_id ON world_elements(parent_id);
CREATE INDEX IF NOT EXISTS idx_world_elements_custom_fields ON world_elements USING GIN (custom_fields);

-- Step 4: Create a function to safely convert text to JSON
CREATE OR REPLACE FUNCTION try_parse_json(text_value TEXT)
RETURNS JSONB AS $$
BEGIN
    -- Try to parse as JSON
    RETURN text_value::JSONB;
EXCEPTION WHEN OTHERS THEN
    -- If it fails, return an empty JSON object
    RETURN '{}'::JSONB;
END;
$$ LANGUAGE plpgsql;

-- Step 5: Update elements with non-empty attributes
UPDATE world_elements
SET custom_fields = CASE
    -- If attributes is valid JSON, use it directly
    WHEN try_parse_json(attributes) <> '{}'::JSONB THEN try_parse_json(attributes)
    -- Otherwise, try to parse as key-value pairs
    WHEN attributes <> '{}' AND attributes IS NOT NULL THEN
        (SELECT jsonb_object_agg(
            trim(key),
            trim(value)
        ) FROM (
            SELECT
                trim(split_part(kv, ':', 1)) AS key,
                trim(split_part(kv, ':', 2)) AS value
            FROM regexp_split_to_table(
                -- Remove curly braces
                regexp_replace(attributes, '[{}]', '', 'g'),
                ','
            ) AS kv
            WHERE kv <> ''
        ) AS kvs)
    ELSE custom_fields
END
WHERE attributes <> '{}' AND attributes IS NOT NULL;

-- Step 6: Create a backup of the attributes column data
ALTER TABLE world_elements ADD COLUMN IF NOT EXISTS attributes_backup TEXT;
UPDATE world_elements SET attributes_backup = attributes;

-- Step 7: Set attributes to empty JSON object
UPDATE world_elements SET attributes = '{}';

-- Step 8: Insert standard relationship types
INSERT INTO relationship_types (relationship_type_id, name, inverse_relationship_type_id, description)
VALUES
    ('contains', 'Contains', 'belongs_to', 'Indicates that one element contains another'),
    ('belongs_to', 'Belongs To', 'contains', 'Indicates that one element belongs to another'),
    ('orbits', 'Orbits', 'has_orbiting', 'Indicates that one celestial body orbits another'),
    ('has_orbiting', 'Has Orbiting', 'orbits', 'Indicates that one celestial body has another orbiting it'),
    ('rules', 'Rules', 'ruled_by', 'Indicates that one entity rules another'),
    ('ruled_by', 'Ruled By', 'rules', 'Indicates that one entity is ruled by another'),
    ('part_of', 'Part Of', 'has_part', 'Indicates that one element is part of another'),
    ('has_part', 'Has Part', 'part_of', 'Indicates that one element has another as a part'),
    ('connected_to', 'Connected To', 'connected_to', 'Indicates that elements are connected'),
    ('allied_with', 'Allied With', 'allied_with', 'Indicates that entities are allies'),
    ('hostile_to', 'Hostile To', 'hostile_to', 'Indicates that entities are hostile to each other'),
    ('trades_with', 'Trades With', 'trades_with', 'Indicates that entities trade with each other'),
    ('influences', 'Influences', 'influenced_by', 'Indicates that one entity influences another'),
    ('influenced_by', 'Influenced By', 'influences', 'Indicates that one entity is influenced by another'),
    ('related_to', 'Related To', 'related_to', 'Generic relationship between elements')
ON CONFLICT (relationship_type_id) DO UPDATE
SET
    name = EXCLUDED.name,
    inverse_relationship_type_id = EXCLUDED.inverse_relationship_type_id,
    description = EXCLUDED.description,
    updated_at = CURRENT_TIMESTAMP;

-- Step 9: Update existing relationships with more specific types
UPDATE world_element_relationships
SET relationship_type = CASE
    -- Celestial body relationships
    WHEN
        (SELECT element_type FROM world_elements WHERE element_id = source_element_id) IN ('star', 'planet', 'moon') AND
        (SELECT element_type FROM world_elements WHERE element_id = target_element_id) IN ('star', 'planet', 'moon')
    THEN
        CASE
            WHEN (SELECT element_type FROM world_elements WHERE element_id = source_element_id) = 'planet' AND
                 (SELECT element_type FROM world_elements WHERE element_id = target_element_id) = 'star'
            THEN 'orbits'
            WHEN (SELECT element_type FROM world_elements WHERE element_id = source_element_id) = 'star' AND
                 (SELECT element_type FROM world_elements WHERE element_id = target_element_id) = 'planet'
            THEN 'has_orbiting'
            WHEN (SELECT element_type FROM world_elements WHERE element_id = source_element_id) = 'moon' AND
                 (SELECT element_type FROM world_elements WHERE element_id = target_element_id) = 'planet'
            THEN 'orbits'
            WHEN (SELECT element_type FROM world_elements WHERE element_id = source_element_id) = 'planet' AND
                 (SELECT element_type FROM world_elements WHERE element_id = target_element_id) = 'moon'
            THEN 'has_orbiting'
            ELSE relationship_type
        END

    -- Default case
    ELSE relationship_type
END;

-- Step 10: Create a trigger function for basic element validation
CREATE OR REPLACE FUNCTION validate_element_before_save()
RETURNS TRIGGER AS $$
BEGIN
    -- Check if element_type is provided
    IF NEW.element_type IS NULL OR NEW.element_type = '' THEN
        RAISE EXCEPTION 'Element type is required';
    END IF;

    -- Check if name is provided
    IF NEW.name IS NULL OR NEW.name = '' THEN
        RAISE EXCEPTION 'Element name is required';
    END IF;

    -- Check if custom_fields is valid JSON
    IF NEW.custom_fields IS NULL THEN
        NEW.custom_fields := '{}'::jsonb;
    END IF;

    -- Check if parent exists (if parent_id is provided)
    IF NEW.parent_id IS NOT NULL THEN
        IF NOT EXISTS (SELECT 1 FROM world_elements WHERE element_id = NEW.parent_id) THEN
            RAISE EXCEPTION 'Parent element with ID % does not exist', NEW.parent_id;
        END IF;

        -- Set has_children flag on parent
        UPDATE world_elements SET has_children = TRUE WHERE element_id = NEW.parent_id;
    END IF;

    -- Set updated_at timestamp
    NEW.updated_at := CURRENT_TIMESTAMP;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger
DROP TRIGGER IF EXISTS validate_element_before_save_trigger ON world_elements;
CREATE TRIGGER validate_element_before_save_trigger
BEFORE INSERT OR UPDATE ON world_elements
FOR EACH ROW EXECUTE FUNCTION validate_element_before_save();

-- Step 11: Create a trigger function for relationship validation
CREATE OR REPLACE FUNCTION validate_relationship_before_save()
RETURNS TRIGGER AS $$
BEGIN
    -- Check if source_element_id is provided
    IF NEW.source_element_id IS NULL THEN
        RAISE EXCEPTION 'Source element ID is required';
    END IF;

    -- Check if target_element_id is provided
    IF NEW.target_element_id IS NULL THEN
        RAISE EXCEPTION 'Target element ID is required';
    END IF;

    -- Check if relationship_type is provided
    IF NEW.relationship_type IS NULL OR NEW.relationship_type = '' THEN
        RAISE EXCEPTION 'Relationship type is required';
    END IF;

    -- Check if source element exists
    IF NOT EXISTS (SELECT 1 FROM world_elements WHERE element_id = NEW.source_element_id) THEN
        RAISE EXCEPTION 'Source element with ID % does not exist', NEW.source_element_id;
    END IF;

    -- Check if target element exists
    IF NOT EXISTS (SELECT 1 FROM world_elements WHERE element_id = NEW.target_element_id) THEN
        RAISE EXCEPTION 'Target element with ID % does not exist', NEW.target_element_id;
    END IF;

    -- Set updated_at timestamp
    NEW.updated_at := CURRENT_TIMESTAMP;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger
DROP TRIGGER IF EXISTS validate_relationship_before_save_trigger ON world_element_relationships;
CREATE TRIGGER validate_relationship_before_save_trigger
BEFORE INSERT OR UPDATE ON world_element_relationships
FOR EACH ROW EXECUTE FUNCTION validate_relationship_before_save();

-- Step 12: Add template import placeholder
-- Note: The actual template import will be done by the Python script
-- This is just a placeholder comment
-- INSERT INTO element_templates (...) VALUES (...);

-- Insert missing templates
-- These templates failed to insert due to foreign key constraint violations

-- First, insert Continent template
INSERT INTO element_templates (
    template_id, 
    template_name, 
    display_name, 
    category_id, 
    parent_template_id, 
    description, 
    field_definitions, 
    relationship_definitions
) VALUES (
    'continent', 
    'ContinentTemplate', 
    'Continent', 
    'cat_physical_geography', 
    'planet', 
    'Template for Continent elements', 
    '{"fields": [
        {"id": "id", "label": "id", "type": "text", "required": false, "description": "Unique identifier"},
        {"id": "name", "label": "name", "type": "text", "required": false, "description": "Name of the continent"},
        {"id": "planet_id", "label": "planet_id", "type": "text", "required": false, "description": "Planet it''s located on"},
        {"id": "size", "label": "size", "type": "text", "required": false, "description": "Area measurement"},
        {"id": "location", "label": "location", "type": "text", "required": false, "description": "Position on planet"},
        {"id": "climate_zones", "label": "climate_zones", "type": "text", "required": false, "description": "Types of climates present"},
        {"id": "biome_types", "label": "biome_types", "type": "text", "required": false, "description": "Major ecosystems"},
        {"id": "dominant_terrain", "label": "dominant_terrain", "type": "text", "required": false, "description": "Primary landscape features"},
        {"id": "major_mountain_ranges", "label": "major_mountain_ranges", "type": "text", "required": false, "description": "Significant mountains"},
        {"id": "major_water_bodies", "label": "major_water_bodies", "type": "text", "required": false, "description": "Significant lakes/rivers"},
        {"id": "natural_resources", "label": "natural_resources", "type": "text", "required": false, "description": "Important resources"},
        {"id": "indigenous_species", "label": "indigenous_species", "type": "text", "required": false, "description": "Native life forms"},
        {"id": "population", "label": "population", "type": "text", "required": false, "description": "Number of inhabitants"},
        {"id": "dominant_species_id", "label": "dominant_species_id", "type": "text", "required": false, "description": "Main intelligent species"},
        {"id": "major_nations", "label": "major_nations", "type": "text", "required": false, "description": "Political entities"},
        {"id": "cultural_regions", "label": "cultural_regions", "type": "text", "required": false, "description": "Distinct cultural areas"},
        {"id": "discovery_date", "label": "discovery_date", "type": "date", "required": false, "description": "When first explored (in-world)"},
        {"id": "development_level", "label": "development_level", "type": "text", "required": false, "description": "Civilization advancement"},
        {"id": "image_reference", "label": "image_reference", "type": "text", "required": false, "description": "Map/visual reference"},
        {"id": "description", "label": "description", "type": "text", "required": false, "description": "Detailed description"}
    ]}', 
    '{"valid_relationships": [
        {"target_template": "planet", "relationship_type": "belongs_to", "inverse_relationship": "contains", "label": "Belongs To Planet", "description": "Planet this Continent belongs to"}
    ]}'
);

-- Next, insert Moon template
INSERT INTO element_templates (
    template_id, 
    template_name, 
    display_name, 
    category_id, 
    parent_template_id, 
    description, 
    field_definitions, 
    relationship_definitions
) VALUES (
    'moon', 
    'MoonTemplate', 
    'Moon', 
    'cat_metaphysical_cosmology', 
    'planet', 
    'Template for Moon elements', 
    '{"fields": [
        {"id": "id", "label": "id", "type": "text", "required": false, "description": "Unique identifier"},
        {"id": "name", "label": "name", "type": "text", "required": false, "description": "Name of the moon"},
        {"id": "parent_planet_id", "label": "parent_planet_id", "type": "text", "required": false, "description": "Planet it orbits"},
        {"id": "size", "label": "size", "type": "text", "required": false, "description": "Diameter/radius"},
        {"id": "mass", "label": "mass", "type": "text", "required": false, "description": "Mass measurement"},
        {"id": "orbit_distance", "label": "orbit_distance", "type": "text", "required": false, "description": "Distance from planet"},
        {"id": "orbit_period", "label": "orbit_period", "type": "text", "required": false, "description": "Time to orbit planet once"},
        {"id": "rotation_period", "label": "rotation_period", "type": "text", "required": false, "description": "Time to rotate once"},
        {"id": "atmosphere", "label": "atmosphere", "type": "text", "required": false, "description": "Atmospheric composition if any"},
        {"id": "surface_composition", "label": "surface_composition", "type": "text", "required": false, "description": "What the surface is made of"},
        {"id": "surface_features", "label": "surface_features", "type": "text", "required": false, "description": "Notable geography"},
        {"id": "gravity", "label": "gravity", "type": "text", "required": false, "description": "Surface gravity relative to Earth"},
        {"id": "habitable", "label": "habitable", "type": "boolean", "required": false, "description": "Boolean (yes/no)"},
        {"id": "life_present", "label": "life_present", "type": "boolean", "required": false, "description": "Boolean (yes/no)"},
        {"id": "settlements", "label": "settlements", "type": "text", "required": false, "description": "Any colonies or bases"},
        {"id": "controlling_faction_id", "label": "controlling_faction_id", "type": "text", "required": false, "description": "Who claims/controls it"},
        {"id": "resources", "label": "resources", "type": "text", "required": false, "description": "Valuable materials present"},
        {"id": "cultural_significance", "label": "cultural_significance", "type": "text", "required": false, "description": "Importance in local cultures"},
        {"id": "image_reference", "label": "image_reference", "type": "text", "required": false, "description": "Visual reference"},
        {"id": "description", "label": "description", "type": "text", "required": false, "description": "Detailed description"}
    ]}', 
    '{"valid_relationships": [
        {"target_template": "planet", "relationship_type": "orbits", "inverse_relationship": "has_orbiting", "label": "Orbits Planet", "description": "Planet this Moon orbits"}
    ]}'
);

-- Finally, insert Ocean/Sea template
INSERT INTO element_templates (
    template_id, 
    template_name, 
    display_name, 
    category_id, 
    parent_template_id, 
    description, 
    field_definitions, 
    relationship_definitions
) VALUES (
    'ocean_sea', 
    'Ocean/SeaTemplate', 
    'Ocean/Sea', 
    'cat_physical_geography', 
    'planet', 
    'Template for Ocean/Sea elements', 
    '{"fields": [
        {"id": "id", "label": "id", "type": "text", "required": false, "description": "Unique identifier"},
        {"id": "name", "label": "name", "type": "text", "required": false, "description": "Name of the water body"},
        {"id": "planet_id", "label": "planet_id", "type": "text", "required": false, "description": "Planet it''s located on"},
        {"id": "size", "label": "size", "type": "text", "required": false, "description": "Surface area/volume"},
        {"id": "location", "label": "location", "type": "text", "required": false, "description": "Position on planet"},
        {"id": "max_depth", "label": "max_depth", "type": "text", "required": false, "description": "Deepest point"},
        {"id": "average_depth", "label": "average_depth", "type": "text", "required": false, "description": "Mean depth"},
        {"id": "water_composition", "label": "water_composition", "type": "text", "required": false, "description": "Chemical makeup"},
        {"id": "salinity", "label": "salinity", "type": "text", "required": false, "description": "Salt content"},
        {"id": "temperature_range", "label": "temperature_range", "type": "text", "required": false, "description": "Min/max temperatures"},
        {"id": "currents", "label": "currents", "type": "text", "required": false, "description": "Major water movements"},
        {"id": "indigenous_life", "label": "indigenous_life", "type": "text", "required": false, "description": "Native species"},
        {"id": "coastal_nations", "label": "coastal_nations", "type": "text", "required": false, "description": "Bordering countries"},
        {"id": "ports", "label": "ports", "type": "text", "required": false, "description": "Major harbors"},
        {"id": "resources", "label": "resources", "type": "text", "required": false, "description": "Valuable materials/food sources"},
        {"id": "weather_patterns", "label": "weather_patterns", "type": "text", "required": false, "description": "Common meteorological events"},
        {"id": "navigational_hazards", "label": "navigational_hazards", "type": "text", "required": false, "description": "Dangers to ships"},
        {"id": "cultural_significance", "label": "cultural_significance", "type": "text", "required": false, "description": "Importance to civilizations"},
        {"id": "image_reference", "label": "image_reference", "type": "text", "required": false, "description": "Map/visual reference"},
        {"id": "description", "label": "description", "type": "text", "required": false, "description": "Detailed description"}
    ]}', 
    '{"valid_relationships": [
        {"target_template": "planet", "relationship_type": "belongs_to", "inverse_relationship": "contains", "label": "Belongs To Planet", "description": "Planet this Ocean/Sea belongs to"}
    ]}'
);

-- Template Versioning Migration Script
-- This script adds versioning support to element templates and tracks template usage

-- Add versioning fields to element_templates table
ALTER TABLE element_templates
ADD COLUMN version TEXT NOT NULL DEFAULT '1.0',
ADD COLUMN source_template_id TEXT REFERENCES element_templates(template_id),
ADD COLUMN creator_id TEXT,
ADD COLUMN is_system_template BOOLEAN NOT NULL DEFAULT TRUE;

-- Add template reference fields to world_elements table
ALTER TABLE world_elements
ADD COLUMN template_id TEXT REFERENCES element_templates(template_id),
ADD COLUMN template_version TEXT;

-- Initialize template_id in world_elements based on element_type
UPDATE world_elements
SET template_id = element_type
WHERE element_type IS NOT NULL;

-- Initialize template_version in world_elements
UPDATE world_elements
SET template_version = '1.0'
WHERE template_id IS NOT NULL;

-- Create template usage statistics table
CREATE TABLE template_usage_stats (
    id SERIAL PRIMARY KEY,
    template_id TEXT NOT NULL REFERENCES element_templates(template_id),
    template_version TEXT NOT NULL,
    book_id TEXT NOT NULL,
    user_id TEXT NOT NULL,
    usage_count INTEGER NOT NULL DEFAULT 0,
    last_used_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(template_id, template_version, book_id, user_id)
);

-- Initialize template_usage_stats based on existing elements
INSERT INTO template_usage_stats (template_id, template_version, book_id, user_id, usage_count)
SELECT 
    e.template_id,
    '1.0' as template_version,
    e.book_id,
    b.user_id,
    COUNT(*) as usage_count
FROM 
    world_elements e
JOIN 
    books b ON e.book_id = b.book_id
WHERE 
    e.template_id IS NOT NULL
GROUP BY 
    e.template_id, e.book_id, b.user_id;

-- Add indexes for performance
CREATE INDEX idx_element_templates_version ON element_templates(version);
CREATE INDEX idx_element_templates_creator ON element_templates(creator_id);
CREATE INDEX idx_world_elements_template ON world_elements(template_id, template_version);
CREATE INDEX idx_template_usage_stats_template ON template_usage_stats(template_id, template_version);
CREATE INDEX idx_template_usage_stats_book ON template_usage_stats(book_id);
CREATE INDEX idx_template_usage_stats_user ON template_usage_stats(user_id);

-- Add comments to explain the versioning system
COMMENT ON COLUMN element_templates.version IS 'Template version in major.minor format';
COMMENT ON COLUMN element_templates.source_template_id IS 'Reference to the original template this was derived from';
COMMENT ON COLUMN element_templates.creator_id IS 'User ID of the template creator (NULL for system templates)';
COMMENT ON COLUMN element_templates.is_system_template IS 'Flag indicating if this is a system template (TRUE) or user-created (FALSE)';
COMMENT ON COLUMN world_elements.template_id IS 'Reference to the template used to create this element';
COMMENT ON COLUMN world_elements.template_version IS 'Version of the template used to create this element';
COMMENT ON TABLE template_usage_stats IS 'Tracks usage statistics for templates';
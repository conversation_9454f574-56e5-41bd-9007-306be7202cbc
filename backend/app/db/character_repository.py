# backend/app/db/character_repository.py
import logging
from .base import get_db_connection
from fastapi import HTTPException
import json
import uuid

# Configure logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

class CharacterRepository:
    """Repository for managing character data within book memory."""

    def get_characters(self, book_id: str) -> dict:
        """Fetches all characters for a book from the characters table."""
        conn = get_db_connection()
        try:
            with conn.cursor() as cursor:
                # Get all characters for the book
                cursor.execute(
                    "SELECT * FROM characters WHERE book_id = %s",
                    (book_id,)
                )
                characters = {}
                for row in cursor.fetchall():
                    character_id = row['character_id']
                    characters[row['name']] = {
                        'character_id': character_id,
                        'description': row['description'],
                        'age': row['age'],
                        'role': row['role'],
                        'headshot': row['headshot'],
                        'race': row['race'],
                        'backstory': row['backstory'] if 'backstory' in row else '',
                        'arc': row['arc'] if 'arc' in row else '',
                        'gender_identity': row['gender_identity'] if 'gender_identity' in row else '',
                        'sexual_orientation': row['sexual_orientation'] if 'sexual_orientation' in row else '',
                        'traits': [],
                        'relationships': []
                    }

                # Get traits for all characters
                cursor.execute(
                    """SELECT ct.character_id, ct.trait
                    FROM character_traits ct
                    JOIN characters c ON ct.character_id = c.character_id
                    WHERE c.book_id = %s""",
                    (book_id,)
                )
                for row in cursor.fetchall():
                    character_id = row['character_id']
                    # Find the character by ID
                    for char_name, char_data in characters.items():
                        if char_data['character_id'] == character_id:
                            char_data['traits'].append(row['trait'])
                            break

                # Get relationships for all characters
                cursor.execute(
                    """SELECT cr.character_id, c2.name as related_name, cr.relationship_type, cr.description, cr.strength
                    FROM character_relationships cr
                    JOIN characters c1 ON cr.character_id = c1.character_id
                    JOIN characters c2 ON cr.related_character_id = c2.character_id
                    WHERE c1.book_id = %s""",
                    (book_id,)
                )
                for row in cursor.fetchall():
                    character_id = row['character_id']
                    # Find the character by ID
                    for char_name, char_data in characters.items():
                        if char_data['character_id'] == character_id:
                            char_data['relationships'].append({
                                'name': row['related_name'],
                                'type': row['relationship_type'],
                                'description': row['description'],
                                'strength': row['strength'] if row['strength'] is not None else 3  # Default to 3 if null
                            })
                            break

                return characters
        finally:
            conn.close()

    def add_character(self, book_id: str, name: str, details: dict) -> str:
        """Adds or updates a character in the database. Returns the character_id."""
        conn = get_db_connection()
        try:
            with conn.cursor() as cursor:
                # Generate a unique character_id if not provided
                character_id = details.get("character_id", f"char_{book_id}_{name}_{uuid.uuid4().hex[:8]}")

                # Insert or update the character
                # First, check if gender_identity and sexual_orientation columns exist
                try:
                    cursor.execute("""
                        SELECT column_name
                        FROM information_schema.columns
                        WHERE table_name = 'characters'
                        AND column_name IN ('gender_identity', 'sexual_orientation');
                    """)
                    existing_columns = [row[0] for row in cursor.fetchall()]

                    # Add columns if they don't exist
                    if 'gender_identity' not in existing_columns:
                        cursor.execute("ALTER TABLE characters ADD COLUMN gender_identity TEXT;")
                    if 'sexual_orientation' not in existing_columns:
                        cursor.execute("ALTER TABLE characters ADD COLUMN sexual_orientation TEXT;")
                except Exception as e:
                    logger.error(f"Error checking or adding columns: {str(e)}")
                    # Continue anyway, as the columns might already exist

                # Now insert or update with the new columns
                cursor.execute(
                    """INSERT INTO characters
                    (character_id, book_id, name, description, age, role, race, headshot, backstory, arc, gender_identity, sexual_orientation)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    ON CONFLICT (character_id) DO UPDATE SET
                    name = EXCLUDED.name,
                    description = EXCLUDED.description,
                    age = EXCLUDED.age,
                    role = EXCLUDED.role,
                    race = EXCLUDED.race,
                    headshot = EXCLUDED.headshot,
                    backstory = EXCLUDED.backstory,
                    arc = EXCLUDED.arc,
                    gender_identity = EXCLUDED.gender_identity,
                    sexual_orientation = EXCLUDED.sexual_orientation,
                    updated_at = CURRENT_TIMESTAMP""",
                    (
                        character_id,
                        book_id,
                        name,
                        details.get("description", ""),
                        details.get("age", ""),
                        details.get("role", ""),
                        details.get("race", ""),
                        details.get("headshot", ""),
                        details.get("backstory", ""),
                        details.get("arc", ""),
                        details.get("gender_identity", ""),
                        details.get("sexual_orientation", "")
                    )
                )

                # Handle traits - first delete existing traits
                cursor.execute(
                    "DELETE FROM character_traits WHERE character_id = %s",
                    (character_id,)
                )

                # Insert new traits
                traits = details.get("traits", [])
                if isinstance(traits, str):
                    traits = [t.strip() for t in traits.split(',') if t.strip()]

                for trait in traits:
                    trait_id = f"trait_{character_id}_{uuid.uuid4().hex[:8]}"
                    cursor.execute(
                        """INSERT INTO character_traits
                        (trait_id, character_id, trait)
                        VALUES (%s, %s, %s)""",
                        (trait_id, character_id, trait)
                    )

                # Only handle relationships if they're included in the details
                if "relationships" in details:
                    # Handle relationships - first delete existing relationships
                    cursor.execute(
                        "DELETE FROM character_relationships WHERE character_id = %s",
                        (character_id,)
                    )

                    # Insert new relationships
                    relationships = details.get("relationships", [])
                    if isinstance(relationships, str):
                        # Try to parse relationship string
                        try:
                            rel_pairs = [r.strip() for r in relationships.split(',') if r.strip()]
                            parsed_relationships = []
                            for pair in rel_pairs:
                                if ':' in pair:
                                    rel_name, rel_type = pair.split(':', 1)
                                    parsed_relationships.append({"name": rel_name.strip(), "type": rel_type.strip()})
                            relationships = parsed_relationships
                        except Exception as e:
                            logger.error(f"Error parsing relationships string: {e}")
                            relationships = []

                    # Process relationships only if they're included in the details
                    for rel in relationships:
                        if isinstance(rel, dict) and "name" in rel and "type" in rel:
                            # Find the related character_id
                            cursor.execute(
                                "SELECT character_id FROM characters WHERE book_id = %s AND name = %s",
                                (book_id, rel["name"])
                            )
                            related_char = cursor.fetchone()
                            if related_char:
                                related_character_id = related_char["character_id"]
                                relationship_id = f"rel_{character_id}_{related_character_id}_{uuid.uuid4().hex[:8]}"
                                # Get description from various possible fields
                                description = rel.get("description", "")

                                # Check all possible keys that might contain the relationship description
                                if not description:
                                    possible_keys = [
                                        "Description of relationship with this character",
                                        "description of relationship with this character",
                                        "Description",
                                        "Relationship description",
                                        "relationship description"
                                    ]

                                    for key in possible_keys:
                                        if rel.get(key):
                                            description = rel.get(key)
                                            break

                                # Get strength with default of 3 (close friend)
                                strength = rel.get("strength", 3)

                                # Ensure strength is an integer between 0 and 5
                                try:
                                    strength = int(strength)
                                    strength = max(0, min(5, strength))
                                except (ValueError, TypeError):
                                    strength = 3  # Default to 3 if invalid

                                cursor.execute(
                                    """INSERT INTO character_relationships
                                    (relationship_id, character_id, related_character_id, relationship_type, description, strength)
                                    VALUES (%s, %s, %s, %s, %s, %s)""",
                                    (
                                        relationship_id,
                                        character_id,
                                        related_character_id,
                                        rel["type"],
                                        description,
                                        strength
                                    )
                                )

            conn.commit()
            return character_id
        except Exception as e:
            conn.rollback()
            logger.error(f"Error adding character: {str(e)}")
            raise e
        finally:
            conn.close()

    # Old _insert_character method removed as it's no longer used and could cause conflicts

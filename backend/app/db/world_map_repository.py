"""
Repository for managing world maps and map elements.
"""
from .base import get_db_connection
import json
import uuid
import logging

logger = logging.getLogger(__name__)

class WorldMapRepository:
    """Repository for managing world maps and map elements."""

    def get_maps(self, book_id: str) -> list:
        """Fetches all maps for a book."""
        conn = get_db_connection()
        try:
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    SELECT 
                        map_id,
                        book_id,
                        name,
                        description,
                        base_image_path,
                        width,
                        height,
                        created_at,
                        updated_at
                    FROM world_maps
                    WHERE book_id = %s
                    ORDER BY name
                    """,
                    (book_id,)
                )
                maps = cursor.fetchall()
                return list(maps)
        except Exception as e:
            logger.error(f"Error fetching maps: {str(e)}")
            return []
        finally:
            conn.close()

    def get_map(self, map_id: str) -> dict:
        """Fetches a specific map by ID."""
        conn = get_db_connection()
        try:
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    SELECT 
                        map_id,
                        book_id,
                        name,
                        description,
                        base_image_path,
                        width,
                        height,
                        created_at,
                        updated_at
                    FROM world_maps
                    WHERE map_id = %s
                    """,
                    (map_id,)
                )
                map_data = cursor.fetchone()
                if not map_data:
                    raise ValueError(f"Map with ID {map_id} not found")
                return dict(map_data)
        except Exception as e:
            logger.error(f"Error fetching map: {str(e)}")
            raise
        finally:
            conn.close()

    def create_map(self, map_data: dict) -> dict:
        """Creates a new map."""
        conn = get_db_connection()
        try:
            # Generate a map ID if not provided
            if not map_data.get('map_id'):
                map_data['map_id'] = f"map_{uuid.uuid4()}"

            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    INSERT INTO world_maps (
                        map_id,
                        book_id,
                        name,
                        description,
                        base_image_path,
                        width,
                        height
                    )
                    VALUES (%s, %s, %s, %s, %s, %s, %s)
                    RETURNING 
                        map_id,
                        book_id,
                        name,
                        description,
                        base_image_path,
                        width,
                        height,
                        created_at,
                        updated_at
                    """,
                    (
                        map_data.get('map_id'),
                        map_data.get('book_id'),
                        map_data.get('name'),
                        map_data.get('description', ''),
                        map_data.get('base_image_path', ''),
                        map_data.get('width'),
                        map_data.get('height')
                    )
                )
                result = cursor.fetchone()
                conn.commit()
                return dict(result)
        except Exception as e:
            conn.rollback()
            logger.error(f"Error creating map: {str(e)}")
            raise
        finally:
            conn.close()

    def update_map(self, map_id: str, map_data: dict) -> dict:
        """Updates an existing map."""
        conn = get_db_connection()
        try:
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    UPDATE world_maps
                    SET 
                        name = %s,
                        description = %s,
                        base_image_path = %s,
                        width = %s,
                        height = %s,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE map_id = %s
                    RETURNING 
                        map_id,
                        book_id,
                        name,
                        description,
                        base_image_path,
                        width,
                        height,
                        created_at,
                        updated_at
                    """,
                    (
                        map_data.get('name'),
                        map_data.get('description', ''),
                        map_data.get('base_image_path', ''),
                        map_data.get('width'),
                        map_data.get('height'),
                        map_id
                    )
                )
                result = cursor.fetchone()
                if not result:
                    raise ValueError(f"Map with ID {map_id} not found")
                conn.commit()
                return dict(result)
        except Exception as e:
            conn.rollback()
            logger.error(f"Error updating map: {str(e)}")
            raise
        finally:
            conn.close()

    def delete_map(self, map_id: str) -> bool:
        """Deletes a map and all its elements."""
        conn = get_db_connection()
        try:
            with conn.cursor() as cursor:
                # First delete all map elements
                cursor.execute(
                    "DELETE FROM world_map_elements WHERE map_id = %s",
                    (map_id,)
                )
                
                # Then delete the map itself
                cursor.execute(
                    "DELETE FROM world_maps WHERE map_id = %s",
                    (map_id,)
                )
                conn.commit()
                return cursor.rowcount > 0
        except Exception as e:
            conn.rollback()
            logger.error(f"Error deleting map: {str(e)}")
            raise
        finally:
            conn.close()

    def get_map_elements(self, map_id: str) -> list:
        """Fetches all elements for a specific map."""
        conn = get_db_connection()
        try:
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    SELECT 
                        me.map_element_id,
                        me.map_id,
                        me.world_element_id,
                        me.element_type,
                        me.coordinates,
                        me.style_data,
                        me.label,
                        me.created_at,
                        me.updated_at,
                        we.name as element_name,
                        we.category as element_category
                    FROM world_map_elements me
                    JOIN world_elements we ON me.world_element_id = we.element_id
                    WHERE me.map_id = %s
                    """,
                    (map_id,)
                )
                elements = cursor.fetchall()
                return list(elements)
        except Exception as e:
            logger.error(f"Error fetching map elements: {str(e)}")
            return []
        finally:
            conn.close()

    def create_map_element(self, element_data: dict) -> dict:
        """Creates a new map element."""
        conn = get_db_connection()
        try:
            # Generate an element ID if not provided
            if not element_data.get('map_element_id'):
                element_data['map_element_id'] = f"mapelem_{uuid.uuid4()}"

            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    INSERT INTO world_map_elements (
                        map_element_id,
                        map_id,
                        world_element_id,
                        element_type,
                        coordinates,
                        style_data,
                        label
                    )
                    VALUES (%s, %s, %s, %s, %s, %s, %s)
                    RETURNING 
                        map_element_id,
                        map_id,
                        world_element_id,
                        element_type,
                        coordinates,
                        style_data,
                        label,
                        created_at,
                        updated_at
                    """,
                    (
                        element_data.get('map_element_id'),
                        element_data.get('map_id'),
                        element_data.get('world_element_id'),
                        element_data.get('element_type'),
                        element_data.get('coordinates'),
                        element_data.get('style_data', ''),
                        element_data.get('label', '')
                    )
                )
                result = cursor.fetchone()
                conn.commit()
                return dict(result)
        except Exception as e:
            conn.rollback()
            logger.error(f"Error creating map element: {str(e)}")
            raise
        finally:
            conn.close()

    def update_map_element(self, map_element_id: str, element_data: dict) -> dict:
        """Updates an existing map element."""
        conn = get_db_connection()
        try:
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    UPDATE world_map_elements
                    SET 
                        element_type = %s,
                        coordinates = %s,
                        style_data = %s,
                        label = %s,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE map_element_id = %s
                    RETURNING 
                        map_element_id,
                        map_id,
                        world_element_id,
                        element_type,
                        coordinates,
                        style_data,
                        label,
                        created_at,
                        updated_at
                    """,
                    (
                        element_data.get('element_type'),
                        element_data.get('coordinates'),
                        element_data.get('style_data', ''),
                        element_data.get('label', ''),
                        map_element_id
                    )
                )
                result = cursor.fetchone()
                if not result:
                    raise ValueError(f"Map element with ID {map_element_id} not found")
                conn.commit()
                return dict(result)
        except Exception as e:
            conn.rollback()
            logger.error(f"Error updating map element: {str(e)}")
            raise
        finally:
            conn.close()

    def delete_map_element(self, map_element_id: str) -> bool:
        """Deletes a map element."""
        conn = get_db_connection()
        try:
            with conn.cursor() as cursor:
                cursor.execute(
                    "DELETE FROM world_map_elements WHERE map_element_id = %s",
                    (map_element_id,)
                )
                conn.commit()
                return cursor.rowcount > 0
        except Exception as e:
            conn.rollback()
            logger.error(f"Error deleting map element: {str(e)}")
            raise
        finally:
            conn.close()

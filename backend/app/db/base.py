# backend/app/db/base.py
import psycopg2
from psycopg2.extras import RealDictCursor
from pathlib import Path
from fastapi import HTTPException

# Database configuration (adjust these as needed)
DB_CONFIG = {
    "dbname": "bookapp1",
    "user": "project1_user",
    "password": "rHEWX*.V.Adaline..",
    "host": "localhost",
    "port": "5432"
}

def get_db_connection():
    """Establishes a connection to the PostgreSQL database."""
    try:
        conn = psycopg2.connect(**DB_CONFIG, cursor_factory=RealDictCursor)
        return conn
    except psycopg2.Error as e:
        raise HTTPException(status_code=500, detail=f"Database connection failed: {str(e)}")

def init_db():
    """Initializes the database schema if it doesn't exist."""
    conn = get_db_connection()
    try:
        with conn.cursor() as cursor:
            # Check if users table exists (as a proxy for all tables)
            cursor.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables
                    WHERE table_schema = 'public' AND table_name = 'users'
                );
            """)
            tables_exist = cursor.fetchone()['exists']

            if tables_exist:
                print("Database tables already exist")
                return

            # If tables don't exist, read the schema file and create them
            schema_path = Path(__file__).parent / "schema.sql"
            with open(schema_path, 'r') as f:
                schema_sql = f.read()

            # Execute the schema in chunks to handle errors better
            statements = schema_sql.split(';')
            for statement in statements:
                if statement.strip():
                    try:
                        cursor.execute(statement + ';')
                    except psycopg2.Error as e:
                        # Ignore errors about tables already existing
                        if "already exists" not in str(e):
                            print(f"Error executing statement: {e}")

            conn.commit()
            print("Database initialized successfully")
    except psycopg2.Error as e:
        conn.rollback()
        print(f"Database initialization error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to initialize database: {str(e)}")
    finally:
        conn.close()

# Utility to ensure proper cleanup in endpoints
class DBContext:
    """Context manager for database connections."""
    def __init__(self):
        self.conn = None

    def __enter__(self):
        self.conn = get_db_connection()
        return self.conn

    def __exit__(self, exc_type, exc_val, exc_tb):
        if exc_type is not None:
            self.conn.rollback()
        else:
            self.conn.commit()
        self.conn.close()
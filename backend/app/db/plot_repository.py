# backend/app/db/plot_repository.py
from .base import get_db_connection
from fastapi import HTTPException
import uuid

class PlotRepository:
    """Repository for managing plot data using the new database schema."""

    def __init__(self):
        """Initialize the repository and ensure required tables exist."""
        self.ensure_tables_exist()

    def ensure_tables_exist(self):
        """Ensure that all required tables exist in the database."""
        conn = get_db_connection()
        try:
            with conn.cursor() as cursor:
                # Add source_node_id to plot_events if it doesn't exist
                cursor.execute("""
                    DO $$
                    BEGIN
                        IF NOT EXISTS (
                            SELECT FROM information_schema.columns
                            WHERE table_name = 'plot_events' AND column_name = 'source_node_id'
                        ) THEN
                            ALTER TABLE plot_events ADD COLUMN source_node_id TEXT;
                        END IF;
                    END
                    $$;
                """)

                # Create event_characters table if it doesn't exist
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS event_characters (
                        id SERIAL PRIMARY KEY,
                        event_id TEXT NOT NULL REFERENCES plot_events(event_id) ON DELETE CASCADE,
                        character_name TEXT NOT NULL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    );
                """)

                # Create event_locations table if it doesn't exist
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS event_locations (
                        id SERIAL PRIMARY KEY,
                        event_id TEXT NOT NULL REFERENCES plot_events(event_id) ON DELETE CASCADE,
                        location_name TEXT NOT NULL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    );
                """)

                # Add indexes for better performance
                cursor.execute("""
                    CREATE INDEX IF NOT EXISTS idx_event_characters_event_id ON event_characters(event_id);
                """)
                cursor.execute("""
                    CREATE INDEX IF NOT EXISTS idx_event_locations_event_id ON event_locations(event_id);
                """)

                # Add node_type to brainstorm_nodes if it doesn't exist
                cursor.execute("""
                    DO $$
                    BEGIN
                        IF NOT EXISTS (
                            SELECT FROM information_schema.columns
                            WHERE table_name = 'brainstorm_nodes' AND column_name = 'node_type'
                        ) THEN
                            ALTER TABLE brainstorm_nodes ADD COLUMN node_type TEXT;
                        END IF;
                    END
                    $$;
                """)

                conn.commit()
        except Exception as e:
            conn.rollback()
            print(f"Error ensuring tables exist: {e}")
        finally:
            conn.close()

    def get_plot(self, book_id: str) -> dict:
        """Fetches the plot data for a given book from the new database tables."""
        conn = get_db_connection()
        try:
            with conn.cursor() as cursor:
                # Get all chapters for the book
                cursor.execute(
                    """
                    SELECT chapter_id, title, sequence_number
                    FROM chapters
                    WHERE book_id = %s
                    ORDER BY sequence_number
                    """,
                    (book_id,)
                )
                chapters_data = cursor.fetchall()

                # Initialize plot structure
                plot = {
                    "chapters": [],
                    "bank": []
                }

                # Process chapters
                for chapter in chapters_data:
                    # Get events for this chapter
                    cursor.execute(
                        """
                        SELECT event_id, title, description, sequence_number,
                               x, y, width, height
                        FROM plot_events
                        WHERE book_id = %s AND chapter_id = %s
                        ORDER BY sequence_number
                        """,
                        (book_id, chapter['chapter_id'])
                    )
                    events = cursor.fetchall()

                    # Fetch character and location data for each event
                    for event in events:
                        event_id = event['event_id']

                        # Get characters - safely check if table exists
                        try:
                            cursor.execute(
                                """
                                SELECT EXISTS (
                                    SELECT FROM information_schema.tables
                                    WHERE table_name = 'event_characters'
                                )
                                """
                            )
                            table_exists = cursor.fetchone()[0]

                            if table_exists:
                                cursor.execute(
                                    "SELECT character_name FROM event_characters WHERE event_id = %s",
                                    (event_id,)
                                )
                                characters = [row['character_name'] for row in cursor.fetchall()]
                            else:
                                characters = []
                        except Exception as e:
                            print(f"Error checking event_characters table: {e}")
                            characters = []

                        event['characters'] = characters

                        # Get locations - safely check if table exists
                        try:
                            cursor.execute(
                                """
                                SELECT EXISTS (
                                    SELECT FROM information_schema.tables
                                    WHERE table_name = 'event_locations'
                                )
                                """
                            )
                            table_exists = cursor.fetchone()[0]

                            if table_exists:
                                cursor.execute(
                                    "SELECT location_name FROM event_locations WHERE event_id = %s",
                                    (event_id,)
                                )
                                locations = [row['location_name'] for row in cursor.fetchall()]
                            else:
                                locations = []
                        except Exception as e:
                            print(f"Error checking event_locations table: {e}")
                            locations = []

                        event['locations'] = locations

                    # Add chapter with its events to the plot
                    plot["chapters"].append({
                        "id": chapter['chapter_id'],
                        "title": chapter['title'],
                        "events": events
                    })

                # Get events in the bank (not assigned to any chapter)
                cursor.execute(
                    """
                    SELECT event_id, title, description, sequence_number,
                           x, y, width, height
                    FROM plot_events
                    WHERE book_id = %s AND is_in_bank = TRUE
                    ORDER BY sequence_number
                    """,
                    (book_id,)
                )
                bank_events = cursor.fetchall()

                # Fetch character and location data for each bank event
                for event in bank_events:
                    event_id = event['event_id']

                    # Get characters - safely check if table exists
                    try:
                        cursor.execute(
                            """
                            SELECT EXISTS (
                                SELECT FROM information_schema.tables
                                WHERE table_name = 'event_characters'
                            )
                            """
                        )
                        table_exists = cursor.fetchone()[0]

                        if table_exists:
                            cursor.execute(
                                "SELECT character_name FROM event_characters WHERE event_id = %s",
                                (event_id,)
                            )
                            characters = [row['character_name'] for row in cursor.fetchall()]
                        else:
                            characters = []
                    except Exception as e:
                        print(f"Error checking event_characters table: {e}")
                        characters = []

                    event['characters'] = characters

                    # Get locations - safely check if table exists
                    try:
                        cursor.execute(
                            """
                            SELECT EXISTS (
                                SELECT FROM information_schema.tables
                                WHERE table_name = 'event_locations'
                            )
                            """
                        )
                        table_exists = cursor.fetchone()[0]

                        if table_exists:
                            cursor.execute(
                                "SELECT location_name FROM event_locations WHERE event_id = %s",
                                (event_id,)
                            )
                            locations = [row['location_name'] for row in cursor.fetchall()]
                        else:
                            locations = []
                    except Exception as e:
                        print(f"Error checking event_locations table: {e}")
                        locations = []

                    event['locations'] = locations

                plot["bank"] = bank_events

                # Ensure at least one chapter exists
                if not plot["chapters"]:
                    chapter_id = f"chapter_{uuid.uuid4().hex[:8]}"
                    cursor.execute(
                        """
                        INSERT INTO chapters (chapter_id, book_id, title, sequence_number)
                        VALUES (%s, %s, %s, %s)
                        """,
                        (chapter_id, book_id, "Chapter 1", 1)
                    )
                    conn.commit()
                    plot["chapters"] = [{"id": chapter_id, "title": "Chapter 1", "events": []}]

                return plot

        except Exception as e:
            print(f"Failed to fetch plot for book_id {book_id}: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to fetch plot: {str(e)}")
        finally:
            conn.close()

    def add_plot_node(self, book_id: str, plot: dict) -> str:
        """Updates plot data using the new database schema."""
        conn = get_db_connection()
        try:
            with conn.cursor() as cursor:
                # First, verify the book exists
                cursor.execute("SELECT book_id FROM books WHERE book_id = %s", (book_id,))
                if not cursor.fetchone():
                    raise ValueError(f"Book {book_id} not found")

                # Transaction automatically begins with the first query
                # No need to call conn.begin() as it doesn't exist in psycopg2

                # Process chapters
                if "chapters" in plot:
                    for chapter_idx, chapter in enumerate(plot["chapters"]):
                        chapter_id = chapter.get("id")

                        # Check if chapter exists
                        cursor.execute(
                            "SELECT chapter_id FROM chapters WHERE chapter_id = %s",
                            (chapter_id,)
                        )
                        chapter_exists = cursor.fetchone()

                        if chapter_exists:
                            # Update existing chapter
                            cursor.execute(
                                """
                                UPDATE chapters
                                SET title = %s, sequence_number = %s
                                WHERE chapter_id = %s
                                """,
                                (chapter["title"], chapter_idx + 1, chapter_id)
                            )
                        else:
                            # Create new chapter
                            if not chapter_id:
                                chapter_id = f"chapter_{uuid.uuid4().hex[:8]}"
                                chapter["id"] = chapter_id

                            cursor.execute(
                                """
                                INSERT INTO chapters (chapter_id, book_id, title, sequence_number)
                                VALUES (%s, %s, %s, %s)
                                """,
                                (chapter_id, book_id, chapter["title"], chapter_idx + 1)
                            )

                        # Process events in this chapter
                        if "events" in chapter:
                            for event_idx, event in enumerate(chapter["events"]):
                                event_id = event.get("id") or event.get("event_id")

                                # Check if event exists
                                cursor.execute(
                                    "SELECT event_id FROM plot_events WHERE event_id = %s",
                                    (event_id,)
                                )
                                event_exists = cursor.fetchone()

                                if event_exists:
                                    # Update existing event
                                    cursor.execute(
                                        """
                                        UPDATE plot_events
                                        SET title = %s, description = %s, sequence_number = %s,
                                            chapter_id = %s, is_in_bank = FALSE,
                                            x = %s, y = %s, width = %s, height = %s
                                        WHERE event_id = %s
                                        """,
                                        (
                                            event.get("title", ""),
                                            event.get("description", ""),
                                            event_idx + 1,
                                            chapter_id,
                                            event.get("x", 0),
                                            event.get("y", 0),
                                            event.get("width", 200),
                                            event.get("height", 100),
                                            event_id
                                        )
                                    )

                                    # Safely handle character associations
                                    try:
                                        cursor.execute(
                                            """
                                            SELECT EXISTS (
                                                SELECT FROM information_schema.tables
                                                WHERE table_name = 'event_characters'
                                            )
                                            """
                                        )
                                        char_table_exists = cursor.fetchone()[0]

                                        if char_table_exists:
                                            # Delete existing character associations
                                            cursor.execute(
                                                "DELETE FROM event_characters WHERE event_id = %s",
                                                (event_id,)
                                            )

                                            # Insert new character associations
                                            characters = event.get("characters", [])
                                            if characters:
                                                for character in characters:
                                                    cursor.execute(
                                                        """
                                                        INSERT INTO event_characters (event_id, character_name)
                                                        VALUES (%s, %s)
                                                        """,
                                                        (event_id, character)
                                                    )
                                        else:
                                            # Create the event_characters table if it doesn't exist
                                            cursor.execute(
                                                """
                                                CREATE TABLE IF NOT EXISTS event_characters (
                                                    id SERIAL PRIMARY KEY,
                                                    event_id TEXT NOT NULL REFERENCES plot_events(event_id) ON DELETE CASCADE,
                                                    character_name TEXT NOT NULL,
                                                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                                                )
                                                """
                                            )

                                            # Insert new character associations
                                            characters = event.get("characters", [])
                                            if characters:
                                                for character in characters:
                                                    cursor.execute(
                                                        """
                                                        INSERT INTO event_characters (event_id, character_name)
                                                        VALUES (%s, %s)
                                                        """,
                                                        (event_id, character)
                                                    )
                                    except Exception as e:
                                        print(f"Error handling event_characters: {e}")

                                    # Safely handle location associations
                                    try:
                                        cursor.execute(
                                            """
                                            SELECT EXISTS (
                                                SELECT FROM information_schema.tables
                                                WHERE table_name = 'event_locations'
                                            )
                                            """
                                        )
                                        loc_table_exists = cursor.fetchone()[0]

                                        if loc_table_exists:
                                            # Delete existing location associations
                                            cursor.execute(
                                                "DELETE FROM event_locations WHERE event_id = %s",
                                                (event_id,)
                                            )

                                            # Insert new location associations
                                            locations = event.get("locations", [])
                                            if locations:
                                                for location in locations:
                                                    cursor.execute(
                                                        """
                                                        INSERT INTO event_locations (event_id, location_name)
                                                        VALUES (%s, %s)
                                                        """,
                                                        (event_id, location)
                                                    )
                                        else:
                                            # Create the event_locations table if it doesn't exist
                                            cursor.execute(
                                                """
                                                CREATE TABLE IF NOT EXISTS event_locations (
                                                    id SERIAL PRIMARY KEY,
                                                    event_id TEXT NOT NULL REFERENCES plot_events(event_id) ON DELETE CASCADE,
                                                    location_name TEXT NOT NULL,
                                                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                                                )
                                                """
                                            )

                                            # Insert new location associations
                                            locations = event.get("locations", [])
                                            if locations:
                                                for location in locations:
                                                    cursor.execute(
                                                        """
                                                        INSERT INTO event_locations (event_id, location_name)
                                                        VALUES (%s, %s)
                                                        """,
                                                        (event_id, location)
                                                    )
                                    except Exception as e:
                                        print(f"Error handling event_locations: {e}")
                                else:
                                    # Create new event
                                    if not event_id:
                                        event_id = f"event_{uuid.uuid4().hex[:8]}"

                                    cursor.execute(
                                        """
                                        INSERT INTO plot_events (
                                            event_id, book_id, chapter_id, title, description,
                                            sequence_number, is_in_bank, x, y, width, height
                                        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                                        """,
                                        (
                                            event_id,
                                            book_id,
                                            chapter_id,
                                            event.get("title", ""),
                                            event.get("description", ""),
                                            event_idx + 1,
                                            False,
                                            event.get("x", 0),
                                            event.get("y", 0),
                                            event.get("width", 200),
                                            event.get("height", 100)
                                        )
                                    )

                                    # Safely handle character associations
                                    try:
                                        cursor.execute(
                                            """
                                            SELECT EXISTS (
                                                SELECT FROM information_schema.tables
                                                WHERE table_name = 'event_characters'
                                            )
                                            """
                                        )
                                        char_table_exists = cursor.fetchone()[0]

                                        if char_table_exists:
                                            # Insert character associations
                                            characters = event.get("characters", [])
                                            if characters:
                                                for character in characters:
                                                    cursor.execute(
                                                        """
                                                        INSERT INTO event_characters (event_id, character_name)
                                                        VALUES (%s, %s)
                                                        """,
                                                        (event_id, character)
                                                    )
                                        else:
                                            # Create the event_characters table if it doesn't exist
                                            cursor.execute(
                                                """
                                                CREATE TABLE IF NOT EXISTS event_characters (
                                                    id SERIAL PRIMARY KEY,
                                                    event_id TEXT NOT NULL REFERENCES plot_events(event_id) ON DELETE CASCADE,
                                                    character_name TEXT NOT NULL,
                                                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                                                )
                                                """
                                            )

                                            # Insert character associations
                                            characters = event.get("characters", [])
                                            if characters:
                                                for character in characters:
                                                    cursor.execute(
                                                        """
                                                        INSERT INTO event_characters (event_id, character_name)
                                                        VALUES (%s, %s)
                                                        """,
                                                        (event_id, character)
                                                    )
                                    except Exception as e:
                                        print(f"Error handling event_characters: {e}")

                                    # Safely handle location associations
                                    try:
                                        cursor.execute(
                                            """
                                            SELECT EXISTS (
                                                SELECT FROM information_schema.tables
                                                WHERE table_name = 'event_locations'
                                            )
                                            """
                                        )
                                        loc_table_exists = cursor.fetchone()[0]

                                        if loc_table_exists:
                                            # Insert location associations
                                            locations = event.get("locations", [])
                                            if locations:
                                                for location in locations:
                                                    cursor.execute(
                                                        """
                                                        INSERT INTO event_locations (event_id, location_name)
                                                        VALUES (%s, %s)
                                                        """,
                                                        (event_id, location)
                                                    )
                                        else:
                                            # Create the event_locations table if it doesn't exist
                                            cursor.execute(
                                                """
                                                CREATE TABLE IF NOT EXISTS event_locations (
                                                    id SERIAL PRIMARY KEY,
                                                    event_id TEXT NOT NULL REFERENCES plot_events(event_id) ON DELETE CASCADE,
                                                    location_name TEXT NOT NULL,
                                                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                                                )
                                                """
                                            )

                                            # Insert location associations
                                            locations = event.get("locations", [])
                                            if locations:
                                                for location in locations:
                                                    cursor.execute(
                                                        """
                                                        INSERT INTO event_locations (event_id, location_name)
                                                        VALUES (%s, %s)
                                                        """,
                                                        (event_id, location)
                                                    )
                                    except Exception as e:
                                        print(f"Error handling event_locations: {e}")

                # Process bank events
                if "bank" in plot:
                    for event_idx, event in enumerate(plot["bank"]):
                        event_id = event.get("id") or event.get("event_id")

                        # Check if event exists
                        cursor.execute(
                            "SELECT event_id FROM plot_events WHERE event_id = %s",
                            (event_id,)
                        )
                        event_exists = cursor.fetchone()

                        if event_exists:
                            # Update existing event
                            cursor.execute(
                                """
                                UPDATE plot_events
                                SET title = %s, description = %s, sequence_number = %s,
                                    chapter_id = NULL, is_in_bank = TRUE,
                                    x = %s, y = %s, width = %s, height = %s
                                WHERE event_id = %s
                                """,
                                (
                                    event.get("title", ""),
                                    event.get("description", ""),
                                    event_idx + 1,
                                    event.get("x", 0),
                                    event.get("y", 0),
                                    event.get("width", 200),
                                    event.get("height", 100),
                                    event_id
                                )
                            )

                            # Update character relationships
                            # First, delete existing relationships
                            cursor.execute(
                                "DELETE FROM event_characters WHERE event_id = %s",
                                (event_id,)
                            )

                            # Then add new ones
                            characters = event.get("characters", [])
                            if characters:
                                for character in characters:
                                    cursor.execute(
                                        """
                                        INSERT INTO event_characters (event_id, character_name)
                                        VALUES (%s, %s)
                                        """,
                                        (event_id, character)
                                    )

                            # Update location relationships
                            # First, delete existing relationships
                            cursor.execute(
                                "DELETE FROM event_locations WHERE event_id = %s",
                                (event_id,)
                            )

                            # Then add new ones
                            locations = event.get("locations", [])
                            if locations:
                                for location in locations:
                                    cursor.execute(
                                        """
                                        INSERT INTO event_locations (event_id, location_name)
                                        VALUES (%s, %s)
                                        """,
                                        (event_id, location)
                                    )
                        else:
                            # Create new event
                            if not event_id:
                                event_id = f"event_{uuid.uuid4().hex[:8]}"

                            cursor.execute(
                                """
                                INSERT INTO plot_events (
                                    event_id, book_id, chapter_id, title, description,
                                    sequence_number, is_in_bank, x, y, width, height
                                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                                """,
                                (
                                    event_id,
                                    book_id,
                                    None,
                                    event.get("title", ""),
                                    event.get("description", ""),
                                    event_idx + 1,
                                    True,
                                    event.get("x", 0),
                                    event.get("y", 0),
                                    event.get("width", 200),
                                    event.get("height", 100)
                                )
                            )

                            # Safely handle character associations
                            try:
                                cursor.execute(
                                    """
                                    SELECT EXISTS (
                                        SELECT FROM information_schema.tables
                                        WHERE table_name = 'event_characters'
                                    )
                                    """
                                )
                                char_table_exists = cursor.fetchone()[0]

                                if char_table_exists:
                                    # Insert character associations
                                    characters = event.get("characters", [])
                                    if characters:
                                        for character in characters:
                                            cursor.execute(
                                                """
                                                INSERT INTO event_characters (event_id, character_name)
                                                VALUES (%s, %s)
                                                """,
                                                (event_id, character)
                                            )
                                else:
                                    # Create the event_characters table if it doesn't exist
                                    cursor.execute(
                                        """
                                        CREATE TABLE IF NOT EXISTS event_characters (
                                            id SERIAL PRIMARY KEY,
                                            event_id TEXT NOT NULL REFERENCES plot_events(event_id) ON DELETE CASCADE,
                                            character_name TEXT NOT NULL,
                                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                                        )
                                        """
                                    )

                                    # Insert character associations
                                    characters = event.get("characters", [])
                                    if characters:
                                        for character in characters:
                                            cursor.execute(
                                                """
                                                INSERT INTO event_characters (event_id, character_name)
                                                VALUES (%s, %s)
                                                """,
                                                (event_id, character)
                                            )
                            except Exception as e:
                                print(f"Error handling event_characters: {e}")

                            # Safely handle location associations
                            try:
                                cursor.execute(
                                    """
                                    SELECT EXISTS (
                                        SELECT FROM information_schema.tables
                                        WHERE table_name = 'event_locations'
                                    )
                                    """
                                )
                                loc_table_exists = cursor.fetchone()[0]

                                if loc_table_exists:
                                    # Insert location associations
                                    locations = event.get("locations", [])
                                    if locations:
                                        for location in locations:
                                            cursor.execute(
                                                """
                                                INSERT INTO event_locations (event_id, location_name)
                                                VALUES (%s, %s)
                                                """,
                                                (event_id, location)
                                            )
                                else:
                                    # Create the event_locations table if it doesn't exist
                                    cursor.execute(
                                        """
                                        CREATE TABLE IF NOT EXISTS event_locations (
                                            id SERIAL PRIMARY KEY,
                                            event_id TEXT NOT NULL REFERENCES plot_events(event_id) ON DELETE CASCADE,
                                            location_name TEXT NOT NULL,
                                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                                        )
                                        """
                                    )

                                    # Insert location associations
                                    locations = event.get("locations", [])
                                    if locations:
                                        for location in locations:
                                            cursor.execute(
                                                """
                                                INSERT INTO event_locations (event_id, location_name)
                                                VALUES (%s, %s)
                                                """,
                                                (event_id, location)
                                            )
                            except Exception as e:
                                print(f"Error handling event_locations: {e}")

                # Commit all changes
                conn.commit()
                return "Plot updated successfully"

        except Exception as e:
            conn.rollback()
            print(f"Error updating plot for book_id {book_id}: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to update plot: {str(e)}")
        finally:
            conn.close()

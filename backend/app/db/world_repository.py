from .base import get_db_connection
import json
import uuid

class WorldRepository:
    """Repository for managing world building elements and AI memory data."""

    def get_world_elements(self, book_id: str, category: str = None) -> dict:
        """Fetches world elements for a book from the world_elements table.

        Args:
            book_id: The ID of the book
            category: Optional category filter

        Returns:
            A dictionary mapping categories to lists of elements
        """
        conn = get_db_connection()
        try:
            with conn.cursor() as cursor:
                # Check if attributes column exists
                cursor.execute(
                    """SELECT EXISTS (
                        SELECT FROM information_schema.columns
                        WHERE table_name = 'world_elements' AND column_name = 'attributes'
                    );"""
                )
                attributes_exists = cursor.fetchone()['exists']

                # Build the query based on whether a category is provided
                if attributes_exists:
                    if category:
                        query = "SELECT element_id, category, name, description, attributes FROM world_elements WHERE book_id = %s AND category = %s"
                        params = (book_id, category)
                    else:
                        query = "SELECT element_id, category, name, description, attributes FROM world_elements WHERE book_id = %s"
                        params = (book_id,)
                else:
                    if category:
                        query = "SELECT element_id, category, name, description FROM world_elements WHERE book_id = %s AND category = %s"
                        params = (book_id, category)
                    else:
                        query = "SELECT element_id, category, name, description FROM world_elements WHERE book_id = %s"
                        params = (book_id,)

                cursor.execute(query, params)
                elements = cursor.fetchall()

                # Organize elements by category
                result = {}
                for element in elements:
                    category = element['category']
                    if category not in result:
                        result[category] = []

                    # Parse attributes JSON if it exists and the column exists
                    attributes = {}
                    if attributes_exists and 'attributes' in element and element['attributes']:
                        try:
                            if isinstance(element['attributes'], str):
                                attributes = json.loads(element['attributes'])
                            else:
                                attributes = element['attributes']
                        except json.JSONDecodeError:
                            print(f"Error parsing attributes for element {element['element_id']}")

                    result[category].append({
                        "id": element['element_id'],
                        "name": element['name'],
                        "description": element['description'],
                        "attributes": attributes
                    })

                return result
        finally:
            conn.close()

    def get_ai_memory_data(self, book_id: str) -> dict:
        """Fetches AI memory data for a book from world_elements table."""
        conn = get_db_connection()
        try:
            # Get locations from world_elements table
            locations = []
            with conn.cursor() as cursor:
                # Check if the world_elements table exists
                cursor.execute(
                    """SELECT EXISTS (
                        SELECT FROM information_schema.tables
                        WHERE table_name = 'world_elements'
                    );"""
                )
                table_exists = cursor.fetchone()['exists']

                if table_exists:
                    # Get all elements with category 'places' or 'locations'
                    cursor.execute(
                        """SELECT element_id, name, description, attributes
                           FROM world_elements
                           WHERE book_id = %s AND (category = 'places' OR category = 'locations')""",
                        (book_id,)
                    )
                    location_elements = cursor.fetchall()

                    # Convert to the expected format
                    for element in location_elements:
                        # Parse attributes if available
                        attributes = {}
                        if 'attributes' in element and element['attributes']:
                            try:
                                if isinstance(element['attributes'], str):
                                    attributes = json.loads(element['attributes'])
                                else:
                                    attributes = element['attributes']
                            except json.JSONDecodeError:
                                print(f"Error parsing attributes for location {element['element_id']}")

                        # Add to locations list
                        locations.append({
                            "id": element['element_id'],
                            "name": element['name'],
                            "description": element['description'],
                            "attributes": attributes
                        })

                    print(f"Found {len(locations)} locations for book {book_id}")

            # Return the data in the expected format
            return {
                "setting_types": ["fantasy"],  # Default setting type
                "lists": {
                    "locations": {"items": locations}
                }
            }
        except Exception as e:
            print(f"Error fetching world data: {str(e)}")
            # Return default empty data
            return {
                "setting_types": [],
                "lists": {
                    "locations": {"items": []}
                }
            }
        finally:
            conn.close()

    def save_ai_memory_data(self, book_id: str, data: dict) -> dict:
        """Saves AI memory data for a book using world_elements table."""
        conn = get_db_connection()
        try:
            # Extract locations from the data
            locations = data.get("lists", {}).get("locations", {}).get("items", [])

            # Save each location as a world element
            for location in locations:
                if isinstance(location, dict) and location.get("name"):
                    # Check if this location already exists
                    location_id = location.get("id")

                    # Create or update the location
                    self.create_update_world_element(
                        book_id=book_id,
                        element_id=location_id,  # May be None for new locations
                        category="places",
                        name=location.get("name", ""),
                        description=location.get("description", ""),
                        attributes=location.get("attributes", {})
                    )

            # Return the updated data
            return self.get_ai_memory_data(book_id)
        except Exception as e:
            print(f"Error saving world data: {str(e)}")
            conn.rollback()
            return {
                "setting_types": data.get("setting_types", []),
                "lists": {
                    "locations": {"items": []}
                }
            }
        finally:
            conn.close()

    def get_world_data_for_ai(self, book_id: str) -> dict:
        """Combines world elements and AI memory data for AI context."""
        # Get AI memory data
        ai_data = self.get_ai_memory_data(book_id)

        # Get world elements
        elements = self.get_world_elements(book_id)

        # Combine data
        for category, items in elements.items():
            if category not in ai_data["lists"]:
                ai_data["lists"][category] = {"items": []}

            # Add elements to AI data
            for item in items:
                ai_data["lists"][category]["items"].append({
                    "name": item["name"],
                    "description": item["description"]
                })

        return ai_data

    def create_update_world_element(self, book_id: str, category: str, name: str, description: str, element_id: str = None, attributes: dict = None) -> dict:
        """Creates or updates a world element."""
        conn = get_db_connection()
        try:
            with conn.cursor() as cursor:
                if element_id:
                    # Update existing element
                    # Convert attributes to JSON string if provided
                    attributes_json = json.dumps(attributes) if attributes else '{}'

                    cursor.execute(
                        """
                        UPDATE world_elements
                        SET category = %s, name = %s, description = %s, attributes = %s
                        WHERE element_id = %s AND book_id = %s
                        RETURNING element_id
                        """,
                        (category, name, description, attributes_json, element_id, book_id)
                    )
                    result = cursor.fetchone()
                    if not result:
                        raise ValueError(f"Element {element_id} not found for book {book_id}")

                    # Commit the transaction
                    conn.commit()
                    print(f"Updated world element {element_id} for book {book_id}")

                    return {
                        "element_id": result['element_id'],
                        "category": category,
                        "name": name,
                        "description": description,
                        "attributes": attributes or {}
                    }
                else:
                    # Create new element
                    element_id = str(uuid.uuid4())

                    # Convert attributes to JSON string if provided
                    attributes_json = json.dumps(attributes) if attributes else '{}'

                    cursor.execute(
                        """
                        INSERT INTO world_elements (element_id, book_id, category, name, description, attributes)
                        VALUES (%s, %s, %s, %s, %s, %s)
                        RETURNING element_id
                        """,
                        (element_id, book_id, category, name, description, attributes_json)
                    )
                    result = cursor.fetchone()

                    # Commit the transaction
                    conn.commit()
                    print(f"Created new world element {element_id} for book {book_id}")

                    return {
                        "element_id": result['element_id'],
                        "category": category,
                        "name": name,
                        "description": description,
                        "attributes": attributes or {}
                    }
        finally:
            conn.close()

    def delete_world_element(self, book_id: str, element_id: str) -> None:
        """Deletes a world element."""
        conn = get_db_connection()
        try:
            with conn.cursor() as cursor:
                cursor.execute(
                    "DELETE FROM world_elements WHERE element_id = %s AND book_id = %s",
                    (element_id, book_id)
                )

                # Commit the transaction
                conn.commit()
                print(f"Deleted world element {element_id} for book {book_id}")
                if cursor.rowcount == 0:
                    raise ValueError(f"Element {element_id} not found for book {book_id}")
        finally:
            conn.close()

"""
Repository for managing relationship types.
"""
from .base import get_db_connection
import logging

logger = logging.getLogger(__name__)

class RelationshipTypeRepository:
    """Repository for managing relationship types."""

    def get_relationship_types(self) -> list:
        """
        Fetches all relationship types from the database.

        Returns:
            A list of relationship type dictionaries
        """
        conn = get_db_connection()
        try:
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    SELECT
                        relationship_type_id,
                        name,
                        inverse_relationship_type_id,
                        description,
                        created_at,
                        updated_at
                    FROM relationship_types
                    ORDER BY name
                    """
                )
                types = cursor.fetchall()
                
                # Process the types to convert datetime objects to strings
                processed_types = []
                for type_data in types:
                    type_dict = dict(type_data)
                    
                    # Convert datetime objects to strings
                    if 'created_at' in type_dict and type_dict['created_at']:
                        type_dict['created_at'] = type_dict['created_at'].isoformat()
                    if 'updated_at' in type_dict and type_dict['updated_at']:
                        type_dict['updated_at'] = type_dict['updated_at'].isoformat()
                    
                    processed_types.append(type_dict)
                
                return processed_types
        except Exception as e:
            logger.error(f"Error fetching relationship types: {str(e)}")
            return []
        finally:
            conn.close()

    def get_relationship_type(self, relationship_type_id: str) -> dict:
        """
        Fetches a specific relationship type by ID.

        Args:
            relationship_type_id: The ID of the relationship type

        Returns:
            A dictionary containing the relationship type data
        """
        conn = get_db_connection()
        try:
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    SELECT
                        relationship_type_id,
                        name,
                        inverse_relationship_type_id,
                        description,
                        created_at,
                        updated_at
                    FROM relationship_types
                    WHERE relationship_type_id = %s
                    """,
                    (relationship_type_id,)
                )
                type_data = cursor.fetchone()
                
                if not type_data:
                    raise ValueError(f"Relationship type with ID {relationship_type_id} not found")
                
                type_dict = dict(type_data)
                
                # Convert datetime objects to strings
                if 'created_at' in type_dict and type_dict['created_at']:
                    type_dict['created_at'] = type_dict['created_at'].isoformat()
                if 'updated_at' in type_dict and type_dict['updated_at']:
                    type_dict['updated_at'] = type_dict['updated_at'].isoformat()
                
                return type_dict
        except Exception as e:
            logger.error(f"Error fetching relationship type: {str(e)}")
            raise
        finally:
            conn.close()

    def create_relationship_type(self, type_data: dict) -> dict:
        """
        Creates a new relationship type.

        Args:
            type_data: Dictionary containing relationship type data

        Returns:
            A dictionary containing the created relationship type
        """
        conn = get_db_connection()
        try:
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    INSERT INTO relationship_types (
                        relationship_type_id,
                        name,
                        inverse_relationship_type_id,
                        description
                    )
                    VALUES (%s, %s, %s, %s)
                    RETURNING
                        relationship_type_id,
                        name,
                        inverse_relationship_type_id,
                        description,
                        created_at,
                        updated_at
                    """,
                    (
                        type_data.get('relationship_type_id'),
                        type_data.get('name'),
                        type_data.get('inverse_relationship_type_id'),
                        type_data.get('description', '')
                    )
                )
                result = cursor.fetchone()
                conn.commit()
                
                result_dict = dict(result)
                
                # Convert datetime objects to strings
                if 'created_at' in result_dict and result_dict['created_at']:
                    result_dict['created_at'] = result_dict['created_at'].isoformat()
                if 'updated_at' in result_dict and result_dict['updated_at']:
                    result_dict['updated_at'] = result_dict['updated_at'].isoformat()
                
                return result_dict
        except Exception as e:
            conn.rollback()
            logger.error(f"Error creating relationship type: {str(e)}")
            raise
        finally:
            conn.close()

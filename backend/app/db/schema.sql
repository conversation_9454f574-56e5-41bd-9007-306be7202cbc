-- Create users table
CREATE TABLE users (
    user_id TEXT PRIMARY KEY,
    email TEXT UNIQUE NOT NULL,
    password_hash TEXT NOT NULL
);

-- Create books table with security relationship to users
CREATE TABLE books (
    book_id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    title TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
);

-- Create book_memory table
CREATE TABLE book_memory (
    book_id TEXT PRIMARY KEY,
    memory JSONB DEFAULT '{
        "world": {
            "name": "",
            "description": "",
            "image_url": "",
            "setting_types": [],
            "lists": {
                "Items": {"items": [], "description": ""},
                "Places": {"items": [], "description": ""},
                "Species": {"items": [], "description": ""},
                "Systems": {"items": [], "description": ""},
                "Concepts": {"items": [], "description": ""},
                "Conflicts": {"items": [], "description": ""},
                "Societies": {"items": [], "description": ""},
                "Character Types": {"items": [], "description": ""}
            }
        },
        "plot": {
            "chapters": [],
            "bank": []
        },
        "characters": {},
        "brainstorm": {
            "pages": {}
        },
        "content": {
            "chapters": {}
        },
        "ai_context": {
            "recent_generations": [],
            "character_profiles": [],
            "world_context": {}
        }
    }'::JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (book_id) REFERENCES books(book_id) ON DELETE CASCADE
);

-- Create chapters table
CREATE TABLE chapters (
    chapter_id TEXT PRIMARY KEY,
    book_id TEXT NOT NULL,
    title TEXT NOT NULL,
    sequence_number INTEGER NOT NULL,
    content TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (book_id) REFERENCES books(book_id) ON DELETE CASCADE
);

-- Create characters table
CREATE TABLE characters (
    character_id TEXT PRIMARY KEY,
    book_id TEXT NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    age TEXT,
    role TEXT,
    race TEXT,
    headshot TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (book_id) REFERENCES books(book_id) ON DELETE CASCADE
);

-- Create character_traits table
CREATE TABLE character_traits (
    trait_id TEXT PRIMARY KEY,
    character_id TEXT NOT NULL,
    trait TEXT NOT NULL,
    FOREIGN KEY (character_id) REFERENCES characters(character_id) ON DELETE CASCADE
);

-- Create character_relationships table
CREATE TABLE character_relationships (
    relationship_id TEXT PRIMARY KEY,
    character_id TEXT NOT NULL,
    related_character_id TEXT NOT NULL,
    relationship_type TEXT NOT NULL,
    description TEXT,
    strength INTEGER DEFAULT 3, -- Default to 3 (close friend)
    FOREIGN KEY (character_id) REFERENCES characters(character_id) ON DELETE CASCADE,
    FOREIGN KEY (related_character_id) REFERENCES characters(character_id) ON DELETE CASCADE
);

-- Create world_elements table
CREATE TABLE world_elements (
    element_id TEXT PRIMARY KEY,
    book_id TEXT NOT NULL,
    category TEXT NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (book_id) REFERENCES books(book_id) ON DELETE CASCADE
);

-- Create plot_events table
CREATE TABLE plot_events (
    event_id TEXT PRIMARY KEY,
    book_id TEXT NOT NULL,
    chapter_id TEXT,
    title TEXT NOT NULL,
    description TEXT,
    sequence_number INTEGER,
    is_in_bank BOOLEAN DEFAULT FALSE,
    x FLOAT DEFAULT 0,
    y FLOAT DEFAULT 0,
    width FLOAT DEFAULT 200,
    height FLOAT DEFAULT 100,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (book_id) REFERENCES books(book_id) ON DELETE CASCADE,
    FOREIGN KEY (chapter_id) REFERENCES chapters(chapter_id) ON DELETE SET NULL
);

-- Create brainstorm_pages table
CREATE TABLE brainstorm_pages (
    page_id TEXT PRIMARY KEY,
    book_id TEXT NOT NULL,
    title TEXT NOT NULL,
    sequence_number INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (book_id) REFERENCES books(book_id) ON DELETE CASCADE
);

-- Create brainstorm_nodes table
CREATE TABLE brainstorm_nodes (
    node_id TEXT PRIMARY KEY,
    page_id TEXT NOT NULL,
    content TEXT NOT NULL,
    position_x FLOAT,
    position_y FLOAT,
    node_type TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (page_id) REFERENCES brainstorm_pages(page_id) ON DELETE CASCADE
);

-- Create brainstorm_connectors table
CREATE TABLE brainstorm_connectors (
    connector_id TEXT PRIMARY KEY,
    page_id TEXT NOT NULL,
    source_node_id TEXT NOT NULL,
    target_node_id TEXT NOT NULL,
    connector_type TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (page_id) REFERENCES brainstorm_pages(page_id) ON DELETE CASCADE,
    FOREIGN KEY (source_node_id) REFERENCES brainstorm_nodes(node_id) ON DELETE CASCADE,
    FOREIGN KEY (target_node_id) REFERENCES brainstorm_nodes(node_id) ON DELETE CASCADE
);

-- Create brainstorm_shapes table
CREATE TABLE brainstorm_shapes (
    shape_id TEXT PRIMARY KEY,
    page_id TEXT NOT NULL,
    shape_type TEXT NOT NULL,
    position_x FLOAT,
    position_y FLOAT,
    width FLOAT,
    height FLOAT,
    properties JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (page_id) REFERENCES brainstorm_pages(page_id) ON DELETE CASCADE
);

-- Create indexes for performance
CREATE INDEX idx_books_user_id ON books(user_id);
CREATE INDEX idx_chapters_book_id ON chapters(book_id);
CREATE INDEX idx_plot_events_book_id ON plot_events(book_id);
CREATE INDEX idx_plot_events_chapter_id ON plot_events(chapter_id);
CREATE INDEX idx_characters_book_id ON characters(book_id);
CREATE INDEX idx_character_traits_character_id ON character_traits(character_id);
CREATE INDEX idx_character_relationships_character_id ON character_relationships(character_id);
CREATE INDEX idx_character_relationships_related_character_id ON character_relationships(related_character_id);
CREATE INDEX idx_world_elements_book_id ON world_elements(book_id);
CREATE INDEX idx_brainstorm_pages_book_id ON brainstorm_pages(book_id);
CREATE INDEX idx_brainstorm_nodes_page_id ON brainstorm_nodes(page_id);
CREATE INDEX idx_brainstorm_connectors_page_id ON brainstorm_connectors(page_id);
CREATE INDEX idx_brainstorm_shapes_page_id ON brainstorm_shapes(page_id);
CREATE INDEX idx_book_memory_book_id ON book_memory(book_id);
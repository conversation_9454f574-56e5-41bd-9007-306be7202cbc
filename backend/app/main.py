# backend/app/main.py
from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from contextlib import asynccontextmanager
from app.db.base import init_db
from app.api.auth_endpoints import router as auth_router
# Removed import for old book_endpoints (file moved to old_files)
from app.api.character_endpoints import router as character_router
from app.api.plot_endpoints import router as plot_router
from app.api.plot_event_endpoints import router as plot_event_router
from app.api.ai_endpoints import router as ai_router
from app.api.world_endpoints import router as world_router
from app.api.world_building_endpoints import router as world_building_router
from app.api.brainstorm_endpoints import router as brainstorm_router
from app.api.chapter_endpoints import router as chapter_router
from app.api.user_preferences_endpoints import router as user_preferences_router
from app.api.template_endpoints import router as template_router
from app.api.relationship_type_endpoints import router as relationship_type_router
from app.api import ai_context_endpoints
from app.api.admin_endpoints import router as admin_router

# Import semantic search endpoints
try:
    from app.api.semantic_search_endpoints import router as semantic_search_router
    SEMANTIC_SEARCH_AVAILABLE = True
except ImportError as e:
    print(f"Semantic search endpoints not available: {str(e)}")
    SEMANTIC_SEARCH_AVAILABLE = False

# Import book import endpoints
try:
    from app.api.book_import_endpoints import router as book_import_router
    BOOK_IMPORT_AVAILABLE = True
except ImportError as e:
    print(f"Book import endpoints not available: {str(e)}")
    BOOK_IMPORT_AVAILABLE = False

# Import new endpoints that don't use book_memory
from app.api.book_endpoints_new import router as book_router_new
from fastapi.staticfiles import StaticFiles
import logging

logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Initializes the database on startup."""
    # Initialize the database
    init_db()

    # Database migrations have been removed and are now handled by a separate script

    yield

app = FastAPI(lifespan=lifespan)
app.mount("/static", StaticFiles(directory="static"), name="static")

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins for local network access
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
    expose_headers=["*"]  # Add this line to expose all headers
)

# Custom exception handler to ensure CORS headers on errors
@app.exception_handler(Exception)
async def custom_exception_handler(request: Request, exc: Exception):
    return JSONResponse(
        status_code=500,
        content={"detail": f"Internal server error: {str(exc)}"},
        headers={
            "Access-Control-Allow-Origin": request.headers.get("Origin", "http://localhost:3000"),
            "Access-Control-Allow-Credentials": "true",
            "Access-Control-Allow-Methods": "*",
            "Access-Control-Allow-Headers": "*"
        }
    )

@app.get("/")
def read_root():
    """Root endpoint for health check."""
    return {"message": "Backend is running"}

app.include_router(auth_router)

# Switched to the new book endpoints that don't use book_memory
# Old endpoints are commented out
# app.include_router(book_router)
app.include_router(book_router_new)

app.include_router(character_router)
app.include_router(plot_router)
app.include_router(plot_event_router)
app.include_router(ai_router)
app.include_router(world_router)
app.include_router(world_building_router)
app.include_router(brainstorm_router)
app.include_router(chapter_router)

# Include the new AI context endpoints
app.include_router(ai_context_endpoints.router)

# Include the user preferences endpoints
app.include_router(user_preferences_router)

# Include the template management endpoints
app.include_router(template_router)
app.include_router(relationship_type_router)

# Include the admin endpoints
app.include_router(admin_router)

# Include the semantic search endpoints if available
if SEMANTIC_SEARCH_AVAILABLE:
    app.include_router(semantic_search_router)
    logger.info("Semantic search endpoints included in the API")
else:
    logger.warning("Semantic search endpoints not included in the API")

# Include the book import endpoints if available
if BOOK_IMPORT_AVAILABLE:
    app.include_router(book_import_router)
    logger.info("Book import endpoints included in the API")
else:
    logger.warning("Book import endpoints not included in the API")

# Memory endpoints are now commented out as we're migrating away from book_memory
# app.include_router(memory_endpoints.router)

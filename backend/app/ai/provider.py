from abc import ABC, abstractmethod
import httpx
import logging
import re
from typing import Optional, Dict
from ..config import XAI_API_KEY, XAI_IMG_API_KEY, API_BASE_URL
from ..db.base import get_db_connection

logger = logging.getLogger(__name__)

# Global dictionaries to store user preferences
_user_image_styles: Dict[str, str] = {}
_user_image_models: Dict[str, str] = {}
_user_image_qualities: Dict[str, str] = {}

def set_user_image_style(user_id: str, style: str) -> None:
    """Store a user's image style preference in memory"""
    if user_id:
        _user_image_styles[user_id] = style
        logger.debug(f"Stored image style '{style}' for user {user_id}")

def get_user_image_style(user_id: str) -> str:
    """Get a user's image style preference from memory"""
    if not user_id or user_id not in _user_image_styles:
        return "default"
    return _user_image_styles.get(user_id, "default")

def set_user_image_model(user_id: str, model: str) -> None:
    """Store a user's image model preference in memory"""
    if user_id:
        _user_image_models[user_id] = model
        logger.debug(f"Stored image model '{model}' for user {user_id}")

def get_user_image_model(user_id: str) -> str:
    """Get a user's image model preference from memory"""
    if not user_id or user_id not in _user_image_models:
        return "dall-e-3"
    return _user_image_models.get(user_id, "dall-e-3")

def set_user_image_quality(user_id: str, quality: str) -> None:
    """Store a user's image quality preference in memory"""
    if user_id:
        _user_image_qualities[user_id] = quality
        logger.debug(f"Stored image quality '{quality}' for user {user_id}")

def get_user_image_quality(user_id: str) -> str:
    """Get a user's image quality preference from memory"""
    if not user_id or user_id not in _user_image_qualities:
        return "standard"
    return _user_image_qualities.get(user_id, "standard")

class TextGenerationProvider(ABC):
    """Abstract base class for text generation providers"""

    @abstractmethod
    async def generate_text(self, prompt: str) -> str:
        """Generate text based on a prompt"""
        pass

    @property
    @abstractmethod
    def provider_name(self) -> str:
        """Get the name of the provider"""
        pass

class ImageGenerationProvider(ABC):
    """Abstract base class for image generation providers"""

    @abstractmethod
    async def generate_image(self, prompt: str, image_path: Optional[str] = None) -> str:
        """
        Generate an image URL based on a prompt

        Args:
            prompt: The text prompt for image generation
            image_path: Optional path to save the image locally

        Returns:
            str: URL to the generated image
        """
        pass

    @property
    @abstractmethod
    def provider_name(self) -> str:
        """Get the name of the provider"""
        pass

# Legacy AIProvider for backward compatibility
class AIProvider(ABC):
    @abstractmethod
    async def generate_text(self, prompt: str) -> str:
        """Generate text based on a prompt"""
        pass

    @abstractmethod
    async def generate_image(self, prompt: str, image_path: Optional[str] = None) -> str:
        """
        Generate an image based on a prompt

        Args:
            prompt: The text prompt for image generation
            image_path: Optional path to save the image locally

        Returns:
            str: URL to the generated image
        """
        pass

# Grok Text Generation Provider
class GrokTextProvider(TextGenerationProvider):
    """Implementation of TextGenerationProvider using Grok API"""

    @property
    def provider_name(self) -> str:
        return "grok"

    async def generate_text(self, prompt: str) -> str:
        """Generate text using Grok API"""
        try:
            logger.debug(f"Generating text with Grok API. Prompt length: {len(prompt)}")

            async with httpx.AsyncClient(timeout=60.0) as client:
                headers = {"Authorization": f"Bearer {XAI_API_KEY}", "Content-Type": "application/json"}
                payload = {
                    "model": "grok-2-1212",
                    "messages": [
                        {"role": "system", "content": "You are a creative writing assistant that generates structured content for authors. Return only valid JSON as specified in the prompt."},
                        {"role": "user", "content": prompt}
                    ],
                    "max_tokens": 16000,
                    "temperature": 0.7
                }

                logger.debug(f"Sending request to Grok API for text generation")
                response = await client.post(f"{API_BASE_URL}/chat/completions", headers=headers, json=payload)
                response.raise_for_status()
                data = response.json()

                # Extract the response text
                choices = data.get("choices", [])
                if choices and isinstance(choices, list) and len(choices) > 0:
                    ai_text = choices[0].get("message", {}).get("content", "")
                else:
                    ai_text = ""
                logger.debug(f"Received response from Grok API: {len(ai_text)} characters")

                return ai_text
        except Exception as e:
            logger.error(f"Error generating text with Grok API: {str(e)}")
            raise

# OpenAI Text Generation Provider
class OpenAITextProvider(TextGenerationProvider):
    """Implementation of TextGenerationProvider using OpenAI API"""

    @property
    def provider_name(self) -> str:
        return "openai"

    async def generate_text(self, prompt: str) -> str:
        """Generate text using OpenAI API"""
        try:
            from ..config import OPENAI_API_KEY, OPENAI_API_BASE_URL

            if not OPENAI_API_KEY or OPENAI_API_KEY == "sk-your-openai-api-key":
                logger.error("OpenAI API key not configured or using placeholder value")
                return "OpenAI API key not configured. Please add your API key to the backend/.env file."

            logger.debug(f"Generating text with OpenAI API. Prompt length: {len(prompt)}")

            # Extract system message if present
            system_message = "You are a creative writing assistant that generates structured content for authors. Return only valid JSON as specified in the prompt."
            user_prompt = prompt

            # Check if the prompt contains a system message section
            if "Context:" in prompt and "\n\n" in prompt:
                parts = prompt.split("\n\n", 1)
                if len(parts) == 2 and parts[0].startswith("Context:"):
                    system_message = parts[0]
                    user_prompt = parts[1]

            async with httpx.AsyncClient(timeout=60.0) as client:
                headers = {"Authorization": f"Bearer {OPENAI_API_KEY}", "Content-Type": "application/json"}
                payload = {
                    "model": "gpt-4o",  # Use GPT-4o for high-quality responses
                    "messages": [
                        {"role": "system", "content": system_message},
                        {"role": "user", "content": user_prompt}
                    ],
                    "max_tokens": 16000,
                    "temperature": 0.7
                }

                logger.debug(f"Sending request to OpenAI API for text generation")
                response = await client.post(f"{OPENAI_API_BASE_URL}/chat/completions", headers=headers, json=payload)
                response.raise_for_status()
                data = response.json()

                # Extract the response text
                choices = data.get("choices", [])
                if choices and isinstance(choices, list) and len(choices) > 0:
                    ai_text = choices[0].get("message", {}).get("content", "")
                else:
                    ai_text = ""
                logger.debug(f"Received response from OpenAI API: {len(ai_text)} characters")

                return ai_text
        except Exception as e:
            logger.error(f"Error generating text with OpenAI API: {str(e)}")
            raise

# Anthropic Text Generation Provider
class AnthropicTextProvider(TextGenerationProvider):
    """Implementation of TextGenerationProvider using Anthropic API"""

    @property
    def provider_name(self) -> str:
        return "anthropic"

    async def generate_text(self, prompt: str) -> str:
        """Generate text using Anthropic API"""
        try:
            from ..config import ANTHROPIC_API_KEY, ANTHROPIC_API_BASE_URL

            if not ANTHROPIC_API_KEY or ANTHROPIC_API_KEY == "sk-ant-your-anthropic-api-key":
                logger.error("Anthropic API key not configured or using placeholder value")
                return "Anthropic API key not configured. Please add your API key to the backend/.env file."

            logger.debug(f"Generating text with Anthropic API. Prompt length: {len(prompt)}")

            # Extract system message if present
            system_message = "You are a creative writing assistant that generates structured content for authors. Return only valid JSON as specified in the prompt."
            user_prompt = prompt

            # Check if the prompt contains a system message section
            if "Context:" in prompt and "\n\n" in prompt:
                parts = prompt.split("\n\n", 1)
                if len(parts) == 2 and parts[0].startswith("Context:"):
                    system_message = parts[0]
                    user_prompt = parts[1]

            async with httpx.AsyncClient(timeout=60.0) as client:
                headers = {
                    "x-api-key": ANTHROPIC_API_KEY,
                    "anthropic-version": "2023-06-01",
                    "Content-Type": "application/json"
                }

                payload = {
                    "model": "claude-3-opus-20240229",  # Use Claude 3 Opus for high-quality responses
                    "max_tokens": 16000,
                    "temperature": 0.7,
                    "system": system_message,
                    "messages": [
                        {"role": "user", "content": user_prompt}
                    ]
                }

                logger.debug(f"Sending request to Anthropic API for text generation")
                response = await client.post(f"{ANTHROPIC_API_BASE_URL}/messages", headers=headers, json=payload)
                response.raise_for_status()
                data = response.json()

                # Extract the response text
                content = data.get("content", [])
                if content and isinstance(content, list) and len(content) > 0:
                    ai_text = content[0].get("text", "")
                else:
                    ai_text = ""
                logger.debug(f"Received response from Anthropic API: {len(ai_text)} characters")

                return ai_text
        except Exception as e:
            logger.error(f"Error generating text with Anthropic API: {str(e)}")
            raise

# Grok Image Generation Provider
class GrokImageProvider(ImageGenerationProvider):
    """Implementation of ImageGenerationProvider using Grok API"""

    @property
    def provider_name(self) -> str:
        return "grok"

    async def generate_image(self, prompt: str, image_path: Optional[str] = None) -> str:
        """
        Generate an image URL using Grok API

        Args:
            prompt: The text prompt for image generation
            image_path: Optional path to save the image locally

        Returns:
            str: URL to the generated image
        """
        try:
            # Log the prompt for debugging, but truncate it if it's too long
            truncated_prompt = prompt[:200] + "..." if len(prompt) > 200 else prompt
            logger.debug(f"Generating image with Grok API. Prompt (truncated): {truncated_prompt}")

            # Log the full prompt for debugging
            logger.debug(f"GROK FULL PROMPT: {prompt}")

            async with httpx.AsyncClient(timeout=30.0) as client:
                headers = {"Authorization": f"Bearer {XAI_IMG_API_KEY}", "Content-Type": "application/json"}
                payload = {
                    "prompt": prompt,
                    "model": "grok-2-image-1212",
                    "n": 1
                }

                logger.debug(f"Sending request to Grok API for image generation")
                response = await client.post(f"{API_BASE_URL}/images/generations", headers=headers, json=payload)
                response.raise_for_status()
                data = response.json()

                # Extract the image URL
                image_url = data.get("data", [{}])[0].get("url")
                logger.debug(f"Received image URL from Grok API: {image_url}")

                # If image_path is provided, download and save the image
                if image_path:
                    try:
                        # Download the image
                        async with httpx.AsyncClient(timeout=60.0) as img_client:
                            img_response = await img_client.get(image_url)
                            img_response.raise_for_status()

                            # Save the image to the specified path
                            with open(image_path, "wb") as f:
                                f.write(img_response.content)

                            logger.debug(f"Image saved to {image_path}")

                            # Return a local URL path instead of the remote URL
                            if image_path.startswith("static/"):
                                return f"/{image_path}"
                    except Exception as save_error:
                        logger.error(f"Error saving image to {image_path}: {str(save_error)}")
                        # Continue and return the remote URL if saving fails

                return image_url
        except Exception as e:
            logger.error(f"Error generating image with Grok API: {str(e)}")
            raise

# OpenAI DALL-E 3 Image Generation Provider
class OpenAIImageProvider(ImageGenerationProvider):
    """Implementation of ImageGenerationProvider using OpenAI DALL-E 3 API"""

    @property
    def provider_name(self) -> str:
        return "openai"

    async def generate_image(self, prompt: str, image_path: Optional[str] = None) -> str:
        """
        Generate an image URL using OpenAI DALL-E 3 API

        Args:
            prompt: The text prompt for image generation
            image_path: Optional path to save the image locally

        Returns:
            str: URL to the generated image
        """
        try:
            from ..config import OPENAI_API_KEY, OPENAI_API_BASE_URL

            if not OPENAI_API_KEY or OPENAI_API_KEY == "sk-your-openai-api-key":
                logger.error("OpenAI API key not configured or using placeholder value")
                placeholder_url = "https://via.placeholder.com/800x400?text=OpenAI+API+Key+Not+Configured"

                # If image_path is provided, create a placeholder image
                if image_path:
                    try:
                        # Create a simple placeholder file
                        with open(image_path, "w") as f:
                            f.write("OpenAI API key not configured")

                        # Return a local URL path
                        if image_path.startswith("static/"):
                            return f"/{image_path}"
                    except Exception as save_error:
                        logger.error(f"Error creating placeholder at {image_path}: {str(save_error)}")

                return placeholder_url

            # Get the user ID from the image_path if available
            user_id = None
            if image_path and "/user_" in image_path:
                user_id_match = re.search(r"/user_([^/]+)/", image_path)
                if user_id_match:
                    user_id = user_id_match.group(1)

            # Get the quality preference for the user
            quality = get_user_image_quality(user_id) if user_id else "standard"
            # For DALL-E 3, only "standard" and "hd" are valid
            if quality not in ["standard", "hd"]:
                quality = "standard"

            # Log the prompt for debugging, but truncate it if it's too long
            truncated_prompt = prompt[:200] + "..." if len(prompt) > 200 else prompt
            logger.debug(f"Generating image with OpenAI DALL-E 3 API. Prompt: {truncated_prompt}, Quality: {quality}")

            async with httpx.AsyncClient(timeout=60.0) as client:
                headers = {"Authorization": f"Bearer {OPENAI_API_KEY}", "Content-Type": "application/json"}
                payload = {
                    "model": "dall-e-3",
                    "prompt": prompt,
                    "n": 1,
                    "size": "1024x1024",
                    "quality": quality
                }

                logger.debug(f"Sending request to OpenAI API for image generation")
                response = await client.post(f"{OPENAI_API_BASE_URL}/images/generations", headers=headers, json=payload)
                response.raise_for_status()
                data = response.json()

                # Extract the image URL
                image_url = data.get("data", [{}])[0].get("url", "")
                logger.debug(f"Received image URL from OpenAI API: {image_url}")

                if not image_url:
                    logger.error("No image URL returned from OpenAI API")
                    placeholder_url = "https://via.placeholder.com/800x400?text=OpenAI+Image+Generation+Failed"

                    # If image_path is provided, create a placeholder image
                    if image_path:
                        try:
                            # Create a simple placeholder file
                            with open(image_path, "w") as f:
                                f.write("OpenAI image generation failed")

                            # Return a local URL path
                            if image_path.startswith("static/"):
                                return f"/{image_path}"
                        except Exception as save_error:
                            logger.error(f"Error creating placeholder at {image_path}: {str(save_error)}")

                    return placeholder_url

                # If image_path is provided, download and save the image
                if image_path:
                    try:
                        # Download the image
                        async with httpx.AsyncClient(timeout=60.0) as img_client:
                            img_response = await img_client.get(image_url)
                            img_response.raise_for_status()

                            # Save the image to the specified path
                            with open(image_path, "wb") as f:
                                f.write(img_response.content)

                            logger.debug(f"Image saved to {image_path}")

                            # Return a local URL path instead of the remote URL
                            if image_path.startswith("static/"):
                                return f"/{image_path}"
                    except Exception as save_error:
                        logger.error(f"Error saving image to {image_path}: {str(save_error)}")
                        # Continue and return the remote URL if saving fails

                return image_url
        except Exception as e:
            logger.error(f"Error generating image with OpenAI DALL-E 3 API: {str(e)}")
            # Return a placeholder image with the error message
            error_text = str(e).replace(" ", "+")[:100]
            placeholder_url = f"https://via.placeholder.com/800x400?text=Error:+{error_text}"

            # If image_path is provided, create a placeholder image
            if image_path:
                try:
                    # Create a simple placeholder file
                    with open(image_path, "w") as f:
                        f.write(f"Error generating image: {str(e)}")

                    # Return a local URL path
                    if image_path.startswith("static/"):
                        return f"/{image_path}"
                except Exception as save_error:
                    logger.error(f"Error creating placeholder at {image_path}: {str(save_error)}")

            return placeholder_url

# OpenAI GPT Image Generation Provider
class OpenAIGPTImageProvider(ImageGenerationProvider):
    """Implementation of ImageGenerationProvider using OpenAI GPT Image 1 API"""

    @property
    def provider_name(self) -> str:
        return "openai_gpt"

    async def generate_image(self, prompt: str, image_path: Optional[str] = None) -> str:
        """
        Generate an image using OpenAI GPT Image 1 API

        Args:
            prompt: The text prompt for image generation
            image_path: Optional path to save the image locally

        Returns:
            str: URL to the generated image
        """
        try:
            from ..config import OPENAI_API_KEY, OPENAI_API_BASE_URL

            if not OPENAI_API_KEY or OPENAI_API_KEY == "sk-your-openai-api-key":
                logger.error("OpenAI API key not configured or using placeholder value")
                placeholder_url = "https://via.placeholder.com/800x400?text=OpenAI+API+Key+Not+Configured"

                # If image_path is provided, create a placeholder image
                if image_path:
                    try:
                        # Create a simple placeholder file
                        with open(image_path, "w") as f:
                            f.write("OpenAI API key not configured")

                        # Return a local URL path
                        if image_path.startswith("static/"):
                            return f"/{image_path}"
                    except Exception as save_error:
                        logger.error(f"Error creating placeholder at {image_path}: {str(save_error)}")

                return placeholder_url

            # Get the user ID from the image_path if available
            user_id = None
            if image_path and "/user_" in image_path:
                user_id_match = re.search(r"/user_([^/]+)/", image_path)
                if user_id_match:
                    user_id = user_id_match.group(1)

            # Get the quality preference for the user
            quality = get_user_image_quality(user_id) if user_id else "high"

            # For GPT Image 1, only "high", "medium", and "low" are valid
            if quality not in ["high", "medium", "low"]:
                quality = "high"

            # Log the prompt for debugging, but truncate it if it's too long
            truncated_prompt = prompt[:200] + "..." if len(prompt) > 200 else prompt
            logger.debug(f"Generating image with OpenAI GPT Image 1 API. Prompt: {truncated_prompt}, Quality: {quality}")

            async with httpx.AsyncClient(timeout=60.0) as client:
                headers = {"Authorization": f"Bearer {OPENAI_API_KEY}", "Content-Type": "application/json"}

                # Use GPT Image 1 with the correct parameters
                payload = {
                    "model": "gpt-image-1",
                    "prompt": prompt,
                    "n": 1,
                    "size": "1024x1024",
                    "quality": quality
                }

                logger.debug(f"Sending request to OpenAI API for GPT Image 1 generation: {payload}")

                try:
                    response = await client.post(f"{OPENAI_API_BASE_URL}/images/generations", headers=headers, json=payload)
                    response.raise_for_status()
                    data = response.json()

                    # Log the full response for debugging
                    logger.debug(f"Full response from OpenAI GPT Image 1 API: {data}")

                    # Get the first data item
                    image_data = data.get("data", [{}])[0]

                    # Check if we have base64 data (GPT Image 1 always returns base64)
                    if "b64_json" in image_data:
                        logger.debug("Received base64 encoded image from OpenAI GPT Image 1 API")

                        # If image_path is provided, save the base64 data to a file
                        if image_path:
                            try:
                                import base64

                                # Decode the base64 data
                                image_bytes = base64.b64decode(image_data["b64_json"])

                                # Save the image to the specified path
                                with open(image_path, "wb") as f:
                                    f.write(image_bytes)

                                logger.debug(f"Image saved to {image_path} from base64 data")

                                # Return a local URL path
                                if image_path.startswith("static/"):
                                    return f"/{image_path}"
                            except Exception as save_error:
                                logger.error(f"Error saving base64 image to {image_path}: {str(save_error)}")

                        # If we don't have an image_path or saving failed, create a data URL
                        return f"data:image/png;base64,{image_data['b64_json']}"

                    # If no base64 data, try to get a URL (fallback)
                    image_url = image_data.get("url", "")
                    logger.debug(f"Received image URL from OpenAI GPT Image 1 API: {image_url}")

                    if not image_url:
                        raise Exception("No image data returned from OpenAI API")

                    # If image_path is provided, download and save the image
                    if image_path and image_url:
                        try:
                            # Download the image
                            async with httpx.AsyncClient(timeout=60.0) as img_client:
                                img_response = await img_client.get(image_url)
                                img_response.raise_for_status()

                                # Save the image to the specified path
                                with open(image_path, "wb") as f:
                                    f.write(img_response.content)

                                logger.debug(f"Image saved to {image_path}")

                                # Return a local URL path instead of the remote URL
                                if image_path.startswith("static/"):
                                    return f"/{image_path}"
                        except Exception as save_error:
                            logger.error(f"Error saving image to {image_path}: {str(save_error)}")
                            # Continue and return the remote URL if saving fails

                    return image_url

                except Exception as api_error:
                    logger.error(f"Error calling OpenAI GPT Image 1 API: {str(api_error)}")

                    # Create a placeholder image
                    if image_path:
                        try:
                            # Try to use PIL to create a simple placeholder image
                            from PIL import Image, ImageDraw, ImageFont

                            # Create a new image with a gray background
                            img = Image.new('RGB', (400, 400), color=(108, 117, 125))
                            draw = ImageDraw.Draw(img)

                            # Try to use a system font, fall back to default if not available
                            try:
                                font = ImageFont.truetype("Arial", 20)
                            except IOError:
                                font = ImageFont.load_default()

                            # Draw the error message
                            error_msg = f"GPT Image 1 API Error: {str(api_error)[:50]}..."
                            text_width, text_height = draw.textbbox((0, 0), error_msg, font=font)[2:4]
                            position = ((400 - text_width) // 2, (400 - text_height) // 2)

                            # Draw the text in white
                            draw.text(position, error_msg, font=font, fill=(255, 255, 255))

                            # Save the image
                            img.save(image_path, 'JPEG')

                            logger.info(f"Created local placeholder image at {image_path}")

                            # Return the local URL
                            if image_path.startswith("static/"):
                                return f"/{image_path}"
                        except Exception as img_error:
                            # If PIL fails, create an extremely simple placeholder file
                            logger.warning(f"Failed to create PIL image: {str(img_error)}. Creating simple file instead.")
                            with open(image_path, "w") as f:
                                f.write(f"GPT Image 1 API Error: {str(api_error)}")

                            # Return the local URL
                            if image_path.startswith("static/"):
                                return f"/{image_path}"

                    # If we couldn't create a local image, return a placeholder URL
                    error_text = str(api_error).replace(" ", "+")[:100]
                    return f"https://via.placeholder.com/800x400?text=GPT+Image+1+API+Error:+{error_text}"

        except Exception as e:
            logger.error(f"Error generating image with OpenAI GPT Image 1 API: {str(e)}")

            # Return a placeholder image with the error message
            if image_path:
                try:
                    # Create a simple placeholder file
                    with open(image_path, "w") as f:
                        f.write(f"Error generating image with GPT Image 1: {str(e)}")

                    # Return a local URL path
                    if image_path.startswith("static/"):
                        return f"/{image_path}"
                except Exception as save_error:
                    logger.error(f"Error creating placeholder at {image_path}: {str(save_error)}")

            # If we couldn't create a local image, return a placeholder URL
            error_text = str(e).replace(" ", "+")[:100]
            return f"https://via.placeholder.com/800x400?text=GPT+Image+1+Error:+{error_text}"

# Legacy GrokAIProvider for backward compatibility
class GrokAIProvider(AIProvider):
    """Implementation of AIProvider using Grok API"""

    async def generate_text(self, prompt: str) -> str:
        """Generate text using Grok API"""
        text_provider = GrokTextProvider()
        return await text_provider.generate_text(prompt)

    async def generate_image(self, prompt: str, image_path: Optional[str] = None) -> str:
        """Generate an image URL using Grok API"""
        image_provider = GrokImageProvider()
        return await image_provider.generate_image(prompt, image_path)

# Factory functions to get providers
async def get_text_provider(user_id: Optional[str] = None) -> TextGenerationProvider:
    """
    Factory function to get the configured text generation provider for a user

    Args:
        user_id: Optional user ID to get user-specific provider preferences

    Returns:
        TextGenerationProvider: The configured text generation provider
    """
    if user_id:
        # Try to get user preference from database
        try:
            conn = get_db_connection()
            with conn.cursor() as cursor:
                cursor.execute(
                    "SELECT text_provider FROM user_preferences WHERE user_id = %s",
                    (user_id,)
                )
                result = cursor.fetchone()
                if result and result.get('text_provider'):
                    provider_name = result.get('text_provider')
                    logger.debug(f"Using user-configured text provider: {provider_name}")

                    if provider_name == "openai":
                        return OpenAITextProvider()
                    elif provider_name == "anthropic":
                        return AnthropicTextProvider()
        except Exception as e:
            logger.error(f"Error getting user text provider preference: {str(e)}")
        finally:
            if 'conn' in locals():
                conn.close()

    # Default to Grok
    return GrokTextProvider()

async def get_image_provider(user_id: Optional[str] = None) -> ImageGenerationProvider:
    """
    Factory function to get the configured image generation provider for a user

    Args:
        user_id: Optional user ID to get user-specific provider preferences

    Returns:
        ImageGenerationProvider: The configured image generation provider
    """
    # Default values
    image_style = "default"
    image_model = "dall-e-3"
    image_quality = "standard"

    if user_id:
        # Try to get user preference from database
        try:
            conn = get_db_connection()
            with conn.cursor() as cursor:
                # First check if the columns exist
                try:
                    # Try to get all preferences
                    cursor.execute(
                        "SELECT image_provider, image_style, image_model, image_quality FROM user_preferences WHERE user_id = %s",
                        (user_id,)
                    )
                    result = cursor.fetchone()
                    if result:
                        provider_name = result.get('image_provider')
                        image_style = result.get('image_style', 'default')
                        image_model = result.get('image_model', 'dall-e-3')
                        image_quality = result.get('image_quality', 'standard')

                        logger.debug(f"Using user-configured image provider: {provider_name}, style: {image_style}, model: {image_model}, quality: {image_quality}")

                        # Store the preferences in global variables for access by other functions
                        set_user_image_style(user_id, image_style)
                        set_user_image_model(user_id, image_model)
                        set_user_image_quality(user_id, image_quality)

                        if provider_name == "openai":
                            return OpenAIImageProvider()
                        elif provider_name == "openai_gpt":
                            return OpenAIGPTImageProvider()
                except Exception as column_error:
                    # If the query fails, it might be because the columns don't exist
                    logger.warning(f"Error getting all preferences, trying with just image_provider: {str(column_error)}")

                    # Try to get just the image_provider
                    cursor.execute(
                        "SELECT image_provider FROM user_preferences WHERE user_id = %s",
                        (user_id,)
                    )
                    result = cursor.fetchone()
                    if result:
                        provider_name = result.get('image_provider')

                        logger.debug(f"Using user-configured image provider: {provider_name} (default style, model, and quality)")

                        # Store default preferences in global variables
                        set_user_image_style(user_id, image_style)
                        set_user_image_model(user_id, image_model)
                        set_user_image_quality(user_id, image_quality)

                        if provider_name == "openai":
                            return OpenAIImageProvider()
                        elif provider_name == "openai_gpt":
                            return OpenAIGPTImageProvider()
        except Exception as e:
            logger.error(f"Error getting user image provider preference: {str(e)}")
        finally:
            if 'conn' in locals():
                conn.close()

    # Default to Grok
    return GrokImageProvider()

# Legacy function for backward compatibility
def get_ai_provider() -> AIProvider:
    """Factory function to get the configured AI provider"""
    return GrokAIProvider()
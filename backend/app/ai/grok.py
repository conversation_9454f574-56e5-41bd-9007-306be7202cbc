import httpx
import os
from typing import Optional
from app.ai.provider import AIProvider
from app.config import XAI_API_KEY, API_BASE_URL, XAI_IMG_API_KEY

class GrokProvider(AIProvider):
    async def generate_text(self, prompt: str) -> str:
        """Generate text based on a prompt"""
        return await self.generateResponse(prompt, "")

    async def generate_image(self, prompt: str, image_path: Optional[str] = None) -> str:
        """Generate an image based on a prompt"""
        response = await self.generateResponse(f"Generate an image: {prompt}", "")

        # Extract the URL from the response
        import json
        try:
            data = json.loads(response)
            image_url = data.get("headshot", "")

            # If image_path is provided, download and save the image
            if image_path and image_url:
                try:
                    # Download the image
                    async with httpx.AsyncClient(timeout=60.0) as client:
                        img_response = await client.get(image_url)
                        img_response.raise_for_status()

                        # Save the image to the specified path
                        with open(image_path, "wb") as f:
                            f.write(img_response.content)

                        # Return a local URL path instead of the remote URL
                        if image_path.startswith("static/"):
                            return f"/{image_path}"
                except Exception as e:
                    print(f"Error saving image to {image_path}: {str(e)}")

            return image_url
        except Exception as e:
            print(f"Error parsing image response: {str(e)}")
            return ""

    async def generateResponse(self, prompt: str, context: str) -> str:
        print(f"Using XAI_IMG_API_KEY: {XAI_IMG_API_KEY}")  # Debug print
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                if "Generate an image" in prompt or "headshot URL" in prompt:
                    response = await client.post(
                        f"{API_BASE_URL}/images/generations",
                        headers={"Authorization": f"Bearer {XAI_IMG_API_KEY}"},
                        json={
                            "prompt": prompt,
                            "model": "grok-2-image-1212",
                            "n": 1
                        }
                    )
                else:
                    response = await client.post(
                        f"{API_BASE_URL}/chat/completions",
                        headers={"Authorization": f"Bearer {XAI_API_KEY}"},
                        json={
                            "model": "grok-2-1212",
                            "messages": [
                                {
                                    "role": "system",
                                    "content": f"Context: {context}\n" +
                                               "If the prompt asks for a character, respond with a single JSON object like:\n" +
                                               '{"name": "Character Name", "description": "A short backstory", "traits": ["trait1", "trait2"]}\n' +
                                               "Otherwise, respond with 3 event suggestions in this JSON format:\n" +
                                               '{"suggestions": [{"text": "Event 1"}, {"text": "Event 2"}, {"text": "Event 3"}]}\n' +
                                               "Keep responses concise."
                                },
                                {"role": "user", "content": prompt}
                            ],
                            "max_tokens": 500
                        }
                    )
                response.raise_for_status()
                data = response.json()
                if "choices" in data and data["choices"]:
                    return data["choices"][0]["message"]["content"]
                elif "data" in data and data["data"]:
                    return f'{{"headshot": "{data["data"][0]["url"]}"}}'
                else:
                    raise ValueError(f"Invalid response format: {data}")
        except httpx.HTTPStatusError as e:
            raise Exception(f"Grok API error: {e.response.status_code} - {e.response.text}")
        except httpx.RequestError as e:
            raise Exception(f"Network error: {str(e)}")
        except Exception as e:
            raise Exception(f"Unexpected error: {str(e)}")
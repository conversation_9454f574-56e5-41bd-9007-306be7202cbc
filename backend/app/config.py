# backend/app/config.py
import os
from dotenv import load_dotenv
from pathlib import Path

env_path = Path(__file__).parent.parent / ".env"
load_dotenv(dotenv_path=env_path)

# Grok API keys
XAI_API_KEY = os.getenv("XAI_API_KEY")
XAI_IMG_API_KEY = os.getenv("XAI_IMG_API_KEY", XAI_API_KEY)  # Fall back to XAI_API_KEY if not set
API_BASE_URL = os.getenv("XAI_API_BASE_URL", "https://api.x.ai/v1")

# OpenAI API keys
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
OPENAI_API_BASE_URL = os.getenv("OPENAI_API_BASE_URL", "https://api.openai.com/v1")

# Anthropic API keys
ANTHROPIC_API_KEY = os.getenv("ANTHROPIC_API_KEY")
ANTHROPIC_API_BASE_URL = os.getenv("ANTHROPIC_API_BASE_URL", "https://api.anthropic.com/v1")

# Authentication
SECRET_KEY = os.getenv("SECRET_KEY")

#print(f"Loaded XAI_API_KEY: {XAI_API_KEY}")
#print(f"Loaded XAI_IMG_API_KEY: {XAI_IMG_API_KEY}")
#print(f"Loaded SECRET_KEY: {SECRET_KEY}")  # Debug print
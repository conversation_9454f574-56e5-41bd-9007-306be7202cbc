-- Add aspect_type column to world_aspect_categories table
ALTER TABLE world_aspect_categories ADD COLUMN IF NOT EXISTS aspect_type TEXT DEFAULT 'physical';

-- Update existing categories with appropriate aspect_type values
UPDATE world_aspect_categories 
SET aspect_type = 
    CASE 
        WHEN category_id LIKE 'cat_cosmology_physical%' THEN 'physical'
        WHEN category_id LIKE 'cat_cultural_social%' THEN 'social'
        WHEN category_id LIKE 'cat_economic_material%' THEN 'economic'
        WHEN category_id LIKE 'cat_knowledge_technology%' THEN 'technological'
        WHEN category_id LIKE 'cat_temporal_historical%' THEN 'temporal'
        WHEN category_id LIKE 'cat_magical_supernatural%' THEN 'metaphysical'
        WHEN category_id LIKE 'cat_interactive_game%' THEN 'interactive'
        ELSE 'physical'
    END;

# Core dependencies
fastapi==0.115.0
uvicorn==0.30.6
httpx==0.27.2
pydantic==2.9.2
python-dotenv==1.0.1
anyio==4.9.0
starlette==0.46.1
h11==0.14.0

# Authentication and security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
bcrypt==4.1.2
cryptography==44.0.2
ecdsa==0.19.1
pyasn1==0.4.8
rsa==4.9

# Database
psycopg2-binary==2.9.10
SQLAlchemy==2.0.40

# FastAPI extensions
pydantic-settings==2.8.1
orjson==3.10.16
jinja2==3.1.3
annotated-types==0.7.0
pydantic_core==2.23.4

# AI and LLM dependencies
langchain==0.3.22
langchain-core==0.3.50
langchain-community==0.3.20
langchain-openai==0.3.12
langgraph==0.3.25
openai==1.70.0
tiktoken==0.9.0
tenacity==9.1.0
regex==2024.11.6

# Image processing
pillow==11.2.1

# HTTP and networking
aiohttp==3.11.16
aiosignal==1.3.2
certifi==2025.1.31
charset-normalizer==3.4.1
frozenlist==1.5.0
idna==3.10
multidict==6.3.2
requests==2.32.3
urllib3==2.3.0
yarl==1.18.3

# Utilities
tqdm==4.67.1
typing_extensions==4.12.2
sniffio==1.3.1
packaging==24.2

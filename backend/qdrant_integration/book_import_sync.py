"""
Book Import Synchronization for PostgreSQL and Qdrant

This module handles the synchronization of book data during import operations,
ensuring consistency between PostgreSQL and Qdrant.
"""

import logging
import asyncio
from typing import Dict, List, Optional, Union, Any
from datetime import datetime

from .enhanced_collection_manager import EnhancedCollectionManager
from .sync_transaction import SyncTransaction

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class BookImportSync:
    """Synchronization for book import operations"""
    
    def __init__(self, db_config: Dict[str, str]):
        """
        Initialize the book import synchronization
        
        Args:
            db_config: PostgreSQL database configuration
        """
        self.db_config = db_config
        self.sync_transaction = SyncTransaction(db_config)
        self.collection_manager = EnhancedCollectionManager()
    
    async def import_book(self, book_id: int, user_id: int) -> Dict[str, Any]:
        """
        Import a book's data into Qdrant
        
        Args:
            book_id: ID of the book to import
            user_id: ID of the user importing the book
            
        Returns:
            Dict: Result of the import operation
        """
        try:
            logger.info(f"Starting import of book {book_id} for user {user_id}")
            
            # Initialize sync transaction
            await self.sync_transaction.initialize()
            
            # Ensure user collections exist
            if not self.collection_manager.ensure_user_collections(user_id):
                logger.error(f"Failed to ensure collections for user {user_id}")
                return {
                    "success": False,
                    "message": f"Failed to ensure collections for user {user_id}",
                    "book_id": book_id,
                    "user_id": user_id,
                    "timestamp": datetime.now().isoformat()
                }
            
            # Fetch all elements and characters for the book
            async with self.sync_transaction.pool.acquire() as conn:
                # Fetch elements
                elements = await conn.fetch(
                    """
                    SELECT id FROM elements WHERE book_id = $1
                    """,
                    book_id
                )
                
                # Fetch characters
                characters = await conn.fetch(
                    """
                    SELECT id FROM characters WHERE book_id = $1
                    """,
                    book_id
                )
                
                # Add operations to the transaction
                for element in elements:
                    await self.sync_transaction.add_operation(
                        operation_type="upsert",
                        entity_type="element",
                        entity_id=element["id"],
                        user_id=user_id
                    )
                
                for character in characters:
                    await self.sync_transaction.add_operation(
                        operation_type="upsert",
                        entity_type="character",
                        entity_id=character["id"],
                        user_id=user_id
                    )
                
                # Execute the transaction
                success = await self.sync_transaction.execute()
                
                if not success:
                    logger.error(f"Failed to import book {book_id} for user {user_id}")
                    await self.sync_transaction.rollback()
                    return {
                        "success": False,
                        "message": "Failed to import book data",
                        "book_id": book_id,
                        "user_id": user_id,
                        "timestamp": datetime.now().isoformat(),
                        "status": self.sync_transaction.status
                    }
                
                logger.info(f"Successfully imported book {book_id} for user {user_id}")
                return {
                    "success": True,
                    "message": "Book imported successfully",
                    "book_id": book_id,
                    "user_id": user_id,
                    "timestamp": datetime.now().isoformat(),
                    "elements_count": len(elements),
                    "characters_count": len(characters),
                    "status": self.sync_transaction.status
                }
                
        except Exception as e:
            logger.error(f"Error importing book {book_id}: {str(e)}")
            await self.sync_transaction.rollback()
            return {
                "success": False,
                "message": f"Error importing book: {str(e)}",
                "book_id": book_id,
                "user_id": user_id,
                "timestamp": datetime.now().isoformat(),
                "error": str(e)
            }
        finally:
            await self.sync_transaction.close()

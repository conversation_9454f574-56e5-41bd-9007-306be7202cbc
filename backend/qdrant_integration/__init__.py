"""
Qdrant Integration Package

This package provides integration between the book authoring application
and the Qdrant vector database for semantic search capabilities.
"""

from .collection_manager import (
    create_user_collections,
    create_shared_book_collections,
    delete_user_collections,
    remove_user_from_shared_collections
)

from .query_utils import (
    secure_search,
    upsert_vectors,
    delete_vectors,
    get_collection_info
)

from .embedding_service import (
    embedding_service,
    get_element_embedding,
    get_character_embedding,
    get_template_embedding
)

from .enhanced_embedding_service import (
    enhanced_embedding_service,
    EmbeddingStrategy,
    CharacterEmbeddingStrategy,
    LocationEmbeddingStrategy,
    TemplateEmbeddingStrategy
)

from .text_preprocessor import TextPreprocessor
from .field_weighted_embedder import FieldWeightedEmbedder
from .embedding_validator import embedding_validator

from .sync_service import SyncService
from .sync_transaction import SyncTransaction
from .enhanced_collection_manager import EnhancedCollectionManager
from .book_import_sync import BookImportSync

__all__ = [
    # Collection management
    'create_user_collections',
    'create_shared_book_collections',
    'delete_user_collections',
    'remove_user_from_shared_collections',
    'EnhancedCollectionManager',

    # Query utilities
    'secure_search',
    'upsert_vectors',
    'delete_vectors',
    'get_collection_info',

    # Basic embedding service
    'embedding_service',
    'get_element_embedding',
    'get_character_embedding',
    'get_template_embedding',

    # Enhanced embedding pipeline
    'enhanced_embedding_service',
    'EmbeddingStrategy',
    'CharacterEmbeddingStrategy',
    'LocationEmbeddingStrategy',
    'TemplateEmbeddingStrategy',
    'TextPreprocessor',
    'FieldWeightedEmbedder',
    'embedding_validator',

    # Synchronization
    'SyncService',
    'SyncTransaction',
    'BookImportSync'
]

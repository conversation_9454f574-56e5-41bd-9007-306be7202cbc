"""
Collection Manager for Qdrant Vector Database

This module handles the creation, management, and deletion of Qdrant collections
for the book authoring application.
"""

import requests
import json
import os
import logging
import time
from typing import Dict, List, Optional, Union

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Configuration
QDRANT_URL = os.getenv("QDRANT_URL", "http://localhost:6333")
API_KEY = os.getenv("QDRANT_API_KEY", "Qf1wtuJVTItWgMg4GhKh4Khpm/dmmfbnhgIj2U5Qxck=")
HEADERS = {
    "Content-Type": "application/json",
    "api-key": API_KEY
}

# Collection configurations
COLLECTION_CONFIGS = {
    "elements": {"size": 384, "distance": "Cosine"},
    "characters": {"size": 768, "distance": "Cosine"},
    "chapters": {"size": 384, "distance": "Cosine"},
    "concepts": {"size": 768, "distance": "Cosine"},
    "queries": {"size": 384, "distance": "Cosine"},
    "templates": {"size": 768, "distance": "Cosine"},
    "template_fields": {"size": 768, "distance": "Cosine"}
}

class EnhancedCollectionManager:
    """
    Enhanced collection manager with automatic creation and verification

    This class provides advanced collection management capabilities including:
    - Automatic collection creation
    - Collection existence verification
    - User-specific collection management
    - Shared collection management
    - Collection health monitoring
    """

    def __init__(self):
        """Initialize the collection manager"""
        self.qdrant_url = QDRANT_URL
        self.api_key = API_KEY
        self.headers = HEADERS
        self.collection_configs = COLLECTION_CONFIGS

    def check_collection_exists(self, collection_name: str) -> bool:
        """
        Check if a collection exists

        Args:
            collection_name: Name of the collection to check

        Returns:
            bool: True if the collection exists, False otherwise
        """
        try:
            response = requests.get(
                f"{self.qdrant_url}/collections/{collection_name}",
                headers=self.headers
            )

            return response.status_code == 200
        except Exception as e:
            logger.error(f"Error checking if collection {collection_name} exists: {str(e)}")
            return False

    def create_collection(
        self,
        collection_name: str,
        vector_size: int,
        distance: str = "Cosine",
        on_disk_payload: bool = True,
        shard_number: int = 1,
        replication_factor: int = 1,
        write_consistency_factor: int = 1,
        optimizers_config: Optional[Dict] = None,
        wal_config: Optional[Dict] = None,
        hnsw_config: Optional[Dict] = None,
        quantization_config: Optional[Dict] = None
    ) -> bool:
        """
        Create a new collection

        Args:
            collection_name: Name of the collection to create
            vector_size: Dimensionality of vectors
            distance: Distance function (Cosine, Euclid, Dot)
            on_disk_payload: Whether to store payload on disk
            shard_number: Number of shards
            replication_factor: Number of replicas
            write_consistency_factor: Write consistency factor
            optimizers_config: Optimizer configuration
            wal_config: WAL configuration
            hnsw_config: HNSW configuration
            quantization_config: Quantization configuration

        Returns:
            bool: True if successful, False otherwise
        """
        # Default optimizer config if not provided
        if optimizers_config is None:
            optimizers_config = {
                "deleted_threshold": 0.2,
                "vacuum_min_vector_number": 1000,
                "default_segment_number": 2,
                "max_segment_size": 100000
            }

        # Default HNSW config if not provided
        if hnsw_config is None:
            hnsw_config = {
                "m": 16,
                "ef_construct": 100,
                "full_scan_threshold": 10000
            }

        # Build collection configuration
        collection_config = {
            "vectors": {
                "size": vector_size,
                "distance": distance
            },
            "shard_number": shard_number,
            "replication_factor": replication_factor,
            "write_consistency_factor": write_consistency_factor,
            "on_disk_payload": on_disk_payload,
            "optimizers_config": optimizers_config,
            "hnsw_config": hnsw_config
        }

        # Add optional configurations if provided
        if wal_config:
            collection_config["wal_config"] = wal_config

        if quantization_config:
            collection_config["quantization_config"] = quantization_config

        try:
            response = requests.put(
                f"{self.qdrant_url}/collections/{collection_name}",
                headers=self.headers,
                data=json.dumps(collection_config)
            )

            if response.status_code == 200:
                logger.info(f"Created collection {collection_name}")
                return True
            else:
                logger.error(f"Failed to create collection {collection_name}: {response.text}")
                return False
        except Exception as e:
            logger.error(f"Error creating collection {collection_name}: {str(e)}")
            return False

    def ensure_collection_exists(
        self,
        collection_name: str,
        vector_size: int,
        distance: str = "Cosine",
        **kwargs
    ) -> bool:
        """
        Ensure a collection exists, creating it if necessary

        Args:
            collection_name: Name of the collection
            vector_size: Dimensionality of vectors
            distance: Distance function (Cosine, Euclid, Dot)
            **kwargs: Additional parameters for collection creation

        Returns:
            bool: True if the collection exists or was created, False otherwise
        """
        # Check if collection exists
        if self.check_collection_exists(collection_name):
            logger.info(f"Collection {collection_name} already exists")
            return True

        # Create collection
        logger.info(f"Collection {collection_name} does not exist, creating...")
        return self.create_collection(
            collection_name=collection_name,
            vector_size=vector_size,
            distance=distance,
            **kwargs
        )

    def create_field_index(
        self,
        collection_name: str,
        field_name: str,
        field_schema: str = "keyword",
        wait: bool = True
    ) -> bool:
        """
        Create an index on a field in a collection

        Args:
            collection_name: Name of the collection
            field_name: Name of the field to index
            field_schema: Schema type (keyword, integer, float, geo, text)
            wait: Whether to wait for the index to be built

        Returns:
            bool: True if successful, False otherwise
        """
        index_config = {
            "field_name": field_name,
            "field_schema": field_schema,
            "wait": wait
        }

        try:
            response = requests.put(
                f"{self.qdrant_url}/collections/{collection_name}/index",
                headers=self.headers,
                data=json.dumps(index_config)
            )

            if response.status_code == 200:
                logger.info(f"Created index on {field_name} in collection {collection_name}")
                return True
            else:
                logger.error(f"Failed to create index on {field_name} in collection {collection_name}: {response.text}")
                return False
        except Exception as e:
            logger.error(f"Error creating index on {field_name} in collection {collection_name}: {str(e)}")
            return False

    def ensure_user_collections(self, user_id: int) -> bool:
        """
        Ensure all necessary collections exist for a user

        Args:
            user_id: ID of the user

        Returns:
            bool: True if all collections exist or were created, False otherwise
        """
        success = True

        # Create user-specific collections
        for collection_type, vector_config in self.collection_configs.items():
            if collection_type in ["elements", "characters", "chapters", "concepts", "queries"]:
                collection_name = f"user_{user_id}_{collection_type}"

                if not self.ensure_collection_exists(
                    collection_name=collection_name,
                    vector_size=vector_config["size"],
                    distance=vector_config["distance"]
                ):
                    logger.error(f"Failed to ensure collection {collection_name} exists")
                    success = False
                    continue

                # Create book_id index for filtering by book
                if collection_type in ["elements", "characters", "chapters"]:
                    if not self.create_field_index(
                        collection_name=collection_name,
                        field_name="book_id",
                        field_schema="keyword"
                    ):
                        logger.warning(f"Failed to create book_id index on {collection_name}")

        return success

    def ensure_shared_collections(self) -> bool:
        """
        Ensure shared collections exist

        Returns:
            bool: True if all collections exist or were created, False otherwise
        """
        success = True

        # Create shared collections
        for collection_type, vector_config in self.collection_configs.items():
            if collection_type in ["templates", "template_fields"]:
                if not self.ensure_collection_exists(
                    collection_name=collection_type,
                    vector_size=vector_config["size"],
                    distance=vector_config["distance"]
                ):
                    logger.error(f"Failed to ensure collection {collection_type} exists")
                    success = False
                    continue

                # Create category_id index for templates
                if collection_type == "templates":
                    if not self.create_field_index(
                        collection_name=collection_type,
                        field_name="category_id",
                        field_schema="keyword"
                    ):
                        logger.warning(f"Failed to create category_id index on {collection_type}")

                # Create template_id index for template_fields
                if collection_type == "template_fields":
                    if not self.create_field_index(
                        collection_name=collection_type,
                        field_name="template_id",
                        field_schema="keyword"
                    ):
                        logger.warning(f"Failed to create template_id index on {collection_type}")

        return success

    def get_collection_health(self, collection_name: str) -> Dict:
        """
        Get health information about a collection

        Args:
            collection_name: Name of the collection

        Returns:
            Dict: Collection health information
        """
        try:
            # Get collection info
            response = requests.get(
                f"{self.qdrant_url}/collections/{collection_name}",
                headers=self.headers
            )

            if response.status_code != 200:
                logger.error(f"Failed to get collection info: {response.text}")
                return {"status": "error", "message": f"Failed to get collection info: {response.text}"}

            collection_info = response.json().get("result", {})

            # Get collection cluster info
            response = requests.get(
                f"{self.qdrant_url}/collections/{collection_name}/cluster",
                headers=self.headers
            )

            cluster_info = {}
            if response.status_code == 200:
                cluster_info = response.json().get("result", {})

            # Combine information
            health_info = {
                "status": "healthy",
                "name": collection_name,
                "vectors_count": collection_info.get("vectors_count", 0),
                "segments_count": collection_info.get("segments_count", 0),
                "points_count": collection_info.get("points_count", 0),
                "config": collection_info.get("config", {}),
                "cluster": cluster_info
            }

            return health_info
        except Exception as e:
            logger.error(f"Error getting collection health: {str(e)}")
            return {"status": "error", "message": str(e)}

    def delete_collection(self, collection_name: str) -> bool:
        """
        Delete a collection

        Args:
            collection_name: Name of the collection to delete

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            response = requests.delete(
                f"{self.qdrant_url}/collections/{collection_name}",
                headers=self.headers
            )

            if response.status_code == 200:
                logger.info(f"Deleted collection {collection_name}")
                return True
            else:
                logger.error(f"Failed to delete collection {collection_name}: {response.text}")
                return False
        except Exception as e:
            logger.error(f"Error deleting collection {collection_name}: {str(e)}")
            return False

    def delete_user_collections(self, user_id: int) -> bool:
        """
        Delete all collections for a user

        Args:
            user_id: ID of the user

        Returns:
            bool: True if successful, False otherwise
        """
        success = True

        # List all collections
        try:
            response = requests.get(
                f"{self.qdrant_url}/collections",
                headers=self.headers
            )

            if response.status_code != 200:
                logger.error(f"Failed to list collections: {response.text}")
                return False

            collections = response.json().get("result", {}).get("collections", [])

            # Find and delete user collections
            user_prefix = f"user_{user_id}_"
            for collection in collections:
                if collection["name"].startswith(user_prefix):
                    if not self.delete_collection(collection["name"]):
                        success = False

            return success
        except Exception as e:
            logger.error(f"Error deleting user collections: {str(e)}")
            return False

    def list_collections(self) -> List[Dict]:
        """
        List all collections

        Returns:
            List[Dict]: List of collection information
        """
        try:
            response = requests.get(
                f"{self.qdrant_url}/collections",
                headers=self.headers
            )

            if response.status_code == 200:
                return response.json().get("result", {}).get("collections", [])
            else:
                logger.error(f"Failed to list collections: {response.text}")
                return []
        except Exception as e:
            logger.error(f"Error listing collections: {str(e)}")
            return []

    def get_all_collections_health(self) -> Dict[str, Dict]:
        """
        Get health information for all collections

        Returns:
            Dict[str, Dict]: Dictionary of collection health information
        """
        collections = self.list_collections()
        health_info = {}

        for collection in collections:
            collection_name = collection["name"]
            health_info[collection_name] = self.get_collection_health(collection_name)

        return health_info


# Legacy functions that use the new EnhancedCollectionManager class

def create_user_collections(user_id: int) -> bool:
    """
    Create all necessary collections for a new user

    Args:
        user_id: The user ID

    Returns:
        bool: True if successful, False otherwise
    """
    logger.info(f"Creating collections for user {user_id}")

    # Use the enhanced collection manager
    collection_manager = EnhancedCollectionManager()
    return collection_manager.ensure_user_collections(user_id)

def create_shared_book_collections(book_id: int) -> bool:
    """
    Create collections for a shared book

    Args:
        book_id: The book ID

    Returns:
        bool: True if successful, False otherwise
    """
    logger.info(f"Creating shared collections for book {book_id}")

    # Use the enhanced collection manager
    collection_manager = EnhancedCollectionManager()

    # Exclude queries from shared collections
    shared_configs = {k: v for k, v in COLLECTION_CONFIGS.items() if k != "queries"}
    success = True

    for collection_type, vector_config in shared_configs.items():
        collection_name = f"shared_book_{book_id}_{collection_type}"

        # Ensure collection exists
        if not collection_manager.ensure_collection_exists(
            collection_name=collection_name,
            vector_size=vector_config["size"],
            distance=vector_config["distance"]
        ):
            logger.error(f"Failed to ensure collection {collection_name} exists")
            success = False
            continue

        # Create access control index
        if not collection_manager.create_field_index(
            collection_name=collection_name,
            field_name="access_control.collaborators",
            field_schema="keyword"
        ):
            logger.warning(f"Failed to create access control index on {collection_name}")

    return success

def delete_user_collections(user_id: int) -> bool:
    """
    Delete all collections for a user

    Args:
        user_id: The user ID

    Returns:
        bool: True if successful, False otherwise
    """
    logger.info(f"Deleting collections for user {user_id}")

    # Use the enhanced collection manager
    collection_manager = EnhancedCollectionManager()
    return collection_manager.delete_user_collections(user_id)

def remove_user_from_shared_collections(user_id: int) -> bool:
    """
    Remove user from access control lists in shared collections

    Args:
        user_id: The user ID

    Returns:
        bool: True if successful, False otherwise
    """
    logger.info(f"Removing user {user_id} from shared collections")

    # Use the enhanced collection manager
    collection_manager = EnhancedCollectionManager()

    # List all collections
    collections = collection_manager.list_collections()

    # Find shared collections
    shared_collections = [c["name"] for c in collections if c["name"].startswith("shared_book_")]
    success = True

    for collection_name in shared_collections:
        # Search for points where user is in the collaborators list
        search_query = {
            "filter": {
                "must": [
                    {
                        "key": "access_control.collaborators",
                        "match": {"value": user_id}
                    }
                ]
            },
            "limit": 10000  # Adjust based on expected size
        }

        try:
            response = requests.post(
                f"{collection_manager.qdrant_url}/collections/{collection_name}/points/scroll",
                headers=collection_manager.headers,
                data=json.dumps(search_query)
            )

            if response.status_code != 200:
                logger.error(f"Failed to search collection {collection_name}: {response.text}")
                success = False
                continue

            points = response.json().get("result", {}).get("points", [])

            for point in points:
                point_id = point["id"]
                payload = point["payload"]

                # Remove user from collaborators and permissions
                if "access_control" in payload:
                    if "collaborators" in payload["access_control"]:
                        if user_id in payload["access_control"]["collaborators"]:
                            payload["access_control"]["collaborators"].remove(user_id)

                    if "permissions" in payload["access_control"]:
                        if str(user_id) in payload["access_control"]["permissions"]:
                            del payload["access_control"]["permissions"][str(user_id)]

                # Update the point
                update_query = {
                    "points": [
                        {
                            "id": point_id,
                            "payload": payload
                        }
                    ]
                }

                update_response = requests.put(
                    f"{collection_manager.qdrant_url}/collections/{collection_name}/points",
                    headers=collection_manager.headers,
                    data=json.dumps(update_query)
                )

                if update_response.status_code != 200:
                    logger.error(f"Failed to update point {point_id}: {update_response.text}")
                    success = False
        except Exception as e:
            logger.error(f"Error processing collection {collection_name}: {str(e)}")
            success = False

    return success

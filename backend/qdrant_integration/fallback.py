"""
Fallback mechanisms for Qdrant integration

This module provides fallback mechanisms for when Qdrant is unavailable,
ensuring the application can continue to function with degraded capabilities.
"""

import os
import logging
import time
import json
import sqlite3
from typing import Dict, List, Optional, Union, Any
from functools import wraps
import requests

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Configuration
QDRANT_URL = os.getenv("QDRANT_URL", "http://localhost:6333")
API_KEY = os.getenv("QDRANT_API_KEY", "Qf1wtuJVTItWgMg4GhKh4Khpm/dmmfbnhgIj2U5Qxck=")
HEADERS = {
    "Content-Type": "application/json",
    "api-key": API_KEY
}
FALLBACK_DB_PATH = os.getenv("FALLBACK_DB_PATH", "fallback_vectors.db")
MAX_RETRIES = int(os.getenv("QDRANT_MAX_RETRIES", "3"))
RETRY_DELAY = float(os.getenv("QDRANT_RETRY_DELAY", "1.0"))

class QdrantUnavailableError(Exception):
    """Exception raised when Qdrant is unavailable"""
    pass

def initialize_fallback_db():
    """Initialize the fallback SQLite database"""
    try:
        conn = sqlite3.connect(FALLBACK_DB_PATH)
        cursor = conn.cursor()
        
        # Create tables if they don't exist
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS pending_operations (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            operation TEXT NOT NULL,
            collection TEXT NOT NULL,
            data TEXT NOT NULL,
            timestamp REAL NOT NULL
        )
        ''')
        
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS fallback_vectors (
            id TEXT NOT NULL,
            collection TEXT NOT NULL,
            vector TEXT NOT NULL,
            payload TEXT,
            PRIMARY KEY (id, collection)
        )
        ''')
        
        conn.commit()
        conn.close()
        logger.info("Initialized fallback database")
        return True
    except Exception as e:
        logger.error(f"Failed to initialize fallback database: {str(e)}")
        return False

def check_qdrant_available() -> bool:
    """
    Check if Qdrant is available
    
    Returns:
        bool: True if available, False otherwise
    """
    try:
        response = requests.get(
            f"{QDRANT_URL}/readiness",
            headers=HEADERS,
            timeout=5
        )
        return response.status_code == 200
    except Exception:
        return False

def with_qdrant_fallback(fallback_fn=None):
    """
    Decorator to handle Qdrant unavailability
    
    Args:
        fallback_fn: Function to call if Qdrant is unavailable
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            for attempt in range(MAX_RETRIES):
                try:
                    if check_qdrant_available():
                        return func(*args, **kwargs)
                    elif attempt < MAX_RETRIES - 1:
                        logger.warning(f"Qdrant unavailable, retrying in {RETRY_DELAY} seconds...")
                        time.sleep(RETRY_DELAY)
                    else:
                        logger.error("Qdrant unavailable after max retries")
                        if fallback_fn:
                            logger.info("Using fallback function")
                            return fallback_fn(*args, **kwargs)
                        else:
                            logger.info("Using default fallback behavior")
                            return handle_default_fallback(func.__name__, args, kwargs)
                except Exception as e:
                    if attempt < MAX_RETRIES - 1:
                        logger.warning(f"Error calling Qdrant, retrying: {str(e)}")
                        time.sleep(RETRY_DELAY)
                    else:
                        logger.error(f"Error calling Qdrant after max retries: {str(e)}")
                        if fallback_fn:
                            return fallback_fn(*args, **kwargs)
                        else:
                            return handle_default_fallback(func.__name__, args, kwargs)
        return wrapper
    return decorator

def handle_default_fallback(function_name: str, args: tuple, kwargs: dict) -> Any:
    """
    Default fallback behavior
    
    Args:
        function_name: Name of the original function
        args: Function arguments
        kwargs: Function keyword arguments
        
    Returns:
        Any: Default fallback value based on function
    """
    # Store operation for later replay
    store_pending_operation(function_name, args, kwargs)
    
    # Return appropriate fallback value based on function
    if function_name == "secure_search":
        return []  # Empty results for search
    elif function_name in ["upsert_vectors", "delete_vectors"]:
        return False  # Operation failed
    elif function_name == "get_collection_info":
        return {}  # Empty collection info
    else:
        return None

def store_pending_operation(function_name: str, args: tuple, kwargs: dict):
    """
    Store a pending operation for later replay
    
    Args:
        function_name: Name of the function
        args: Function arguments
        kwargs: Function keyword arguments
    """
    try:
        # Initialize fallback DB if needed
        initialize_fallback_db()
        
        # Prepare data
        data = {
            "function": function_name,
            "args": [arg for arg in args if isinstance(arg, (str, int, float, bool, list, dict))],
            "kwargs": kwargs
        }
        
        # Determine collection name
        collection = None
        if function_name in ["secure_search", "upsert_vectors", "delete_vectors"]:
            if len(args) > 1:  # collection_name is typically the second arg
                collection = args[1]
            elif "collection_name" in kwargs:
                collection = kwargs["collection_name"]
        
        if not collection:
            collection = "unknown"
        
        # Store in database
        conn = sqlite3.connect(FALLBACK_DB_PATH)
        cursor = conn.cursor()
        
        cursor.execute(
            "INSERT INTO pending_operations (operation, collection, data, timestamp) VALUES (?, ?, ?, ?)",
            (function_name, collection, json.dumps(data), time.time())
        )
        
        conn.commit()
        conn.close()
        
        logger.info(f"Stored pending operation: {function_name} for collection {collection}")
    except Exception as e:
        logger.error(f"Failed to store pending operation: {str(e)}")

def replay_pending_operations():
    """Replay pending operations when Qdrant becomes available again"""
    if not check_qdrant_available():
        logger.warning("Cannot replay operations: Qdrant still unavailable")
        return False
    
    try:
        # Get pending operations
        conn = sqlite3.connect(FALLBACK_DB_PATH)
        cursor = conn.cursor()
        
        cursor.execute(
            "SELECT id, operation, data FROM pending_operations ORDER BY timestamp"
        )
        
        operations = cursor.fetchall()
        
        if not operations:
            logger.info("No pending operations to replay")
            return True
        
        logger.info(f"Replaying {len(operations)} pending operations")
        
        # Import here to avoid circular imports
        from .query_utils import upsert_vectors, delete_vectors
        
        # Replay operations
        for op_id, operation, data_json in operations:
            try:
                data = json.loads(data_json)
                function = data["function"]
                args = data["args"]
                kwargs = data["kwargs"]
                
                logger.info(f"Replaying operation {op_id}: {function}")
                
                # Call appropriate function
                if function == "upsert_vectors":
                    upsert_vectors(*args, **kwargs)
                elif function == "delete_vectors":
                    delete_vectors(*args, **kwargs)
                
                # Delete operation after successful replay
                cursor.execute("DELETE FROM pending_operations WHERE id = ?", (op_id,))
                conn.commit()
                
            except Exception as e:
                logger.error(f"Failed to replay operation {op_id}: {str(e)}")
        
        conn.close()
        return True
        
    except Exception as e:
        logger.error(f"Failed to replay pending operations: {str(e)}")
        return False

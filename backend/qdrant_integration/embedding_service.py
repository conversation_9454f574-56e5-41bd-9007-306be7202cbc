"""
Embedding Service for Text-to-Vector Conversion

This module handles the generation of vector embeddings from text
using various embedding models, with a focus on Instructor-XL for
template-specific embeddings.
"""

import os
import logging
from typing import List, Dict, Optional, Union, Any
import numpy as np
from functools import lru_cache
import json

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Try to import sentence-transformers, handle gracefully if not installed
try:
    from sentence_transformers import SentenceTransformer
    SENTENCE_TRANSFORMERS_AVAILABLE = True
except ImportError:
    logger.warning("sentence-transformers not available. Install with: pip install sentence-transformers")
    SENTENCE_TRANSFORMERS_AVAILABLE = False

class EmbeddingService:
    """Service for generating embeddings from text"""

    def __init__(self):
        self.models = {}
        self.default_model_name = os.getenv("DEFAULT_EMBEDDING_MODEL", "all-MiniLM-L6-v2")
        self.instructor_model_name = os.getenv("INSTRUCTOR_EMBEDDING_MODEL", "hkunlp/instructor-xl")

    def load_model(self, model_name: str) -> bool:
        """
        Load an embedding model

        Args:
            model_name: Name of the model to load

        Returns:
            bool: True if successful, False otherwise
        """
        if not SENTENCE_TRANSFORMERS_AVAILABLE:
            logger.error("Cannot load model: sentence-transformers not installed")
            return False

        try:
            if model_name not in self.models:
                logger.info(f"Loading embedding model: {model_name}")
                self.models[model_name] = SentenceTransformer(model_name)
            return True
        except Exception as e:
            logger.error(f"Error loading model {model_name}: {str(e)}")
            return False

    @lru_cache(maxsize=1024)
    def get_embedding(
        self,
        text: str,
        model_name: Optional[str] = None
    ) -> List[float]:
        """
        Generate embedding for a single text

        Args:
            text: The text to embed
            model_name: Optional model name to use (defaults to default model)

        Returns:
            List[float]: The embedding vector
        """
        if not text:
            logger.warning("Empty text provided for embedding")
            # Return zero vector of appropriate size
            if model_name and model_name.startswith("hkunlp/instructor"):
                return [0.0] * 768
            else:
                return [0.0] * 384

        model_name = model_name or self.default_model_name

        # Load model if not already loaded
        if model_name not in self.models:
            success = self.load_model(model_name)
            if not success:
                logger.error(f"Failed to load model {model_name}")
                return []

        try:
            # Generate embedding
            embedding = self.models[model_name].encode(text)
            return embedding.tolist()
        except Exception as e:
            logger.error(f"Error generating embedding: {str(e)}")
            return []

    def get_embeddings(
        self,
        texts: List[str],
        model_name: Optional[str] = None
    ) -> List[List[float]]:
        """
        Generate embeddings for multiple texts

        Args:
            texts: List of texts to embed
            model_name: Optional model name to use (defaults to default model)

        Returns:
            List[List[float]]: List of embedding vectors
        """
        model_name = model_name or self.default_model_name

        # Load model if not already loaded
        if model_name not in self.models:
            success = self.load_model(model_name)
            if not success:
                logger.error(f"Failed to load model {model_name}")
                return []

        try:
            # Generate embeddings
            embeddings = self.models[model_name].encode(texts)
            return embeddings.tolist()
        except Exception as e:
            logger.error(f"Error generating embeddings: {str(e)}")
            return []

    def get_instructor_embedding(
        self,
        text: str,
        instruction: str
    ) -> List[float]:
        """
        Generate embedding using Instructor model with specific instruction

        Args:
            text: The text to embed
            instruction: The instruction for embedding generation

        Returns:
            List[float]: The embedding vector
        """
        if not SENTENCE_TRANSFORMERS_AVAILABLE:
            logger.error("Cannot generate embedding: sentence-transformers not installed")
            return []

        model_name = self.instructor_model_name

        # Load model if not already loaded
        if model_name not in self.models:
            success = self.load_model(model_name)
            if not success:
                logger.error(f"Failed to load model {model_name}")
                return []

        try:
            # Generate embedding with instruction
            embedding = self.models[model_name].encode([[instruction, text]])
            return embedding.tolist()
        except Exception as e:
            logger.error(f"Error generating instructor embedding: {str(e)}")
            return []

    def get_instructor_embeddings(
        self,
        texts: List[str],
        instruction: str
    ) -> List[List[float]]:
        """
        Generate embeddings for multiple texts using Instructor model with specific instruction

        Args:
            texts: List of texts to embed
            instruction: The instruction for embedding generation

        Returns:
            List[List[float]]: List of embedding vectors
        """
        if not SENTENCE_TRANSFORMERS_AVAILABLE:
            logger.error("Cannot generate embeddings: sentence-transformers not installed")
            return []

        model_name = self.instructor_model_name

        # Load model if not already loaded
        if model_name not in self.models:
            success = self.load_model(model_name)
            if not success:
                logger.error(f"Failed to load model {model_name}")
                return []

        try:
            # Prepare input pairs
            input_pairs = [[instruction, text] for text in texts]

            # Generate embeddings with instruction
            embeddings = self.models[model_name].encode(input_pairs)
            return embeddings.tolist()
        except Exception as e:
            logger.error(f"Error generating instructor embeddings: {str(e)}")
            return []

# Create a singleton instance
embedding_service = EmbeddingService()

def get_element_embedding(element: Dict[str, Any]) -> List[float]:
    """
    Generate an embedding for a world-building element

    Args:
        element: Dictionary containing element data

    Returns:
        List[float]: The embedding vector
    """
    # Combine relevant fields for embedding
    text_parts = []

    if "name" in element:
        text_parts.append(f"Name: {element['name']}")

    if "category" in element:
        text_parts.append(f"Category: {element['category']}")

    if "subcategory" in element and element["subcategory"]:
        text_parts.append(f"Subcategory: {element['subcategory']}")

    if "description" in element and element["description"]:
        text_parts.append(f"Description: {element['description']}")

    if "attributes" in element and element["attributes"]:
        for key, value in element["attributes"].items():
            if value:
                text_parts.append(f"{key}: {value}")

    combined_text = "\n".join(text_parts)

    # Generate embedding
    return embedding_service.get_embedding(combined_text)

def get_character_embedding(character: Dict[str, Any]) -> List[float]:
    """
    Generate an embedding for a character

    Args:
        character: Dictionary containing character data

    Returns:
        List[float]: The embedding vector
    """
    # Use Instructor model for characters
    instruction = "Represent the character for retrieval based on personality, background, and traits"

    # Combine relevant fields for embedding
    text_parts = []

    if "name" in character:
        text_parts.append(f"Name: {character['name']}")

    if "full_name" in character and character["full_name"]:
        text_parts.append(f"Full name: {character['full_name']}")

    if "gender_identity" in character and character["gender_identity"]:
        text_parts.append(f"Gender identity: {character['gender_identity']}")

    if "sexual_orientation" in character and character["sexual_orientation"]:
        text_parts.append(f"Sexual orientation: {character['sexual_orientation']}")

    if "personality" in character and character["personality"]:
        text_parts.append(f"Personality: {character['personality']}")

    if "background" in character and character["background"]:
        text_parts.append(f"Background: {character['background']}")

    if "appearance" in character and character["appearance"]:
        text_parts.append(f"Appearance: {character['appearance']}")

    if "motivations" in character and character["motivations"]:
        text_parts.append(f"Motivations: {character['motivations']}")

    combined_text = "\n".join(text_parts)

    # Generate embedding with instruction
    return embedding_service.get_instructor_embedding(combined_text, instruction)

def get_template_embedding(template: Dict[str, Any]) -> List[float]:
    """
    Generate an embedding for a template

    Args:
        template: Dictionary containing template data

    Returns:
        List[float]: The embedding vector
    """
    # Use Instructor model for templates with specific instruction
    instruction = "Represent this template for retrieval based on its purpose, fields, and relationships"

    # Combine relevant fields for embedding
    text_parts = []

    if "template_name" in template:
        text_parts.append(f"Template Name: {template['template_name']}")

    if "display_name" in template:
        text_parts.append(f"Display Name: {template['display_name']}")

    if "category_name" in template:
        text_parts.append(f"Category: {template['category_name']}")

    if "description" in template and template["description"]:
        text_parts.append(f"Description: {template['description']}")

    # Add field definitions
    if "field_definitions" in template and template["field_definitions"]:
        field_defs = template["field_definitions"]

        # Handle both string and dict formats
        if isinstance(field_defs, str):
            try:
                field_defs = json.loads(field_defs)
            except json.JSONDecodeError:
                logger.warning(f"Failed to parse field_definitions as JSON: {field_defs}")
                field_defs = {}

        # Process each section
        for section, fields in field_defs.items():
            text_parts.append(f"Section: {section}")

            for field in fields:
                if isinstance(field, dict):
                    field_text = f"Field: {field.get('label', 'Unnamed')} ({field.get('type', 'text')})"
                    if field.get('description'):
                        field_text += f" - {field['description']}"
                    text_parts.append(field_text)

    # Add relationship definitions
    if "relationship_definitions" in template and template["relationship_definitions"]:
        rel_defs = template["relationship_definitions"]

        # Handle both string and dict formats
        if isinstance(rel_defs, str):
            try:
                rel_defs = json.loads(rel_defs)
            except json.JSONDecodeError:
                logger.warning(f"Failed to parse relationship_definitions as JSON: {rel_defs}")
                rel_defs = {}

        # Process each relationship type
        for rel_type, relationships in rel_defs.items():
            text_parts.append(f"Relationship Type: {rel_type}")

            for rel in relationships:
                if isinstance(rel, dict):
                    rel_text = f"Related to: {rel.get('target_template', 'Unknown')}"
                    if rel.get('description'):
                        rel_text += f" - {rel['description']}"
                    text_parts.append(rel_text)

    combined_text = "\n".join(text_parts)

    # Generate embedding with instruction
    return embedding_service.get_instructor_embedding(combined_text, instruction)

def get_template_field_embedding(field: Dict[str, Any]) -> List[float]:
    """
    Generate an embedding for a template field

    Args:
        field: Dictionary containing field data

    Returns:
        List[float]: The embedding vector
    """
    # Use Instructor model for fields with specific instruction
    instruction = "Represent this field for retrieval based on its purpose and characteristics"

    # Combine relevant fields for embedding
    text_parts = []

    if "label" in field:
        text_parts.append(f"Label: {field['label']}")

    if "id" in field:
        text_parts.append(f"ID: {field['id']}")

    if "type" in field:
        text_parts.append(f"Type: {field['type']}")

    if "description" in field and field["description"]:
        text_parts.append(f"Description: {field['description']}")

    if "required" in field:
        text_parts.append(f"Required: {field['required']}")

    if "options" in field and field["options"]:
        options_text = ", ".join(field["options"])
        text_parts.append(f"Options: {options_text}")

    combined_text = "\n".join(text_parts)

    # Generate embedding with instruction
    return embedding_service.get_instructor_embedding(combined_text, instruction)

def get_content_embedding_for_template_suggestion(content: str) -> List[float]:
    """
    Generate an embedding for content to suggest relevant templates

    Args:
        content: Text content to analyze

    Returns:
        List[float]: The embedding vector
    """
    # Use Instructor model with specific instruction for template suggestion
    instruction = "Represent this content for finding relevant templates and categories"

    # Generate embedding with instruction
    return embedding_service.get_instructor_embedding(content, instruction)

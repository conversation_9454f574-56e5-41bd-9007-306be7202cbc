"""
Enhanced Embedding Service for Advanced Embedding Generation

This module provides an enhanced embedding service that combines text preprocessing,
field weighting, and template-specific strategies for high-quality embeddings.
"""

import logging
from typing import List, Dict, Optional, Union, Any, Type, Protocol
from abc import ABC, abstractmethod
import json
from functools import lru_cache

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Import components
from .text_preprocessor import TextPreprocessor
from .field_weighted_embedder import FieldWeightedEmbedder
from .embedding_service import embedding_service

class EmbeddingStrategy(ABC):
    """
    Abstract base class for embedding strategies

    This class defines the interface for different embedding strategies,
    which can be customized for different template types.
    """

    @abstractmethod
    def get_embedding(self, data: Dict[str, Any]) -> List[float]:
        """
        Get embedding for data

        Args:
            data: Dictionary containing data to embed

        Returns:
            List[float]: The embedding vector
        """
        pass

    @abstractmethod
    def get_instruction(self) -> str:
        """
        Get instruction for embedding generation

        Returns:
            str: Instruction for embedding generation
        """
        pass

    @abstractmethod
    def get_field_weights(self) -> Dict[str, float]:
        """
        Get field weights for embedding generation

        Returns:
            Dict[str, float]: Dictionary of field names and weights
        """
        pass

    @abstractmethod
    def get_field_instructions(self) -> Dict[str, str]:
        """
        Get field-specific instructions for embedding generation

        Returns:
            Dict[str, str]: Dictionary of field names and instructions
        """
        pass

class CharacterEmbeddingStrategy(EmbeddingStrategy):
    """Embedding strategy for characters"""

    def get_embedding(self, data: Dict[str, Any]) -> List[float]:
        """
        Get embedding for a character

        Args:
            data: Dictionary containing character data

        Returns:
            List[float]: The embedding vector
        """
        # Preprocess fields
        preprocessor = TextPreprocessor()
        processed_data = {}

        for field_name, field_value in data.items():
            if isinstance(field_value, str) and field_value:
                processed_data[field_name] = preprocessor.preprocess(field_value, "character")
            else:
                processed_data[field_name] = field_value

        # Get weighted embedding
        embedder = FieldWeightedEmbedder()
        return embedder.get_character_embedding(
            processed_data,
            self.get_field_weights(),
            self.get_field_instructions()
        )

    def get_instruction(self) -> str:
        """
        Get instruction for character embedding generation

        Returns:
            str: Instruction for embedding generation
        """
        return "Represent the character for retrieval based on personality, background, and traits"

    def get_field_weights(self) -> Dict[str, float]:
        """
        Get field weights for character embedding generation

        Returns:
            Dict[str, float]: Dictionary of field names and weights
        """
        return {
            "name": 1.5,
            "full_name": 1.2,
            "personality": 1.8,
            "background": 1.5,
            "appearance": 1.0,
            "motivations": 1.6,
            "gender_identity": 0.8,
            "sexual_orientation": 0.8
        }

    def get_field_instructions(self) -> Dict[str, str]:
        """
        Get field-specific instructions for character embedding generation

        Returns:
            Dict[str, str]: Dictionary of field names and instructions
        """
        return {
            "personality": "Represent the character's personality traits and characteristics",
            "background": "Represent the character's history, origins, and experiences",
            "motivations": "Represent the character's goals, desires, and driving forces"
        }

class LocationEmbeddingStrategy(EmbeddingStrategy):
    """Embedding strategy for locations"""

    def get_embedding(self, data: Dict[str, Any]) -> List[float]:
        """
        Get embedding for a location

        Args:
            data: Dictionary containing location data

        Returns:
            List[float]: The embedding vector
        """
        # Preprocess fields
        preprocessor = TextPreprocessor()
        processed_data = {}

        for field_name, field_value in data.items():
            if isinstance(field_value, str) and field_value:
                processed_data[field_name] = preprocessor.preprocess(field_value, "location")
            else:
                processed_data[field_name] = field_value

        # Get weighted embedding
        embedder = FieldWeightedEmbedder()
        return embedder.get_weighted_embedding(
            processed_data,
            "location",
            self.get_field_instructions()
        )

    def get_instruction(self) -> str:
        """
        Get instruction for location embedding generation

        Returns:
            str: Instruction for embedding generation
        """
        return "Represent the location for retrieval based on geography, climate, and features"

    def get_field_weights(self) -> Dict[str, float]:
        """
        Get field weights for location embedding generation

        Returns:
            Dict[str, float]: Dictionary of field names and weights
        """
        return {
            "name": 1.5,
            "geography": 1.8,
            "climate": 1.5,
            "description": 1.2,
            "history": 1.0,
            "inhabitants": 1.0
        }

    def get_field_instructions(self) -> Dict[str, str]:
        """
        Get field-specific instructions for location embedding generation

        Returns:
            Dict[str, str]: Dictionary of field names and instructions
        """
        return {
            "geography": "Represent the location's physical features and terrain",
            "climate": "Represent the location's weather patterns and environmental conditions",
            "inhabitants": "Represent the people, creatures, or beings that live in this location"
        }

class TemplateEmbeddingStrategy(EmbeddingStrategy):
    """Embedding strategy for templates"""

    def get_embedding(self, data: Dict[str, Any]) -> List[float]:
        """
        Get embedding for a template

        Args:
            data: Dictionary containing template data

        Returns:
            List[float]: The embedding vector
        """
        # Preprocess fields
        preprocessor = TextPreprocessor()
        processed_data = {}

        # Process basic fields
        for field_name in ["template_name", "display_name", "description", "category_name"]:
            if field_name in data and data[field_name]:
                processed_data[field_name] = preprocessor.preprocess(data[field_name])

        # Process field definitions
        if "field_definitions" in data and data["field_definitions"]:
            field_defs = data["field_definitions"]

            # Handle both string and dict formats
            if isinstance(field_defs, str):
                try:
                    field_defs = json.loads(field_defs)
                except json.JSONDecodeError:
                    logger.warning(f"Failed to parse field_definitions as JSON: {field_defs}")
                    field_defs = {}

            # Process each section
            field_texts = []
            for section, fields in field_defs.items():
                field_texts.append(f"Section: {section}")

                for field in fields:
                    if isinstance(field, dict):
                        field_text = f"Field: {field.get('label', 'Unnamed')} ({field.get('type', 'text')})"
                        if field.get('description'):
                            field_text += f" - {field['description']}"
                        field_texts.append(field_text)

            if field_texts:
                processed_data["fields"] = preprocessor.preprocess("\n".join(field_texts))

        # Process relationship definitions
        if "relationship_definitions" in data and data["relationship_definitions"]:
            rel_defs = data["relationship_definitions"]

            # Handle both string and dict formats
            if isinstance(rel_defs, str):
                try:
                    rel_defs = json.loads(rel_defs)
                except json.JSONDecodeError:
                    logger.warning(f"Failed to parse relationship_definitions as JSON: {rel_defs}")
                    rel_defs = {}

            # Process each relationship type
            rel_texts = []
            for rel_type, relationships in rel_defs.items():
                rel_texts.append(f"Relationship Type: {rel_type}")

                for rel in relationships:
                    if isinstance(rel, dict):
                        rel_text = f"Related to: {rel.get('target_template', 'Unknown')}"
                        if rel.get('description'):
                            rel_text += f" - {rel['description']}"
                        rel_texts.append(rel_text)

            if rel_texts:
                processed_data["relationships"] = preprocessor.preprocess("\n".join(rel_texts))

        # Get weighted embedding
        embedder = FieldWeightedEmbedder()
        return embedder.get_weighted_embedding(
            processed_data,
            "template",
            self.get_field_instructions()
        )

    def get_instruction(self) -> str:
        """
        Get instruction for template embedding generation

        Returns:
            str: Instruction for embedding generation
        """
        return "Represent this template for retrieval based on its purpose, fields, and relationships"

    def get_field_weights(self) -> Dict[str, float]:
        """
        Get field weights for template embedding generation

        Returns:
            Dict[str, float]: Dictionary of field names and weights
        """
        return {
            "template_name": 1.5,
            "display_name": 1.5,
            "description": 1.8,
            "category_name": 1.2,
            "fields": 1.5,
            "relationships": 1.2
        }

    def get_field_instructions(self) -> Dict[str, str]:
        """
        Get field-specific instructions for template embedding generation

        Returns:
            Dict[str, str]: Dictionary of field names and instructions
        """
        return {
            "description": "Represent the template's purpose and usage",
            "fields": "Represent the template's fields and their characteristics",
            "relationships": "Represent the template's relationships with other templates"
        }

class EnhancedEmbeddingService:
    """
    Enhanced embedding service for advanced embedding generation

    This class combines text preprocessing, field weighting, and template-specific
    strategies for high-quality embeddings.
    """

    def __init__(self):
        """Initialize the enhanced embedding service"""
        self.preprocessor = TextPreprocessor()
        self.embedder = FieldWeightedEmbedder()

        # Register embedding strategies
        self.strategies = {
            "character": CharacterEmbeddingStrategy(),
            "location": LocationEmbeddingStrategy(),
            "template": TemplateEmbeddingStrategy()
        }

    def register_strategy(self, entity_type: str, strategy: EmbeddingStrategy) -> None:
        """
        Register an embedding strategy for an entity type

        Args:
            entity_type: Type of entity
            strategy: Embedding strategy for the entity type
        """
        self.strategies[entity_type] = strategy
        logger.info(f"Registered embedding strategy for {entity_type}")

    @lru_cache(maxsize=128)
    def get_embedding(self, text: str, instruction: Optional[str] = None) -> List[float]:
        """
        Get embedding for a single text

        Args:
            text: Text to embed
            instruction: Optional instruction for embedding generation

        Returns:
            List[float]: The embedding vector
        """
        # Preprocess text
        processed_text = self.preprocessor.preprocess(text)

        # Get embedding
        if instruction:
            return embedding_service.get_instructor_embedding(processed_text, instruction)
        else:
            return embedding_service.get_embedding(processed_text)

    def get_entity_embedding(self, entity: Dict[str, Any], entity_type: str) -> List[float]:
        """
        Get embedding for an entity using the appropriate strategy

        Args:
            entity: Dictionary containing entity data
            entity_type: Type of entity

        Returns:
            List[float]: The embedding vector
        """
        # Get strategy for entity type
        strategy = self.strategies.get(entity_type)

        if strategy:
            # Use strategy to get embedding
            return strategy.get_embedding(entity)
        else:
            # Fall back to generic embedding
            logger.warning(f"No embedding strategy found for {entity_type}, using generic embedding")

            # Extract text from entity
            text_parts = []
            for key, value in entity.items():
                if isinstance(value, str) and value:
                    text_parts.append(f"{key}: {value}")

            combined_text = "\n".join(text_parts)

            # Get embedding
            return self.get_embedding(combined_text)

    def get_template_embedding(self, template: Dict[str, Any]) -> List[float]:
        """
        Get embedding for a template

        Args:
            template: Dictionary containing template data

        Returns:
            List[float]: The embedding vector
        """
        return self.get_entity_embedding(template, "template")

    def get_character_embedding(self, character: Dict[str, Any]) -> List[float]:
        """
        Get embedding for a character

        Args:
            character: Dictionary containing character data

        Returns:
            List[float]: The embedding vector
        """
        return self.get_entity_embedding(character, "character")

    def get_element_embedding(self, element: Dict[str, Any]) -> List[float]:
        """
        Get embedding for an element

        Args:
            element: Dictionary containing element data

        Returns:
            List[float]: The embedding vector
        """
        # Determine entity type from element
        entity_type = element.get("template_type", "default")

        # Get embedding
        return self.get_entity_embedding(element, entity_type)

    def get_relationship_focused_embedding(
        self,
        entity: Dict[str, Any],
        related_entities: List[Dict[str, Any]],
        entity_type: str,
        relationship_weight: float = 0.3
    ) -> List[float]:
        """
        Get embedding that incorporates relationship information

        Args:
            entity: Dictionary containing entity data
            related_entities: List of dictionaries containing related entity data
            entity_type: Type of entity
            relationship_weight: Weight to give to relationship information

        Returns:
            List[float]: The embedding vector
        """
        # Get base embedding for entity
        base_embedding = self.get_entity_embedding(entity, entity_type)

        if not related_entities:
            return base_embedding

        # Get embeddings for related entities
        related_embeddings = []
        for related_entity in related_entities:
            related_type = related_entity.get("template_type", "default")
            embedding = self.get_entity_embedding(related_entity, related_type)
            if embedding:
                related_embeddings.append(embedding)

        if not related_embeddings:
            return base_embedding

        # Combine embeddings
        import numpy as np

        # Convert to numpy arrays
        base_array = np.array(base_embedding)
        related_arrays = [np.array(emb) for emb in related_embeddings]

        # Average related embeddings
        related_avg = np.mean(related_arrays, axis=0)

        # Combine with base embedding
        combined = (1 - relationship_weight) * base_array + relationship_weight * related_avg

        # Normalize
        norm = np.linalg.norm(combined)
        if norm > 0:
            combined /= norm

        return combined.tolist()

# Create a singleton instance
enhanced_embedding_service = EnhancedEmbeddingService()
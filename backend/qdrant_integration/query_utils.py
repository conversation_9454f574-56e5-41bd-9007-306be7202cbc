"""
Query Utilities for Qdrant Vector Database

This module provides utilities for querying the Qdrant vector database
with proper security controls.
"""

import requests
import json
import os
import logging
from typing import Dict, List, Optional, Union, Any

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Configuration
QDRANT_URL = os.getenv("QDRANT_URL", "http://localhost:6333")
API_KEY = os.getenv("QDRANT_API_KEY", "Qf1wtuJVTItWgMg4GhKh4Khpm/dmmfbnhgIj2U5Qxck=")
HEADERS = {
    "Content-Type": "application/json",
    "api-key": API_KEY
}

def secure_search(
    user_id: int, 
    collection_name: str, 
    vector: List[float], 
    limit: int = 10, 
    filter_params: Optional[Dict] = None
) -> List[Dict]:
    """
    Perform a secure vector search with proper access controls
    
    Args:
        user_id: The user ID performing the search
        collection_name: The collection to search
        vector: The query vector
        limit: Maximum number of results to return
        filter_params: Additional filter parameters
        
    Returns:
        List of search results
        
    Raises:
        PermissionError: If the user doesn't have access to the collection
    """
    # Validate access
    if collection_name.startswith(f"user_{user_id}_"):
        # User's own collection - access granted
        logger.debug(f"User {user_id} accessing own collection {collection_name}")
    elif collection_name.startswith("shared_book_"):
        # Shared collection - need to filter by access control
        logger.debug(f"User {user_id} accessing shared collection {collection_name}")
        
        if filter_params is None:
            filter_params = {"must": []}
        elif "must" not in filter_params:
            filter_params["must"] = []
            
        # Add access control filter
        filter_params["must"].append({
            "key": "access_control.collaborators",
            "match": {"value": user_id}
        })
    else:
        # Unauthorized access
        logger.warning(f"User {user_id} attempted unauthorized access to {collection_name}")
        raise PermissionError(f"User {user_id} cannot access {collection_name}")
    
    # Build search query
    search_query = {
        "vector": vector,
        "limit": limit
    }
    
    if filter_params:
        search_query["filter"] = filter_params
    
    # Execute search
    try:
        response = requests.post(
            f"{QDRANT_URL}/collections/{collection_name}/points/search",
            headers=HEADERS,
            data=json.dumps(search_query)
        )
        
        if response.status_code == 200:
            return response.json().get("result", [])
        else:
            logger.error(f"Search failed: {response.text}")
            return []
    except Exception as e:
        logger.error(f"Error during search: {str(e)}")
        return []

def upsert_vectors(
    collection_name: str, 
    vectors: List[List[float]], 
    ids: Optional[List[Union[int, str]]] = None, 
    payloads: Optional[List[Dict]] = None
) -> bool:
    """
    Insert or update vectors in a collection
    
    Args:
        collection_name: The collection to update
        vectors: List of vectors to insert/update
        ids: Optional list of IDs for the vectors
        payloads: Optional list of payloads for the vectors
        
    Returns:
        bool: True if successful, False otherwise
    """
    points = []
    
    for i, vector in enumerate(vectors):
        point = {"vector": vector}
        
        if ids is not None:
            point["id"] = ids[i]
            
        if payloads is not None:
            point["payload"] = payloads[i]
            
        points.append(point)
    
    upsert_query = {"points": points}
    
    try:
        response = requests.put(
            f"{QDRANT_URL}/collections/{collection_name}/points",
            headers=HEADERS,
            data=json.dumps(upsert_query)
        )
        
        if response.status_code == 200:
            logger.info(f"Successfully upserted {len(vectors)} vectors to {collection_name}")
            return True
        else:
            logger.error(f"Upsert failed: {response.text}")
            return False
    except Exception as e:
        logger.error(f"Error during upsert: {str(e)}")
        return False

def delete_vectors(
    collection_name: str, 
    ids: List[Union[int, str]]
) -> bool:
    """
    Delete vectors from a collection
    
    Args:
        collection_name: The collection to delete from
        ids: List of vector IDs to delete
        
    Returns:
        bool: True if successful, False otherwise
    """
    delete_query = {"points": ids}
    
    try:
        response = requests.post(
            f"{QDRANT_URL}/collections/{collection_name}/points/delete",
            headers=HEADERS,
            data=json.dumps(delete_query)
        )
        
        if response.status_code == 200:
            logger.info(f"Successfully deleted {len(ids)} vectors from {collection_name}")
            return True
        else:
            logger.error(f"Delete failed: {response.text}")
            return False
    except Exception as e:
        logger.error(f"Error during delete: {str(e)}")
        return False

def get_collection_info(collection_name: str) -> Dict:
    """
    Get information about a collection
    
    Args:
        collection_name: The collection to get info about
        
    Returns:
        Dict: Collection information or empty dict if failed
    """
    try:
        response = requests.get(
            f"{QDRANT_URL}/collections/{collection_name}",
            headers=HEADERS
        )
        
        if response.status_code == 200:
            return response.json().get("result", {})
        else:
            logger.error(f"Failed to get collection info: {response.text}")
            return {}
    except Exception as e:
        logger.error(f"Error getting collection info: {str(e)}")
        return {}

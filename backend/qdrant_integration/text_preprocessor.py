"""
Text Preprocessor for Embedding Generation

This module provides text preprocessing functionality for embedding generation,
including normalization, cleaning, and standardization.
"""

import re
import unicodedata
import html
from typing import List, Dict, Optional, Union, Any, Callable
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class TextPreprocessor:
    """
    Text preprocessor for embedding generation

    This class provides methods for preprocessing text before generating embeddings,
    including normalization, cleaning, and standardization.
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the text preprocessor

        Args:
            config: Optional configuration dictionary
        """
        self.config = config or {}

        # Default preprocessing steps
        self.default_steps = [
            self.normalize_unicode,
            self.decode_html_entities,
            self.remove_extra_whitespace,
            self.standardize_punctuation
        ]

        # Template-specific preprocessing steps
        self.template_steps = {
            "character": self.default_steps + [self.extract_character_traits],
            "location": self.default_steps + [self.extract_location_features],
            "item": self.default_steps + [self.extract_item_properties],
            "concept": self.default_steps + [self.extract_concept_elements],
            "event": self.default_steps + [self.extract_event_details],
            "default": self.default_steps
        }

    def preprocess(self, text: str, template_type: Optional[str] = None) -> str:
        """
        Preprocess text using appropriate steps for the template type

        Args:
            text: Text to preprocess
            template_type: Optional template type to use specific preprocessing steps

        Returns:
            str: Preprocessed text
        """
        if not text:
            return ""

        # Get preprocessing steps for template type
        steps = self.template_steps.get(template_type, self.default_steps)

        # Apply preprocessing steps
        processed_text = text
        for step in steps:
            processed_text = step(processed_text)

        return processed_text

    def normalize_unicode(self, text: str) -> str:
        """
        Normalize Unicode characters to their canonical form

        Args:
            text: Text to normalize

        Returns:
            str: Normalized text
        """
        return unicodedata.normalize('NFKC', text)

    def decode_html_entities(self, text: str) -> str:
        """
        Decode HTML entities to their corresponding characters

        Args:
            text: Text to decode

        Returns:
            str: Decoded text
        """
        return html.unescape(text)

    def remove_extra_whitespace(self, text: str) -> str:
        """
        Remove extra whitespace, including newlines, tabs, and multiple spaces

        Args:
            text: Text to clean

        Returns:
            str: Cleaned text
        """
        # Replace newlines and tabs with spaces
        text = re.sub(r'[\n\t\r]+', ' ', text)

        # Replace multiple spaces with a single space
        text = re.sub(r'\s+', ' ', text)

        # Trim leading and trailing whitespace
        return text.strip()

    def standardize_punctuation(self, text: str) -> str:
        """
        Standardize punctuation marks

        Args:
            text: Text to standardize

        Returns:
            str: Standardized text
        """
        # Replace multiple periods with ellipsis
        text = re.sub(r'\.{3,}', '…', text)

        # Replace multiple dashes with em dash
        text = re.sub(r'-{2,}', '—', text)

        # Ensure consistent spacing around punctuation
        text = re.sub(r'\s*([,.;:!?])', r'\1 ', text)

        # Remove extra spaces after punctuation
        text = re.sub(r'([,.;:!?])\s+', r'\1 ', text)

        return text.strip()

    def extract_character_traits(self, text: str) -> str:
        """
        Extract and highlight character traits from text

        Args:
            text: Text to process

        Returns:
            str: Processed text with highlighted traits
        """
        # This is a placeholder for more sophisticated trait extraction
        # In a real implementation, this would use NLP to identify traits

        # For now, just highlight certain keywords
        trait_keywords = [
            "personality", "trait", "characteristic", "temperament",
            "nature", "disposition", "attitude", "behavior", "quality",
            "attribute", "tendency", "habit", "manner", "quirk"
        ]

        for keyword in trait_keywords:
            text = re.sub(
                rf'\b({keyword}s?)\b',
                r'TRAIT_MARKER: \1',
                text,
                flags=re.IGNORECASE
            )

        return text

    def extract_location_features(self, text: str) -> str:
        """
        Extract and highlight location features from text

        Args:
            text: Text to process

        Returns:
            str: Processed text with highlighted features
        """
        # Placeholder for more sophisticated feature extraction
        feature_keywords = [
            "geography", "terrain", "landscape", "climate", "environment",
            "region", "area", "zone", "location", "place", "site", "spot",
            "landmark", "feature", "formation", "structure"
        ]

        for keyword in feature_keywords:
            text = re.sub(
                rf'\b({keyword}s?)\b',
                r'LOCATION_MARKER: \1',
                text,
                flags=re.IGNORECASE
            )

        return text

    def extract_item_properties(self, text: str) -> str:
        """
        Extract and highlight item properties from text

        Args:
            text: Text to process

        Returns:
            str: Processed text with highlighted properties
        """
        # Placeholder for more sophisticated property extraction
        property_keywords = [
            "property", "attribute", "quality", "characteristic", "feature",
            "aspect", "trait", "specification", "parameter", "dimension",
            "measurement", "size", "weight", "color", "material", "composition"
        ]

        for keyword in property_keywords:
            text = re.sub(
                rf'\b({keyword}s?)\b',
                r'PROPERTY_MARKER: \1',
                text,
                flags=re.IGNORECASE
            )

        return text

    def extract_concept_elements(self, text: str) -> str:
        """
        Extract and highlight concept elements from text

        Args:
            text: Text to process

        Returns:
            str: Processed text with highlighted elements
        """
        # Placeholder for more sophisticated element extraction
        element_keywords = [
            "concept", "idea", "notion", "principle", "theory", "philosophy",
            "doctrine", "belief", "ideology", "paradigm", "framework", "model",
            "system", "structure", "foundation", "basis", "premise", "assumption"
        ]

        for keyword in element_keywords:
            text = re.sub(
                rf'\b({keyword}s?)\b',
                r'CONCEPT_MARKER: \1',
                text,
                flags=re.IGNORECASE
            )

        return text

    def extract_event_details(self, text: str) -> str:
        """
        Extract and highlight event details from text

        Args:
            text: Text to process

        Returns:
            str: Processed text with highlighted details
        """
        # Placeholder for more sophisticated detail extraction
        detail_keywords = [
            "event", "occurrence", "incident", "happening", "occasion",
            "circumstance", "situation", "affair", "episode", "experience",
            "time", "date", "duration", "period", "interval", "moment"
        ]

        for keyword in detail_keywords:
            text = re.sub(
                rf'\b({keyword}s?)\b',
                r'EVENT_MARKER: \1',
                text,
                flags=re.IGNORECASE
            )

        return text

    def add_custom_step(self, step_name: str, step_function: Callable[[str], str], template_types: Optional[List[str]] = None) -> None:
        """
        Add a custom preprocessing step

        Args:
            step_name: Name of the step
            step_function: Function that takes a string and returns a processed string
            template_types: Optional list of template types to apply this step to
        """
        if template_types:
            for template_type in template_types:
                if template_type in self.template_steps:
                    self.template_steps[template_type].append(step_function)
                else:
                    self.template_steps[template_type] = self.default_steps + [step_function]
        else:
            # Add to default steps
            self.default_steps.append(step_function)

            # Add to all template steps
            for template_type in self.template_steps:
                self.template_steps[template_type].append(step_function)

        logger.info(f"Added custom preprocessing step: {step_name}")

    def remove_step(self, step_function: Callable[[str], str], template_types: Optional[List[str]] = None) -> None:
        """
        Remove a preprocessing step

        Args:
            step_function: Function to remove
            template_types: Optional list of template types to remove this step from
        """
        if template_types:
            for template_type in template_types:
                if template_type in self.template_steps:
                    if step_function in self.template_steps[template_type]:
                        self.template_steps[template_type].remove(step_function)
        else:
            # Remove from default steps
            if step_function in self.default_steps:
                self.default_steps.remove(step_function)

            # Remove from all template steps
            for template_type in self.template_steps:
                if step_function in self.template_steps[template_type]:
                    self.template_steps[template_type].remove(step_function)

        logger.info(f"Removed preprocessing step: {step_function.__name__}")
"""
Embedding Validator for Quality Assessment

This module provides functionality for validating and assessing the quality
of embeddings generated by the embedding service.
"""

import numpy as np
from typing import List, Dict, Optional, Union, Any, Tuple
import logging
from sklearn.metrics.pairwise import cosine_similarity

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class EmbeddingValidator:
    """
    Embedding validator for quality assessment

    This class provides methods for validating and assessing the quality
    of embeddings generated by the embedding service.
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the embedding validator

        Args:
            config: Optional configuration dictionary
        """
        self.config = config or {}

        # Default thresholds
        self.thresholds = {
            "min_norm": 0.9,
            "max_norm": 1.1,
            "min_variance": 0.01,
            "min_similarity_related": 0.6,
            "max_similarity_unrelated": 0.4
        }

        if "thresholds" in self.config:
            self.thresholds.update(self.config["thresholds"])

    def validate_embedding(self, embedding: List[float]) -> Tuple[bool, Dict[str, Any]]:
        """
        Validate a single embedding

        Args:
            embedding: The embedding vector to validate

        Returns:
            Tuple[bool, Dict[str, Any]]: Tuple of (is_valid, details)
        """
        if not embedding:
            return False, {"error": "Empty embedding"}

        # Convert to numpy array
        embedding_array = np.array(embedding)

        # Calculate metrics
        norm = np.linalg.norm(embedding_array)
        variance = np.var(embedding_array)

        # Check if embedding is valid
        is_valid = True
        details = {
            "norm": norm,
            "variance": variance,
            "issues": []
        }

        # Check norm
        if norm < self.thresholds["min_norm"]:
            is_valid = False
            details["issues"].append(f"Norm too small: {norm} < {self.thresholds['min_norm']}")

        if norm > self.thresholds["max_norm"]:
            is_valid = False
            details["issues"].append(f"Norm too large: {norm} > {self.thresholds['max_norm']}")

        # Check variance
        if variance < self.thresholds["min_variance"]:
            is_valid = False
            details["issues"].append(f"Variance too small: {variance} < {self.thresholds['min_variance']}")

        return is_valid, details

    def compare_embeddings(
        self,
        embedding1: List[float],
        embedding2: List[float]
    ) -> Tuple[float, Dict[str, Any]]:
        """
        Compare two embeddings

        Args:
            embedding1: First embedding vector
            embedding2: Second embedding vector

        Returns:
            Tuple[float, Dict[str, Any]]: Tuple of (similarity, details)
        """
        if not embedding1 or not embedding2:
            return 0.0, {"error": "Empty embedding"}

        # Convert to numpy arrays
        embedding1_array = np.array(embedding1).reshape(1, -1)
        embedding2_array = np.array(embedding2).reshape(1, -1)

        # Calculate similarity
        similarity = cosine_similarity(embedding1_array, embedding2_array)[0][0]

        # Calculate other metrics
        euclidean_distance = np.linalg.norm(embedding1_array - embedding2_array)

        details = {
            "similarity": similarity,
            "euclidean_distance": euclidean_distance
        }

        return similarity, details

    def validate_related_embeddings(
        self,
        embeddings: List[List[float]]
    ) -> Tuple[bool, Dict[str, Any]]:
        """
        Validate a set of embeddings that should be related

        Args:
            embeddings: List of embedding vectors

        Returns:
            Tuple[bool, Dict[str, Any]]: Tuple of (is_valid, details)
        """
        if not embeddings or len(embeddings) < 2:
            return False, {"error": "Not enough embeddings"}

        # Calculate pairwise similarities
        similarities = []
        for i in range(len(embeddings)):
            for j in range(i + 1, len(embeddings)):
                similarity, _ = self.compare_embeddings(embeddings[i], embeddings[j])
                similarities.append(similarity)

        # Calculate metrics
        avg_similarity = np.mean(similarities)
        min_similarity = np.min(similarities)

        # Check if embeddings are valid
        is_valid = min_similarity >= self.thresholds["min_similarity_related"]

        details = {
            "avg_similarity": avg_similarity,
            "min_similarity": min_similarity,
            "similarities": similarities,
            "issues": []
        }

        if not is_valid:
            details["issues"].append(
                f"Minimum similarity too low: {min_similarity} < {self.thresholds['min_similarity_related']}"
            )

        return is_valid, details

    def validate_unrelated_embeddings(
        self,
        embeddings: List[List[float]]
    ) -> Tuple[bool, Dict[str, Any]]:
        """
        Validate a set of embeddings that should be unrelated

        Args:
            embeddings: List of embedding vectors

        Returns:
            Tuple[bool, Dict[str, Any]]: Tuple of (is_valid, details)
        """
        if not embeddings or len(embeddings) < 2:
            return False, {"error": "Not enough embeddings"}

        # Calculate pairwise similarities
        similarities = []
        for i in range(len(embeddings)):
            for j in range(i + 1, len(embeddings)):
                similarity, _ = self.compare_embeddings(embeddings[i], embeddings[j])
                similarities.append(similarity)

        # Calculate metrics
        avg_similarity = np.mean(similarities)
        max_similarity = np.max(similarities)

        # Check if embeddings are valid
        is_valid = max_similarity <= self.thresholds["max_similarity_unrelated"]

        details = {
            "avg_similarity": avg_similarity,
            "max_similarity": max_similarity,
            "similarities": similarities,
            "issues": []
        }

        if not is_valid:
            details["issues"].append(
                f"Maximum similarity too high: {max_similarity} > {self.thresholds['max_similarity_unrelated']}"
            )

        return is_valid, details

    def evaluate_retrieval_quality(
        self,
        query_embedding: List[float],
        relevant_embeddings: List[List[float]],
        irrelevant_embeddings: List[List[float]]
    ) -> Dict[str, Any]:
        """
        Evaluate the quality of retrieval using a query embedding

        Args:
            query_embedding: Query embedding vector
            relevant_embeddings: List of relevant embedding vectors
            irrelevant_embeddings: List of irrelevant embedding vectors

        Returns:
            Dict[str, Any]: Evaluation metrics
        """
        if not query_embedding or not relevant_embeddings or not irrelevant_embeddings:
            return {"error": "Missing embeddings"}

        # Calculate similarities with relevant embeddings
        relevant_similarities = []
        for embedding in relevant_embeddings:
            similarity, _ = self.compare_embeddings(query_embedding, embedding)
            relevant_similarities.append(similarity)

        # Calculate similarities with irrelevant embeddings
        irrelevant_similarities = []
        for embedding in irrelevant_embeddings:
            similarity, _ = self.compare_embeddings(query_embedding, embedding)
            irrelevant_similarities.append(similarity)

        # Calculate metrics
        avg_relevant_similarity = np.mean(relevant_similarities)
        avg_irrelevant_similarity = np.mean(irrelevant_similarities)

        # Calculate precision at k
        k = min(len(relevant_embeddings), 5)
        all_similarities = [(s, True) for s in relevant_similarities] + [(s, False) for s in irrelevant_similarities]
        all_similarities.sort(key=lambda x: x[0], reverse=True)

        precision_at_k = sum(1 for s, is_relevant in all_similarities[:k] if is_relevant) / k

        # Calculate separation
        separation = avg_relevant_similarity - avg_irrelevant_similarity

        return {
            "avg_relevant_similarity": avg_relevant_similarity,
            "avg_irrelevant_similarity": avg_irrelevant_similarity,
            "precision_at_k": precision_at_k,
            "separation": separation,
            "relevant_similarities": relevant_similarities,
            "irrelevant_similarities": irrelevant_similarities
        }

# Create a singleton instance
embedding_validator = EmbeddingValidator()
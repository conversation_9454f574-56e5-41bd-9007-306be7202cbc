"""
Field Weighted Embedder for Enhanced Embedding Generation

This module provides functionality for generating embeddings with field weighting,
allowing different fields to have different importance in the final embedding.
"""

import numpy as np
from typing import List, Dict, Optional, Union, Any, Tuple
import logging
from functools import lru_cache

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Import the embedding service
from .embedding_service import embedding_service

class FieldWeightedEmbedder:
    """
    Field weighted embedder for enhanced embedding generation

    This class provides methods for generating embeddings with field weighting,
    allowing different fields to have different importance in the final embedding.
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the field weighted embedder

        Args:
            config: Optional configuration dictionary
        """
        self.config = config or {}

        # Default field weights for different template types
        self.default_weights = {
            "name": 1.5,
            "title": 1.5,
            "display_name": 1.5,
            "description": 1.2,
            "content": 1.0,
            "summary": 1.0,
            "default": 0.8
        }

        # Template-specific field weights
        self.template_weights = {
            "character": {
                "name": 1.5,
                "full_name": 1.2,
                "personality": 1.8,
                "background": 1.5,
                "appearance": 1.0,
                "motivations": 1.6,
                "gender_identity": 0.8,
                "sexual_orientation": 0.8,
                "default": 0.7
            },
            "location": {
                "name": 1.5,
                "geography": 1.8,
                "climate": 1.5,
                "description": 1.2,
                "history": 1.0,
                "inhabitants": 1.0,
                "default": 0.7
            },
            "item": {
                "name": 1.5,
                "type": 1.2,
                "description": 1.2,
                "properties": 1.8,
                "history": 1.0,
                "usage": 1.5,
                "default": 0.7
            },
            "concept": {
                "name": 1.5,
                "description": 1.8,
                "principles": 1.5,
                "implications": 1.2,
                "examples": 1.0,
                "default": 0.7
            },
            "event": {
                "name": 1.5,
                "description": 1.2,
                "participants": 1.5,
                "location": 1.0,
                "time": 1.0,
                "consequences": 1.8,
                "default": 0.7
            },
            "default": self.default_weights
        }

    def get_field_weight(self, field_name: str, template_type: Optional[str] = None) -> float:
        """
        Get the weight for a specific field

        Args:
            field_name: Name of the field
            template_type: Optional template type

        Returns:
            float: Weight for the field
        """
        # Get weights for template type
        weights = self.template_weights.get(template_type, self.default_weights)

        # Get weight for field
        return weights.get(field_name.lower(), weights.get("default", 1.0))

    def set_field_weight(self, field_name: str, weight: float, template_type: Optional[str] = None) -> None:
        """
        Set the weight for a specific field

        Args:
            field_name: Name of the field
            weight: Weight for the field
            template_type: Optional template type
        """
        if template_type:
            if template_type not in self.template_weights:
                self.template_weights[template_type] = dict(self.default_weights)
            self.template_weights[template_type][field_name.lower()] = weight
        else:
            self.default_weights[field_name.lower()] = weight

        logger.info(f"Set weight for field {field_name} to {weight} for template type {template_type or 'default'}")

    def get_field_embedding(self, field_text: str, instruction: Optional[str] = None) -> List[float]:
        """
        Get embedding for a single field

        Args:
            field_text: Text of the field
            instruction: Optional instruction for embedding generation

        Returns:
            List[float]: Embedding for the field
        """
        if not field_text:
            return []

        if instruction:
            return embedding_service.get_instructor_embedding(field_text, instruction)
        else:
            return embedding_service.get_embedding(field_text)

    @lru_cache(maxsize=128)
    def get_weighted_embedding(
        self,
        fields: Dict[str, str],
        template_type: Optional[str] = None,
        instructions: Optional[Dict[str, str]] = None
    ) -> List[float]:
        """
        Get weighted embedding for multiple fields

        Args:
            fields: Dictionary of field names and values
            template_type: Optional template type
            instructions: Optional dictionary of field-specific instructions

        Returns:
            List[float]: Weighted embedding
        """
        if not fields:
            return []

        # Get embeddings for each field
        field_embeddings = {}
        field_weights = {}

        for field_name, field_text in fields.items():
            if not field_text:
                continue

            # Get instruction for field
            instruction = None
            if instructions and field_name in instructions:
                instruction = instructions[field_name]

            # Get embedding for field
            embedding = self.get_field_embedding(field_text, instruction)

            if not embedding:
                continue

            # Get weight for field
            weight = self.get_field_weight(field_name, template_type)

            field_embeddings[field_name] = embedding
            field_weights[field_name] = weight

        if not field_embeddings:
            return []

        # Combine embeddings with weights
        combined_embedding = self._combine_embeddings(field_embeddings, field_weights)

        return combined_embedding

    def _combine_embeddings(
        self,
        embeddings: Dict[str, List[float]],
        weights: Dict[str, float]
    ) -> List[float]:
        """
        Combine multiple embeddings with weights

        Args:
            embeddings: Dictionary of field names and embeddings
            weights: Dictionary of field names and weights

        Returns:
            List[float]: Combined embedding
        """
        if not embeddings:
            return []

        # Convert embeddings to numpy arrays
        embedding_arrays = {}
        for field_name, embedding in embeddings.items():
            embedding_arrays[field_name] = np.array(embedding)

        # Get embedding dimension
        embedding_dim = next(iter(embedding_arrays.values())).shape[0]

        # Initialize combined embedding
        combined_embedding = np.zeros(embedding_dim)
        total_weight = 0.0

        # Combine embeddings with weights
        for field_name, embedding in embedding_arrays.items():
            weight = weights.get(field_name, 1.0)
            combined_embedding += embedding * weight
            total_weight += weight

        # Normalize by total weight
        if total_weight > 0:
            combined_embedding /= total_weight

        # Normalize to unit length
        norm = np.linalg.norm(combined_embedding)
        if norm > 0:
            combined_embedding /= norm

        return combined_embedding.tolist()

    def get_template_embedding(
        self,
        template: Dict[str, Any],
        template_type: Optional[str] = None,
        custom_weights: Optional[Dict[str, float]] = None,
        custom_instructions: Optional[Dict[str, str]] = None
    ) -> List[float]:
        """
        Get embedding for a template with field weighting

        Args:
            template: Dictionary containing template data
            template_type: Optional template type
            custom_weights: Optional custom weights for fields
            custom_instructions: Optional custom instructions for fields

        Returns:
            List[float]: The embedding vector
        """
        # Extract fields from template
        fields = {}

        # Common fields
        if "name" in template:
            fields["name"] = template["name"]
        elif "template_name" in template:
            fields["name"] = template["template_name"]
        elif "display_name" in template:
            fields["name"] = template["display_name"]

        if "description" in template and template["description"]:
            fields["description"] = template["description"]

        # Template-specific fields
        if template_type == "character":
            if "full_name" in template and template["full_name"]:
                fields["full_name"] = template["full_name"]
            if "personality" in template and template["personality"]:
                fields["personality"] = template["personality"]
            if "background" in template and template["background"]:
                fields["background"] = template["background"]
            if "appearance" in template and template["appearance"]:
                fields["appearance"] = template["appearance"]
            if "motivations" in template and template["motivations"]:
                fields["motivations"] = template["motivations"]
            if "gender_identity" in template and template["gender_identity"]:
                fields["gender_identity"] = template["gender_identity"]
            if "sexual_orientation" in template and template["sexual_orientation"]:
                fields["sexual_orientation"] = template["sexual_orientation"]
        elif template_type == "location":
            if "geography" in template and template["geography"]:
                fields["geography"] = template["geography"]
            if "climate" in template and template["climate"]:
                fields["climate"] = template["climate"]
            if "history" in template and template["history"]:
                fields["history"] = template["history"]
            if "inhabitants" in template and template["inhabitants"]:
                fields["inhabitants"] = template["inhabitants"]
        elif template_type == "item":
            if "type" in template and template["type"]:
                fields["type"] = template["type"]
            if "properties" in template and template["properties"]:
                fields["properties"] = template["properties"]
            if "history" in template and template["history"]:
                fields["history"] = template["history"]
            if "usage" in template and template["usage"]:
                fields["usage"] = template["usage"]
        elif template_type == "concept":
            if "principles" in template and template["principles"]:
                fields["principles"] = template["principles"]
            if "implications" in template and template["implications"]:
                fields["implications"] = template["implications"]
            if "examples" in template and template["examples"]:
                fields["examples"] = template["examples"]
        elif template_type == "event":
            if "participants" in template and template["participants"]:
                fields["participants"] = template["participants"]
            if "location" in template and template["location"]:
                fields["location"] = template["location"]
            if "time" in template and template["time"]:
                fields["time"] = template["time"]
            if "consequences" in template and template["consequences"]:
                fields["consequences"] = template["consequences"]

        # Apply custom weights if provided
        weights = dict(self.template_weights.get(template_type, self.default_weights))
        if custom_weights:
            for field_name, weight in custom_weights.items():
                weights[field_name] = weight

        # Get weighted embedding
        return self.get_weighted_embedding(fields, template_type, custom_instructions)

    def get_element_embedding(
        self,
        element: Dict[str, Any],
        custom_weights: Optional[Dict[str, float]] = None,
        custom_instructions: Optional[Dict[str, str]] = None
    ) -> List[float]:
        """
        Get embedding for an element with field weighting

        Args:
            element: Dictionary containing element data
            custom_weights: Optional custom weights for fields
            custom_instructions: Optional custom instructions for fields

        Returns:
            List[float]: The embedding vector
        """
        # Extract fields from element
        fields = {}

        if "name" in element:
            fields["name"] = element["name"]

        if "category" in element:
            fields["category"] = element["category"]

        if "subcategory" in element and element["subcategory"]:
            fields["subcategory"] = element["subcategory"]

        if "description" in element and element["description"]:
            fields["description"] = element["description"]

        if "attributes" in element and element["attributes"]:
            for key, value in element["attributes"].items():
                if value:
                    fields[key] = value

        # Get template type from element
        template_type = element.get("template_type", "default")

        # Get weighted embedding
        return self.get_weighted_embedding(fields, template_type, custom_instructions)

    def get_character_embedding(
        self,
        character: Dict[str, Any],
        custom_weights: Optional[Dict[str, float]] = None,
        custom_instructions: Optional[Dict[str, str]] = None
    ) -> List[float]:
        """
        Get embedding for a character with field weighting

        Args:
            character: Dictionary containing character data
            custom_weights: Optional custom weights for fields
            custom_instructions: Optional custom instructions for fields

        Returns:
            List[float]: The embedding vector
        """
        # Extract fields from character
        fields = {}

        if "name" in character:
            fields["name"] = character["name"]

        if "full_name" in character and character["full_name"]:
            fields["full_name"] = character["full_name"]

        if "gender_identity" in character and character["gender_identity"]:
            fields["gender_identity"] = character["gender_identity"]

        if "sexual_orientation" in character and character["sexual_orientation"]:
            fields["sexual_orientation"] = character["sexual_orientation"]

        if "personality" in character and character["personality"]:
            fields["personality"] = character["personality"]

        if "background" in character and character["background"]:
            fields["background"] = character["background"]

        if "appearance" in character and character["appearance"]:
            fields["appearance"] = character["appearance"]

        if "motivations" in character and character["motivations"]:
            fields["motivations"] = character["motivations"]

        # Get weighted embedding
        return self.get_weighted_embedding(fields, "character", custom_instructions)

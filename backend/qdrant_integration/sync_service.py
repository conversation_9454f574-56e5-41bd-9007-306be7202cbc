"""
Synchronization Service for PostgreSQL and Qdrant

This module handles the synchronization between PostgreSQL and Qdrant
to ensure data consistency across both databases.
"""

import logging
import json
from typing import Dict, List, Optional, Union, Any
from datetime import datetime
import asyncio
import asyncpg

from .embedding_service import (
    embedding_service,
    get_element_embedding,
    get_character_embedding,
    get_template_embedding,
    get_template_field_embedding
)
from .query_utils import upsert_vectors, delete_vectors

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class SyncService:
    """Service for synchronizing PostgreSQL and Qdrant"""

    def __init__(self, db_config: Dict[str, str]):
        """
        Initialize the sync service

        Args:
            db_config: PostgreSQL database configuration
        """
        self.db_config = db_config
        self.pool = None

    async def initialize(self):
        """Initialize database connection pool"""
        if self.pool is None:
            self.pool = await asyncpg.create_pool(**self.db_config)
            logger.info("Database connection pool initialized")

    async def close(self):
        """Close database connection pool"""
        if self.pool:
            await self.pool.close()
            self.pool = None
            logger.info("Database connection pool closed")

    async def sync_element(self, element_id: int) -> bool:
        """
        Synchronize a single element between PostgreSQL and Qdrant

        Args:
            element_id: ID of the element to synchronize

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            await self.initialize()

            async with self.pool.acquire() as conn:
                # Fetch element data from PostgreSQL
                element = await conn.fetchrow(
                    """
                    SELECT e.*, b.user_id
                    FROM elements e
                    JOIN books b ON e.book_id = b.id
                    WHERE e.id = $1
                    """,
                    element_id
                )

                if not element:
                    logger.warning(f"Element {element_id} not found")
                    return False

                # Convert to dict
                element_dict = dict(element)

                # Generate embedding
                embedding = get_element_embedding(element_dict)

                if not embedding:
                    logger.error(f"Failed to generate embedding for element {element_id}")
                    return False

                # Prepare payload
                payload = {
                    "element_id": element_dict["id"],
                    "book_id": element_dict["book_id"],
                    "name": element_dict["name"],
                    "description": element_dict.get("description", ""),
                    "category": element_dict.get("category", ""),
                    "subcategory": element_dict.get("subcategory", ""),
                    "template_type": element_dict.get("template_type", ""),
                    "attributes": element_dict.get("attributes", {}),
                    "created_at": element_dict.get("created_at", datetime.now()).isoformat(),
                    "updated_at": element_dict.get("updated_at", datetime.now()).isoformat(),
                    "parent_id": element_dict.get("parent_id"),
                    "tags": element_dict.get("tags", [])
                }

                # Determine collection name
                user_id = element_dict["user_id"]
                collection_name = f"user_{user_id}_elements"

                # Upsert to Qdrant
                success = upsert_vectors(
                    collection_name=collection_name,
                    vectors=[embedding],
                    ids=[element_id],
                    payloads=[payload]
                )

                if success:
                    logger.info(f"Successfully synchronized element {element_id}")
                    return True
                else:
                    logger.error(f"Failed to upsert element {element_id} to Qdrant")
                    return False

        except Exception as e:
            logger.error(f"Error synchronizing element {element_id}: {str(e)}")
            return False

    async def sync_character(self, character_id: int) -> bool:
        """
        Synchronize a single character between PostgreSQL and Qdrant

        Args:
            character_id: ID of the character to synchronize

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            await self.initialize()

            async with self.pool.acquire() as conn:
                # Fetch character data from PostgreSQL
                character = await conn.fetchrow(
                    """
                    SELECT c.*, b.user_id
                    FROM characters c
                    JOIN books b ON c.book_id = b.id
                    WHERE c.id = $1
                    """,
                    character_id
                )

                if not character:
                    logger.warning(f"Character {character_id} not found")
                    return False

                # Convert to dict
                character_dict = dict(character)

                # Generate embedding
                embedding = get_character_embedding(character_dict)

                if not embedding:
                    logger.error(f"Failed to generate embedding for character {character_id}")
                    return False

                # Prepare payload
                payload = {
                    "character_id": character_dict["id"],
                    "book_id": character_dict["book_id"],
                    "name": character_dict["name"],
                    "full_name": character_dict.get("full_name", ""),
                    "age": character_dict.get("age"),
                    "gender_identity": character_dict.get("gender_identity", ""),
                    "sexual_orientation": character_dict.get("sexual_orientation", ""),
                    "personality": character_dict.get("personality", ""),
                    "background": character_dict.get("background", ""),
                    "appearance": character_dict.get("appearance", ""),
                    "motivations": character_dict.get("motivations", ""),
                    "created_at": character_dict.get("created_at", datetime.now()).isoformat(),
                    "updated_at": character_dict.get("updated_at", datetime.now()).isoformat(),
                    "tags": character_dict.get("tags", [])
                }

                # Determine collection name
                user_id = character_dict["user_id"]
                collection_name = f"user_{user_id}_characters"

                # Upsert to Qdrant
                success = upsert_vectors(
                    collection_name=collection_name,
                    vectors=[embedding],
                    ids=[character_id],
                    payloads=[payload]
                )

                if success:
                    logger.info(f"Successfully synchronized character {character_id}")
                    return True
                else:
                    logger.error(f"Failed to upsert character {character_id} to Qdrant")
                    return False

        except Exception as e:
            logger.error(f"Error synchronizing character {character_id}: {str(e)}")
            return False

    async def delete_element(self, element_id: int, user_id: int) -> bool:
        """
        Delete an element from Qdrant

        Args:
            element_id: ID of the element to delete
            user_id: ID of the user who owns the element

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Determine collection name
            collection_name = f"user_{user_id}_elements"

            # Delete from Qdrant
            success = delete_vectors(
                collection_name=collection_name,
                ids=[element_id]
            )

            if success:
                logger.info(f"Successfully deleted element {element_id} from Qdrant")
                return True
            else:
                logger.error(f"Failed to delete element {element_id} from Qdrant")
                return False

        except Exception as e:
            logger.error(f"Error deleting element {element_id}: {str(e)}")
            return False

    async def delete_character(self, character_id: int, user_id: int) -> bool:
        """
        Delete a character from Qdrant

        Args:
            character_id: ID of the character to delete
            user_id: ID of the user who owns the character

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Determine collection name
            collection_name = f"user_{user_id}_characters"

            # Delete from Qdrant
            success = delete_vectors(
                collection_name=collection_name,
                ids=[character_id]
            )

            if success:
                logger.info(f"Successfully deleted character {character_id} from Qdrant")
                return True
            else:
                logger.error(f"Failed to delete character {character_id} from Qdrant")
                return False

        except Exception as e:
            logger.error(f"Error deleting character {character_id}: {str(e)}")
            return False

    async def sync_template(self, template_id: str) -> bool:
        """
        Synchronize a single template between PostgreSQL and Qdrant

        Args:
            template_id: ID of the template to synchronize

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            await self.initialize()

            async with self.pool.acquire() as conn:
                # Fetch template data from PostgreSQL
                template = await conn.fetchrow(
                    """
                    SELECT t.*, c.name as category_name, c.aspect_type as category_aspect_type
                    FROM element_templates t
                    JOIN world_aspect_categories c ON t.category_id = c.category_id
                    WHERE t.template_id = $1
                    """,
                    template_id
                )

                if not template:
                    logger.warning(f"Template {template_id} not found")
                    return False

                # Convert to dict
                template_dict = dict(template)

                # Generate embedding
                embedding = get_template_embedding(template_dict)

                if not embedding:
                    logger.error(f"Failed to generate embedding for template {template_id}")
                    return False

                # Prepare payload
                payload = {
                    "template_id": template_dict["template_id"],
                    "template_name": template_dict["template_name"],
                    "display_name": template_dict["display_name"],
                    "category_id": template_dict["category_id"],
                    "category_name": template_dict.get("category_name", ""),
                    "category_aspect_type": template_dict.get("category_aspect_type", ""),
                    "parent_template_id": template_dict.get("parent_template_id"),
                    "description": template_dict.get("description", ""),
                    "field_definitions": template_dict.get("field_definitions", {}),
                    "relationship_definitions": template_dict.get("relationship_definitions", {}),
                    "icon": template_dict.get("icon", ""),
                    "version": template_dict.get("version", "1.0"),
                    "source_template_id": template_dict.get("source_template_id"),
                    "creator_id": template_dict.get("creator_id"),
                    "is_system_template": template_dict.get("is_system_template", True),
                    "created_at": template_dict.get("created_at", datetime.now()).isoformat(),
                    "updated_at": template_dict.get("updated_at", datetime.now()).isoformat()
                }

                # Determine collection name - templates are shared across all users
                collection_name = "templates"

                # Upsert to Qdrant
                success = upsert_vectors(
                    collection_name=collection_name,
                    vectors=[embedding],
                    ids=[template_id],
                    payloads=[payload]
                )

                if success:
                    logger.info(f"Successfully synchronized template {template_id}")

                    # Also sync template fields for more granular search
                    await self.sync_template_fields(template_dict)

                    return True
                else:
                    logger.error(f"Failed to upsert template {template_id} to Qdrant")
                    return False

        except Exception as e:
            logger.error(f"Error synchronizing template {template_id}: {str(e)}")
            return False

    async def sync_template_fields(self, template: Dict[str, Any]) -> bool:
        """
        Synchronize the fields of a template to Qdrant

        Args:
            template: Dictionary containing template data

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            template_id = template["template_id"]
            field_defs = template.get("field_definitions", {})

            # Handle both string and dict formats
            if isinstance(field_defs, str):
                try:
                    field_defs = json.loads(field_defs)
                except json.JSONDecodeError:
                    logger.warning(f"Failed to parse field_definitions as JSON: {field_defs}")
                    return False

            # Collect all fields
            all_fields = []
            field_ids = []

            for section, fields in field_defs.items():
                for field in fields:
                    if isinstance(field, dict) and "id" in field:
                        # Add template context to field
                        field["template_id"] = template_id
                        field["template_name"] = template.get("template_name", "")
                        field["template_display_name"] = template.get("display_name", "")
                        field["section"] = section

                        all_fields.append(field)
                        field_ids.append(f"{template_id}_{field['id']}")

            if not all_fields:
                logger.info(f"No fields found for template {template_id}")
                return True

            # Generate embeddings for all fields
            embeddings = []
            for field in all_fields:
                embedding = get_template_field_embedding(field)
                if embedding:
                    embeddings.append(embedding)
                else:
                    logger.warning(f"Failed to generate embedding for field {field.get('id')} in template {template_id}")
                    return False

            # Determine collection name - template fields are shared across all users
            collection_name = "template_fields"

            # Upsert to Qdrant
            success = upsert_vectors(
                collection_name=collection_name,
                vectors=embeddings,
                ids=field_ids,
                payloads=all_fields
            )

            if success:
                logger.info(f"Successfully synchronized {len(all_fields)} fields for template {template_id}")
                return True
            else:
                logger.error(f"Failed to upsert fields for template {template_id} to Qdrant")
                return False

        except Exception as e:
            logger.error(f"Error synchronizing template fields: {str(e)}")
            return False

    async def delete_template(self, template_id: str) -> bool:
        """
        Delete a template from Qdrant

        Args:
            template_id: ID of the template to delete

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Determine collection name
            collection_name = "templates"

            # Delete from Qdrant
            success = delete_vectors(
                collection_name=collection_name,
                ids=[template_id]
            )

            if success:
                logger.info(f"Successfully deleted template {template_id} from Qdrant")

                # Also delete template fields
                await self.delete_template_fields(template_id)

                return True
            else:
                logger.error(f"Failed to delete template {template_id} from Qdrant")
                return False

        except Exception as e:
            logger.error(f"Error deleting template {template_id}: {str(e)}")
            return False

    async def delete_template_fields(self, template_id: str) -> bool:
        """
        Delete all fields for a template from Qdrant

        Args:
            template_id: ID of the template whose fields should be deleted

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # We need to query Qdrant to find all field IDs for this template
            # For now, we'll use a simple filter to find all fields with this template_id
            # In a real implementation, you would use the Qdrant API to query for these fields

            # This is a placeholder - in a real implementation, you would query Qdrant
            # to get the actual field IDs for this template

            # For now, we'll just log a message
            logger.info(f"Would delete all fields for template {template_id} from Qdrant")
            return True

        except Exception as e:
            logger.error(f"Error deleting template fields for {template_id}: {str(e)}")
            return False

"""
Synchronization Transaction for PostgreSQL and Qdrant

This module provides transaction-like synchronization between PostgreSQL and Qdrant
to ensure data consistency across both databases.
"""

import logging
import json
import asyncio
import asyncpg
from typing import Dict, List, Optional, Union, Any
from datetime import datetime

from .enhanced_collection_manager import EnhancedCollectionManager
from .embedding_service import (
    embedding_service,
    get_element_embedding,
    get_character_embedding,
    get_template_embedding
)
from .query_utils import upsert_vectors, delete_vectors

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class SyncTransaction:
    """Transaction-like synchronization between PostgreSQL and Qdrant"""

    def __init__(self, db_config: Dict[str, str]):
        """
        Initialize the sync transaction

        Args:
            db_config: PostgreSQL database configuration
        """
        self.db_config = db_config
        self.pool = None
        self.collection_manager = EnhancedCollectionManager()
        self.pending_operations = []
        self.executed_operations = []
        self.status = "initialized"

    async def initialize(self):
        """Initialize database connection pool"""
        if self.pool is None:
            self.pool = await asyncpg.create_pool(**self.db_config)
            logger.info("Database connection pool initialized")

        # Ensure shared collections exist
        if not self.collection_manager.ensure_shared_collections():
            logger.warning("Failed to ensure shared collections exist")

    async def close(self):
        """Close database connection pool"""
        if self.pool:
            await self.pool.close()
            self.pool = None
            logger.info("Database connection pool closed")

    async def add_operation(self, operation_type: str, entity_type: str, entity_id: Union[int, str], user_id: Optional[int] = None):
        """
        Add an operation to the transaction

        Args:
            operation_type: Type of operation ("upsert" or "delete")
            entity_type: Type of entity ("element", "character", "template", etc.)
            entity_id: ID of the entity
            user_id: ID of the user who owns the entity (required for elements and characters)
        """
        self.pending_operations.append({
            "type": operation_type,
            "entity_type": entity_type,
            "entity_id": entity_id,
            "user_id": user_id,
            "status": "pending",
            "timestamp": datetime.now().isoformat()
        })

        logger.info(f"Added {operation_type} operation for {entity_type} {entity_id}")

    async def execute(self) -> bool:
        """
        Execute all pending operations

        Returns:
            bool: True if all operations were successful, False otherwise
        """
        if not self.pending_operations:
            logger.info("No pending operations to execute")
            return True

        await self.initialize()
        self.status = "executing"
        success = True

        for operation in self.pending_operations:
            try:
                if operation["type"] == "upsert":
                    if operation["entity_type"] == "element":
                        result = await self._sync_element(operation["entity_id"])
                    elif operation["entity_type"] == "character":
                        result = await self._sync_character(operation["entity_id"])
                    elif operation["entity_type"] == "template":
                        result = await self._sync_template(operation["entity_id"])
                    else:
                        logger.warning(f"Unknown entity type: {operation['entity_type']}")
                        result = False
                elif operation["type"] == "delete":
                    if operation["entity_type"] == "element":
                        result = await self._delete_element(operation["entity_id"], operation["user_id"])
                    elif operation["entity_type"] == "character":
                        result = await self._delete_character(operation["entity_id"], operation["user_id"])
                    elif operation["entity_type"] == "template":
                        result = await self._delete_template(operation["entity_id"])
                    else:
                        logger.warning(f"Unknown entity type: {operation['entity_type']}")
                        result = False
                else:
                    logger.warning(f"Unknown operation type: {operation['type']}")
                    result = False

                operation["status"] = "success" if result else "failed"
                operation["completed_at"] = datetime.now().isoformat()

                if result:
                    self.executed_operations.append(operation)
                else:
                    success = False
                    logger.error(f"Operation failed: {operation}")
            except Exception as e:
                operation["status"] = "failed"
                operation["error"] = str(e)
                operation["completed_at"] = datetime.now().isoformat()
                success = False
                logger.error(f"Error executing operation {operation}: {str(e)}")

        self.pending_operations = [op for op in self.pending_operations if op["status"] == "pending"]
        self.status = "completed" if success else "failed"

        return success

    async def rollback(self) -> bool:
        """
        Rollback executed operations

        Returns:
            bool: True if all rollbacks were successful, False otherwise
        """
        if not self.executed_operations:
            logger.info("No executed operations to rollback")
            return True

        self.status = "rolling_back"
        success = True

        # Process operations in reverse order
        for operation in reversed(self.executed_operations):
            try:
                if operation["type"] == "upsert":
                    # For upserts, we delete
                    if operation["entity_type"] == "element":
                        result = await self._delete_element(operation["entity_id"], operation["user_id"])
                    elif operation["entity_type"] == "character":
                        result = await self._delete_character(operation["entity_id"], operation["user_id"])
                    elif operation["entity_type"] == "template":
                        result = await self._delete_template(operation["entity_id"])
                    else:
                        logger.warning(f"Unknown entity type for rollback: {operation['entity_type']}")
                        result = False
                elif operation["type"] == "delete":
                    # For deletes, we would need to restore from PostgreSQL
                    # This is more complex and would require additional implementation
                    logger.warning(f"Rollback for delete operations not fully implemented")
                    result = False
                else:
                    logger.warning(f"Unknown operation type for rollback: {operation['type']}")
                    result = False

                if not result:
                    success = False
                    logger.error(f"Rollback failed for operation: {operation}")
            except Exception as e:
                success = False
                logger.error(f"Error rolling back operation {operation}: {str(e)}")

        self.executed_operations = []
        self.status = "rolled_back" if success else "rollback_failed"

        return success

    async def _sync_element(self, element_id: int) -> bool:
        """
        Synchronize a single element between PostgreSQL and Qdrant

        Args:
            element_id: ID of the element to synchronize

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            async with self.pool.acquire() as conn:
                # Fetch element data from PostgreSQL
                element = await conn.fetchrow(
                    """
                    SELECT e.*, b.user_id
                    FROM elements e
                    JOIN books b ON e.book_id = b.id
                    WHERE e.id = $1
                    """,
                    element_id
                )

                if not element:
                    logger.warning(f"Element {element_id} not found")
                    return False

                # Convert to dict
                element_dict = dict(element)

                # Ensure user collection exists
                user_id = element_dict["user_id"]
                if not self.collection_manager.ensure_user_collections(user_id):
                    logger.error(f"Failed to ensure collections for user {user_id}")
                    return False

                # Generate embedding
                embedding = get_element_embedding(element_dict)

                if not embedding:
                    logger.error(f"Failed to generate embedding for element {element_id}")
                    return False

                # Prepare payload
                payload = {
                    "element_id": element_dict["id"],
                    "book_id": element_dict["book_id"],
                    "name": element_dict["name"],
                    "description": element_dict.get("description", ""),
                    "category": element_dict.get("category", ""),
                    "subcategory": element_dict.get("subcategory", ""),
                    "template_type": element_dict.get("template_type", ""),
                    "attributes": element_dict.get("attributes", {}),
                    "created_at": element_dict.get("created_at", datetime.now()).isoformat(),
                    "updated_at": element_dict.get("updated_at", datetime.now()).isoformat(),
                    "parent_id": element_dict.get("parent_id"),
                    "tags": element_dict.get("tags", [])
                }

                # Determine collection name
                collection_name = f"user_{user_id}_elements"

                # Upsert to Qdrant
                success = upsert_vectors(
                    collection_name=collection_name,
                    vectors=[embedding],
                    ids=[element_id],
                    payloads=[payload]
                )

                if success:
                    logger.info(f"Successfully synchronized element {element_id}")
                    return True
                else:
                    logger.error(f"Failed to upsert element {element_id} to Qdrant")
                    return False

        except Exception as e:
            logger.error(f"Error synchronizing element {element_id}: {str(e)}")
            return False

    async def _sync_character(self, character_id: int) -> bool:
        """
        Synchronize a single character between PostgreSQL and Qdrant

        Args:
            character_id: ID of the character to synchronize

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            async with self.pool.acquire() as conn:
                # Fetch character data from PostgreSQL
                character = await conn.fetchrow(
                    """
                    SELECT c.*, b.user_id
                    FROM characters c
                    JOIN books b ON c.book_id = b.id
                    WHERE c.id = $1
                    """,
                    character_id
                )

                if not character:
                    logger.warning(f"Character {character_id} not found")
                    return False

                # Convert to dict
                character_dict = dict(character)

                # Ensure user collection exists
                user_id = character_dict["user_id"]
                if not self.collection_manager.ensure_user_collections(user_id):
                    logger.error(f"Failed to ensure collections for user {user_id}")
                    return False

                # Generate embedding
                embedding = get_character_embedding(character_dict)

                if not embedding:
                    logger.error(f"Failed to generate embedding for character {character_id}")
                    return False

                # Prepare payload
                payload = {
                    "character_id": character_dict["id"],
                    "book_id": character_dict["book_id"],
                    "name": character_dict["name"],
                    "full_name": character_dict.get("full_name", ""),
                    "age": character_dict.get("age"),
                    "gender_identity": character_dict.get("gender_identity", ""),
                    "sexual_orientation": character_dict.get("sexual_orientation", ""),
                    "personality": character_dict.get("personality", ""),
                    "background": character_dict.get("background", ""),
                    "appearance": character_dict.get("appearance", ""),
                    "motivations": character_dict.get("motivations", ""),
                    "created_at": character_dict.get("created_at", datetime.now()).isoformat(),
                    "updated_at": character_dict.get("updated_at", datetime.now()).isoformat(),
                    "tags": character_dict.get("tags", [])
                }

                # Determine collection name
                collection_name = f"user_{user_id}_characters"

                # Upsert to Qdrant
                success = upsert_vectors(
                    collection_name=collection_name,
                    vectors=[embedding],
                    ids=[character_id],
                    payloads=[payload]
                )

                if success:
                    logger.info(f"Successfully synchronized character {character_id}")
                    return True
                else:
                    logger.error(f"Failed to upsert character {character_id} to Qdrant")
                    return False

        except Exception as e:
            logger.error(f"Error synchronizing character {character_id}: {str(e)}")
            return False

    async def _sync_template(self, template_id: str) -> bool:
        """
        Synchronize a single template between PostgreSQL and Qdrant

        Args:
            template_id: ID of the template to synchronize

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            async with self.pool.acquire() as conn:
                # Fetch template data from PostgreSQL
                template = await conn.fetchrow(
                    """
                    SELECT t.*, c.name as category_name, c.aspect_type as category_aspect_type
                    FROM element_templates t
                    JOIN world_aspect_categories c ON t.category_id = c.category_id
                    WHERE t.template_id = $1
                    """,
                    template_id
                )

                if not template:
                    logger.warning(f"Template {template_id} not found")
                    return False

                # Convert to dict
                template_dict = dict(template)

                # Ensure shared collections exist
                if not self.collection_manager.ensure_shared_collections():
                    logger.error("Failed to ensure shared collections exist")
                    return False

                # Generate embedding
                embedding = get_template_embedding(template_dict)

                if not embedding:
                    logger.error(f"Failed to generate embedding for template {template_id}")
                    return False

                # Prepare payload
                payload = {
                    "template_id": template_dict["template_id"],
                    "template_name": template_dict["template_name"],
                    "display_name": template_dict["display_name"],
                    "category_id": template_dict["category_id"],
                    "category_name": template_dict.get("category_name", ""),
                    "category_aspect_type": template_dict.get("category_aspect_type", ""),
                    "parent_template_id": template_dict.get("parent_template_id"),
                    "description": template_dict.get("description", ""),
                    "field_definitions": template_dict.get("field_definitions", {}),
                    "relationship_definitions": template_dict.get("relationship_definitions", {}),
                    "icon": template_dict.get("icon", ""),
                    "version": template_dict.get("version", "1.0"),
                    "source_template_id": template_dict.get("source_template_id"),
                    "creator_id": template_dict.get("creator_id"),
                    "is_system_template": template_dict.get("is_system_template", True),
                    "created_at": template_dict.get("created_at", datetime.now()).isoformat(),
                    "updated_at": template_dict.get("updated_at", datetime.now()).isoformat()
                }

                # Determine collection name - templates are shared across all users
                collection_name = "templates"

                # Upsert to Qdrant
                success = upsert_vectors(
                    collection_name=collection_name,
                    vectors=[embedding],
                    ids=[template_id],
                    payloads=[payload]
                )

                if success:
                    logger.info(f"Successfully synchronized template {template_id}")
                    return True
                else:
                    logger.error(f"Failed to upsert template {template_id} to Qdrant")
                    return False

        except Exception as e:
            logger.error(f"Error synchronizing template {template_id}: {str(e)}")
            return False

    async def _delete_element(self, element_id: int, user_id: int) -> bool:
        """
        Delete an element from Qdrant

        Args:
            element_id: ID of the element to delete
            user_id: ID of the user who owns the element

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Determine collection name
            collection_name = f"user_{user_id}_elements"

            # Delete from Qdrant
            success = delete_vectors(
                collection_name=collection_name,
                ids=[element_id]
            )

            if success:
                logger.info(f"Successfully deleted element {element_id} from Qdrant")
                return True
            else:
                logger.error(f"Failed to delete element {element_id} from Qdrant")
                return False

        except Exception as e:
            logger.error(f"Error deleting element {element_id}: {str(e)}")
            return False

    async def _delete_character(self, character_id: int, user_id: int) -> bool:
        """
        Delete a character from Qdrant

        Args:
            character_id: ID of the character to delete
            user_id: ID of the user who owns the character

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Determine collection name
            collection_name = f"user_{user_id}_characters"

            # Delete from Qdrant
            success = delete_vectors(
                collection_name=collection_name,
                ids=[character_id]
            )

            if success:
                logger.info(f"Successfully deleted character {character_id} from Qdrant")
                return True
            else:
                logger.error(f"Failed to delete character {character_id} from Qdrant")
                return False

        except Exception as e:
            logger.error(f"Error deleting character {character_id}: {str(e)}")
            return False

    async def _delete_template(self, template_id: str) -> bool:
        """
        Delete a template from Qdrant

        Args:
            template_id: ID of the template to delete

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Determine collection name
            collection_name = "templates"

            # Delete from Qdrant
            success = delete_vectors(
                collection_name=collection_name,
                ids=[template_id]
            )

            if success:
                logger.info(f"Successfully deleted template {template_id} from Qdrant")
                return True
            else:
                logger.error(f"Failed to delete template {template_id} from Qdrant")
                return False

        except Exception as e:
            logger.error(f"Error deleting template {template_id}: {str(e)}")
            return False

"""
Monitoring utilities for Qdrant integration

This module provides monitoring capabilities for the Qdrant vector database
integration, including metrics collection and health checks.
"""

import os
import time
import logging
import requests
import json
from typing import Dict, List, Optional, Union, Any
from prometheus_client import Counter, Gauge, Histogram, start_http_server

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Configuration
QDRANT_URL = os.getenv("QDRANT_URL", "http://localhost:6333")
API_KEY = os.getenv("QDRANT_API_KEY", "Qf1wtuJVTItWgMg4GhKh4Khpm/dmmfbnhgIj2U5Qxck=")
HEADERS = {
    "Content-Type": "application/json",
    "api-key": API_KEY
}

# Define metrics
SEARCH_COUNTER = Counter('qdrant_searches_total', 'Total number of vector searches', ['collection', 'user_id'])
UPSERT_COUNTER = Counter('qdrant_upserts_total', 'Total number of vector upserts', ['collection', 'user_id'])
DELETE_COUNTER = Counter('qdrant_deletes_total', 'Total number of vector deletions', ['collection', 'user_id'])
COLLECTION_SIZE = Gauge('qdrant_collection_size', 'Number of vectors in collection', ['collection'])
SEARCH_LATENCY = Histogram('qdrant_search_latency_seconds', 'Search latency in seconds', ['collection'])
UPSERT_LATENCY = Histogram('qdrant_upsert_latency_seconds', 'Upsert latency in seconds', ['collection'])
EMBEDDING_LATENCY = Histogram('qdrant_embedding_latency_seconds', 'Embedding generation latency in seconds', ['model'])
QDRANT_UP = Gauge('qdrant_up', 'Whether Qdrant is up (1) or down (0)')

def start_metrics_server(port: int = 8000):
    """
    Start Prometheus metrics server
    
    Args:
        port: Port to listen on
    """
    try:
        start_http_server(port)
        logger.info(f"Started metrics server on port {port}")
    except Exception as e:
        logger.error(f"Failed to start metrics server: {str(e)}")

def check_qdrant_health() -> bool:
    """
    Check if Qdrant is healthy
    
    Returns:
        bool: True if healthy, False otherwise
    """
    try:
        response = requests.get(
            f"{QDRANT_URL}/readiness",
            headers=HEADERS
        )
        
        is_healthy = response.status_code == 200
        QDRANT_UP.set(1 if is_healthy else 0)
        return is_healthy
    except Exception as e:
        logger.error(f"Qdrant health check failed: {str(e)}")
        QDRANT_UP.set(0)
        return False

def update_collection_metrics():
    """Update metrics for all collections"""
    try:
        # Get all collections
        response = requests.get(
            f"{QDRANT_URL}/collections",
            headers=HEADERS
        )
        
        if response.status_code != 200:
            logger.error(f"Failed to get collections: {response.text}")
            return
            
        collections = response.json().get("result", {}).get("collections", [])
        
        # Update metrics for each collection
        for collection in collections:
            collection_name = collection["name"]
            
            # Get collection info
            info_response = requests.get(
                f"{QDRANT_URL}/collections/{collection_name}",
                headers=HEADERS
            )
            
            if info_response.status_code != 200:
                logger.error(f"Failed to get info for collection {collection_name}: {info_response.text}")
                continue
                
            info = info_response.json().get("result", {})
            
            # Get vector count
            count_response = requests.post(
                f"{QDRANT_URL}/collections/{collection_name}/points/count",
                headers=HEADERS,
                data=json.dumps({})
            )
            
            if count_response.status_code != 200:
                logger.error(f"Failed to get count for collection {collection_name}: {count_response.text}")
                continue
                
            count = count_response.json().get("result", {}).get("count", 0)
            
            # Update gauge
            COLLECTION_SIZE.labels(collection=collection_name).set(count)
            
    except Exception as e:
        logger.error(f"Error updating collection metrics: {str(e)}")

def track_search(collection: str, user_id: int, latency: float):
    """
    Track a search operation
    
    Args:
        collection: Collection name
        user_id: User ID
        latency: Search latency in seconds
    """
    SEARCH_COUNTER.labels(collection=collection, user_id=user_id).inc()
    SEARCH_LATENCY.labels(collection=collection).observe(latency)

def track_upsert(collection: str, user_id: int, count: int, latency: float):
    """
    Track an upsert operation
    
    Args:
        collection: Collection name
        user_id: User ID
        count: Number of vectors upserted
        latency: Upsert latency in seconds
    """
    UPSERT_COUNTER.labels(collection=collection, user_id=user_id).inc(count)
    UPSERT_LATENCY.labels(collection=collection).observe(latency)

def track_delete(collection: str, user_id: int, count: int):
    """
    Track a delete operation
    
    Args:
        collection: Collection name
        user_id: User ID
        count: Number of vectors deleted
    """
    DELETE_COUNTER.labels(collection=collection, user_id=user_id).inc(count)

def track_embedding_generation(model: str, latency: float):
    """
    Track embedding generation
    
    Args:
        model: Model name
        latency: Generation latency in seconds
    """
    EMBEDDING_LATENCY.labels(model=model).observe(latency)

# Decorator for tracking search operations
def track_search_operation(func):
    """Decorator to track search operations"""
    def wrapper(user_id, collection_name, *args, **kwargs):
        start_time = time.time()
        result = func(user_id, collection_name, *args, **kwargs)
        latency = time.time() - start_time
        track_search(collection_name, user_id, latency)
        return result
    return wrapper

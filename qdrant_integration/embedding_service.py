"""
Embedding service for generating embeddings from text.
"""
import numpy as np
import logging
import hashlib
from typing import List, Dict, Any, Optional, Union

logger = logging.getLogger(__name__)

def get_embedding(text: str, model: str = "default") -> List[float]:
    """
    Generates an embedding for the given text.
    
    Args:
        text: The text to embed
        model: The model to use for embedding
        
    Returns:
        A list of floats representing the embedding
    """
    try:
        # For now, we'll generate a deterministic pseudo-embedding based on the text
        # In a real implementation, you would use a proper embedding model
        
        # Create a hash of the text
        text_hash = hashlib.md5(text.encode()).hexdigest()
        
        # Use the hash to seed a random number generator
        np.random.seed(int(text_hash, 16) % (2**32))
        
        # Generate a random embedding vector
        embedding = np.random.normal(0, 1, 1536)  # 1536 dimensions like OpenAI embeddings
        
        # Normalize the embedding
        embedding = embedding / np.linalg.norm(embedding)
        
        return embedding.tolist()
    except Exception as e:
        logger.error(f"Error generating embedding: {str(e)}")
        # Return a zero vector as fallback
        return [0.0] * 1536

def get_template_embedding(template_data: Dict[str, Any], field_weights: Optional[Dict[str, float]] = None) -> List[float]:
    """
    Generates an embedding for a template by combining field embeddings with weights.
    
    Args:
        template_data: Dictionary containing template data
        field_weights: Optional dictionary mapping field IDs to weights
        
    Returns:
        A list of floats representing the embedding
    """
    try:
        # Extract text from template fields
        text_parts = []
        
        # Add template name and description
        if "display_name" in template_data:
            text_parts.append(template_data["display_name"])
        
        if "description" in template_data:
            text_parts.append(template_data["description"])
        
        # Add field values
        if "field_definitions" in template_data:
            for section, fields in template_data["field_definitions"].items():
                for field in fields:
                    if "value" in field and field["value"]:
                        # Apply field weight if available
                        weight = 1.0
                        if field_weights and field["id"] in field_weights:
                            weight = field_weights[field["id"]]
                        
                        # Add the field value with its weight
                        text_parts.append(f"{field['label']}: {field['value']} " * int(weight * 10))
        
        # Combine all text parts
        combined_text = " ".join(text_parts)
        
        # Generate embedding for the combined text
        return get_embedding(combined_text)
    except Exception as e:
        logger.error(f"Error generating template embedding: {str(e)}")
        # Return a zero vector as fallback
        return [0.0] * 1536

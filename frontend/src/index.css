/* frontend/src/index.css */
body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: var(--background-secondary);
  color: var(--text-primary);
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Custom scrollbar styles */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--background-secondary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--button-secondary-background);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--button-secondary-hover);
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New', monospace;
}

/* Common container styles */
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: auto;
  background: var(--background-primary);
}

/* Common button styles */
.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  color: white;
  transition: background 0.2s;
}

.btn-primary {
  background: var(--button-primary-background);
}

.btn-primary:hover {
  background: var(--button-primary-hover);
}

.btn-success {
  background: var(--button-success-background);
}

.btn-success:hover {
  background: var(--button-success-hover);
}

.btn-danger {
  background: var(--button-danger-background);
}

.btn-danger:hover {
  background: var(--button-danger-hover);
}

.btn-secondary {
  background: var(--button-secondary-background);
}

.btn-secondary:hover {
  background: var(--button-secondary-hover);
}

/* Cancel button style - blue with white text */
.cancel-button {
  background-color: var(--button-primary-background);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
}

.cancel-button:hover {
  background-color: var(--button-primary-hover);
}

.cancel-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

/* Common input styles */
.input {
  padding: 8px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  font-size: 14px;
  background-color: var(--background-primary);
  color: var(--text-primary);
}

.input:focus {
  outline: none;
  border-color: var(--primary);
}

/* Common textarea styles */
.textarea {
  padding: 8px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  font-size: 14px;
  resize: vertical;
  background-color: var(--background-primary);
  color: var(--text-primary);
}

/* Common modal styles */
.modal {
  position: absolute;
  background: var(--background-primary);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  padding: 10px;
  z-index: 10;
  box-shadow: var(--shadow-md);
  color: var(--text-primary);
}
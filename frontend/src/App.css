/* frontend/src/App.css */
.App {
  display: flex;
  justify-content: center;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
}

.main-container {
  width: 100vw; /* Full screen width */
  height: 100vh; /* Full screen height */
  position: relative;
  display: flex;
  flex-direction: column; /* Stack TitleBar and content */
  background-color: var(--background-primary);
  color: var(--text-primary);
  transition: background-color 0.3s ease, color 0.3s ease;
}

.content {
  flex: 1; /* Take remaining space after TitleBar */
  padding: 0;
  overflow: hidden; /* Prevent scrollbars */
  position: relative; /* For absolute positioning of children */
  background-color: var(--background-primary);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px;
  height: 50px;
  background: var(--header-background); /* Use theme variable */
  border-bottom: 1px solid var(--border-color);
  box-shadow: var(--shadow-md);
  color: var(--text-primary);
}

.buttons {
  display: flex;
  gap: 15px; /* Tighten spacing for circles */
}

.logout-button {
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.theme-dark .logout-button {
  background-color: #BB86FC;
}
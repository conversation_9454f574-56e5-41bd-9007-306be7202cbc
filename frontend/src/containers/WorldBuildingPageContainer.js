// frontend/src/containers/WorldBuildingPageContainer.js
import React, { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useParams, useNavigate } from 'react-router-dom';
import {
  fetchWorldBuildingData,
  updateCustomization,
  resetCustomizations,
  createWorldElement,
  setActiveTab,
  setActiveCategory,
  setViewMode,
  setColumnTab,
  setSelectedTemplate,
  toggleMapMode,
  toggleRelationshipMode,
  selectElement,
  clearSelection,
  selectWorldBuildingState,
  selectVisibleCategories,
  selectAllCategories,
  selectAllCustomizations,
  selectActiveCategory,
  selectSelectedElements,
  selectRootElementsForCategory,
  selectChildElements,
  selectElementRelationships,
  selectAllElementsForRelationshipView,
  selectAllRelationships,
  selectColumnTab,
  selectSelectedTemplateId,
  fetchChildElementsThunk,
  fetchElementsForCategoryThunk,
  updateWorldElementThunk,
  deleteWorldElementThunk,
  fetchAllElementsWithChildrenThunk,
  fetchElementByIdThunk,
  createRelationshipThunk,
  updateRelationshipThunk,
  deleteRelationshipThunk,
  fetchElementTemplatesThunk
} from '../redux/slices/worldBuildingSlice';
import { selectCurrentBook } from '../redux/slices/bookSlice';

// Import the actual UI components
import WorldBuildingPage from '../components/WorldBuildingPage';

const WorldBuildingPageContainer = () => {
  const { bookId } = useParams();
  const dispatch = useDispatch();
  const navigate = useNavigate();

  // Get current book from Redux store
  const currentBook = useSelector(selectCurrentBook);

  // Get world building state from Redux store
  const {
    isLoading,
    error,
    ui
  } = useSelector(selectWorldBuildingState);

  // Get derived data
  const visibleCategories = useSelector(selectVisibleCategories);
  const allCategories = useSelector(selectAllCategories);
  const customizations = useSelector(selectAllCustomizations);
  const activeCategory = useSelector(selectActiveCategory);
  const selectedElements = useSelector(selectSelectedElements);
  const columnTab = useSelector(selectColumnTab);
  const selectedTemplateId = useSelector(selectSelectedTemplateId);

  // Get root elements for the active category
  const rootElements = useSelector(selectRootElementsForCategory);

  // Get all elements for the relationship view
  const allElements = useSelector(selectAllElementsForRelationshipView);

  // Get all relationships for the relationship view
  const allRelationships = useSelector(selectAllRelationships);

  // Get the currently selected element (first one if multiple are selected)
  const selectedElement = selectedElements.length > 0 ? selectedElements[0] : null;

  // Get child elements for the selected element
  const childElements = useSelector(state =>
    selectedElement && selectedElement.has_children ?
    selectChildElements(state, selectedElement.element_id) : []
  );

  // Get relationships for the selected element
  const elementRelationships = useSelector(state =>
    selectedElement ? selectElementRelationships(state, selectedElement.element_id) : []
  );

  // Use current book ID if not provided in URL
  const activeBookId = bookId || (currentBook && currentBook.book_id);

  // Fetch data on component mount
  useEffect(() => {
    if (activeBookId) {
      // Save the current book ID to localStorage for components that need it
      localStorage.setItem('currentBookId', activeBookId);
      console.log('WorldBuildingPageContainer - Saved current book ID to localStorage:', activeBookId);

      dispatch(fetchWorldBuildingData(activeBookId));

      // Also fetch all elements with their children for relationship data
      dispatch(fetchAllElementsWithChildrenThunk({ bookId: activeBookId }));

      // Fetch element templates from the database
      dispatch(fetchElementTemplatesThunk());
    } else {
      // If no book ID is available, redirect to books page
      navigate('/books');
    }
  }, [dispatch, activeBookId, navigate]);

  // Fetch elements when active category changes
  useEffect(() => {
    if (activeBookId && activeCategory) {
      console.log('WorldBuildingPageContainer - Active category changed, fetching elements:', activeCategory.category_id);
      dispatch(fetchElementsForCategoryThunk({
        bookId: activeBookId,
        categoryId: activeCategory.category_id
      }));
    }
  }, [dispatch, activeBookId, activeCategory?.category_id]);

  // Event handlers
  const handleTabChange = (tab) => {
    dispatch(setActiveTab(tab));
  };

  const handleCategoryChange = (categoryId) => {
    dispatch(setActiveCategory(categoryId));
  };

  const handleCategorySelect = (categoryId) => {
    dispatch(setActiveCategory(categoryId));

    // Fetch elements for the selected category
    if (activeBookId && categoryId) {
      console.log('WorldBuildingPageContainer - Fetching elements for category:', categoryId);
      dispatch(fetchElementsForCategoryThunk({
        bookId: activeBookId,
        categoryId
      }));
    }
  };

  const handleViewModeChange = (mode) => {
    dispatch(setViewMode(mode));
  };

  const handleColumnTabChange = (tab) => {
    dispatch(setColumnTab(tab));
  };

  const handleTemplateSelect = (templateId) => {
    dispatch(setSelectedTemplate(templateId));
  };

  const handleToggleRelationshipMode = () => {
    const newState = !ui.relationshipMode;
    dispatch(toggleRelationshipMode());

    // If turning on relationship mode, fetch all elements with their children
    if (newState && activeBookId) {
      console.log('Fetching all elements with children for relationship view');
      dispatch(fetchAllElementsWithChildrenThunk({ bookId: activeBookId }));
    }
  };

  const handleElementSelect = (elementId) => {
    console.log('WorldBuildingPageContainer - handleElementSelect called with:', elementId);

    // First, select the element
    dispatch(selectElement(elementId));

    // Then, fetch the element's data if it's not already loaded
    // We need to check if the element exists in the state
    const elementExists = allElements.some(element => element.element_id === elementId);

    if (!elementExists && activeBookId) {
      console.log('WorldBuildingPageContainer - Element not found in state, fetching it:', elementId);

      // Fetch the specific element by ID
      dispatch(fetchElementByIdThunk({ bookId: activeBookId, elementId }));

      // Also fetch all elements for all categories to ensure we have complete data
      dispatch(fetchAllElementsWithChildrenThunk({ bookId: activeBookId }));
    }
  };

  const handleClearSelection = () => {
    dispatch(clearSelection());
  };

  const handleUpdateCustomization = (categoryId, isEnabled, displayOrder) => {
    dispatch(updateCustomization({
      bookId: activeBookId,
      categoryId,
      customizationData: { is_enabled: isEnabled, display_order: displayOrder }
    }));
  };

  const handleResetCustomizations = () => {
    dispatch(resetCustomizations(activeBookId));
  };

  const handleCreateElement = (elementData, parentId = null, elementType = null, customFields = {}) => {
    console.log('WorldBuildingPageContainer - handleCreateElement called with:', { elementData, parentId, elementType, customFields });

    // Check if this is a multi-level structure from the AI generator
    if (elementData._is_multi_level_structure) {
      console.log('WorldBuildingPageContainer - Processing multi-level structure:', elementData);

      if (activeCategory) {
        // Create a recursive function to save elements level by level
        const saveHierarchy = async (elements, parentMap = {}) => {
          // Group elements by level
          const elementsByLevel = elementData.elementsByLevel;

          // Get the maximum level
          const maxLevel = elementData.maxLevel;

          // Helper function to create an element and return a promise
          const createElementPromise = (params) => {
            return new Promise((resolve, reject) => {
              dispatch(createWorldElement({
                ...params,
                onSuccess: (element) => resolve(element),
                onError: (error) => reject(error)
              }));
            });
          };

          // Save elements level by level, starting with the top
          for (let currentLevel = 0; currentLevel <= maxLevel; currentLevel++) {
            const currentElements = elementsByLevel[currentLevel] || [];
            console.log(`WorldBuildingPageContainer - Creating ${currentElements.length} elements at level ${currentLevel}`);

            // Save all elements at this level
            for (const element of currentElements) {
              // If this element has a parent that was just created, use the real parent ID
              let realParentId = element.parent_id;
              if (element.parent_id && element.parent_id.startsWith('temp_')) {
                const tempParentId = element.parent_id;
                realParentId = parentMap[tempParentId] || null;
                console.log(`WorldBuildingPageContainer - Replacing temp parent ID ${tempParentId} with real ID ${realParentId}`);
              }

              // Clean the element data
              const { element_type, parent_id, custom_fields, _level, _is_generated, has_children, ...cleanedElementData } = element;

              const elementType = element_type || 'generic';
              const customFields = custom_fields || {};
              const categoryValue = element.category || activeCategory.category_id;

              console.log('WorldBuildingPageContainer - Creating element:', {
                bookId: activeBookId,
                categoryId: categoryValue,
                parentId: realParentId,
                elementType,
                elementData: cleanedElementData
              });

              try {
                // Create the element
                const createdElement = await createElementPromise({
                  bookId: activeBookId,
                  categoryId: categoryValue,
                  parentId: realParentId,
                  elementType,
                  elementData: cleanedElementData,
                  customFields
                });

                // Store the mapping from temp ID to real ID for children
                const tempId = 'temp_' + element.name.replace(/\s+/g, '_').toLowerCase();
                parentMap[tempId] = createdElement.element_id;
                console.log(`WorldBuildingPageContainer - Created element with ID ${createdElement.element_id}, mapped to temp ID ${tempId}`);
              } catch (error) {
                console.error('WorldBuildingPageContainer - Error creating element:', error);
              }
            }
          }
        };

        // Start the recursive saving process
        saveHierarchy(elementData.elements)
          .then(() => {
            console.log('WorldBuildingPageContainer - Successfully created all elements in the hierarchy');
          })
          .catch(error => {
            console.error('WorldBuildingPageContainer - Error saving hierarchy:', error);
          });
      } else {
        console.error('WorldBuildingPageContainer - Cannot create multi-level elements: No active category');
      }
    }
    // Check if this is a nested structure from the AI generator
    else if (elementData._is_nested_structure) {
      console.log('WorldBuildingPageContainer - Processing nested structure:', elementData);

      // First, create the parent element
      const parentElement = elementData.parent;

      if (activeCategory) {
        // Create the parent element first
        const { element_type, parent_id, custom_fields, ...cleanedParentData } = parentElement;

        const parentElementType = element_type || 'generic';
        const parentCustomFields = custom_fields || {};

        // Use category from activeCategory
        const categoryValue = parentElement.category || activeCategory.category_id;

        console.log('WorldBuildingPageContainer - Creating parent element:', {
          bookId: activeBookId,
          categoryId: categoryValue,
          elementType: parentElementType,
          elementData: cleanedParentData,
          customFields: parentCustomFields
        });

        // Create the parent element and get its ID
        dispatch(createWorldElement({
          bookId: activeBookId,
          categoryId: categoryValue,
          elementType: parentElementType,
          elementData: cleanedParentData,
          customFields: parentCustomFields,
          onSuccess: (createdParent) => {
            console.log('WorldBuildingPageContainer - Parent element created:', createdParent);

            // Now create all the child elements with the parent ID
            if (elementData.children && elementData.children.length > 0) {
              elementData.children.forEach(childElement => {
                const { element_type: childType, custom_fields: childCustomFields, ...cleanedChildData } = childElement;

                const childElementType = childType || 'generic';
                const childMergedCustomFields = childCustomFields || {};

                console.log('WorldBuildingPageContainer - Creating child element:', {
                  bookId: activeBookId,
                  categoryId: categoryValue,
                  parentId: createdParent.element_id,
                  elementType: childElementType,
                  elementData: cleanedChildData,
                  customFields: childMergedCustomFields
                });

                dispatch(createWorldElement({
                  bookId: activeBookId,
                  categoryId: categoryValue,
                  parentId: createdParent.element_id,
                  elementType: childElementType,
                  elementData: cleanedChildData,
                  customFields: childMergedCustomFields
                }));
              });
            }
          }
        }));
      } else {
        console.error('WorldBuildingPageContainer - Cannot create nested elements: No active category');
      }
    } else if (activeCategory) {
      // Standard element creation (not nested)
      // If elementData already contains element_type and parent_id (from CreateSubElementModal),
      // use those values instead of the parameters
      const finalElementType = elementData.element_type || elementType || 'generic';
      const finalParentId = elementData.parent_id || parentId;

      // Remove element_type and parent_id from elementData if they exist
      // to avoid duplication in the Redux action
      const { element_type, parent_id, custom_fields, ...cleanedElementData } = elementData;

      // Merge custom_fields from elementData with the customFields parameter
      const mergedCustomFields = {
        ...customFields,
        ...(custom_fields || {})
      };

      // Use category instead of category_id to match the database schema
      // If elementData already has a category field, use that, otherwise use activeCategory.category_id
      const categoryValue = elementData.category || activeCategory.category_id;

      console.log('WorldBuildingPageContainer - Dispatching createWorldElement with:', {
        bookId: activeBookId,
        categoryId: categoryValue,
        parentId: finalParentId,
        elementType: finalElementType,
        elementData: cleanedElementData,
        customFields: mergedCustomFields
      });

      dispatch(createWorldElement({
        bookId: activeBookId,
        categoryId: categoryValue,
        parentId: finalParentId,
        elementType: finalElementType,
        elementData: cleanedElementData,
        customFields: mergedCustomFields
      }));
    } else {
      console.error('WorldBuildingPageContainer - Cannot create element: No active category');
    }
  };

  // Function to fetch child elements for a parent element
  const handleFetchChildElements = (parentId) => {
    if (activeBookId && parentId) {
      console.log('WorldBuildingPageContainer - Fetching child elements for parent:', parentId);
      dispatch(fetchChildElementsThunk({
        bookId: activeBookId,
        parentId
      }));
    }
  };

  // Function to update an element
  const handleUpdateElement = ({ elementId, elementData, customFields = {} }) => {
    if (activeBookId && elementId) {
      console.log('WorldBuildingPageContainer - Updating element:', { elementId, elementData, customFields });

      // Add custom_fields to elementData if provided
      const updatedElementData = {
        ...elementData,
        custom_fields: customFields
      };

      dispatch(updateWorldElementThunk({
        bookId: activeBookId,
        elementId,
        elementData: updatedElementData
      }));
    } else {
      console.error('WorldBuildingPageContainer - Cannot update element: Missing book ID or element ID');
    }
  };

  // Function to delete an element
  const handleDeleteElement = (elementId) => {
    if (activeBookId && elementId) {
      console.log('WorldBuildingPageContainer - Deleting element:', elementId);
      dispatch(deleteWorldElementThunk({
        bookId: activeBookId,
        elementId
      }));
    } else {
      console.error('WorldBuildingPageContainer - Cannot delete element: Missing book ID or element ID');
    }
  };

  // Function to create a relationship
  const handleCreateRelationship = (relationshipData) => {
    if (activeBookId) {
      console.log('WorldBuildingPageContainer - Creating relationship:', relationshipData);
      dispatch(createRelationshipThunk({
        bookId: activeBookId,
        relationshipData
      }));
    } else {
      console.error('WorldBuildingPageContainer - Cannot create relationship: Missing book ID');
    }
  };

  // Function to update a relationship
  const handleUpdateRelationship = (relationshipId, relationshipData) => {
    if (activeBookId && relationshipId) {
      console.log('WorldBuildingPageContainer - Updating relationship:', { relationshipId, relationshipData });
      dispatch(updateRelationshipThunk({
        bookId: activeBookId,
        relationshipId,
        relationshipData
      }));
    } else {
      console.error('WorldBuildingPageContainer - Cannot update relationship: Missing book ID or relationship ID');
    }
  };

  // Function to delete a relationship
  const handleDeleteRelationship = (relationshipId) => {
    if (activeBookId && relationshipId) {
      console.log('WorldBuildingPageContainer - Deleting relationship:', relationshipId);
      dispatch(deleteRelationshipThunk({
        bookId: activeBookId,
        relationshipId
      }));
    } else {
      console.error('WorldBuildingPageContainer - Cannot delete relationship: Missing book ID or relationship ID');
    }
  };

  // Pass props to the presentation component
  return (
    <WorldBuildingPage
      isLoading={isLoading}
      error={error}
      ui={ui}
      visibleCategories={visibleCategories}
      allCategories={allCategories}
      customizations={customizations}
      activeCategory={activeCategory}
      currentBook={currentBook}
      rootElements={rootElements}
      childElements={childElements}
      selectedElement={selectedElement}
      elementRelationships={elementRelationships}
      allElements={allElements}
      allRelationships={allRelationships}

      onTabChange={handleTabChange}
      onCategoryChange={handleCategoryChange}
      onCategorySelect={handleCategorySelect}
      onViewModeChange={handleViewModeChange}
      onColumnTabChange={handleColumnTabChange}
      onTemplateSelect={handleTemplateSelect}
      onToggleRelationshipMode={handleToggleRelationshipMode}
      onElementSelect={handleElementSelect}
      onClearSelection={handleClearSelection}
      onUpdateCustomization={handleUpdateCustomization}
      onResetCustomizations={handleResetCustomizations}
      onCreateElement={handleCreateElement}
      onFetchChildElements={handleFetchChildElements}
      onUpdateElement={handleUpdateElement}
      onDeleteElement={handleDeleteElement}
      onRelationshipCreated={handleCreateRelationship}
      onRelationshipUpdated={handleUpdateRelationship}
      onRelationshipDeleted={handleDeleteRelationship}
    />
  );
};

export default WorldBuildingPageContainer;

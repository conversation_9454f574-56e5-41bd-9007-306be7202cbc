// src/containers/AISidebarContainer.js
import React, { useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import AISidebar from '../components/AISidebar';
import {
  generateResponse,
  toggleAISidebar,
  selectAIResponses,
  selectAILoading,
  selectAISidebarOpen
} from '../redux/slices/aiSlice';
import { selectCurrentBook } from '../redux/slices/bookSlice';

const AISidebarContainer = () => {
  const dispatch = useDispatch();

  // Redux state - simplified
  const selectedBook = useSelector(selectCurrentBook);
  const responses = useSelector(selectAIResponses);
  const isLoading = useSelector(selectAILoading);
  const isOpen = useSelector(selectAISidebarOpen);

  // We've removed the selected nodes handling as it's now handled in the main interface

  // Removed current chapter and character extraction - simplifying the AI sidebar

  // Toggle sidebar
  const handleToggleSidebar = useCallback(() => {
    dispatch(toggleAISidebar());
  }, [dispatch]);

  // Submit prompt - simplified with no book context
  const handlePromptSubmit = useCallback((promptText) => {
    if (!promptText.trim() || !selectedBook) {
      console.debug('Prompt submission skipped: missing book or empty prompt');
      return;
    }

    // Simplified to just send the prompt with no additional context
    dispatch(generateResponse({
      bookId: selectedBook.book_id,
      prompt: promptText,
      chapterContent: ''
    }));
  }, [dispatch, selectedBook]);

  // Response click function removed - this functionality is now handled elsewhere

  // Suggestion click function removed - simplifying the AI sidebar

  // Refine idea function removed - this functionality is now handled elsewhere

  // Connect ideas function removed - this functionality is now handled in the main interface

  return (
    <AISidebar
      responses={responses}
      isLoading={isLoading}
      isOpen={isOpen}
      onToggleSidebar={handleToggleSidebar}
      onPromptSubmit={handlePromptSubmit}
    />
  );
};

export default AISidebarContainer;

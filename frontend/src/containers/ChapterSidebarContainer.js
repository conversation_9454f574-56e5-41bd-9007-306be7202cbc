// frontend/src/containers/ChapterSidebarContainer.js
import React, { useCallback } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import ChapterSidebar from '../components/ChapterSidebar';
import {
  selectChapters,
  selectCurrentChapter,
  setCurrentChapter,
  addChapter as addChapterAction,
  saveCurrentChapterContent,
  updateEditorState,
  selectChapterContent
} from '../redux/slices/writeSlice';
import { selectCurrentBook } from '../redux/slices/bookSlice';

/**
 * Container component for ChapterSidebar that connects to Redux
 * @returns {JSX.Element} ChapterSidebar container
 */
const ChapterSidebarContainer = () => {
  const dispatch = useDispatch();

  // Select state from Redux
  const book = useSelector(selectCurrentBook);
  const chapters = useSelector(selectChapters);
  const currentChapter = useSelector(selectCurrentChapter);

  // Handle chapter selection
  const handleChapterSelect = useCallback((chapter) => {
    if (chapter && chapter.id) {
      dispatch(setCurrentChapter(chapter.id));
    }
  }, [dispatch]);

  // No chapter reordering in the Write page

  // Get current chapter content from Redux
  const chapterContent = useSelector(selectChapterContent);

  // Handle adding a new chapter
  const handleAddChapter = useCallback((title) => {
    if (book?.book_id) {
      // First, save the current chapter's content if there is a current chapter
      if (currentChapter?.id && chapterContent) {
        console.log('[DEBUG ChapterSidebarContainer] Saving current chapter content before creating new chapter');
        dispatch(saveCurrentChapterContent({
          bookId: book.book_id,
          chapterId: currentChapter.id,
          content: chapterContent
        }));
      }

      // Then create the new chapter
      return dispatch(addChapterAction({
        bookId: book.book_id,
        title
      })).unwrap()
        .then(newChapter => {
          console.log('[DEBUG ChapterSidebarContainer] New chapter created:', newChapter);

          // Clear the editor state when switching to the new chapter
          dispatch(updateEditorState(''));

          // Set the new chapter as the current chapter
          dispatch(setCurrentChapter(newChapter.id));

          return newChapter;
        });
    }
    return Promise.reject(new Error('No book selected'));
  }, [book, currentChapter, chapterContent, dispatch]);

  return (
    <ChapterSidebar
      chapters={chapters}
      currentChapter={currentChapter}
      onChapterSelect={handleChapterSelect}
      onAddChapter={handleAddChapter}
    />
  );
};

export default ChapterSidebarContainer;

// frontend/src/containers/WritePageReduxContainer.js
import React, { useEffect, useCallback, useRef } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { debounce } from 'lodash';
import WritePage from '../components/WritePage';
import {
  fetchAllChapters,
  fetchCurrentChapterContent,
  saveCurrentChapterContent,
  setCurrentChapter,
  updateEditorState,
  updateCursorPosition,
  updateSelection,
  updateWordCount,
  toggleReferencePanel,
  toggleEventSidebar,
  toggleChapterSidebar,
  clearError,
  selectChapters,
  selectCurrentChapterId,
  selectChapterContent,
  selectShowReferencePanel,
  selectShowEventSidebar,
  selectShowChapterSidebar,
  selectIsLoading,
  selectIsSaving,
  selectError,
  selectLastSaved,
  selectCurrentChapter
} from '../redux/slices/writeSlice';
import { selectCurrentBook } from '../redux/slices/bookSlice';
import { selectCurrentEvent, selectAllPlotEvents, selectAllChapters as selectPlotChapters, fetchAllPlotEvents, fetchAllChapters as fetchAllPlotChapters } from '../redux/slices/plotSlice';

/**
 * Container component for WritePage that connects to Redux
 * @returns {JSX.Element} WritePage container
 */
const WritePageReduxContainer = () => {
  const dispatch = useDispatch();

  // Select state from Redux
  const book = useSelector(selectCurrentBook);
  const chapters = useSelector(selectChapters);
  const currentChapterId = useSelector(selectCurrentChapterId);
  const currentChapter = useSelector(selectCurrentChapter);
  const chapterContent = useSelector(selectChapterContent);
  const showReferencePanel = useSelector(selectShowReferencePanel);
  const showEventSidebar = useSelector(selectShowEventSidebar);
  const showChapterSidebar = useSelector(selectShowChapterSidebar);
  const isLoading = useSelector(selectIsLoading);
  const isSaving = useSelector(selectIsSaving);
  const error = useSelector(selectError);
  const lastSaved = useSelector(selectLastSaved);
  const selectedEvent = useSelector(selectCurrentEvent);

  // Create debounced save function with improved error handling
  const debouncedSave = useRef(
    debounce((bookId, chapterId, content) => {
      console.log(`[DEBUG WriteContainer] Debounced save for chapter ${chapterId} in book ${bookId}`);

      // Validate content before saving
      if (!content) {
        console.warn('[DEBUG WriteContainer] Empty content, creating empty Draft.js content');
        // Create empty Draft.js content
        const emptyContent = JSON.stringify({
          blocks: [
            {
              key: 'empty',
              text: '',
              type: 'unstyled',
              depth: 0,
              inlineStyleRanges: [],
              entityRanges: [],
              data: {}
            }
          ],
          entityMap: {}
        });
        content = emptyContent;
      } else if (typeof content === 'string') {
        try {
          // Try to parse as JSON to validate
          JSON.parse(content);
          console.log('[DEBUG WriteContainer] Content is valid JSON');
        } catch (e) {
          console.error('[DEBUG WriteContainer] Content is not valid JSON, creating empty content');
          // Create empty Draft.js content
          const emptyContent = JSON.stringify({
            blocks: [
              {
                key: 'empty',
                text: '',
                type: 'unstyled',
                depth: 0,
                inlineStyleRanges: [],
                entityRanges: [],
                data: {}
              }
            ],
            entityMap: {}
          });
          content = emptyContent;
        }
      }

      // Dispatch the save action and handle the promise
      dispatch(saveCurrentChapterContent({ bookId, chapterId, content }))
        .then(resultAction => {
          if (saveCurrentChapterContent.fulfilled.match(resultAction)) {
            console.log('[DEBUG WriteContainer] Content saved successfully');
          } else if (saveCurrentChapterContent.rejected.match(resultAction)) {
            console.error('[DEBUG WriteContainer] Failed to save content:', resultAction.error);
          }
        })
        .catch(error => {
          console.error('[DEBUG WriteContainer] Error saving content:', error);
        });
    }, 1000) // Reduced debounce time for more responsive saving
  ).current;

  // Fetch chapters and plot data when book changes
  useEffect(() => {
    if (book?.book_id) {
      console.log('[DEBUG WriteContainer] Fetching chapters and plot data for book:', book.book_id);

      // Fetch write chapters
      dispatch(fetchAllChapters(book.book_id))
        .then(resultAction => {
          if (fetchAllChapters.fulfilled.match(resultAction)) {
            console.log('[DEBUG WriteContainer] Successfully fetched write chapters:', resultAction.payload?.length || 0);
          } else if (fetchAllChapters.rejected.match(resultAction)) {
            console.error('[DEBUG WriteContainer] Failed to fetch write chapters:', resultAction.error);
          }
        });

      // Fetch plot chapters
      dispatch(fetchAllPlotChapters(book.book_id))
        .then(resultAction => {
          if (fetchAllPlotChapters.fulfilled.match(resultAction)) {
            console.log('[DEBUG WriteContainer] Successfully fetched plot chapters:', resultAction.payload?.length || 0);
          } else if (fetchAllPlotChapters.rejected.match(resultAction)) {
            console.error('[DEBUG WriteContainer] Failed to fetch plot chapters:', resultAction.error);
          }
        });

      // Fetch plot events
      dispatch(fetchAllPlotEvents(book.book_id))
        .then(resultAction => {
          if (fetchAllPlotEvents.fulfilled.match(resultAction)) {
            console.log('[DEBUG WriteContainer] Successfully fetched plot events:', resultAction.payload?.length || 0);
          } else if (fetchAllPlotEvents.rejected.match(resultAction)) {
            console.error('[DEBUG WriteContainer] Failed to fetch plot events:', resultAction.error);
          }
        });
    }
  }, [book, dispatch]);

  // Fetch chapter content when current chapter changes with localStorage backup
  useEffect(() => {
    console.log('[DEBUG WriteContainer] Chapter change effect triggered');
    console.log('[DEBUG WriteContainer] Book:', book);
    console.log('[DEBUG WriteContainer] Current chapter ID:', currentChapterId);

    if (book?.book_id && currentChapterId) {
      console.log('[DEBUG WriteContainer] Fetching chapter content for:', currentChapterId);

      // Try to get content from localStorage first for immediate display
      try {
        const backupKey = `chapter_content_${book.book_id}_${currentChapterId}`;
        const localContent = localStorage.getItem(backupKey);

        if (localContent) {
          console.log('[DEBUG WriteContainer] Found content in localStorage, using it temporarily');
          dispatch(updateEditorState(localContent));
        }
      } catch (e) {
        console.warn('[DEBUG WriteContainer] Error retrieving content from localStorage:', e);
      }

      // Always fetch from the server to get the latest content
      dispatch(fetchCurrentChapterContent({
        bookId: book.book_id,
        chapterId: currentChapterId
      }))
      .then(resultAction => {
        if (fetchCurrentChapterContent.fulfilled.match(resultAction)) {
          console.log('[DEBUG WriteContainer] Successfully fetched content from server');

          // If we got content from the server, update the editor state
          if (resultAction.payload && resultAction.payload.content) {
            console.log('[DEBUG WriteContainer] Updating editor with content from server');
            dispatch(updateEditorState(resultAction.payload.content));
          } else {
            console.log('[DEBUG WriteContainer] Server returned empty content');
          }
        } else if (fetchCurrentChapterContent.rejected.match(resultAction)) {
          console.error('[DEBUG WriteContainer] Failed to fetch content from server:', resultAction.error);

          // If server fetch fails, try to recover from localStorage
          try {
            const backupKey = `chapter_content_${book.book_id}_${currentChapterId}`;
            const localContent = localStorage.getItem(backupKey);

            if (localContent) {
              console.log('[DEBUG WriteContainer] Using localStorage content as fallback');
              dispatch(updateEditorState(localContent));
            }
          } catch (e) {
            console.warn('[DEBUG WriteContainer] Error retrieving content from localStorage:', e);
          }
        }
      });
    } else {
      console.log('[DEBUG WriteContainer] Not fetching content - missing book ID or chapter ID');
    }
  }, [book, currentChapterId, dispatch]);

  // Handle chapter selection with improved state management
  const handleChapterSelect = useCallback((chapter) => {
    if (chapter && chapter.id) {
      console.log('[DEBUG WriteContainer] Selecting chapter:', chapter.id);

      // If we have unsaved content for the current chapter, save it before switching
      if (book?.book_id && currentChapterId && chapterContent) {
        console.log('[DEBUG WriteContainer] Saving content before switching chapters');
        dispatch(saveCurrentChapterContent({
          bookId: book.book_id,
          chapterId: currentChapterId,
          content: chapterContent
        }));
      }

      // Set the new current chapter
      dispatch(setCurrentChapter(chapter.id));

      // Clear the editor state first to ensure it updates properly
      dispatch(updateEditorState(''));

      // Fetch the content for the new chapter
      if (book?.book_id) {
        console.log('[DEBUG WriteContainer] Fetching content for new chapter:', chapter.id);

        // Try to get content from localStorage first for immediate display
        try {
          const backupKey = `chapter_content_${book.book_id}_${chapter.id}`;
          const localContent = localStorage.getItem(backupKey);

          if (localContent) {
            console.log('[DEBUG WriteContainer] Found content in localStorage, using it immediately');
            dispatch(updateEditorState(localContent));
          }
        } catch (e) {
          console.warn('[DEBUG WriteContainer] Error retrieving content from localStorage:', e);
        }

        // Then fetch from server
        dispatch(fetchCurrentChapterContent({
          bookId: book.book_id,
          chapterId: chapter.id
        }))
        .then(resultAction => {
          if (fetchCurrentChapterContent.fulfilled.match(resultAction)) {
            console.log('[DEBUG WriteContainer] Successfully fetched content from server');

            // If we got content from the server, update the editor state
            if (resultAction.payload && resultAction.payload.content) {
              console.log('[DEBUG WriteContainer] Updating editor with content from server');
              dispatch(updateEditorState(resultAction.payload.content));
            }
          }
        });
      }
    }
  }, [book, currentChapterId, chapterContent, dispatch]);

  // Handle content change with improved persistence
  const handleContentChange = useCallback((content) => {
    console.log('[DEBUG WriteContainer] Content change handler called');
    console.log('[DEBUG WriteContainer] Content type:', typeof content);
    console.log('[DEBUG WriteContainer] Content length:', content?.length || 0);

    if (book?.book_id && currentChapterId) {
      console.log('[DEBUG WriteContainer] Saving content to Redux and backend');

      // Save to Redux immediately for instant UI updates
      dispatch(updateEditorState(content));

      // Debounced save to backend to prevent too many API calls
      debouncedSave(book.book_id, currentChapterId, content);

      // Store the content in localStorage as a backup
      try {
        const backupKey = `chapter_content_${book.book_id}_${currentChapterId}`;
        localStorage.setItem(backupKey, content);
        console.log('[DEBUG WriteContainer] Content backed up to localStorage');
      } catch (e) {
        console.warn('[DEBUG WriteContainer] Failed to backup content to localStorage:', e);
      }
    } else {
      console.warn('[DEBUG WriteContainer] Cannot save content - missing book ID or chapter ID');
    }
  }, [book, currentChapterId, dispatch, debouncedSave]);

  // Handle cursor position change
  const handleCursorPositionChange = useCallback((position) => {
    dispatch(updateCursorPosition(position));
  }, [dispatch]);

  // Handle selection change
  const handleSelectionChange = useCallback((selection) => {
    dispatch(updateSelection(selection));
  }, [dispatch]);

  // Handle word count update
  const handleWordCountUpdate = useCallback((count) => {
    dispatch(updateWordCount(count));
  }, [dispatch]);

  // Toggle reference panel
  const handleToggleReferencePanel = useCallback(() => {
    dispatch(toggleReferencePanel());
  }, [dispatch]);

  // Toggle event sidebar
  const handleToggleEventSidebar = useCallback(() => {
    dispatch(toggleEventSidebar());
  }, [dispatch]);

  // Toggle chapter sidebar
  const handleToggleChapterSidebar = useCallback(() => {
    dispatch(toggleChapterSidebar());
  }, [dispatch]);

  // Handle clear error
  const handleClearError = useCallback(() => {
    dispatch(clearError());
  }, [dispatch]);

  // Get plot data from Redux
  const plotEvents = useSelector(selectAllPlotEvents);
  const plotChapters = useSelector(selectPlotChapters);

  return (
    <WritePage
      currentChapter={currentChapter}
      chapters={chapters}
      chapterContent={chapterContent}
      plotEvents={plotEvents} plotChapters={plotChapters}
      selectedEvent={selectedEvent}
      isLoading={isLoading}
      isSaving={isSaving}
      showReferencePanel={showReferencePanel}
      showEventSidebar={showEventSidebar}
      showChapterSidebar={showChapterSidebar}
      errorMessage={error}
      lastSaved={lastSaved}
      handleContentChange={handleContentChange}
      handleChapterSelect={handleChapterSelect}
      toggleReferencePanel={handleToggleReferencePanel}
      toggleEventSidebar={handleToggleEventSidebar}
      toggleChapterSidebar={handleToggleChapterSidebar}
      clearError={handleClearError}
    />
  );
};

export default WritePageReduxContainer;

// frontend/src/containers/ReferencePanelReduxContainer.js
import React, { useEffect, useCallback, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import ReferencePanel from '../components/ReferencePanel';
import QuickWorldElementForm from '../components/QuickWorldElementForm';
import Modal from '../components/Modal';
import {
  setActiveTab,
  setSearchTerm,
  addToRecentlyUsed,
  setCharacterFilter,
  setWorldFilter,
  clearFilters,
  selectActiveTab,
  selectSearchTerm,
  selectRecentlyUsed,
  selectCharacterFilters,
  selectWorldFilters,
  selectFilteredCharacters,
  selectFilteredWorldElements
} from '../redux/slices/referenceSlice';
import { selectCurrentBook } from '../redux/slices/bookSlice';
import { fetchAllCharacters } from '../redux/slices/charactersSlice';
import {
  fetchAllElementsWithChildrenThunk as fetchAllWorldElements,
  createWorldElement,
  fetchElementsForCategoryThunk,
  selectAllCategories
} from '../redux/slices/worldBuildingSlice';

/**
 * Container component for ReferencePanel that connects to Redux
 * @returns {JSX.Element} ReferencePanel container
 */
const ReferencePanelReduxContainer = () => {
  const dispatch = useDispatch();
  const [showDetailedAddModal, setShowDetailedAddModal] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState('');
  const [parentElements, setParentElements] = useState([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState(null);

  // Select state from Redux
  const book = useSelector(selectCurrentBook);
  const activeTab = useSelector(selectActiveTab);
  const searchTerm = useSelector(selectSearchTerm);
  const recentlyUsed = useSelector(selectRecentlyUsed);
  const characterFilters = useSelector(selectCharacterFilters);
  const worldFilters = useSelector(selectWorldFilters);
  const filteredCharacters = useSelector(selectFilteredCharacters);
  const filteredWorldElements = useSelector(state => {
    console.log('ReferencePanelReduxContainer - worldBuilding state:', state.worldBuilding);
    return selectFilteredWorldElements(state);
  });
  const categories = useSelector(selectAllCategories);

  // Fetch data when book changes
  useEffect(() => {
    if (book?.book_id) {
      console.log('Fetching data for book:', book.book_id);
      dispatch(fetchAllCharacters(book.book_id));
      dispatch(fetchAllWorldElements({ bookId: book.book_id }))
        .unwrap()
        .then(result => {
          console.log('Fetched world elements:', result);
        })
        .catch(error => {
          console.error('Error fetching world elements:', error);
        });
    }
  }, [book, dispatch]);

  // Handle tab change
  const handleTabChange = useCallback((tab) => {
    dispatch(setActiveTab(tab));
  }, [dispatch]);

  // Handle search
  const handleSearch = useCallback((term) => {
    dispatch(setSearchTerm(term));
  }, [dispatch]);

  // Handle item selection
  const handleItemSelect = useCallback((item) => {
    dispatch(addToRecentlyUsed(item));
  }, [dispatch]);

  // Handle character filter change
  const handleCharacterFilterChange = useCallback((filters) => {
    dispatch(setCharacterFilter(filters));
  }, [dispatch]);

  // Handle world filter change
  const handleWorldFilterChange = useCallback((filters) => {
    dispatch(setWorldFilter(filters));
  }, [dispatch]);

  // Handle clear filters
  const handleClearFilters = useCallback(() => {
    dispatch(clearFilters());
  }, [dispatch]);

  // Handle detailed add button click
  const handleDetailedAddClick = useCallback(() => {
    setShowDetailedAddModal(true);
  }, []);

  // Handle category change in the form
  const handleCategoryChange = useCallback((categoryId) => {
    setSelectedCategory(categoryId);

    // Fetch parent elements for the selected category
    if (book?.book_id && categoryId) {
      dispatch(fetchElementsForCategoryThunk({ bookId: book.book_id, categoryId }))
        .unwrap()
        .then(({ elements }) => {
          // Filter to only include elements that can have children
          const validParents = elements.filter(elem => elem.has_children);
          setParentElements(validParents);
        })
        .catch(error => {
          console.error('Error fetching parent elements:', error);
          setParentElements([]);
        });
    }
  }, [book, dispatch]);

  // Handle form submission
  const handleFormSubmit = useCallback(async (formData) => {
    if (!book?.book_id) {
      setError('No book selected');
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      await dispatch(createWorldElement({
        bookId: book.book_id,
        categoryId: formData.category_id,
        parentId: formData.parent_id || null,
        elementType: 'generic',
        elementData: {
          name: formData.name,
          description: formData.description || '',
          has_children: formData.has_children
        }
      })).unwrap();

      // Close the modal and refresh the elements
      setShowDetailedAddModal(false);
      dispatch(fetchAllWorldElements({ bookId: book.book_id }));
    } catch (error) {
      console.error('Error creating world element:', error);
      setError(error.message || 'Failed to create element');
    } finally {
      setIsSubmitting(false);
    }
  }, [book, dispatch]);

  return (
    <>
      <ReferencePanel
        activeTab={activeTab}
        searchTerm={searchTerm}
        recentlyUsed={recentlyUsed}
        characterFilters={characterFilters}
        worldFilters={worldFilters}
        characters={filteredCharacters}
        worldElements={filteredWorldElements}
        onTabChange={handleTabChange}
        onSearch={handleSearch}
        onItemSelect={handleItemSelect}
        onCharacterFilterChange={handleCharacterFilterChange}
        onWorldFilterChange={handleWorldFilterChange}
        onClearFilters={handleClearFilters}
        onDetailedAddClick={handleDetailedAddClick}
      />

      {/* Detailed Add Modal */}
      {showDetailedAddModal && (
        <Modal
          isOpen={showDetailedAddModal}
          onClose={() => setShowDetailedAddModal(false)}
          title="Add World Element"
        >
          {error && (
            <div className="error-message" style={{ color: 'red', marginBottom: '10px' }}>
              {error}
            </div>
          )}
          <QuickWorldElementForm
            onSubmit={handleFormSubmit}
            onCancel={() => setShowDetailedAddModal(false)}
            categories={categories}
            parentElements={parentElements}
            onCategoryChange={handleCategoryChange}
            isSubmitting={isSubmitting}
          />
        </Modal>
      )}
    </>
  );
};

export default ReferencePanelReduxContainer;

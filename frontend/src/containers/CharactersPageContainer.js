// frontend/src/containers/CharactersPageContainer.js
import React, { useState, useEffect, useCallback } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useParams } from 'react-router-dom';
import {
  fetchAllCharacters,
  createCharacter,
  updateExistingCharacter,
  removeCharacter,
  generateAICharacter,
  setCurrentBookId,
  selectAllCharacters,
  selectCharactersLoading,
  selectCharactersError
} from '../redux/slices/charactersSlice';
import { selectCurrentBook } from '../redux/slices/bookSlice';
import CharactersPage from '../components/CharactersPage';
import { BASE_URL } from '../utils/apiConfig';

/**
 * Container component for CharactersPage that handles state management and data fetching
 * @returns {JSX.Element} CharactersPage container
 */
const CharactersPageContainer = () => {
  const { bookId } = useParams();
  const dispatch = useDispatch();

  // Select state from Redux store
  const characters = useSelector(selectAllCharacters);
  const currentBook = useSelector(selectCurrentBook);
  const isLoading = useSelector(selectCharactersLoading);
  const error = useSelector(selectCharactersError);

  // Use current book ID if not provided in URL
  const activeBookId = bookId || (currentBook && currentBook.book_id);

  // Local state
  const [comparedCharacters, setComparedCharacters] = useState([]);
  const [editingCharacter, setEditingCharacter] = useState(null);
  const [worldSettingTypes, setWorldSettingTypes] = useState([
    'fantasy', 'sci-fi', 'mystery', 'romance', 'thriller', 'horror', 'historical'
  ]);
  const [isGeneratingHeadshot, setIsGeneratingHeadshot] = useState(false);
  const [isGeneratingCharacter, setIsGeneratingCharacter] = useState(false);

  // Effect for initial load and book changes
  useEffect(() => {
    if (activeBookId) {
      // Set the current book ID in the characters slice
      dispatch(setCurrentBookId(activeBookId));
      // Fetch characters for this book
      dispatch(fetchAllCharacters(activeBookId));
    }
  }, [dispatch, activeBookId]);

  // Handle character selection for editing
  const handleEditCharacter = useCallback((character) => {
    // Make sure we're working with a copy of the character to avoid reference issues
    const characterCopy = { ...character };

    // Ensure the character has all required fields
    if (!characterCopy.traits) characterCopy.traits = [];
    if (!characterCopy.relationships) characterCopy.relationships = [];
    if (!characterCopy.goals) characterCopy.goals = [];

    console.log('Editing character:', characterCopy);
    setEditingCharacter(characterCopy);
  }, []);

  // Handle character creation
  const handleCreateCharacter = useCallback(() => {
    setEditingCharacter({
      name: '',
      role: '',
      description: '',
      traits: [],
      goals: [],
      backstory: '',
      relationships: [],
      headshot: '',
      age: '',
      race: '',
      gender_identity: '',
      sexual_orientation: '',
      arc: ''
    });
  }, []);

  // Handle character save
  const handleSaveCharacter = useCallback((character) => {
    if (!activeBookId) return;

    try {
      console.log('Saving character:', character);

      // Ensure gender_identity, sexual_orientation, and relationship strength are included
      const processedCharacter = {
        ...character,
        gender_identity: character.gender_identity || '',
        sexual_orientation: character.sexual_orientation || '',
        relationships: Array.isArray(character.relationships)
          ? character.relationships.map(rel => ({
              ...rel,
              strength: rel.strength !== undefined ? rel.strength : 3 // Default to 3 if not specified
            }))
          : []
      };

      // Close the modal immediately to prevent double-clicks
      setEditingCharacter(null);

      // Then dispatch the action
      if (character.id) {
        // Update existing character
        dispatch(updateExistingCharacter({
          bookId: activeBookId,
          characterId: character.id,
          characterData: processedCharacter
        }));
      } else {
        // Create new character
        dispatch(createCharacter({
          bookId: activeBookId,
          characterData: processedCharacter
        }));
      }
    } catch (error) {
      console.error('Error saving character:', error);
      alert(`Error saving character: ${error.message}`);
    }
  }, [activeBookId, dispatch]);

  // Handle character deletion
  const handleDeleteCharacter = useCallback((characterId) => {
    if (!activeBookId) return;

    dispatch(removeCharacter({
      bookId: activeBookId,
      characterId
    }));
  }, [activeBookId, dispatch]);

  // Handle character comparison
  const handleCompareCharacter = useCallback((character) => {
    setComparedCharacters(prev => {
      // If character is already in the compared list, remove it
      if (prev.some(c => c.id === character.id)) {
        return prev.filter(c => c.id !== character.id);
      }

      // Otherwise add it, but limit to 2 characters
      const newCompared = [...prev, character];
      return newCompared.slice(-2);
    });
  }, []);

  // Handle AI character generation
  const handleGenerateCharacter = useCallback(async (prompt) => {
    if (!activeBookId) return;

    // Set loading state to true
    setIsGeneratingCharacter(true);

    try {
      // Create a structured prompt with existing characters for relationships
      const existingCharacterInfo = characters.map(char => ({
        id: char.id,
        name: char.name,
        role: char.role || 'unknown',
        description: char.description || '',
        age: char.age || '',
        backstory: char.backstory || '',
        gender_identity: char.gender_identity || '',
        sexual_orientation: char.sexual_orientation || ''
      }));

      // Get book genre from current book
      const bookGenre = currentBook?.genre || 'fiction';

      // Create a structured prompt
      const structuredPrompt = {
        userPrompt: prompt,
        bookGenre: bookGenre,
        existingCharacters: existingCharacterInfo,
        requestedFields: [
          'name',
          'description',
          'age',
          'role',
          'race',
          'traits',
          'relationships'
        ]
      };

      console.log('Generating character with structured prompt:', structuredPrompt);

      const resultAction = await dispatch(generateAICharacter({
        bookId: activeBookId,
        prompt: JSON.stringify(structuredPrompt)
      }));

      if (generateAICharacter.fulfilled.match(resultAction)) {
        setEditingCharacter(resultAction.payload);
      }
    } catch (error) {
      console.error('Error generating character:', error);
      alert('Failed to generate character. Please try again.');
    } finally {
      // Set loading state back to false regardless of success or failure
      setIsGeneratingCharacter(false);
    }
  }, [activeBookId, dispatch, characters, currentBook]);

  // Handle headshot generation
  const handleGenerateHeadshot = useCallback(async (character) => {
    if (!activeBookId || !character || !character.name || !character.description) return;

    // Store the current character data to preserve it
    const currentCharacterData = { ...character };

    // Set loading state
    setIsGeneratingHeadshot(true);

    // Update the character with a temporary loading image
    // Make sure we preserve the entire character object
    setEditingCharacter(prev => {
      // If prev is null or undefined, use the current character data
      const baseCharacter = prev || currentCharacterData;
      return {
        ...baseCharacter,
        headshot: 'generating' // Special flag to show loading animation
      };
    });

    // Set a safety timeout to reset the state if generation takes too long (30 seconds)
    const safetyTimeout = setTimeout(() => {
      console.log('Headshot generation safety timeout triggered');
      // Check if we're still in generating state
      if (editingCharacter?.headshot === 'generating') {
        console.log('Still in generating state after timeout, resetting');
        setIsGeneratingHeadshot(false);
        setEditingCharacter(prev => {
          if (prev && prev.headshot === 'generating') {
            return {
              ...prev,
              headshot: 'https://via.placeholder.com/150?text=Generation+Timeout'
            };
          }
          return prev;
        });
      }
    }, 30000); // 30 second timeout

    try {
      // Create a detailed prompt for the headshot
      let prompt = `Generate a headshot portrait for ${character.name}`;

      if (character.description) {
        prompt += `, who is ${character.description}`;
      }

      if (character.traits && character.traits.length > 0) {
        const traitsArray = Array.isArray(character.traits)
          ? character.traits
          : (typeof character.traits === 'string' ? character.traits.split('\n') : []);

        if (traitsArray.length > 0) {
          prompt += `. They are ${traitsArray.join(', ')}`;
        }
      }

      if (character.race) {
        prompt += `. Their race is ${character.race}`;
      }

      if (character.age) {
        prompt += `. They are ${character.age} years old`;
      }

      console.log('Generating headshot with prompt:', prompt);

      // Get the user's preferred style from localStorage or use a default
      const userPreferences = JSON.parse(localStorage.getItem('userPreferences') || '{}');
      const preferredStyle = userPreferences.image_style || 'default';

      console.log('Using image style for headshot generation:', preferredStyle);

      // Make the API call to generate the headshot
      const response = await fetch(`${BASE_URL}/books/${activeBookId}/ai/save_image`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          prompt,
          character_name: character.name,
          style: preferredStyle
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to generate headshot: ${response.statusText}`);
      }

      const data = await response.json();
      console.log('Headshot generation response:', data);

      // Clear the safety timeout since we got a response
      clearTimeout(safetyTimeout);

      // Update the character with the new headshot URL
      let headshotUrl = data.headshot_url;
      console.log('Original headshot URL from API:', headshotUrl);

      // Fix the URL if it's a relative path
      if (headshotUrl && headshotUrl.startsWith('/')) {
        // Extract the path components
        const pathParts = headshotUrl.split('/');
        const fileName = pathParts[pathParts.length - 1];

        // Reconstruct the URL with proper encoding for the filename
        const basePath = pathParts.slice(0, pathParts.length - 1).join('/');
        const encodedFileName = encodeURIComponent(fileName);
        const encodedPath = `${basePath}/${encodedFileName}`;

        headshotUrl = `${BASE_URL}${encodedPath}`;
        console.log('Fixed headshot URL with BASE_URL and encoding:', headshotUrl);
      } else {
        console.log('URL did not need fixing (not starting with /)');
      }

      // Only update the headshot field, preserving all other character data
      const updatedCharacter = {
        ...currentCharacterData,
        headshot: headshotUrl
      };

      console.log('Final headshot URL being set in character:', headshotUrl);
      console.log('Updated character object:', updatedCharacter);

      // Update the editing character state - force a re-render by creating a new object
      setEditingCharacter(null); // First clear the current character
      setTimeout(() => {
        console.log('Setting updated character with headshot:', updatedCharacter);
        setEditingCharacter(updatedCharacter); // Then set the updated character
      }, 10);

      // Also update the character in the Redux store to ensure persistence
      if (character.id) {
        console.log('Updating character in Redux store with new headshot:', headshotUrl);
        dispatch(updateExistingCharacter({
          bookId: activeBookId,
          characterId: character.id,
          characterData: updatedCharacter
        }));
      }

      // Show a notification if the image was rejected by content moderation, prompt was too long, or timed out
      if (data.moderation_rejected) {
        // Use alert for now, but this could be replaced with a nicer modal/toast notification
        alert('The image was rejected by content moderation. A placeholder has been provided instead.');
      } else if (data.prompt_too_long) {
        alert('The character description was too detailed for image generation. A placeholder has been provided instead.');
      } else if (data.timeout_error) {
        alert('The image generation timed out. Try a shorter description or fewer details.');
      }
    } catch (error) {
      console.error('Error generating headshot:', error);

      // Clear the safety timeout since we're handling the error now
      clearTimeout(safetyTimeout);

      // Set a placeholder image if generation fails
      const updatedCharacter = {
        ...currentCharacterData,
        headshot: 'https://via.placeholder.com/150?text=Generation+Failed'
      };

      // Update the editing character state - force a re-render by creating a new object
      setEditingCharacter(null); // First clear the current character
      setTimeout(() => {
        console.log('Setting updated character with placeholder headshot:', updatedCharacter);
        setEditingCharacter(updatedCharacter); // Then set the updated character
      }, 10);

      // Also update the character in the Redux store to ensure persistence
      if (character.id) {
        console.log('Updating character in Redux store with placeholder headshot');
        dispatch(updateExistingCharacter({
          bookId: activeBookId,
          characterId: character.id,
          characterData: updatedCharacter
        }));
      }

      // Show an error message to the user
      alert('Failed to generate headshot. Please try again with a shorter description or check your connection.');
    } finally {
      setIsGeneratingHeadshot(false);
    }
  }, [activeBookId, dispatch]);

  // Handle headshot deletion
  const handleDeleteHeadshot = useCallback(() => {
    // Check if we're in the generating state
    if (editingCharacter?.headshot === 'generating') {
      console.log('Cancelling headshot generation');
      // Reset the loading state
      setIsGeneratingHeadshot(false);
    }

    // Clear the headshot field
    setEditingCharacter(prev => ({
      ...prev,
      headshot: ''
    }));
  }, [editingCharacter]);

  // Handle cancel editing
  const handleCancelEdit = useCallback(() => {
    setEditingCharacter(null);
  }, []);

  // Handle generating character name
  const handleGenerateName = useCallback(async (character) => {
    if (!activeBookId) return;

    try {
      const bookGenre = currentBook?.genre || 'fiction';
      const race = character?.race || '';
      const gender_identity = character?.gender_identity || '';

      console.log('Generating name with gender identity:', gender_identity);

      const response = await fetch(`${BASE_URL}/books/${activeBookId}/characters/generate-name`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          genre: bookGenre,
          race: race,
          gender: gender_identity // Send gender_identity as gender parameter
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to generate name: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.name) {
        setEditingCharacter(prev => ({
          ...prev,
          name: data.name
        }));
      }
    } catch (error) {
      console.error('Error generating name:', error);
    }
  }, [activeBookId, currentBook]);

  // Handle generating character age
  const handleGenerateAge = useCallback(async (character) => {
    if (!activeBookId) return;

    try {
      const bookGenre = currentBook?.genre || 'fiction';
      const race = character?.race || '';
      const role = character?.role || '';

      const response = await fetch(`${BASE_URL}/books/${activeBookId}/characters/generate-age`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          genre: bookGenre,
          race: race,
          role: role
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to generate age: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.age) {
        setEditingCharacter(prev => ({
          ...prev,
          age: data.age
        }));
      }
    } catch (error) {
      console.error('Error generating age:', error);
    }
  }, [activeBookId, currentBook]);

  // Handle generating character race
  const handleGenerateRace = useCallback(async (character) => {
    if (!activeBookId) return;

    try {
      const bookGenre = currentBook?.genre || 'fiction';

      const response = await fetch(`${BASE_URL}/books/${activeBookId}/characters/generate-race`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          genre: bookGenre
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to generate race: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.race) {
        setEditingCharacter(prev => ({
          ...prev,
          race: data.race
        }));
      }
    } catch (error) {
      console.error('Error generating race:', error);
    }
  }, [activeBookId, currentBook]);

  // Handle generating character description
  const handleGenerateDescription = useCallback(async (character) => {
    if (!activeBookId) return;

    try {
      const bookGenre = currentBook?.genre || 'fiction';
      const name = character?.name || '';
      const race = character?.race || '';
      const age = character?.age || '';

      const response = await fetch(`${BASE_URL}/books/${activeBookId}/characters/generate-description`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          genre: bookGenre,
          name: name,
          race: race,
          age: age
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to generate description: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.description) {
        setEditingCharacter(prev => ({
          ...prev,
          description: data.description
        }));
      }
    } catch (error) {
      console.error('Error generating description:', error);
    }
  }, [activeBookId, currentBook]);

  // Handle generating character traits
  const handleGenerateTraits = useCallback(async (character) => {
    if (!activeBookId) return;

    try {
      const bookGenre = currentBook?.genre || 'fiction';
      const name = character?.name || '';
      const role = character?.role || '';
      const description = character?.description || '';

      console.log('Generating traits for character:', { name, role, description });

      // Default value in case of error
      let formattedTraits = '';

      try {
        const response = await fetch(`${BASE_URL}/books/${activeBookId}/characters/generate-traits`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          },
          body: JSON.stringify({
            genre: bookGenre,
            name: name,
            role: role,
            description: description
          })
        });

        if (!response.ok) {
          throw new Error(`Failed to generate traits: ${response.statusText}`);
        }

        const data = await response.json();
        console.log('Received traits data from API:', data);

        if (data.traits && Array.isArray(data.traits)) {
          formattedTraits = data.traits.join('\n');
          console.log('Formatted traits for form:', formattedTraits);
        } else {
          console.error('Invalid traits data format received:', data);
          formattedTraits = ''; // Default to empty string if invalid data
        }
      } catch (fetchError) {
        console.error('Error fetching traits:', fetchError);
        // Use a default value if the API call fails
        formattedTraits = '';
      }

      // Always update the character with the new traits, using the same pattern as other attributes
      console.log('About to update editingCharacter with traits:', formattedTraits);
      console.log('Current editingCharacter:', editingCharacter);

      // Create a new object to ensure React detects the change
      const updatedCharacter = {
        ...editingCharacter,
        traits: formattedTraits
      };
      console.log('Updated character with traits:', updatedCharacter);

      // Force a re-render by creating a new object
      setEditingCharacter(null); // First clear the current character
      setTimeout(() => {
        setEditingCharacter(updatedCharacter); // Then set the updated character
        console.log('Character updated with new traits');
      }, 10);


    } catch (error) {
      console.error('Error in handleGenerateTraits:', error);
    }
  }, [activeBookId, currentBook, editingCharacter]);

  // Handle generating character backstory
  const handleGenerateBackstory = useCallback(async (character) => {
    if (!activeBookId) return;

    try {
      const bookGenre = currentBook?.genre || 'fiction';
      const name = character?.name || '';
      const race = character?.race || '';
      const age = character?.age || '';
      const traits = character?.traits ?
        (typeof character.traits === 'string' ? character.traits.split('\n') : character.traits) :
        [];

      const response = await fetch(`${BASE_URL}/books/${activeBookId}/characters/generate-backstory`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          genre: bookGenre,
          name: name,
          race: race,
          age: age,
          traits: traits
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to generate backstory: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.backstory) {
        setEditingCharacter(prev => ({
          ...prev,
          backstory: data.backstory
        }));
      }
    } catch (error) {
      console.error('Error generating backstory:', error);
    }
  }, [activeBookId, currentBook]);

  // Handle generating character arc
  const handleGenerateArc = useCallback(async (character) => {
    if (!activeBookId) return;

    try {
      const bookGenre = currentBook?.genre || 'fiction';
      const name = character?.name || '';
      const role = character?.role || '';
      const backstory = character?.backstory || '';
      const traits = character?.traits ?
        (typeof character.traits === 'string' ? character.traits.split('\n') : character.traits) :
        [];

      const response = await fetch(`${BASE_URL}/books/${activeBookId}/characters/generate-arc`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          genre: bookGenre,
          name: name,
          role: role,
          backstory: backstory,
          traits: traits
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to generate character arc: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.arc) {
        setEditingCharacter(prev => ({
          ...prev,
          arc: data.arc
        }));
      }
    } catch (error) {
      console.error('Error generating character arc:', error);
    }
  }, [activeBookId, currentBook]);

  // Handle generating character relationships
  const handleGenerateRelationships = useCallback(async (character) => {
    if (!activeBookId) return;

    console.log('handleGenerateRelationships called with character:', character);

    try {
      const bookGenre = currentBook?.genre || 'fiction';
      const name = character?.name || '';

      // Get existing characters excluding the current one
      const existingCharacters = characters.filter(c => c.id !== character.id).map(char => ({
        id: char.id,
        name: char.name,
        role: char.role || '',
        description: char.description || '',
        age: char.age || '',
        backstory: char.backstory || '',
        gender_identity: char.gender_identity || '',
        sexual_orientation: char.sexual_orientation || ''
      }));

      console.log('Existing characters for relationships:', existingCharacters);

      if (existingCharacters.length === 0) {
        alert('No other characters exist to create relationships with.');
        return;
      }

      // Default value in case of error
      let formattedRelationships = '';

      try {
        console.log('Sending relationship generation request with:', {
          genre: bookGenre,
          name: name,
          existingCharacters: existingCharacters
        });

        const response = await fetch(`${BASE_URL}/books/${activeBookId}/characters/generate-relationships`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          },
          body: JSON.stringify({
            genre: bookGenre,
            name: name,
            existingCharacters: existingCharacters
          })
        });

        if (!response.ok) {
          throw new Error(`Failed to generate relationships: ${response.statusText}`);
        }

        const data = await response.json();
        console.log('Received relationship data from API:', data);

        if (data.relationships && Array.isArray(data.relationships)) {
          // Format relationships for the form
          formattedRelationships = data.relationships.map(rel => {
            // Handle both backend formats (original API response and transformed)
            const name = rel.relatedCharacterName || rel.character || '';
            const type = rel.relationshipType || rel.type || '';
            const description = rel.description || '';
            const strength = rel.strength !== undefined ? rel.strength : 3; // Default to 3 if not specified

            console.log('Processing relationship:', { name, type, description, strength });

            // Include strength in the formatted string
            return `${name}:${type}:${description}:${strength}`;
          }).join('\n');

          console.log('Formatted relationships for form:', formattedRelationships);
        } else {
          console.error('Invalid relationship data format received:', data);
          formattedRelationships = ''; // Default to empty string if invalid data
        }
      } catch (fetchError) {
        console.error('Error fetching relationships:', fetchError);
        // Use a default value if the API call fails
        formattedRelationships = '';
      }

      // Always update the character with the new relationships, using the same pattern as other attributes
      setEditingCharacter(prev => ({
        ...prev,
        relationships: formattedRelationships
      }));

      console.log('Updated character with relationships using standard pattern');

    } catch (error) {
      console.error('Error in handleGenerateRelationships:', error);
    }
  }, [activeBookId, currentBook, characters, editingCharacter]);

  // Handle generating gender identity
  const handleGenerateGenderIdentity = useCallback(async (character) => {
    if (!activeBookId) return;

    try {
      const bookGenre = currentBook?.genre || 'fiction';
      const name = character?.name || '';
      const race = character?.race || '';
      const description = character?.description || '';
      const backstory = character?.backstory || '';

      // Default value in case of error
      let genderIdentity = '';

      try {
        const response = await fetch(`${BASE_URL}/books/${activeBookId}/characters/generate-gender-identity`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          },
          body: JSON.stringify({
            genre: bookGenre,
            name: name,
            race: race,
            description: description,
            backstory: backstory
          })
        });

        if (!response.ok) {
          throw new Error(`Failed to generate gender identity: ${response.statusText}`);
        }

        const data = await response.json();

        if (data.gender_identity) {
          genderIdentity = data.gender_identity;
        }
      } catch (fetchError) {
        console.error('Error fetching gender identity:', fetchError);
        // Use a default value if the API call fails
        genderIdentity = 'Not specified';
      }

      // Always update the character, even if we're using a default value
      setEditingCharacter(prev => ({
        ...prev,
        gender_identity: genderIdentity
      }));
    } catch (error) {
      console.error('Error in handleGenerateGenderIdentity:', error);
      // Update with a default value in case of any error
      setEditingCharacter(prev => ({
        ...prev,
        gender_identity: 'Not specified'
      }));
    }
  }, [activeBookId, currentBook]);

  // Handle generating sexual orientation
  const handleGenerateSexualOrientation = useCallback(async (character) => {
    if (!activeBookId) return;

    try {
      const bookGenre = currentBook?.genre || 'fiction';
      const name = character?.name || '';
      const gender_identity = character?.gender_identity || '';
      const backstory = character?.backstory || '';

      // Default value in case of error
      let sexualOrientation = '';

      try {
        const response = await fetch(`${BASE_URL}/books/${activeBookId}/characters/generate-sexual-orientation`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          },
          body: JSON.stringify({
            genre: bookGenre,
            name: name,
            gender_identity: gender_identity,
            backstory: backstory
          })
        });

        if (!response.ok) {
          throw new Error(`Failed to generate sexual orientation: ${response.statusText}`);
        }

        const data = await response.json();

        if (data.sexual_orientation) {
          sexualOrientation = data.sexual_orientation;
        }
      } catch (fetchError) {
        console.error('Error fetching sexual orientation:', fetchError);
        // Use a default value if the API call fails
        sexualOrientation = 'Not specified';
      }

      // Always update the character, even if we're using a default value
      setEditingCharacter(prev => ({
        ...prev,
        sexual_orientation: sexualOrientation
      }));
    } catch (error) {
      console.error('Error in handleGenerateSexualOrientation:', error);
      // Update with a default value in case of any error
      setEditingCharacter(prev => ({
        ...prev,
        sexual_orientation: 'Not specified'
      }));
    }
  }, [activeBookId, currentBook]);

  return (
    <CharactersPage
      characters={characters}
      comparedCharacters={comparedCharacters}
      editingCharacter={editingCharacter}
      worldSettingTypes={worldSettingTypes}
      isLoading={isLoading || isGeneratingHeadshot}
      isGeneratingCharacter={isGeneratingCharacter}
      error={error}
      setEditingCharacter={setEditingCharacter}
      handleEditCharacter={handleEditCharacter}
      handleCreateCharacter={handleCreateCharacter}
      handleSaveCharacter={handleSaveCharacter}
      handleDeleteCharacter={handleDeleteCharacter}
      handleCompareCharacter={handleCompareCharacter}
      handleGenerateCharacter={handleGenerateCharacter}
      handleGenerateHeadshot={handleGenerateHeadshot}
      handleDeleteHeadshot={handleDeleteHeadshot}
      handleCancelEdit={handleCancelEdit}
      handleGenerateName={handleGenerateName}
      handleGenerateAge={handleGenerateAge}
      handleGenerateRace={handleGenerateRace}
      handleGenerateDescription={handleGenerateDescription}
      handleGenerateTraits={handleGenerateTraits}
      handleGenerateBackstory={handleGenerateBackstory}
      handleGenerateArc={handleGenerateArc}
      handleGenerateRelationships={handleGenerateRelationships}
      handleGenerateGenderIdentity={handleGenerateGenderIdentity}
      handleGenerateSexualOrientation={handleGenerateSexualOrientation}
    />
  );
};

export default CharactersPageContainer;

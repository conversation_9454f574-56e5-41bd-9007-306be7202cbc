// frontend/src/containers/CharacterListContainer.js
import React, { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useParams } from 'react-router-dom';
import { 
  fetchAllCharacters, 
  createCharacter, 
  updateExistingCharacter, 
  removeCharacter, 
  generateAICharacter,
  selectCharacter,
  clearError,
  selectAllCharacters,
  selectCurrentCharacter,
  selectCharactersLoading,
  selectCharactersSaving,
  selectCharactersError
} from '../redux/slices/charactersSlice';
import { selectCurrentBook } from '../redux/slices/bookSlice';
import CharacterList from '../components/CharacterList';

/**
 * Container component for CharacterList that handles state management and data fetching
 * @returns {JSX.Element} CharacterList container
 */
const CharacterListContainer = () => {
  const { bookId } = useParams();
  const dispatch = useDispatch();
  
  // Select state from Redux store
  const characters = useSelector(selectAllCharacters);
  const selectedCharacter = useSelector(selectCurrentCharacter);
  const currentBook = useSelector(selectCurrentBook);
  const isLoading = useSelector(selectCharactersLoading);
  const isSaving = useSelector(selectCharactersSaving);
  const error = useSelector(selectCharactersError);
  
  // Use current book ID if not provided in URL
  const activeBookId = bookId || (currentBook && currentBook.book_id);
  
  // Fetch characters on component mount or when book ID changes
  useEffect(() => {
    if (activeBookId) {
      dispatch(fetchAllCharacters(activeBookId));
    }
  }, [dispatch, activeBookId]);
  
  // Handle character selection
  const handleSelectCharacter = (character) => {
    dispatch(selectCharacter(character));
  };
  
  // Handle character creation
  const handleCreateCharacter = (characterData) => {
    if (activeBookId) {
      dispatch(createCharacter({ bookId: activeBookId, characterData }));
    }
  };
  
  // Handle character update
  const handleUpdateCharacter = ({ characterId, characterData }) => {
    if (activeBookId) {
      dispatch(updateExistingCharacter({ 
        bookId: activeBookId, 
        characterId, 
        characterData 
      }));
    }
  };
  
  // Handle character deletion
  const handleDeleteCharacter = (characterId) => {
    if (activeBookId) {
      dispatch(removeCharacter({ bookId: activeBookId, characterId }));
    }
  };
  
  // Handle AI character generation
  const handleGenerateCharacter = ({ prompt }) => {
    if (activeBookId) {
      dispatch(generateAICharacter({ bookId: activeBookId, prompt }));
    }
  };
  
  // Handle clear error
  const handleClearError = () => {
    dispatch(clearError());
  };
  
  // If no book is selected, show a message
  if (!activeBookId) {
    return (
      <div className="no-book-selected">
        <h2>No Book Selected</h2>
        <p>Please select a book to manage characters.</p>
      </div>
    );
  }
  
  return (
    <CharacterList
      characters={characters}
      selectedCharacter={selectedCharacter}
      isLoading={isLoading}
      isSaving={isSaving}
      error={error}
      onSelectCharacter={handleSelectCharacter}
      onCreateCharacter={handleCreateCharacter}
      onUpdateCharacter={handleUpdateCharacter}
      onDeleteCharacter={handleDeleteCharacter}
      onGenerateCharacter={handleGenerateCharacter}
      onClearError={handleClearError}
    />
  );
};

export default CharacterListContainer;

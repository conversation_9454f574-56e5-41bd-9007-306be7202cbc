// frontend/src/containers/AdminPanelContainer.js
import React, { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigate, useParams } from 'react-router-dom';
import { selectAuthToken, selectIsAuthenticated } from '../redux/slices/authSlice';
import AdminPanel from '../components/AdminPanel/AdminPanel';

/**
 * Container component for the Admin Panel
 * Handles data fetching, state management, and business logic for the admin panel
 */
const AdminPanelContainer = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { tabId } = useParams();
  
  // Get auth state from Redux
  const token = useSelector(selectAuthToken);
  const isAuthenticated = useSelector(selectIsAuthenticated);
  
  // Local state
  const [activeTab, setActiveTab] = useState(tabId || 'dashboard');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  
  // Check if user is authenticated and has admin privileges
  // This is a placeholder - you'll need to implement proper admin role checking
  const [isAdmin, setIsAdmin] = useState(true);
  
  // Effect to handle tab changes from URL
  useEffect(() => {
    if (tabId) {
      setActiveTab(tabId);
    }
  }, [tabId]);
  
  // Effect to check admin status
  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login');
      return;
    }
    
    // Here you would check if the user has admin privileges
    // For now, we're assuming all authenticated users can access the admin panel
    // In a real application, you would check the user's role
    
    // Example of how you might check admin status:
    // const checkAdminStatus = async () => {
    //   try {
    //     const response = await fetch('/api/users/check-admin', {
    //       headers: { Authorization: `Bearer ${token}` }
    //     });
    //     const data = await response.json();
    //     setIsAdmin(data.isAdmin);
    //     if (!data.isAdmin) {
    //       navigate('/');
    //     }
    //   } catch (error) {
    //     console.error('Error checking admin status:', error);
    //     setError('Failed to verify admin privileges');
    //     navigate('/');
    //   }
    // };
    // 
    // checkAdminStatus();
  }, [isAuthenticated, navigate, token]);
  
  // Handle tab change
  const handleTabChange = (tabId) => {
    setActiveTab(tabId);
    navigate(`/admin/${tabId}`);
  };
  
  // If not authenticated or not admin, don't render the component
  if (!isAuthenticated || !isAdmin) {
    return null;
  }
  
  return (
    <AdminPanel 
      activeTab={activeTab}
      onTabChange={handleTabChange}
      isLoading={isLoading}
      error={error}
    />
  );
};

export default AdminPanelContainer;

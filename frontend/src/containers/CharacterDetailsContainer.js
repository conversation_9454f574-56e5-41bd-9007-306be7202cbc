// frontend/src/containers/CharacterDetailsContainer.js
import React from 'react';
import { useSelector, useDispatch } from 'react-redux';
import {
  updateExistingCharacter,
  addCharacterRelationship,
  updateCharacterRelationship,
  removeCharacterRelationship,
  clearError,
  selectAllCharacters,
  selectCurrentCharacter,
  selectCharacterRelationships,
  selectCharactersLoading,
  selectCharactersError
} from '../redux/slices/charactersSlice';
import { selectCurrentBook } from '../redux/slices/bookSlice';
import CharacterDetails from '../components/CharacterDetails';

/**
 * Container component for CharacterDetails that handles state management
 * @returns {JSX.Element} CharacterDetails container
 */
const CharacterDetailsContainer = () => {
  const dispatch = useDispatch();

  // Select state from Redux store
  const character = useSelector(selectCurrentCharacter);
  const characters = useSelector(selectAllCharacters);
  const currentBook = useSelector(selectCurrentBook);
  const isLoading = useSelector(selectCharactersLoading);
  const error = useSelector(selectCharactersError);

  // Get relationships for the current character
  const relationships = useSelector(
    state => character ? selectCharacterRelationships(state, character.id) : []
  );

  // Handle character update
  const handleUpdateCharacter = ({ characterId, characterData }) => {
    if (currentBook) {
      dispatch(updateExistingCharacter({
        bookId: currentBook.book_id,
        characterId,
        characterData
      }));
    }
  };

  // Handle add relationship
  const handleAddRelationship = ({ characterId, relatedCharacterId, relationshipType, strength }) => {
    dispatch(addCharacterRelationship({
      characterId,
      relatedCharacterId,
      relationshipType,
      strength
    }));
  };

  // Handle update relationship
  const handleUpdateRelationship = ({ characterId, relationshipId, relationshipType, strength }) => {
    dispatch(updateCharacterRelationship({
      characterId,
      relationshipId,
      relationshipType,
      strength
    }));
  };

  // Handle remove relationship
  const handleRemoveRelationship = ({ characterId, relationshipId }) => {
    dispatch(removeCharacterRelationship({
      characterId,
      relationshipId
    }));
  };

  // Handle clear error
  const handleClearError = () => {
    dispatch(clearError());
  };

  return (
    <CharacterDetails
      character={character}
      characters={characters}
      relationships={relationships}
      isLoading={isLoading}
      error={error}
      onUpdateCharacter={handleUpdateCharacter}
      onAddRelationship={handleAddRelationship}
      onUpdateRelationship={handleUpdateRelationship}
      onRemoveRelationship={handleRemoveRelationship}
      onClearError={handleClearError}
    />
  );
};

export default CharacterDetailsContainer;

// frontend/src/containers/BookDetailsPageContainer.js
import React, { useEffect, useState, useCallback } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import {
  fetchBookById,
  fetchMetadata,
  clearError,
  updateExistingBook,
  selectCurrentBook,
  selectBookMetadata,
  selectBooksLoading,
  selectBooksError,
  selectBookDetail,
  selectIsBookDetailLoading
} from '../redux/slices/bookSlice';
import BookDetailsPage from '../components/BookDetailsPage';

/**
 * Container component for BookDetailsPage that handles state management and data fetching
 * @returns {JSX.Element} BookDetailsPage container
 */
const BookDetailsPageContainer = () => {
  const { bookId } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();

  // Select state from Redux store
  const book = useSelector(selectCurrentBook);
  const metadata = useSelector((state) => selectBookMetadata(state, bookId));
  const isLoading = useSelector(selectBooksLoading);
  const error = useSelector(selectBooksError);

  // Get book details from cache and loading state
  const bookDetail = useSelector((state) => selectBookDetail(state, bookId));
  const isBookDetailLoading = useSelector((state) => selectIsBookDetailLoading(state, bookId));

  // Fetch book details and metadata on component mount
  useEffect(() => {
    // Check if we have a valid book object with all required fields
    const hasValidBook = book &&
                        book.book_id &&
                        book.title &&
                        typeof book.author !== 'undefined' &&
                        typeof book.genre !== 'undefined' &&
                        typeof book.description !== 'undefined';

    // Check if we have valid book details in the cache
    const hasValidBookDetail = bookDetail &&
                              bookDetail.book_id &&
                              bookDetail.title &&
                              typeof bookDetail.author !== 'undefined' &&
                              typeof bookDetail.genre !== 'undefined' &&
                              typeof bookDetail.description !== 'undefined';

    console.log('BookDetailsPageContainer: Current book state:', {
      hasBook: !!book,
      bookId: book?.book_id,
      hasValidBook,
      hasBookDetail: !!bookDetail,
      hasValidBookDetail,
      isBookDetailLoading,
      metadata
    });

    // If we're on a specific book page, fetch that book's data
    if (bookId && window.location.pathname.includes(`/books/${bookId}`)) {
      // Always fetch metadata to ensure we have the latest statistics
      console.log('BookDetailsPageContainer: Fetching metadata for bookId:', bookId);
      dispatch(fetchMetadata(bookId));

      // Fetch book details if we don't have valid book details in the cache and it's not already loading
      if (!hasValidBookDetail && !isBookDetailLoading) {
        console.log('BookDetailsPageContainer: Book details not in cache, fetching details');
        dispatch(fetchBookById(bookId));
      }
    } else if (book?.book_id && !hasValidBook && !isBookDetailLoading) {
      // If we're on the main books page and have a selected book but it's not fully loaded
      console.log('BookDetailsPageContainer: On main page with incomplete book data, fetching details for:', book.book_id);
      dispatch(fetchBookById(book.book_id));
    }
  }, [dispatch, bookId, book, bookDetail, isBookDetailLoading]);

  // Navigation handlers
  const handleNavigateToWorld = () => {
    navigate('/world');
  };

  const handleNavigateToCharacters = () => {
    navigate('/characters');
  };

  const handleNavigateToPlot = () => {
    navigate('/plot');
  };

  const handleNavigateToBrainstorm = () => {
    navigate('/brainstorm/page1');
  };

  const handleNavigateToWrite = () => {
    navigate('/write');
  };

  // Handle clear error
  const handleClearError = () => {
    dispatch(clearError());
  };

  // Debounce function
  const debounce = (func, wait) => {
    let timeout;
    return (...args) => {
      clearTimeout(timeout);
      timeout = setTimeout(() => func(...args), wait);
    };
  };

  // Handle book update with debounce
  const updateBookWithDebounce = useCallback(
    debounce((bookId, bookData) => {
      console.log('Updating book:', bookId, bookData);
      dispatch(updateExistingBook({ bookId, bookData }));
    }, 300),
    [dispatch]
  );

  // Handle book update
  const handleUpdateBook = (bookId, bookData) => {
    updateBookWithDebounce(bookId, bookData);
  };

  // Use book details from cache if available, otherwise use the selected book
  const displayBook = bookDetail || book;

  // Debug log for metadata
  console.log('BookDetailsPageContainer: Final metadata being passed to component:', metadata);
  console.log('BookDetailsPageContainer: Book ID for metadata:', bookId);
  console.log('BookDetailsPageContainer: Book ID from displayBook:', displayBook?.book_id);

  // Force fetch metadata if it's empty
  useEffect(() => {
    if (bookId && (!metadata || Object.keys(metadata).length === 0)) {
      console.log('BookDetailsPageContainer: Forcing metadata fetch for bookId:', bookId);
      dispatch(fetchMetadata(bookId));
    }
  }, [bookId, metadata, dispatch]);

  return (
    <BookDetailsPage
      book={displayBook}
      metadata={metadata}
      isLoading={isLoading || isBookDetailLoading}
      error={error}
      onNavigateToWorld={handleNavigateToWorld}
      onNavigateToCharacters={handleNavigateToCharacters}
      onNavigateToPlot={handleNavigateToPlot}
      onNavigateToBrainstorm={handleNavigateToBrainstorm}
      onNavigateToWrite={handleNavigateToWrite}
      onClearError={handleClearError}
      onUpdateBook={handleUpdateBook}
    />
  );
};

export default BookDetailsPageContainer;

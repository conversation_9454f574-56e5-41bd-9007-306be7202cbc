// frontend/src/containers/UserProfilePageContainer.js
import React from 'react';
import { useSelector, useDispatch } from 'react-redux';
import {
  selectUser,
  selectSettings,
  selectAuthLoading,
  selectAuthError,
  updateProfile,
  updateTheme,
  updateFontSize,
  updateAutoSave,
  updateNotifications,
  clearError
} from '../redux/slices/authSlice';
import UserProfilePage from '../components/UserProfilePage';

/**
 * Container component for UserProfilePage that handles state management and data fetching
 * @returns {JSX.Element} UserProfilePage container
 */
const UserProfilePageContainer = () => {
  const dispatch = useDispatch();

  // Select state from Redux store
  const user = useSelector(selectUser);
  const settings = useSelector(selectSettings);
  const isLoading = useSelector(selectAuthLoading);
  const error = useSelector(selectAuthError);

  // Handle profile update
  const handleUpdateProfile = (userData) => {
    dispatch(updateProfile(userData));
  };

  // Handle theme update
  const handleUpdateTheme = (theme) => {
    dispatch(updateTheme(theme));
    console.debug('UserProfilePageContainer: Theme updated to', theme);
  };

  // Handle font size update
  const handleUpdateFontSize = (fontSize) => {
    dispatch(updateFontSize(fontSize));
  };

  // Handle auto save toggle
  const handleUpdateAutoSave = (autoSave) => {
    dispatch(updateAutoSave(autoSave));
  };

  // Handle notifications toggle
  const handleUpdateNotifications = (notifications) => {
    dispatch(updateNotifications(notifications));
  };

  // Handle clear error
  const handleClearError = () => {
    dispatch(clearError());
  };

  return (
    <UserProfilePage
      user={user}
      settings={settings}
      isLoading={isLoading}
      error={error}
      onUpdateProfile={handleUpdateProfile}
      onUpdateTheme={handleUpdateTheme}
      onUpdateFontSize={handleUpdateFontSize}
      onUpdateAutoSave={handleUpdateAutoSave}
      onUpdateNotifications={handleUpdateNotifications}
      onClearError={handleClearError}
    />
  );
};

export default UserProfilePageContainer;

// frontend/src/containers/BookListContainer.js
import React, { useEffect, useCallback } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import {
  fetchAllBooks,
  fetchBookById,
  createNewBook,
  updateExistingBook,
  deleteExistingBook,
  selectBook,
  clearError,
  selectAllBooks,
  selectCurrentBook,
  selectBooksLoading,
  selectBooksSaving,
  selectBooksError,
  selectBookDetail
} from '../redux/slices/bookSlice';
import BookList from '../components/BookList';

/**
 * Container component for BookList that handles state management and data fetching
 * @returns {JSX.Element} BookList container
 */
const BookListContainer = () => {
  const dispatch = useDispatch();

  // Select state from Redux store
  const books = useSelector(selectAllBooks);
  const selectedBook = useSelector(selectCurrentBook);
  const isLoading = useSelector(selectBooksLoading);
  const isSaving = useSelector(selectBooksSaving);
  const error = useSelector(selectBooksError);

  // Fetch details for all books when the component mounts
  useEffect(() => {
    // Check if we have books but they might not have complete details
    if (books && books.length > 0) {
      console.log('BookListContainer: Books already loaded, checking for complete details');

      // Fetch details for all books to ensure we have complete data
      const fetchAllBookDetails = async () => {
        console.log('BookListContainer: Fetching details for all books');
        for (const book of books) {
          // Check if we already have complete details for this book
          const hasCompleteDetails = book.title &&
                                    typeof book.author !== 'undefined' &&
                                    typeof book.genre !== 'undefined' &&
                                    typeof book.description !== 'undefined';

          if (!hasCompleteDetails) {
            console.log(`BookListContainer: Fetching details for book ${book.book_id} - ${book.title}`);
            await dispatch(fetchBookById(book.book_id));
            // Add a small delay to prevent overwhelming the server
            await new Promise(resolve => setTimeout(resolve, 100));
          }
        }
        console.log('BookListContainer: All book details fetched');
      };

      fetchAllBookDetails();
    }
  }, [books, dispatch]);

  // Debounce function
  const debounce = (func, wait) => {
    let timeout;
    return (...args) => {
      clearTimeout(timeout);
      timeout = setTimeout(() => func(...args), wait);
    };
  };

  // Handle book selection with debounce
  const selectBookWithDebounce = useCallback(
    debounce((book) => {
      // Just dispatch selectBook action when on the Books page
      // This prevents the infinite loop of API calls
      dispatch(selectBook(book));

      // Save the book ID to localStorage for persistence
      // This will be used by other pages to load the selected book
      localStorage.setItem('lastBookId', book.book_id);

      console.log('BookListContainer: Selected book:', book.title);

      // Fetch full book details after selection
      dispatch(fetchBookById(book.book_id));
    }, 300),
    [dispatch]
  );

  // Handle book selection
  const handleSelectBook = (book) => {
    selectBookWithDebounce(book);
  };

  // Handle book creation
  const handleCreateBook = (bookData) => {
    dispatch(createNewBook(bookData));
  };

  // Debounce function for updates
  const debounceUpdate = (func, wait) => {
    let timeout;
    return (...args) => {
      clearTimeout(timeout);
      timeout = setTimeout(() => func(...args), wait);
    };
  };

  // Handle book update with debounce
  const updateBookWithDebounce = useCallback(
    debounceUpdate(({ bookId, bookData }) => {
      dispatch(updateExistingBook({ bookId, bookData }))
        .then(() => {
          // After updating, fetch the full book details to ensure cache is updated
          dispatch(fetchBookById(bookId));
        });
    }, 300),
    [dispatch]
  );

  // Handle book update
  const handleUpdateBook = ({ bookId, bookData }) => {
    updateBookWithDebounce({ bookId, bookData });
  };

  // Handle book deletion
  const handleDeleteBook = (bookId) => {
    dispatch(deleteExistingBook(bookId));
  };

  // Handle clear error
  const handleClearError = () => {
    dispatch(clearError());
  };

  return (
    <BookList
      books={books}
      selectedBook={selectedBook}
      isLoading={isLoading}
      isSaving={isSaving}
      error={error}
      onSelectBook={handleSelectBook}
      onCreateBook={handleCreateBook}
      onUpdateBook={handleUpdateBook}
      onDeleteBook={handleDeleteBook}
      onClearError={handleClearError}
    />
  );
};

export default BookListContainer;

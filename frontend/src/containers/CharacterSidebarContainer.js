// frontend/src/containers/CharacterSidebarContainer.js
import React, { useCallback } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import {
  fetchAllCharacters,
  createCharacter,
  remove<PERSON>hara<PERSON>,
  generateAICharacter,
  selectAllCharacters
} from '../redux/slices/charactersSlice';
import { selectCurrentBook } from '../redux/slices/bookSlice';
import CharacterSidebar from '../components/CharacterSidebar';

/**
 * Container component for CharacterSidebar that handles state management and data fetching
 * @returns {JSX.Element} CharacterSidebar container
 */
const CharacterSidebarContainer = () => {
  const dispatch = useDispatch();

  // Select state from Redux store
  const characters = useSelector(selectAllCharacters);
  const currentBook = useSelector(selectCurrentBook);
  
  // Use current book ID
  const activeBookId = currentBook?.book_id;

  // Handle adding a character
  const handleAddCharacter = useCallback(async (characterData) => {
    if (!activeBookId) return;

    try {
      await dispatch(createCharacter({
        bookId: activeBookId,
        characterData
      }));
    } catch (error) {
      console.error('Error adding character:', error);
    }
  }, [activeBookId, dispatch]);

  // Handle deleting a character
  const handleDeleteCharacter = useCallback(async (characterId) => {
    if (!activeBookId) return;

    try {
      await dispatch(removeCharacter({
        bookId: activeBookId,
        characterId
      }));
    } catch (error) {
      console.error('Error deleting character:', error);
    }
  }, [activeBookId, dispatch]);

  // Handle generating an AI character
  const handleGenerateAICharacter = useCallback(async (prompt) => {
    if (!activeBookId) return;

    try {
      const resultAction = await dispatch(generateAICharacter({
        bookId: activeBookId,
        prompt
      }));

      if (generateAICharacter.fulfilled.match(resultAction)) {
        return resultAction.payload;
      }
    } catch (error) {
      console.error('Error generating AI character:', error);
    }
  }, [activeBookId, dispatch]);

  // Handle refreshing characters
  const handleRefreshCharacters = useCallback(() => {
    if (activeBookId) {
      dispatch(fetchAllCharacters(activeBookId));
    }
  }, [activeBookId, dispatch]);

  return (
    <CharacterSidebar
      characters={characters}
      selectedBook={currentBook}
      onAddCharacter={handleAddCharacter}
      onDeleteCharacter={handleDeleteCharacter}
      onGenerateAICharacter={handleGenerateAICharacter}
      onRefreshCharacters={handleRefreshCharacters}
    />
  );
};

export default CharacterSidebarContainer;

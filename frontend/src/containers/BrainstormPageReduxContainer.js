// frontend/src/containers/BrainstormPageReduxContainer.js
import React, { useEffect, useCallback, useState, useRef } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useParams, useNavigate } from 'react-router-dom';
import { saveWorldElementsToStorage, loadWorldElementsFromStorage } from '../utils/localStorageUtils';
import {
  fetchAllBrainstormCards,
  createBrainstormCard,
  updateExistingBrainstormCard,
  removeBrainstormCard,
  generateAIIdea,
  connectSelectedIdeas,
  updateNodePosition,
  selectAllBrainstormCards,
  selectCurrentPage,
  selectCardsByPage,
  selectAllPages,
  setCurrentPage,
  selectBrainstormLoading,
  selectBrainstormError,
  clearError,
  selectCurrentCard,
  selectCard,
  selectNodesByPage,
  selectEdgesByPage,
  addEdge,
  removeEdge,
  sendNodeToPLot,
  updateFetchStatus,
  resetFetchStatus,
  cleanupOldDeletedCardIds
} from '../redux/slices/brainstormSlice';
// We're now using the connectSelectedIdeas from brainstormSlice instead of connectIdeas from aiSlice
import { selectCurrentBook } from '../redux/slices/bookSlice';
import { fetchAllCharacters, selectAllCharacters, createCharacter } from '../redux/slices/charactersSlice';
// Import from worldBuildingSlice
import {
  fetchAllElementsWithChildrenThunk as fetchWorld,
  selectAllElementsForRelationshipView as selectWorldElements,
  selectAllCategories,
  createWorldElement,
  setCurrentBookId
} from '../redux/slices/worldBuildingSlice';
import { createPlotEvent } from '../redux/slices/plotSlice';
import BrainstormPageReduxPresentation from '../components/BrainstormPageReduxPresentation';

/**
 * Container component for BrainstormPage that connects to Redux
 * @param {Object} props - Component props
 * @param {Function} props.onPageChange - Function to handle page change
 * @returns {JSX.Element} BrainstormPage container
 */
const BrainstormPageReduxContainer = () => {
  const { bookId, pageId } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();

  // Select state from Redux store
  const allCards = useSelector(selectAllBrainstormCards);
  const currentPage = useSelector(selectCurrentPage);
  const pages = useSelector(selectAllPages);
  const currentBook = useSelector(selectCurrentBook);
  const isLoading = useSelector(selectBrainstormLoading);
  const error = useSelector(selectBrainstormError);
  const selectedCard = useSelector(selectCurrentCard);
  const characters = useSelector(selectAllCharacters);
  const worldElements = useSelector(selectWorldElements);
  const categories = useSelector(selectAllCategories);

  // Debug log the characters, worldElements, and categories
  useEffect(() => {
    console.log('[DEBUG BrainstormContainer] Characters from Redux:', characters);
    console.log('[DEBUG BrainstormContainer] World elements from Redux:', worldElements);
    console.log('[DEBUG BrainstormContainer] Categories from Redux:', categories);
  }, [characters, worldElements, categories]);

  // Use current book ID if not provided in URL
  const activeBookId = bookId || (currentBook && currentBook.book_id);

  // Set current page from URL if provided
  useEffect(() => {
    if (pageId && pages.includes(pageId)) {
      dispatch(setCurrentPage(pageId));
    }
  }, [dispatch, pageId, pages]);

  // Get dataLoaded state and cards count from Redux
  const dataLoaded = useSelector(state => state.brainstorm.dataLoaded);
  const cardsCount = useSelector(state => state.brainstorm.cards.length);

  // Use a ref to track if component is mounted
  const isMounted = useRef(true);

  // Get fetch status from Redux
  const fetchStatus = useSelector(state => state.brainstorm.fetchStatus);

  // Track if we've already fetched data for this book
  const [fetchedForBook, setFetchedForBook] = useState(null);

  // Reset fetch status when book changes
  useEffect(() => {
    if (activeBookId && fetchedForBook !== activeBookId) {
      console.log('[DEBUG BrainstormContainer] Book ID changed or component mounted:', activeBookId);
      console.log('[DEBUG BrainstormContainer] Previously fetched for book:', fetchedForBook);

      // Reset fetch status in Redux
      dispatch(resetFetchStatus());

      // Reset fetchedForBook to ensure we fetch new data
      setFetchedForBook(null);

      console.log('[DEBUG BrainstormContainer] Reset fetch status for new book');
    }
  }, [activeBookId, fetchedForBook, dispatch]);

  // Log component mount
  useEffect(() => {
    console.log('[DEBUG BrainstormContainer] Component mounted with book ID:', activeBookId);

    // Cleanup on unmount
    return () => {
      console.log('[DEBUG BrainstormContainer] Component unmounting');
      isMounted.current = false;
    };
  }, [activeBookId]);

  // Fetch all data on component mount or when book ID changes
  useEffect(() => {
    if (!activeBookId || !isMounted.current) return;

    console.log('[DEBUG BrainstormContainer] Fetch effect triggered');
    console.log('[DEBUG BrainstormContainer] Book ID:', activeBookId);
    console.log('[DEBUG BrainstormContainer] Data loaded:', dataLoaded);
    console.log('[DEBUG BrainstormContainer] Cards count:', cardsCount);
    console.log('[DEBUG BrainstormContainer] Previously fetched for:', fetchedForBook);
    console.log('[DEBUG BrainstormContainer] Fetch status:', fetchStatus);
    console.log('[DEBUG BrainstormContainer] Characters count:', characters ? characters.length : 0);
    console.log('[DEBUG BrainstormContainer] World elements count:', worldElements ? worldElements.length : 0);

    // Check if we need to fetch data
    const needsFetch = !dataLoaded || cardsCount === 0 || fetchedForBook !== activeBookId ||
                      !characters || characters.length === 0 ||
                      !worldElements || worldElements.length === 0;
    const alreadyFetching = fetchStatus.characters || fetchStatus.world || fetchStatus.brainstorm;

    if (needsFetch && !alreadyFetching) {
      console.log('[DEBUG BrainstormContainer] Fetching all data for book:', activeBookId);

      // First reset fetch status to ensure we're starting fresh
      dispatch(resetFetchStatus());

      // Fetch data directly instead of using the combined action
      const fetchAllData = async () => {
        try {
          // First clean up old deleted card IDs
          await dispatch(cleanupOldDeletedCardIds());

          // Fetch characters
          console.log('[DEBUG BrainstormContainer] Fetching characters');
          await dispatch(fetchAllCharacters(activeBookId));
          dispatch(updateFetchStatus({ dataType: 'characters', status: true }));

          // Fetch world data
          console.log('[DEBUG BrainstormContainer] Fetching world data');
          dispatch(setCurrentBookId(activeBookId));
          await dispatch(fetchWorld({ bookId: activeBookId }));
          dispatch(updateFetchStatus({ dataType: 'world', status: true }));

          // Fetch brainstorm cards
          console.log('[DEBUG BrainstormContainer] Fetching brainstorm cards');
          await dispatch(fetchAllBrainstormCards(activeBookId));
          dispatch(updateFetchStatus({ dataType: 'brainstorm', status: true }));

          console.log('[DEBUG BrainstormContainer] Successfully fetched all data');
          console.log('[DEBUG BrainstormContainer] Characters after fetch:', characters ? characters.length : 0);
          console.log('[DEBUG BrainstormContainer] World elements after fetch:', worldElements ? worldElements.length : 0);

          setFetchedForBook(activeBookId);
        } catch (error) {
          console.error('[DEBUG BrainstormContainer] Error fetching data:', error);
          dispatch(resetFetchStatus());
        }
      };

      // Execute the fetch function
      fetchAllData();
    } else {
      console.log('[DEBUG BrainstormContainer] Skipping data fetch - data already loaded or fetch in progress');
    }
  }, [dispatch, activeBookId, dataLoaded, cardsCount, fetchedForBook, fetchStatus, characters, worldElements]);

  // Get cards for the current page using memoized selector
  const cards = useSelector(state => selectCardsByPage(state, currentPage), (prev, next) => {
    // Custom equality function to prevent unnecessary re-renders
    if (!prev || !next) return prev === next;
    if (prev.length !== next.length) return false;
    return prev.every((card, index) => {
      const nextCard = next[index];
      return card.id === nextCard.id &&
             card.title === nextCard.title &&
             card.content === nextCard.content &&
             card.type === nextCard.type &&
             JSON.stringify(card.position) === JSON.stringify(nextCard.position);
    });
  });

  // Get nodes for the current page using memoized selector
  const nodes = useSelector(state => selectNodesByPage(state, currentPage), (prev, next) => {
    // Custom equality function to prevent unnecessary re-renders
    if (!prev || !next) return prev === next;
    if (prev.length !== next.length) return false;
    return prev.every((node, index) => {
      const nextNode = next[index];
      return node.id === nextNode.id &&
             JSON.stringify(node.position) === JSON.stringify(nextNode.position) &&
             JSON.stringify(node.data) === JSON.stringify(nextNode.data);
    });
  });

  // Get edges for the current page using memoized selector
  const edges = useSelector(state => selectEdgesByPage(state, currentPage), (prev, next) => {
    // Custom equality function to prevent unnecessary re-renders
    if (!prev || !next) return prev === next;
    if (prev.length !== next.length) return false;
    return prev.every((edge, index) =>
      edge.id === next[index].id &&
      edge.source === next[index].source &&
      edge.target === next[index].target
    );
  });

  // Handle page change
  const handlePageChange = useCallback((page) => {
    dispatch(setCurrentPage(page));
    navigate(`/brainstorm/${page}`);
  }, [dispatch, navigate]);

  // Handle card selection
  const handleCardSelect = useCallback((cardId) => {
    dispatch(selectCard(cardId));
  }, [dispatch]);

  // Handle character creation
  const handleCreateCharacter = useCallback(async (characterName) => {
    if (activeBookId && characterName) {
      console.log('[DEBUG BrainstormContainer] Creating new character:', characterName);
      try {
        // Generate a unique ID for each trait
        const defaultTraits = [
          { trait_id: `trait_${Date.now()}_1`, trait: 'Determined' },
          { trait_id: `trait_${Date.now()}_2`, trait: 'Loyal' }
        ];

        // Create the character with proper trait IDs
        const result = await dispatch(createCharacter({
          bookId: activeBookId,
          characterData: {
            name: characterName,
            description: '',
            role: '',
            traits: defaultTraits.map(t => t.trait), // Just send the trait names
            goals: [],
            backstory: '',
            // Include trait_ids for the backend
            trait_ids: defaultTraits.map(t => t.trait_id)
          }
        })).unwrap();
        console.log('[DEBUG BrainstormContainer] Character created successfully:', result);
        return result;
      } catch (error) {
        console.error('[DEBUG BrainstormContainer] Failed to create character:', error);
        return null;
      }
    }
    return null;
  }, [dispatch, activeBookId]);

  // Handle location creation
  const handleCreateLocation = useCallback(async (locationData) => {
    if (activeBookId) {
      // Handle both string input (simple mode) and object input (detailed mode)
      const isSimpleMode = typeof locationData === 'string';
      const locationName = isSimpleMode ? locationData : locationData.name;

      if (!locationName) {
        console.error('[DEBUG BrainstormContainer] Location name is required');
        throw new Error('Location name is required');
      }

      console.log('[DEBUG BrainstormContainer] Creating new location:',
        isSimpleMode ? locationName : locationData);

      try {
        // For simple mode, just create a basic location
        if (isSimpleMode) {
          console.log('[DEBUG BrainstormContainer] Creating simple element with data:', {
            bookId: activeBookId,
            categoryId: 'cat_physical_locations',
            elementData: {
              name: locationName,
              description: '',
              attributes: {}
            }
          });

          const result = await dispatch(createWorldElement({
            bookId: activeBookId,
            categoryId: 'cat_physical_locations',
            elementData: {
              name: locationName,
              description: '',
              attributes: {}
            }
          })).unwrap();
          console.log('[DEBUG BrainstormContainer] Location created successfully (simple mode):', result);
          return result;
        }
        // For detailed mode, use the provided data
        else {
          console.log('[DEBUG BrainstormContainer] Creating detailed element with data:', {
            bookId: activeBookId,
            categoryId: locationData.category_id,
            parentId: locationData.parent_id || null,
            elementData: {
              name: locationData.name,
              description: locationData.description || '',
              has_children: locationData.has_children || false
            }
          });

          // Make sure we have a valid category ID
          if (!locationData.category_id) {
            throw new Error('Category is required');
          }

          const result = await dispatch(createWorldElement({
            bookId: activeBookId,
            categoryId: locationData.category_id,
            parentId: locationData.parent_id || null,
            elementData: {
              name: locationData.name,
              description: locationData.description || '',
              has_children: locationData.has_children || false,
              attributes: {}
            }
          })).unwrap();
          console.log('[DEBUG BrainstormContainer] Location created successfully (detailed mode):', result);
          return result;
        }
      } catch (error) {
        console.error('[DEBUG BrainstormContainer] Failed to create location:', error);
        throw error; // Re-throw the error so it can be caught by the caller
      }
    }
    throw new Error('No active book selected');
  }, [dispatch, activeBookId]);

  // Handle card creation
  const handleCreateCard = useCallback(async (cardData) => {
    if (activeBookId) {
      // Check for new characters and create them
      const existingCharacters = characters.map(char => char.name);
      const newCharacters = cardData.characters.filter(name => !existingCharacters.includes(name));

      // Create any new characters
      for (const characterName of newCharacters) {
        await handleCreateCharacter(characterName);
      }

      // Check for new locations and create them - include all world elements
      const existingLocations = worldElements
        .map(loc => loc.name);
      const newLocations = cardData.locations.filter(name => !existingLocations.includes(name));

      // Create any new locations
      for (const locationName of newLocations) {
        await handleCreateLocation(locationName);
      }

      // Create the brainstorm card
      dispatch(createBrainstormCard({
        bookId: activeBookId,
        cardData: {
          ...cardData,
          page: currentPage
        }
      }));
    }
  }, [dispatch, activeBookId, currentPage, characters, worldElements, handleCreateCharacter, handleCreateLocation]);

  // Handle card update
  const handleUpdateCard = useCallback(async (cardId, cardData) => {
    if (activeBookId) {
      console.log('[DEBUG BrainstormContainer] Updating card with ID:', cardId, 'Data:', cardData);

      // Check for new characters and create them
      const existingCharacters = characters.map(char => char.name);
      const newCharacters = cardData.characters.filter(name => !existingCharacters.includes(name));

      // Create any new characters
      for (const characterName of newCharacters) {
        await handleCreateCharacter(characterName);
      }

      // Check for new locations and create them - include all world elements
      const existingLocations = worldElements
        .map(loc => loc.name);
      const newLocations = cardData.locations.filter(name => !existingLocations.includes(name));

      // Create any new locations
      for (const locationName of newLocations) {
        await handleCreateLocation(locationName);
      }

      // Ensure the card has a page property
      const enhancedCardData = {
        ...cardData,
        page: cardData.page || currentPage
      };

      dispatch(updateExistingBrainstormCard({
        bookId: activeBookId,
        cardId,
        cardData: enhancedCardData
      }));
    }
  }, [dispatch, activeBookId, currentPage, characters, worldElements, handleCreateCharacter, handleCreateLocation]);

  // Track nodes that are being deleted to prevent position updates
  const [nodesBeingDeleted, setNodesBeingDeleted] = useState([]);

  // Handle card deletion
  const handleDeleteCard = useCallback((cardId) => {
    if (activeBookId) {
      // Add the node to the list of nodes being deleted
      setNodesBeingDeleted(prev => [...prev, cardId]);

      // Remove the node from the list after a delay to ensure no position updates are processed
      setTimeout(() => {
        setNodesBeingDeleted(prev => prev.filter(id => id !== cardId));
      }, 2000); // 2 second delay

      dispatch(removeBrainstormCard({
        bookId: activeBookId,
        cardId
      }));
    }
  }, [dispatch, activeBookId]);

  // Handle node position update with debounce
  const debouncedPositionUpdates = useRef({});

  const handleNodePositionChange = useCallback((nodeId, position) => {
    console.debug('[DEBUG BrainstormContainer] Node position change:', nodeId, position);

    // Check if the node is being deleted
    if (nodesBeingDeleted.includes(nodeId)) {
      console.debug('[DEBUG BrainstormContainer] Ignoring position update for node being deleted:', nodeId);
      return;
    }

    // Validate position data
    if (!position || typeof position !== 'object') {
      console.error('[DEBUG BrainstormContainer] Position is not an object:', position);
      return;
    }

    if (typeof position.x !== 'number' || typeof position.y !== 'number' ||
        isNaN(position.x) || isNaN(position.y)) {
      console.error('[DEBUG BrainstormContainer] Invalid position data:', position);
      return;
    }

    // Ensure position values are within reasonable bounds
    const validPosition = {
      x: Math.max(-5000, Math.min(5000, position.x)),
      y: Math.max(-5000, Math.min(5000, position.y))
    };

    if (validPosition.x !== position.x || validPosition.y !== position.y) {
      console.warn('[DEBUG BrainstormContainer] Position values were clamped:', position, '->', validPosition);
    }

    // Round the position values for better precision
    const roundedPosition = {
      x: Math.round(validPosition.x),
      y: Math.round(validPosition.y)
    };

    // First update the node position in the Redux store immediately
    // This ensures the UI updates immediately without waiting for the database
    dispatch(updateNodePosition({
      id: nodeId,
      position: roundedPosition
    }));

    // Clear any existing timeout for this node
    if (debouncedPositionUpdates.current[nodeId]) {
      clearTimeout(debouncedPositionUpdates.current[nodeId]);
    }

    // Set a new timeout to update the database after a short delay
    debouncedPositionUpdates.current[nodeId] = setTimeout(() => {
      // Check again if the node still exists and is not being deleted
      if (activeBookId && !nodeId.toString().startsWith('temp-') && !nodesBeingDeleted.includes(nodeId)) {
        // Find the card in the Redux store
        const card = cards.find(c => c.id === nodeId);

        if (card) {
          console.log('[DEBUG BrainstormContainer] Found card for node:', card);

          // Create a copy of the card with the updated position
          const updatedCard = {
            ...card,
            position: roundedPosition
          };

          // Update the card in the database
          dispatch(updateExistingBrainstormCard({
            bookId: activeBookId,
            cardId: nodeId,
            cardData: updatedCard
          }))
          .then(resultAction => {
            if (updateExistingBrainstormCard.fulfilled.match(resultAction)) {
              console.log('[DEBUG BrainstormContainer] Successfully updated card position in database');
            } else if (updateExistingBrainstormCard.rejected.match(resultAction)) {
              console.error('[DEBUG BrainstormContainer] Failed to update card position in database:', resultAction.error);
            }
          });
        } else {
          console.warn('[DEBUG BrainstormContainer] No card found for node:', nodeId);

          // Create a placeholder card if none exists
          const placeholderCard = {
            id: nodeId,
            title: 'Placeholder Card',
            content: '',
            type: 'event',
            characters: [],
            locations: [],
            page: currentPage,
            position: roundedPosition
          };

          console.log('[DEBUG BrainstormContainer] Creating placeholder card:', placeholderCard);

          // Create the card in the database
          dispatch(createBrainstormCard({
            bookId: activeBookId,
            cardData: placeholderCard
          }))
          .then(resultAction => {
            if (createBrainstormCard.fulfilled.match(resultAction)) {
              console.log('[DEBUG BrainstormContainer] Successfully created placeholder card in database');
            } else if (createBrainstormCard.rejected.match(resultAction)) {
              console.error('[DEBUG BrainstormContainer] Failed to create placeholder card in database:', resultAction.error);
            }
          });
        }
      } else {
        console.log('[DEBUG BrainstormContainer] Skipping database update for temporary node or missing book ID');
      }
    }, 300); // 300ms debounce
  }, [dispatch, activeBookId, cards, currentPage, nodesBeingDeleted]);

  // Handle AI idea generation
  const handleGenerateIdea = useCallback(() => {
    if (activeBookId) {
      console.log('[DEBUG BrainstormContainer] Generating AI ideas for book:', activeBookId);
      console.log('[DEBUG BrainstormContainer] Current page:', currentPage);

      // First, make sure we have the latest world data
      dispatch(setCurrentBookId(activeBookId));
      dispatch(fetchWorld({ bookId: activeBookId }))
        .then(() => {
          console.log('[DEBUG BrainstormContainer] World data fetched, now generating AI ideas');

          // Then generate AI ideas
          return dispatch(generateAIIdea({
            bookId: activeBookId,
            page: currentPage
          }));
        })
        .then(resultAction => {
          if (generateAIIdea.fulfilled.match(resultAction)) {
            console.log('[DEBUG BrainstormContainer] Successfully generated AI ideas:', resultAction.payload);
          } else if (generateAIIdea.rejected.match(resultAction)) {
            console.error('[DEBUG BrainstormContainer] Failed to generate AI ideas:', resultAction.error);
          }
        })
        .catch(error => {
          console.error('[DEBUG BrainstormContainer] Error in AI idea generation process:', error);
        });
    } else {
      console.error('[DEBUG BrainstormContainer] No active book ID found');
    }
  }, [dispatch, activeBookId, currentPage]);

  // Handle clear error
  const handleClearError = useCallback(() => {
    dispatch(clearError());
  }, [dispatch]);

  // Handle connecting ideas
  const handleConnectIdeas = useCallback((selectedNodes, currentPage) => {
    if (activeBookId) {
      console.log('[DEBUG BrainstormContainer] Connecting ideas for book:', activeBookId);
      console.log('[DEBUG BrainstormContainer] Selected nodes:', selectedNodes);
      console.log('[DEBUG BrainstormContainer] Current page:', currentPage);

      if (selectedNodes.length < 2 || selectedNodes.length > 5) {
        alert('Please select between 2 and 5 nodes to connect');
        return;
      }

      // First, make sure we have the latest world data
      dispatch(setCurrentBookId(activeBookId));
      dispatch(fetchWorld({ bookId: activeBookId }))
        .then(() => {
          console.log('[DEBUG BrainstormContainer] World data fetched, now connecting ideas');

          // Then connect the ideas
          return dispatch(connectSelectedIdeas({
            bookId: activeBookId,
            selectedNodes,
            page: currentPage
          }));
        })
        .then(resultAction => {
          if (resultAction.meta.requestStatus === 'fulfilled') {
            console.log('[DEBUG BrainstormContainer] Successfully connected ideas:', resultAction.payload);
            // Success message is shown by the reducer
          } else {
            console.error('[DEBUG BrainstormContainer] Failed to connect ideas:', resultAction.error);
            alert(`Failed to connect ideas: ${resultAction.error?.message || 'Unknown error'}`);
          }
        })
        .catch(error => {
          console.error('[DEBUG BrainstormContainer] Error in connecting ideas process:', error);
          alert(`Error connecting ideas: ${error.message || 'Unknown error'}`);
        });
    } else {
      console.error('[DEBUG BrainstormContainer] No active book ID found');
    }
  }, [dispatch, activeBookId]);

  // Handle sending card to plot
  const handleSendToPlot = useCallback((cardId, isBatch = false) => {
    console.log('[DEBUG] handleSendToPlot called with:', { cardId, isBatch });
    console.log('[DEBUG] Current state:', { activeBookId, cards });

    if (!activeBookId) {
      console.error('[DEBUG] No active book ID found');
      alert('Error: No active book selected');
      return;
    }

    if (!cardId) {
      console.error('[DEBUG] No card ID provided');
      alert('Error: No card selected');
      return;
    }

    const card = cards.find(c => c.id === cardId);
    console.log('[DEBUG] Found card:', card);

    if (!card) {
      console.error(`[DEBUG] Card with ID ${cardId} not found`);
      alert(`Error: Card with ID ${cardId} not found`);
      return;
    }

    // Format the event data to match the backend API requirements
    // Include title, description, and metadata
    const eventData = {
      title: card.title || 'Untitled from Brainstorm',
      description: card.content || '',
      // Include additional metadata
      characters: card.characters || [],
      locations: card.locations || [],
      event_type: card.type || 'idea'
    };

    console.log('[DEBUG] Card metadata:', {
      characters: card.characters,
      locations: card.locations,
      type: card.type
    });

    // Ensure we're not sending any invalid values
    if (eventData.title === '') {
      eventData.title = 'Untitled from Brainstorm';
    }

    if (eventData.description === undefined) {
      eventData.description = '';
    }

    console.log('[DEBUG] Sending event data to plot:', eventData);

    // Use the new sendNodeToPLot action to send the node to plot
    dispatch(sendNodeToPLot({
      bookId: activeBookId,
      nodeId: cardId,
      eventData: eventData
    }))
    .then(resultAction => {
      console.log('[DEBUG] sendNodeToPLot result:', resultAction);

      if (resultAction.type.endsWith('/fulfilled')) {
        console.log('[DEBUG] Successfully sent node to plot:', resultAction.payload);
        // Show success message (only for single cards, not when sending multiple)
        if (!isBatch) {
          alert(`Card "${card.title || 'Untitled'}" sent to Plot Bank`);
        }
      } else if (resultAction.type.endsWith('/rejected')) {
        console.error('[DEBUG] Failed to send node to plot:', resultAction.error);
        alert(`Error sending card to Plot: ${resultAction.error.message || 'Unknown error'}`);
      }
    })
    .catch(error => {
      console.error('[DEBUG] Exception in sendNodeToPLot:', error);
      alert(`Exception sending card to Plot: ${error.message || 'Unknown error'}`);
    });
  }, [dispatch, activeBookId, cards]);

  // New function to handle sending multiple nodes to plot
  const handleSendMultipleToPlot = useCallback((nodeIds) => {
    console.log('[DEBUG] handleSendMultipleToPlot called with:', nodeIds);

    if (!Array.isArray(nodeIds) || nodeIds.length === 0) {
      console.error('[DEBUG] No node IDs provided or invalid format');
      return;
    }

    // Process each node sequentially to ensure proper state updates
    const sendNodesSequentially = async () => {
      const results = [];

      for (const nodeId of nodeIds) {
        try {
          // Use the existing handleSendToPlot function with the batch flag
          await handleSendToPlot(nodeId, true);
          results.push({ nodeId, success: true });
        } catch (error) {
          console.error(`[DEBUG] Error sending node ${nodeId} to plot:`, error);
          results.push({ nodeId, success: false, error });
        }
      }

      return results;
    };

    // Execute the sequential sending
    sendNodesSequentially()
      .then(results => {
        console.log('[DEBUG] All nodes processed:', results);
        const successCount = results.filter(r => r.success).length;
        alert(`${successCount} of ${nodeIds.length} cards sent to Plot Bank`);
      })
      .catch(error => {
        console.error('[DEBUG] Error in sendNodesSequentially:', error);
        alert(`Error sending cards to Plot: ${error.message || 'Unknown error'}`);
      });
  }, [handleSendToPlot]);

  return (
    <BrainstormPageReduxPresentation
      onPageChange={handlePageChange}
      onCardSelect={handleCardSelect}
      onCreateCard={handleCreateCard}
      onUpdateCard={handleUpdateCard}
      onDeleteCard={handleDeleteCard}
      onNodePositionChange={handleNodePositionChange}
      onGenerateIdea={handleGenerateIdea}
      onConnectIdeas={handleConnectIdeas}
      onClearError={handleClearError}
      onSendToPlot={handleSendToPlot}
      onSendMultipleToPlot={handleSendMultipleToPlot}
      currentPage={currentPage}
      pages={pages}
      cards={cards}
      nodes={nodes}
      edges={edges}
      selectedCard={selectedCard}
      isLoading={isLoading}
      error={error}
      characters={characters}
      worldElements={worldElements}
      categories={categories}
      onCreateCharacter={handleCreateCharacter}
      onCreateLocation={handleCreateLocation}
    />
  );
};

// Wrap the component with React.memo to prevent unnecessary re-renders
export default React.memo(BrainstormPageReduxContainer);

// frontend/src/containers/EventSidebarContainer.js
import React, { useCallback } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import EventSidebar from '../components/EventSidebar';
import { 
  selectCurrentEvent, 
  selectEventsByChapter,
  selectEvent 
} from '../redux/slices/plotSlice';

/**
 * Container component for EventSidebar that connects to Redux
 * @param {Object} props - Component props
 * @param {Object} props.currentChapter - Current chapter being edited
 * @returns {JSX.Element} EventSidebar container
 */
const EventSidebarContainer = ({ currentChapter }) => {
  const dispatch = useDispatch();
  
  // Select state from Redux
  const selectedEvent = useSelector(selectCurrentEvent);
  const events = useSelector(state => 
    currentChapter ? selectEventsByChapter(state, currentChapter.id) : []
  );
  
  // Handle event selection
  const handleEventSelect = useCallback((event) => {
    if (event && event.id) {
      dispatch(selectEvent(event.id));
    }
  }, [dispatch]);
  
  return (
    <EventSidebar
      events={events}
      selectedEvent={selectedEvent}
      setSelectedEvent={handleEventSelect}
    />
  );
};

export default EventSidebarContainer;

// PROPOSED DATA FLOW

// 1. Book Selection (TitleBar.js)
// - User selects book from dropdown
// - <PERSON><PERSON><PERSON> loads basic book info
// - No automatic loading of all book data

// 2. Plot Management (PlotPage.js)
// - Load chapters and events on demand when PlotPage is opened
// - Chapters are created/edited directly in PlotPage
// - Events can be moved between chapters or bank
// - No polling/interval needed

// 3. Brainstorm (BrainstormPage.js)
// - Load brainstorm data on demand when page is opened
// - Create event cards that can be exported to plot
// - Character/location tagging with local state

// 4. Character Management (CharacterPage.js)
// - Load characters on demand when page is opened
// - Create/edit characters with immediate backend sync
// - No automatic syncing with other components

// 5. AI Integration
// - Generate memory blob on-demand before AI calls
// - No need to keep memory in sync constantly
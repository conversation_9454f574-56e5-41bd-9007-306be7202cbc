// frontend/src/index.js
import React from 'react';
import { createRoot } from 'react-dom/client';
import { Provider } from 'react-redux';
import { <PERSON>rowserRouter as Router } from 'react-router-dom';
import * as Sentry from '@sentry/react';
import ErrorBoundary from './components/ErrorBoundary'; // Import ErrorBoundary
import store from './redux/store';
import App from './App';
import './index.css';

// Suppress ResizeObserver loop limit exceeded error
// This error is caused by ReactFlow and is harmless
const originalConsoleError = console.error;
console.error = (...args) => {
  if (args[0] && typeof args[0] === 'string' &&
      (args[0].includes('ResizeObserver loop') ||
       args[0].includes('ResizeObserver loop completed with undelivered notifications'))) {
    // Ignore ResizeObserver errors
    return;
  }
  originalConsoleError(...args);
};

/**
 * Initializes Sentry for error tracking
 */
Sentry.init({
  dsn: process.env.REACT_APP_SENTRY_DSN,
  integrations: [Sentry.browserTracingIntegration()],
  tracesSampleRate: 1.0,
  environment: process.env.NODE_ENV,
});

// Get the root element
const container = document.getElementById('root');
const root = createRoot(container);

/**
 * Renders the app with Redux Provider and ErrorBoundary
 */
root.render(
  <React.StrictMode>
    <Provider store={store}>
      <Router>
        <App />
      </Router>
    </Provider>
  </React.StrictMode>
);

// Debugging log for app initialization
console.debug('App initialized and rendered with Redux Provider and ErrorBoundary');
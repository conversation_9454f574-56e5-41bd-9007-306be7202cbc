/* frontend/src/styles/themes.css */

/* Light Theme Variables */
.theme-light {
  /* Background Colors */
  --background-primary: #FFFFFF;
  --background-secondary: #F8F9FA;
  --background-tertiary: #F0F0F0;

  /* Text Colors */
  --text-primary: #333333;
  --text-secondary: #6C757D;
  --text-tertiary: #909090;

  /* UI Colors */
  --primary: #0D6EFD;
  --secondary: #6C757D;
  --accent: #28A745;
  --highlight: #FFC107;
  --error: #DC3545;

  /* Card and Border Colors */
  --card-background: #F8F9FA;
  --border-color: #DEE2E6;

  /* Specific Component Colors */
  --header-background: linear-gradient(135deg, #FFFFFF, #F0F0F0);
  --sidebar-background: #F5F5F5;
  --button-primary-background: #0D6EFD;
  --button-primary-hover: #0B5ED7;
  --button-secondary-background: #6C757D;
  --button-secondary-hover: #5A6268;
  --button-success-background: #28A745;
  --button-success-hover: #218838;
  --button-danger-background: #DC3545;
  --button-danger-hover: #C82333;

  /* Shadows */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
}

/* Dark Theme Variables */
.theme-dark {
  /* Background Colors */
  --background-primary: #121212;
  --background-secondary: #1E1E1E;
  --background-tertiary: #2D2D2D;

  /* Text Colors */
  --text-primary: #E0E0E0;
  --text-secondary: #A0A0A0;
  --text-tertiary: #808080;

  /* UI Colors */
  --primary: #BB86FC;
  --secondary: #03DAC6;
  --accent: #3700B3;
  --highlight: #FFB300;
  --error: #CF6679;

  /* Card and Border Colors */
  --card-background: #1E1E1E;
  --border-color: #333333;

  /* Specific Component Colors */
  --header-background: linear-gradient(135deg, #1A1A1A, #121212);
  --sidebar-background: #1A1A1A;
  --button-primary-background: #BB86FC;
  --button-primary-hover: #A66EFC;
  --button-secondary-background: #03DAC6;
  --button-secondary-hover: #00C4B4;
  --button-success-background: #3700B3;
  --button-success-hover: #3000A0;
  --button-danger-background: #CF6679;
  --button-danger-hover: #B55A6A;

  /* Title Bar Specific */
  --title-bar-background: #1A1A1A;
  --title-bar-border: #333333;
  --title-bar-text: #E0E0E0;
  --book-info-background: #1A1A1A; /* Match title bar background */
  --book-info-hover: #333333;
  --book-title-color: #E0E0E0;
  --change-book-color: #BB86FC;

  /* Write Page Specific */
  --write-page-header-background: #1A1A1A;
  --write-page-header-border: #333333;
  --editor-background: #1E1E1E;
  --editor-border: #333333;
  --editor-toolbar-background: #252525;
  --editor-toolbar-border: #333333;
  --editor-toolbar-button: #2D2D2D;
  --editor-toolbar-button-hover: #3D3D3D;
  --editor-toolbar-button-active: #4D4D4D;
  --chapter-sidebar-background: #1A1A1A;
  --chapter-sidebar-border: #333333;
  --chapter-item-background: #252525;
  --chapter-item-hover: #2D2D2D;
  --chapter-item-selected: #BB86FC; /* Match button color */
  --event-sidebar-background: #1A1A1A;
  --event-sidebar-border: #333333;
  --event-card-background: #252525;
  --event-card-border: #333333;
  --event-card-hover: #2D2D2D;
  --event-card-selected: #3700B3;

  /* Shadows */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.2);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.3);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.4);
}

/* Sepia Theme Variables */
.theme-sepia {
  /* Background Colors */
  --background-primary: #F5EFE0;
  --background-secondary: #F8F4E9;
  --background-tertiary: #EFE6D5;

  /* Text Colors */
  --text-primary: #5B4636;
  --text-secondary: #7D6E5B;
  --text-tertiary: #9C8C7A;

  /* UI Colors */
  --primary: #8B6E4E;
  --secondary: #A98467;
  --accent: #6B8E23;
  --highlight: #D4AC0D;
  --error: #A52A2A;

  /* Card and Border Colors */
  --card-background: #F8F4E9;
  --border-color: #D3C6A6;

  /* Specific Component Colors */
  --header-background: linear-gradient(135deg, #F5EFE0, #EFE6D5);
  --sidebar-background: #EFE6D5;
  --button-primary-background: #8B6E4E;
  --button-primary-hover: #7A5F41;
  --button-secondary-background: #A98467;
  --button-secondary-hover: #97755A;
  --button-success-background: #6B8E23;
  --button-success-hover: #5A7A1E;
  --button-danger-background: #A52A2A;
  --button-danger-hover: #8E2323;

  /* Shadows */
  --shadow-sm: 0 1px 2px rgba(91, 70, 54, 0.1);
  --shadow-md: 0 4px 6px rgba(91, 70, 54, 0.15);
  --shadow-lg: 0 10px 15px rgba(91, 70, 54, 0.2);
}


// frontend/src/services/apiService.js
/**
 * This file contains all API service functions for the application.
 *
 * IMPORTANT: Most of these functions are used by BookContext.js and are not
 * directly imported by components. This is why they appear as "unused exports"
 * in the IDE, but they are actually used through the BookContext.
 */

import { BASE_URL } from '../utils/apiConfig';

/**
 * Authenticates a user and returns a JWT token
 * @param {string} email - User email
 * @param {string} password - User password
 * @returns {Promise<Object>} Authentication response with access_token
 */
export const loginUser = async (email, password) => {
  const response = await fetch(`${BASE_URL}/auth/token`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ user_id: '', email, password }),
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.detail || 'Lo<PERSON> failed');
  }

  const data = await response.json();
  if (!data.access_token) {
    throw new Error('No access token received');
  }

  return data;
};

/**
 * Generates headers with the auth token
 * @returns {Object} Headers object
 */
export const getHeaders = () => {
  const token = localStorage.getItem('token');

  if (token) {
    console.log('Token found in localStorage:', token.substring(0, 10) + '...');
  } else {
    console.warn('No token found in localStorage');
  }

  return {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  };
};

/**
 * Makes an API request with standardized error handling and logging
 * @param {string} url - The API endpoint URL
 * @param {Object} options - Fetch options (method, headers, body)
 * @param {Object} config - Additional configuration
 * @param {boolean} config.retry - Whether to retry failed requests
 * @param {number} config.retryCount - Number of retries (default: 2)
 * @param {number} config.retryDelay - Delay between retries in ms (default: 1000)
 * @param {boolean} config.parseJson - Whether to parse response as JSON (default: true)
 * @returns {Promise<any>} Response data
 * @throws {Error} If request fails
 */
export const apiRequest = async (url, options = {}, config = {}) => {
  const {
    retry = false,
    retryCount = 2,
    retryDelay = 1000,
    parseJson = true,
    logResponse = true
  } = config;

  console.log(`API Request to ${url}`);

  // Ensure headers are set
  options.headers = options.headers || getHeaders();
  console.log('Request headers:', options.headers);

  // Add request ID for tracking
  const requestId = `req_${Date.now()}_${Math.random().toString(36).substring(2, 7)}`;

  // Log request details
  console.debug(`[${requestId}] API Request:`, {
    url,
    method: options.method || 'GET',
    bodySize: options.body ? options.body.length : 0
  });

  let attempts = 0;
  let lastError = null;

  while (attempts <= (retry ? retryCount : 0)) {
    try {
      if (attempts > 0) {
        console.debug(`[${requestId}] Retry attempt ${attempts}/${retryCount}`);
        // Wait before retrying
        await new Promise(resolve => setTimeout(resolve, retryDelay));
      }

      const response = await fetch(url, options);

      // Handle non-OK responses
      console.log(`API response status for ${url}:`, response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`[${requestId}] API request failed: ${response.status} - ${errorText}`);
        console.error(`Failed URL: ${url}`);
        console.error(`Request options:`, options);

        // Special handling for 401 Unauthorized
        if (response.status === 401) {
          console.error('Authentication error: Token may be invalid or expired');
          throw new Error('Unauthorized');
        }

        // Try to parse error as JSON
        try {
          const errorData = JSON.parse(errorText);
          throw new Error(errorData.detail || `API error: ${response.status}`);
        } catch (e) {
          // If parsing fails, use the raw error text
          throw new Error(`API error: ${response.status} - ${errorText}`);
        }
      }

      // Parse response
      let data;
      if (parseJson) {
        data = await response.json();
        if (logResponse) {
          console.debug(`[${requestId}] API response:`, data);
        }
      } else {
        data = await response.text();
        if (logResponse) {
          console.debug(`[${requestId}] API response: [Text data of length ${data.length}]`);
        }
      }

      return data;
    } catch (error) {
      console.error(`[${requestId}] Request error:`, error);
      lastError = error;
      attempts++;

      // If we've exhausted retries or not using retry, throw the error
      if (!retry || attempts > retryCount) {
        throw lastError;
      }
    }
  }

  // This should never be reached due to the throw in the loop
  throw lastError || new Error('Unknown error in API request');
};

/**
 * Handles API response, throwing errors for non-OK status (legacy method)
 * @param {Response} response - Fetch response object
 * @returns {Promise<any>} Parsed JSON data
 * @throws {Error} If response is not OK
 * @deprecated Use apiRequest instead
 */
export const handleResponse = async (response) => {
  if (!response.ok) {
    const errorText = await response.text();
    console.error(`API request failed: ${response.status} - ${errorText}`);
    if (response.status === 401) throw new Error('Unauthorized');
    throw new Error(`API error: ${response.status} - ${errorText}`);
  }
  const data = await response.json();
  console.debug('API response:', data);
  return data;
};

/**
 * Fetches all books for the authenticated user
 * @returns {Promise<Array>} List of books
 */
export const fetchBooks = async () => {
  return apiRequest(`${BASE_URL}/books`);
};

/**
 * Creates a new book
 * @param {Object} bookData - Book data (e.g., { title: string })
 * @returns {Promise<Object>} Created book data
 */
export const createBook = async (bookData) => {
  return apiRequest(`${BASE_URL}/books`, {
    method: 'POST',
    body: JSON.stringify(bookData)
  });
};

/**
 * Fetches plot chapters for a specific book
 * @param {string} bookId - Book identifier
 * @returns {Promise<Object>} Plot data with chapters
 */
export const fetchChapters = async (bookId) => {
  console.log(`[DEBUG apiService] Fetching chapters for book ${bookId}`);

  try {
    // First try the dedicated chapters endpoint
    const response = await apiRequest(`${BASE_URL}/books/${bookId}/chapters`);
    console.log('[DEBUG apiService] Chapters response:', response);

    if (Array.isArray(response)) {
      console.log('[DEBUG apiService] Using chapters endpoint data');
      return response;
    }

    // If that doesn't work, try the plot endpoint as fallback
    const plotResponse = await apiRequest(`${BASE_URL}/books/${bookId}/plot`);
    console.log('[DEBUG apiService] Plot response:', plotResponse);

    // Extract chapters from the nested response
    if (plotResponse && plotResponse.plot && Array.isArray(plotResponse.plot.chapters)) {
      console.log('[DEBUG apiService] Extracted chapters from plot:', plotResponse.plot.chapters);
      return plotResponse.plot.chapters;
    }

    console.warn('[DEBUG apiService] No chapters found in any response, returning empty array');
    return [];
  } catch (error) {
    console.error('[DEBUG apiService] Error fetching chapters:', error);
    return [];
  }
};

/**
 * Saves updated chapters for a book
 * @param {string} bookId - Book identifier
 * @param {Array} chapters - Updated chapters array
 * @returns {Promise<Object>} Saved plot data
 */
export const saveChapters = async (bookId, chapters) => {
  console.debug('saveChapters called with:', { bookId, chaptersCount: chapters.length });

  try {
    // First fetch existing plot data to avoid overwriting other data
    const existingPlotData = await fetchPlot(bookId);
    console.debug('Existing plot data:', existingPlotData);

    // Create properly formatted plot data for backend
    const formattedPlot = {
      bank: existingPlotData.plot?.bank || []
    };

    // Format chapters correctly for backend
    formattedPlot.chapters = chapters.map(chapter => ({
      id: chapter.id,
      title: chapter.title,
      events: chapter.events || []
    }));

    console.debug('Sending formatted plot to backend:', formattedPlot);

    // Save the properly formatted plot data
    const response = await savePlot(bookId, formattedPlot);
    console.debug('Backend response:', response);
    return response;
  } catch (error) {
    console.error('Error saving chapters:', error);
    throw error;
  }
};

/**
 * Fetches characters for a book
 * @param {string} bookId - Book identifier
 * @returns {Promise<Object>} Characters data
 */
export const fetchCharacters = async (bookId) => {
  try {
    const response = await apiRequest(`${BASE_URL}/books/${bookId}/characters`, {}, {
      retry: true,
      retryCount: 1, // Reduce retry count to avoid infinite loops
      logResponse: false // Characters data can be large
    });

    // The API returns an object with a 'characters' property that contains an object
    // where keys are character names and values are character objects
    if (response && response.characters) {
      // Convert the object of characters to an array
      const charactersArray = Object.entries(response.characters).map(([name, data]) => {
        // Safely access properties with fallbacks
        return {
          id: data.character_id,
          name: name,
          description: data.description || '',
          age: data.age || '',
          role: data.role || '',
          race: data.race || '',
          headshot: data.headshot || '',
          traits: data.traits || [],
          relationships: Array.isArray(data.relationships) ? data.relationships.map(rel => ({
            ...rel,
            strength: rel.strength !== undefined ? rel.strength : 3 // Default to 3 if not specified
          })) : [],
          backstory: data.backstory || '',
          arc: data.arc || '',
          // Safely handle the new fields
          gender_identity: (data.gender_identity !== undefined) ? data.gender_identity : '',
          sexual_orientation: (data.sexual_orientation !== undefined) ? data.sexual_orientation : ''
        };
      });
      return charactersArray;
    } else if (Array.isArray(response)) {
      // If response is already an array, return it directly
      return response;
    } else {
      console.warn('fetchCharacters: Unexpected response format', response);
      return [];
    }
  } catch (error) {
    console.error('Error fetching characters:', error);
    // Return an empty array instead of throwing to prevent UI from getting stuck
    return [];
  }
};

/**
 * Fetches all characters for a book (alias for fetchCharacters)
 * @param {string} bookId - Book identifier
 * @returns {Promise<Array>} Array of character objects
 */
export const fetchAllCharacters = async (bookId) => {
  console.debug(`[DEBUG API] Fetching all characters for book ${bookId}`);
  try {
    const characters = await fetchCharacters(bookId);
    console.debug(`[DEBUG API] Successfully fetched ${characters.length} characters`);
    return characters;
  } catch (error) {
    console.error('[DEBUG API] Error fetching all characters:', error);
    // Return empty array instead of throwing to prevent UI from getting stuck
    return [];
  }
};

/**
 * Adds a character to a book
 * @param {string} bookId - Book identifier
 * @param {Object} character - Character data
 * @returns {Promise<Object>} Updated memory
 */
export const addCharacter = async (bookId, characterData) => {
  try {
    // Process traits to ensure they have IDs
    let processedData = { ...characterData };

    if (processedData.traits && Array.isArray(processedData.traits)) {
      // If trait_ids are not provided, generate them
      if (!processedData.trait_ids || !Array.isArray(processedData.trait_ids) || processedData.trait_ids.length !== processedData.traits.length) {
        console.log('[DEBUG apiService] Generating trait IDs for character traits');
        processedData.trait_ids = processedData.traits.map((_, index) => `trait_${Date.now()}_${index}`);
      }

      // Create a traits array with proper structure for the backend
      const formattedTraits = processedData.traits.map((trait, index) => ({
        trait_id: processedData.trait_ids[index],
        trait: trait
      }));

      console.log('[DEBUG apiService] Formatted traits with IDs:', formattedTraits);

      // Update the character data with the formatted traits
      processedData = {
        ...processedData,
        formatted_traits: formattedTraits
      };
    }

    // Ensure gender_identity, sexual_orientation, and relationship strength fields are included
    processedData = {
      ...processedData,
      gender_identity: processedData.gender_identity || '',
      sexual_orientation: processedData.sexual_orientation || '',
      relationships: Array.isArray(processedData.relationships)
        ? processedData.relationships.map(rel => ({
            ...rel,
            strength: rel.strength !== undefined ? rel.strength : 3 // Default to 3 if not specified
          }))
        : []
    };

    console.log('Final processed data:', processedData);

    console.log('[DEBUG apiService] Sending character data to backend:', processedData);

    const response = await fetch(`${BASE_URL}/books/${bookId}/characters`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      },
      body: JSON.stringify(processedData)
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Error creating character: ${response.status} ${response.statusText}`);
      console.error('Response body:', errorText);
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const responseData = await response.json();
    console.log('Character creation response:', responseData);

    // Return the response data, falling back to our sent data for any missing fields
    return {
      ...processedData,
      ...responseData
    };
  } catch (error) {
    console.error('Error creating character:', error);
    throw error; // Let the caller handle the error
  }
};

/**
 * Fetches brainstorm data for a book
 * @param {string} bookId - Book identifier
 * @returns {Promise<Object>} Brainstorm data
 */
export const fetchBrainstorm = async (bookId) => {
  return apiRequest(`${BASE_URL}/books/${bookId}/brainstorm`, {}, {
    retry: true,
    logResponse: false // Brainstorm data can be large
  });
};

/**
 * Saves brainstorm data for a book
 * @param {string} bookId - Book identifier
 * @param {Object} brainstorm - Brainstorm data to save
 * @returns {Promise<Object>} Saved brainstorm data
 */
export const saveBrainstorm = async (bookId, brainstorm) => {
  // DEBUG: Log what's being sent to the backend
  console.debug('API saveBrainstorm called with:', {
    bookId,
    pagesCount: Object.keys(brainstorm.pages || {}).length,
    currentPageId: Object.keys(brainstorm.pages || {})[0] // First page ID
  });

  // DEBUG: Log detailed structure of first page
  const firstPageId = Object.keys(brainstorm.pages || {})[0];
  if (firstPageId) {
    const firstPage = brainstorm.pages[firstPageId];
    console.debug('First page details:', {
      pageId: firstPageId,
      title: firstPage.title,
      nodesCount: firstPage.nodes?.length || 0,
      shapesCount: firstPage.shapes?.length || 0
    });

    // Log sample node if available
    if (firstPage.nodes && firstPage.nodes.length > 0) {
      console.debug('Sample node structure:', firstPage.nodes[0]);
    }
  }

  return apiRequest(`${BASE_URL}/books/${bookId}/brainstorm`, {
    method: 'POST',
    body: JSON.stringify(brainstorm)
  }, {
    retry: true,
    logResponse: false // Brainstorm data can be large
  });
};

/**
 * Sends a brainstorm node to the plot as an event
 * @param {string} bookId - Book identifier
 * @param {string} nodeId - Node identifier
 * @param {Object} eventData - Event data including title, characters, and locations
 * @returns {Promise<Object>} Created plot event
 */
export const sendNodeToPlot = async (bookId, nodeId, eventData) => {
  console.log(`[DEBUG apiService] Sending node ${nodeId} to plot with data:`, eventData);
  return apiRequest(`${BASE_URL}/books/${bookId}/brainstorm/nodes/${nodeId}/send-to-plot`, {
    method: 'POST',
    body: JSON.stringify(eventData)
  });
};

/**
 * Generates an AI response for a book
 * @param {string} bookId - Book identifier
 * @param {string} prompt - AI prompt
 * @param {string} responseType - Type of response to generate
 * @returns {Promise<Object>} AI response
 */
export const generateAIResponse = async (bookId, prompt, responseType = "text") => {
  try {
    // First, ensure AI context is generated from dedicated tables
    await apiRequest(`${BASE_URL}/books/${bookId}/ai/context`, {
      method: 'POST'
    }, {
      retry: true
    });

    // Then make the AI call
    return apiRequest(`${BASE_URL}/books/${bookId}/ai/generate`, {
      method: 'POST',
      body: JSON.stringify({ prompt, response_type: responseType })
    }, {
      retry: true
    });
  } catch (error) {
    console.error('Error generating AI response:', error);
    throw error;
  }
};

/**
 * Generates AI context for a book from dedicated tables
 * @param {string} bookId - Book identifier
 * @returns {Promise<Object>} AI context
 */
export const generateAIContext = async (bookId) => {
  return apiRequest(`${BASE_URL}/books/${bookId}/ai/context`, {
    method: 'POST'
  }, {
    retry: true
  });
};

/**
 * Generates a memory blob for AI context
 * @param {string} bookId - Book identifier
 * @returns {Promise<Object>} Memory blob
 * @deprecated Use generateAIContext which generates context from dedicated tables
 */
export const generateMemoryBlob = async (bookId) => {
  console.warn('generateMemoryBlob is deprecated. Use generateAIContext which generates context from dedicated tables.');
  return generateAIContext(bookId);
};

/**
 * Fetches the list of available AI providers
 * @returns {Promise<Object>} Available AI providers
 */
export const fetchAIProviders = async () => {
  return apiRequest(`${BASE_URL}/user/preferences/providers`);
};

/**
 * Fetches the user's AI provider preferences
 * @returns {Promise<Object>} User preferences
 */
export const fetchUserPreferences = async () => {
  return apiRequest(`${BASE_URL}/user/preferences`);
};

/**
 * Updates the user's AI provider preferences
 * @param {Object} preferences - AI provider preferences
 * @param {string} preferences.text_provider - Text provider ID
 * @param {string} preferences.image_provider - Image provider ID
 * @param {string} preferences.image_style - Image style ID
 * @param {string} preferences.image_model - Image model ID
 * @param {string} preferences.image_quality - Image quality ID
 * @returns {Promise<Object>} Updated preferences
 */
export const updateAIProviderPreferences = async (preferences) => {
  return apiRequest(`${BASE_URL}/user/preferences`, {
    method: 'POST',
    body: JSON.stringify(preferences)
  });
};

/**
 * Fetches world settings for a book
 * @param {string} bookId - Book identifier
 * @returns {Promise<Object>} World data
 */
export const fetchWorld = async (bookId) => {
  return apiRequest(`${BASE_URL}/books/${bookId}/world`);
};

/**
 * Updates world settings for a book
 * @param {string} bookId - Book identifier
 * @param {Object} world - World data
 * @returns {Promise<Object>} Updated world data
 */
export const updateWorld = async (bookId, world) => {
  return apiRequest(`${BASE_URL}/books/${bookId}/world`, {
    method: 'POST',
    body: JSON.stringify(world)
  });
};

/**
 * Fetches plot data for a book
 * @param {string} bookId - Book identifier
 * @returns {Promise<Object>} Plot data
 */
export const fetchPlot = async (bookId) => {
  return apiRequest(`${BASE_URL}/books/${bookId}/plot`, {}, {
    retry: true
  });
};

/**
 * Saves plot data for a book
 * @param {string} bookId - Book identifier
 * @param {Object} plotData - Plot data to save
 * @returns {Promise<Object>} Response from the server
 */
export const savePlot = async (bookId, plotData) => {
  console.log("savePlot called with:", { bookId, plotData });

  // Ensure we're sending the complete plot structure
  const payload = {
    bookId: bookId,
    plotData: {
      bank: plotData.bank || [],
      chapters: Array.isArray(plotData.chapters) ? plotData.chapters : []
    }
  };

  return apiRequest(`${BASE_URL}/books/${bookId}/plot`, {
    method: 'POST',
    body: JSON.stringify(payload)
  }, {
    retry: true
  });
};

/**
 * Saves chapter content to the backend
 * @param {string} chapterId - The ID of the chapter
 * @param {string} content - The Draft.js content in raw JSON string format
 * @deprecated token parameter removed - now retrieved from localStorage
 * @returns {Promise<Object>} Updated chapter data
 */
export const saveChapterContent = async (chapterId, content) => {
  try {
    const response = await fetch(`${BASE_URL}/chapters/${chapterId}/content`, {
      method: 'PUT',
      headers: getHeaders(),
      body: JSON.stringify({ content }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('API error response:', errorText);
      try {
        const errorData = JSON.parse(errorText);
        throw new Error(errorData.detail || `Failed to save chapter content: ${response.status}`);
      } catch (e) {
        throw new Error(`Failed to save chapter content: ${response.status} - ${errorText}`);
      }
    }

    const data = await response.json();
    console.debug('API success response:', data);
    return data;
  } catch (error) {
    console.error('Error in saveChapterContent API call:', error);
    throw error;
  }
};

// The fetchEvents, createChapter, and updateEventChapter functions are already defined below

// Add new API service functions for world elements

/**
 * Fetches world elements for a book
 * @param {string} bookId - Book identifier
 * @returns {Promise<Object>} World elements data
 * @deprecated Use fetchWorld which provides all world data
 */
export const fetchWorldElements = async (bookId) => {
  console.warn('fetchWorldElements is deprecated. Use fetchWorld which provides all world data.');
  return apiRequest(`${BASE_URL}/books/${bookId}/world/elements`, {}, {
    retry: true
  });
};

export const updateWorldElement = async (bookId, element) => {
  try {
    console.log('updateWorldElement called with:', { bookId, element });

    // Use the correct endpoint format based on the backend API
    const response = await fetch(`${BASE_URL}/books/${bookId}/world/elements`, {
      method: 'POST',
      headers: getHeaders(),
      body: JSON.stringify({
        category: element.category,
        name: element.name,
        description: element.description || '',
        element_id: element.id, // This will be null for new elements
        attributes: element.attributes || {}
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('API error response:', errorText);
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    console.log('updateWorldElement response:', result);
    return result;
  } catch (error) {
    console.error('Error updating world element:', error);
    throw error;
  }
};

export const deleteWorldElement = async (bookId, elementId) => {
  try {
    const response = await fetch(`${BASE_URL}/books/${bookId}/world/elements/${elementId}`, {
      method: 'DELETE',
      headers: getHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error deleting world element:', error);
    throw error;
  }
};

/**
 * Generates world elements using AI
 * @param {string} bookId - Book identifier
 * @param {string} prompt - Prompt for AI generation
 * @param {string} category - Category of the world element
 * @param {number} count - Number of elements to generate (default: 1, max: 5)
 * @returns {Promise<Array>} Array of generated world elements
 */
export const generateWorldElement = async (bookId, prompt, category, count = 1) => {
  try {
    // Ensure count is between 1 and 5
    const validCount = Math.max(1, Math.min(count, 5));

    console.log('Generating world elements with AI:', { bookId, prompt, category, count: validCount });
    const response = await apiRequest(`${BASE_URL}/books/${bookId}/world/generate`, {
      method: 'POST',
      body: JSON.stringify({ prompt, category, count: validCount })
    }, {
      retry: true
    });

    console.log(`Generated ${Array.isArray(response) ? response.length : 1} world elements:`, response);
    return response;
  } catch (error) {
    console.error('Error generating world elements:', error);
    throw error;
  }
};

/**
 * Fetches brainstorm data for a book
 * @param {string} bookId - Book identifier
 * @returns {Promise<Array>} List of brainstorm cards
 */
export const fetchBrainstormData = async (bookId) => {
  try {
    console.debug(`Fetching brainstorm data for book ${bookId}`);
    const response = await fetch(`${BASE_URL}/books/${bookId}/brainstorm`, {
      method: 'GET',
      headers: getHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    console.debug('Brainstorm API response structure:', {
      hasBookId: !!data.book_id,
      hasBrainstorm: !!data.brainstorm,
      hasPages: data.brainstorm && !!data.brainstorm.pages,
      pageCount: data.brainstorm && data.brainstorm.pages ? Object.keys(data.brainstorm.pages).length : 0
    });

    // Ensure we always return an array
    if (Array.isArray(data)) {
      console.debug('Data is already an array, returning directly');
      return data;
    } else if (data && typeof data === 'object') {
      // Handle the specific format we're seeing in the logs
      if (data.pages) {
        console.debug('Found brainstorm data with pages structure');
        return processPages(data.pages);
      } else if (data.brainstorm && data.brainstorm.pages) {
        console.debug('Found brainstorm data with nested pages structure');
        return processPages(data.brainstorm.pages);
      } else if (data.cards && Array.isArray(data.cards)) {
        console.debug('Found cards array directly in data');
        return data.cards;
      }
    }

    // Helper function to process pages and extract cards
    function processPages(pages) {
      const allCards = [];
      const pageKeys = Object.keys(pages);

      console.debug(`Processing ${pageKeys.length} pages:`, pageKeys);

      pageKeys.forEach(pageKey => {
        const page = pages[pageKey];
        console.debug(`Processing page ${pageKey}:`, {
          hasNodes: Array.isArray(page?.nodes),
          nodeCount: Array.isArray(page?.nodes) ? page.nodes.length : 0
        });

        if (page && page.nodes && Array.isArray(page.nodes)) {
          // Convert nodes to cards format
          const cardsFromNodes = page.nodes.map(node => {
            // Check if node has an ID
            if (!node.id) {
              console.error('Node missing ID - skipping this node:', node);
              // Skip nodes without IDs instead of generating stable IDs
              return null;
            }

            // Handle different node formats
            let card = {
              id: node.id,
              page: pageKey,
              position: {
                x: 0,
                y: 0
              }
            };

            // Extract position with detailed logging
            if (node.position) {
              console.debug(`Node ${node.id} has position object:`, node.position);
              card.position = node.position;
            } else if (node.x !== undefined && node.y !== undefined) {
              console.debug(`Node ${node.id} has x/y coordinates:`, { x: node.x, y: node.y });
              card.position = { x: node.x, y: node.y };
            } else if (node.position_x !== undefined && node.position_y !== undefined) {
              console.debug(`Node ${node.id} has position_x/position_y:`, { x: node.position_x, y: node.position_y });
              card.position = { x: node.position_x, y: node.position_y };
            }

            // Extract card data with detailed logging
            if (node.data) {
              console.debug(`Node ${node.id} has data object`);
              card.title = node.data.label || node.data.title || 'Untitled';
              card.content = node.data.content || '';
              card.type = node.data.type || 'idea';
              card.characters = Array.isArray(node.data.characters) ? node.data.characters : [];
              card.locations = Array.isArray(node.data.locations) ? node.data.locations : [];
            } else {
              console.debug(`Node ${node.id} has direct properties`);
              card.title = node.title || node.text || 'Untitled';
              card.content = node.content || node.description || node.text || '';
              card.type = node.type || 'idea';
              card.characters = Array.isArray(node.characters) ? node.characters : [];
              card.locations = Array.isArray(node.locations) ? node.locations : [];
            }

            console.debug(`Processed node ${node.id} into card:`, {
              id: card.id,
              title: card.title,
              type: card.type,
              position: card.position
            });

            return card;
          });

          // Filter out null values before adding to allCards
          const validCards = cardsFromNodes.filter(card => card !== null);
          console.debug(`Adding ${validCards.length} valid cards from page ${pageKey} (filtered out ${cardsFromNodes.length - validCards.length} invalid cards)`);
          allCards.push(...validCards);
        }
      });

      console.debug('Extracted cards from pages:', {
        totalCards: allCards.length,
        cardIds: allCards.map(card => card.id)
      });
      return allCards;
    }

    console.warn('Unexpected brainstorm data format:', data);
    return [];
  } catch (error) {
    console.error('Error fetching brainstorm data:', error);
    return []; // Return empty array on error instead of throwing
  }
};

/**
 * Adds a new brainstorm card
 * @param {string} bookId - Book identifier
 * @param {Object} cardData - Brainstorm card data
 * @returns {Promise<Object>} Created brainstorm card
 */
export const addBrainstormCard = async (bookId, cardData) => {
  try {
    console.debug('Adding brainstorm card with data:', cardData);

    // Ensure page is included in the request
    const pageId = cardData.page || 'page1';

    // Generate a UUID-based ID with node_ prefix if none is provided
    let cardId = cardData.id;
    if (!cardId) {
      cardId = `node_${Math.random().toString(36).substring(2, 15)}${Math.random().toString(36).substring(2, 15)}`;
      console.debug(`Generated new frontend node ID: ${cardId}`);
    }

    // Prepare the card data
    const cardToSend = {
      id: cardId,
      page: pageId,
      title: cardData.title || 'Untitled',
      content: cardData.content || '',
      type: cardData.type || 'idea',
      position: cardData.position || { x: Math.random() * 300 + 100, y: Math.random() * 300 + 100 },
      width: cardData.width || 200,
      height: cardData.height || 100,
      characters: Array.isArray(cardData.characters) ? cardData.characters : [],
      locations: Array.isArray(cardData.locations) ? cardData.locations : []
    };

    console.debug('Sending card data to backend:', {
      id: cardToSend.id,
      page: cardToSend.page,
      title: cardToSend.title,
      type: cardToSend.type
    });

    // Send the card data to the new endpoint
    const response = await fetch(`${BASE_URL}/books/${bookId}/brainstorm/cards`, {
      method: 'POST',
      headers: getHeaders(),
      body: JSON.stringify(cardToSend)
    });

    if (!response.ok) {
      console.error(`HTTP error when creating brainstorm card: ${response.status}`);
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const responseData = await response.json();
    console.debug('Brainstorm card creation response:', responseData);

    // Return the new card data in the format expected by the frontend
    const newCard = {
      id: cardId, // Use the generated or provided ID
      title: cardData.title || 'Untitled',
      content: cardData.content || '',
      type: cardData.type || 'idea',
      characters: Array.isArray(cardData.characters) ? cardData.characters : [],
      locations: Array.isArray(cardData.locations) ? cardData.locations : [],
      page: pageId,
      position: {
        x: cardData.position?.x || Math.random() * 300 + 100,
        y: cardData.position?.y || Math.random() * 300 + 100
      },
      created_at: new Date().toISOString()
    };

    console.debug('Returning new card to caller:', {
      id: newCard.id,
      title: newCard.title,
      type: newCard.type,
      page: newCard.page
    });

    return newCard;
  } catch (error) {
    console.error('Error adding brainstorm card:', error);
    throw error;
  }
};

/**
 * Updates a brainstorm card
 * @param {string} bookId - Book identifier
 * @param {string} cardId - Brainstorm card identifier
 * @param {Object} cardData - Updated brainstorm card data
 * @returns {Promise<Object>} Updated brainstorm card
 */
export const updateBrainstormCard = async (bookId, cardId, cardData) => {
  try {
    console.debug('Updating brainstorm card with ID:', cardId, 'Data:', {
      title: cardData.title,
      type: cardData.type,
      page: cardData.page,
      position: cardData.position
    });

    // Ensure page is included in the request
    const pageId = cardData.page || 'page1';

    // Prepare the card data
    const cardToSend = {
      id: cardId,
      page: pageId,
      title: cardData.title || 'Untitled',
      content: cardData.content || '',
      type: cardData.type || 'idea',
      position: cardData.position || { x: 0, y: 0 },
      width: cardData.width || 200,
      height: cardData.height || 100,
      characters: Array.isArray(cardData.characters) ? cardData.characters : [],
      locations: Array.isArray(cardData.locations) ? cardData.locations : []
    };

    console.debug('Sending card data to backend:', {
      id: cardToSend.id,
      page: cardToSend.page,
      title: cardToSend.title,
      type: cardToSend.type
    });

    // Send the card data to the new endpoint
    const response = await fetch(`${BASE_URL}/books/${bookId}/brainstorm/cards/${cardId}`, {
      method: 'PUT',
      headers: getHeaders(),
      body: JSON.stringify(cardToSend)
    });

    if (!response.ok) {
      console.error(`HTTP error when updating brainstorm card: ${response.status}`);
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const responseData = await response.json();
    console.debug('Brainstorm card update response:', responseData);

    // Return the updated card data in the format expected by the frontend
    const updatedCard = {
      ...cardData,
      id: cardId,
      title: cardData.title || 'Untitled',
      content: cardData.content || '',
      type: cardData.type || 'idea',
      characters: Array.isArray(cardData.characters) ? cardData.characters : [],
      locations: Array.isArray(cardData.locations) ? cardData.locations : [],
      page: pageId,
      position: cardData.position || { x: 0, y: 0 },
      updated_at: new Date().toISOString()
    };

    console.debug('Returning updated card to caller:', {
      id: updatedCard.id,
      title: updatedCard.title,
      type: updatedCard.type,
      page: updatedCard.page
    });

    return updatedCard;
  } catch (error) {
    console.error('Error updating brainstorm card:', error);
    throw error;
  }
};

/**
 * Deletes a brainstorm card
 * @param {string} bookId - Book identifier
 * @param {string} cardId - Brainstorm card identifier
 * @returns {Promise<Object>} Success message
 */
export const deleteBrainstormCard = async (bookId, cardId) => {
  try {
    console.debug('Deleting brainstorm card with ID:', cardId);

    // Send the delete request to the new endpoint
    const response = await fetch(`${BASE_URL}/books/${bookId}/brainstorm/cards/${cardId}`, {
      method: 'DELETE',
      headers: getHeaders()
    });

    if (!response.ok) {
      console.error(`HTTP error when deleting brainstorm card: ${response.status}`);
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const responseData = await response.json();
    console.debug('Brainstorm card deletion response:', responseData);

    // Return the card ID to confirm deletion
    console.debug(`Successfully deleted card ${cardId}`);
    return cardId;
  } catch (error) {
    console.error('Error deleting brainstorm card:', error);
    // Return the card ID even on error to ensure it's removed from the UI
    return cardId;
  }
};

/**
 * Generates a brainstorm idea using AI
 * @param {string} bookId - Book identifier
 * @param {string} page - Current page ID (e.g., 'page1', 'page2')
 * @param {string} prompt - Prompt for AI generation (optional)
 * @param {string} cardType - Type of brainstorm card (optional)
 * @returns {Promise<Object>} Generated brainstorm idea
 */
export const generateBrainstormIdea = async (bookId, page = 'page1') => {
  try {
    console.log('[DEBUG API] Generating brainstorm ideas for book:', bookId, 'on page:', page);

    const response = await fetch(`${BASE_URL}/books/${bookId}/brainstorm/generate`, {
      method: 'POST',
      headers: {
        ...getHeaders(),
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ page })
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`[DEBUG API] HTTP error! status: ${response.status}, message:`, errorText);
      throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
    }

    const data = await response.json();
    console.log('[DEBUG API] Generated brainstorm ideas:', data);
    return data;
  } catch (error) {
    console.error('[DEBUG API] Error generating brainstorm ideas:', error);
    throw error;
  }
};

/**
 * Connects selected brainstorm ideas using AI
 * @param {string} bookId - Book identifier
 * @param {string} page - Current page ID (e.g., 'page1', 'page2')
 * @param {Array} selectedNodes - Array of selected nodes to connect
 * @returns {Promise<Object>} Generated connected ideas
 */
export const connectBrainstormIdeas = async (bookId, page = 'page1', selectedNodes = []) => {
  try {
    console.log('[DEBUG API] Connecting brainstorm ideas for book:', bookId, 'on page:', page);
    console.log('[DEBUG API] Selected nodes count:', selectedNodes.length);

    const response = await fetch(`${BASE_URL}/books/${bookId}/brainstorm/connect`, {
      method: 'POST',
      headers: {
        ...getHeaders(),
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ page, selectedNodes })
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`[DEBUG API] HTTP error! status: ${response.status}, message:`, errorText);
      throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
    }

    const data = await response.json();
    console.log('[DEBUG API] Connected brainstorm ideas:', data);
    return data;
  } catch (error) {
    console.error('[DEBUG API] Error connecting brainstorm ideas:', error);
    throw error;
  }
};

/**
 * Generates a plot event using AI
 * @param {string} bookId - Book identifier
 * @param {string} prompt - Prompt for AI generation
 * @returns {Promise<Object>} Generated plot event
 */
export const generatePlotEvent = async (bookId, prompt) => {
  try {
    const response = await fetch(`${BASE_URL}/books/${bookId}/plot/events/generate`, {
      method: 'POST',
      headers: getHeaders(),
      body: JSON.stringify({ prompt })
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error generating plot event:', error);
    throw error;
  }
};

// Add these new functions for chapter content and plot events

/**
 * Fetches chapter content from the backend
 * @param {string} bookId - The ID of the book
 * @param {string} chapterId - The ID of the chapter
 * @returns {Promise<Object>} Chapter content
 */
export const fetchChapterContent = async (bookId, chapterId) => {
  if (!bookId || !chapterId) {
    console.error('[DEBUG API] Missing bookId or chapterId in fetchChapterContent');
    return { content: '' };
  }

  console.log(`[DEBUG API] Fetching content for chapter ${chapterId} in book ${bookId}`);

  try {
    // Add retry logic for robustness
    let retries = 1;

    while (retries >= 0) {
      try {
        console.log(`[DEBUG API] Fetch attempt ${2-retries}/2`);
        const url = `${BASE_URL}/books/${bookId}/chapters/${chapterId}/content`;
        console.log('[DEBUG API] Request URL:', url);

        const response = await fetch(url, {
          method: 'GET',
          headers: getHeaders()
        });

        console.log('[DEBUG API] Response status:', response.status);

        // Parse the response even if it's not OK to get any error details
        const data = await response.json().catch((err) => {
          console.error('[DEBUG API] Error parsing JSON response:', err);
          return { content: '' };
        });

        console.log('[DEBUG API] Response data type:', typeof data);
        console.log('[DEBUG API] Response content type:', typeof data.content);
        console.log('[DEBUG API] Response content length:', data.content?.length || 0);

        if (!response.ok) {
          console.error(`[DEBUG API] Failed to fetch chapter content (attempt ${1-retries}/2): ${response.status}`);
          retries--;

          if (retries >= 0) {
            // Wait before retrying
            console.log('[DEBUG API] Waiting before retry...');
            await new Promise(resolve => setTimeout(resolve, 1000));
            continue;
          }

          return { content: data.content || '' };
        }

        console.log('[DEBUG API] Chapter content fetched successfully');
        return { content: data.content || '' };
      } catch (error) {
        console.error(`[DEBUG API] Error in fetch attempt (${retries} retries left):`, error);
        retries--;

        if (retries < 0) {
          return { content: '' };
        }

        // Wait before retrying
        console.log('[DEBUG API] Waiting before retry...');
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    return { content: '' };
  } catch (error) {
    console.error('[DEBUG API] Error in fetchChapterContent:', error);
    return { content: '' };
  }
};

/**
 * Saves chapter content to the backend using book ID and chapter ID
 * @param {string} bookId - The ID of the book
 * @param {string} chapterId - The ID of the chapter
 * @param {string} content - The Draft.js content in raw JSON string format
 * @returns {Promise<Object>} Updated chapter data
 */
export const saveChapterContentWithBookId = async (bookId, chapterId, content) => {
  if (!bookId || !chapterId) {
    console.error('[DEBUG API] Missing bookId or chapterId in saveChapterContentWithBookId');
    throw new Error('Missing bookId or chapterId');
  }

  console.log(`[DEBUG API] Saving content for chapter ${chapterId} in book ${bookId}`);
  console.log('[DEBUG API] Content type:', typeof content);
  console.log('[DEBUG API] Content length:', content?.length || 0);

  // Ensure content is a string
  const contentToSave = typeof content === 'string' ? content : JSON.stringify(content);
  console.log('[DEBUG API] Content to save type:', typeof contentToSave);
  console.log('[DEBUG API] Content to save length:', contentToSave.length);

  try {
    // Add retry logic for robustness
    let retries = 2;
    let lastError = null;

    while (retries >= 0) {
      try {
        console.log(`[DEBUG API] Save attempt ${3-retries}/3`);
        const url = `${BASE_URL}/books/${bookId}/chapters/${chapterId}/content`;
        console.log('[DEBUG API] Request URL:', url);

        const requestBody = JSON.stringify({ content: contentToSave });
        console.log('[DEBUG API] Request body length:', requestBody.length);

        const response = await fetch(url, {
          method: 'PUT',
          headers: getHeaders(),
          body: requestBody
        });

        console.log('[DEBUG API] Response status:', response.status);

        if (!response.ok) {
          const errorText = await response.text();
          console.error(`[DEBUG API] API error (attempt ${2-retries}/3):`, errorText);
          throw new Error(`Failed to save chapter content: ${response.status} - ${errorText}`);
        }

        const data = await response.json();
        console.log('[DEBUG API] Chapter content saved successfully:', data);
        return data;
      } catch (error) {
        console.error(`[DEBUG API] Save attempt failed (${retries} retries left):`, error);
        lastError = error;
        retries--;

        if (retries >= 0) {
          // Wait before retrying
          console.log('[DEBUG API] Waiting before retry...');
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }
    }

    // If we get here, all retries failed
    console.error('[DEBUG API] All save attempts failed');
    throw lastError || new Error('Failed to save chapter content after multiple attempts');
  } catch (error) {
    console.error('[DEBUG API] Error in saveChapterContentWithBookId:', error);
    throw error;
  }
};

export const fetchPlotEvents = async (bookId, chapterId) => {
  try {
    const response = await fetch(`${BASE_URL}/books/${bookId}/plot/chapters/${chapterId}/events`, {
      method: 'GET',
      headers: getHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error fetching plot events:', error);
    throw error;
  }
};

export const fetchEvents = async (bookId) => {
  try {
    const response = await fetch(`${BASE_URL}/books/${bookId}/plot/events`, {
      headers: getHeaders()
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status}`);
    }

    const data = await response.json();
    return data.events || [];
  } catch (error) {
    console.error('Error fetching events:', error);
    throw error;
  }
};

/**
 * Creates a new chapter in a book
 * @param {string} bookId - Book identifier
 * @param {object} chapterData - Chapter data (title, sequence_number, etc.)
 * @returns {Promise<Object>} New chapter data
 */
export const createChapter = async (bookId, chapterData) => {
  return apiRequest(`${BASE_URL}/books/${bookId}/chapters`, {
    method: 'POST',
    body: JSON.stringify(chapterData)
  }, {
    retry: true
  });
};

export const deleteChapter = async (bookId, chapterId) => {
  try {
    const response = await fetch(`${BASE_URL}/books/${bookId}/chapters/${chapterId}`, {
      method: 'DELETE',
      headers: getHeaders()
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('API error response:', errorText);
      try {
        const errorData = JSON.parse(errorText);
        throw new Error(errorData.detail || `Failed to delete chapter: ${response.status}`);
      } catch (e) {
        throw new Error(`Failed to delete chapter: ${response.status} - ${errorText}`);
      }
    }

    return true; // Successfully deleted
  } catch (error) {
    console.error('Error deleting chapter:', error);
    throw error;
  }
};

export const updateEventChapter = async (bookId, eventId, chapterId) => {
  try {
    const response = await fetch(`${BASE_URL}/books/${bookId}/plot/events/${eventId}`, {
      method: 'PATCH',
      headers: getHeaders(),
      body: JSON.stringify({ chapter_id: chapterId })
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error updating event chapter:', error);
    throw error;
  }
};

/**
 * Updates the order of chapters in a book
 * @param {string} bookId - Book identifier
 * @param {Array} chapters - Array of chapter objects with updated sequence_number
 * @returns {Promise<Object>} Updated chapters data
 */
export const updateChapterOrder = async (bookId, chapters) => {
  return apiRequest(`${BASE_URL}/books/${bookId}/chapters/order`, {
    method: 'PUT',
    body: JSON.stringify({ chapters })
  }, {
    retry: true
  });
};

/**
 * Creates a new event for a book
 * @param {string} bookId - Book identifier
 * @param {Object} eventData - Event data to create
 * @deprecated token parameter removed - now retrieved from localStorage
 * @returns {Promise<Object>} Created event data
 */
export const createEvent = async (bookId, eventData) => {
  try {
    const response = await fetch(`${BASE_URL}/books/${bookId}/plot/events`, {
      method: 'POST',
      headers: getHeaders(),
      body: JSON.stringify(eventData)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error creating event:', error);
    throw error;
  }
};

/**
 * Updates a user's profile information
 * @param {Object} userData - User data to update
 * @param {string} userData.name - User's name
 * @param {string} userData.email - User's email
 * @param {string} userData.bio - User's biography
 * @param {string} userData.avatar - User's avatar URL
 * @returns {Promise<Object>} Updated user data
 */
export const updateUserProfile = async (userData) => {
  try {
    const response = await fetch(`${BASE_URL}/users/profile`, {
      method: 'PUT',
      headers: getHeaders(),
      body: JSON.stringify(userData)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error updating user profile:', error);
    throw error;
  }
};

/**
 * Logs out the current user
 * @returns {Promise<Object>} Success message
 */
export const logoutUser = async () => {
  try {
    const response = await fetch(`${BASE_URL}/auth/logout`, {
      method: 'POST',
      headers: getHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error logging out:', error);
    throw error;
  }
};

/**
 * Registers a new user
 * @param {string} email - Email for the new user
 * @param {string} password - Password for the new user
 * @returns {Promise<Object>} New user data
 */
export const registerUser = async (email, password) => {
  try {
    const response = await fetch(`${BASE_URL}/auth/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ email, password })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || `Registration failed: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error registering user:', error);
    throw error;
  }
};

/**
 * Fetches metadata for a book
 * @param {string} bookId - ID of the book to fetch metadata for
 * @returns {Promise<Object>} Book metadata
 */
export const fetchBookMetadata = async (bookId) => {
  try {
    console.log('apiService: Fetching metadata for book ID:', bookId);
    console.log('apiService: Request URL:', `${BASE_URL}/books/${bookId}/metadata`);
    console.log('apiService: Headers:', getHeaders());

    const response = await fetch(`${BASE_URL}/books/${bookId}/metadata`, {
      method: 'GET',
      headers: getHeaders()
    });

    console.log('apiService: Metadata response status:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('apiService: Error response body:', errorText);
      throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
    }

    const data = await response.json();
    console.log('apiService: Metadata response data:', data);
    return data;
  } catch (error) {
    console.error('Error fetching book metadata:', error);
    // Return empty object instead of throwing to prevent UI errors
    return {};
  }
};

/**
 * Fetches detailed information about a book
 * @param {string} bookId - ID of the book to fetch
 * @returns {Promise<Object>} Book details
 */
export const fetchBookDetails = async (bookId) => {
  try {
    const response = await fetch(`${BASE_URL}/books/${bookId}`, {
      method: 'GET',
      headers: getHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error fetching book details:', error);
    throw error;
  }
};

/**
 * Updates an existing book
 * @param {string} bookId - ID of the book to update
 * @param {Object} bookData - Updated book data
 * @returns {Promise<Object>} Updated book
 */
export const updateBook = async (bookId, bookData) => {
  try {
    console.log(`Updating book with ID: ${bookId}`, bookData);
    // Use the PUT endpoint for updating books
    const response = await fetch(`${BASE_URL}/books/${bookId}`, {
      method: 'PUT',
      headers: getHeaders(),
      body: JSON.stringify(bookData)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error updating book:', error);
    throw error;
  }
};

/**
 * Updates a book's memory data
 * @param {string} bookId - Book ID
 * @param {Object} memoryData - Memory data to store
 * @returns {Promise<Object>} Updated book memory
 */
export const updateBookMemory = async (bookId, memoryData) => {
  try {
    console.log('%c apiService: updateBookMemory called with:', 'background: #3498db; color: white; padding: 2px 5px; border-radius: 3px;', {
      bookId,
      memoryDataLength: memoryData.length
    });

    const requestBody = JSON.stringify({ memory: memoryData });
    console.log('%c apiService: Request body:', 'background: #3498db; color: white; padding: 2px 5px; border-radius: 3px;', {
      bodyLength: requestBody.length,
      bodyPreview: requestBody.substring(0, 100) + '...'
    });

    console.log('%c apiService: Sending PUT request to:', 'background: #3498db; color: white; padding: 2px 5px; border-radius: 3px;', `${BASE_URL}/books/${bookId}/memory`);

    const response = await fetch(`${BASE_URL}/books/${bookId}/memory`, {
      method: 'PUT',
      headers: getHeaders(),
      body: requestBody,
    });

    console.log('%c apiService: Response status:', 'background: #3498db; color: white; padding: 2px 5px; border-radius: 3px;', {
      status: response.status,
      ok: response.ok
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('%c apiService: Error response:', 'background: #e74c3c; color: white; padding: 2px 5px; border-radius: 3px;', {
        status: response.status,
        text: errorText
      });
      throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
    }

    const responseData = await response.json();
    console.log('%c apiService: Response data:', 'background: #2ecc71; color: white; padding: 2px 5px; border-radius: 3px;', responseData);

    return responseData;
  } catch (error) {
    console.error('%c apiService: Error in updateBookMemory:', 'background: #e74c3c; color: white; padding: 2px 5px; border-radius: 3px;', error);
    throw error;
  }
};

/**
 * Deletes a book
 * @param {string} bookId - ID of the book to delete
 * @returns {Promise<Object>} Success message
 */
export const deleteBook = async (bookId) => {
  try {
    const response = await fetch(`${BASE_URL}/books/${bookId}`, {
      method: 'DELETE',
      headers: getHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error deleting book:', error);
    throw error;
  }
};

/**
 * Updates a character
 * @param {string} bookId - ID of the book the character belongs to
 * @param {string} characterId - ID of the character to update
 * @param {Object} characterData - Updated character data
 * @returns {Promise<Object>} Updated character
 */
export const updateCharacter = async (bookId, characterId, characterData) => {
  try {
    const url = `${BASE_URL}/books/${bookId}/characters/${characterId}`;
    console.log(`Sending PUT request to: ${url}`);

    // Ensure gender_identity, sexual_orientation, and relationship strength are included
    const updatedCharacterData = {
      ...characterData,
      gender_identity: characterData.gender_identity || '',
      sexual_orientation: characterData.sexual_orientation || '',
      relationships: Array.isArray(characterData.relationships)
        ? characterData.relationships.map(rel => ({
            ...rel,
            strength: rel.strength !== undefined ? rel.strength : 3 // Default to 3 if not specified
          }))
        : []
    };

    const response = await fetch(url, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      },
      body: JSON.stringify(updatedCharacterData)
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Error updating character: ${response.status} ${response.statusText}`);
      console.error('Response body:', errorText);
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const responseData = await response.json();
    console.log('Character update response:', responseData);

    // Return the response data, falling back to our sent data for any missing fields
    return {
      ...updatedCharacterData,
      id: characterId,
      ...responseData
    };
  } catch (error) {
    console.error('Error updating character:', error);
    throw error; // Let the caller handle the error
  }
};

/**
 * Deletes a character
 * @param {string} bookId - ID of the book the character belongs to
 * @param {string} characterId - ID of the character to delete
 * @returns {Promise<Object>} Success message
 */
export const deleteCharacter = async (bookId, characterId) => {
  try {
    const response = await fetch(`${BASE_URL}/books/${bookId}/characters/${characterId}`, {
      method: 'DELETE',
      headers: getHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error deleting character:', error);
    throw error;
  }
};

/**
 * Generates a character using AI
 * @param {string} bookId - ID of the book to generate a character for
 * @param {Object} prompt - Prompt data for character generation
 * @returns {Promise<Object>} Generated character
 */
export const generateCharacter = async (bookId, prompt) => {
  try {
    const response = await fetch(`${BASE_URL}/books/${bookId}/characters/generate`, {
      method: 'POST',
      headers: getHeaders(),
      body: JSON.stringify({ prompt })
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error generating character:', error);
    throw error;
  }
};

/**
 * Reorders chapters for a book
 * @param {string} bookId - The ID of the book
 * @param {Array} chapters - Array of chapter objects with updated sequence numbers
 * @returns {Promise<Array>} Updated chapters array
 */
export const reorderChaptersWithBookId = async (bookId, chapters) => {
  if (!bookId || !chapters) {
    console.error('[DEBUG API] Missing bookId or chapters in reorderChaptersWithBookId');
    throw new Error('Missing bookId or chapters');
  }

  console.log(`[DEBUG API] Reordering chapters for book ${bookId}`);
  console.log('[DEBUG API] Chapters to reorder:', chapters);

  try {
    const response = await fetch(`${BASE_URL}/books/${bookId}/chapters/reorder`, {
      method: 'PUT',
      headers: {
        ...getHeaders(),
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ chapters })
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error('[DEBUG API] Error response:', errorData);
      throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    console.log('[DEBUG API] Reorder response:', data);
    return chapters; // Return the reordered chapters
  } catch (error) {
    console.error('[DEBUG API] Error in reorderChaptersWithBookId:', error);
    throw error;
  }
};

/**
 * Creates a new chapter for a book
 * @param {string} bookId - The ID of the book
 * @param {string} title - The title of the new chapter
 * @returns {Promise<Object>} The created chapter
 */
export const createChapterWithBookId = async (bookId, title, sequenceNumber) => {
  if (!bookId || !title) {
    console.error('[DEBUG API] Missing bookId or title in createChapterWithBookId');
    throw new Error('Missing bookId or title');
  }

  console.log(`[DEBUG API] Creating new chapter for book ${bookId}`);
  console.log('[DEBUG API] Chapter title:', title);
  console.log('[DEBUG API] Sequence number:', sequenceNumber);

  try {
    // First, get all existing chapters to calculate the next sequence number if not provided
    if (sequenceNumber === undefined) {
      try {
        console.log('[DEBUG API] Sequence number not provided, fetching chapters to calculate');
        const chaptersResponse = await fetch(`${BASE_URL}/books/${bookId}/chapters`, {
          method: 'GET',
          headers: getHeaders()
        });

        if (chaptersResponse.ok) {
          const chapters = await chaptersResponse.json();
          console.log('[DEBUG API] Fetched chapters:', chapters);

          // Calculate the next sequence number
          sequenceNumber = chapters && chapters.length > 0
            ? Math.max(...chapters.map(ch => ch.sequence_number || 0)) + 1
            : 1;

          console.log('[DEBUG API] Calculated sequence number:', sequenceNumber);
        } else {
          console.error('[DEBUG API] Failed to fetch chapters, using default sequence number 1');
          sequenceNumber = 1;
        }
      } catch (error) {
        console.error('[DEBUG API] Error fetching chapters:', error);
        sequenceNumber = 1;
      }
    }

    // Now create the chapter with the sequence number
    const response = await fetch(`${BASE_URL}/books/${bookId}/chapters`, {
      method: 'POST',
      headers: {
        ...getHeaders(),
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        title,
        sequence_number: sequenceNumber
      })
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error('[DEBUG API] Error response:', errorData);
      throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    console.log('[DEBUG API] Create chapter response:', data);
    return data;
  } catch (error) {
    console.error('[DEBUG API] Error in createChapterWithBookId:', error);
    throw error;
  }
};

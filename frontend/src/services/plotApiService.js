// frontend/src/services/plotApiService.js
import { getHeaders } from './apiService';
import { BASE_URL } from '../utils/apiConfig';

/**
 * Fetches all plot events for a book
 * @param {string} bookId - Book identifier
 * @returns {Promise<Array>} List of plot events
 */
export const fetchAllEvents = async (bookId) => {
  try {
    console.log(`[DEBUG] Fetching all plot events for book ${bookId}`);
    const response = await fetch(`${BASE_URL}/books/${bookId}/plot/events`, {
      method: 'GET',
      headers: getHeaders()
    });

    console.log(`[DEBUG] Response status:`, response.status);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    console.log(`[DEBUG] Fetched plot events:`, data);

    // Log detailed information about each event's characters and locations
    if (Array.isArray(data)) {
      data.forEach(event => {
        console.log(`[DEBUG] Event ${event.id} data:`, {
          title: event.title,
          characters: event.characters,
          locations: event.locations,
          type: event.type
        });
      });
    }

    return data;
  } catch (error) {
    console.error('Error fetching all plot events:', error);
    throw error;
  }
};

/**
 * Fetches a single plot event
 * @param {string} bookId - Book identifier
 * @param {string} eventId - Event identifier
 * @returns {Promise<Object>} Plot event
 */
export const fetchEvent = async (bookId, eventId) => {
  try {
    console.log(`[DEBUG] Fetching plot event ${eventId} for book ${bookId}`);
    const response = await fetch(`${BASE_URL}/books/${bookId}/plot/events/${eventId}`, {
      method: 'GET',
      headers: getHeaders()
    });

    console.log(`[DEBUG] Response status:`, response.status);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    console.log(`[DEBUG] Fetched plot event:`, data);
    return data;
  } catch (error) {
    console.error(`Error fetching plot event ${eventId}:`, error);
    throw error;
  }
};

/**
 * Fetches plot events for a specific chapter
 * @param {string} bookId - Book identifier
 * @param {string} chapterId - Chapter identifier
 * @returns {Promise<Array>} List of plot events for the chapter
 */
export const fetchChapterEvents = async (bookId, chapterId) => {
  try {
    const response = await fetch(`${BASE_URL}/books/${bookId}/chapters/${chapterId}/events`, {
      method: 'GET',
      headers: getHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error(`Error fetching events for chapter ${chapterId}:`, error);
    throw error;
  }
};

/**
 * Fetches all chapters for a book
 * @param {string} bookId - Book identifier
 * @returns {Promise<Array>} List of chapters
 */
export const fetchChapters = async (bookId) => {
  try {
    console.log(`[DEBUG] Fetching chapters for book ${bookId}`);
    console.log(`[DEBUG] API URL: ${BASE_URL}/books/${bookId}/chapters`);
    console.log(`[DEBUG] Headers:`, getHeaders());

    const response = await fetch(`${BASE_URL}/books/${bookId}/chapters`, {
      method: 'GET',
      headers: getHeaders()
    });

    console.log(`[DEBUG] Response status:`, response.status);
    console.log(`[DEBUG] Response headers:`, Object.fromEntries([...response.headers.entries()]));

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`[DEBUG] Error response body:`, errorText);
      throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
    }

    const data = await response.json();
    console.log(`[DEBUG] Fetched chapters:`, data);
    return data;
  } catch (error) {
    console.error('[DEBUG] Error fetching chapters:', error);
    console.error('[DEBUG] Error stack:', error.stack);
    throw error;
  }
};

/**
 * Creates a new plot event
 * @param {string} bookId - Book identifier
 * @param {Object} eventData - Event data
 * @returns {Promise<Object>} Created event
 */
export const createEvent = async (bookId, eventData) => {
  try {
    console.log(`[DEBUG] Creating plot event for book ${bookId}`);
    console.log(`[DEBUG] Event data:`, eventData);

    // Ensure the eventData has the required fields for the backend API
    // Include title, description, and metadata
    const validatedEventData = {
      title: eventData.title || 'Untitled Event',
      description: eventData.description || '',
      // Include metadata if available
      characters: eventData.characters || [],
      locations: eventData.locations || [],
      event_type: eventData.event_type || 'idea'
      // Don't send chapter_id or sequence_number - let the backend handle it
    };

    console.log('[DEBUG] Including metadata in request:', {
      characters: validatedEventData.characters,
      locations: validatedEventData.locations,
      event_type: validatedEventData.event_type
    });

    console.log(`[DEBUG] Validated event data:`, validatedEventData);
    console.log(`[DEBUG] API URL: ${BASE_URL}/books/${bookId}/plot/events`);
    console.log(`[DEBUG] Headers:`, getHeaders());
    console.log(`[DEBUG] Request body:`, JSON.stringify(validatedEventData));

    const response = await fetch(`${BASE_URL}/books/${bookId}/plot/events`, {
      method: 'POST',
      headers: getHeaders(),
      body: JSON.stringify(validatedEventData)
    });

    console.log(`[DEBUG] Response status:`, response.status);
    console.log(`[DEBUG] Response headers:`, Object.fromEntries([...response.headers.entries()]));

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`[DEBUG] Error response body:`, errorText);
      throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
    }

    const data = await response.json();
    console.log(`[DEBUG] Created plot event:`, data);

    // Ensure the response has the expected format
    return {
      id: data.id || data.event_id,
      book_id: data.book_id,
      chapter_id: data.chapter_id,
      title: data.title,
      description: data.description,
      sequence_number: data.sequence_number,
      is_in_bank: data.is_in_bank !== undefined ? data.is_in_bank : (data.chapter_id === null),
      created_at: data.created_at,
      updated_at: data.updated_at
    };
  } catch (error) {
    console.error('[DEBUG] Error creating plot event:', error);
    console.error('[DEBUG] Error stack:', error.stack);
    throw error;
  }
};

/**
 * Updates an existing plot event
 * @param {string} bookId - Book identifier
 * @param {string} eventId - Event identifier
 * @param {Object} eventData - Updated event data
 * @returns {Promise<Object>} Updated event
 */
export const updateEvent = async (bookId, eventId, eventData) => {
  try {
    const response = await fetch(`${BASE_URL}/books/${bookId}/plot/events/${eventId}`, {
      method: 'PUT',
      headers: getHeaders(),
      body: JSON.stringify(eventData)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error(`Error updating plot event ${eventId}:`, error);
    throw error;
  }
};

/**
 * Deletes a plot event
 * @param {string} bookId - Book identifier
 * @param {string} eventId - Event identifier
 * @returns {Promise<Object>} Success message
 */
export const deleteEvent = async (bookId, eventId) => {
  try {
    const response = await fetch(`${BASE_URL}/books/${bookId}/plot/events/${eventId}`, {
      method: 'DELETE',
      headers: getHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error(`Error deleting plot event ${eventId}:`, error);
    throw error;
  }
};

/**
 * Creates a new chapter
 * @param {string} bookId - Book identifier
 * @param {Object} chapterData - Chapter data
 * @returns {Promise<Object>} Created chapter
 */
export const createChapter = async (bookId, chapterData) => {
  try {
    console.log(`[DEBUG] Creating chapter with data:`, chapterData);
    console.log(`[DEBUG] Book ID:`, bookId);
    console.log(`[DEBUG] API URL:`, `${BASE_URL}/books/${bookId}/chapters`);
    console.log(`[DEBUG] Headers:`, getHeaders());
    console.log(`[DEBUG] Request body:`, JSON.stringify(chapterData));

    const response = await fetch(`${BASE_URL}/books/${bookId}/chapters`, {
      method: 'POST',
      headers: getHeaders(),
      body: JSON.stringify(chapterData)
    });

    console.log(`[DEBUG] Response status:`, response.status);
    console.log(`[DEBUG] Response headers:`, Object.fromEntries([...response.headers.entries()]));

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`[DEBUG] Error response body:`, errorText);
      throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
    }

    const responseData = await response.json();
    console.log(`[DEBUG] Response data:`, responseData);
    return responseData;
  } catch (error) {
    console.error('[DEBUG] Error creating chapter:', error);
    throw error;
  }
};

/**
 * Updates an existing chapter
 * @param {string} bookId - Book identifier
 * @param {string} chapterId - Chapter identifier
 * @param {Object} chapterData - Updated chapter data
 * @returns {Promise<Object>} Updated chapter
 */
export const updateChapter = async (bookId, chapterId, chapterData) => {
  try {
    const response = await fetch(`${BASE_URL}/books/${bookId}/chapters/${chapterId}`, {
      method: 'PUT',
      headers: getHeaders(),
      body: JSON.stringify(chapterData)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error(`Error updating chapter ${chapterId}:`, error);
    throw error;
  }
};

/**
 * Deletes a chapter
 * @param {string} bookId - Book identifier
 * @param {string} chapterId - Chapter identifier
 * @returns {Promise<Object>} Success message
 */
export const deleteChapter = async (bookId, chapterId) => {
  try {
    const response = await fetch(`${BASE_URL}/books/${bookId}/chapters/${chapterId}`, {
      method: 'DELETE',
      headers: getHeaders()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error(`Error deleting chapter ${chapterId}:`, error);
    throw error;
  }
};

/**
 * Updates the order of chapters
 * @param {string} bookId - Book identifier
 * @param {Array} chapters - Chapters with updated order
 * @returns {Promise<Object>} Success message
 */
export const updateChapterOrder = async (bookId, chapters) => {
  try {
    const response = await fetch(`${BASE_URL}/books/${bookId}/chapters/order`, {
      method: 'PUT',
      headers: getHeaders(),
      body: JSON.stringify({ chapters })
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error updating chapter order:', error);
    throw error;
  }
};

/**
 * Assigns an event to a chapter
 * @param {string} bookId - Book identifier
 * @param {string} eventId - Event identifier
 * @param {string} chapterId - Chapter identifier
 * @returns {Promise<Object>} Updated event
 */
export const assignEventToChapter = async (bookId, eventId, chapterId) => {
  try {
    console.log(`[DEBUG] assignEventToChapter called with bookId: ${bookId}, eventId: ${eventId}, chapterId: ${chapterId}`);
    console.log(`[DEBUG] API URL: ${BASE_URL}/books/${bookId}/plot/events/${eventId}/assign`);

    // Handle null or undefined chapterId by converting to empty string
    const effectiveChapterId = chapterId === null || chapterId === undefined ? '' : chapterId;
    console.log(`[DEBUG] Effective chapterId:`, effectiveChapterId);
    console.log(`[DEBUG] Request body:`, { chapter_id: effectiveChapterId });

    const response = await fetch(`${BASE_URL}/books/${bookId}/plot/events/${eventId}/assign`, {
      method: 'POST',
      headers: getHeaders(),
      body: JSON.stringify({ chapter_id: effectiveChapterId })
    });

    console.log(`[DEBUG] Response status:`, response.status);
    console.log(`[DEBUG] Response headers:`, Object.fromEntries([...response.headers.entries()]));

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`[DEBUG] Error response body:`, errorText);
      throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
    }

    const result = await response.json();
    console.log(`[DEBUG] assignEventToChapter response:`, result);
    return result;
  } catch (error) {
    console.error(`[DEBUG] Error assigning event ${eventId} to chapter ${chapterId}:`, error);
    console.error(`[DEBUG] Error stack:`, error.stack);
    throw error;
  }
};

/**
 * Generates a plot event using AI
 * @param {string} bookId - Book identifier
 * @param {Object} promptData - Prompt data for AI generation
 * @returns {Promise<Object>} Generated event
 */
export const generatePlotEvent = async (bookId, promptData) => {
  try {
    const response = await fetch(`${BASE_URL}/books/${bookId}/plot/generate`, {
      method: 'POST',
      headers: getHeaders(),
      body: JSON.stringify(promptData)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error generating plot event with AI:', error);
    throw error;
  }
};

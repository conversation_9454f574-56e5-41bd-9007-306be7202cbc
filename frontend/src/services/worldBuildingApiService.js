// frontend/src/services/worldBuildingApiService.js
import { apiRequest } from './apiService';
import { BASE_URL } from '../utils/apiConfig';

/**
 * Fetches all world aspect categories
 * @returns {Promise<Array>} List of world aspect categories
 */
export const fetchWorldCategories = async (bookId) => {
  return apiRequest(`${BASE_URL}/books/${bookId}/world-building/categories`);
};

/**
 * Fetches a specific world aspect category
 * @param {string} bookId - Book identifier
 * @param {string} categoryId - Category identifier
 * @returns {Promise<Object>} Category data
 */
export const fetchWorldCategory = async (bookId, categoryId) => {
  return apiRequest(`${BASE_URL}/books/${bookId}/world-building/categories/${categoryId}`);
};

/**
 * Creates a new world aspect category
 * @param {string} bookId - Book identifier
 * @param {Object} categoryData - Category data
 * @returns {Promise<Object>} Created category
 */
export const createWorldCategory = async (bookId, categoryData) => {
  return apiRequest(`${BASE_URL}/books/${bookId}/world-building/categories`, {
    method: 'POST',
    body: JSON.stringify(categoryData)
  });
};

/**
 * Updates an existing world aspect category
 * @param {string} bookId - Book identifier
 * @param {string} categoryId - Category identifier
 * @param {Object} categoryData - Updated category data
 * @returns {Promise<Object>} Updated category
 */
export const updateWorldCategory = async (bookId, categoryId, categoryData) => {
  return apiRequest(`${BASE_URL}/books/${bookId}/world-building/categories/${categoryId}`, {
    method: 'PUT',
    body: JSON.stringify(categoryData)
  });
};

/**
 * Deletes a world aspect category
 * @param {string} bookId - Book identifier
 * @param {string} categoryId - Category identifier
 * @returns {Promise<Object>} Response data
 */
export const deleteWorldCategory = async (bookId, categoryId) => {
  return apiRequest(`${BASE_URL}/books/${bookId}/world-building/categories/${categoryId}`, {
    method: 'DELETE'
  });
};

/**
 * Fetches category customizations for a book
 * @param {string} bookId - Book identifier
 * @returns {Promise<Array>} List of category customizations
 */
export const fetchCategoryCustomizations = async (bookId) => {
  return apiRequest(`${BASE_URL}/books/${bookId}/world-building/customizations`);
};

/**
 * Updates a category customization
 * @param {string} bookId - Book identifier
 * @param {string} categoryId - Category identifier
 * @param {Object} customizationData - Customization data
 * @returns {Promise<Object>} Updated customization
 */
export const updateCategoryCustomization = async (bookId, categoryId, customizationData) => {
  // Include the category_id in the request body as required by the backend
  const requestData = {
    category_id: categoryId,
    ...customizationData
  };

  console.log('Updating category customization:', { bookId, categoryId, requestData });

  return apiRequest(`${BASE_URL}/books/${bookId}/world-building/customizations/${categoryId}`, {
    method: 'PUT',
    body: JSON.stringify(requestData)
  });
};

/**
 * Resets category customizations to defaults based on book genre
 * @param {string} bookId - Book identifier
 * @returns {Promise<Object>} Response data
 */
export const resetCategoryCustomizations = async (bookId) => {
  return apiRequest(`${BASE_URL}/books/${bookId}/world-building/customizations/reset`, {
    method: 'POST'
  });
};

/**
 * Fetches visible (enabled) categories for a book
 * @param {string} bookId - Book identifier
 * @returns {Promise<Array>} List of visible categories
 */
export const fetchVisibleCategories = async (bookId) => {
  return apiRequest(`${BASE_URL}/books/${bookId}/world-building/visible-categories`);
};

/**
 * Fetches all relationships for elements in a book
 * @param {string} bookId - Book identifier
 * @returns {Promise<Array>} List of relationships
 */
export const fetchWorldRelationships = async (bookId) => {
  return apiRequest(`${BASE_URL}/books/${bookId}/world-building/relationships`);
};

/**
 * Fetches relationships for a specific element
 * @param {string} bookId - Book identifier
 * @param {string} elementId - Element identifier
 * @returns {Promise<Array>} List of relationships
 */
export const fetchElementRelationships = async (bookId, elementId) => {
  return apiRequest(`${BASE_URL}/books/${bookId}/world-building/elements/${elementId}/relationships`);
};

/**
 * Creates a new relationship between world elements
 * @param {string} bookId - Book identifier
 * @param {Object} relationshipData - Relationship data
 * @returns {Promise<Object>} Created relationship
 */
export const createWorldRelationship = async (bookId, relationshipData) => {
  try {
    console.log('API Service - Creating world relationship:', { bookId, relationshipData });

    const response = await apiRequest(`${BASE_URL}/books/${bookId}/world-building/relationships`, {
      method: 'POST',
      body: JSON.stringify(relationshipData)
    });

    console.log('API Service - Created world relationship:', response);
    return response;
  } catch (error) {
    console.error('Error creating world relationship:', error);
    throw error;
  }
};

/**
 * Updates an existing relationship
 * @param {string} bookId - Book identifier
 * @param {string} relationshipId - Relationship identifier
 * @param {Object} relationshipData - Updated relationship data
 * @returns {Promise<Object>} Updated relationship
 */
export const updateWorldRelationship = async (bookId, relationshipId, relationshipData) => {
  return apiRequest(`${BASE_URL}/books/${bookId}/world-building/relationships/${relationshipId}`, {
    method: 'PUT',
    body: JSON.stringify(relationshipData)
  });
};

/**
 * Deletes a relationship
 * @param {string} bookId - Book identifier
 * @param {string} relationshipId - Relationship identifier
 * @returns {Promise<Object>} Response data
 */
export const deleteWorldRelationship = async (bookId, relationshipId) => {
  try {
    console.log('API Service - Deleting world relationship:', { bookId, relationshipId });

    const response = await apiRequest(`${BASE_URL}/books/${bookId}/world-building/relationships/${relationshipId}`, {
      method: 'DELETE'
    });

    console.log('API Service - Deleted world relationship:', response);
    return response;
  } catch (error) {
    console.error('Error deleting world relationship:', error);
    throw error;
  }
};

/**
 * Fetches relationship types
 * @param {string} bookId - Book identifier
 * @returns {Promise<Array>} List of relationship types
 */
export const fetchRelationshipTypes = async (bookId) => {
  return apiRequest(`${BASE_URL}/books/${bookId}/world-building/relationship-types`);
};

/**
 * Fetches table schemas for a book
 * @param {string} bookId - Book identifier
 * @returns {Promise<Array>} List of table schemas
 */
export const fetchTableSchemas = async (bookId) => {
  return apiRequest(`${BASE_URL}/books/${bookId}/world-building/tables/schemas`);
};

/**
 * Fetches a specific table schema
 * @param {string} bookId - Book identifier
 * @param {string} schemaId - Schema identifier
 * @returns {Promise<Object>} Table schema
 */
export const fetchTableSchema = async (bookId, schemaId) => {
  return apiRequest(`${BASE_URL}/books/${bookId}/world-building/tables/schemas/${schemaId}`);
};

/**
 * Creates a new table schema
 * @param {string} bookId - Book identifier
 * @param {Object} schemaData - Schema data
 * @returns {Promise<Object>} Created schema
 */
export const createTableSchema = async (bookId, schemaData) => {
  return apiRequest(`${BASE_URL}/books/${bookId}/world-building/tables/schemas`, {
    method: 'POST',
    body: JSON.stringify(schemaData)
  });
};

/**
 * Updates an existing table schema
 * @param {string} bookId - Book identifier
 * @param {string} schemaId - Schema identifier
 * @param {Object} schemaData - Updated schema data
 * @returns {Promise<Object>} Updated schema
 */
export const updateTableSchema = async (bookId, schemaId, schemaData) => {
  return apiRequest(`${BASE_URL}/books/${bookId}/world-building/tables/schemas/${schemaId}`, {
    method: 'PUT',
    body: JSON.stringify(schemaData)
  });
};

/**
 * Deletes a table schema
 * @param {string} bookId - Book identifier
 * @param {string} schemaId - Schema identifier
 * @returns {Promise<Object>} Response data
 */
export const deleteTableSchema = async (bookId, schemaId) => {
  return apiRequest(`${BASE_URL}/books/${bookId}/world-building/tables/schemas/${schemaId}`, {
    method: 'DELETE'
  });
};

/**
 * Fetches table rows for a specific element
 * @param {string} bookId - Book identifier
 * @param {string} elementId - Element identifier
 * @returns {Promise<Array>} List of table rows
 */
export const fetchTableRows = async (bookId, elementId) => {
  return apiRequest(`${BASE_URL}/books/${bookId}/world-building/elements/${elementId}/table-rows`);
};

/**
 * Creates a new table row
 * @param {string} bookId - Book identifier
 * @param {Object} rowData - Row data
 * @returns {Promise<Object>} Created row
 */
export const createTableRow = async (bookId, rowData) => {
  return apiRequest(`${BASE_URL}/books/${bookId}/world-building/tables/rows`, {
    method: 'POST',
    body: JSON.stringify(rowData)
  });
};

/**
 * Updates an existing table row
 * @param {string} bookId - Book identifier
 * @param {string} rowId - Row identifier
 * @param {Object} rowData - Updated row data
 * @returns {Promise<Object>} Updated row
 */
export const updateTableRow = async (bookId, rowId, rowData) => {
  return apiRequest(`${BASE_URL}/books/${bookId}/world-building/tables/rows/${rowId}`, {
    method: 'PUT',
    body: JSON.stringify(rowData)
  });
};

/**
 * Deletes a table row
 * @param {string} bookId - Book identifier
 * @param {string} rowId - Row identifier
 * @returns {Promise<Object>} Response data
 */
export const deleteTableRow = async (bookId, rowId) => {
  return apiRequest(`${BASE_URL}/books/${bookId}/world-building/tables/rows/${rowId}`, {
    method: 'DELETE'
  });
};

/**
 * Reorders table rows for an element
 * @param {string} bookId - Book identifier
 * @param {string} elementId - Element identifier
 * @param {Array} rowOrder - Array of row IDs in the desired order
 * @returns {Promise<Object>} Response data
 */
export const reorderTableRows = async (bookId, elementId, rowOrder) => {
  return apiRequest(`${BASE_URL}/books/${bookId}/world-building/elements/${elementId}/table-rows/reorder`, {
    method: 'POST',
    body: JSON.stringify(rowOrder)
  });
};

/**
 * Fetches maps for a book
 * @param {string} bookId - Book identifier
 * @returns {Promise<Array>} List of maps
 */
export const fetchMaps = async (bookId) => {
  return apiRequest(`${BASE_URL}/books/${bookId}/world-building/maps`);
};

/**
 * Fetches a specific map
 * @param {string} bookId - Book identifier
 * @param {string} mapId - Map identifier
 * @returns {Promise<Object>} Map data
 */
export const fetchMap = async (bookId, mapId) => {
  return apiRequest(`${BASE_URL}/books/${bookId}/world-building/maps/${mapId}`);
};

/**
 * Creates a new map
 * @param {string} bookId - Book identifier
 * @param {Object} mapData - Map data
 * @returns {Promise<Object>} Created map
 */
export const createMap = async (bookId, mapData) => {
  return apiRequest(`${BASE_URL}/books/${bookId}/world-building/maps`, {
    method: 'POST',
    body: JSON.stringify(mapData)
  });
};

/**
 * Updates an existing map
 * @param {string} bookId - Book identifier
 * @param {string} mapId - Map identifier
 * @param {Object} mapData - Updated map data
 * @returns {Promise<Object>} Updated map
 */
export const updateMap = async (bookId, mapId, mapData) => {
  return apiRequest(`${BASE_URL}/books/${bookId}/world-building/maps/${mapId}`, {
    method: 'PUT',
    body: JSON.stringify(mapData)
  });
};

/**
 * Deletes a map
 * @param {string} bookId - Book identifier
 * @param {string} mapId - Map identifier
 * @returns {Promise<Object>} Response data
 */
export const deleteMap = async (bookId, mapId) => {
  return apiRequest(`${BASE_URL}/books/${bookId}/world-building/maps/${mapId}`, {
    method: 'DELETE'
  });
};

/**
 * Fetches map elements for a specific map
 * @param {string} bookId - Book identifier
 * @param {string} mapId - Map identifier
 * @returns {Promise<Array>} List of map elements
 */
export const fetchMapElements = async (bookId, mapId) => {
  return apiRequest(`${BASE_URL}/books/${bookId}/world-building/maps/${mapId}/elements`);
};

/**
 * Creates a new map element
 * @param {string} bookId - Book identifier
 * @param {string} mapId - Map identifier
 * @param {Object} elementData - Element data
 * @returns {Promise<Object>} Created element
 */
export const createMapElement = async (bookId, mapId, elementData) => {
  return apiRequest(`${BASE_URL}/books/${bookId}/world-building/maps/${mapId}/elements`, {
    method: 'POST',
    body: JSON.stringify(elementData)
  });
};

/**
 * Updates an existing map element
 * @param {string} bookId - Book identifier
 * @param {string} mapId - Map identifier
 * @param {string} elementId - Element identifier
 * @param {Object} elementData - Updated element data
 * @returns {Promise<Object>} Updated element
 */
export const updateMapElement = async (bookId, mapId, elementId, elementData) => {
  return apiRequest(`${BASE_URL}/books/${bookId}/world-building/maps/${mapId}/elements/${elementId}`, {
    method: 'PUT',
    body: JSON.stringify(elementData)
  });
};

/**
 * Deletes a map element
 * @param {string} bookId - Book identifier
 * @param {string} mapId - Map identifier
 * @param {string} elementId - Element identifier
 * @returns {Promise<Object>} Response data
 */
export const deleteMapElement = async (bookId, mapId, elementId) => {
  return apiRequest(`${BASE_URL}/books/${bookId}/world-building/maps/${mapId}/elements/${elementId}`, {
    method: 'DELETE'
  });
};

/**
 * Creates a new world element
 * @param {string} bookId - Book identifier
 * @param {Object} elementData - Element data including category_id, parent_id, element_type, and custom fields
 * @returns {Promise<Object>} Created element
 */
export const createWorldElement = async (bookId, elementData) => {
  try {
    console.log('API Service - Creating world element:', { bookId, elementData });

    // Log the request details for debugging
    console.log('API Service - Request URL:', `${BASE_URL}/books/${bookId}/world-building/elements`);
    console.log('API Service - Request body:', JSON.stringify(elementData));

    const response = await apiRequest(`${BASE_URL}/books/${bookId}/world-building/elements`, {
      method: 'POST',
      body: JSON.stringify(elementData)
    });

    console.log('API Service - Created world element:', response);
    return response;
  } catch (error) {
    console.error('API Service - Error creating world element:', error);
    console.error('API Service - Error details:', error.response?.data || 'No response data');
    throw error;
  }
};

/**
 * Fetches all world elements for a book, optionally filtered by category
 * @param {string} bookId - Book identifier
 * @param {string} [category] - Optional category filter
 * @returns {Promise<Array>} List of world elements
 */
export const fetchWorldElements = async (bookId, category = null) => {
  try {
    console.log('API Service - Fetching world elements:', { bookId, category });

    let url = `${BASE_URL}/books/${bookId}/world-building/elements`;
    if (category) {
      url += `?category=${encodeURIComponent(category)}`;
    }

    const response = await apiRequest(url);

    console.log('API Service - Fetched world elements:', response);
    return response;
  } catch (error) {
    console.error('Error fetching world elements:', error);
    throw error;
  }
};

/**
 * Fetches a specific world element
 * @param {string} bookId - Book identifier
 * @param {string} elementId - Element identifier
 * @returns {Promise<Object>} Element data
 */
export const fetchWorldElement = async (bookId, elementId) => {
  try {
    console.log('API Service - Fetching world element:', { bookId, elementId });

    const response = await apiRequest(`${BASE_URL}/books/${bookId}/world-building/elements/${elementId}`);

    console.log('API Service - Fetched world element:', response);
    return response;
  } catch (error) {
    console.error('Error fetching world element:', error);
    throw error;
  }
};

/**
 * Fetches child elements for a specific parent element
 * @param {string} bookId - Book identifier
 * @param {string} parentId - Parent element identifier
 * @returns {Promise<Array>} List of child elements
 */
export const fetchChildElements = async (bookId, parentId) => {
  try {
    console.log('API Service - Fetching child elements:', { bookId, parentId });

    const response = await apiRequest(`${BASE_URL}/books/${bookId}/world-building/elements/${parentId}/children`);

    console.log('API Service - Fetched child elements:', response);
    return response;
  } catch (error) {
    console.error('Error fetching child elements:', error);
    throw error;
  }
};

/**
 * Updates an existing world element
 * @param {string} bookId - Book identifier
 * @param {string} elementId - Element identifier
 * @param {Object} elementData - Updated element data
 * @returns {Promise<Object>} Updated element
 */
export const updateWorldElement = async (bookId, elementId, elementData) => {
  try {
    console.log('API Service - Updating world element:', { bookId, elementId, elementData });

    const response = await apiRequest(`${BASE_URL}/books/${bookId}/world-building/elements/${elementId}`, {
      method: 'PUT',
      body: JSON.stringify(elementData)
    });

    console.log('API Service - Updated world element:', response);
    return response;
  } catch (error) {
    console.error('Error updating world element:', error);
    throw error;
  }
};

/**
 * Deletes a world element
 * @param {string} bookId - Book identifier
 * @param {string} elementId - Element identifier
 * @returns {Promise<Object>} Response data
 */
export const deleteWorldElement = async (bookId, elementId) => {
  try {
    console.log('API Service - Deleting world element:', { bookId, elementId });

    const response = await apiRequest(`${BASE_URL}/books/${bookId}/world-building/elements/${elementId}`, {
      method: 'DELETE'
    });

    console.log('API Service - Deleted world element:', response);
    return response;
  } catch (error) {
    console.error('Error deleting world element:', error);
    throw error;
  }
};

// AI Integration Functions

/**
 * Generates world elements using AI
 * @param {string} bookId - Book identifier
 * @param {string} categoryId - Category identifier
 * @param {string} prompt - Generation prompt
 * @param {number} count - Number of elements to generate (1-20)
 * @param {string} parentId - Optional parent element ID for hierarchical elements
 * @param {boolean} specialMode - Whether to generate in nested or multi-level mode
 * @param {number} subElementCount - Number of sub-elements for nested mode
 * @param {number} nestingDepth - Depth of hierarchy for multi-level mode
 * @param {Array} levelCounts - Array of counts for each level in multi-level mode
 * @returns {Promise<Array|Object>} Generated world elements or nested structure
 */
export const generateWorldElements = async (
  bookId,
  categoryId,
  prompt,
  count = 1,
  parentId = null,
  specialMode = false,
  subElementCount = 3,
  nestingDepth = 1,
  levelCounts = [3, 2, 2]
) => {
  try {
    const validCount = Math.min(Math.max(1, count), 20); // Ensure count is between 1 and 20

    // Determine if this is nested mode or multi-level mode based on the specialMode flag
    const isNestedMode = specialMode && prompt.includes('hierarchical structure');
    const isMultiLevelMode = specialMode && !isNestedMode;

    console.log('Generating world elements with AI:', {
      bookId,
      categoryId,
      prompt,
      count: validCount,
      parentId: parentId || 'none',
      specialMode,
      isNestedMode,
      isMultiLevelMode,
      subElementCount,
      nestingDepth,
      levelCounts
    });

    // Create a clean prompt without template instructions
    const cleanPrompt = prompt.replace(/Generate a complete hierarchical structure consisting of.*?(?=\n\n)/s, '')
                             .replace(/Return the results as a JSON.*?(?=\n\n)/s, '')
                             .trim();

    const requestBody = {
      category_id: categoryId,
      prompt: cleanPrompt,
      count: validCount
    };

    // Add parent_id to request if provided
    if (parentId) {
      requestBody.parent_id = parentId;
    }

    // Add mode flags and additional parameters
    if (isNestedMode) {
      requestBody.nested_mode = true;
      requestBody.sub_element_count = subElementCount;
    }

    if (isMultiLevelMode) {
      requestBody.multi_level_mode = true;
      requestBody.nesting_depth = nestingDepth;
      requestBody.level_counts = levelCounts;
    }

    const response = await apiRequest(`${BASE_URL}/books/${bookId}/world-building/generate`, {
      method: 'POST',
      body: JSON.stringify(requestBody)
    }, {
      retry: true
    });

    console.log('Raw response from backend:', response);

    // Handle various response formats
    // Check if we have a nested structure inside elements array
    if (response.elements &&
        Array.isArray(response.elements) &&
        response.elements[0] &&
        response.elements[0].parent &&
        response.elements[0].sub_elements) {

      console.log('Detected nested structure inside elements array');

      // Extract the parent and sub_elements
      const { parent, sub_elements } = response.elements[0];

      // If we're in nested mode, return the expected format
      if (isNestedMode) {
        return {
          parent,
          sub_elements
        };
      }

      // If we're in multi-level mode, convert to the expected format
      if (isMultiLevelMode) {
        return {
          elements: [{
            ...parent,
            children: sub_elements
          }]
        };
      }

      // For standard mode, flatten the structure
      return [parent, ...sub_elements];
    }

    // For multi-level mode, we might get a different response structure
    if (isMultiLevelMode) {
      console.log('Generated multi-level structure:', response);

      // If the backend doesn't support multi-level mode yet, simulate it on the frontend
      if (!response.elements && Array.isArray(response) && response.length > 0) {
        console.log('Backend does not support multi-level mode, simulating on frontend');

        // Create a simulated hierarchical structure
        const simulatedElements = [];

        // Take the first element as the top-level parent
        const topLevelElements = response.slice(0, Math.min(2, response.length));

        // For each top-level element, create some children
        topLevelElements.forEach(topElement => {
          const children = [];

          // Create 2 children for each top-level element
          for (let i = 0; i < 2; i++) {
            const childName = `Sub-element ${i+1} of ${topElement.name}`;
            const childDescription = `A component of ${topElement.name}`;

            children.push({
              name: childName,
              description: childDescription,
              element_type: topElement.element_type
            });
          }

          // Add the top-level element with its children
          simulatedElements.push({
            ...topElement,
            children
          });
        });

        return {
          elements: simulatedElements
        };
      }
    }
    // For nested mode, we might get a different response structure
    else if (isNestedMode) {
      console.log('Generated nested structure:', response);

      // If the backend doesn't support nested mode yet, simulate it on the frontend
      if (!response.parent && Array.isArray(response) && response.length > 0) {
        console.log('Backend does not support nested mode, simulating on frontend');

        // Take the first element as the parent
        const parent = response[0];

        // Create sub-elements from the remaining elements or generate placeholder sub-elements
        const subElements = response.length > 1
          ? response.slice(1)
          : [
              {
                name: `Sub-element of ${parent.name}`,
                description: `A component of ${parent.name}`,
                element_type: parent.element_type
              }
            ];

        return {
          parent,
          sub_elements: subElements
        };
      }
    }

    console.log(`Generated ${Array.isArray(response) ? response.length : 1} world elements:`, response);
    return response;
  } catch (error) {
    console.error('Error generating world elements:', error);
    throw error;
  }
};

/**
 * Enhances a world element description using AI
 * @param {string} bookId - Book identifier
 * @param {string} elementId - Element identifier
 * @param {string} prompt - Enhancement prompt
 * @returns {Promise<Object>} Enhanced world element
 */
export const enhanceWorldElement = async (bookId, elementId, prompt) => {
  try {
    console.log('Enhancing world element with AI:', { bookId, elementId, prompt });

    const response = await apiRequest(`${BASE_URL}/books/${bookId}/world-building/elements/${elementId}/enhance`, {
      method: 'POST',
      body: JSON.stringify({ prompt })
    }, {
      retry: true
    });

    console.log('Enhanced world element:', response);
    return response;
  } catch (error) {
    console.error('Error enhancing world element:', error);
    throw error;
  }
};

/**
 * Suggests relationships between world elements using AI
 * @param {string} bookId - Book identifier
 * @param {Array} elementIds - Array of element identifiers
 * @returns {Promise<Array>} Suggested relationships
 */
export const suggestRelationships = async (bookId, elementIds) => {
  try {
    console.log('Suggesting relationships with AI:', { bookId, elementIds });

    const response = await apiRequest(`${BASE_URL}/books/${bookId}/world-building/relationships/suggest`, {
      method: 'POST',
      body: JSON.stringify({ element_ids: elementIds })
    }, {
      retry: true
    });

    console.log('Suggested relationships:', response);
    return response;
  } catch (error) {
    console.error('Error suggesting relationships:', error);
    throw error;
  }
};

/**
 * Analyzes world consistency using AI
 * @param {string} bookId - Book identifier
 * @returns {Promise<Object>} Consistency analysis
 */
export const analyzeWorldConsistency = async (bookId) => {
  try {
    console.log('Analyzing world consistency with AI:', { bookId });

    const response = await apiRequest(`${BASE_URL}/books/${bookId}/world-building/analyze`, {
      method: 'POST'
    }, {
      retry: true
    });

    console.log('World consistency analysis:', response);
    return response;
  } catch (error) {
    console.error('Error analyzing world consistency:', error);
    throw error;
  }
};

/**
 * Fetches all element templates from the database
 * @returns {Promise<Array>} List of element templates
 */
export const fetchElementTemplates = async () => {
  try {
    console.log('API Service - Fetching element templates');

    const response = await apiRequest(`${BASE_URL}/api/templates`);

    console.log('API Service - Fetched element templates:', response);
    return response;
  } catch (error) {
    console.error('Error fetching element templates:', error);
    throw error;
  }
};

/**
 * Generates a map using AI
 * @param {string} bookId - Book identifier
 * @param {string} prompt - Map generation prompt
 * @returns {Promise<Object>} Generated map
 */
export const generateMap = async (bookId, prompt) => {
  try {
    console.log('Generating map with AI:', { bookId, prompt });

    const response = await apiRequest(`${BASE_URL}/books/${bookId}/world-building/maps/generate`, {
      method: 'POST',
      body: JSON.stringify({ prompt })
    }, {
      retry: true
    });

    console.log('Generated map:', response);
    return response;
  } catch (error) {
    console.error('Error generating map:', error);
    throw error;
  }
};

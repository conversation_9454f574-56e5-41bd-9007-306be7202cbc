// frontend/src/App.js
import React, { useEffect, useCallback, useRef } from 'react';
import ThemeProvider from './components/ThemeProvider';
import { BrowserRouter as Router, Routes, Route, Navigate, useLocation, useNavigate } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { selectCurrentBook, fetchBookById, selectAllBooks, fetchAllBooks, selectBook } from './redux/slices/bookSlice';
import { cleanupOldDeletedCardIds } from './redux/slices/brainstormSlice';
import { selectCurrentChapter } from './redux/slices/writeSlice';
import { selectIsAuthenticated, selectAuthToken, logout as logoutAction } from './redux/slices/authSlice';
import Login from './components/Login';
import RegisterPage from './components/RegisterPage';
import TitleBar from './components/TitleBar';
import ButtonCircle from './components/ButtonCircle';
// Old import removed: BrainstormPageContainer
import CharactersPageContainer from './containers/CharactersPageContainer';
import UserProfilePageContainer from './containers/UserProfilePageContainer';
import BooksPage from './components/BooksPage';
// WorldPageRedux has been replaced by WorldBuildingPageContainer
import WorldBuildingPageContainer from './containers/WorldBuildingPageContainer';
// PlotPageReduxContainer moved to old_files
import PlotPageDndKitContainer from './components/PlotPageDndKitContainer';
import BrainstormPageReduxContainer from './containers/BrainstormPageReduxContainer';
import DebugBooks from './components/DebugBooks';
import BookRequiredWrapper from './components/BookRequiredWrapper';
// Redux container components
import WritePageReduxContainer from './containers/WritePageReduxContainer';
// Admin Panel
import AdminPanelContainer from './containers/AdminPanelContainer';
// Removed AISidebarContainer import
import ErrorBoundary from './components/ErrorBoundary';
import './App.css';

function App() {
  const isAuthenticated = useSelector(selectIsAuthenticated);

  // For debugging
  useEffect(() => {
    console.debug('App: Auth state changed', {
      reduxAuthenticated: isAuthenticated
    });
  }, [isAuthenticated]);

  // Use isAuthenticated to determine if user is logged in
  const isLoggedIn = isAuthenticated;

  return (
    <ThemeProvider>
      <ErrorBoundary>
        {!isLoggedIn ? (
          <Routes>
            <Route path="/login" element={<Login />} />
            <Route path="/register" element={<RegisterPage />} />
            <Route path="*" element={<Navigate to="/login" replace />} />
          </Routes>
        ) : (
          <AppContent />
        )}
      </ErrorBoundary>
    </ThemeProvider>
  );
}

function AppContent() {
  const token = useSelector(selectAuthToken);
  const navigate = useNavigate();
  const dispatch = useDispatch();

  // Handle logout with proper navigation
  const handleLogout = useCallback(() => {
    console.debug('AppContent: Handling logout');
    // Dispatch the Redux logout action
    dispatch(logoutAction())
      .then(() => {
        console.debug('Logout action completed, navigating to login');
        // Force clear any auth-related data
        localStorage.removeItem('token');
        // Then navigate to login page
        window.location.href = '/login'; // Use direct location change to force a full page reload
      })
      .catch(error => {
        console.error('Logout failed:', error);
        // Even if logout fails, try to force navigation
        localStorage.removeItem('token');
        window.location.href = '/login';
      });
  }, [dispatch]);

  // Select state from Redux store
  const selectedBook = useSelector(selectCurrentBook);
  const books = useSelector(selectAllBooks);
  const currentChapter = useSelector(selectCurrentChapter);

  // Use Redux selectors for state
  const location = useLocation();

  // Ref to track initialization
  const initialized = useRef(false);

  // Ref to track cleanup interval
  const cleanupIntervalRef = useRef(null);

  const currentPage = location.pathname.split('/')[1] || 'world';

  // Handle book switching
  const handleBookSwitch = useCallback(async (newBookId) => {
    const newBook = books.find(b => b.book_id === newBookId) || null;
    if (newBook) {
      localStorage.setItem('lastBookId', newBookId);
      dispatch(fetchBookById(newBookId));
    }
  }, [books, dispatch]);



  // REMOVED: Book selection logic is now handled by a dedicated middleware
  // This prevents multiple components from competing for book selection

  // Helper function to fetch all books and select the first one
  // Keeping this commented out for reference
  /*
  const fetchAllBooksAndSelect = () => {
    dispatch(fetchAllBooks())
      .then(resultAction => {
        console.log('App.js: Books fetch result:', resultAction);
        if (fetchAllBooks.fulfilled.match(resultAction)) {
          const booksData = resultAction.payload;
          console.log('App.js: Books fetched successfully:', booksData);

          if (booksData && booksData.length > 0) {
            const lastBookId = localStorage.getItem('lastBookId');
            const book = booksData.find(b => b.book_id === lastBookId) || booksData[0];

            console.log('App.js: Setting selected book to:', book);
            dispatch(fetchBookById(book.book_id));
          } else {
            console.log('App.js: No books found');
          }
        } else {
          console.error('App.js: Books fetch failed:', resultAction.error);
        }
      })
      .catch(error => {
        console.error('App.js: Error fetching books:', error);
        dispatch(logoutAction());
      });
  };*/

  // Set up periodic cleanup of deleted card IDs
  useEffect(() => {
    if (token) {
      // Clean up old deleted card IDs on app start
      dispatch(cleanupOldDeletedCardIds());

      // Set up interval to clean up old deleted card IDs every hour
      cleanupIntervalRef.current = setInterval(() => {
        console.log('App.js: Running scheduled cleanup of old deleted card IDs');
        dispatch(cleanupOldDeletedCardIds());
      }, 60 * 60 * 1000); // 1 hour

      return () => {
        // Clear the interval when component unmounts
        if (cleanupIntervalRef.current) {
          clearInterval(cleanupIntervalRef.current);
          cleanupIntervalRef.current = null;
        }
      };
    }
  }, [token, dispatch]);

  // Initialize data when component mounts
  // This is the ONLY place that should fetch books on app initialization
  useEffect(() => {
    // Check if user is authenticated before fetching books
    if (token && !initialized.current) {
      initialized.current = true;
      console.log('App.js: Initializing book data');

      // Fetch all books and select the last used book or the first one
      console.log('App.js: Fetching all books');
      dispatch(fetchAllBooks())
        .then(resultAction => {
          if (fetchAllBooks.fulfilled.match(resultAction)) {
            const booksData = resultAction.payload;
            console.log('App.js: Books fetched successfully:', {
              count: booksData?.length,
              books: booksData
            });

            if (booksData && booksData.length > 0) {
              const lastBookId = localStorage.getItem('lastBookId');
              console.log('App.js: Last selected book ID from localStorage:', lastBookId);

              // Find the book with the last selected ID or use the first book
              const book = booksData.find(b => b.book_id === lastBookId) || booksData[0];

              console.log('App.js: Setting selected book to:', {
                book_id: book.book_id,
                title: book.title,
                isLastSelected: book.book_id === lastBookId
              });

              // Save the selected book ID to localStorage
              localStorage.setItem('lastBookId', book.book_id);
              console.log('App.js: Saved book ID to localStorage:', book.book_id);

              // Dispatch the selectBook action
              dispatch(selectBook(book));

              // Also fetch the book details to ensure we have the latest data
              console.log('App.js: Fetching book details for:', book.book_id);
              dispatch(fetchBookById(book.book_id))
                .then(() => {
                  console.log('App.js: Book details fetched successfully');
                })
                .catch(error => {
                  console.error('App.js: Error fetching book details:', error);
                });
            } else {
              console.log('App.js: No books found');
            }
          } else {
            console.error('App.js: Books fetch failed:', resultAction.error);
          }
        })
        .catch(error => {
          console.error('App.js: Error fetching books:', error);
        });
    }

    // Cleanup function
    return () => {
      console.debug('AppContent unmounting');
    };
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [token, dispatch]); // Only depend on these to prevent re-runs

  // These functions are now handled by Redux actions in their respective containers

  // This function is now handled in BrainstormPageContainer
  // const addBrainstormPage = useCallback(() => {
  //   const newPageId = `page${Object.keys(brainstormData.pages).length + 1}`;
  //   return newPageId;
  // }, [brainstormData]);

  console.debug('AppContent rendered with:', {
    token: token ? 'present' : 'missing',
    selectedBook: selectedBook?.book_id,
    books: books?.length || 0
  });

  return (
    <div className="App">
      <div className="main-container">
        <div className="header">
          <TitleBar
            currentPage={currentPage}
            currentChapter={currentChapter}
            onBookSelect={handleBookSwitch}
          />
          <div className="buttons">
            <ButtonCircle label="W" onClick={() => navigate('/write')} />
            <ButtonCircle label="Br" onClick={() => navigate('/brainstorm/page1')} />
            <ButtonCircle label="Pl" onClick={() => navigate('/plot')} />
            {/* World button now redirects to world-building */}
            <ButtonCircle label="Wo" onClick={() => navigate('/world-building')} />
            <ButtonCircle label="C" onClick={() => navigate('/characters')} />
            <ButtonCircle label="Bo" onClick={() => navigate('/books')} />
            <ButtonCircle label="U" onClick={() => navigate('/profile')} />
            <ButtonCircle label="A" onClick={() => navigate('/admin')} />
            <button
              onClick={handleLogout}
              className="logout-button"
              style={{ padding: '5px 10px' }}
            >
              Logout
            </button>
          </div>
        </div>
        <div className="content">
          <Routes>
            <Route path="/" element={<Navigate to="/world-building" />} />
            {/* Redirect /world to /world-building */}
            <Route path="/world" element={<Navigate to="/world-building" replace />} />
            <Route path="/world-building" element={<BookRequiredWrapper><WorldBuildingPageContainer /></BookRequiredWrapper>} />
            <Route path="/brainstorm" element={<Navigate to="/brainstorm/page1" />} />
            <Route path="/brainstorm/:pageId" element={
              <BookRequiredWrapper>
                {selectedBook && (
                  <BrainstormPageReduxContainer key={`brainstorm-${selectedBook.book_id}`} />
                )}
              </BookRequiredWrapper>
            } />
            <Route path="/plot" element={<BookRequiredWrapper><PlotPageDndKitContainer /></BookRequiredWrapper>} />
            <Route path="/write" element={<BookRequiredWrapper><WritePageReduxContainer /></BookRequiredWrapper>} />
            <Route path="/characters" element={<BookRequiredWrapper><CharactersPageContainer /></BookRequiredWrapper>} />
            <Route path="/profile" element={<UserProfilePageContainer />} />
            <Route path="/books" element={<BooksPage />} />
            <Route path="/books/:bookId" element={<BooksPage />} />
            <Route path="/debug" element={<DebugBooks />} />
            <Route path="/admin" element={<AdminPanelContainer />} />
            <Route path="/admin/:tabId" element={<AdminPanelContainer />} />
            <Route path="/login" element={<Login />} />
            <Route path="/register" element={<RegisterPage />} />
            <Route path="*" element={<Navigate to="/world-building" />} />
          </Routes>
        </div>
        {/* AI Sidebar removed */}
      </div>
    </div>
  );
}

export default App;

// src/redux/middleware/loggerMiddleware.js

/**
 * Middleware for logging Redux actions and state changes
 * Only active in development mode
 */
const loggerMiddleware = store => next => action => {
  if (process.env.NODE_ENV !== 'production') {
    console.group(`%c Action: ${action.type}`, 'color: #4CAF50; font-weight: bold');
    console.log('%c Previous State', 'color: #9E9E9E; font-weight: bold', store.getState());
    console.log('%c Action', 'color: #03A9F4; font-weight: bold', action);
    
    // Call the next dispatch method in the middleware chain
    const result = next(action);
    
    console.log('%c Next State', 'color: #4CAF50; font-weight: bold', store.getState());
    console.groupEnd();
    
    return result;
  }
  
  return next(action);
};

export default loggerMiddleware;

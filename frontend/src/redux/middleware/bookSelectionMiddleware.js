// src/redux/middleware/bookSelectionMiddleware.js

/**
 * Middleware for handling book selection in a centralized way
 * This prevents multiple components from competing for book selection
 * and causing infinite API calls
 */
const bookSelectionMiddleware = store => next => action => {
  // Check if this is a book selection action
  if (action.type === 'books/selectBook') {
    // Get the current selected book before the action is processed
    const prevState = store.getState();
    const prevSelectedBook = prevState.books.selectedBook;

    // Process the action
    const result = next(action);

    // Get the new selected book after the action is processed
    const newSelectedBook = action.payload;

    // If the book has changed, reset state in other slices
    if (newSelectedBook && (!prevSelectedBook || prevSelectedBook.book_id !== newSelectedBook.book_id)) {
      console.log('bookSelectionMiddleware: Book changed from',
        prevSelectedBook?.book_id || 'none', 'to', newSelectedBook.book_id);

      // Import the reset actions from other slices
      const { resetBrainstormState } = require('../slices/brainstormSlice');
      // World slice has been replaced by worldBuildingSlice
      const { setCurrentBookId } = require('../slices/worldBuildingSlice');

      // Reset state in other slices
      console.log('bookSelectionMiddleware: Resetting brainstorm state for new book');
      store.dispatch(resetBrainstormState());

      // Set current book ID in worldBuildingSlice
      console.log('bookSelectionMiddleware: Setting current book ID in worldBuildingSlice');
      store.dispatch(setCurrentBookId(newSelectedBook.book_id));
    }

    return result;
  }

  // For other actions, just call next and continue
  const result = next(action);

  // After state update, check if we need to select a book
  if (action.type === 'books/fetchAll/fulfilled') {
    const state = store.getState();
    const { books, selectedBook } = state.books;

    // If we have books but no selected book, select one
    if (books && books.length > 0 && !selectedBook) {
      console.log('bookSelectionMiddleware: Books exist but no book is selected, selecting a book');

      // Try to get the last selected book from localStorage
      const lastBookId = localStorage.getItem('lastBookId');
      const bookToSelect = lastBookId ?
        books.find(b => b.book_id === lastBookId) :
        books[0];

      if (bookToSelect) {
        console.log('bookSelectionMiddleware: Selecting book:', bookToSelect.title || bookToSelect.book_id);

        // Import the selectBook action
        const { selectBook, fetchBookById } = require('../slices/bookSlice');

        // Dispatch the selectBook action
        store.dispatch(selectBook(bookToSelect));

        // Update the currentBookId in the worldBuildingSlice
        const { setCurrentBookId } = require('../slices/worldBuildingSlice');
        store.dispatch(setCurrentBookId(bookToSelect.book_id));

        // Also fetch the full book details to ensure we have complete data
        // This ensures we have author, genre, and description
        console.log('bookSelectionMiddleware: Fetching full book details for:', bookToSelect.book_id);
        store.dispatch(fetchBookById(bookToSelect.book_id));
      }
    }
  }

  return result;
};

export default bookSelectionMiddleware;

// src/redux/slices/writeSlice.js
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import {
  fetchChapterContent,
  saveChapterContentWithBookId,
  fetchChapters,
  reorderChaptersWithBookId,
  createChapterWithBookId
} from '../../services/apiService';

// Initial state
const initialState = {
  // Chapter data
  chapters: [],
  currentChapterId: null,
  chapterContent: '',

  // Editor state
  editorState: null,
  cursorPosition: { line: 0, column: 0 },
  selection: { start: 0, end: 0 },
  wordCount: 0,

  // UI state
  showReferencePanel: false,
  showEventSidebar: true,
  showChapterSidebar: true,

  // Status
  isLoading: false,
  isSaving: false,
  lastSaved: null,
  error: null
};

// Async thunks
export const fetchAllChapters = createAsyncThunk(
  'write/fetchChapters',
  async (bookId, { rejectWithValue }) => {
    try {
      console.log(`[DEBUG WriteSlice] Fetching chapters for book ${bookId}`);
      const response = await fetchChapters(bookId);
      console.log('[DEBUG WriteSlice] Chapters response:', response);

      // Ensure we're returning an array
      const chapters = Array.isArray(response) ? response : [];
      console.log('[DEBUG WriteSlice] Processed chapters:', chapters);

      return chapters;
    } catch (error) {
      console.error('[DEBUG WriteSlice] Error fetching chapters:', error);
      return rejectWithValue(error.message || 'Failed to fetch chapters');
    }
  }
);

export const fetchCurrentChapterContent = createAsyncThunk(
  'write/fetchChapterContent',
  async ({ bookId, chapterId }, { rejectWithValue, getState }) => {
    try {
      console.log(`[DEBUG WriteSlice] Fetching content for chapter ${chapterId} in book ${bookId}`);

      // Try to get content from localStorage first for immediate display
      let localContent = null;
      try {
        const backupKey = `chapter_content_${bookId}_${chapterId}`;
        localContent = localStorage.getItem(backupKey);
        console.log('[DEBUG WriteSlice] Found content in localStorage:', localContent ? 'yes' : 'no');
      } catch (e) {
        console.warn('[DEBUG WriteSlice] Error retrieving content from localStorage:', e);
      }

      // Fetch from server
      const response = await fetchChapterContent(bookId, chapterId);
      console.log('[DEBUG WriteSlice] Received response:', response);
      console.log('[DEBUG WriteSlice] Content type:', typeof response.content);
      console.log('[DEBUG WriteSlice] Content length:', response.content?.length || 0);

      // If server returned empty content but we have localStorage content, use that
      const serverContent = response.content || '';
      const finalContent = serverContent.length > 0 ? serverContent : (localContent || '');
      console.log('[DEBUG WriteSlice] Using content from:',
        serverContent.length > 0 ? 'server' : (localContent ? 'localStorage' : 'empty string'));

      return {
        chapterId,
        content: finalContent
      };
    } catch (error) {
      console.error('[DEBUG WriteSlice] Error fetching chapter content:', error);

      // Try to recover from localStorage if server fetch fails
      try {
        const backupKey = `chapter_content_${bookId}_${chapterId}`;
        const localContent = localStorage.getItem(backupKey);

        if (localContent) {
          console.log('[DEBUG WriteSlice] Recovered content from localStorage after server error');
          return {
            chapterId,
            content: localContent,
            fromLocalStorage: true
          };
        }
      } catch (e) {
        console.warn('[DEBUG WriteSlice] Error recovering from localStorage:', e);
      }

      return rejectWithValue(error.message || 'Failed to fetch chapter content');
    }
  }
);

export const saveCurrentChapterContent = createAsyncThunk(
  'write/saveChapterContent',
  async ({ bookId, chapterId, content }, { rejectWithValue }) => {
    try {
      console.log(`[DEBUG WriteSlice] Saving content for chapter ${chapterId} in book ${bookId}`);
      console.log('[DEBUG WriteSlice] Content type:', typeof content);
      console.log('[DEBUG WriteSlice] Content length:', content?.length || 0);

      // Save to localStorage as a backup
      try {
        const backupKey = `chapter_content_${bookId}_${chapterId}`;
        localStorage.setItem(backupKey, content);
        console.log('[DEBUG WriteSlice] Content backed up to localStorage');
      } catch (e) {
        console.warn('[DEBUG WriteSlice] Failed to backup content to localStorage:', e);
      }

      // Save to server
      await saveChapterContentWithBookId(bookId, chapterId, content);
      console.log('[DEBUG WriteSlice] Content saved successfully to server');

      return {
        chapterId,
        content,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('[DEBUG WriteSlice] Error saving chapter content to server:', error);

      // Even if server save fails, we still have the localStorage backup
      try {
        const backupKey = `chapter_content_${bookId}_${chapterId}`;
        localStorage.setItem(backupKey, content);
        console.log('[DEBUG WriteSlice] Content backed up to localStorage after server error');
      } catch (e) {
        console.warn('[DEBUG WriteSlice] Failed to backup content to localStorage after server error:', e);
      }

      return rejectWithValue(error.message || 'Failed to save chapter content');
    }
  }
);

// Reorder chapters
export const reorderChapters = createAsyncThunk(
  'write/reorderChapters',
  async ({ bookId, chapters }, { rejectWithValue }) => {
    try {
      console.log(`[DEBUG WriteSlice] Reordering chapters for book ${bookId}`);
      console.log('[DEBUG WriteSlice] Chapters to reorder:', chapters);

      const response = await reorderChaptersWithBookId(bookId, chapters);
      console.log('[DEBUG WriteSlice] Chapters reordered successfully:', response);

      return chapters;
    } catch (error) {
      console.error('[DEBUG WriteSlice] Error reordering chapters:', error);
      return rejectWithValue(error.message || 'Failed to reorder chapters');
    }
  }
);

// Add a new chapter
export const addChapter = createAsyncThunk(
  'write/addChapter',
  async ({ bookId, title }, { rejectWithValue }) => {
    try {
      console.log(`[DEBUG WriteSlice] Adding new chapter to book ${bookId}`);
      console.log('[DEBUG WriteSlice] Chapter title:', title);

      const response = await createChapterWithBookId(bookId, title);
      console.log('[DEBUG WriteSlice] Chapter added successfully:', response);

      return response;
    } catch (error) {
      console.error('[DEBUG WriteSlice] Error adding chapter:', error);
      return rejectWithValue(error.message || 'Failed to add chapter');
    }
  }
);

// Slice
const writeSlice = createSlice({
  name: 'write',
  initialState,
  reducers: {
    // Set current chapter
    setCurrentChapter: (state, action) => {
      state.currentChapterId = action.payload;
    },

    // Update editor state
    updateEditorState: (state, action) => {
      console.log('[DEBUG WriteSlice Reducer] updateEditorState called');
      console.log('[DEBUG WriteSlice Reducer] Content type:', typeof action.payload);
      console.log('[DEBUG WriteSlice Reducer] Content length:', action.payload?.length || 0);

      // Validate content
      let validContent = action.payload;

      if (typeof action.payload === 'string') {
        try {
          // Try to parse as JSON to validate
          JSON.parse(action.payload);
          console.log('[DEBUG WriteSlice Reducer] Content is valid JSON');
        } catch (e) {
          console.error('[DEBUG WriteSlice Reducer] Content is not valid JSON, creating empty content');
          // Create empty Draft.js content
          const emptyContent = JSON.stringify({
            blocks: [
              {
                key: 'empty',
                text: '',
                type: 'unstyled',
                depth: 0,
                inlineStyleRanges: [],
                entityRanges: [],
                data: {}
              }
            ],
            entityMap: {}
          });
          validContent = emptyContent;
        }
      }

      state.editorState = validContent;
      console.log('[DEBUG WriteSlice Reducer] Editor state updated');
    },

    // Update cursor position
    updateCursorPosition: (state, action) => {
      state.cursorPosition = action.payload;
    },

    // Update selection
    updateSelection: (state, action) => {
      state.selection = action.payload;
    },

    // Update word count
    updateWordCount: (state, action) => {
      state.wordCount = action.payload;
    },

    // Toggle UI elements
    toggleReferencePanel: (state) => {
      state.showReferencePanel = !state.showReferencePanel;
    },

    toggleEventSidebar: (state) => {
      state.showEventSidebar = !state.showEventSidebar;
    },

    toggleChapterSidebar: (state) => {
      state.showChapterSidebar = !state.showChapterSidebar;
    },

    // Clear error
    clearError: (state) => {
      state.error = null;
    }
  },
  extraReducers: (builder) => {
    builder
      // Fetch chapters
      .addCase(fetchAllChapters.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchAllChapters.fulfilled, (state, action) => {
        console.log('[DEBUG WriteSlice Reducer] fetchAllChapters.fulfilled');
        console.log('[DEBUG WriteSlice Reducer] Chapters payload:', action.payload);

        state.isLoading = false;

        // Ensure chapters is always an array
        state.chapters = Array.isArray(action.payload) ? action.payload : [];
        console.log('[DEBUG WriteSlice Reducer] Chapters state after update:', state.chapters);

        // If no current chapter is selected, select the first one
        if (!state.currentChapterId && state.chapters.length > 0) {
          // Use chapter_id or id, depending on what's available
          const firstChapterId = state.chapters[0].chapter_id || state.chapters[0].id;
          state.currentChapterId = firstChapterId;
          console.log('[DEBUG WriteSlice Reducer] Selected first chapter:', state.currentChapterId);
        }
      })
      .addCase(fetchAllChapters.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })

      // Fetch chapter content
      .addCase(fetchCurrentChapterContent.pending, (state) => {
        console.log('[DEBUG WriteSlice Reducer] fetchCurrentChapterContent.pending');
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchCurrentChapterContent.fulfilled, (state, action) => {
        console.log('[DEBUG WriteSlice Reducer] fetchCurrentChapterContent.fulfilled');
        console.log('[DEBUG WriteSlice Reducer] Content type:', typeof action.payload.content);
        console.log('[DEBUG WriteSlice Reducer] Content length:', action.payload.content?.length || 0);
        console.log('[DEBUG WriteSlice Reducer] From localStorage:', action.payload.fromLocalStorage || false);

        state.isLoading = false;
        state.currentChapterId = action.payload.chapterId;

        // Only update the content if it's not empty or if we don't already have content
        if (action.payload.content && action.payload.content.length > 0) {
          state.chapterContent = action.payload.content;
          console.log('[DEBUG WriteSlice Reducer] State updated with new content');
        } else if (!state.chapterContent) {
          state.chapterContent = '';
          console.log('[DEBUG WriteSlice Reducer] State updated with empty content');
        } else {
          console.log('[DEBUG WriteSlice Reducer] Keeping existing content');
        }
      })
      .addCase(fetchCurrentChapterContent.rejected, (state, action) => {
        console.log('[DEBUG WriteSlice Reducer] fetchCurrentChapterContent.rejected');
        console.error('[DEBUG WriteSlice Reducer] Error:', action.payload);

        state.isLoading = false;
        state.error = action.payload;
      })

      // Save chapter content
      .addCase(saveCurrentChapterContent.pending, (state) => {
        console.log('[DEBUG WriteSlice Reducer] saveCurrentChapterContent.pending');
        state.isSaving = true;
        state.error = null;
      })
      .addCase(saveCurrentChapterContent.fulfilled, (state, action) => {
        console.log('[DEBUG WriteSlice Reducer] saveCurrentChapterContent.fulfilled');
        console.log('[DEBUG WriteSlice Reducer] Timestamp:', action.payload.timestamp);

        state.isSaving = false;
        state.lastSaved = action.payload.timestamp;

        console.log('[DEBUG WriteSlice Reducer] Content saved successfully');
      })
      .addCase(saveCurrentChapterContent.rejected, (state, action) => {
        console.log('[DEBUG WriteSlice Reducer] saveCurrentChapterContent.rejected');
        console.error('[DEBUG WriteSlice Reducer] Error:', action.payload);

        state.isSaving = false;
        state.error = action.payload;
      })

      // Reorder chapters
      .addCase(reorderChapters.pending, (state) => {
        console.log('[DEBUG WriteSlice Reducer] reorderChapters.pending');
        state.isLoading = true;
        state.error = null;
      })
      .addCase(reorderChapters.fulfilled, (state, action) => {
        console.log('[DEBUG WriteSlice Reducer] reorderChapters.fulfilled');

        state.isLoading = false;
        // Update the chapters array with the new order
        state.chapters = action.payload;

        console.log('[DEBUG WriteSlice Reducer] Chapters reordered successfully');
      })
      .addCase(reorderChapters.rejected, (state, action) => {
        console.log('[DEBUG WriteSlice Reducer] reorderChapters.rejected');
        console.error('[DEBUG WriteSlice Reducer] Error:', action.payload);

        state.isLoading = false;
        state.error = action.payload;
      })

      // Add chapter
      .addCase(addChapter.pending, (state) => {
        console.log('[DEBUG WriteSlice Reducer] addChapter.pending');
        state.isLoading = true;
        state.error = null;
      })
      .addCase(addChapter.fulfilled, (state, action) => {
        console.log('[DEBUG WriteSlice Reducer] addChapter.fulfilled');
        console.log('[DEBUG WriteSlice Reducer] New chapter:', action.payload);

        state.isLoading = false;
        // Add the new chapter to the chapters array
        state.chapters.push(action.payload);
        // Sort chapters by sequence number
        state.chapters.sort((a, b) => a.sequence_number - b.sequence_number);

        // Note: We don't automatically set the new chapter as the current chapter here
        // because that's now handled in the ChapterSidebarContainer

        // Clear the chapter content to ensure the new chapter starts empty
        state.chapterContent = '';

        console.log('[DEBUG WriteSlice Reducer] Chapter added successfully');
      })
      .addCase(addChapter.rejected, (state, action) => {
        console.log('[DEBUG WriteSlice Reducer] addChapter.rejected');
        console.error('[DEBUG WriteSlice Reducer] Error:', action.payload);

        state.isLoading = false;
        state.error = action.payload;
      });
  }
});

// Actions
export const {
  setCurrentChapter,
  updateEditorState,
  updateCursorPosition,
  updateSelection,
  updateWordCount,
  toggleReferencePanel,
  toggleEventSidebar,
  toggleChapterSidebar,
  clearError
} = writeSlice.actions;

// Selectors
export const selectChapters = (state) => state.write.chapters || [];
export const selectCurrentChapterId = (state) => state.write.currentChapterId;
export const selectChapterContent = (state) => state.write.chapterContent;
export const selectEditorState = (state) => state.write.editorState;
export const selectCursorPosition = (state) => state.write.cursorPosition;
export const selectSelection = (state) => state.write.selection;
export const selectWordCount = (state) => state.write.wordCount;
export const selectShowReferencePanel = (state) => state.write.showReferencePanel;
export const selectShowEventSidebar = (state) => state.write.showEventSidebar;
export const selectShowChapterSidebar = (state) => state.write.showChapterSidebar;
export const selectIsLoading = (state) => state.write.isLoading;
export const selectIsSaving = (state) => state.write.isSaving;
export const selectLastSaved = (state) => state.write.lastSaved;
export const selectError = (state) => state.write.error;

// Find chapter by ID
export const selectChapterById = (state, chapterId) => {
  const chapters = state.write.chapters || [];
  return chapters.find(chapter => chapter.id === chapterId);
};

// Current chapter
export const selectCurrentChapter = (state) => {
  // Ensure chapters is always an array
  const chapters = Array.isArray(state.write.chapters) ? state.write.chapters : [];
  console.log('[DEBUG] selectCurrentChapter - chapters:', chapters);
  console.log('[DEBUG] selectCurrentChapter - currentChapterId:', state.write.currentChapterId);

  if (!state.write.currentChapterId) {
    console.log('[DEBUG] selectCurrentChapter - No current chapter ID');
    return null;
  }

  // Find the chapter with the matching ID
  const chapter = chapters.find(chapter =>
    chapter &&
    (chapter.id === state.write.currentChapterId || chapter.chapter_id === state.write.currentChapterId)
  );

  console.log('[DEBUG] selectCurrentChapter - Found chapter:', chapter);
  return chapter || null;
};

export default writeSlice.reducer;

// frontend/src/redux/slices/semanticSearchSlice.js
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { BASE_URL } from '../../utils/apiConfig';
import { getHeaders } from '../../services/apiService';

// Async thunks
export const searchTemplates = createAsyncThunk(
  'semanticSearch/searchTemplates',
  async ({ query, limit = 10, filters = null }, { rejectWithValue }) => {
    try {
      const response = await fetch(`${BASE_URL}/api/search/templates`, {
        method: 'POST',
        headers: getHeaders(),
        body: JSON.stringify({ query, limit, filters })
      });

      if (!response.ok) {
        const errorData = await response.json();
        return rejectWithValue(errorData.detail || 'Failed to search templates');
      }

      const data = await response.json();
      return data;
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to search templates');
    }
  }
);

export const hybridSearch = createAsyncThunk(
  'semanticSearch/hybridSearch',
  async ({ query, bookId, contentTypes = ['templates', 'elements', 'characters'], limit = 10, filters = null }, { rejectWithValue }) => {
    try {
      // Build the URL with query parameters for contentTypes
      const contentTypesParam = contentTypes.join(',');
      const url = `${BASE_URL}/api/search/hybrid?content_types=${contentTypesParam}`;

      const response = await fetch(url, {
        method: 'POST',
        headers: getHeaders(),
        body: JSON.stringify({ query, book_id: bookId, limit, filters })
      });

      if (!response.ok) {
        const errorData = await response.json();
        return rejectWithValue(errorData.detail || 'Failed to perform hybrid search');
      }

      const data = await response.json();
      return data;
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to perform hybrid search');
    }
  }
);

export const suggestTemplates = createAsyncThunk(
  'semanticSearch/suggestTemplates',
  async ({ content, limit = 5 }, { rejectWithValue }) => {
    try {
      const response = await fetch(`${BASE_URL}/api/search/suggest-templates`, {
        method: 'POST',
        headers: getHeaders(),
        body: JSON.stringify({ content, limit })
      });

      if (!response.ok) {
        const errorData = await response.json();
        return rejectWithValue(errorData.detail || 'Failed to suggest templates');
      }

      const data = await response.json();
      return data;
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to suggest templates');
    }
  }
);

// Initial state
const initialState = {
  templateSearchResults: [],
  hybridSearchResults: [],
  templateSuggestions: [],
  isLoading: false,
  error: null,
  lastQuery: '',
  searchHistory: []
};

// Slice
const semanticSearchSlice = createSlice({
  name: 'semanticSearch',
  initialState,
  reducers: {
    clearSearchResults: (state) => {
      state.templateSearchResults = [];
      state.hybridSearchResults = [];
      state.error = null;
    },
    clearTemplateSuggestions: (state) => {
      state.templateSuggestions = [];
      state.error = null;
    },
    addToSearchHistory: (state, action) => {
      // Add to history if not already present
      if (!state.searchHistory.includes(action.payload)) {
        state.searchHistory.unshift(action.payload);
        // Keep only the last 10 searches
        if (state.searchHistory.length > 10) {
          state.searchHistory.pop();
        }
      }
    },
    clearSearchHistory: (state) => {
      state.searchHistory = [];
    }
  },
  extraReducers: (builder) => {
    builder
      // searchTemplates
      .addCase(searchTemplates.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(searchTemplates.fulfilled, (state, action) => {
        state.isLoading = false;
        state.templateSearchResults = action.payload;
        state.lastQuery = action.meta.arg.query;
      })
      .addCase(searchTemplates.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload || 'Failed to search templates';
      })
      
      // hybridSearch
      .addCase(hybridSearch.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(hybridSearch.fulfilled, (state, action) => {
        state.isLoading = false;
        state.hybridSearchResults = action.payload;
        state.lastQuery = action.meta.arg.query;
      })
      .addCase(hybridSearch.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload || 'Failed to perform hybrid search';
      })
      
      // suggestTemplates
      .addCase(suggestTemplates.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(suggestTemplates.fulfilled, (state, action) => {
        state.isLoading = false;
        state.templateSuggestions = action.payload;
      })
      .addCase(suggestTemplates.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload || 'Failed to suggest templates';
      });
  }
});

// Export actions and reducer
export const { 
  clearSearchResults, 
  clearTemplateSuggestions, 
  addToSearchHistory, 
  clearSearchHistory 
} = semanticSearchSlice.actions;

export default semanticSearchSlice.reducer;

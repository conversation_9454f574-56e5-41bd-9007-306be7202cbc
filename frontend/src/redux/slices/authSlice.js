// src/redux/slices/authSlice.js
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { loginUser, logoutUser, registerUser, updateUserProfile } from '../../services/apiService';
import { safeGetItem, safeSetItem, safeRemoveItem } from '../../utils/localStorage';

// Initial state
const initialState = {
  token: safeGetItem('token', null),
  user: null,
  isAuthenticated: !!safeGetItem('token'),
  isLoading: false,
  error: null,
  settings: {
    theme: safeGetItem('theme', 'light'),
    fontSize: safeGetItem('fontSize', 'medium'),
    autoSave: safeGetItem('autoSave') !== 'false', // Default to true
    notifications: safeGetItem('notifications') !== 'false', // Default to true
  }
};

// Async thunks
export const login = createAsyncThunk(
  'auth/login',
  async ({ email, password }, { rejectWithValue }) => {
    try {
      const response = await loginUser(email, password);
      safeSetItem('token', response.access_token);
      return {
        token: response.access_token,
        user: response.user || { email }
      };
    } catch (error) {
      return rejectWithValue(error.message || 'Login failed');
    }
  }
);

export const logout = createAsyncThunk(
  'auth/logout',
  async (_, { rejectWithValue }) => {
    try {
      console.debug('Executing logout thunk');
      try {
        // Try to call the backend logout endpoint
        await logoutUser();
      } catch (apiError) {
        // If the backend endpoint fails, just log it but continue with client-side logout
        console.warn('Backend logout failed, continuing with client-side logout:', apiError.message);
      }

      // Always remove the token from localStorage
      safeRemoveItem('token');
      console.debug('Token removed from localStorage');
      return null;
    } catch (error) {
      console.error('Logout thunk error:', error);
      // Even if there's an error, we'll fulfill this action to ensure the user is logged out client-side
      safeRemoveItem('token');
      return null;
    }
  }
);

export const register = createAsyncThunk(
  'auth/register',
  async ({ email, password }, { rejectWithValue }) => {
    try {
      const response = await registerUser(email, password);

      // If registration includes a token, store it safely
      if (response && response.access_token) {
        safeSetItem('token', response.access_token);
      }

      return response;
    } catch (error) {
      return rejectWithValue(error.message || 'Registration failed');
    }
  }
);

export const updateProfile = createAsyncThunk(
  'auth/updateProfile',
  async (userData, { rejectWithValue }) => {
    try {
      const response = await updateUserProfile(userData);
      return response;
    } catch (error) {
      return rejectWithValue(error.message || 'Profile update failed');
    }
  }
);

export const updateSettings = createAsyncThunk(
  'auth/updateSettings',
  async (settings, { getState }) => {
    // Update settings in localStorage
    Object.entries(settings).forEach(([key, value]) => {
      localStorage.setItem(key, value.toString());
    });

    // Return the updated settings
    return settings;
  }
);

// Auth slice
const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    updateTheme: (state, action) => {
      state.settings.theme = action.payload;
      safeSetItem('theme', action.payload);
    },
    updateFontSize: (state, action) => {
      state.settings.fontSize = action.payload;
      safeSetItem('fontSize', action.payload);
    },
    updateAutoSave: (state, action) => {
      state.settings.autoSave = action.payload;
      safeSetItem('autoSave', action.payload.toString());
    },
    updateNotifications: (state, action) => {
      state.settings.notifications = action.payload;
      safeSetItem('notifications', action.payload.toString());
    }
  },
  extraReducers: (builder) => {
    builder
      // Login
      .addCase(login.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(login.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isAuthenticated = true;
        state.token = action.payload.token;
        state.user = action.payload.user;
      })
      .addCase(login.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })
      // Logout
      .addCase(logout.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(logout.fulfilled, (state) => {
        console.debug('Logout fulfilled, clearing auth state');
        state.isLoading = false;
        state.isAuthenticated = false;
        state.token = null;
        state.user = null;
        // Clear any other auth-related state
        state.error = null;
      })
      .addCase(logout.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })
      // Register
      .addCase(register.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(register.fulfilled, (state, action) => {
        state.isLoading = false;

        // If registration response includes a token, update auth state
        if (action.payload && action.payload.access_token) {
          state.token = action.payload.access_token;
          state.isAuthenticated = true;
          state.user = action.payload.user || { email: action.payload.email };
        }
      })
      .addCase(register.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })

      // Update Profile
      .addCase(updateProfile.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updateProfile.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = action.payload;
      })
      .addCase(updateProfile.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })

      // Update Settings
      .addCase(updateSettings.fulfilled, (state, action) => {
        // Update all settings at once
        state.settings = {
          ...state.settings,
          ...action.payload
        };
      });
  },
});

// Export actions and reducer
export const {
  clearError,
  updateTheme,
  updateFontSize,
  updateAutoSave,
  updateNotifications
} = authSlice.actions;
export default authSlice.reducer;

// Selectors
export const selectAuth = (state) => state.auth;
export const selectIsAuthenticated = (state) => state.auth.isAuthenticated;
export const selectAuthLoading = (state) => state.auth.isLoading;
export const selectAuthError = (state) => state.auth.error;
export const selectAuthToken = (state) => state.auth.token;
export const selectUser = (state) => state.auth.user;
export const selectSettings = (state) => state.auth.settings;
export const selectTheme = (state) => state.auth.settings.theme;
export const selectFontSize = (state) => state.auth.settings.fontSize;

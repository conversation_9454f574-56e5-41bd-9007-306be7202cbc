// src/redux/slices/referenceSlice.js
import { createSlice } from '@reduxjs/toolkit';

// Initial state
const initialState = {
  activeTab: 'characters', // 'characters' or 'world'
  searchTerm: '',
  recentlyUsed: [],
  filters: {
    characters: {
      role: null,
      age: null
    },
    world: {
      type: null,
      location: null
    }
  }
};

// Slice
const referenceSlice = createSlice({
  name: 'reference',
  initialState,
  reducers: {
    setActiveTab: (state, action) => {
      state.activeTab = action.payload;
    },

    setSearchTerm: (state, action) => {
      state.searchTerm = action.payload;
    },

    addToRecentlyUsed: (state, action) => {
      // Remove if already exists
      state.recentlyUsed = state.recentlyUsed.filter(item =>
        !(item.id === action.payload.id && item.type === action.payload.type)
      );

      // Add to the beginning
      state.recentlyUsed.unshift(action.payload);

      // Keep only the last 10 items
      if (state.recentlyUsed.length > 10) {
        state.recentlyUsed = state.recentlyUsed.slice(0, 10);
      }
    },

    setCharacterFilter: (state, action) => {
      state.filters.characters = {
        ...state.filters.characters,
        ...action.payload
      };
    },

    setWorldFilter: (state, action) => {
      state.filters.world = {
        ...state.filters.world,
        ...action.payload
      };
    },

    clearFilters: (state) => {
      state.filters = {
        characters: {
          role: null,
          age: null
        },
        world: {
          type: null,
          location: null
        }
      };
    }
  }
});

// Actions
export const {
  setActiveTab,
  setSearchTerm,
  addToRecentlyUsed,
  setCharacterFilter,
  setWorldFilter,
  clearFilters
} = referenceSlice.actions;

// Selectors
export const selectActiveTab = (state) => state.reference.activeTab;
export const selectSearchTerm = (state) => state.reference.searchTerm;
export const selectRecentlyUsed = (state) => state.reference.recentlyUsed;
export const selectCharacterFilters = (state) => state.reference.filters.characters;
export const selectWorldFilters = (state) => state.reference.filters.world;

// Filtered selectors that combine with other slices
export const selectFilteredCharacters = (state) => {
  const { characters } = state;
  const { searchTerm, filters } = state.reference;

  if (!characters || !characters.characters) {
    return [];
  }

  return characters.characters.filter(character => {
    // Search term filter
    if (searchTerm && !character.name.toLowerCase().includes(searchTerm.toLowerCase()) &&
        !character.description?.toLowerCase().includes(searchTerm.toLowerCase())) {
      return false;
    }

    // Role filter
    if (filters.characters.role && character.role !== filters.characters.role) {
      return false;
    }

    // Age filter (simplified)
    if (filters.characters.age) {
      const age = parseInt(character.age);
      if (isNaN(age)) return false;

      if (filters.characters.age === 'young' && age >= 30) return false;
      if (filters.characters.age === 'middle' && (age < 30 || age >= 60)) return false;
      if (filters.characters.age === 'old' && age < 60) return false;
    }

    return true;
  });
};

export const selectFilteredWorldElements = (state) => {
  const { searchTerm, filters } = state.reference;

  // Check if worldBuilding state exists and has elements
  if (!state.worldBuilding || !state.worldBuilding.elements || !state.worldBuilding.elements.allIds) {
    console.log('No world building elements found in state');
    return [];
  }

  // Get all elements from the worldBuilding slice
  const elements = state.worldBuilding.elements.allIds
    .map(id => state.worldBuilding.elements.byId[id])
    .filter(Boolean); // Filter out any undefined elements

  console.log('Found world elements:', elements.length);

  return elements.filter(element => {
    // Search term filter
    if (searchTerm &&
        !element.name?.toLowerCase().includes(searchTerm.toLowerCase()) &&
        !element.description?.toLowerCase().includes(searchTerm.toLowerCase())) {
      return false;
    }

    // Type filter (if implemented)
    if (filters.world.type && element.element_type !== filters.world.type) {
      return false;
    }

    // Location filter (if implemented)
    if (filters.world.location && element.location !== filters.world.location) {
      return false;
    }

    return true;
  });
};

export default referenceSlice.reducer;

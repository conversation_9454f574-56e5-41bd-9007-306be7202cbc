// src/redux/slices/bookSlice.js
import { createSlice, createAsyncThunk, createSelector } from '@reduxjs/toolkit';
import {
  fetchBooks,
  fetchBookDetails,
  createBook,
  updateBook,
  deleteBook,
  fetchBookMetadata
} from '../../services/apiService';

// Initial state
const initialState = {
  books: [],
  selectedBook: null,
  bookMetadata: {},
  isLoading: false,
  isSaving: false,
  error: null,
  lastFetched: null,
  bookDetails: {}, // Cache for complete book details
  bookDetailsLoading: {} // Loading state for individual books
};

// Async thunks
export const fetchAllBooks = createAsyncThunk(
  'books/fetchAll',
  async (_, { rejectWithValue }) => {
    try {
      const response = await fetchBooks();
      return response;
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to fetch books');
    }
  }
);

export const fetchBookById = createAsyncThunk(
  'books/fetchById',
  async (bookId, { rejectWithValue }) => {
    try {
      const response = await fetchBookDetails(bookId);
      return response;
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to fetch book details');
    }
  }
);

export const createNewBook = createAsyncThunk(
  'books/create',
  async (bookData, { rejectWithValue, dispatch }) => {
    try {
      const response = await createBook(bookData);
      // After creating a book, fetch all books to update the list
      dispatch(fetchAllBooks());
      return response;
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to create book');
    }
  }
);

export const updateExistingBook = createAsyncThunk(
  'books/update',
  async ({ bookId, bookData }, { rejectWithValue }) => {
    try {
      console.log('updateExistingBook thunk called with:', { bookId, bookData });
      const response = await updateBook(bookId, bookData);
      console.log('updateExistingBook response:', response);
      return response;
    } catch (error) {
      console.error('Error in updateExistingBook:', error);
      return rejectWithValue(error.message || 'Failed to update book');
    }
  }
);

export const deleteExistingBook = createAsyncThunk(
  'books/delete',
  async (bookId, { rejectWithValue, dispatch }) => {
    try {
      await deleteBook(bookId);
      // After deleting a book, fetch all books to update the list
      dispatch(fetchAllBooks());
      return bookId;
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to delete book');
    }
  }
);

export const fetchMetadata = createAsyncThunk(
  'books/fetchMetadata',
  async (bookId, { rejectWithValue }) => {
    try {
      console.log('fetchMetadata thunk called with bookId:', bookId);
      const response = await fetchBookMetadata(bookId);
      console.log('fetchMetadata response:', response);
      return { bookId, metadata: response };
    } catch (error) {
      console.error('Error in fetchMetadata:', error);
      return rejectWithValue(error.message || 'Failed to fetch book metadata');
    }
  }
);

// The updateBookMemoryData thunk has been removed as part of the migration away from book_memory table

// Book slice
const bookSlice = createSlice({
  name: 'books',
  initialState,
  reducers: {
    selectBook: (state, action) => {
      console.log('selectBook action with payload:', action.payload);

      // Ensure we have a complete book object
      if (action.payload && action.payload.book_id) {
        const bookId = action.payload.book_id;

        // Check if we have complete details for this book in our cache
        if (state.bookDetails[bookId]) {
          console.log('Using cached book details for:', bookId);
          state.selectedBook = state.bookDetails[bookId];
        } else {
          // If we have a book with this ID in our books array, use that data
          // to ensure we have all fields
          const existingBook = state.books.find(book => book.book_id === bookId);

          if (existingBook) {
            console.log('Found existing book in books array:', existingBook);
            state.selectedBook = {
              ...existingBook,
              ...action.payload,
              // Ensure these fields exist even if they're empty
              author: action.payload.author || existingBook.author || '',
              genre: action.payload.genre || existingBook.genre || '',
              description: action.payload.description || existingBook.description || ''
            };
          } else {
            // If we don't have this book in our array, use the payload but ensure fields exist
            state.selectedBook = {
              ...action.payload,
              author: action.payload.author || '',
              genre: action.payload.genre || '',
              description: action.payload.description || ''
            };
          }
        }

        // Save the book ID to localStorage
        localStorage.setItem('lastBookId', action.payload.book_id);
      } else {
        console.error('selectBook received invalid book data:', action.payload);
        state.selectedBook = action.payload;
      }
    },
    clearSelectedBook: (state) => {
      state.selectedBook = null;
    },
    clearError: (state) => {
      state.error = null;
    },
    setBookMetadata: (state, action) => {
      const { bookId, metadata } = action.payload;
      state.bookMetadata[bookId] = {
        ...state.bookMetadata[bookId],
        ...metadata
      };
    }
  },
  extraReducers: (builder) => {
    builder
      // Fetch all books
      .addCase(fetchAllBooks.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchAllBooks.fulfilled, (state, action) => {
        state.isLoading = false;
        state.books = action.payload;
        state.lastFetched = new Date().toISOString();
      })
      .addCase(fetchAllBooks.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })

      // Fetch book by ID
      .addCase(fetchBookById.pending, (state, action) => {
        state.isLoading = true;
        state.error = null;

        // Set loading state for this specific book
        const bookId = action.meta.arg; // The bookId passed to the thunk
        state.bookDetailsLoading[bookId] = true;
      })
      .addCase(fetchBookById.fulfilled, (state, action) => {
        state.isLoading = false;
        const bookId = action.meta.arg; // The bookId passed to the thunk

        // Clear loading state for this specific book
        state.bookDetailsLoading[bookId] = false;

        // Log the received book data for debugging
        console.log('fetchBookById.fulfilled with payload:', action.payload);

        // Ensure we have a complete book object
        if (action.payload && action.payload.book_id) {
          // Normalize the book data to ensure all fields exist
          const normalizedBook = {
            ...action.payload,
            // Ensure these fields exist even if they're empty
            author: action.payload.author || '',
            genre: action.payload.genre || '',
            description: action.payload.description || ''
          };

          // Store the complete book details in our cache
          state.bookDetails[action.payload.book_id] = normalizedBook;

          // Update selectedBook with the full book data if this is the currently selected book
          if (state.selectedBook && state.selectedBook.book_id === action.payload.book_id) {
            state.selectedBook = normalizedBook;
          }

          // Update the book in the books array if it exists, or add it if it doesn't
          const index = state.books.findIndex(book => book.book_id === action.payload.book_id);
          if (index !== -1) {
            console.log('Updating book in books array. Before:', state.books[index]);
            state.books[index] = normalizedBook;
            console.log('After update:', state.books[index]);
          } else {
            // Add the book to the books array if it doesn't exist
            state.books.push(normalizedBook);
            console.log('Added book to books array:', normalizedBook);
          }

          // Save the book ID to localStorage
          localStorage.setItem('lastBookId', action.payload.book_id);
        } else {
          console.error('fetchBookById.fulfilled received invalid book data:', action.payload);
        }
      })
      .addCase(fetchBookById.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;

        // Clear loading state for this specific book
        const bookId = action.meta.arg; // The bookId passed to the thunk
        state.bookDetailsLoading[bookId] = false;
      })

      // Create new book
      .addCase(createNewBook.pending, (state) => {
        state.isSaving = true;
        state.error = null;
      })
      .addCase(createNewBook.fulfilled, (state, action) => {
        state.isSaving = false;
        // Add the new book to the books array
        state.books.push(action.payload);
        state.selectedBook = action.payload;
        console.log('Added new book to books array:', action.payload);

        // Save the last selected book ID to localStorage
        localStorage.setItem('lastBookId', action.payload.book_id);
      })
      .addCase(createNewBook.rejected, (state, action) => {
        state.isSaving = false;
        state.error = action.payload;
      })

      // Update existing book
      .addCase(updateExistingBook.pending, (state) => {
        state.isSaving = true;
        state.error = null;
      })
      .addCase(updateExistingBook.fulfilled, (state, action) => {
        state.isSaving = false;
        console.log('updateExistingBook.fulfilled with payload:', action.payload);

        // Make sure we have a valid book_id in the payload
        if (!action.payload || !action.payload.book_id) {
          console.error('Invalid payload in updateExistingBook.fulfilled:', action.payload);
          return;
        }

        // Normalize the book data to ensure all fields exist
        const normalizedBook = {
          ...action.payload,
          // Ensure these fields exist even if they're empty
          author: action.payload.author || '',
          genre: action.payload.genre || '',
          description: action.payload.description || ''
        };

        // Update the book in the books array
        const index = state.books.findIndex(book => book.book_id === action.payload.book_id);
        console.log('Book index in books array:', index, 'for book_id:', action.payload.book_id);

        if (index !== -1) {
          console.log('Updating book in books array. Before:', state.books[index]);
          state.books[index] = normalizedBook;
          console.log('After update:', state.books[index]);
        } else {
          console.log('Book not found in books array. Current books:', state.books.map(b => b.book_id));
        }

        // Update the book details cache
        state.bookDetails[action.payload.book_id] = normalizedBook;
        console.log('Updated book details cache for book_id:', action.payload.book_id);

        // Update selected book if it's the same book
        if (state.selectedBook && state.selectedBook.book_id === action.payload.book_id) {
          console.log('Updating selected book. Before:', state.selectedBook);
          state.selectedBook = normalizedBook;
          console.log('After update:', state.selectedBook);
        } else {
          console.log('Selected book not updated. Current selected book:',
            state.selectedBook ? state.selectedBook.book_id : 'none');
        }
      })
      .addCase(updateExistingBook.rejected, (state, action) => {
        state.isSaving = false;
        state.error = action.payload;
      })

      // Delete existing book
      .addCase(deleteExistingBook.pending, (state) => {
        state.isSaving = true;
        state.error = null;
      })
      .addCase(deleteExistingBook.fulfilled, (state, action) => {
        state.isSaving = false;

        // Remove the book from the books array
        state.books = state.books.filter(book => book.book_id !== action.payload);

        // Clear selected book if it's the deleted book
        if (state.selectedBook && state.selectedBook.book_id === action.payload) {
          state.selectedBook = null;
        }
      })
      .addCase(deleteExistingBook.rejected, (state, action) => {
        state.isSaving = false;
        state.error = action.payload;
      })

      // The updateBookMemoryData cases have been removed as part of the migration away from book_memory table

      // Fetch book metadata
      .addCase(fetchMetadata.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchMetadata.fulfilled, (state, action) => {
        state.isLoading = false;
        const { bookId, metadata } = action.payload;

        // Initialize metadata object for this book if it doesn't exist
        if (!state.bookMetadata[bookId]) {
          state.bookMetadata[bookId] = {};
        }

        // Update metadata
        state.bookMetadata[bookId] = {
          ...state.bookMetadata[bookId],
          ...metadata
        };
      })
      .addCase(fetchMetadata.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      });
  },
});

// Export actions and reducer
export const {
  selectBook,
  clearSelectedBook,
  clearError,
  setBookMetadata
} = bookSlice.actions;

export default bookSlice.reducer;

// Basic selectors
const selectBooksState = (state) => state.books;
const selectBooksArray = (state) => state.books.books;
const selectSelectedBookRaw = (state) => state.books.selectedBook;
const selectBookMetadataRaw = (state) => state.books.bookMetadata;
const selectBookDetailsRaw = (state) => state.books.bookDetails;
const selectBookDetailsLoadingRaw = (state) => state.books.bookDetailsLoading;

// Memoized selectors
export const selectAllBooks = createSelector(
  [selectBooksArray],
  (books) => books
);

export const selectCurrentBook = createSelector(
  [selectSelectedBookRaw],
  (selectedBook) => selectedBook
);

export const selectBookById = createSelector(
  [selectBooksArray, (_, bookId) => bookId],
  (books, bookId) => books.find(book => book.book_id === bookId)
);

export const selectBookMetadata = createSelector(
  [selectBookMetadataRaw, (_, bookId) => bookId],
  (metadata, bookId) => metadata[bookId] || {}
);

export const selectBooksLoading = createSelector(
  [selectBooksState],
  (booksState) => booksState.isLoading
);

export const selectBooksSaving = createSelector(
  [selectBooksState],
  (booksState) => booksState.isSaving
);

export const selectBooksError = createSelector(
  [selectBooksState],
  (booksState) => booksState.error
);

export const selectLastFetched = createSelector(
  [selectBooksState],
  (booksState) => booksState.lastFetched
);

export const selectBookDetail = createSelector(
  [selectBookDetailsRaw, (_, bookId) => bookId],
  (bookDetails, bookId) => bookDetails[bookId] || null
);

export const selectIsBookDetailLoading = createSelector(
  [selectBookDetailsLoadingRaw, (_, bookId) => bookId],
  (loadingState, bookId) => !!loadingState[bookId]
);

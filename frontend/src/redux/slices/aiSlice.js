// src/redux/slices/aiSlice.js
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import {
  generateAIResponse,
  fetchWorld,
  fetchBrainstorm,
  saveBrainstorm
} from '../../services/apiService';
import { getHeaders } from '../../services/apiService';

// Initial state
const initialState = {
  responses: [],
  originalPrompt: '',
  isLoading: false,
  error: null,
  isOpen: false
};

// Async thunks
export const generateResponse = createAsyncThunk(
  'ai/generateResponse',
  async ({ bookId, prompt, chapterContent = '' }, { rejectWithValue }) => {
    try {
      console.debug('Generating AI response for prompt:', prompt);
      const contextualPrompt = `${prompt} (Context: ${chapterContent})`;
      const response = await generateAIResponse(bookId, contextualPrompt);

      try {
        const parsedResponse = JSON.parse(response.response);
        if (parsedResponse.suggestions && Array.isArray(parsedResponse.suggestions)) {
          return {
            responses: parsedResponse.suggestions.map(s => s.text),
            originalPrompt: prompt
          };
        }
      } catch (parseError) {
        // If parsing fails, just return the raw response
        console.debug('Could not parse AI response as JSON, using raw text');
      }

      return {
        responses: [response.response],
        originalPrompt: prompt
      };
    } catch (error) {
      console.error('Error generating AI response:', error);
      return rejectWithValue(error.message || 'Failed to generate AI response');
    }
  }
);

export const connectIdeas = createAsyncThunk(
  'ai/connectIdeas',
  async ({
    bookId,
    selectedNodes,
    currentPage
  }, { rejectWithValue }) => {
    try {
      console.debug('Connecting ideas for nodes:', selectedNodes.map(n => n.id));

      if (!selectedNodes || selectedNodes.length === 0 || selectedNodes.length > 5) {
        throw new Error('Please select between 1 and 5 nodes to connect');
      }

      // Process the selected nodes directly from the input
      const processedNodes = selectedNodes.map(node => {
        // Extract content from node
        let nodeContent = '';
        try {
          if (node.data && node.data.content) {
            nodeContent = node.data.content;
          } else if (typeof node.content === 'string') {
            try {
              const contentObj = JSON.parse(node.content);
              nodeContent = contentObj.data?.content || contentObj.content || node.content;
            } catch {
              nodeContent = node.content;
            }
          } else if (node.content?.data?.content) {
            nodeContent = node.content.data.content;
          } else if (node.content?.content) {
            nodeContent = node.content.content;
          } else {
            nodeContent = JSON.stringify(node.content || node.data || 'No content');
          }
        } catch (e) {
          console.error('Error extracting content from node:', e);
          nodeContent = 'Unknown content';
        }

        return {
          id: node.id,
          title: node.data?.label || node.title || 'Untitled',
          content: nodeContent
        };
      });

      if (processedNodes.length === 0) {
        throw new Error('No valid nodes found');
      }

      // Fetch world data for context
      const worldData = await fetchWorld(bookId);
      const settingTypes = worldData.world?.setting_types || ['fantasy'];

      // Create a prompt that asks for new ideas connecting the selected nodes
      const prompt = `I have ${processedNodes.length} ideas for my ${settingTypes.join('-')} book:

${processedNodes.map((node, i) => `Idea ${i+1}: ${node.title} - ${node.content}`).join('\n\n')}

Generate 3 new creative ideas that connect these concepts together. Each idea should bridge these concepts in an interesting way.

Return as JSON: {"suggestions": [{"title": "New Idea Title", "content": "Detailed description", "type": "idea"}]}`;

      // Generate connected ideas
      const response = await generateAIResponse(bookId, prompt);

      // Parse the response
      const parsedResponse = JSON.parse(response.response);
      const newIdeas = parsedResponse.suggestions.map(suggestion => ({
        title: suggestion.title || 'Connected Idea',
        content: suggestion.content || '',
        type: suggestion.type || 'idea'
      }));

      return {
        newIdeas,
        selectedNodes,
        currentPage
      };
    } catch (error) {
      console.error('Error connecting ideas:', error);
      return rejectWithValue(error.message || 'Failed to connect ideas');
    }
  }
);

export const refineIdea = createAsyncThunk(
  'ai/refineIdea',
  async ({
    bookId,
    nodeId,
    brainstormPageId,
    nodes
  }, { rejectWithValue }) => {
    try {
      console.debug('Refining idea for node:', nodeId);

      // Fetch brainstorm data
      const brainstormData = await fetchBrainstorm(bookId);
      const node = brainstormData.brainstorm.pages?.[brainstormPageId]?.nodes.find(n => n.id === nodeId);
      if (!node) throw new Error('Node not found');

      // Fetch world data for context
      const worldData = await fetchWorld(bookId);
      const settingTypes = worldData.world?.setting_types || ['fantasy'];

      // Generate refined idea
      const response = await generateAIResponse(
        bookId,
        `Improve this brainstorming idea for a ${settingTypes.join('-')} book: '${node.text}'. Return as JSON: {"suggestions": [{"text": "Improved idea"}]}`
      );

      const parsedResponse = JSON.parse(response.response);
      const refinedText = parsedResponse.suggestions[0].text;

      // Extract characters from refined text
      const characters = [];
      // Character extraction will be handled by the component for now

      // Update the node in the brainstorm
      const updatedNodes = nodes.map(n =>
        n.id === nodeId ? { ...n, text: refinedText, characters } : n
      );

      // Save the updated brainstorm
      await saveBrainstorm(bookId, {
        pages: {
          [brainstormPageId]: {
            nodes: updatedNodes,
            connectors: brainstormData.brainstorm.pages[brainstormPageId].connectors
          }
        },
      });

      return {
        nodeId,
        refinedText,
        characters
      };
    } catch (error) {
      console.error('Error refining idea:', error);
      return rejectWithValue(error.message || 'Failed to refine idea');
    }
  }
);

// Slice
const aiSlice = createSlice({
  name: 'ai',
  initialState,
  reducers: {
    clearResponses: (state) => {
      state.responses = [];
      state.originalPrompt = '';
    },
    toggleAISidebar: (state) => {
      state.isOpen = !state.isOpen;
    },
    setAISidebarOpen: (state, action) => {
      state.isOpen = action.payload;
    }
  },
  extraReducers: (builder) => {
    builder
      // Generate response
      .addCase(generateResponse.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(generateResponse.fulfilled, (state, action) => {
        state.isLoading = false;
        state.responses = action.payload.responses;
        state.originalPrompt = action.payload.originalPrompt;
      })
      .addCase(generateResponse.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload || 'Failed to generate response';
        state.responses = ['Error: Could not generate response'];
      })

      // Refine idea
      .addCase(refineIdea.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(refineIdea.fulfilled, (state, action) => {
        state.isLoading = false;
        state.responses = [action.payload.refinedText];
      })
      .addCase(refineIdea.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload || 'Failed to refine idea';
        state.responses = ['Error refining idea: ' + (action.payload || 'Unknown error')];
      })

      // Connect ideas
      .addCase(connectIdeas.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(connectIdeas.fulfilled, (state, action) => {
        state.isLoading = false;
        // Format the ideas as responses
        state.responses = action.payload.newIdeas.map(idea =>
          `${idea.title}: ${idea.content}`
        );
        state.originalPrompt = 'Connect ideas';
      })
      .addCase(connectIdeas.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload || 'Failed to connect ideas';
        state.responses = ['Error connecting ideas: ' + (action.payload || 'Unknown error')];
      });
  }
});

// Actions
export const { clearResponses, toggleAISidebar, setAISidebarOpen } = aiSlice.actions;

// Selectors
export const selectAIResponses = (state) => state.ai.responses;
export const selectOriginalPrompt = (state) => state.ai.originalPrompt;
export const selectAILoading = (state) => state.ai.isLoading;
export const selectAIError = (state) => state.ai.error;
export const selectAISidebarOpen = (state) => state.ai.isOpen;

export default aiSlice.reducer;

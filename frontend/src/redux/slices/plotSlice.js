// src/redux/slices/plotSlice.js
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import * as plotApi from '../../services/plotApiService';

// Initial state
const initialState = {
  events: [],
  chapters: [],
  selectedEvent: null,
  selectedChapter: null,
  isLoading: false,
  isSaving: false,
  error: null,
  lastFetched: null
};

// Async thunks for plot events
export const fetchAllPlotEvents = createAsyncThunk(
  'plot/fetchAllEvents',
  async (bookId, { rejectWithValue }) => {
    try {
      const response = await plotApi.fetchAllEvents(bookId);
      return response;
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to fetch plot data');
    }
  }
);

export const createPlotEvent = createAsyncThunk(
  'plot/createEvent',
  async ({ bookId, eventData }, { rejectWithValue }) => {
    console.log('[DEBUG] createPlotEvent thunk called with:', { bookId, eventData });

    try {
      console.log('[DEBUG] Calling plotApi.createEvent');
      const response = await plotApi.createEvent(bookId, eventData);
      console.log('[DEBUG] plotApi.createEvent response:', response);

      // Ensure the response has an id field (might be event_id in the response)
      const processedResponse = {
        ...response,
        // If id is missing but event_id exists, use event_id as id
        id: response.id || response.event_id
      };

      console.log('[DEBUG] Processed response:', processedResponse);
      return processedResponse;
    } catch (error) {
      console.error('[DEBUG] Error in createPlotEvent thunk:', error);
      return rejectWithValue(error.message || 'Failed to create plot event');
    }
  }
);

export const updateExistingPlotEvent = createAsyncThunk(
  'plot/updateEvent',
  async ({ bookId, eventId, eventData }, { rejectWithValue }) => {
    try {
      const response = await plotApi.updateEvent(bookId, eventId, eventData);
      return response;
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to update plot event');
    }
  }
);

export const removePlotEvent = createAsyncThunk(
  'plot/removeEvent',
  async ({ bookId, eventId }, { rejectWithValue }) => {
    try {
      await plotApi.deleteEvent(bookId, eventId);
      return eventId;
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to delete plot event');
    }
  }
);

// Async thunks for chapters
export const fetchAllChapters = createAsyncThunk(
  'plot/fetchAllChapters',
  async (bookId, { rejectWithValue }) => {
    try {
      const response = await plotApi.fetchChapters(bookId);
      return response;
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to fetch chapters');
    }
  }
);

export const createChapter = createAsyncThunk(
  'plot/createChapter',
  async ({ bookId, chapterData }, { rejectWithValue }) => {
    console.log('[DEBUG] createChapter thunk started with:', { bookId, chapterData });
    try {
      console.log('[DEBUG] Calling plotApi.createChapter');
      const response = await plotApi.createChapter(bookId, chapterData);
      console.log('[DEBUG] createChapter API response:', response);
      return response;
    } catch (error) {
      console.error('[DEBUG] createChapter error:', error);
      return rejectWithValue(error.message || 'Failed to create chapter');
    }
  }
);

export const updateExistingChapter = createAsyncThunk(
  'plot/updateChapter',
  async ({ bookId, chapterId, chapterData }, { rejectWithValue }) => {
    try {
      const response = await plotApi.updateChapter(bookId, chapterId, chapterData);
      return response;
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to update chapter');
    }
  }
);

export const removeChapter = createAsyncThunk(
  'plot/removeChapter',
  async ({ bookId, chapterId }, { rejectWithValue }) => {
    try {
      await plotApi.deleteChapter(bookId, chapterId);
      return chapterId;
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to delete chapter');
    }
  }
);

export const reorderChapters = createAsyncThunk(
  'plot/reorderChapters',
  async ({ bookId, chapters }, { rejectWithValue }) => {
    try {
      await plotApi.updateChapterOrder(bookId, chapters);
      return chapters;
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to reorder chapters');
    }
  }
);

// Async thunks for event-chapter relationships
export const assignEventToChapter = createAsyncThunk(
  'plot/assignEventToChapter',
  async ({ bookId, eventId, chapterId }, { rejectWithValue }) => {
    try {
      console.log('[DEBUG] assignEventToChapter thunk called with:', { bookId, eventId, chapterId });
      const response = await plotApi.assignEventToChapter(bookId, eventId, chapterId);
      console.log('[DEBUG] assignEventToChapter response:', response);
      return { eventId, chapterId, updatedEvent: response };
    } catch (error) {
      console.error('[DEBUG] Error in assignEventToChapter thunk:', error);
      return rejectWithValue(error.message || 'Failed to assign event to chapter');
    }
  }
);

export const addEventToChapter = createAsyncThunk(
  'plot/addEventToChapter',
  async ({ bookId, eventId, chapterId }, { rejectWithValue }) => {
    try {
      const response = await plotApi.assignEventToChapter(bookId, eventId, chapterId);
      return { eventId, chapterId, updatedEvent: response };
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to add event to chapter');
    }
  }
);

// Async thunk for AI generation
export const generateAIPlotEvent = createAsyncThunk(
  'plot/generateAIEvent',
  async ({ bookId, promptData }, { rejectWithValue }) => {
    try {
      const response = await plotApi.generatePlotEvent(bookId, promptData);
      return response;
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to generate AI plot event');
    }
  }
);

// New thunk to handle receiving events from brainstorm
export const receiveEventFromBrainstorm = createAsyncThunk(
  'plot/receiveEventFromBrainstorm',
  async (event, { dispatch, getState }) => {
    console.log('[DEBUG] Receiving event from brainstorm:', event);
    return event;
  }
);

// Create the plot slice
const plotSlice = createSlice({
  name: 'plot',
  initialState,
  reducers: {
    // Select an event
    selectEvent: (state, action) => {
      const eventId = action.payload;
      state.selectedEvent = state.events.find(event => event.id === eventId) || null;
    },
    // Select a chapter
    selectChapter: (state, action) => {
      const chapterId = action.payload;
      state.selectedChapter = state.chapters.find(chapter => chapter.id === chapterId) || null;
    },
    // Clear selected event
    clearSelectedEvent: (state) => {
      state.selectedEvent = null;
    },
    // Clear selected chapter
    clearSelectedChapter: (state) => {
      state.selectedChapter = null;
    },
    // Clear error
    clearError: (state) => {
      state.error = null;
    }
  },
  extraReducers: (builder) => {
    builder
      // Fetch all plot events
      .addCase(fetchAllPlotEvents.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchAllPlotEvents.fulfilled, (state, action) => {
        state.isLoading = false;
        state.events = action.payload;
        state.lastFetched = new Date().toISOString();
      })
      .addCase(fetchAllPlotEvents.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })

      // Create plot event
      .addCase(createPlotEvent.pending, (state) => {
        console.log('[DEBUG] createPlotEvent.pending reducer');
        state.isSaving = true;
        state.error = null;
      })
      .addCase(createPlotEvent.fulfilled, (state, action) => {
        console.log('[DEBUG] createPlotEvent.fulfilled reducer with payload:', action.payload);
        state.isSaving = false;

        // Ensure the event has an id field (might be event_id in the response)
        const event = {
          ...action.payload,
          // If id is missing but event_id exists, use event_id as id
          id: action.payload.id || action.payload.event_id
        };

        console.log('[DEBUG] Processed event with id:', event.id);
        state.events.push(event);
        state.selectedEvent = event;
        console.log('[DEBUG] Updated events state, now has', state.events.length, 'events');
      })
      .addCase(createPlotEvent.rejected, (state, action) => {
        console.error('[DEBUG] createPlotEvent.rejected reducer with error:', action.payload);
        state.isSaving = false;
        state.error = action.payload;
      })

      // Update plot event
      .addCase(updateExistingPlotEvent.pending, (state) => {
        state.isSaving = true;
        state.error = null;
      })
      .addCase(updateExistingPlotEvent.fulfilled, (state, action) => {
        state.isSaving = false;
        const index = state.events.findIndex(event => event.id === action.payload.id);
        if (index !== -1) {
          state.events[index] = action.payload;
        }
        if (state.selectedEvent && state.selectedEvent.id === action.payload.id) {
          state.selectedEvent = action.payload;
        }
      })
      .addCase(updateExistingPlotEvent.rejected, (state, action) => {
        state.isSaving = false;
        state.error = action.payload;
      })

      // Remove plot event
      .addCase(removePlotEvent.pending, (state) => {
        state.isSaving = true;
        state.error = null;
      })
      .addCase(removePlotEvent.fulfilled, (state, action) => {
        state.isSaving = false;
        state.events = state.events.filter(event => event.id !== action.payload);
        if (state.selectedEvent && state.selectedEvent.id === action.payload) {
          state.selectedEvent = null;
        }
      })
      .addCase(removePlotEvent.rejected, (state, action) => {
        state.isSaving = false;
        state.error = action.payload;
      })

      // Fetch all chapters
      .addCase(fetchAllChapters.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchAllChapters.fulfilled, (state, action) => {
        state.isLoading = false;
        state.chapters = action.payload.sort((a, b) => {
          // Support both sequence_number and order fields
          const aOrder = a.sequence_number !== undefined ? a.sequence_number : (a.order || 0);
          const bOrder = b.sequence_number !== undefined ? b.sequence_number : (b.order || 0);
          console.log('[DEBUG] Sorting chapters:', { a: a.title, aOrder, b: b.title, bOrder });
          return aOrder - bOrder;
        });
      })
      .addCase(fetchAllChapters.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })

      // Create chapter
      .addCase(createChapter.pending, (state) => {
        console.log('[DEBUG] createChapter.pending reducer');
        state.isSaving = true;
        state.error = null;
      })
      .addCase(createChapter.fulfilled, (state, action) => {
        console.log('[DEBUG] createChapter.fulfilled reducer with payload:', action.payload);
        state.isSaving = false;

        // Ensure the chapter has an id field (might be chapter_id in the response)
        const chapter = {
          ...action.payload,
          // If id is missing but chapter_id exists, use chapter_id as id
          id: action.payload.id || action.payload.chapter_id
        };

        console.log('[DEBUG] Processed chapter with id:', chapter);
        state.chapters.push(chapter);
        state.chapters.sort((a, b) => {
          // Support both sequence_number and order fields
          const aOrder = a.sequence_number !== undefined ? a.sequence_number : (a.order || 0);
          const bOrder = b.sequence_number !== undefined ? b.sequence_number : (b.order || 0);
          return aOrder - bOrder;
        });
        state.selectedChapter = chapter;
        console.log('[DEBUG] Updated chapters state:', state.chapters);
      })
      .addCase(createChapter.rejected, (state, action) => {
        console.error('[DEBUG] createChapter.rejected reducer with error:', action.payload);
        state.isSaving = false;
        state.error = action.payload;
      })

      // Update chapter
      .addCase(updateExistingChapter.pending, (state) => {
        state.isSaving = true;
        state.error = null;
      })
      .addCase(updateExistingChapter.fulfilled, (state, action) => {
        state.isSaving = false;
        const index = state.chapters.findIndex(chapter => chapter.id === action.payload.id);
        if (index !== -1) {
          state.chapters[index] = action.payload;
        }
        if (state.selectedChapter && state.selectedChapter.id === action.payload.id) {
          state.selectedChapter = action.payload;
        }
      })
      .addCase(updateExistingChapter.rejected, (state, action) => {
        state.isSaving = false;
        state.error = action.payload;
      })

      // Remove chapter
      .addCase(removeChapter.pending, (state) => {
        state.isSaving = true;
        state.error = null;
      })
      .addCase(removeChapter.fulfilled, (state, action) => {
        state.isSaving = false;
        state.chapters = state.chapters.filter(chapter => chapter.id !== action.payload);
        if (state.selectedChapter && state.selectedChapter.id === action.payload) {
          state.selectedChapter = null;
        }
        // Update events that were in this chapter to be unassigned
        state.events = state.events.map(event => {
          if (event.chapter_id === action.payload) {
            return { ...event, chapter_id: null };
          }
          return event;
        });
      })
      .addCase(removeChapter.rejected, (state, action) => {
        state.isSaving = false;
        state.error = action.payload;
      })

      // Reorder chapters
      .addCase(reorderChapters.pending, (state) => {
        state.isSaving = true;
        state.error = null;
      })
      .addCase(reorderChapters.fulfilled, (state, action) => {
        state.isSaving = false;
        // Update the order of chapters in the state
        const updatedChapters = action.payload;
        updatedChapters.forEach(updatedChapter => {
          const index = state.chapters.findIndex(chapter => chapter.id === updatedChapter.id);
          if (index !== -1) {
            state.chapters[index] = { ...state.chapters[index], sequence_number: updatedChapter.sequence_number };
          }
        });
        state.chapters.sort((a, b) => {
          // Support both sequence_number and order fields
          const aOrder = a.sequence_number !== undefined ? a.sequence_number : (a.order || 0);
          const bOrder = b.sequence_number !== undefined ? b.sequence_number : (b.order || 0);
          return aOrder - bOrder;
        });
      })
      .addCase(reorderChapters.rejected, (state, action) => {
        state.isSaving = false;
        state.error = action.payload;
      })

      // Assign event to chapter
      .addCase(assignEventToChapter.pending, (state) => {
        state.isSaving = true;
        state.error = null;
      })
      .addCase(assignEventToChapter.fulfilled, (state, action) => {
        state.isSaving = false;
        const { eventId, updatedEvent } = action.payload;

        console.log('[DEBUG] assignEventToChapter.fulfilled with payload:', action.payload);

        // Ensure the updated event has all the required fields
        const processedEvent = {
          ...updatedEvent,
          // Ensure id is set correctly
          id: updatedEvent.id || updatedEvent.event_id,
          // Ensure characters is an array
          characters: Array.isArray(updatedEvent.characters) ? updatedEvent.characters : [],
          // Ensure locations is an array
          locations: Array.isArray(updatedEvent.locations) ? updatedEvent.locations : [],
          // Ensure type is set
          type: updatedEvent.type || updatedEvent.event_type
        };

        console.log('[DEBUG] Processed event for Redux store:', processedEvent);

        // Update the event in the state
        const index = state.events.findIndex(event => event.id === eventId);
        if (index !== -1) {
          state.events[index] = processedEvent;
        }
        if (state.selectedEvent && state.selectedEvent.id === eventId) {
          state.selectedEvent = processedEvent;
        }
      })
      .addCase(assignEventToChapter.rejected, (state, action) => {
        state.isSaving = false;
        state.error = action.payload;
      })

      // Add event to chapter (same as assignEventToChapter but with different action type)
      .addCase(addEventToChapter.pending, (state) => {
        state.isSaving = true;
        state.error = null;
      })
      .addCase(addEventToChapter.fulfilled, (state, action) => {
        state.isSaving = false;
        const { eventId, updatedEvent } = action.payload;

        console.log('[DEBUG] addEventToChapter.fulfilled with payload:', action.payload);

        // Ensure the updated event has all the required fields
        const processedEvent = {
          ...updatedEvent,
          // Ensure id is set correctly
          id: updatedEvent.id || updatedEvent.event_id,
          // Ensure characters is an array
          characters: Array.isArray(updatedEvent.characters) ? updatedEvent.characters : [],
          // Ensure locations is an array
          locations: Array.isArray(updatedEvent.locations) ? updatedEvent.locations : [],
          // Ensure type is set
          type: updatedEvent.type || updatedEvent.event_type
        };

        console.log('[DEBUG] Processed event for Redux store:', processedEvent);

        // Update the event in the state
        const index = state.events.findIndex(event => event.id === eventId);
        if (index !== -1) {
          state.events[index] = processedEvent;
        }
        if (state.selectedEvent && state.selectedEvent.id === eventId) {
          state.selectedEvent = processedEvent;
        }
      })
      .addCase(addEventToChapter.rejected, (state, action) => {
        state.isSaving = false;
        state.error = action.payload;
      })

      // Generate AI plot event
      .addCase(generateAIPlotEvent.pending, (state) => {
        state.isSaving = true;
        state.error = null;
      })
      .addCase(generateAIPlotEvent.fulfilled, (state, action) => {
        state.isSaving = false;
        state.events.push(action.payload);
        state.selectedEvent = action.payload;
      })
      .addCase(generateAIPlotEvent.rejected, (state, action) => {
        state.isSaving = false;
        state.error = action.payload;
      })

      // Receive event from brainstorm
      .addCase(receiveEventFromBrainstorm.fulfilled, (state, action) => {
        const event = action.payload;
        console.log('[DEBUG] Processing event from brainstorm:', event);

        // Ensure the event has all the required fields
        const processedEvent = {
          ...event,
          // Ensure id is set correctly
          id: event.id || event.event_id,
          // Ensure characters is an array
          characters: Array.isArray(event.characters) ? event.characters : [],
          // Ensure locations is an array
          locations: Array.isArray(event.locations) ? event.locations : [],
          // Ensure type is set
          type: event.type || event.event_type
        };

        console.log('[DEBUG] Processed event for Redux store:', processedEvent);

        // Check if the event already exists in the state
        const existingIndex = state.events.findIndex(e => e.id === processedEvent.id);
        if (existingIndex !== -1) {
          // Update existing event
          state.events[existingIndex] = processedEvent;
        } else {
          // Add new event
          state.events.push(processedEvent);
        }
      });
  }
});

// Export actions
export const {
  selectEvent,
  selectChapter,
  clearSelectedEvent,
  clearSelectedChapter,
  clearError
} = plotSlice.actions;

// Export selectors
export const selectAllPlotEvents = (state) => state.plot.events;
export const selectAllChapters = (state) => {
  console.log('[DEBUG] selectAllChapters called, chapters:', state.plot.chapters);
  return state.plot.chapters;
};
export const selectEventsByChapter = (state, chapterId) =>
  state.plot.events.filter(event => event.chapter_id === chapterId)
    .sort((a, b) => {
      // Support both sequence_number and order fields
      const aOrder = a.sequence_number !== undefined ? a.sequence_number : (a.order || 0);
      const bOrder = b.sequence_number !== undefined ? b.sequence_number : (b.order || 0);
      return aOrder - bOrder;
    });
export const selectUnassignedEvents = (state) => {
  console.log('[DEBUG] selectUnassignedEvents called, events:', state.plot.events);

  // Add more detailed debugging
  console.log('[DEBUG] Events with null chapter_id:',
    state.plot.events.filter(event => event.chapter_id === null));
  console.log('[DEBUG] Events with undefined chapter_id:',
    state.plot.events.filter(event => event.chapter_id === undefined));
  console.log('[DEBUG] Events with empty string chapter_id:',
    state.plot.events.filter(event => event.chapter_id === ''));

  // Filter events that don't have a chapter_id or have a null/empty chapter_id
  const unassignedEvents = state.plot.events.filter(event =>
    !event.chapter_id || event.chapter_id === null || event.chapter_id === '')
    .sort((a, b) => {
      // Support both sequence_number and order fields
      const aOrder = a.sequence_number !== undefined ? a.sequence_number : (a.order || 0);
      const bOrder = b.sequence_number !== undefined ? b.sequence_number : (b.order || 0);
      return aOrder - bOrder;
    });

  console.log('[DEBUG] Filtered unassignedEvents:', unassignedEvents);
  return unassignedEvents;
};
export const selectCurrentEvent = (state) => state.plot.selectedEvent;
export const selectCurrentChapter = (state) => state.plot.selectedChapter;
export const selectPlotLoading = (state) => state.plot.isLoading;
export const selectPlotSaving = (state) => state.plot.isSaving;
export const selectPlotError = (state) => state.plot.error;

// Export reducer
export default plotSlice.reducer;

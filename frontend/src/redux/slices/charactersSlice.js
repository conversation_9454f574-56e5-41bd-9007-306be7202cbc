// src/redux/slices/charactersSlice.js
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import {
  fetchCharacters,
  add<PERSON><PERSON><PERSON>,
  update<PERSON><PERSON><PERSON>,
  deleteCharacter,
  generateCharacter
} from '../../services/apiService';

// Try to load characters from localStorage
const loadCharactersFromStorage = () => {
  try {
    const currentBookId = localStorage.getItem('selectedBookId');
    if (currentBookId) {
      const savedCharacters = localStorage.getItem(`characters_${currentBookId}`);
      if (savedCharacters) {
        const parsedCharacters = JSON.parse(savedCharacters);
        console.log('Loaded characters from localStorage:', parsedCharacters.length);
        return {
          characters: parsedCharacters,
          currentBookId
        };
      }
    }
  } catch (error) {
    console.error('Error loading characters from localStorage:', error);
  }
  return { characters: [], currentBookId: null };
};

// Get saved characters
const { characters: saved<PERSON>haracters, currentBookId: savedBookId } = loadCharactersFromStorage();

// Initial state
const initialState = {
  characters: savedCharacters || [],
  selectedCharacter: null,
  characterRelationships: {},
  isLoading: false,
  isSaving: false,
  error: null,
  lastFetched: null,
  currentBookId: savedBookId
};

// Async thunks
export const fetchAllCharacters = createAsyncThunk(
  'characters/fetchAll',
  async (bookId, { rejectWithValue }) => {
    try {
      const response = await fetchCharacters(bookId);

      // Ensure all characters have the required fields
      if (Array.isArray(response)) {
        const processedCharacters = response.map(character => ({
          ...character,
          gender_identity: character.gender_identity || '',
          sexual_orientation: character.sexual_orientation || ''
        }));
        return processedCharacters;
      }

      return response;
    } catch (error) {
      console.error('Error in fetchAllCharacters thunk:', error);
      return rejectWithValue(error.message || 'Failed to fetch characters');
    }
  }
);

export const createCharacter = createAsyncThunk(
  'characters/create',
  async ({ bookId, characterData }, { rejectWithValue }) => {
    try {
      // Ensure character data has the required fields
      const processedCharacterData = {
        ...characterData,
        gender_identity: characterData.gender_identity || '',
        sexual_orientation: characterData.sexual_orientation || '',
        relationships: Array.isArray(characterData.relationships)
          ? characterData.relationships.map(rel => ({
              ...rel,
              strength: rel.strength !== undefined ? rel.strength : 3 // Default to 3 if not specified
            }))
          : []
      };

      const response = await addCharacter(bookId, processedCharacterData);

      // If the response doesn't include the full character data, merge it with the submitted data
      if (response && response.name && !response.id) {
        // Generate a temporary ID if none exists
        const tempId = `temp_${Date.now()}`;

        return {
          ...processedCharacterData,
          id: tempId,
          // Override with any values from the response
          ...response
        };
      }

      return response;
    } catch (error) {
      console.error('Error in createCharacter thunk:', error);
      return rejectWithValue(error.message || 'Failed to create character');
    }
  }
);

export const updateExistingCharacter = createAsyncThunk(
  'characters/update',
  async ({ bookId, characterId, characterData }, { rejectWithValue }) => {
    try {
      // Ensure character data has the required fields
      const processedCharacterData = {
        ...characterData,
        gender_identity: characterData.gender_identity || '',
        sexual_orientation: characterData.sexual_orientation || '',
        relationships: Array.isArray(characterData.relationships)
          ? characterData.relationships.map(rel => ({
              ...rel,
              strength: rel.strength !== undefined ? rel.strength : 3 // Default to 3 if not specified
            }))
          : []
      };

      // Check if this is a temporary ID (created locally)
      if (characterId && characterId.toString().startsWith('temp_')) {
        console.log('Character has a temporary ID, creating instead of updating:', characterId);
        // This is a new character that hasn't been properly saved to the backend yet
        const response = await addCharacter(bookId, processedCharacterData);
        return {
          ...processedCharacterData,
          ...response,
          id: response.id || characterId // Use the server ID if available, otherwise keep temp ID
        };
      }

      // Normal update for existing characters
      try {
        console.log(`Updating character with ID: ${characterId}`);
        // Make sure the characterId is properly encoded for the URL
        const encodedCharacterId = encodeURIComponent(characterId);
        const response = await updateCharacter(bookId, encodedCharacterId, processedCharacterData);
        return response;
      } catch (updateError) {
        // If we get a 404, the character might not exist yet, so create it
        if (updateError.message && updateError.message.includes('404')) {
          console.log('Character not found, creating instead:', characterId);
          const response = await addCharacter(bookId, processedCharacterData);
          return {
            ...processedCharacterData,
            ...response,
            id: response.id || characterId
          };
        }
        throw updateError;
      }
    } catch (error) {
      console.error('Error updating character:', error);
      return rejectWithValue(error.message || 'Failed to update character');
    }
  }
);

export const removeCharacter = createAsyncThunk(
  'characters/delete',
  async ({ bookId, characterId }, { rejectWithValue }) => {
    try {
      await deleteCharacter(bookId, characterId);
      return characterId;
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to delete character');
    }
  }
);

export const generateAICharacter = createAsyncThunk(
  'characters/generate',
  async ({ bookId, prompt }, { rejectWithValue }) => {
    try {
      const response = await generateCharacter(bookId, prompt);

      // Ensure the generated character has the required fields
      const processedResponse = {
        ...response,
        gender_identity: response.gender_identity || '',
        sexual_orientation: response.sexual_orientation || '',
        relationships: Array.isArray(response.relationships)
          ? response.relationships.map(rel => ({
              ...rel,
              strength: rel.strength !== undefined ? rel.strength : 3 // Default to 3 if not specified
            }))
          : []
      };

      return processedResponse;
    } catch (error) {
      console.error('Error in generateAICharacter thunk:', error);
      return rejectWithValue(error.message || 'Failed to generate character');
    }
  }
);

// Characters slice
const charactersSlice = createSlice({
  name: 'characters',
  initialState,
  reducers: {
    setCurrentBookId: (state, action) => {
      state.currentBookId = action.payload;
      // Save to localStorage
      try {
        localStorage.setItem('selectedBookId', action.payload);
      } catch (error) {
        console.error('Error saving book ID to localStorage:', error);
      }
    },
    selectCharacter: (state, action) => {
      state.selectedCharacter = action.payload;
    },
    clearSelectedCharacter: (state) => {
      state.selectedCharacter = null;
    },
    clearError: (state) => {
      state.error = null;
    },
    addCharacterRelationship: (state, action) => {
      const { characterId, relatedCharacterId, relationshipType, strength = 3 } = action.payload;

      // Initialize relationships for this character if they don't exist
      if (!state.characterRelationships[characterId]) {
        state.characterRelationships[characterId] = [];
      }

      // Add the relationship
      state.characterRelationships[characterId].push({
        relatedCharacterId,
        relationshipType,
        strength, // Include strength with default of 3 (close friend)
        id: `rel_${Date.now()}`
      });
    },
    removeCharacterRelationship: (state, action) => {
      const { characterId, relationshipId } = action.payload;

      if (state.characterRelationships[characterId]) {
        state.characterRelationships[characterId] = state.characterRelationships[characterId]
          .filter(rel => rel.id !== relationshipId);
      }
    },
    updateCharacterRelationship: (state, action) => {
      const { characterId, relationshipId, relationshipType, strength } = action.payload;

      if (state.characterRelationships[characterId]) {
        const index = state.characterRelationships[characterId]
          .findIndex(rel => rel.id === relationshipId);

        if (index !== -1) {
          state.characterRelationships[characterId][index].relationshipType = relationshipType;

          // Update strength if provided
          if (strength !== undefined) {
            state.characterRelationships[characterId][index].strength = strength;
          }
        }
      }
    }
  },
  extraReducers: (builder) => {
    builder
      // Fetch all characters
      .addCase(fetchAllCharacters.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchAllCharacters.fulfilled, (state, action) => {
        state.isLoading = false;

        // The fetchCharacters function in apiService.js should return an array of characters
        // If it doesn't, log an error and use an empty array
        if (Array.isArray(action.payload)) {
          // Process each character to ensure relationship strength is preserved
          state.characters = action.payload.map(character => ({
            ...character,
            relationships: Array.isArray(character.relationships)
              ? character.relationships.map(rel => ({
                  ...rel,
                  strength: rel.strength !== undefined ? rel.strength : 3 // Default to 3 if not specified
                }))
              : []
          }));
          console.log('Characters loaded successfully:', state.characters.length);
        } else {
          console.error('Expected an array of characters but got:', action.payload);
          state.characters = [];
        }

        state.lastFetched = new Date().toISOString();

        // Store characters in localStorage for persistence
        try {
          if (state.currentBookId && state.characters.length > 0) {
            localStorage.setItem('characters_' + state.currentBookId, JSON.stringify(state.characters));
            console.log('Saved characters to localStorage:', state.characters.length);
          }
        } catch (error) {
          console.error('Error saving characters to localStorage:', error);
        }
      })
      .addCase(fetchAllCharacters.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
        // Set characters to empty array to prevent UI from getting stuck
        state.characters = [];
      })

      // Create character
      .addCase(createCharacter.pending, (state) => {
        state.isSaving = true;
        state.error = null;
      })
      .addCase(createCharacter.fulfilled, (state, action) => {
        state.isSaving = false;
        // Ensure characters is an array before pushing
        if (!Array.isArray(state.characters)) {
          state.characters = [];
        }

        console.log('createCharacter.fulfilled with payload:', action.payload);

        // Make sure we have a valid character object
        if (action.payload) {
          // Generate a temporary ID if none exists
          const characterId = action.payload.id || action.payload.character_id || `temp_${Date.now()}`;

          const newCharacter = {
            ...action.payload,
            id: characterId,
            // Ensure these fields are present
            gender_identity: action.payload.gender_identity || '',
            sexual_orientation: action.payload.sexual_orientation || '',
            relationships: Array.isArray(action.payload.relationships)
              ? action.payload.relationships.map(rel => ({
                  ...rel,
                  strength: rel.strength !== undefined ? rel.strength : 3 // Default to 3 if not specified
                }))
              : []
          };

          state.characters = [...state.characters, newCharacter];
          state.selectedCharacter = newCharacter;
          console.log('Character created successfully:', newCharacter);

          // Save to localStorage
          try {
            if (state.currentBookId) {
              localStorage.setItem(`characters_${state.currentBookId}`, JSON.stringify(state.characters));
              console.log('Saved characters to localStorage after creation:', state.characters.length);
            }
          } catch (error) {
            console.error('Error saving characters to localStorage:', error);
          }

          // Clear any errors
          state.error = null;
        } else {
          console.error('Invalid character data received:', action.payload);
          state.error = 'Invalid character data received';
        }
      })
      .addCase(createCharacter.rejected, (state, action) => {
        state.isSaving = false;
        state.error = action.payload;
      })

      // Update character
      .addCase(updateExistingCharacter.pending, (state) => {
        state.isSaving = true;
        state.error = null;
      })
      .addCase(updateExistingCharacter.fulfilled, (state, action) => {
        state.isSaving = false;

        console.log('updateExistingCharacter.fulfilled with payload:', action.payload);

        // Make sure we have a valid character object with an id
        if (action.payload && (action.payload.id || action.payload.character_id)) {
          const updatedCharacter = {
            ...action.payload,
            id: action.payload.id || action.payload.character_id,
            // Ensure these fields are present
            gender_identity: action.payload.gender_identity || '',
            sexual_orientation: action.payload.sexual_orientation || '',
            relationships: Array.isArray(action.payload.relationships)
              ? action.payload.relationships.map(rel => ({
                  ...rel,
                  strength: rel.strength !== undefined ? rel.strength : 3 // Default to 3 if not specified
                }))
              : []
          };

          // Update the character in the characters array
          const index = state.characters.findIndex(char => char.id === updatedCharacter.id);
          if (index !== -1) {
            // Merge with existing character to preserve any fields not in the update
            state.characters[index] = {
              ...state.characters[index],
              ...updatedCharacter
            };
            console.log('Character updated successfully:', state.characters[index]);
          } else {
            // If character not found, add it to the array
            state.characters.push(updatedCharacter);
            console.log('Character not found in state, adding as new:', updatedCharacter);
          }

          // Update selected character if it's the same character
          if (state.selectedCharacter && state.selectedCharacter.id === updatedCharacter.id) {
            state.selectedCharacter = {
              ...state.selectedCharacter,
              ...updatedCharacter
            };
          }

          // Save to localStorage
          try {
            if (state.currentBookId) {
              localStorage.setItem(`characters_${state.currentBookId}`, JSON.stringify(state.characters));
              console.log('Saved characters to localStorage after update:', state.characters.length);
            }
          } catch (error) {
            console.error('Error saving characters to localStorage:', error);
          }

          // If there was an error in the payload, store it
          if (action.payload.error) {
            state.error = action.payload.error;
            console.error('Error in character update:', action.payload.error);
          } else {
            state.error = null;
          }
        } else {
          console.error('Invalid character data received for update:', action.payload);
          state.error = 'Invalid character data received for update';
        }
      })
      .addCase(updateExistingCharacter.rejected, (state, action) => {
        state.isSaving = false;
        state.error = action.payload;
      })

      // Delete character
      .addCase(removeCharacter.pending, (state) => {
        state.isSaving = true;
        state.error = null;
      })
      .addCase(removeCharacter.fulfilled, (state, action) => {
        state.isSaving = false;

        // Remove the character from the characters array
        state.characters = state.characters.filter(char => char.id !== action.payload);

        // Clear selected character if it's the deleted character
        if (state.selectedCharacter && state.selectedCharacter.id === action.payload) {
          state.selectedCharacter = null;
        }

        // Remove character relationships
        delete state.characterRelationships[action.payload];

        // Remove references to this character in other characters' relationships
        Object.keys(state.characterRelationships).forEach(charId => {
          state.characterRelationships[charId] = state.characterRelationships[charId]
            .filter(rel => rel.relatedCharacterId !== action.payload);
        });

        // Save to localStorage
        try {
          if (state.currentBookId) {
            localStorage.setItem(`characters_${state.currentBookId}`, JSON.stringify(state.characters));
          }
        } catch (error) {
          console.error('Error saving characters to localStorage:', error);
        }
      })
      .addCase(removeCharacter.rejected, (state, action) => {
        state.isSaving = false;
        state.error = action.payload;
      })

      // Generate AI character
      .addCase(generateAICharacter.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(generateAICharacter.fulfilled, (state, action) => {
        state.isLoading = false;

        // Process the payload to ensure relationship strength is preserved
        const processedPayload = {
          ...action.payload,
          relationships: Array.isArray(action.payload.relationships)
            ? action.payload.relationships.map(rel => ({
                ...rel,
                strength: rel.strength !== undefined ? rel.strength : 3 // Default to 3 if not specified
              }))
            : []
        };

        // Don't add to characters array yet, wait for explicit creation
        state.selectedCharacter = processedPayload;
      })
      .addCase(generateAICharacter.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      });
  },
});

// Export actions and reducer
export const {
  setCurrentBookId,
  selectCharacter,
  clearSelectedCharacter,
  clearError,
  addCharacterRelationship,
  removeCharacterRelationship,
  updateCharacterRelationship
} = charactersSlice.actions;

export default charactersSlice.reducer;

// Selectors
export const selectAllCharacters = (state) => state.characters.characters;
export const selectCurrentCharacter = (state) => state.characters.selectedCharacter;
export const selectCharacterById = (state, characterId) =>
  state.characters.characters.find(char => char.id === characterId);
export const selectCharacterRelationships = (state, characterId) =>
  state.characters.characterRelationships[characterId] || [];
export const selectAllRelationships = (state) => state.characters.characterRelationships;
export const selectCharactersLoading = (state) => state.characters.isLoading;
export const selectCharactersSaving = (state) => state.characters.isSaving;
export const selectCharactersError = (state) => state.characters.error;
export const selectLastFetched = (state) => state.characters.lastFetched;

// src/redux/slices/brainstormSlice.js
import { createSlice, createAsyncThunk, createSelector } from '@reduxjs/toolkit';
import {
  fetchBrainstormData,
  addBrainstormCard,
  updateBrainstormCard,
  deleteBrainstormCard,
  generateBrainstormIdea,
  sendNodeToPlot,
  fetchWorld,
  generateAIResponse,
  connectBrainstormIdeas,
  fetchAllCharacters
} from '../../services/apiService';

// We no longer need to load deleted card IDs from localStorage
// The backend's deleted_nodes table is the source of truth
const loadDeletedCardIds = () => {
  // Clean up any existing localStorage entries for backward compatibility
  try {
    if (localStorage.getItem('brainstorm_deletedCardIds')) {
      console.log('[DEBUG Brainstorm] Removing deprecated brainstorm_deletedCardIds from localStorage');
      localStorage.removeItem('brainstorm_deletedCardIds');
    }
  } catch (error) {
    console.error('[DEBUG Brainstorm] Error removing deleted card IDs from localStorage:', error);
  }
  return [];
};

// Initial state
const initialState = {
  cards: [],
  pages: ['page1', 'page2', 'page3'],
  selectedCard: null,
  currentPage: 'page1',
  nodes: [], // Visual representation of cards
  edges: [], // Connections between nodes
  isLoading: false,
  isSaving: false,
  error: null,
  lastFetched: null,
  dataLoaded: false, // Flag to track if data has been loaded
  recentlyDeletedCards: [], // Track recently deleted cards to prevent recreation
  // We no longer use deletedCardIds - the backend's deleted_nodes table is the source of truth
  fetchStatus: {
    characters: false,
    world: false,
    brainstorm: false
  } // Track fetch status in Redux instead of component state
};

// Clean up any existing localStorage entries for backward compatibility
loadDeletedCardIds();

// Async thunks
export const fetchAllBrainstormCards = createAsyncThunk(
  'brainstorm/fetchAll',
  async (bookId, { rejectWithValue }) => {
    try {
      console.debug(`Fetching brainstorm data for book ${bookId}`);
      const response = await fetchBrainstormData(bookId);
      return response;
    } catch (error) {
      console.error('Error fetching brainstorm data:', error);
      return rejectWithValue(error.message || 'Failed to fetch brainstorm data');
    }
  }
);

export const createBrainstormCard = createAsyncThunk(
  'brainstorm/createCard',
  async ({ bookId, cardData }, { rejectWithValue }) => {
    try {
      console.debug(`Creating brainstorm card for book ${bookId}:`, {
        title: cardData.title,
        type: cardData.type,
        page: cardData.page
      });
      const response = await addBrainstormCard(bookId, cardData);
      console.debug('Card created successfully:', response.id);
      return response;
    } catch (error) {
      console.error('Error creating brainstorm card:', error);
      return rejectWithValue(error.message || 'Failed to create brainstorm card');
    }
  }
);

export const updateExistingBrainstormCard = createAsyncThunk(
  'brainstorm/updateCard',
  async ({ bookId, cardId, cardData }, { rejectWithValue, getState }) => {
    try {
      // Check if this card was recently deleted
      const state = getState();
      const recentlyDeletedCards = state.brainstorm.recentlyDeletedCards || [];
      const isRecentlyDeleted = recentlyDeletedCards.some(
        entry => entry.id === cardId && entry.deletedAt > Date.now() - 10000
      );

      if (isRecentlyDeleted) {
        console.debug(`Skipping update for recently deleted card ${cardId}`);
        return { skipped: true, id: cardId };
      }

      console.debug(`Updating brainstorm card ${cardId} for book ${bookId}:`, {
        title: cardData.title,
        type: cardData.type,
        page: cardData.page,
        position: cardData.position
      });

      const response = await updateBrainstormCard(bookId, cardId, cardData);

      // If the backend indicates the card was recently deleted, don't update the Redux store
      if (response && response.status === 'skipped') {
        console.debug(`Backend skipped update for recently deleted card ${cardId}`);
        return { skipped: true, id: cardId };
      }

      console.debug('Card updated successfully:', response.id);
      return response;
    } catch (error) {
      console.error('Error updating brainstorm card:', error);
      return rejectWithValue(error.message || 'Failed to update brainstorm card');
    }
  }
);

export const updateBrainstormNode = createAsyncThunk(
  'brainstorm/updateNode',
  async ({ bookId, pageId, nodeId, nodeData }, { rejectWithValue, getState }) => {
    try {
      console.debug(`Updating brainstorm node ${nodeId} on page ${pageId}:`, nodeData);

      // Get current brainstorm data
      const state = getState();
      const currentBrainstorm = state.brainstorm;

      // Find the page and node
      const page = currentBrainstorm.pages.find(p => p.id === pageId);
      if (!page) {
        throw new Error(`Page ${pageId} not found`);
      }

      const nodeIndex = page.nodes.findIndex(n => n.id === nodeId);
      if (nodeIndex === -1) {
        throw new Error(`Node ${nodeId} not found on page ${pageId}`);
      }

      // Update the node
      const updatedNode = {
        ...page.nodes[nodeIndex],
        ...nodeData
      };

      // Save the updated brainstorm
      await updateBrainstormCard(bookId, nodeId, updatedNode);

      return {
        pageId,
        nodeId,
        updatedNode
      };
    } catch (error) {
      console.error('Error updating brainstorm node:', error);
      return rejectWithValue(error.message || 'Failed to update brainstorm node');
    }
  }
);

export const removeBrainstormCard = createAsyncThunk(
  'brainstorm/deleteCard',
  async ({ bookId, cardId }, { rejectWithValue }) => {
    try {
      console.debug(`[DEBUG Brainstorm] Deleting brainstorm card ${cardId} from book ${bookId}`);

      // The backend will handle adding the card to the deleted_nodes table
      await deleteBrainstormCard(bookId, cardId);

      // We now use only node_ format for IDs, no need to handle alternate formats

      console.log(`[DEBUG Brainstorm] Successfully deleted card ${cardId} from backend`);
      return cardId;
    } catch (error) {
      console.error('[DEBUG Brainstorm] Error deleting brainstorm card:', error);
      return rejectWithValue(error.message || 'Failed to delete brainstorm card');
    }
  }
);

// We no longer need this function as we're using the backend's deleted_nodes table
// Keeping it as a no-op for backward compatibility
export const cleanupOldDeletedCardIds = createAsyncThunk(
  'brainstorm/cleanupOldDeletedCardIds',
  async (_, { dispatch }) => {
    console.log('[DEBUG Brainstorm] cleanupOldDeletedCardIds is deprecated - using backend deleted_nodes table');
    return { success: true };
  }
);

export const fetchBrainstormPageData = createAsyncThunk(
  'brainstorm/fetchPageData',
  async (bookId, { dispatch }) => {
    console.debug(`[DEBUG Brainstorm] Fetching all data for book ${bookId}`);

    try {
      // First, clean up old deleted card IDs to reduce unnecessary checks
      await dispatch(cleanupOldDeletedCardIds());

      // Fetch all data sequentially to avoid Promise issues
      // First fetch characters
      const charactersResult = await dispatch(fetchAllCharacters(bookId));
      dispatch(updateFetchStatus({ dataType: 'characters', status: true }));
      console.debug(`[DEBUG Brainstorm] Successfully fetched characters for book ${bookId}`);

      // Then fetch world data
      const worldResult = await dispatch(fetchWorld(bookId));
      dispatch(updateFetchStatus({ dataType: 'world', status: true }));
      console.debug(`[DEBUG Brainstorm] Successfully fetched world data for book ${bookId}`);

      // Finally fetch brainstorm cards
      const brainstormResult = await dispatch(fetchAllBrainstormCards(bookId));
      dispatch(updateFetchStatus({ dataType: 'brainstorm', status: true }));
      console.debug(`[DEBUG Brainstorm] Successfully fetched brainstorm cards for book ${bookId}`);

      console.debug(`[DEBUG Brainstorm] Successfully fetched all data for book ${bookId}`);

      return {
        characters: charactersResult.payload,
        world: worldResult.payload,
        brainstorm: brainstormResult.payload
      };
    } catch (error) {
      console.error('[DEBUG Brainstorm] Error fetching page data:', error);
      throw error;
    }
  }
);

export const generateAIIdea = createAsyncThunk(
  'brainstorm/generateIdea',
  async ({ bookId, page }, { rejectWithValue }) => {
    try {
      console.debug(`[DEBUG Brainstorm] Generating AI ideas for book ${bookId} on page ${page}`);
      const ideas = await generateBrainstormIdea(bookId, page);
      console.debug(`[DEBUG Brainstorm] Received ${ideas.length} AI ideas`);

      // The ideas are already properly formatted by the backend
      // Just return them directly
      return ideas;
    } catch (error) {
      console.error('[DEBUG Brainstorm] Error generating AI ideas:', error);
      return rejectWithValue(error.message || 'Failed to generate AI ideas');
    }
  }
);

export const sendNodeToPLot = createAsyncThunk(
  'brainstorm/sendNodeToPlot',
  async ({ bookId, nodeId, eventData }, { rejectWithValue, dispatch, getState }) => {
    try {
      console.debug(`[DEBUG Brainstorm] Sending brainstorm node ${nodeId} to plot for book ${bookId}`);
      console.debug('[DEBUG Brainstorm] Event data:', eventData);

      // Get the current state to find the node and card
      const state = getState();
      const card = state.brainstorm.cards.find(c => c.id === nodeId);

      if (!card) {
        console.error(`[DEBUG Brainstorm] Card with ID ${nodeId} not found in Redux store`);
      } else {
        console.debug(`[DEBUG Brainstorm] Found card in Redux store:`, card);
      }

      // Send the node to plot
      const response = await sendNodeToPlot(bookId, nodeId, eventData);
      console.debug(`[DEBUG Brainstorm] Response from sendNodeToPlot:`, response);

      // Import the receiveEventFromBrainstorm action from plotSlice
      const { receiveEventFromBrainstorm } = require('../slices/plotSlice');

      // Dispatch the action to update the plot state
      dispatch(receiveEventFromBrainstorm(response));

      // Return both the response and the original card for the reducer
      return { response, originalCard: card };
    } catch (error) {
      console.error('[DEBUG Brainstorm] Error sending node to plot:', error);
      return rejectWithValue(error.message || 'Failed to send node to plot');
    }
  }
);

export const generateAIBrainstormIdea = createAsyncThunk(
  'brainstorm/generateAIBrainstormIdea',
  async ({ bookId, page = 'page1', prompt, cardType }, { rejectWithValue, getState }) => {
    try {
      // Get the current page from state if not provided
      if (!page) {
        const state = getState();
        page = state.brainstorm.currentPage || 'page1';
      }

      console.debug(`[DEBUG Brainstorm] Generating AI brainstorm idea for book ${bookId} on page ${page}`);
      const response = await generateBrainstormIdea(bookId, page, prompt, cardType);
      return response;
    } catch (error) {
      return rejectWithValue(error.message || 'Failed to generate brainstorm idea');
    }
  }
);

export const connectSelectedIdeas = createAsyncThunk(
  'brainstorm/connectSelectedIdeas',
  async ({ bookId, selectedNodes, page }, { rejectWithValue }) => {
    try {
      console.debug(`[DEBUG Brainstorm] Connecting ideas for book ${bookId} on page ${page}`);
      console.debug(`[DEBUG Brainstorm] Selected nodes:`, selectedNodes.map(n => n.id));

      if (!selectedNodes || selectedNodes.length < 2 || selectedNodes.length > 5) {
        throw new Error('Please select between 2 and 5 nodes to connect');
      }

      // Process the selected nodes to extract the necessary information
      const processedNodes = selectedNodes.map(node => {
        // Extract content from node
        let nodeContent = '';
        try {
          if (node.data && node.data.content) {
            nodeContent = node.data.content;
          } else if (typeof node.content === 'string') {
            try {
              const contentObj = JSON.parse(node.content);
              nodeContent = contentObj.data?.content || contentObj.content || node.content;
            } catch {
              nodeContent = node.content;
            }
          } else if (node.content?.data?.content) {
            nodeContent = node.content.data.content;
          } else if (node.content?.content) {
            nodeContent = node.content.content;
          } else {
            nodeContent = JSON.stringify(node.content || node.data || 'No content');
          }
        } catch (e) {
          console.error('Error extracting content from node:', e);
          nodeContent = 'Unknown content';
        }

        return {
          id: node.id,
          title: node.data?.label || node.title || 'Untitled',
          content: nodeContent
        };
      });

      // Call the backend endpoint to connect ideas
      const ideas = await connectBrainstormIdeas(bookId, page, processedNodes);
      console.debug(`[DEBUG Brainstorm] Received ${ideas.length} connected ideas`);

      // The ideas are already properly formatted by the backend
      // Just return them directly
      return ideas;
    } catch (error) {
      console.error('[DEBUG Brainstorm] Error connecting ideas:', error);
      return rejectWithValue(error.message || 'Failed to connect ideas');
    }
  }
);

// Brainstorm slice
const brainstormSlice = createSlice({
  name: 'brainstorm',
  initialState,
  reducers: {
    resetBrainstormState: (state) => {
      // Reset to initial state
      Object.assign(state, initialState);
      state.dataLoaded = false;
      console.log('[DEBUG Brainstorm Reducer] State reset for book change');
    },
    selectCard: (state, action) => {
      state.selectedCard = action.payload;
    },
    clearSelectedCard: (state) => {
      state.selectedCard = null;
    },
    setCurrentPage: (state, action) => {
      state.currentPage = action.payload;
    },
    addPage: (state, action) => {
      if (!state.pages.includes(action.payload)) {
        state.pages.push(action.payload);
      }
    },
    // Add node
    addNode: (state, action) => {
      state.nodes.push(action.payload);
    },
    // Update node position
    updateNodePosition: (state, action) => {
      const { id, position } = action.payload;

      // Validate position data
      if (!position || typeof position.x !== 'number' || typeof position.y !== 'number' ||
          isNaN(position.x) || isNaN(position.y)) {
        console.error('[DEBUG Brainstorm Reducer] Invalid position data in updateNodePosition:', position);
        return;
      }

      // Ensure position values are valid numbers
      const validPosition = {
        x: Math.round(position.x) || 0,
        y: Math.round(position.y) || 0
      };

      console.log('[DEBUG Brainstorm Reducer] Updating node position:', id, validPosition);

      // Check if the node exists in the nodes array
      const nodeIndex = state.nodes.findIndex(node => node.id === id);
      if (nodeIndex !== -1) {
        // Update the node position
        console.log('[DEBUG Brainstorm Reducer] Found node at index:', nodeIndex);
        state.nodes[nodeIndex].position = validPosition;
      } else {
        console.warn('[DEBUG Brainstorm Reducer] Node not found for position update:', id);

        // Create a new node if it doesn't exist
        const card = state.cards.find(card => card.id === id);
        if (card) {
          console.log('[DEBUG Brainstorm Reducer] Creating missing node from existing card:', card);

          // Create a new node with the card data
          const newNode = {
            id: id,
            type: 'cardNode',
            position: validPosition,
            data: {
              title: card.title || '',
              content: card.content || '',
              type: card.type || 'event',
              characters: card.characters || [],
              locations: card.locations || []
            }
          };

          // Add the node to the nodes array
          state.nodes.push(newNode);
          console.log('[DEBUG Brainstorm Reducer] Added new node:', newNode);
        } else {
          console.warn('[DEBUG Brainstorm Reducer] No card found for node:', id);

          // Create a placeholder card and node
          const placeholderCard = {
            id: id,
            title: 'Placeholder Card',
            content: '',
            type: 'event',
            characters: [],
            locations: [],
            page: state.currentPage,
            position: validPosition
          };

          // Add the placeholder card to the cards array
          state.cards.push(placeholderCard);
          console.log('[DEBUG Brainstorm Reducer] Added placeholder card:', placeholderCard);

          // Create a new node with the placeholder card data
          const placeholderNode = {
            id: id,
            type: 'cardNode',
            position: validPosition,
            data: {
              title: 'Placeholder Card',
              content: '',
              type: 'event',
              characters: [],
              locations: []
            }
          };

          // Add the placeholder node to the nodes array
          state.nodes.push(placeholderNode);
          console.log('[DEBUG Brainstorm Reducer] Added placeholder node:', placeholderNode);
        }
      }

      // Update the corresponding card
      const cardIndex = state.cards.findIndex(card => card.id === id);
      if (cardIndex !== -1) {
        console.log('[DEBUG Brainstorm Reducer] Found card at index:', cardIndex);
        state.cards[cardIndex].position = validPosition;
      } else {
        console.warn('[DEBUG Brainstorm Reducer] No card found for node:', id);
      }
    },
    // Add edge
    addEdge: (state, action) => {
      state.edges.push(action.payload);
    },
    // Remove edge
    removeEdge: (state, action) => {
      const { id } = action.payload;
      state.edges = state.edges.filter(edge => edge.id !== id);
    },
    removePage: (state, action) => {
      // Don't remove if it's the last page
      if (state.pages.length > 1) {
        state.pages = state.pages.filter(page => page !== action.payload);

        // If the current page is removed, set the current page to the first page
        if (state.currentPage === action.payload) {
          state.currentPage = state.pages[0];
        }

        // Update cards that were on this page
        state.cards.forEach(card => {
          if (card.page === action.payload) {
            card.page = state.pages[0];
          }
        });
      }
    },
    renamePage: (state, action) => {
      const { oldName, newName } = action.payload;
      const pageIndex = state.pages.findIndex(page => page === oldName);

      if (pageIndex !== -1 && !state.pages.includes(newName)) {
        state.pages[pageIndex] = newName;

        // Update cards that were on this page
        state.cards.forEach(card => {
          if (card.page === oldName) {
            card.page = newName;
          }
        });

        // Update current page if it was renamed
        if (state.currentPage === oldName) {
          state.currentPage = newName;
        }
      }
    },
    moveCardToPage: (state, action) => {
      const { cardId, page } = action.payload;
      const cardIndex = state.cards.findIndex(card => card.id === cardId);

      if (cardIndex !== -1 && state.pages.includes(page)) {
        state.cards[cardIndex].page = page;
      }
    },
    updateCardPosition: (state, action) => {
      const { cardId, position } = action.payload;
      const cardIndex = state.cards.findIndex(card => card.id === cardId);

      if (cardIndex !== -1) {
        state.cards[cardIndex].position = position;
      }
    },
    clearError: (state) => {
      state.error = null;
    },
    // These functions are now deprecated as we're using the backend's deleted_nodes table
    // Keeping them as no-ops for backward compatibility
    clearDeletedCardIds: (state) => {
      console.log('[DEBUG Brainstorm Reducer] clearDeletedCardIds is deprecated - using backend deleted_nodes table');
      // Keep the state property for backward compatibility
      state.deletedCardIds = [];
    },
    cleanupDeletedCardIds: (state) => {
      console.log('[DEBUG Brainstorm Reducer] cleanupDeletedCardIds is deprecated - using backend deleted_nodes table');
      // No-op
    },
    updateFetchStatus: (state, action) => {
      // Update fetch status for a specific data type
      const { dataType, status } = action.payload;
      if (state.fetchStatus && dataType) {
        state.fetchStatus[dataType] = status;
      }
    },
    resetFetchStatus: (state) => {
      // Reset all fetch status flags
      state.fetchStatus = {
        characters: false,
        world: false,
        brainstorm: false
      };
    }

  },
  extraReducers: (builder) => {
    builder
      // Fetch all brainstorm cards
      .addCase(fetchAllBrainstormCards.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchAllBrainstormCards.fulfilled, (state, action) => {
        state.isLoading = false;

        // Ensure action.payload is an array
        let newCards = Array.isArray(action.payload) ? action.payload : [];
        console.log('[DEBUG Brainstorm Reducer] Brainstorm cards received:', newCards.length);

        // We no longer need to filter deleted cards here as the backend already filters them
        // The backend's deleted_nodes table is the source of truth for deleted nodes
        console.log('[DEBUG Brainstorm Reducer] Received cards from backend (already filtered):', newCards.length);

        // Only update the state if we have cards or if data hasn't been loaded yet
        if (newCards.length > 0 || !state.dataLoaded) {
          console.log('[DEBUG Brainstorm Reducer] Updating state with new cards');

          // Create a map of existing cards by ID for faster lookup
          const existingCardsMap = {};
          state.cards.forEach(card => {
            existingCardsMap[card.id] = card;
          });

          // Create a map of new cards by ID
          const newCardsMap = {};
          newCards.forEach(card => {
            newCardsMap[card.id] = card;
          });

          // Check if we're loading a different book
          const isNewBook = state.dataLoaded && state.cards.length > 0 &&
                          !state.cards.some(card => newCardsMap[card.id]);

          if (isNewBook) {
            // All cards are new, likely a different book - replace everything
            state.cards = newCards;
            console.log('[DEBUG Brainstorm Reducer] Replaced all cards with new set');
          } else {
            // Merge existing cards with new cards, preferring new card data
            const mergedCards = [];
            const processedIds = new Set();

            // First add all existing cards, updating them if there's new data
            state.cards.forEach(existingCard => {
              const cardId = existingCard.id;

              // Skip cards that have been deleted
              if (state.deletedCardIds) {
                const isDeleted = state.deletedCardIds.some(entry => {
                  if (typeof entry === 'string') {
                    return entry === cardId;
                  } else if (typeof entry === 'object' && entry.id) {
                    return entry.id === cardId;
                  }
                  return false;
                });

                if (isDeleted) {
                  console.log('[DEBUG Brainstorm Reducer] Skipping deleted card:', cardId);
                  return;
                }
              }

              processedIds.add(cardId);

              if (newCardsMap[cardId]) {
                // Card exists in both sets - use new data but preserve position if not provided
                const newCard = newCardsMap[cardId];
                mergedCards.push({
                  ...newCard,
                  position: newCard.position || existingCard.position
                });
              } else {
                // Card only exists in current state
                mergedCards.push(existingCard);
              }
            });

            // Then add any new cards that weren't in the existing set
            newCards.forEach(newCard => {
              // Skip cards that have been deleted
              if (state.deletedCardIds) {
                const isDeleted = state.deletedCardIds.some(entry => {
                  if (typeof entry === 'string') {
                    return entry === newCard.id;
                  } else if (typeof entry === 'object' && entry.id) {
                    return entry.id === newCard.id;
                  }
                  return false;
                });

                if (isDeleted) {
                  console.log('[DEBUG Brainstorm Reducer] Skipping deleted card:', newCard.id);
                  return;
                }
              }

              if (!processedIds.has(newCard.id)) {
                mergedCards.push(newCard);
              }
            });

            state.cards = mergedCards;
            console.log('[DEBUG Brainstorm Reducer] Merged cards - total count:', mergedCards.length);
          }

          // Generate nodes from all cards
          state.nodes = state.cards.map(card => {
            console.log('Creating node from card:', card);
            return {
              id: card.id,
              type: 'cardNode',
              data: {
                label: card.title || 'Untitled',
                title: card.title || 'Untitled',
                content: card.content || '',
                type: card.type || 'idea',
                characters: card.characters || [],
                locations: card.locations || []
              },
              position: card.position || { x: Math.random() * 300 + 100, y: Math.random() * 300 + 100 }
            };
          });

          // Generate edges if cards have connections
          state.edges = [];
          state.cards.forEach(card => {
            if (card.connections && Array.isArray(card.connections)) {
              card.connections.forEach(connection => {
                state.edges.push({
                  id: `e-${card.id}-${connection.targetId}`,
                  source: card.id,
                  target: connection.targetId,
                  type: connection.type || 'default'
                });
              });
            }
          });

          // Mark data as loaded
          state.dataLoaded = true;
          state.lastFetched = new Date().toISOString();
          console.log('[DEBUG Brainstorm Reducer] State updated with', state.cards.length, 'cards and', state.nodes.length, 'nodes');
        } else {
          console.log('[DEBUG Brainstorm Reducer] Skipping state update - data already loaded with', state.cards.length, 'cards');
        }
      })
      .addCase(fetchAllBrainstormCards.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })

      // Create brainstorm card
      .addCase(createBrainstormCard.pending, (state) => {
        state.isSaving = true;
        state.error = null;
      })
      .addCase(createBrainstormCard.fulfilled, (state, action) => {
        state.isSaving = false;
        console.log('[DEBUG Brainstorm Reducer] Card created successfully:', action.payload.id);

        // Ensure the card has all required fields
        const newCard = {
          ...action.payload,
          title: action.payload.title || 'Untitled',
          content: action.payload.content || '',
          type: action.payload.type || 'idea',
          characters: action.payload.characters || [],
          locations: action.payload.locations || [],
          page: action.payload.page || 'page1',
          position: action.payload.position || { x: Math.random() * 300 + 100, y: Math.random() * 300 + 100 }
        };

        state.cards.push(newCard);
        state.selectedCard = newCard;

        // Create a node for the new card
        const newNode = {
          id: newCard.id,
          type: 'cardNode',
          data: {
            label: newCard.title,
            title: newCard.title,
            content: newCard.content,
            type: newCard.type,
            characters: newCard.characters,
            locations: newCard.locations
          },
          position: newCard.position
        };

        console.log('Created new node with characters:', newCard.characters);

        console.log('[DEBUG Brainstorm Reducer] Adding new node to state:', newNode.id);
        state.nodes.push(newNode);

        // Mark data as loaded
        state.dataLoaded = true;
      })
      .addCase(createBrainstormCard.rejected, (state, action) => {
        state.isSaving = false;
        state.error = action.payload;
      })

      // Update brainstorm card
      .addCase(updateExistingBrainstormCard.pending, (state) => {
        state.isSaving = true;
        state.error = null;
      })
      .addCase(updateExistingBrainstormCard.fulfilled, (state, action) => {
        state.isSaving = false;

        // Check if this update was skipped because the card was recently deleted
        if (action.payload && action.payload.skipped) {
          console.log('[DEBUG Brainstorm Reducer] Skipping update for recently deleted card:', action.payload.id);
          return;
        }

        console.log('[DEBUG Brainstorm Reducer] Card updated successfully:', action.payload.id);

        // Ensure the card has all required fields
        const updatedCard = {
          ...action.payload,
          title: action.payload.title || 'Untitled',
          content: action.payload.content || '',
          type: action.payload.type || 'idea',
          characters: action.payload.characters || [],
          locations: action.payload.locations || [],
          page: action.payload.page || 'page1'
        };

        // Check if this card was recently deleted
        const recentlyDeleted = state.recentlyDeletedCards.some(
          entry => entry.id === updatedCard.id && entry.deletedAt > Date.now() - 10000
        );

        if (recentlyDeleted) {
          console.log('[DEBUG Brainstorm Reducer] Skipping update for recently deleted card:', updatedCard.id);
          return;
        }

        // Update the card in the cards array
        const index = state.cards.findIndex(card => card.id === updatedCard.id);
        if (index !== -1) {
          console.log('[DEBUG Brainstorm Reducer] Updating card in cards array at index:', index);
          state.cards[index] = updatedCard;
        } else {
          console.log('[DEBUG Brainstorm Reducer] Card not found in cards array, adding it');
          state.cards.push(updatedCard);
        }

        // Update selected card if it's the same card
        if (state.selectedCard && state.selectedCard.id === updatedCard.id) {
          state.selectedCard = updatedCard;
        }

        // Update the node for this card
        const nodeIndex = state.nodes.findIndex(node => node.id === updatedCard.id);
        if (nodeIndex !== -1) {
          console.log('[DEBUG Brainstorm Reducer] Updating node in nodes array at index:', nodeIndex);
          state.nodes[nodeIndex] = {
            ...state.nodes[nodeIndex],
            data: {
              label: updatedCard.title,
              title: updatedCard.title,
              content: updatedCard.content,
              type: updatedCard.type,
              characters: updatedCard.characters,
              locations: updatedCard.locations
            },
            // Only update position if it's provided in the payload
            position: updatedCard.position || state.nodes[nodeIndex].position
          };

          console.log('Updated node with characters:', updatedCard.characters);
        } else {
          console.log('[DEBUG Brainstorm Reducer] Node not found in nodes array, adding it');
          // Create a new node if it doesn't exist
          const newNode = {
            id: updatedCard.id,
            type: 'cardNode',
            data: {
              label: updatedCard.title,
              title: updatedCard.title,
              content: updatedCard.content,
              type: updatedCard.type,
              characters: updatedCard.characters,
              locations: updatedCard.locations
            },
            position: updatedCard.position || { x: Math.random() * 300 + 100, y: Math.random() * 300 + 100 }
          };

          console.log('Created new node with characters:', updatedCard.characters);
          state.nodes.push(newNode);
        }

        // Mark data as loaded
        state.dataLoaded = true;
      })
      .addCase(updateExistingBrainstormCard.rejected, (state, action) => {
        state.isSaving = false;
        state.error = action.payload;
      })

      // Update brainstorm node
      .addCase(updateBrainstormNode.pending, (state) => {
        state.isSaving = true;
        state.error = null;
      })
      .addCase(updateBrainstormNode.fulfilled, (state, action) => {
        state.isSaving = false;

        // Find the page
        const pageIndex = state.pages.findIndex(p => p.id === action.payload.pageId);
        if (pageIndex !== -1) {
          // Find the node
          const nodeIndex = state.pages[pageIndex].nodes.findIndex(n => n.id === action.payload.nodeId);
          if (nodeIndex !== -1) {
            // Update the node
            state.pages[pageIndex].nodes[nodeIndex] = action.payload.updatedNode;
          }
        }
      })
      .addCase(updateBrainstormNode.rejected, (state, action) => {
        state.isSaving = false;
        state.error = action.payload;
      })

      // Delete brainstorm card
      .addCase(removeBrainstormCard.pending, (state) => {
        state.isSaving = true;
        state.error = null;
      })
      .addCase(removeBrainstormCard.fulfilled, (state, action) => {
        state.isSaving = false;
        const cardId = action.payload;
        console.log('[DEBUG Brainstorm Reducer] Removing card with ID:', cardId);

        // Get the card before removing it to check for similar cards
        const deletedCard = state.cards.find(card => card.id === cardId);

        // Remove the card from the cards array
        state.cards = state.cards.filter(card => card.id !== cardId);

        // Clear selected card if it's the deleted card
        if (state.selectedCard && state.selectedCard.id === cardId) {
          state.selectedCard = null;
        }

        // Remove the node for this card
        state.nodes = state.nodes.filter(node => node.id !== cardId);

        // Remove any edges connected to this node
        state.edges = state.edges.filter(edge =>
          edge.source !== cardId && edge.target !== cardId
        );

        // Add the card ID to a recently deleted list to prevent immediate recreation
        if (!state.recentlyDeletedCards) {
          state.recentlyDeletedCards = [];
        }
        state.recentlyDeletedCards.push({
          id: cardId,
          deletedAt: Date.now()
        });

        // Add to permanent deleted cards array for this session
        if (!state.deletedCardIds) {
          state.deletedCardIds = [];
        }

        // Check if this ID is already in the list
        const existingEntry = state.deletedCardIds.find(entry =>
          typeof entry === 'object' ? entry.id === cardId : entry === cardId
        );

        if (!existingEntry) {
          // Add new entry with timestamp
          const newEntry = {
            id: cardId,
            deletedAt: Date.now()
          };
          state.deletedCardIds.push(newEntry);
          console.log('[DEBUG Brainstorm Reducer] Added card ID to permanent deleted list:', cardId);
          console.log('[DEBUG Brainstorm Reducer] Total permanently deleted cards:', state.deletedCardIds.length);

          // Save the updated deletedCardIds to localStorage
          try {
            localStorage.setItem('brainstorm_deletedCardIds', JSON.stringify(state.deletedCardIds));
            console.log('[DEBUG Brainstorm Reducer] Saved deleted card IDs to localStorage');
          } catch (error) {
            console.error('[DEBUG Brainstorm Reducer] Error saving deleted card IDs to localStorage:', error);
          }
        } else {
          // Update timestamp for existing entry
          state.deletedCardIds = state.deletedCardIds.map(entry => {
            if (typeof entry === 'object' && entry.id === cardId) {
              return { ...entry, deletedAt: Date.now() };
            } else if (entry === cardId) {
              return { id: cardId, deletedAt: Date.now() };
            }
            return entry;
          });

          // Save the updated list to localStorage
          try {
            localStorage.setItem('brainstorm_deletedCardIds', JSON.stringify(state.deletedCardIds));
            console.log('[DEBUG Brainstorm Reducer] Updated timestamp for deleted card:', cardId);
          } catch (error) {
            console.error('[DEBUG Brainstorm Reducer] Error saving deleted card IDs to localStorage:', error);
          }
        }

        // We've removed the automatic deletion of similar cards to prevent unintended deletions
        // Now we only delete the exact card that the user explicitly wants to delete

        // Clean up old entries from the recently deleted list (older than 10 seconds)
        const tenSecondsAgo = Date.now() - 10000;
        state.recentlyDeletedCards = state.recentlyDeletedCards.filter(
          entry => entry.deletedAt > tenSecondsAgo
        );
      })
      .addCase(removeBrainstormCard.rejected, (state, action) => {
        state.isSaving = false;
        state.error = action.payload;
      })

      // Generate AI brainstorm idea
      .addCase(generateAIBrainstormIdea.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(generateAIBrainstormIdea.fulfilled, (state, action) => {
        state.isLoading = false;
        // Don't add to cards array yet, wait for explicit creation
        state.selectedCard = action.payload;
      })
      .addCase(generateAIBrainstormIdea.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })

      // Generate AI idea
      .addCase(generateAIIdea.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(generateAIIdea.fulfilled, (state, action) => {
        state.isLoading = false;

        // Handle array of ideas
        if (Array.isArray(action.payload)) {
          console.debug(`[DEBUG Brainstorm Reducer] Adding ${action.payload.length} AI-generated ideas`);
          console.debug('[DEBUG Brainstorm Reducer] Payload:', action.payload);

          // Process each idea and add it to both cards and nodes arrays
          // First, filter out duplicates based on title and content
          const uniqueIdeas = [];
          const seenTitles = new Set();

          action.payload.forEach(idea => {
            // Skip ideas with duplicate titles (likely duplicate content)
            if (idea.title && seenTitles.has(idea.title)) {
              console.debug(`[DEBUG Brainstorm Reducer] Skipping duplicate AI idea with title "${idea.title}"`);
              return;
            }

            // Check if a node with similar content already exists in the current state
            const existingNodeWithSimilarContent = state.cards.find(card =>
              card.title === idea.title &&
              JSON.stringify(card.content).includes(idea.content)
            );

            if (existingNodeWithSimilarContent) {
              console.debug(`[DEBUG Brainstorm Reducer] Skipping duplicate of existing node with title "${idea.title}"`);
              return;
            }

            // Add to unique ideas and track the title
            if (idea.title) {
              seenTitles.add(idea.title);
            }
            uniqueIdeas.push(idea);
          });

          console.debug(`[DEBUG Brainstorm Reducer] Filtered ${action.payload.length - uniqueIdeas.length} duplicate ideas, processing ${uniqueIdeas.length} unique ideas`);

          const newCards = uniqueIdeas.filter(idea => {
            // Filter out ideas without IDs instead of generating stable IDs
            if (!idea.id) {
              console.error('[DEBUG Brainstorm Reducer] AI-generated idea missing ID - skipping:', idea);
              return false;
            }
            return true;
          }).map(idea => {

            const card = {
              id: idea.id,
              title: idea.title || '',
              content: idea.content || '',
              type: idea.type || 'event',
              characters: idea.characters || [],
              locations: idea.locations || [],
              page: idea.page || 'page1',
              position: idea.position || { x: Math.random() * 300 + 100, y: Math.random() * 300 + 100 }
            };

            console.debug(`[DEBUG Brainstorm Reducer] Created card with ID: ${card.id}`);
            return card;
          });

          // Add cards to the cards array
          state.cards.push(...newCards);

          // Create nodes from the cards and add them to the nodes array
          const newNodes = newCards.map(card => {
            const node = {
              id: card.id,
              type: 'cardNode',
              position: card.position,
              data: {
                title: card.title,
                content: card.content,
                type: card.type,
                characters: card.characters,
                locations: card.locations
              }
            };

            console.debug(`[DEBUG Brainstorm Reducer] Created node with ID: ${node.id}`);
            return node;
          });

          console.debug(`[DEBUG Brainstorm Reducer] Created ${newNodes.length} nodes from AI ideas`);
          state.nodes.push(...newNodes);

          // Select the first idea
          if (newCards.length > 0) {
            state.selectedCard = newCards[0];
          }
        } else if (action.payload) {
          // Fallback for backward compatibility with single idea
          console.debug('[DEBUG Brainstorm Reducer] Adding single AI-generated idea');

          const idea = action.payload;
          const card = {
            id: idea.id,
            title: idea.title || '',
            content: idea.content || '',
            type: idea.type || 'event',
            characters: idea.characters || [],
            locations: idea.locations || [],
            page: idea.page || 'page1',
            position: idea.position || { x: Math.random() * 300 + 100, y: Math.random() * 300 + 100 }
          };

          // Add card to the cards array
          state.cards.push(card);

          // Create node from the card and add it to the nodes array
          const node = {
            id: card.id,
            type: 'cardNode',
            position: card.position,
            data: {
              title: card.title,
              content: card.content,
              type: card.type,
              characters: card.characters,
              locations: card.locations
            }
          };

          state.nodes.push(node);
          state.selectedCard = card;
        }
      })
      .addCase(generateAIIdea.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })

      // Connect selected ideas
      .addCase(connectSelectedIdeas.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(connectSelectedIdeas.fulfilled, (state, action) => {
        state.isLoading = false;
        state.error = null;

        // The action payload contains the new ideas
        const newIdeas = action.payload;

        if (!Array.isArray(newIdeas) || newIdeas.length === 0) {
          console.warn('[DEBUG Brainstorm Reducer] No valid connected ideas returned from AI');
          return;
        }

        console.debug(`[DEBUG Brainstorm Reducer] Adding ${newIdeas.length} AI-connected ideas`);

        // Filter out any ideas that might already exist (by ID)
        const existingIds = new Set(state.cards.map(card => card.id));
        const uniqueIdeas = newIdeas.filter(idea => !existingIds.has(idea.id));

        if (uniqueIdeas.length < newIdeas.length) {
          console.warn(`[DEBUG Brainstorm Reducer] Filtered out ${newIdeas.length - uniqueIdeas.length} duplicate ideas`);
        }

        // Add the unique ideas to the cards array
        state.cards.push(...uniqueIdeas);

        // Create nodes for the new ideas
        const newNodes = uniqueIdeas.map(idea => ({
          id: idea.id,
          type: 'cardNode',
          position: idea.position,
          data: {
            label: idea.title,
            content: idea.content,
            type: idea.type,
            characters: idea.characters,
            locations: idea.locations
          }
        }));

        // Add the new nodes to the nodes array
        state.nodes.push(...newNodes);

        // Select the first idea
        if (uniqueIdeas.length > 0) {
          state.selectedCard = uniqueIdeas[0];
        }

        // Show success message
        alert(`Created ${uniqueIdeas.length} new connected ideas!`);
      })
      .addCase(connectSelectedIdeas.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload || 'Failed to connect ideas';
      })

      // Send node to plot
      .addCase(sendNodeToPLot.pending, (state) => {
        state.isSaving = true;
        state.error = null;
      })
      .addCase(sendNodeToPLot.fulfilled, (state, action) => {
        state.isSaving = false;

        // Log the successful operation
        console.log('[DEBUG Brainstorm Reducer] Node successfully sent to plot:', action.payload);

        // Extract the response and original card
        const { response, originalCard } = action.payload;

        if (originalCard) {
          console.log('[DEBUG Brainstorm Reducer] Original card found:', originalCard);

          // Make sure the card is still in the cards array
          const cardIndex = state.cards.findIndex(c => c.id === originalCard.id);
          if (cardIndex === -1) {
            console.log('[DEBUG Brainstorm Reducer] Card not found in cards array, adding it back');
            state.cards.push(originalCard);
          } else {
            console.log('[DEBUG Brainstorm Reducer] Card found in cards array at index:', cardIndex);
          }

          // Make sure the node is still in the nodes array
          const nodeIndex = state.nodes.findIndex(n => n.id === originalCard.id);
          if (nodeIndex === -1) {
            console.log('[DEBUG Brainstorm Reducer] Node not found in nodes array, adding it back');
            state.nodes.push({
              id: originalCard.id,
              type: 'cardNode',
              position: originalCard.position || { x: Math.random() * 300 + 100, y: Math.random() * 300 + 100 },
              data: {
                title: originalCard.title || '',
                content: originalCard.content || '',
                type: originalCard.type || 'event',
                characters: originalCard.characters || [],
                locations: originalCard.locations || []
              }
            });
          } else {
            console.log('[DEBUG Brainstorm Reducer] Node found in nodes array at index:', nodeIndex);
          }
        } else {
          console.log('[DEBUG Brainstorm Reducer] No original card found in payload');
        }
      })
      .addCase(sendNodeToPLot.rejected, (state, action) => {
        state.isSaving = false;
        state.error = action.payload;
        console.error('[DEBUG Brainstorm Reducer] Error sending node to plot:', action.payload);
      });
  },
});

// Export actions and reducer


export const {
  selectCard,
  clearSelectedCard,
  setCurrentPage,
  addPage,
  removePage,
  renamePage,
  moveCardToPage,
  updateCardPosition,
  clearError,
  clearDeletedCardIds,
  cleanupDeletedCardIds,
  updateFetchStatus,
  resetFetchStatus,
  // New actions for nodes and edges
  addNode,
  updateNodePosition,
  addEdge,
  removeEdge,
  // Reset action for book changes
  resetBrainstormState
} = brainstormSlice.actions;

export default brainstormSlice.reducer;

// Basic selectors
export const selectAllBrainstormCards = (state) => state.brainstorm.cards;
export const selectCurrentCard = (state) => state.brainstorm.selectedCard;
export const selectAllPages = (state) => state.brainstorm.pages;
export const selectCurrentPage = (state) => state.brainstorm.currentPage;
export const selectBrainstormLoading = (state) => state.brainstorm.isLoading;
export const selectBrainstormSaving = (state) => state.brainstorm.isSaving;
export const selectBrainstormError = (state) => state.brainstorm.error;
export const selectLastFetched = (state) => state.brainstorm.lastFetched;
export const selectCurrentNodeId = (state) => state.brainstorm.selectedCard?.id;
export const selectAllNodes = (state) => state.brainstorm.nodes;
export const selectAllEdges = (state) => state.brainstorm.edges;

// Memoized selectors

// Select cards by page - memoized
export const selectCardsByPage = createSelector(
  [selectAllBrainstormCards, (_, page) => page],
  (cards, page) => {
    console.debug(`Selecting cards for page ${page}`);
    return cards.filter(card => card.page === page);
  }
);

// Select card by ID - memoized
export const selectCardById = createSelector(
  [selectAllBrainstormCards, (_, cardId) => cardId],
  (cards, cardId) => {
    console.debug(`Selecting card with ID ${cardId}`);
    return cards.find(card => card.id === cardId);
  }
);

// Select nodes by page - memoized
export const selectNodesByPage = createSelector(
  [selectAllNodes, selectAllBrainstormCards, (_, page) => page],
  (nodes, cards, page) => {
    console.debug(`Selecting nodes for page ${page}`);
    return nodes.filter(node => {
      const card = cards.find(card => card.id === node.id);
      return card && card.page === page;
    });
  }
);

// Select edges by page - memoized
export const selectEdgesByPage = createSelector(
  [selectAllEdges, selectAllBrainstormCards, (_, page) => page],
  (edges, cards, page) => {
    console.debug(`Selecting edges for page ${page}`);
    return edges.filter(edge => {
      const sourceCard = cards.find(card => card.id === edge.source);
      const targetCard = cards.find(card => card.id === edge.target);
      return sourceCard && targetCard && sourceCard.page === page && targetCard.page === page;
    });
  }
);

// Legacy selector - can be removed once migration is complete
export const selectNodes = createSelector(
  [selectAllBrainstormCards],
  (cards) => {
    console.debug('Using legacy selectNodes selector');
    return cards.map(card => ({
      id: card.id,
      text: card.content,
      characters: card.characters || [],
      type: card.type || 'idea'
    }));
  }
);

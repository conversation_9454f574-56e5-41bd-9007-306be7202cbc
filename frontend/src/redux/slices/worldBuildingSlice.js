// frontend/src/redux/slices/worldBuildingSlice.js
import { createSlice, createAsyncThunk, createSelector } from '@reduxjs/toolkit';
import {
  fetchWorldCategories,
  fetchCategoryCustomizations,
  updateCategoryCustomization,
  resetCategoryCustomizations,
  fetchVisibleCategories,
  fetchWorldRelationships,
  fetchTableSchemas,
  fetchMaps,
  createWorldElement as apiCreateWorldElement,
  fetchChildElements as apiFetchChildElements,
  fetchWorldElements as apiFetchWorldElements,
  fetchWorldElement,
  updateWorldElement,
  deleteWorldElement,
  createWorldRelationship,
  updateWorldRelationship,
  deleteWorldRelationship,
  fetchElementTemplates
} from '../../services/worldBuildingApiService';

// Helper function to load state from localStorage
const loadWorldBuildingFromStorage = () => {
  try {
    const savedBookId = localStorage.getItem('selectedBookId');
    const savedState = localStorage.getItem(`worldBuilding_${savedBookId}`);
    const savedNodePositions = localStorage.getItem(`worldBuilding_nodePositions_${savedBookId}`);
    const savedUserRelationships = localStorage.getItem(`worldBuilding_userRelationships_${savedBookId}`);

    const result = { currentBookId: savedBookId || null };

    if (savedState) {
      Object.assign(result, JSON.parse(savedState));
    }

    if (savedNodePositions) {
      result.nodePositions = JSON.parse(savedNodePositions);
    }

    if (savedUserRelationships) {
      result.userRelationships = JSON.parse(savedUserRelationships);
    }

    return result;
  } catch (error) {
    console.error('Error loading world building data from localStorage:', error);
    return { currentBookId: null };
  }
};

// Get saved state
const { currentBookId: savedBookId, nodePositions: savedNodePositions, userRelationships: savedUserRelationships } = loadWorldBuildingFromStorage();

// Initial state
const initialState = {
  // Core data
  categories: {
    byId: {},
    allIds: [],
    byCategory: {
      cat_cosmology_physical: [],
      cat_cultural_social: [],
      cat_economic_material: [],
      cat_knowledge_technology: [],
      cat_temporal_historical: [],
      cat_magical_supernatural: [],
      cat_interactive_game: []
    },
    universal: [],
    genreSpecific: {}
  },

  customizations: {
    byId: {},
    allIds: []
  },

  elements: {
    byId: {},
    allIds: [],
    byCategory: {},
    byParent: {},
    rootElements: {}
  },

  relationships: {
    byId: {},
    allIds: [],
    bySource: {},
    byTarget: {},
    userCreated: savedUserRelationships || []
  },

  // Template data
  templates: {
    byId: {},
    allIds: [],
    isLoading: false,
    error: null,
    lastFetched: null
  },

  // Table-specific data
  tableSchemas: {
    byId: {},
    byCategoryId: {}
  },

  // UI state
  ui: {
    activeTab: 'universal',
    activeCategory: 'cat_cosmology_physical',
    selectedElementIds: [],
    viewMode: 'card',

    // New property to track whether we're in Templates or Elements tab
    columnTab: 'templates', // 'templates' or 'elements'
    selectedTemplateId: null, // Track the selected template

    relationshipMode: false,
    filters: {
      searchTerm: '',
      tags: [],
      importance: null
    },
    // Store node positions for the relationship view
    nodePositions: savedNodePositions || {}
  },

  // Status flags
  isLoading: false,
  error: null,
  lastFetched: null,
  currentBookId: savedBookId
};

// Async thunks
export const createWorldElement = createAsyncThunk(
  'worldBuilding/createElement',
  async ({ bookId, categoryId, parentId = null, elementType = 'generic', elementData, customFields = {}, onSuccess }, { rejectWithValue }) => {
    try {
      console.log('Redux - Creating world element:', { bookId, categoryId, parentId, elementType, elementData, customFields });

      // Handle both direct elementData and form data from QuickWorldElementForm
      // If elementData has category_id, it's coming from the QuickWorldElementForm
      const isFromQuickForm = elementData && elementData.category_id;

      // Extract data based on the source
      let finalCategoryId = categoryId;
      let finalParentId = parentId;
      let finalElementType = elementType;
      let finalElementData = elementData;
      let finalCustomFields = customFields;
      let hasChildren = false;

      // If data is coming from the QuickWorldElementForm
      if (isFromQuickForm) {
        console.log('Redux - Processing data from QuickWorldElementForm');
        finalCategoryId = elementData.category_id;
        finalParentId = elementData.parent_id || null;
        hasChildren = elementData.has_children || false;

        // Create a clean element data object
        finalElementData = {
          name: elementData.name,
          description: elementData.description || ''
        };
      }

      // Validate required fields
      if (!bookId) {
        throw new Error('Book ID is required');
      }
      if (!finalCategoryId) {
        throw new Error('Category ID is required');
      }
      if (!finalElementData || !finalElementData.name) {
        throw new Error('Element name is required');
      }

      // Validate element type based on parent
      if (finalParentId && finalElementType === 'magic_system') {
        throw new Error('Magic systems cannot be sub-elements');
      }

      // Prepare the data for the API
      const apiData = {
        book_id: bookId,
        category: finalCategoryId, // Use 'category' instead of 'category_id' to match the database schema
        parent_id: finalParentId,
        element_type: finalElementType,
        name: finalElementData.name,
        description: finalElementData.description || '',
        has_children: hasChildren,
        custom_fields: {
          ...finalCustomFields
        }
        // Don't include attributes at all - use custom_fields instead
      };

      // If there are attributes in finalElementData, merge them into custom_fields
      if (finalElementData.attributes) {
        apiData.custom_fields = {
          ...apiData.custom_fields,
          ...finalElementData.attributes
        };
      }

      // Make the API call
      try {
        console.log('Redux - Sending API request to create element:', apiData);
        const response = await apiCreateWorldElement(bookId, apiData);
        console.log('Redux - Created new element via API:', response);

        // Call the onSuccess callback if provided
        if (onSuccess && typeof onSuccess === 'function') {
          console.log('Redux - Calling onSuccess callback with created element:', response);
          onSuccess(response);
        }

        return response;
      } catch (apiError) {
        console.error('Redux - API call failed:', apiError);

        // Don't fall back to local implementation in production
        // Instead, throw the error to be handled by the caller
        throw apiError;
      }
    } catch (error) {
      console.error('Error creating world element:', error);
      return rejectWithValue(error.message || 'Failed to create world element');
    }
  }
);

// No sample data - all data comes from the API

export const fetchWorldBuildingData = createAsyncThunk(
  'worldBuilding/fetchData',
  async (bookId, { rejectWithValue }) => {
    try {
      console.log('Fetching world building data for book:', bookId);

      // Use real API calls instead of sample data
      try {
        // Fetch all required data in parallel
        const [
          categories,
          customizations,
          relationships,
          tableSchemas
        ] = await Promise.all([
          fetchWorldCategories(bookId),
          fetchCategoryCustomizations(bookId),
          fetchWorldRelationships(bookId),
          fetchTableSchemas(bookId)
        ]);

        console.log('Successfully fetched world building data:', {
          categories,
          customizations,
          relationships,
          tableSchemas
        });

        // Debug log relationships
        console.log('Fetched relationships:', relationships);

        return {
          categories,
          customizations,
          relationships,
          tableSchemas,
          elements: [] // Will be fetched separately for each category
        };
      } catch (apiError) {
        console.warn('API call failed, falling back to sample data:', apiError);
        // Log the error and return empty arrays
        console.error('API calls failed, returning empty data:', apiError);
        return {
          categories: [],
          customizations: [],
          elements: [],
          relationships: [],
          tableSchemas: []
        };
      }
    } catch (error) {
      console.error('Error fetching world building data:', error);
      return rejectWithValue(error.message || 'Failed to fetch world building data');
    }
  }
);

export const updateCustomization = createAsyncThunk(
  'worldBuilding/updateCustomization',
  async ({ bookId, categoryId, customizationData }, { rejectWithValue }) => {
    try {
      const result = await updateCategoryCustomization(bookId, categoryId, customizationData);
      return { categoryId, ...result };
    } catch (error) {
      console.error('Error updating category customization:', error);
      return rejectWithValue(error.message || 'Failed to update category customization');
    }
  }
);

export const resetCustomizations = createAsyncThunk(
  'worldBuilding/resetCustomizations',
  async (bookId, { rejectWithValue, dispatch }) => {
    try {
      await resetCategoryCustomizations(bookId);
      // After resetting, fetch the updated customizations
      dispatch(fetchWorldBuildingData(bookId));
      return { success: true };
    } catch (error) {
      console.error('Error resetting category customizations:', error);
      return rejectWithValue(error.message || 'Failed to reset category customizations');
    }
  }
);

export const fetchElementTemplatesThunk = createAsyncThunk(
  'worldBuilding/fetchTemplates',
  async (_, { rejectWithValue }) => {
    try {
      console.log('Fetching element templates');
      const templates = await fetchElementTemplates();
      console.log('Fetched element templates:', templates);
      return templates;
    } catch (error) {
      console.error('Error fetching element templates:', error);
      return rejectWithValue(error.message || 'Failed to fetch element templates');
    }
  }
);

// World Building slice
const worldBuildingSlice = createSlice({
  name: 'worldBuilding',
  initialState,
  reducers: {
    // UI actions
    setActiveTab: (state, action) => {
      state.ui.activeTab = action.payload;
    },
    setActiveCategory: (state, action) => {
      state.ui.activeCategory = action.payload;
    },
    setViewMode: (state, action) => {
      state.ui.viewMode = action.payload;
    },

    // New actions for template/element tabs
    setColumnTab: (state, action) => {
      state.ui.columnTab = action.payload;
    },
    setSelectedTemplate: (state, action) => {
      state.ui.selectedTemplateId = action.payload;
    },

    toggleRelationshipMode: (state) => {
      state.ui.relationshipMode = !state.ui.relationshipMode;
    },

    // Selection actions
    selectElement: (state, action) => {
      state.ui.selectedElementIds = [action.payload];
    },
    addToSelection: (state, action) => {
      if (!state.ui.selectedElementIds.includes(action.payload)) {
        state.ui.selectedElementIds.push(action.payload);
      }
    },
    clearSelection: (state) => {
      state.ui.selectedElementIds = [];
    },

    // Filter actions
    setSearchTerm: (state, action) => {
      state.ui.filters.searchTerm = action.payload;
    },
    addTagFilter: (state, action) => {
      if (!state.ui.filters.tags.includes(action.payload)) {
        state.ui.filters.tags.push(action.payload);
      }
    },
    removeTagFilter: (state, action) => {
      state.ui.filters.tags = state.ui.filters.tags.filter(tag => tag !== action.payload);
    },
    setImportanceFilter: (state, action) => {
      state.ui.filters.importance = action.payload;
    },
    clearFilters: (state) => {
      state.ui.filters = {
        searchTerm: '',
        tags: [],
        importance: null
      };
    },

    // Book change
    setCurrentBookId: (state, action) => {
      state.currentBookId = action.payload;
      // Save to localStorage
      try {
        localStorage.setItem('selectedBookId', action.payload);
      } catch (error) {
        console.error('Error saving book ID to localStorage:', error);
      }
    },

    // Error handling
    clearError: (state) => {
      state.error = null;
    },

    // Node position actions
    updateNodePosition: (state, action) => {
      const { nodeId, position } = action.payload;
      state.ui.nodePositions[nodeId] = position;

      // Save to localStorage
      try {
        const bookId = state.currentBookId;
        if (bookId) {
          const positionsKey = `worldBuilding_nodePositions_${bookId}`;
          const savedPositions = JSON.parse(localStorage.getItem(positionsKey) || '{}');
          savedPositions[nodeId] = position;
          localStorage.setItem(positionsKey, JSON.stringify(savedPositions));
        }
      } catch (error) {
        console.error('Error saving node positions to localStorage:', error);
      }
    },

    updateNodePositions: (state, action) => {
      const positions = action.payload;
      state.ui.nodePositions = { ...state.ui.nodePositions, ...positions };

      // Save to localStorage
      try {
        const bookId = state.currentBookId;
        if (bookId) {
          const positionsKey = `worldBuilding_nodePositions_${bookId}`;
          const savedPositions = JSON.parse(localStorage.getItem(positionsKey) || '{}');
          const updatedPositions = { ...savedPositions, ...positions };
          localStorage.setItem(positionsKey, JSON.stringify(updatedPositions));
        }
      } catch (error) {
        console.error('Error saving node positions to localStorage:', error);
      }
    },

    // Save a user-created relationship
    saveUserRelationship: (state, action) => {
      const relationship = action.payload;

      // Add to userCreated array if not already there
      const exists = state.relationships.userCreated.some(
        r => r.source === relationship.source && r.target === relationship.target
      );

      if (!exists) {
        state.relationships.userCreated.push(relationship);

        // Save to localStorage
        try {
          const bookId = state.currentBookId;
          if (bookId) {
            const relationshipsKey = `worldBuilding_userRelationships_${bookId}`;
            localStorage.setItem(relationshipsKey, JSON.stringify(state.relationships.userCreated));
          }
        } catch (error) {
          console.error('Error saving user relationships to localStorage:', error);
        }
      }
    },

    // Remove a user-created relationship
    removeUserRelationship: (state, action) => {
      const relationshipId = action.payload;

      // Remove from userCreated array
      state.relationships.userCreated = state.relationships.userCreated.filter(
        r => r.id !== relationshipId
      );

      // Remove from byId, bySource, and byTarget
      if (state.relationships.byId[relationshipId]) {
        const relationship = state.relationships.byId[relationshipId];

        // Remove from byId
        delete state.relationships.byId[relationshipId];

        // Remove from allIds
        state.relationships.allIds = state.relationships.allIds.filter(id => id !== relationshipId);

        // Remove from bySource
        if (state.relationships.bySource[relationship.source_element_id]) {
          state.relationships.bySource[relationship.source_element_id] =
            state.relationships.bySource[relationship.source_element_id].filter(id => id !== relationshipId);
        }

        // Remove from byTarget
        if (state.relationships.byTarget[relationship.target_element_id]) {
          state.relationships.byTarget[relationship.target_element_id] =
            state.relationships.byTarget[relationship.target_element_id].filter(id => id !== relationshipId);
        }
      }

      // Save to localStorage
      try {
        const bookId = state.currentBookId;
        if (bookId) {
          const relationshipsKey = `worldBuilding_userRelationships_${bookId}`;
          localStorage.setItem(relationshipsKey, JSON.stringify(state.relationships.userCreated));
        }
      } catch (error) {
        console.error('Error saving user relationships to localStorage:', error);
      }
    }
  },
  extraReducers: (builder) => {
    builder
      // Handle fetchWorldBuildingData
      .addCase(fetchWorldBuildingData.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchWorldBuildingData.fulfilled, (state, action) => {
        state.isLoading = false;
        state.lastFetched = new Date().toISOString();

        // Process categories
        state.categories.byId = {};
        state.categories.allIds = [];
        state.categories.byCategory = {
          cat_cosmology_physical: [],
          cat_cultural_social: [],
          cat_economic_material: [],
          cat_knowledge_technology: [],
          cat_temporal_historical: [],
          cat_magical_supernatural: [],
          cat_interactive_game: []
        };
        state.categories.universal = [];
        state.categories.genreSpecific = {};

        action.payload.categories.forEach(category => {
          state.categories.byId[category.category_id] = category;
          state.categories.allIds.push(category.category_id);

          // Add to byCategory
          state.categories.byCategory[category.category_id] = [];

          if (category.is_universal) {
            state.categories.universal.push(category.category_id);
          } else if (category.applicable_genres && category.applicable_genres.length > 0) {
            category.applicable_genres.forEach(genre => {
              if (!state.categories.genreSpecific[genre]) {
                state.categories.genreSpecific[genre] = [];
              }
              state.categories.genreSpecific[genre].push(category.category_id);
            });
          }
        });

        // Process customizations
        state.customizations.byId = {};
        state.customizations.allIds = [];

        action.payload.customizations.forEach(customization => {
          state.customizations.byId[customization.category_id] = customization;
          state.customizations.allIds.push(customization.category_id);
        });

        // Process relationships
        state.relationships.byId = {};
        state.relationships.bySource = {};
        state.relationships.byTarget = {};

        action.payload.relationships.forEach(relationship => {
          state.relationships.byId[relationship.relationship_id] = relationship;

          if (!state.relationships.bySource[relationship.source_element_id]) {
            state.relationships.bySource[relationship.source_element_id] = [];
          }
          state.relationships.bySource[relationship.source_element_id].push(relationship.relationship_id);

          if (!state.relationships.byTarget[relationship.target_element_id]) {
            state.relationships.byTarget[relationship.target_element_id] = [];
          }
          state.relationships.byTarget[relationship.target_element_id].push(relationship.relationship_id);
        });

        // Process table schemas
        state.tableSchemas.byId = {};
        state.tableSchemas.byCategoryId = {};

        action.payload.tableSchemas.forEach(schema => {
          state.tableSchemas.byId[schema.schema_id] = schema;

          if (!state.tableSchemas.byCategoryId[schema.category_id]) {
            state.tableSchemas.byCategoryId[schema.category_id] = [];
          }
          state.tableSchemas.byCategoryId[schema.category_id].push(schema.schema_id);
        });

        // Process elements
        state.elements.byId = {};
        state.elements.allIds = [];
        state.elements.byCategory = {};
        state.elements.byParent = {};
        state.elements.rootElements = {};

        action.payload.elements.forEach(element => {
          state.elements.byId[element.element_id] = element;
          state.elements.allIds.push(element.element_id);

          // Get the category ID from either category_id or category field
          const categoryId = element.category_id || element.category;

          // Create a new element object with both fields set
          element = {
            ...element,
            category_id: element.category_id || element.category,
            category: element.category || element.category_id
          };

          // Add to category mapping
          if (!state.elements.byCategory[categoryId]) {
            state.elements.byCategory[categoryId] = [];
          }
          state.elements.byCategory[categoryId].push(element.element_id);

          // Add to parent-child mapping
          if (element.parent_id) {
            if (!state.elements.byParent[element.parent_id]) {
              state.elements.byParent[element.parent_id] = [];
            }
            state.elements.byParent[element.parent_id].push(element.element_id);
          } else {
            // Root elements (no parent)
            if (!state.elements.rootElements[categoryId]) {
              state.elements.rootElements[categoryId] = [];
            }
            state.elements.rootElements[categoryId].push(element.element_id);
          }
        });

        // Process relationships
        state.relationships.byId = {};
        state.relationships.allIds = [];
        state.relationships.bySource = {};
        state.relationships.byTarget = {};

        console.log('Processing relationships in reducer:', action.payload.relationships);

        action.payload.relationships.forEach(relationship => {
          console.log('Processing relationship:', relationship);

          // Check for source_id vs source_element_id and target_id vs target_element_id
          const sourceId = relationship.source_id || relationship.source_element_id;
          const targetId = relationship.target_id || relationship.target_element_id;

          if (!sourceId || !targetId) {
            console.error('Relationship missing source or target ID:', relationship);
            return; // Skip this relationship
          }

          // Normalize the relationship object
          const normalizedRelationship = {
            ...relationship,
            source_id: sourceId,
            target_id: targetId,
            relationship_type: relationship.relationship_type || relationship.type
          };

          state.relationships.byId[relationship.relationship_id] = normalizedRelationship;
          state.relationships.allIds.push(relationship.relationship_id);

          // Add to source mapping
          if (!state.relationships.bySource[sourceId]) {
            state.relationships.bySource[sourceId] = [];
          }
          state.relationships.bySource[sourceId].push(relationship.relationship_id);

          // Add to target mapping
          if (!state.relationships.byTarget[targetId]) {
            state.relationships.byTarget[targetId] = [];
          }
          state.relationships.byTarget[targetId].push(relationship.relationship_id);

          console.log('Added relationship to state:', normalizedRelationship);
        });

        // Save to localStorage
        try {
          if (state.currentBookId) {
            localStorage.setItem(`worldBuilding_${state.currentBookId}`, JSON.stringify({
              categories: state.categories,
              customizations: state.customizations,
              relationships: state.relationships,
              tableSchemas: state.tableSchemas
            }));
          }
        } catch (error) {
          console.error('Error saving world building data to localStorage:', error);
        }
      })
      .addCase(fetchWorldBuildingData.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })

      // Handle updateCustomization
      .addCase(updateCustomization.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updateCustomization.fulfilled, (state, action) => {
        state.isLoading = false;

        // Update the customization in state
        const { categoryId, ...customizationData } = action.payload;
        state.customizations.byId[categoryId] = customizationData;

        // If not already in the list, add it
        if (!state.customizations.allIds.includes(categoryId)) {
          state.customizations.allIds.push(categoryId);
        }

        // Save to localStorage
        try {
          if (state.currentBookId) {
            localStorage.setItem(`worldBuilding_${state.currentBookId}`, JSON.stringify({
              categories: state.categories,
              customizations: state.customizations,
              relationships: state.relationships,
              tableSchemas: state.tableSchemas
            }));
          }
        } catch (error) {
          console.error('Error saving world building data to localStorage:', error);
        }
      })
      .addCase(updateCustomization.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })

      // Handle resetCustomizations
      .addCase(resetCustomizations.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(resetCustomizations.fulfilled, (state) => {
        state.isLoading = false;
        // The actual data update will happen in the fetchWorldBuildingData.fulfilled case
      })
      .addCase(resetCustomizations.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })

      // Handle fetchElementTemplatesThunk
      .addCase(fetchElementTemplatesThunk.pending, (state) => {
        state.templates.isLoading = true;
        state.templates.error = null;
      })
      .addCase(fetchElementTemplatesThunk.fulfilled, (state, action) => {
        state.templates.isLoading = false;
        state.templates.lastFetched = Date.now();

        // Reset the templates state
        state.templates.byId = {};
        state.templates.allIds = [];

        // Add the fetched templates
        action.payload.forEach(template => {
          state.templates.byId[template.template_id] = template;
          state.templates.allIds.push(template.template_id);
        });
      })
      .addCase(fetchElementTemplatesThunk.rejected, (state, action) => {
        state.templates.isLoading = false;
        state.templates.error = action.payload;
      })

      // Handle createWorldElement
      .addCase(createWorldElement.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createWorldElement.fulfilled, (state, action) => {
        state.isLoading = false;

        // Add the new element to state
        let newElement = action.payload;

        // Ensure both category and category_id are set
        newElement = {
          ...newElement,
          category_id: newElement.category_id || newElement.category,
          category: newElement.category || newElement.category_id
        };

        // Store the normalized element
        state.elements.byId[newElement.element_id] = newElement;
        state.elements.allIds.push(newElement.element_id);

        // Get the category ID from either category_id or category field
        const categoryId = newElement.category_id || newElement.category;

        // Add to category mapping only if it's a root element (no parent)
        if (!newElement.parent_id) {
          if (!state.elements.byCategory[categoryId]) {
            state.elements.byCategory[categoryId] = [];
          }
          state.elements.byCategory[categoryId].push(newElement.element_id);
        }

        // Handle parent-child relationship
        if (newElement.parent_id) {
          // Add to parent's children
          if (!state.elements.byParent[newElement.parent_id]) {
            state.elements.byParent[newElement.parent_id] = [];
          }
          state.elements.byParent[newElement.parent_id].push(newElement.element_id);

          // Update parent's has_children flag
          if (state.elements.byId[newElement.parent_id]) {
            state.elements.byId[newElement.parent_id].has_children = true;
          }
        } else {
          // Add to root elements
          if (!state.elements.rootElements[categoryId]) {
            state.elements.rootElements[categoryId] = [];
          }
          state.elements.rootElements[categoryId].push(newElement.element_id);
        }

        // Select the new element
        state.ui.selectedElementIds = [newElement.element_id];
      })
      .addCase(createWorldElement.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })

      // Handle fetchElementsForCategoryThunk
      .addCase(fetchElementsForCategoryThunk.pending, (state, action) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchElementsForCategoryThunk.fulfilled, (state, action) => {
        state.isLoading = false;

        const { categoryId, elements } = action.payload;

        // Initialize category lookup if needed
        if (!state.elements.byCategory[categoryId]) {
          state.elements.byCategory[categoryId] = [];
        }

        // Initialize root elements lookup if needed
        if (!state.elements.rootElements[categoryId]) {
          state.elements.rootElements[categoryId] = [];
        }

        // Add each element to the state
        elements.forEach(element => {
          // Create a normalized element with both category and category_id fields
          const normalizedElement = {
            ...element,
            category_id: element.category_id || element.category,
            category: element.category || element.category_id
          };

          // Add to byId lookup
          state.elements.byId[element.element_id] = normalizedElement;

          // Add to allIds if not already there
          if (!state.elements.allIds.includes(element.element_id)) {
            state.elements.allIds.push(element.element_id);
          }

          // Add to category lookup if not already there and only if it's a root element (no parent)
          // This ensures child elements don't appear in the main category list
          if (!element.parent_id && !state.elements.byCategory[categoryId].includes(element.element_id)) {
            state.elements.byCategory[categoryId].push(element.element_id);
          }

          // Add to root elements if it's a root element (no parent)
          if (!element.parent_id && !state.elements.rootElements[categoryId].includes(element.element_id)) {
            state.elements.rootElements[categoryId].push(element.element_id);
          }

          // If it has a parent, make sure the parent's has_children flag is set
          if (element.parent_id && state.elements.byId[element.parent_id]) {
            state.elements.byId[element.parent_id].has_children = true;

            // Add to parent's children if not already there
            if (!state.elements.byParent[element.parent_id]) {
              state.elements.byParent[element.parent_id] = [];
            }
            if (!state.elements.byParent[element.parent_id].includes(element.element_id)) {
              state.elements.byParent[element.parent_id].push(element.element_id);
            }
          }
        });
      })
      .addCase(fetchElementsForCategoryThunk.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload || action.error.message;
      })

      // Handle fetchChildElementsThunk
      .addCase(fetchChildElementsThunk.pending, (state, action) => {
        const parentId = action.meta.arg.parentId;
        if (state.elements.byId[parentId]) {
          state.elements.byId[parentId].loading_children = true;
          state.elements.byId[parentId].children_error = null;
        }
      })
      .addCase(fetchChildElementsThunk.fulfilled, (state, action) => {
        const { parentId, childElements } = action.payload;

        // Clear loading state
        if (state.elements.byId[parentId]) {
          state.elements.byId[parentId].loading_children = false;
        }

        // Initialize parent lookup if needed
        if (!state.elements.byParent[parentId]) {
          state.elements.byParent[parentId] = [];
        }

        // Add each child element to the state
        childElements.forEach(element => {
          // Add to byId lookup
          state.elements.byId[element.element_id] = element;

          // Add to allIds if not already there
          if (!state.elements.allIds.includes(element.element_id)) {
            state.elements.allIds.push(element.element_id);
          }

          // Get the category ID from either category_id or category field
          const categoryId = element.category_id || element.category;

          // Create a new element object with both fields set
          const updatedElement = {
            ...element,
            category_id: element.category_id || element.category,
            category: element.category || element.category_id
          };

          // Update the element in the byId lookup
          state.elements.byId[element.element_id] = updatedElement;

          // Use the updated element for the rest of the function
          element = updatedElement;

          // We don't add child elements to the byCategory array
          // This ensures they only appear as sub-elements under their parent
          // and not in the main elements list

          // Add to parent lookup
          if (!state.elements.byParent[parentId].includes(element.element_id)) {
            state.elements.byParent[parentId].push(element.element_id);
          }
        });

        // Update parent element to indicate it has children
        if (state.elements.byId[parentId] && childElements.length > 0) {
          state.elements.byId[parentId].has_children = true;
        }
      })
      .addCase(fetchChildElementsThunk.rejected, (state, action) => {
        const parentId = action.meta.arg.parentId;

        // Clear loading state and set error
        if (state.elements.byId[parentId]) {
          state.elements.byId[parentId].loading_children = false;
          state.elements.byId[parentId].children_error = action.error.message;
        }
      })

      // Handle updateWorldElementThunk
      .addCase(updateWorldElementThunk.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updateWorldElementThunk.fulfilled, (state, action) => {
        state.isLoading = false;

        // Get the updated element
        const updatedElement = action.payload;

        // Update the element in the state
        if (updatedElement && updatedElement.element_id) {
          // Preserve properties that might not be included in the update
          const existingElement = state.elements.byId[updatedElement.element_id] || {};

          // Merge the existing element with the updated data
          state.elements.byId[updatedElement.element_id] = {
            ...existingElement,
            ...updatedElement
          };
        }
      })
      .addCase(updateWorldElementThunk.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload || action.error.message;
      })

      // Handle deleteWorldElementThunk
      .addCase(deleteWorldElementThunk.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(deleteWorldElementThunk.fulfilled, (state, action) => {
        state.isLoading = false;
        const deletedElementId = action.payload;

        // Remove the element from byId lookup
        if (state.elements.byId[deletedElementId]) {
          // Get the category and parent ID before deleting
          const category = state.elements.byId[deletedElementId].category ||
                          state.elements.byId[deletedElementId].category_id;
          const parentId = state.elements.byId[deletedElementId].parent_id;

          // Delete from byId
          delete state.elements.byId[deletedElementId];

          // Remove from allIds
          state.elements.allIds = state.elements.allIds.filter(id => id !== deletedElementId);

          // Remove from byCategory
          if (category && state.elements.byCategory[category]) {
            state.elements.byCategory[category] = state.elements.byCategory[category].filter(
              id => id !== deletedElementId
            );
          }

          // Remove from rootElements if it's a root element
          if (!parentId && category && state.elements.rootElements[category]) {
            state.elements.rootElements[category] = state.elements.rootElements[category].filter(
              id => id !== deletedElementId
            );
          }

          // Remove from byParent if it has a parent
          if (parentId && state.elements.byParent[parentId]) {
            state.elements.byParent[parentId] = state.elements.byParent[parentId].filter(
              id => id !== deletedElementId
            );

            // Update parent's has_children flag if this was the last child
            if (state.elements.byParent[parentId].length === 0 && state.elements.byId[parentId]) {
              state.elements.byId[parentId].has_children = false;
            }
          }

          // Clear selection if the deleted element was selected
          if (state.ui.selectedElementIds.includes(deletedElementId)) {
            state.ui.selectedElementIds = state.ui.selectedElementIds.filter(
              id => id !== deletedElementId
            );
          }
        }
      })
      .addCase(deleteWorldElementThunk.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload || action.error.message;
      })

      // Handle fetchElementByIdThunk
      .addCase(fetchElementByIdThunk.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchElementByIdThunk.fulfilled, (state, action) => {
        state.isLoading = false;

        // If the element was found, add it to the state
        if (action.payload) {
          const element = action.payload;

          // Normalize the element
          const normalizedElement = {
            ...element,
            category_id: element.category_id || element.category,
            category: element.category || element.category_id
          };

          // Add to byId lookup
          state.elements.byId[element.element_id] = normalizedElement;

          // Add to allIds if not already there
          if (!state.elements.allIds.includes(element.element_id)) {
            state.elements.allIds.push(element.element_id);
          }

          // Add to category lookup if not already there
          const categoryId = normalizedElement.category;
          if (categoryId) {
            if (!state.elements.byCategory[categoryId]) {
              state.elements.byCategory[categoryId] = [];
            }
            if (!state.elements.byCategory[categoryId].includes(element.element_id)) {
              state.elements.byCategory[categoryId].push(element.element_id);
            }

            // Add to root elements if it's a root element (no parent)
            if (!element.parent_id) {
              if (!state.elements.rootElements[categoryId]) {
                state.elements.rootElements[categoryId] = [];
              }
              if (!state.elements.rootElements[categoryId].includes(element.element_id)) {
                state.elements.rootElements[categoryId].push(element.element_id);
              }
            }
          }

          // If it has a parent, update the parent's children
          if (element.parent_id) {
            if (!state.elements.byParent[element.parent_id]) {
              state.elements.byParent[element.parent_id] = [];
            }
            if (!state.elements.byParent[element.parent_id].includes(element.element_id)) {
              state.elements.byParent[element.parent_id].push(element.element_id);
            }

            // Update parent's has_children flag
            if (state.elements.byId[element.parent_id]) {
              state.elements.byId[element.parent_id].has_children = true;
            }
          }
        }
      })
      .addCase(fetchElementByIdThunk.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload || action.error.message;
      })

      // Handle relationship management thunks
      .addCase(createRelationshipThunk.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createRelationshipThunk.fulfilled, (state, action) => {
        state.isLoading = false;

        // Add the new relationship to the state
        const relationship = action.payload;

        // Add to byId lookup
        state.relationships.byId[relationship.relationship_id] = relationship;

        // Add to allIds if not already there
        if (!state.relationships.allIds.includes(relationship.relationship_id)) {
          state.relationships.allIds.push(relationship.relationship_id);
        }

        // Add to bySource lookup
        if (!state.relationships.bySource[relationship.source_element_id]) {
          state.relationships.bySource[relationship.source_element_id] = [];
        }
        if (!state.relationships.bySource[relationship.source_element_id].includes(relationship.relationship_id)) {
          state.relationships.bySource[relationship.source_element_id].push(relationship.relationship_id);
        }

        // Add to byTarget lookup
        if (!state.relationships.byTarget[relationship.target_element_id]) {
          state.relationships.byTarget[relationship.target_element_id] = [];
        }
        if (!state.relationships.byTarget[relationship.target_element_id].includes(relationship.relationship_id)) {
          state.relationships.byTarget[relationship.target_element_id].push(relationship.relationship_id);
        }
      })
      .addCase(createRelationshipThunk.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload || action.error.message;
      })

      .addCase(updateRelationshipThunk.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updateRelationshipThunk.fulfilled, (state, action) => {
        state.isLoading = false;

        // Update the relationship in the state
        const updatedRelationship = action.payload;

        // Update in byId lookup
        state.relationships.byId[updatedRelationship.relationship_id] = updatedRelationship;
      })
      .addCase(updateRelationshipThunk.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload || action.error.message;
      })

      .addCase(deleteRelationshipThunk.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(deleteRelationshipThunk.fulfilled, (state, action) => {
        state.isLoading = false;

        // Get the deleted relationship ID
        const deletedRelationshipId = action.payload;

        // Get the relationship before removing it
        const relationship = state.relationships.byId[deletedRelationshipId];

        if (relationship) {
          // Remove from bySource lookup
          if (state.relationships.bySource[relationship.source_element_id]) {
            state.relationships.bySource[relationship.source_element_id] =
              state.relationships.bySource[relationship.source_element_id].filter(
                id => id !== deletedRelationshipId
              );
          }

          // Remove from byTarget lookup
          if (state.relationships.byTarget[relationship.target_element_id]) {
            state.relationships.byTarget[relationship.target_element_id] =
              state.relationships.byTarget[relationship.target_element_id].filter(
                id => id !== deletedRelationshipId
              );
          }

          // Remove from byId lookup
          delete state.relationships.byId[deletedRelationshipId];

          // Remove from allIds
          state.relationships.allIds = state.relationships.allIds.filter(
            id => id !== deletedRelationshipId
          );
        }
      })
      .addCase(deleteRelationshipThunk.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload || action.error.message;
      });
  }
});

// Export actions
export const {
  setActiveTab,
  setActiveCategory,
  setViewMode,
  setColumnTab,
  setSelectedTemplate,
  toggleRelationshipMode,
  selectElement,
  addToSelection,
  clearSelection,
  setSearchTerm,
  addTagFilter,
  removeTagFilter,
  setImportanceFilter,
  clearFilters,
  setCurrentBookId,
  clearError,
  updateNodePosition,
  updateNodePositions,
  saveUserRelationship,
  removeUserRelationship
} = worldBuildingSlice.actions;

// Basic selectors
export const selectWorldBuildingState = state => state.worldBuilding;
export const selectCategories = state => selectWorldBuildingState(state).categories;
export const selectCustomizations = state => selectWorldBuildingState(state).customizations;
export const selectRelationships = state => selectWorldBuildingState(state).relationships;
export const selectTableSchemas = state => selectWorldBuildingState(state).tableSchemas;
export const selectMaps = state => selectWorldBuildingState(state).maps;
export const selectWorldBuildingUI = state => selectWorldBuildingState(state).ui;
export const selectIsLoading = state => selectWorldBuildingState(state).isLoading;
export const selectError = state => selectWorldBuildingState(state).error;
export const selectCurrentBookId = state => selectWorldBuildingState(state).currentBookId;
export const selectNodePositions = state => selectWorldBuildingState(state).ui.nodePositions;
export const selectUserRelationships = state => selectWorldBuildingState(state).relationships.userCreated;

// New selectors for template/element tabs
export const selectColumnTab = state => selectWorldBuildingState(state).ui.columnTab;
export const selectSelectedTemplateId = state => selectWorldBuildingState(state).ui.selectedTemplateId;

// Selector to get all categories as an array
export const selectAllCategories = createSelector(
  [selectCategories],
  (categories) => {
    return categories.allIds.map(id => categories.byId[id]).filter(Boolean);
  }
);

// Selector to get all customizations as an array
export const selectAllCustomizations = createSelector(
  [selectCustomizations],
  (customizations) => {
    return customizations.allIds.map(id => ({
      category_id: id,
      ...customizations.byId[id]
    })).filter(Boolean);
  }
);

// Derived selectors
export const selectVisibleCategories = createSelector(
  [selectCategories, selectCustomizations, selectWorldBuildingUI],
  (categories, customizations, ui) => {
    // Always show all categories regardless of activeTab
    let filteredIds = [...categories.allIds]; // Create a copy to avoid mutations

    // Filter by aspect if one is selected
    if (ui.activeAspect && categories.byAspect[ui.activeAspect]) {
      filteredIds = filteredIds.filter(id =>
        categories.byAspect[ui.activeAspect].includes(id)
      );
    }

    // Filter by customization (enabled/disabled)
    filteredIds = filteredIds.filter(id => {
      const customization = customizations.byId[id];
      return customization ? customization.is_enabled : true;
    });

    // Remove any duplicates that might have slipped through
    const uniqueIds = [...new Set(filteredIds)];

    // Convert IDs to category objects
    return uniqueIds.map(id => categories.byId[id]).filter(Boolean);
  }
);

export const selectActiveCategory = createSelector(
  [selectCategories, selectWorldBuildingUI],
  (categories, ui) => ui.activeCategory ? categories.byId[ui.activeCategory] : null
);

export const selectSelectedElements = createSelector(
  [state => state.worldBuilding.elements.byId, selectWorldBuildingUI],
  (elementsById, ui) => ui.selectedElementIds.map(id => elementsById[id]).filter(Boolean)
);

export const selectRootElementsForCategory = createSelector(
  [
    state => state.worldBuilding.elements.byId,
    state => state.worldBuilding.elements.byCategory,
    selectActiveCategory
  ],
  (elementsById, elementsByCategory, activeCategory) => {
    if (!activeCategory) return [];

    const categoryId = activeCategory.category_id;
    const elementIds = elementsByCategory[categoryId] || [];

    // Get the elements, filter out those with parent_id, and ensure they have the correct category field
    return elementIds
      .map(id => {
        const element = elementsById[id];
        if (!element) return null;

        // Skip elements that have a parent_id (they should only appear as sub-elements)
        if (element.parent_id) return null;

        // Create a new object with both category fields
        // instead of modifying the existing object
        return {
          ...element,
          category_id: element.category_id || element.category,
          category: element.category || element.category_id
        };
      })
      .filter(Boolean);
  }
);

// Selector to get all elements (including child elements) for the relationship view
export const selectAllElementsForRelationshipView = createSelector(
  [
    state => state.worldBuilding.elements.byId,
    state => state.worldBuilding.elements.allIds,
    state => state.worldBuilding.elements.byParent,
    state => state.worldBuilding.currentBookId
  ],
  (elementsById, allIds, elementsByParent, currentBookId) => {
    console.log('selectAllElementsForRelationshipView - elementsById:', Object.keys(elementsById).length);
    console.log('selectAllElementsForRelationshipView - allIds:', allIds.length);

    // Handle empty state
    if (!allIds || allIds.length === 0) {
      console.log('selectAllElementsForRelationshipView - No elements found');
      return [];
    }

    // Get all elements for the current book
    const elements = allIds
      .map(id => elementsById[id])
      .filter(element => element && element.book_id === currentBookId);
    console.log('selectAllElementsForRelationshipView - elements after filtering:', elements.length);

    // Add parent-child relationships to each element
    return elements.map(element => {
      if (!element || !element.element_id) {
        console.log('selectAllElementsForRelationshipView - Invalid element:', element);
        return null;
      }

      // Check if this element has a parent
      const parentId = element.parent_id;
      const hasParent = !!parentId && !!elementsById[parentId];

      // Check if this element has children
      const childIds = elementsByParent[element.element_id] || [];
      const hasChildren = childIds.length > 0;

      // Add parent and children information to the element
      return {
        ...element,
        hasParent,
        parentElement: hasParent ? elementsById[parentId] : null,
        hasChildren,
        childElements: hasChildren ? childIds.map(id => elementsById[id]).filter(Boolean) : []
      };
    }).filter(Boolean); // Filter out any null elements
  }
);

export const selectElementsForCategory = createSelector(
  [state => state.worldBuilding.elements.byId, state => state.worldBuilding.elements.byCategory, selectActiveCategory],
  (elementsById, elementsByCategory, activeCategory) => {
    if (!activeCategory) return [];

    const categoryId = activeCategory.category_id;
    const elementIds = elementsByCategory[categoryId] || [];

    // Get the elements and ensure they have the correct category field
    return elementIds.map(id => {
      const element = elementsById[id];
      if (!element) return null;

      // Create a new object with both category fields
      // instead of modifying the existing object
      return {
        ...element,
        category_id: element.category_id || element.category,
        category: element.category || element.category_id
      };
    }).filter(Boolean);
  }
);

export const selectChildElements = createSelector(
  [state => state.worldBuilding.elements.byId, state => state.worldBuilding.elements.byParent, (state, parentId) => parentId],
  (elementsById, elementsByParent, parentId) => {
    if (!parentId) return [];

    const childIds = elementsByParent[parentId] || [];
    return childIds.map(id => elementsById[id]).filter(Boolean);
  }
);

// Selector to get a template by ID
export const selectTemplateById = createSelector(
  [state => state.worldBuilding.templates.byId, (state, templateId) => templateId],
  (templatesById, templateId) => templatesById[templateId] || null
);

// Selector to get a template by element type
export const selectTemplateByType = createSelector(
  [state => state.worldBuilding.templates.byId],
  (templatesById) => (elementType) => {
    // Find a template with a matching template_name
    const templateId = Object.keys(templatesById).find(id =>
      templatesById[id].template_name === elementType
    );

    return templateId ? templatesById[templateId] : null;
  }
);

// Note: We already have a selectAllElementsForRelationshipView selector defined above

export const selectElementRelationships = createSelector(
  [
    state => state.worldBuilding.relationships.byId,
    state => state.worldBuilding.relationships.bySource,
    state => state.worldBuilding.relationships.byTarget,
    state => state.worldBuilding.elements.byId,
    (state, elementId) => elementId
  ],
  (relationshipsById, relationshipsBySource, relationshipsByTarget, elementsById, elementId) => {
    if (!elementId) return [];

    // Get relationships where this element is the source
    const sourceRelationshipIds = relationshipsBySource[elementId] || [];
    const sourceRelationships = sourceRelationshipIds.map(id => {
      const relationship = relationshipsById[id];
      if (!relationship) return null;

      return {
        ...relationship,
        direction: 'outgoing',
        otherElement: elementsById[relationship.target_id]
      };
    }).filter(Boolean);

    // Get relationships where this element is the target
    const targetRelationshipIds = relationshipsByTarget[elementId] || [];
    const targetRelationships = targetRelationshipIds.map(id => {
      const relationship = relationshipsById[id];
      if (!relationship) return null;

      return {
        ...relationship,
        direction: 'incoming',
        otherElement: elementsById[relationship.source_id]
      };
    }).filter(Boolean);

    return [...sourceRelationships, ...targetRelationships];
  }
);

// Selector to get all relationships for the relationship view
export const selectAllRelationships = createSelector(
  [state => state.worldBuilding.relationships.byId, state => state.worldBuilding.relationships.allIds],
  (relationshipsById, allIds) => {
    console.log('selectAllRelationships - relationshipsById:', relationshipsById);
    console.log('selectAllRelationships - allIds:', allIds);

    const relationships = allIds.map(id => relationshipsById[id]).filter(Boolean);
    console.log('selectAllRelationships - returning relationships:', relationships);

    return relationships;
  }
);

// Async thunk to fetch elements for a category
export const fetchElementsForCategoryThunk = createAsyncThunk(
  'worldBuilding/fetchElementsForCategory',
  async ({ bookId, categoryId }, { rejectWithValue }) => {
    try {
      console.log('Redux - Fetching elements for category:', { bookId, categoryId });

      try {
        // Make the API call
        const elements = await apiFetchWorldElements(bookId, categoryId);
        console.log('Redux - Fetched elements for category:', elements);
        return { categoryId, elements };
      } catch (apiError) {
        console.warn('API call failed, falling back to local implementation:', apiError);

        // Fallback to local implementation for development
        return { categoryId, elements: [] };
      }
    } catch (error) {
      console.error('Error fetching elements for category:', error);
      return rejectWithValue(error.message || 'Failed to fetch elements for category');
    }
  }
);

// Async thunk to fetch child elements
export const fetchChildElementsThunk = createAsyncThunk(
  'worldBuilding/fetchChildElements',
  async ({ bookId, parentId }, { rejectWithValue }) => {
    try {
      console.log('Redux - Fetching child elements for parent:', parentId);

      try {
        // Make the API call
        // Use the API service function
        const childElements = await apiFetchChildElements(bookId, parentId);
        console.log('Redux - Fetched child elements:', childElements);
        return { parentId, childElements };
      } catch (apiError) {
        console.warn('API call failed, falling back to local implementation:', apiError);

        // Fallback to local implementation for development
        return { parentId, childElements: [] };
      }
    } catch (error) {
      console.error('Error fetching child elements:', error);
      return rejectWithValue(error.message || 'Failed to fetch child elements');
    }
  }
);

// Async thunk to update a world element
export const updateWorldElementThunk = createAsyncThunk(
  'worldBuilding/updateElement',
  async ({ bookId, elementId, elementData }, { rejectWithValue }) => {
    try {
      console.log('Redux - Updating world element:', { elementId, elementData });

      // Validate required fields
      if (!bookId) {
        throw new Error('Book ID is required');
      }
      if (!elementId) {
        throw new Error('Element ID is required');
      }
      if (!elementData) {
        throw new Error('Element data is required');
      }

      try {
        // Make the API call
        const updatedElement = await updateWorldElement(bookId, elementId, elementData);
        console.log('Redux - Updated element via API:', updatedElement);
        return updatedElement;
      } catch (apiError) {
        console.warn('API call failed, falling back to local implementation:', apiError);

        // Fallback to local implementation for development
        // Return the updated element data with the element ID
        return {
          element_id: elementId,
          ...elementData
        };
      }
    } catch (error) {
      console.error('Error updating world element:', error);
      return rejectWithValue(error.message || 'Failed to update world element');
    }
  }
);

// Async thunk to delete a world element
export const deleteWorldElementThunk = createAsyncThunk(
  'worldBuilding/deleteElement',
  async ({ bookId, elementId }, { rejectWithValue }) => {
    try {
      console.log('Redux - Deleting world element:', { bookId, elementId });

      // Validate required fields
      if (!bookId) {
        throw new Error('Book ID is required');
      }
      if (!elementId) {
        throw new Error('Element ID is required');
      }

      try {
        // Make the API call
        await deleteWorldElement(bookId, elementId);
        console.log('Redux - Deleted element via API:', elementId);
        return elementId;
      } catch (apiError) {
        console.warn('API call failed, falling back to local implementation:', apiError);

        // Fallback to local implementation for development
        return elementId;
      }
    } catch (error) {
      console.error('Error deleting world element:', error);
      return rejectWithValue(error.message || 'Failed to delete world element');
    }
  }
);

// Async thunk to fetch a specific element by ID
export const fetchElementByIdThunk = createAsyncThunk(
  'worldBuilding/fetchElementById',
  async ({ bookId, elementId }, { rejectWithValue }) => {
    try {
      console.log('Redux - Fetching element by ID:', { bookId, elementId });

      try {
        // Make the API call
        const element = await fetchWorldElement(bookId, elementId);
        console.log('Redux - Fetched element by ID:', element);
        return element;
      } catch (apiError) {
        console.warn('API call failed, falling back to local implementation:', apiError);

        // Fallback to local implementation for development
        return null;
      }
    } catch (error) {
      console.error('Error fetching element by ID:', error);
      return rejectWithValue(error.message || 'Failed to fetch element by ID');
    }
  }
);

// Relationship management thunks
export const createRelationshipThunk = createAsyncThunk(
  'worldBuilding/createRelationship',
  async ({ bookId, relationshipData }, { rejectWithValue }) => {
    try {
      console.log('Redux - Creating relationship:', { bookId, relationshipData });
      const relationship = await createWorldRelationship(bookId, relationshipData);
      return relationship;
    } catch (error) {
      console.error('Error creating relationship:', error);
      return rejectWithValue(error.message || 'Failed to create relationship');
    }
  }
);

export const updateRelationshipThunk = createAsyncThunk(
  'worldBuilding/updateRelationship',
  async ({ bookId, relationshipId, relationshipData }, { rejectWithValue }) => {
    try {
      console.log('Redux - Updating relationship:', { bookId, relationshipId, relationshipData });
      const relationship = await updateWorldRelationship(bookId, relationshipId, relationshipData);
      return relationship;
    } catch (error) {
      console.error('Error updating relationship:', error);
      return rejectWithValue(error.message || 'Failed to update relationship');
    }
  }
);

export const deleteRelationshipThunk = createAsyncThunk(
  'worldBuilding/deleteRelationship',
  async ({ bookId, relationshipId }, { rejectWithValue }) => {
    try {
      console.log('Redux - Deleting relationship:', { bookId, relationshipId });
      await deleteWorldRelationship(bookId, relationshipId);
      return relationshipId;
    } catch (error) {
      console.error('Error deleting relationship:', error);
      return rejectWithValue(error.message || 'Failed to delete relationship');
    }
  }
);

// Async thunk to fetch all elements with their children for the relationship view
export const fetchAllElementsWithChildrenThunk = createAsyncThunk(
  'worldBuilding/fetchAllElementsWithChildren',
  async ({ bookId }, { dispatch, getState, rejectWithValue }) => {
    try {
      console.log('Redux - Fetching all elements with children for relationship view, bookId:', bookId);

      // First, get all categories
      const state = getState();
      const categories = state.worldBuilding.categories.allIds.map(id => state.worldBuilding.categories.byId[id]);
      console.log('Redux - Categories found:', categories.length);

      // If no categories are found, fetch world building data first
      if (categories.length === 0) {
        console.log('Redux - No categories found, fetching world building data first');
        await dispatch(fetchWorldBuildingData(bookId)).unwrap();

        // Get updated categories
        const updatedState = getState();
        const updatedCategories = updatedState.worldBuilding.categories.allIds
          .map(id => updatedState.worldBuilding.categories.byId[id]);
        console.log('Redux - Updated categories after fetch:', updatedCategories.length);

        // If still no categories, try to use default categories
        if (updatedCategories.length === 0) {
          console.log('Redux - Still no categories, using default categories');
          // Use places as a default category
          await dispatch(fetchElementsForCategoryThunk({
            bookId,
            categoryId: 'places'
          }));
        } else {
          // Fetch elements for each category
          const categoryPromises = updatedCategories.map(category => {
            return dispatch(fetchElementsForCategoryThunk({
              bookId,
              categoryId: category.category_id
            })).unwrap();
          });

          // Wait for all category fetches to complete
          await Promise.all(categoryPromises);
        }
      } else {
        // Fetch elements for each category
        const categoryPromises = categories.map(category => {
          return dispatch(fetchElementsForCategoryThunk({
            bookId,
            categoryId: category.category_id
          })).unwrap();
        });

        // Wait for all category fetches to complete
        await Promise.all(categoryPromises);
      }

      // Get all root elements that have children
      const updatedState = getState();
      const rootElements = updatedState.worldBuilding.elements.allIds
        .map(id => updatedState.worldBuilding.elements.byId[id])
        .filter(element => !element.parent_id && element.has_children);

      console.log('Redux - Root elements with children:', rootElements.length);

      // Fetch children for each root element
      const childPromises = rootElements.map(element => {
        return dispatch(fetchChildElementsThunk({
          bookId,
          parentId: element.element_id
        })).unwrap();
      });

      // Wait for all child fetches to complete
      await Promise.all(childPromises);

      // Log the final state of elements
      const finalState = getState();
      const allElements = finalState.worldBuilding.elements.allIds
        .map(id => finalState.worldBuilding.elements.byId[id]);
      console.log('Redux - Total elements after fetch:', allElements.length);

      return { success: true };
    } catch (error) {
      console.error('Error fetching all elements with children:', error);
      return rejectWithValue(error.message || 'Failed to fetch all elements with children');
    }
  }
);

// Export reducer
export default worldBuildingSlice.reducer;

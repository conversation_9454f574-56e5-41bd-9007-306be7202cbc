// src/redux/store/index.js
import { configureStore } from '@reduxjs/toolkit';
import { thunk } from 'redux-thunk';
import loggerMiddleware from '../middleware/loggerMiddleware';
import bookSelectionMiddleware from '../middleware/bookSelectionMiddleware';

// Import reducers
// We'll add these as we create them
import authReducer from '../slices/authSlice';
import bookReducer from '../slices/bookSlice';
// Old world slice has been removed
// import worldReducer from '../slices/worldSlice';
import worldBuildingReducer from '../slices/worldBuildingSlice';
import charactersReducer from '../slices/charactersSlice';
import plotReducer from '../slices/plotSlice';
import brainstormReducer from '../slices/brainstormSlice';
import aiReducer from '../slices/aiSlice';
import writeReducer from '../slices/writeSlice';
import referenceReducer from '../slices/referenceSlice';
import semanticSearchReducer from '../slices/semanticSearchSlice';
// import uiReducer from '../slices/uiSlice';

// Create root reducer
const rootReducer = {
  auth: authReducer,
  books: bookReducer,
  // Old world reducer has been removed
  // world: worldReducer,
  worldBuilding: worldBuildingReducer,
  characters: charactersReducer,
  plot: plotReducer,
  brainstorm: brainstormReducer,
  ai: aiReducer,
  write: writeReducer,
  reference: referenceReducer,
  semanticSearch: semanticSearchReducer,
};

// Configure store with middleware
const store = configureStore({
  reducer: rootReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        // Ignore these action types
        ignoredActions: ['auth/login/fulfilled', 'auth/logout/fulfilled', 'write/updateEditorState'],
        // Ignore these field paths in all actions
        ignoredActionPaths: ['meta.arg', 'payload.timestamp', 'payload.editorState'],
        // Ignore these paths in the state
        ignoredPaths: ['auth.user', 'books.selectedBook', 'write.editorState'],
      },
    }).concat(thunk, loggerMiddleware, bookSelectionMiddleware),
  devTools: process.env.NODE_ENV !== 'production',
});

export default store;

// src/redux/tests/authSlice.test.js
import authReducer, { login, logout, clearError } from '../slices/authSlice';
import { configureStore } from '@reduxjs/toolkit';
import thunk from 'redux-thunk';
import * as apiService from '../../services/apiService';

// Mock the API service
jest.mock('../../services/apiService');

describe('Auth Slice', () => {
  let store;

  beforeEach(() => {
    // Create a fresh store for each test
    store = configureStore({
      reducer: { auth: authReducer },
      middleware: [thunk],
    });
  });

  it('should handle initial state', () => {
    expect(store.getState().auth).toEqual({
      token: null,
      user: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,
    });
  });

  it('should handle login success', async () => {
    // Mock the API response
    const mockUser = { id: 1, username: 'testuser' };
    const mockToken = 'test-token';
    apiService.loginUser.mockResolvedValue({ user: mockUser, token: mockToken });

    // Dispatch the login action
    await store.dispatch(login({ username: 'testuser', password: 'password' }));

    // Check the resulting state
    expect(store.getState().auth).toEqual({
      token: mockToken,
      user: mockUser,
      isAuthenticated: true,
      isLoading: false,
      error: null,
    });
  });

  it('should handle login failure', async () => {
    // Mock the API error
    const errorMessage = 'Invalid credentials';
    apiService.loginUser.mockRejectedValue(new Error(errorMessage));

    // Dispatch the login action
    await store.dispatch(login({ username: 'testuser', password: 'wrong' }));

    // Check the resulting state
    expect(store.getState().auth).toEqual({
      token: null,
      user: null,
      isAuthenticated: false,
      isLoading: false,
      error: errorMessage,
    });
  });

  it('should handle logout', async () => {
    // Set up initial state with a logged-in user
    store = configureStore({
      reducer: { auth: authReducer },
      middleware: [thunk],
      preloadedState: {
        auth: {
          token: 'test-token',
          user: { id: 1, username: 'testuser' },
          isAuthenticated: true,
          isLoading: false,
          error: null,
        },
      },
    });

    // Mock the API response
    apiService.logoutUser.mockResolvedValue({});

    // Dispatch the logout action
    await store.dispatch(logout());

    // Check the resulting state
    expect(store.getState().auth).toEqual({
      token: null,
      user: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,
    });
  });

  it('should clear error', () => {
    // Set up initial state with an error
    store = configureStore({
      reducer: { auth: authReducer },
      middleware: [thunk],
      preloadedState: {
        auth: {
          token: null,
          user: null,
          isAuthenticated: false,
          isLoading: false,
          error: 'Test error',
        },
      },
    });

    // Dispatch the clearError action
    store.dispatch(clearError());

    // Check the resulting state
    expect(store.getState().auth.error).toBeNull();
  });
});

// frontend/src/components/AdminPanel/TemplateImportExportTab.js
import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { selectAuthToken } from '../../redux/slices/authSlice';
import axios from 'axios';
import { BASE_URL } from '../../utils/apiConfig';
import TemplateImportExport from './TemplateImportExport';
import './TemplateImportExportTab.css';

/**
 * Template Import/Export Tab component
 * Provides a dedicated tab for importing and exporting templates
 *
 * @returns {JSX.Element} Template import/export tab UI
 */
const TemplateImportExportTab = () => {
  const token = useSelector(selectAuthToken);
  
  // State for template management
  const [templates, setTemplates] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [recentImports, setRecentImports] = useState([]);
  
  // Fetch templates
  useEffect(() => {
    const fetchTemplates = async () => {
      setIsLoading(true);
      setError(null);
      
      try {
        const response = await axios.get(`${BASE_URL}/api/templates`, {
          headers: { Authorization: `Bearer ${token}` }
        });
        
        setTemplates(response.data);
        setIsLoading(false);
      } catch (error) {
        console.error('Error fetching templates:', error);
        setError('Failed to load templates: ' + (error.response?.data?.detail || error.message));
        setIsLoading(false);
      }
    };
    
    if (token) {
      fetchTemplates();
    }
  }, [token]);
  
  // Handle template imported
  const handleTemplateImported = (template) => {
    // Add to recent imports
    setRecentImports(prev => [template, ...prev].slice(0, 5));
    
    // Refresh templates list
    setTemplates(prev => {
      // Check if template already exists
      const exists = prev.some(t => t.template_id === template.template_id);
      
      if (exists) {
        // Update existing template
        return prev.map(t => t.template_id === template.template_id ? template : t);
      } else {
        // Add new template
        return [template, ...prev];
      }
    });
  };
  
  // Render loading state
  if (isLoading) {
    return (
      <div className="admin-panel-loading">
        <div className="spinner"></div>
        <p>Loading templates...</p>
      </div>
    );
  }
  
  // Render error state
  if (error) {
    return (
      <div className="admin-panel-error">
        <h2>Template Import/Export</h2>
        <div className="error-message">
          <span className="material-icons">error</span>
          <p>{error}</p>
        </div>
        <button
          className="admin-button"
          onClick={() => window.location.reload()}
        >
          <span className="material-icons">refresh</span>
          Retry
        </button>
      </div>
    );
  }
  
  return (
    <div className="template-import-export-tab">
      <h2>Template Import/Export</h2>
      
      <div className="tab-description">
        <p>
          Import templates from JSON files or export templates to share with others.
          Imported templates will be added to your template library and can be used in your world building.
        </p>
      </div>
      
      <TemplateImportExport 
        templates={templates}
        onTemplateImported={handleTemplateImported}
      />
      
      {recentImports.length > 0 && (
        <div className="recent-imports">
          <h3>Recently Imported Templates</h3>
          <div className="recent-imports-list">
            {recentImports.map(template => (
              <div key={template.template_id} className="recent-import-item">
                <div className="template-info">
                  <h4>{template.display_name}</h4>
                  <p className="template-description">{template.description || 'No description'}</p>
                  <div className="template-meta">
                    <span className="template-category">
                      <span className="material-icons">category</span>
                      {template.category_name || template.category_id}
                    </span>
                    <span className="template-version">
                      <span className="material-icons">tag</span>
                      v{template.version}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
      
      <div className="template-import-export-help">
        <h3>Help & Guidelines</h3>
        
        <div className="help-section">
          <h4>Importing Templates</h4>
          <ul>
            <li>Templates must be in JSON format</li>
            <li>Required fields: template_name, display_name, category_id, field_definitions</li>
            <li>Field definitions should include id, label, and type for each field</li>
            <li>If the category doesn't exist, the template will be assigned to a default category</li>
            <li>Imported templates are always marked as custom templates (not system templates)</li>
          </ul>
        </div>
        
        <div className="help-section">
          <h4>Exporting Templates</h4>
          <ul>
            <li>Exported templates include all fields, relationships, and metadata</li>
            <li>You can export both system and custom templates</li>
            <li>The export format is compatible with the import function</li>
            <li>Template exports include version information and export date</li>
          </ul>
        </div>
        
        <div className="help-section">
          <h4>Template Format Example</h4>
          <pre className="code-example">
{`{
  "template_name": "planet",
  "display_name": "Planet",
  "category_id": "cat_cosmology_physical",
  "description": "A celestial body orbiting a star",
  "field_definitions": {
    "Physical Properties": [
      {
        "id": "size",
        "label": "Size",
        "field_type": "text",
        "required": true
      },
      {
        "id": "atmosphere",
        "label": "Atmosphere",
        "field_type": "text"
      }
    ]
  }
}`}
          </pre>
        </div>
      </div>
    </div>
  );
};

export default TemplateImportExportTab;

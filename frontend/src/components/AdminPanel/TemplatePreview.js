// frontend/src/components/AdminPanel/TemplatePreview.js
import React, { useState } from 'react';
import './TemplatePreview.css';

/**
 * Template Preview component
 * Displays a preview of how a template will look when used
 * 
 * @param {Object} props - Component props
 * @param {Object} props.template - The template to preview
 * @param {Object} props.sampleData - Sample data for the template
 * @returns {JSX.Element} Template preview UI
 */
const TemplatePreview = ({ template, sampleData }) => {
  const [activeTab, setActiveTab] = useState('form');
  
  if (!template || !sampleData) {
    return (
      <div className="template-preview-error">
        <p>Template or sample data not provided</p>
      </div>
    );
  }
  
  // Render field based on its type
  const renderField = (field, value) => {
    const fieldType = field.field_type || field.type;
    
    switch (fieldType) {
      case 'text':
        return (
          <input
            type="text"
            className="preview-text-input"
            value={value || ''}
            readOnly
          />
        );
        
      case 'textarea':
        return (
          <textarea
            className="preview-textarea"
            value={value || ''}
            readOnly
            rows={4}
          />
        );
        
      case 'number':
        return (
          <input
            type="number"
            className="preview-number-input"
            value={value || 0}
            readOnly
          />
        );
        
      case 'date':
        return (
          <input
            type="date"
            className="preview-date-input"
            value={value || ''}
            readOnly
          />
        );
        
      case 'select':
        return (
          <select className="preview-select" value={value || ''} disabled>
            {field.options && field.options.map((option, index) => (
              <option key={index} value={typeof option === 'string' ? option : option.value}>
                {typeof option === 'string' ? option : option.label || option.value}
              </option>
            ))}
          </select>
        );
        
      case 'multiselect':
        return (
          <div className="preview-multiselect">
            {Array.isArray(value) && value.map((val, index) => (
              <div key={index} className="preview-multiselect-item">
                {val}
              </div>
            ))}
          </div>
        );
        
      case 'checkbox':
        return (
          <input
            type="checkbox"
            className="preview-checkbox"
            checked={value || false}
            readOnly
            disabled
          />
        );
        
      case 'radio':
        return (
          <div className="preview-radio-group">
            {field.options && field.options.map((option, index) => (
              <label key={index} className="preview-radio-label">
                <input
                  type="radio"
                  className="preview-radio"
                  checked={value === (typeof option === 'string' ? option : option.value)}
                  readOnly
                  disabled
                />
                {typeof option === 'string' ? option : option.label || option.value}
              </label>
            ))}
          </div>
        );
        
      case 'color':
        return (
          <div className="preview-color">
            <div 
              className="preview-color-swatch" 
              style={{ backgroundColor: value || '#ffffff' }}
            ></div>
            <span className="preview-color-value">{value || '#ffffff'}</span>
          </div>
        );
        
      case 'image':
        return (
          <div className="preview-image">
            {value ? (
              <img src={value} alt="Preview" className="preview-image-thumbnail" />
            ) : (
              <div className="preview-image-placeholder">No Image</div>
            )}
          </div>
        );
        
      default:
        return <div className="preview-unknown-field">Unsupported field type: {fieldType}</div>;
    }
  };
  
  // Render form view
  const renderFormView = () => {
    return (
      <div className="preview-form">
        <div className="preview-header">
          <h2>{sampleData.name}</h2>
        </div>
        
        {template.field_definitions && Object.entries(template.field_definitions).map(([sectionName, fields]) => (
          <div key={sectionName} className="preview-section">
            <h3 className="preview-section-title">{sectionName}</h3>
            
            <div className="preview-fields">
              {fields.map((field, index) => {
                const fieldName = field.field_name || field.label.toLowerCase().replace(/\s+/g, '_');
                const value = sampleData.data[sectionName] && sampleData.data[sectionName][fieldName];
                
                return (
                  <div key={index} className="preview-field">
                    <label className="preview-field-label">
                      {field.label}
                      {field.required && <span className="preview-required">*</span>}
                    </label>
                    
                    <div className="preview-field-input">
                      {renderField(field, value)}
                    </div>
                    
                    {field.description && (
                      <div className="preview-field-description">
                        {field.description}
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          </div>
        ))}
        
        {template.relationship_definitions && 
         template.relationship_definitions.valid_relationships && 
         template.relationship_definitions.valid_relationships.length > 0 && (
          <div className="preview-section">
            <h3 className="preview-section-title">Relationships</h3>
            
            <div className="preview-relationships">
              {template.relationship_definitions.valid_relationships.map((relationship, index) => (
                <div key={index} className="preview-relationship">
                  <div className="preview-relationship-type">
                    {relationship.relationship_type || relationship.relationship_type_id}
                  </div>
                  <div className="preview-relationship-target">
                    {relationship.target_template_name || relationship.target_template}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  };
  
  // Render card view
  const renderCardView = () => {
    return (
      <div className="preview-card">
        <div className="preview-card-header">
          <h3 className="preview-card-title">{sampleData.name}</h3>
          <div className="preview-card-type">{template.display_name}</div>
        </div>
        
        <div className="preview-card-content">
          {template.field_definitions && Object.entries(template.field_definitions).map(([sectionName, fields]) => (
            <div key={sectionName} className="preview-card-section">
              {fields.slice(0, 3).map((field, index) => {
                const fieldName = field.field_name || field.label.toLowerCase().replace(/\s+/g, '_');
                const value = sampleData.data[sectionName] && sampleData.data[sectionName][fieldName];
                
                if (!value) return null;
                
                return (
                  <div key={index} className="preview-card-field">
                    <div className="preview-card-field-label">{field.label}:</div>
                    <div className="preview-card-field-value">
                      {typeof value === 'string' ? value : JSON.stringify(value)}
                    </div>
                  </div>
                );
              })}
            </div>
          ))}
        </div>
        
        <div className="preview-card-footer">
          <div className="preview-card-relationships">
            {template.relationship_definitions && 
             template.relationship_definitions.valid_relationships && 
             template.relationship_definitions.valid_relationships.length > 0 && (
              <div className="preview-card-relationship-count">
                {template.relationship_definitions.valid_relationships.length} possible relationships
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };
  
  return (
    <div className="template-preview">
      <div className="preview-tabs">
        <button 
          className={`preview-tab ${activeTab === 'form' ? 'active' : ''}`}
          onClick={() => setActiveTab('form')}
        >
          Form View
        </button>
        <button 
          className={`preview-tab ${activeTab === 'card' ? 'active' : ''}`}
          onClick={() => setActiveTab('card')}
        >
          Card View
        </button>
      </div>
      
      <div className="preview-content">
        {activeTab === 'form' ? renderFormView() : renderCardView()}
      </div>
    </div>
  );
};

export default TemplatePreview;

/* frontend/src/components/AdminPanel/TemplateValidationTab.css */
.template-validation-tab {
  padding: 1rem;
}

.template-validation-tab h2 {
  margin-top: 0;
  margin-bottom: 1.5rem;
  font-weight: 500;
}

.validation-controls {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background-color: var(--background-tertiary);
  border-radius: 8px;
}

.file-upload-container {
  display: flex;
  flex-direction: column;
  margin-bottom: 1rem;
}

.file-upload-container label {
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.file-input {
  padding: 0.5rem;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--background-primary);
  color: var(--text-primary);
}

.button-container {
  display: flex;
  gap: 1rem;
}

.template-validation-content {
  display: flex;
  gap: 1.5rem;
  height: calc(100vh - 250px);
}

.template-editor-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.template-editor-container h3 {
  margin-top: 0;
  margin-bottom: 1rem;
  font-weight: 500;
}

.template-json-editor {
  flex: 1;
  padding: 1rem;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  background-color: var(--background-secondary);
  color: var(--text-primary);
  font-family: monospace;
  resize: none;
  overflow-y: auto;
}

.validation-results-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  overflow-y: auto;
}

.validation-result {
  padding: 1rem;
  border-radius: 8px;
  background-color: var(--background-secondary);
  border: 1px solid var(--border-color);
}

.validation-result h4 {
  margin-top: 0;
  margin-bottom: 1rem;
  font-weight: 500;
}

.validation-result.valid {
  border-color: #4caf50;
}

.validation-result.invalid {
  border-color: #f44336;
}

.valid-message {
  display: flex;
  align-items: center;
  color: #4caf50;
}

.invalid-message {
  color: #f44336;
}

.valid-message .material-icons,
.invalid-message .material-icons {
  margin-right: 0.5rem;
}

.invalid-message ul {
  margin-top: 0.5rem;
  padding-left: 2rem;
}

.sample-data {
  padding: 1rem;
  border-radius: 8px;
  background-color: var(--background-secondary);
  border: 1px solid var(--border-color);
}

.sample-data h4 {
  margin-top: 0;
  margin-bottom: 1rem;
  font-weight: 500;
}

.sample-data pre {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-word;
  font-family: monospace;
  max-height: 400px;
  overflow-y: auto;
  padding: 0.5rem;
  background-color: var(--background-primary);
  border-radius: 4px;
}

.error-message {
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
  padding: 1rem;
  margin-top: 0.5rem;
  background-color: #ffebee;
  border: 1px solid #ffcdd2;
  border-radius: 4px;
  color: #c62828;
}

.error-message .material-icons {
  color: #f44336;
}

/* Documentation modal styles */
.documentation-modal,
.preview-modal {
  width: 800px;
  max-width: 90%;
  max-height: 90vh;
}

.admin-button.info {
  background-color: #2196f3;
}

.admin-button.info:hover {
  background-color: #1976d2;
}

.admin-button.preview {
  background-color: #9c27b0;
}

.admin-button.preview:hover {
  background-color: #7b1fa2;
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .template-validation-content {
    flex-direction: column;
    height: auto;
  }

  .template-editor-container {
    margin-bottom: 1.5rem;
  }

  .template-json-editor {
    height: 300px;
  }
}

@media (max-width: 768px) {
  .validation-controls {
    flex-direction: column;
    align-items: stretch;
  }

  .button-container {
    flex-direction: column;
  }

  .button-container button {
    width: 100%;
  }
}

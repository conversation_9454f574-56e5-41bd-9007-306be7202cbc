// frontend/src/components/AdminPanel/TemplateManagementTab.js
import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { selectAuthToken } from '../../redux/slices/authSlice';
import axios from 'axios';
import { BASE_URL } from '../../utils/apiConfig';
import TemplateEditor from './TemplateEditor';
import TemplateUsageStats from './TemplateUsageStats';
import TemplateUsageTrends from './TemplateUsageTrends';
import TemplateVersionHistory from './TemplateVersionHistory';
import TemplateImportExport from './TemplateImportExport';
import './TemplateManagementTab.css';

/**
 * Template Management Tab component
 * Provides tools for managing templates in the system
 *
 * @returns {JSX.Element} Template management UI
 */
const TemplateManagementTab = () => {
  const token = useSelector(selectAuthToken);

  // State for template management
  const [templates, setTemplates] = useState([]);
  const [categories, setCategories] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [isEditing, setIsEditing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterCategory, setFilterCategory] = useState('all');
  const [filterType, setFilterType] = useState('all');
  const [showQuickImportExport, setShowQuickImportExport] = useState(false);

  // Fetch templates, categories, and usage statistics
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // Fetch categories
        let categoriesData = [];
        try {
          const categoriesResponse = await axios.get(`${BASE_URL}/api/admin/world-building/categories`, {
            headers: { Authorization: `Bearer ${token}` }
          });
          categoriesData = categoriesResponse.data;
        } catch (categoryError) {
          console.error('Error fetching categories:', categoryError);
          // Continue with empty categories rather than failing completely
        }

        // Fetch templates
        let templatesData = [];
        try {
          const templatesResponse = await axios.get(`${BASE_URL}/api/admin/templates`, {
            headers: { Authorization: `Bearer ${token}` }
          });
          templatesData = templatesResponse.data;
        } catch (templateError) {
          console.error('Error fetching templates:', templateError);
          setError('Failed to load templates: ' + (templateError.response?.data?.detail || templateError.message));
          setIsLoading(false);
          return;
        }

        // Fetch template usage statistics
        let usageStats = [];
        try {
          const usageStatsResponse = await axios.get(`${BASE_URL}/api/templates/usage`, {
            headers: { Authorization: `Bearer ${token}` }
          });
          usageStats = usageStatsResponse.data;
        } catch (usageError) {
          console.error('Error fetching usage statistics:', usageError);
          // Continue with empty usage stats rather than failing completely
        }

        // Create a map of template ID to usage count
        const usageCountMap = {};
        usageStats.forEach(stat => {
          if (!usageCountMap[stat.template_id]) {
            usageCountMap[stat.template_id] = 0;
          }
          usageCountMap[stat.template_id] += stat.usage_count;
        });

        // Add usage count to templates
        const templatesWithUsage = templatesData.map(template => ({
          ...template,
          usage_count: usageCountMap[template.template_id] || 0
        }));

        setCategories(categoriesData);
        setTemplates(templatesWithUsage);
        setIsLoading(false);
      } catch (error) {
        console.error('Error fetching template data:', error);
        setError('Failed to load template data: ' + (error.response?.data?.detail || error.message));
        setIsLoading(false);
      }
    };

    if (token) {
      fetchData();
    }
  }, [token]);

  // Filter templates based on search query and filters
  const filteredTemplates = templates.filter(template => {
    // Filter by search query
    const matchesSearch =
      (template.display_name?.toLowerCase() || '').includes(searchQuery.toLowerCase()) ||
      (template.description?.toLowerCase() || '').includes(searchQuery.toLowerCase()) ||
      (template.template_id?.toLowerCase() || '').includes(searchQuery.toLowerCase());

    // Filter by category
    const matchesCategory =
      filterCategory === 'all' ||
      template.category_id === filterCategory;

    // Filter by type (system or custom)
    const matchesType =
      filterType === 'all' ||
      (filterType === 'system' && template.is_system_template) ||
      (filterType === 'custom' && !template.is_system_template);

    return matchesSearch && matchesCategory && matchesType;
  });

  // Handle template selection
  const handleSelectTemplate = (template) => {
    setSelectedTemplate(template);
    setIsEditing(false);
  };

  // Handle edit button click
  const handleEditClick = () => {
    setIsEditing(true);
  };

  // Handle new template button click
  const handleNewTemplate = () => {
    setSelectedTemplate({
      template_id: '',
      template_name: '',
      display_name: '',
      category_id: categories.length > 0 ? categories[0].category_id : '',
      description: '',
      field_definitions: {},
      relationship_definitions: {},
      is_system_template: false,
      version: '1.0'
    });
    setIsEditing(true);
  };

  // Handle save template
  const handleSaveTemplate = async (templateData) => {
    setIsLoading(true);
    setError(null);

    try {
      let response;

      try {
        if (templateData.template_id) {
          // Update existing template
          response = await axios.put(`${BASE_URL}/api/templates/${templateData.template_id}`, templateData, {
            headers: { Authorization: `Bearer ${token}` }
          });
          console.log('Template updated successfully:', response.data);
        } else {
          // Create new template
          response = await axios.post(`${BASE_URL}/api/templates`, templateData, {
            headers: { Authorization: `Bearer ${token}` }
          });
          console.log('Template created successfully:', response.data);
        }
      } catch (apiError) {
        console.error('Error saving template:', apiError);
        throw new Error('Failed to save template: ' + (apiError.response?.data?.detail || apiError.message));
      }

      // Refresh templates
      const templatesResponse = await axios.get(`${BASE_URL}/api/admin/templates`, {
        headers: { Authorization: `Bearer ${token}` }
      });

      // Fetch template usage statistics
      const usageStatsResponse = await axios.get(`${BASE_URL}/api/templates/usage`, {
        headers: { Authorization: `Bearer ${token}` }
      });

      const templatesData = templatesResponse.data;
      const usageStats = usageStatsResponse.data;

      // Create a map of template ID to usage count
      const usageCountMap = {};
      usageStats.forEach(stat => {
        if (!usageCountMap[stat.template_id]) {
          usageCountMap[stat.template_id] = 0;
        }
        usageCountMap[stat.template_id] += stat.usage_count;
      });

      // Add usage count to templates
      const templatesWithUsage = templatesData.map(template => ({
        ...template,
        usage_count: usageCountMap[template.template_id] || 0
      }));

      setTemplates(templatesWithUsage);
      setSelectedTemplate(response.data);
      setIsEditing(false);
      setIsLoading(false);
    } catch (error) {
      console.error('Error saving template:', error);
      setError('Failed to save template: ' + (error.response?.data?.detail || error.message));
      setIsLoading(false);
    }
  };

  // Handle delete template
  const handleDeleteTemplate = async (templateId) => {
    if (!window.confirm('Are you sure you want to delete this template? This action cannot be undone.')) {
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      await axios.delete(`${BASE_URL}/api/templates/${templateId}`, {
        headers: { Authorization: `Bearer ${token}` }
      });

      // Refresh templates
      const templatesResponse = await axios.get(`${BASE_URL}/api/admin/templates`, {
        headers: { Authorization: `Bearer ${token}` }
      });

      // Fetch template usage statistics
      const usageStatsResponse = await axios.get(`${BASE_URL}/api/templates/usage`, {
        headers: { Authorization: `Bearer ${token}` }
      });

      const templatesData = templatesResponse.data;
      const usageStats = usageStatsResponse.data;

      // Create a map of template ID to usage count
      const usageCountMap = {};
      usageStats.forEach(stat => {
        if (!usageCountMap[stat.template_id]) {
          usageCountMap[stat.template_id] = 0;
        }
        usageCountMap[stat.template_id] += stat.usage_count;
      });

      // Add usage count to templates
      const templatesWithUsage = templatesData.map(template => ({
        ...template,
        usage_count: usageCountMap[template.template_id] || 0
      }));

      setTemplates(templatesWithUsage);
      setSelectedTemplate(null);
      setIsLoading(false);
    } catch (error) {
      console.error('Error deleting template:', error);
      setError('Failed to delete template: ' + (error.response?.data?.detail || error.message));
      setIsLoading(false);
    }
  };

  // Handle export template
  const handleExportTemplate = async (templateId) => {
    try {
      const response = await axios.get(`${BASE_URL}/api/templates/${templateId}/export`, {
        headers: { Authorization: `Bearer ${token}` }
      });

      // Create a JSON file for download
      const templateData = response.data;
      const fileName = `${templateData.template_name}_template.json`;
      const jsonData = JSON.stringify(templateData, null, 2);
      const blob = new Blob([jsonData], { type: 'application/json' });
      const url = URL.createObjectURL(blob);

      // Create a temporary link and trigger download
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();

      // Clean up
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error exporting template:', error);
      setError('Failed to export template: ' + (error.response?.data?.detail || error.message));
    }
  };

  // Handle restore version
  const handleRestoreVersion = async (versionId) => {
    if (!window.confirm('Are you sure you want to restore this version? This will create a new version based on the selected one.')) {
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await axios.post(`${BASE_URL}/api/templates/versions/${versionId}/restore`, {}, {
        headers: { Authorization: `Bearer ${token}` }
      });

      // Refresh templates
      const templatesResponse = await axios.get(`${BASE_URL}/api/admin/templates`, {
        headers: { Authorization: `Bearer ${token}` }
      });

      // Fetch template usage statistics
      const usageStatsResponse = await axios.get(`${BASE_URL}/api/templates/usage`, {
        headers: { Authorization: `Bearer ${token}` }
      });

      const templatesData = templatesResponse.data;
      const usageStats = usageStatsResponse.data;

      // Create a map of template ID to usage count
      const usageCountMap = {};
      usageStats.forEach(stat => {
        if (!usageCountMap[stat.template_id]) {
          usageCountMap[stat.template_id] = 0;
        }
        usageCountMap[stat.template_id] += stat.usage_count;
      });

      // Add usage count to templates
      const templatesWithUsage = templatesData.map(template => ({
        ...template,
        usage_count: usageCountMap[template.template_id] || 0
      }));

      setTemplates(templatesWithUsage);
      setSelectedTemplate(response.data);
      setIsLoading(false);
    } catch (error) {
      console.error('Error restoring template version:', error);
      setError('Failed to restore template version: ' + (error.response?.data?.detail || error.message));
      setIsLoading(false);
    }
  };

  // Handle import template button click
  const handleImportTemplate = () => {
    setShowQuickImportExport(true);
  };

  // Handle template imported
  const handleTemplateImported = (template) => {
    // Refresh templates
    const fetchTemplates = async () => {
      try {
        // Fetch templates
        const templatesResponse = await axios.get(`${BASE_URL}/api/admin/templates`, {
          headers: { Authorization: `Bearer ${token}` }
        });

        // Fetch template usage statistics
        const usageStatsResponse = await axios.get(`${BASE_URL}/api/templates/usage`, {
          headers: { Authorization: `Bearer ${token}` }
        });

        const templatesData = templatesResponse.data;
        const usageStats = usageStatsResponse.data;

        // Create a map of template ID to usage count
        const usageCountMap = {};
        usageStats.forEach(stat => {
          if (!usageCountMap[stat.template_id]) {
            usageCountMap[stat.template_id] = 0;
          }
          usageCountMap[stat.template_id] += stat.usage_count;
        });

        // Add usage count to templates
        const templatesWithUsage = templatesData.map(template => ({
          ...template,
          usage_count: usageCountMap[template.template_id] || 0
        }));

        setTemplates(templatesWithUsage);
        setSelectedTemplate(template);
        setShowQuickImportExport(false);
      } catch (error) {
        console.error('Error refreshing templates:', error);
      }
    };

    fetchTemplates();
  };

  // Render loading state
  if (isLoading) {
    return (
      <div className="admin-panel-loading">
        <div className="spinner"></div>
        <p>Loading templates...</p>
      </div>
    );
  }

  // Render error state
  if (error) {
    return (
      <div className="admin-panel-error">
        <h2>Template Management</h2>
        <div className="error-message">
          <span className="material-icons">error</span>
          <p>{error}</p>
        </div>
        <button
          className="admin-button"
          onClick={() => window.location.reload()}
        >
          <span className="material-icons">refresh</span>
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="template-management-tab">
      <h2>Template Management</h2>

      <div className="template-management-controls">
        <div className="search-and-filters">
          <div className="search-container">
            <input
              type="text"
              placeholder="Search templates..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="search-input"
            />
          </div>

          <div className="filters-container">
            <div className="filter">
              <label>Category:</label>
              <select
                value={filterCategory}
                onChange={(e) => setFilterCategory(e.target.value)}
              >
                <option value="all">All Categories</option>
                {categories.map(category => (
                  <option key={category.category_id} value={category.category_id.toString()}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>

            <div className="filter">
              <label>Type:</label>
              <select
                value={filterType}
                onChange={(e) => setFilterType(e.target.value)}
              >
                <option value="all">All Templates</option>
                <option value="system">System Templates</option>
                <option value="custom">Custom Templates</option>
              </select>
            </div>
          </div>
        </div>

        <div className="template-control-buttons">
          <button className="admin-button" onClick={handleNewTemplate}>
            <span className="material-icons">add</span>
            New Template
          </button>
          <button className="admin-button secondary" onClick={handleImportTemplate}>
            <span className="material-icons">file_upload</span>
            Import Template
          </button>
        </div>
      </div>

      <div className="template-management-content">
        <div className="templates-list">
          <div className="templates-list-header">
            <div className="template-column name-column">Template Name</div>
            <div className="template-column category-column">Category</div>
            <div className="template-column type-column">Type</div>
            <div className="template-column version-column">Version</div>
            <div className="template-column usage-column">Usage</div>
          </div>

          <div className="templates-list-body">
            {filteredTemplates.length === 0 ? (
              <div className="no-templates">
                <p>No templates found matching your criteria.</p>
              </div>
            ) : (
              filteredTemplates.map(template => (
                <div
                  key={template.template_id}
                  className={`template-row ${selectedTemplate?.template_id === template.template_id ? 'selected' : ''}`}
                  onClick={() => handleSelectTemplate(template)}
                >
                  <div className="template-column name-column">{template.display_name}</div>
                  <div className="template-column category-column">{template.category_name}</div>
                  <div className="template-column type-column">
                    {template.is_system_template ? 'System' : 'Custom'}
                  </div>
                  <div className="template-column version-column">{template.version}</div>
                  <div className="template-column usage-column">{template.usage_count || 0}</div>
                </div>
              ))
            )}
          </div>
        </div>

        {selectedTemplate && !isEditing && (
          <div className="template-details">
            <div className="template-details-header">
              <h3>{selectedTemplate.display_name}</h3>
              <div className="template-actions">
                <button
                  className="admin-button"
                  onClick={handleEditClick}
                  title="Edit Template"
                >
                  <span className="material-icons">edit</span>
                </button>
                <button
                  className="admin-button secondary"
                  onClick={() => handleExportTemplate(selectedTemplate.template_id)}
                  title="Export Template"
                >
                  <span className="material-icons">file_download</span>
                </button>
                {!selectedTemplate.is_system_template && (
                  <button
                    className="admin-button danger"
                    onClick={() => handleDeleteTemplate(selectedTemplate.template_id)}
                    title="Delete Template"
                  >
                    <span className="material-icons">delete</span>
                  </button>
                )}
              </div>
            </div>

            <div className="template-details-content">
              <div className="template-info">
                <div className="info-item">
                  <div className="info-label">Template ID:</div>
                  <div className="info-value">{selectedTemplate.template_id}</div>
                </div>
                <div className="info-item">
                  <div className="info-label">Category:</div>
                  <div className="info-value">{selectedTemplate.category_name}</div>
                </div>
                <div className="info-item">
                  <div className="info-label">Type:</div>
                  <div className="info-value">{selectedTemplate.is_system_template ? 'System Template' : 'Custom Template'}</div>
                </div>
                <div className="info-item">
                  <div className="info-label">Version:</div>
                  <div className="info-value">{selectedTemplate.version}</div>
                </div>
                <div className="info-item">
                  <div className="info-label">Created:</div>
                  <div className="info-value">{new Date(selectedTemplate.created_at).toLocaleString()}</div>
                </div>
                <div className="info-item">
                  <div className="info-label">Last Updated:</div>
                  <div className="info-value">{new Date(selectedTemplate.updated_at).toLocaleString()}</div>
                </div>
                <div className="info-item">
                  <div className="info-label">Usage Count:</div>
                  <div className="info-value">{selectedTemplate.usage_count || 0}</div>
                </div>
              </div>

              <div className="template-description">
                <h4>Description</h4>
                <p>{selectedTemplate.description}</p>
              </div>

              {/* Display template usage statistics */}
              <TemplateUsageStats templateId={selectedTemplate.template_id} />

              {/* Display template usage trends */}
              <TemplateUsageTrends templateId={selectedTemplate.template_id} />

              {/* Display template version history */}
              <TemplateVersionHistory
                templateId={selectedTemplate.template_id}
                onRestoreVersion={handleRestoreVersion}
              />
            </div>
          </div>
        )}

        {selectedTemplate && isEditing && (
          <div className="template-editor">
            <div className="template-editor-header">
              <h3>{selectedTemplate.template_id ? 'Edit Template' : 'New Template'}</h3>
              <div className="template-actions">
                <button className="admin-button secondary" onClick={() => setIsEditing(false)}>
                  Cancel
                </button>
              </div>
            </div>

            <div className="template-editor-content">
              <TemplateEditor
                template={selectedTemplate}
                categories={categories}
                templates={templates}
                onSave={handleSaveTemplate}
                onCancel={() => setIsEditing(false)}
              />
            </div>
          </div>
        )}
      </div>

      {/* Quick Import/Export Modal */}
      {showQuickImportExport && (
        <div className="modal-overlay">
          <div className="modal-content modal-large">
            <div className="modal-header">
              <h3>Template Import/Export</h3>
              <button
                className="modal-close-button"
                onClick={() => setShowQuickImportExport(false)}
              >
                <span className="material-icons">close</span>
              </button>
            </div>
            <div className="modal-body">
              <TemplateImportExport
                templates={templates}
                onTemplateImported={handleTemplateImported}
              />
            </div>
            <div className="modal-footer">
              <button
                className="admin-button secondary"
                onClick={() => setShowQuickImportExport(false)}
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TemplateManagementTab;

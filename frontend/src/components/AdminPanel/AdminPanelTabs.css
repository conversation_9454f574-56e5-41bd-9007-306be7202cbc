/* frontend/src/components/AdminPanel/AdminPanelTabs.css */
.admin-panel-tabs {
  width: 100%;
}

.admin-panel-tabs ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.admin-panel-tabs li {
  display: flex;
  align-items: center;
  padding: 1rem;
  cursor: pointer;
  transition: background-color 0.2s;
  border-left: 3px solid transparent;
}

.admin-panel-tabs li:hover {
  background-color: var(--background-tertiary);
}

.admin-panel-tabs li.active {
  background-color: var(--background-tertiary);
  border-left-color: var(--accent-color);
  font-weight: 500;
}

.admin-panel-tabs .tab-icon {
  margin-right: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.admin-panel-tabs .material-icons {
  font-size: 1.25rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .admin-panel-tabs ul {
    display: flex;
    overflow-x: auto;
  }
  
  .admin-panel-tabs li {
    flex-direction: column;
    padding: 0.75rem;
    border-left: none;
    border-bottom: 3px solid transparent;
    min-width: 80px;
    text-align: center;
  }
  
  .admin-panel-tabs li.active {
    border-left-color: transparent;
    border-bottom-color: var(--accent-color);
  }
  
  .admin-panel-tabs .tab-icon {
    margin-right: 0;
    margin-bottom: 0.25rem;
  }
  
  .admin-panel-tabs .tab-label {
    font-size: 0.8rem;
  }
}

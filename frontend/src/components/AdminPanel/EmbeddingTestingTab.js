// frontend/src/components/AdminPanel/EmbeddingTestingTab.js
import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { selectAuthToken } from '../../redux/slices/authSlice';
import axios from 'axios';
import { BASE_URL } from '../../utils/apiConfig';
import './EmbeddingTestingTab.css';

/**
 * Embedding Testing Tab component
 * Provides tools for testing and evaluating the embedding pipeline
 *
 * @returns {JSX.Element} Embedding testing UI
 */
const EmbeddingTestingTab = () => {
  const token = useSelector(selectAuthToken);

  // State for embedding testing
  const [testType, setTestType] = useState('text');
  const [inputText, setInputText] = useState('');
  const [templateType, setTemplateType] = useState('character');
  const [fieldWeights, setFieldWeights] = useState({});
  const [isProcessing, setIsProcessing] = useState(false);
  const [results, setResults] = useState(null);
  const [error, setError] = useState(null);
  const [templateTypes, setTemplateTypes] = useState([]);
  const [availableFields, setAvailableFields] = useState([]);

  // Fetch template types and fields
  useEffect(() => {
    const fetchTemplateData = async () => {
      try {
        // Fetch categories to use as template types
        const categoriesResponse = await axios.get(`${BASE_URL}/api/world-building/categories`, {
          headers: { Authorization: `Bearer ${token}` }
        });

        const categoriesData = categoriesResponse.data;

        // Transform categories into template types
        const types = categoriesData.map(category => ({
          id: category.category_id,
          name: category.name,
          aspect_type: category.aspect_type
        }));

        setTemplateTypes(types);

        // Set default fields based on template type
        updateAvailableFields(templateType);
      } catch (error) {
        console.error('Error fetching template data:', error);
        setError('Failed to load template data: ' + (error.response?.data?.detail || error.message));
      }
    };

    if (token) {
      fetchTemplateData();
    }
  }, [token, templateType]);

  // Update available fields when template type changes
  const updateAvailableFields = (type) => {
    let fields = [];

    switch (type) {
      case 'character':
        fields = [
          { id: 'name', label: 'Name', defaultWeight: 1.5 },
          { id: 'full_name', label: 'Full Name', defaultWeight: 1.2 },
          { id: 'personality', label: 'Personality', defaultWeight: 1.8 },
          { id: 'background', label: 'Background', defaultWeight: 1.5 },
          { id: 'appearance', label: 'Appearance', defaultWeight: 1.0 },
          { id: 'motivations', label: 'Motivations', defaultWeight: 1.6 },
          { id: 'gender_identity', label: 'Gender Identity', defaultWeight: 0.8 },
          { id: 'sexual_orientation', label: 'Sexual Orientation', defaultWeight: 0.8 }
        ];
        break;
      case 'location':
        fields = [
          { id: 'name', label: 'Name', defaultWeight: 1.5 },
          { id: 'geography', label: 'Geography', defaultWeight: 1.8 },
          { id: 'climate', label: 'Climate', defaultWeight: 1.5 },
          { id: 'description', label: 'Description', defaultWeight: 1.2 },
          { id: 'history', label: 'History', defaultWeight: 1.0 },
          { id: 'inhabitants', label: 'Inhabitants', defaultWeight: 1.0 }
        ];
        break;
      case 'item':
        fields = [
          { id: 'name', label: 'Name', defaultWeight: 1.5 },
          { id: 'type', label: 'Type', defaultWeight: 1.2 },
          { id: 'description', label: 'Description', defaultWeight: 1.2 },
          { id: 'properties', label: 'Properties', defaultWeight: 1.8 },
          { id: 'history', label: 'History', defaultWeight: 1.0 },
          { id: 'usage', label: 'Usage', defaultWeight: 1.5 }
        ];
        break;
      default:
        fields = [
          { id: 'name', label: 'Name', defaultWeight: 1.5 },
          { id: 'description', label: 'Description', defaultWeight: 1.2 }
        ];
    }

    setAvailableFields(fields);

    // Initialize field weights
    const weights = {};
    fields.forEach(field => {
      weights[field.id] = field.defaultWeight;
    });
    setFieldWeights(weights);
  };

  // Handle template type change
  const handleTemplateTypeChange = (e) => {
    const newType = e.target.value;
    setTemplateType(newType);
    updateAvailableFields(newType);
  };

  // Handle field weight change
  const handleWeightChange = (fieldId, value) => {
    setFieldWeights(prev => ({
      ...prev,
      [fieldId]: parseFloat(value)
    }));
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsProcessing(true);
    setError(null);
    setResults(null);

    try {
      // Prepare the request data
      const requestData = {
        text: inputText,
        template_type: templateType,
        test_type: testType,
        field_weights: fieldWeights
      };

      // Call the embedding test API
      const response = await axios.post(`${BASE_URL}/api/admin/embedding-test`, requestData, {
        headers: { Authorization: `Bearer ${token}` }
      });

      // If we don't have a real API endpoint yet, use mock data
      if (!response.data) {
        // Simulate embedding vector
        const mockVector = Array.from({ length: 10 }, () => Math.random() * 2 - 1);

        setResults({
          embedding: mockVector,
          metrics: {
            norm: Math.sqrt(mockVector.reduce((sum, val) => sum + val * val, 0)),
            variance: mockVector.reduce((sum, val) => sum + val * val, 0) / mockVector.length,
            similarity: {
              character: 0.85,
              location: 0.32,
              item: 0.41
            }
          },
          preprocessed: {
            text: inputText.trim().toLowerCase().replace(/\s+/g, ' ')
          }
        });
      } else {
        // Use the real API response
        setResults(response.data);
      }

      setIsProcessing(false);
    } catch (error) {
      console.error('Error processing embedding test:', error);
      setError('Failed to process embedding test: ' + (error.response?.data?.detail || error.message));
      setIsProcessing(false);
    }
  };

  return (
    <div className="embedding-testing-tab">
      <h2>Embedding Testing</h2>

      <div className="admin-card">
        <div className="admin-card-header">
          <h3 className="admin-card-title">Test Embedding Generation</h3>
        </div>

        <div className="admin-card-content">
          <form onSubmit={handleSubmit}>
            <div className="admin-form-group">
              <label className="admin-form-label">Test Type</label>
              <select
                className="admin-form-select"
                value={testType}
                onChange={(e) => setTestType(e.target.value)}
              >
                <option value="text">Text Input</option>
                <option value="template">Template Fields</option>
                <option value="entity">Entity ID</option>
              </select>
            </div>

            {testType === 'text' && (
              <div className="admin-form-group">
                <label className="admin-form-label">Input Text</label>
                <textarea
                  className="admin-form-textarea"
                  value={inputText}
                  onChange={(e) => setInputText(e.target.value)}
                  placeholder="Enter text to generate embedding for..."
                  required
                />
              </div>
            )}

            <div className="admin-form-group">
              <label className="admin-form-label">Template Type</label>
              <select
                className="admin-form-select"
                value={templateType}
                onChange={handleTemplateTypeChange}
              >
                {templateTypes.map(type => (
                  <option key={type.id} value={type.id}>{type.name}</option>
                ))}
              </select>
            </div>

            <div className="admin-form-group">
              <label className="admin-form-label">Field Weights</label>
              <div className="field-weights-container">
                {availableFields.map(field => (
                  <div key={field.id} className="field-weight-item">
                    <label>{field.label}</label>
                    <input
                      type="number"
                      min="0"
                      max="2"
                      step="0.1"
                      value={fieldWeights[field.id] || field.defaultWeight}
                      onChange={(e) => handleWeightChange(field.id, e.target.value)}
                    />
                  </div>
                ))}
              </div>
            </div>

            <div className="admin-card-footer">
              <button
                type="submit"
                className="admin-button"
                disabled={isProcessing}
              >
                {isProcessing ? 'Processing...' : 'Generate Embedding'}
              </button>
            </div>
          </form>
        </div>
      </div>

      {error && (
        <div className="admin-card error-card">
          <div className="admin-card-content">
            <p className="error-message">{error}</p>
          </div>
        </div>
      )}

      {results && (
        <div className="admin-card results-card">
          <div className="admin-card-header">
            <h3 className="admin-card-title">Embedding Results</h3>
          </div>

          <div className="admin-card-content">
            <div className="results-section">
              <h4>Embedding Vector (first 10 dimensions)</h4>
              <div className="embedding-vector">
                {results.embedding.slice(0, 10).map((value, index) => (
                  <div key={index} className="vector-dimension">
                    <span className="dimension-index">{index}:</span>
                    <span className="dimension-value">{value.toFixed(4)}</span>
                  </div>
                ))}
              </div>
            </div>

            <div className="results-section">
              <h4>Metrics</h4>
              <div className="metrics-grid">
                <div className="metric">
                  <div className="metric-label">Vector Norm</div>
                  <div className="metric-value">{results.metrics.norm.toFixed(4)}</div>
                </div>
                <div className="metric">
                  <div className="metric-label">Variance</div>
                  <div className="metric-value">{results.metrics.variance.toFixed(4)}</div>
                </div>
              </div>
            </div>

            <div className="results-section">
              <h4>Similarity to Template Types</h4>
              <div className="similarity-bars">
                {Object.entries(results.metrics.similarity).map(([type, value]) => (
                  <div key={type} className="similarity-item">
                    <div className="similarity-label">{type}</div>
                    <div className="similarity-bar-container">
                      <div
                        className="similarity-bar"
                        style={{ width: `${value * 100}%` }}
                      ></div>
                      <span className="similarity-value">{(value * 100).toFixed(1)}%</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default EmbeddingTestingTab;

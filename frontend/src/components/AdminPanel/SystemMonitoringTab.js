// frontend/src/components/AdminPanel/SystemMonitoringTab.js
import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { selectAuthToken } from '../../redux/slices/authSlice';
import axios from 'axios';
import { BASE_URL } from '../../utils/apiConfig';
import './SystemMonitoringTab.css';

/**
 * System Monitoring Tab component
 * Provides tools for monitoring system health and performance
 *
 * @returns {JSX.Element} System monitoring UI
 */
const SystemMonitoringTab = () => {
  const token = useSelector(selectAuthToken);

  // State for system monitoring
  const [systemStatus, setSystemStatus] = useState({
    postgresql: { status: 'unknown', metrics: {} },
    qdrant: { status: 'unknown', metrics: {} },
    api: { status: 'unknown', metrics: {} }
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedSystem, setSelectedSystem] = useState('overview');
  const [refreshInterval, setRefreshInterval] = useState(30);
  const [lastRefreshed, setLastRefreshed] = useState(null);

  // Fetch system status
  useEffect(() => {
    const fetchSystemStatus = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // Fetch system status from the API
        const response = await axios.get(`${BASE_URL}/api/admin/system-status`, {
          headers: { Authorization: `Bearer ${token}` }
        });

        // If we don't have a real API endpoint yet, use mock data
        if (!response.data) {
          setSystemStatus({
            postgresql: {
              status: 'healthy',
              metrics: {
                connections: 12,
                active_queries: 3,
                avg_query_time: 45, // ms
                disk_usage: 1.2, // GB
                uptime: '7d 14h 23m'
              }
            },
            qdrant: {
              status: 'healthy',
              metrics: {
                collections: 8,
                points: 25420,
                segments: 32,
                disk_usage: 0.8, // GB
                avg_search_time: 12, // ms
                uptime: '7d 14h 23m'
              }
            },
            api: {
              status: 'healthy',
              metrics: {
                requests_per_minute: 42,
                avg_response_time: 120, // ms
                error_rate: 0.2, // %
                uptime: '7d 14h 23m'
              }
            }
          });
        } else {
          // Use the real API response
          setSystemStatus(response.data);
        }

        setIsLoading(false);
        setLastRefreshed(new Date());
      } catch (error) {
        console.error('Error fetching system status:', error);
        setError('Failed to load system status: ' + (error.response?.data?.detail || error.message));
        setIsLoading(false);
      }
    };

    if (token) {
      fetchSystemStatus();

      // Set up refresh interval
      const intervalId = setInterval(fetchSystemStatus, refreshInterval * 1000);

      return () => {
        clearInterval(intervalId);
      };
    }
  }, [token, refreshInterval]);

  // Handle refresh button click
  const handleRefresh = () => {
    // Trigger a refresh by changing the refreshInterval slightly and then back
    setRefreshInterval(prev => prev + 0.1);
    setTimeout(() => setRefreshInterval(prev => Math.floor(prev)), 100);
  };

  // Handle system selection
  const handleSystemSelect = (system) => {
    setSelectedSystem(system);
  };

  // Handle refresh interval change
  const handleRefreshIntervalChange = (e) => {
    setRefreshInterval(parseInt(e.target.value, 10));
  };

  // Render loading state
  if (isLoading && !lastRefreshed) {
    return (
      <div className="admin-panel-loading">
        <div className="spinner"></div>
        <p>Loading system status...</p>
      </div>
    );
  }

  return (
    <div className="system-monitoring-tab">
      <div className="system-monitoring-header">
        <h2>System Monitoring</h2>

        <div className="system-monitoring-controls">
          <div className="refresh-control">
            <label>Refresh every:</label>
            <select
              value={refreshInterval}
              onChange={handleRefreshIntervalChange}
            >
              <option value="10">10 seconds</option>
              <option value="30">30 seconds</option>
              <option value="60">1 minute</option>
              <option value="300">5 minutes</option>
            </select>
          </div>

          <button className="admin-button" onClick={handleRefresh}>
            <span className="material-icons">refresh</span>
            Refresh Now
          </button>
        </div>
      </div>

      {lastRefreshed && (
        <div className="last-refreshed">
          Last refreshed: {lastRefreshed.toLocaleTimeString()}
        </div>
      )}

      <div className="system-status-overview">
        <div
          className={`system-status-card ${systemStatus.postgresql.status}`}
          onClick={() => handleSystemSelect('postgresql')}
        >
          <div className="system-status-header">
            <h3>PostgreSQL</h3>
            <div className={`status-indicator ${systemStatus.postgresql.status}`}></div>
          </div>
          <div className="system-status-metrics">
            <div className="metric">
              <div className="metric-value">{systemStatus.postgresql.metrics.connections}</div>
              <div className="metric-label">Connections</div>
            </div>
            <div className="metric">
              <div className="metric-value">{systemStatus.postgresql.metrics.active_queries}</div>
              <div className="metric-label">Active Queries</div>
            </div>
            <div className="metric">
              <div className="metric-value">{systemStatus.postgresql.metrics.avg_query_time} ms</div>
              <div className="metric-label">Avg Query Time</div>
            </div>
          </div>
        </div>

        <div
          className={`system-status-card ${systemStatus.qdrant.status}`}
          onClick={() => handleSystemSelect('qdrant')}
        >
          <div className="system-status-header">
            <h3>Qdrant</h3>
            <div className={`status-indicator ${systemStatus.qdrant.status}`}></div>
          </div>
          <div className="system-status-metrics">
            <div className="metric">
              <div className="metric-value">{systemStatus.qdrant.metrics.collections}</div>
              <div className="metric-label">Collections</div>
            </div>
            <div className="metric">
              <div className="metric-value">{systemStatus.qdrant.metrics.points.toLocaleString()}</div>
              <div className="metric-label">Points</div>
            </div>
            <div className="metric">
              <div className="metric-value">{systemStatus.qdrant.metrics.avg_search_time} ms</div>
              <div className="metric-label">Avg Search Time</div>
            </div>
          </div>
        </div>

        <div
          className={`system-status-card ${systemStatus.api.status}`}
          onClick={() => handleSystemSelect('api')}
        >
          <div className="system-status-header">
            <h3>API Server</h3>
            <div className={`status-indicator ${systemStatus.api.status}`}></div>
          </div>
          <div className="system-status-metrics">
            <div className="metric">
              <div className="metric-value">{systemStatus.api.metrics.requests_per_minute}</div>
              <div className="metric-label">Req/min</div>
            </div>
            <div className="metric">
              <div className="metric-value">{systemStatus.api.metrics.avg_response_time} ms</div>
              <div className="metric-label">Avg Response Time</div>
            </div>
            <div className="metric">
              <div className="metric-value">{systemStatus.api.metrics.error_rate}%</div>
              <div className="metric-label">Error Rate</div>
            </div>
          </div>
        </div>
      </div>

      <div className="system-details">
        {selectedSystem === 'postgresql' && (
          <div className="system-detail-card">
            <h3>PostgreSQL Details</h3>
            <div className="system-metrics-grid">
              <div className="metric">
                <div className="metric-label">Status</div>
                <div className={`metric-value ${systemStatus.postgresql.status}`}>
                  {systemStatus.postgresql.status === 'healthy' ? 'Healthy' :
                   systemStatus.postgresql.status === 'warning' ? 'Warning' :
                   systemStatus.postgresql.status === 'critical' ? 'Critical' : 'Unknown'}
                </div>
              </div>
              <div className="metric">
                <div className="metric-label">Connections</div>
                <div className="metric-value">{systemStatus.postgresql.metrics.connections}</div>
              </div>
              <div className="metric">
                <div className="metric-label">Active Queries</div>
                <div className="metric-value">{systemStatus.postgresql.metrics.active_queries}</div>
              </div>
              <div className="metric">
                <div className="metric-label">Avg Query Time</div>
                <div className="metric-value">{systemStatus.postgresql.metrics.avg_query_time} ms</div>
              </div>
              <div className="metric">
                <div className="metric-label">Disk Usage</div>
                <div className="metric-value">{systemStatus.postgresql.metrics.disk_usage} GB</div>
              </div>
              <div className="metric">
                <div className="metric-label">Uptime</div>
                <div className="metric-value">{systemStatus.postgresql.metrics.uptime}</div>
              </div>
            </div>

            <div className="system-actions">
              <button className="admin-button">
                <span className="material-icons">analytics</span>
                View in Grafana
              </button>
              <button className="admin-button secondary">
                <span className="material-icons">storage</span>
                Database Tools
              </button>
            </div>
          </div>
        )}

        {selectedSystem === 'qdrant' && (
          <div className="system-detail-card">
            <h3>Qdrant Details</h3>
            <div className="system-metrics-grid">
              <div className="metric">
                <div className="metric-label">Status</div>
                <div className={`metric-value ${systemStatus.qdrant.status}`}>
                  {systemStatus.qdrant.status === 'healthy' ? 'Healthy' :
                   systemStatus.qdrant.status === 'warning' ? 'Warning' :
                   systemStatus.qdrant.status === 'critical' ? 'Critical' : 'Unknown'}
                </div>
              </div>
              <div className="metric">
                <div className="metric-label">Collections</div>
                <div className="metric-value">{systemStatus.qdrant.metrics.collections}</div>
              </div>
              <div className="metric">
                <div className="metric-label">Points</div>
                <div className="metric-value">{systemStatus.qdrant.metrics.points.toLocaleString()}</div>
              </div>
              <div className="metric">
                <div className="metric-label">Segments</div>
                <div className="metric-value">{systemStatus.qdrant.metrics.segments}</div>
              </div>
              <div className="metric">
                <div className="metric-label">Disk Usage</div>
                <div className="metric-value">{systemStatus.qdrant.metrics.disk_usage} GB</div>
              </div>
              <div className="metric">
                <div className="metric-label">Avg Search Time</div>
                <div className="metric-value">{systemStatus.qdrant.metrics.avg_search_time} ms</div>
              </div>
              <div className="metric">
                <div className="metric-label">Uptime</div>
                <div className="metric-value">{systemStatus.qdrant.metrics.uptime}</div>
              </div>
            </div>

            <div className="system-actions">
              <button className="admin-button">
                <span className="material-icons">analytics</span>
                View in Grafana
              </button>
              <button className="admin-button secondary">
                <span className="material-icons">manage_search</span>
                Collection Manager
              </button>
            </div>
          </div>
        )}

        {selectedSystem === 'api' && (
          <div className="system-detail-card">
            <h3>API Server Details</h3>
            <div className="system-metrics-grid">
              <div className="metric">
                <div className="metric-label">Status</div>
                <div className={`metric-value ${systemStatus.api.status}`}>
                  {systemStatus.api.status === 'healthy' ? 'Healthy' :
                   systemStatus.api.status === 'warning' ? 'Warning' :
                   systemStatus.api.status === 'critical' ? 'Critical' : 'Unknown'}
                </div>
              </div>
              <div className="metric">
                <div className="metric-label">Requests/min</div>
                <div className="metric-value">{systemStatus.api.metrics.requests_per_minute}</div>
              </div>
              <div className="metric">
                <div className="metric-label">Avg Response Time</div>
                <div className="metric-value">{systemStatus.api.metrics.avg_response_time} ms</div>
              </div>
              <div className="metric">
                <div className="metric-label">Error Rate</div>
                <div className="metric-value">{systemStatus.api.metrics.error_rate}%</div>
              </div>
              <div className="metric">
                <div className="metric-label">Uptime</div>
                <div className="metric-value">{systemStatus.api.metrics.uptime}</div>
              </div>
            </div>

            <div className="system-actions">
              <button className="admin-button">
                <span className="material-icons">analytics</span>
                View in Grafana
              </button>
              <button className="admin-button secondary">
                <span className="material-icons">article</span>
                View Logs
              </button>
            </div>
          </div>
        )}

        {selectedSystem === 'overview' && (
          <div className="system-detail-card">
            <h3>System Overview</h3>
            <p>Click on any system card above to view detailed metrics and actions.</p>

            <div className="system-actions">
              <button className="admin-button">
                <span className="material-icons">analytics</span>
                Open Grafana Dashboard
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default SystemMonitoringTab;

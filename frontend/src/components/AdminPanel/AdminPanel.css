/* frontend/src/components/AdminPanel/AdminPanel.css */
.admin-panel {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  background-color: var(--background-primary);
  color: var(--text-primary);
}

.admin-panel-header {
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--background-secondary);
}

.admin-panel-header h1 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 500;
}

.admin-panel-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.admin-panel-sidebar {
  width: 250px;
  border-right: 1px solid var(--border-color);
  background-color: var(--background-secondary);
  overflow-y: auto;
}

.admin-panel-main {
  flex: 1;
  padding: 1rem;
  overflow-y: auto;
}

.admin-panel-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.admin-panel-loading .spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: var(--accent-color);
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.admin-panel-error {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--error-color);
}

/* Card styles for admin panel sections */
.admin-card {
  background-color: var(--background-secondary);
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.admin-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.admin-card-title {
  font-size: 1.2rem;
  font-weight: 500;
  margin: 0;
}

.admin-card-content {
  margin-bottom: 1rem;
}

.admin-card-footer {
  display: flex;
  justify-content: flex-end;
}

/* Button styles */
.admin-button {
  background-color: #2196f3; /* Bright blue for better visibility */
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s, transform 0.1s;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.admin-button:hover {
  background-color: #1976d2;
  transform: translateY(-1px);
}

.admin-button .material-icons {
  margin-right: 6px;
  font-size: 18px;
}

.admin-button.secondary {
  background-color: #607d8b; /* Blue-gray for better visibility */
  color: white;
}

.admin-button.secondary:hover {
  background-color: #455a64;
}

.admin-button.danger {
  background-color: #f44336; /* Bright red for better visibility */
}

.admin-button.danger:hover {
  background-color: #d32f2f;
}

/* Form styles */
.admin-form-group {
  margin-bottom: 1rem;
}

.admin-form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.admin-form-input,
.admin-form-select,
.admin-form-textarea {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--background-primary);
  color: var(--text-primary);
}

.admin-form-textarea {
  min-height: 100px;
  resize: vertical;
}

/* Grid layout for dashboard */
.admin-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .admin-panel-content {
    flex-direction: column;
  }

  .admin-panel-sidebar {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid var(--border-color);
  }

  .admin-grid {
    grid-template-columns: 1fr;
  }
}

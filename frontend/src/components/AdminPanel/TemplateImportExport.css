/* frontend/src/components/AdminPanel/TemplateImportExport.css */

.template-import-export {
  background-color: var(--card-bg);
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.template-import-export h3 {
  margin-top: 0;
  margin-bottom: 20px;
  color: var(--text-primary);
  font-size: 1.2rem;
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 10px;
}

.template-import-export h4 {
  margin-top: 0;
  margin-bottom: 10px;
  color: var(--text-primary);
  font-size: 1.1rem;
}

.template-import-export h5 {
  margin-top: 15px;
  margin-bottom: 10px;
  color: var(--text-primary);
  font-size: 1rem;
}

.import-export-container {
  display: flex;
  gap: 30px;
}

.import-section, .export-section {
  flex: 1;
  background-color: var(--bg-secondary);
  border-radius: 6px;
  padding: 15px;
}

.file-input-container {
  margin: 15px 0;
}

.file-input {
  display: none;
}

.file-input-label {
  display: inline-flex;
  align-items: center;
  padding: 8px 16px;
  background-color: var(--primary-color);
  color: white;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.file-input-label:hover {
  background-color: var(--primary-color-dark);
}

.file-input-label .material-icons {
  margin-right: 8px;
}

.selected-file {
  display: flex;
  align-items: center;
  margin-top: 10px;
  padding: 8px;
  background-color: var(--bg-primary);
  border-radius: 4px;
}

.selected-file .material-icons {
  margin-right: 8px;
  color: var(--text-secondary);
}

.file-name {
  font-size: 0.9rem;
  color: var(--text-primary);
  word-break: break-all;
}

.import-error, .export-error {
  display: flex;
  align-items: flex-start;
  margin: 15px 0;
  padding: 10px;
  background-color: rgba(255, 0, 0, 0.1);
  border-left: 3px solid #f44336;
  border-radius: 4px;
}

.import-error .material-icons, .export-error .material-icons {
  color: #f44336;
  margin-right: 8px;
}

.import-success {
  display: flex;
  align-items: flex-start;
  margin: 15px 0;
  padding: 10px;
  background-color: rgba(0, 255, 0, 0.1);
  border-left: 3px solid #4caf50;
  border-radius: 4px;
}

.import-success .material-icons {
  color: #4caf50;
  margin-right: 8px;
}

.validation-result {
  margin: 15px 0;
  padding: 10px;
  border-radius: 4px;
}

.validation-result.valid {
  background-color: rgba(0, 255, 0, 0.1);
  border-left: 3px solid #4caf50;
}

.validation-result.invalid {
  background-color: rgba(255, 0, 0, 0.1);
  border-left: 3px solid #f44336;
}

.validation-result ul {
  margin: 5px 0;
  padding-left: 20px;
}

.validation-result li {
  margin-bottom: 5px;
}

.template-preview {
  margin: 15px 0;
  padding: 10px;
  background-color: var(--bg-primary);
  border-radius: 4px;
  border-left: 3px solid var(--primary-color);
}

.preview-item {
  margin-bottom: 8px;
}

.preview-label {
  font-weight: bold;
  margin-right: 5px;
  color: var(--text-secondary);
}

.preview-value {
  color: var(--text-primary);
}

.template-select-container {
  margin: 15px 0;
}

.template-select-container label {
  display: block;
  margin-bottom: 5px;
  color: var(--text-secondary);
}

.template-select {
  width: 100%;
  padding: 8px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--bg-primary);
  color: var(--text-primary);
}

.admin-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s;
}

.admin-button:hover:not(:disabled) {
  background-color: var(--primary-color-dark);
}

.admin-button:disabled {
  background-color: var(--disabled-color);
  cursor: not-allowed;
  opacity: 0.7;
}

.admin-button .material-icons {
  margin-right: 8px;
}

.button-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s linear infinite;
  margin-right: 8px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Responsive styles */
@media (max-width: 768px) {
  .import-export-container {
    flex-direction: column;
  }
  
  .import-section, .export-section {
    margin-bottom: 20px;
  }
}

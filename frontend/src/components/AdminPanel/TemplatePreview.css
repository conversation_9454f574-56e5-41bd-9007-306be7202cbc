/* frontend/src/components/AdminPanel/TemplatePreview.css */
.template-preview {
  background-color: var(--background-secondary);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.preview-tabs {
  display: flex;
  background-color: var(--background-tertiary);
  border-bottom: 1px solid var(--border-color);
}

.preview-tab {
  padding: 0.75rem 1.5rem;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--text-secondary);
  transition: background-color 0.2s, color 0.2s;
}

.preview-tab:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.preview-tab.active {
  color: var(--accent-color);
  border-bottom: 2px solid var(--accent-color);
}

.preview-content {
  padding: 1.5rem;
  max-height: 600px;
  overflow-y: auto;
}

/* Form View Styles */
.preview-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.preview-header {
  margin-bottom: 1rem;
}

.preview-header h2 {
  margin: 0;
  font-size: 1.5rem;
  color: var(--text-primary);
}

.preview-section {
  background-color: var(--background-primary);
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.preview-section-title {
  margin-top: 0;
  margin-bottom: 1rem;
  font-size: 1.1rem;
  color: var(--text-primary);
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 0.5rem;
}

.preview-fields {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

.preview-field {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.preview-field-label {
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--text-primary);
}

.preview-required {
  color: #f44336;
  margin-left: 0.25rem;
}

.preview-field-input {
  width: 100%;
}

.preview-field-description {
  font-size: 0.8rem;
  color: var(--text-secondary);
  margin-top: 0.25rem;
}

.preview-text-input,
.preview-number-input,
.preview-date-input,
.preview-select,
.preview-textarea {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--background-secondary);
  color: var(--text-primary);
  font-size: 0.9rem;
}

.preview-textarea {
  resize: vertical;
}

.preview-checkbox,
.preview-radio {
  margin-right: 0.5rem;
}

.preview-radio-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.preview-radio-label {
  display: flex;
  align-items: center;
  font-size: 0.9rem;
  color: var(--text-primary);
}

.preview-multiselect {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.preview-multiselect-item {
  background-color: var(--accent-color-light);
  color: var(--accent-color);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
}

.preview-color {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.preview-color-swatch {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  border: 1px solid var(--border-color);
}

.preview-color-value {
  font-size: 0.9rem;
  color: var(--text-primary);
}

.preview-image {
  width: 100%;
}

.preview-image-thumbnail {
  max-width: 100%;
  max-height: 150px;
  border-radius: 4px;
}

.preview-image-placeholder {
  width: 100%;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--background-tertiary);
  border-radius: 4px;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.preview-unknown-field {
  padding: 0.5rem;
  background-color: #ffebee;
  border: 1px solid #ffcdd2;
  border-radius: 4px;
  color: #c62828;
  font-size: 0.9rem;
}

.preview-relationships {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.preview-relationship {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  background-color: var(--background-secondary);
  border-radius: 4px;
  border: 1px solid var(--border-color);
}

.preview-relationship-type {
  font-weight: 500;
  color: var(--text-primary);
}

.preview-relationship-target {
  color: var(--text-secondary);
}

/* Card View Styles */
.preview-card {
  background-color: var(--background-primary);
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  max-width: 400px;
  margin: 0 auto;
}

.preview-card-header {
  padding: 1rem;
  background-color: var(--accent-color);
  color: white;
}

.preview-card-title {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 500;
}

.preview-card-type {
  font-size: 0.8rem;
  opacity: 0.8;
  margin-top: 0.25rem;
}

.preview-card-content {
  padding: 1rem;
}

.preview-card-section {
  margin-bottom: 1rem;
}

.preview-card-field {
  display: flex;
  margin-bottom: 0.5rem;
}

.preview-card-field-label {
  font-weight: 500;
  margin-right: 0.5rem;
  color: var(--text-primary);
}

.preview-card-field-value {
  color: var(--text-secondary);
  word-break: break-word;
}

.preview-card-footer {
  padding: 0.75rem 1rem;
  background-color: var(--background-secondary);
  border-top: 1px solid var(--border-color);
  font-size: 0.8rem;
  color: var(--text-secondary);
}

.template-preview-error {
  padding: 1rem;
  background-color: #ffebee;
  border: 1px solid #ffcdd2;
  border-radius: 4px;
  color: #c62828;
  text-align: center;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .preview-fields {
    grid-template-columns: 1fr;
  }
  
  .preview-content {
    padding: 1rem;
  }
}

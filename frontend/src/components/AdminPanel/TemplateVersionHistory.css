/* frontend/src/components/AdminPanel/TemplateVersionHistory.css */
.template-version-history {
  background-color: var(--background-secondary);
  border-radius: 8px;
  padding: 1.5rem;
  margin-top: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.template-version-history h3 {
  margin-top: 0;
  margin-bottom: 1rem;
  font-size: 1.2rem;
  font-weight: 500;
  color: var(--text-primary);
}

.version-history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.version-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
}

.version-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  background-color: var(--background-primary);
  border-radius: 4px;
  border: 1px solid var(--border-color);
  cursor: pointer;
  transition: background-color 0.2s, border-color 0.2s;
}

.version-item:hover {
  background-color: var(--background-hover);
}

.version-item.selected {
  background-color: var(--accent-color-light);
  border-color: var(--accent-color);
}

.version-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.version-number {
  font-weight: 500;
  color: var(--text-primary);
}

.version-date {
  font-size: 0.8rem;
  color: var(--text-secondary);
}

.version-actions {
  display: flex;
  gap: 0.5rem;
}

.admin-button.small {
  padding: 0.25rem;
  min-width: auto;
  display: flex;
  align-items: center;
  justify-content: center;
}

.admin-button.small .material-icons {
  font-size: 1.2rem;
}

.loading-indicator,
.error-message,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem 0;
  color: var(--text-secondary);
}

.loading-indicator .material-icons,
.error-message .material-icons {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.error-message .material-icons {
  color: #f44336;
}

.spinning {
  animation: spin 1.5s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Version Comparison Styles */
.version-comparison {
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid var(--border-color);
}

.version-comparison h4 {
  margin-top: 0;
  margin-bottom: 1rem;
  font-size: 1.1rem;
  font-weight: 500;
  color: var(--text-primary);
}

.comparison-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.comparison-version {
  flex: 1;
  padding: 0.75rem;
  background-color: var(--background-tertiary);
  border-radius: 4px;
  text-align: center;
}

.comparison-version h5 {
  margin: 0;
  font-size: 1rem;
  font-weight: 500;
  color: var(--text-primary);
}

.comparison-version p {
  margin: 0.25rem 0 0;
  font-size: 0.8rem;
  color: var(--text-secondary);
}

.comparison-content {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.comparison-item {
  display: flex;
  flex-direction: column;
  padding: 0.75rem;
  background-color: var(--background-primary);
  border-radius: 4px;
  border: 1px solid var(--border-color);
}

.comparison-item-label {
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
}

.comparison-values {
  display: flex;
  gap: 1rem;
  margin-bottom: 0.5rem;
}

.comparison-value {
  flex: 1;
  padding: 0.5rem;
  background-color: var(--background-secondary);
  border-radius: 4px;
  font-family: monospace;
  font-size: 0.9rem;
  white-space: pre-wrap;
  word-break: break-word;
}

.comparison-value.empty {
  color: var(--text-secondary);
  font-style: italic;
}

.comparison-type {
  align-self: flex-end;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
}

.comparison-type.added {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.comparison-type.removed {
  background-color: #ffebee;
  color: #c62828;
}

.comparison-type.modified {
  background-color: #e3f2fd;
  color: #1565c0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .version-history-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }
  
  .comparison-header {
    flex-direction: column;
    gap: 0.75rem;
  }
  
  .comparison-values {
    flex-direction: column;
  }
}

/* frontend/src/components/AdminPanel/AdminDashboard.css */
.admin-dashboard {
  padding: 1rem;
}

.admin-dashboard h2 {
  margin-top: 0;
  margin-bottom: 1.5rem;
  font-weight: 500;
}

.metric-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
}

.metric {
  text-align: center;
  padding: 0.5rem;
}

.metric-value {
  font-size: 1.8rem;
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.metric-label {
  font-size: 0.85rem;
  color: var(--text-secondary);
}

.system-status {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 0.5rem;
}

.status-indicator.healthy {
  background-color: #4caf50;
}

.status-indicator.warning {
  background-color: #ff9800;
}

.status-indicator.critical {
  background-color: #f44336;
}

.status-indicator.unknown {
  background-color: #9e9e9e;
}

.status-text {
  font-weight: 500;
}

.uptime {
  display: flex;
  align-items: center;
}

.uptime-label {
  margin-right: 0.5rem;
  color: var(--text-secondary);
}

.uptime-value {
  font-weight: 500;
}

.quick-actions {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0.75rem;
}

.quick-action-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  background-color: var(--background-tertiary);
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.quick-action-button:hover {
  background-color: var(--background-quaternary);
}

.quick-action-button .material-icons {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .metric-grid {
    grid-template-columns: 1fr;
  }
  
  .quick-actions {
    grid-template-columns: 1fr;
  }
}

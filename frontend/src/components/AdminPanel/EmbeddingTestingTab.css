/* frontend/src/components/AdminPanel/EmbeddingTestingTab.css */
.embedding-testing-tab {
  padding: 1rem;
}

.embedding-testing-tab h2 {
  margin-top: 0;
  margin-bottom: 1.5rem;
  font-weight: 500;
}

.field-weights-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
  margin-top: 0.5rem;
}

.field-weight-item {
  display: flex;
  flex-direction: column;
}

.field-weight-item label {
  margin-bottom: 0.25rem;
  font-size: 0.9rem;
}

.field-weight-item input {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--background-primary);
  color: var(--text-primary);
}

.error-card {
  background-color: rgba(244, 67, 54, 0.1);
  border-left: 4px solid var(--error-color);
}

.error-message {
  color: var(--error-color);
  margin: 0;
}

.results-card {
  margin-top: 1.5rem;
}

.results-section {
  margin-bottom: 1.5rem;
}

.results-section h4 {
  margin-top: 0;
  margin-bottom: 1rem;
  font-weight: 500;
  font-size: 1rem;
  color: var(--text-secondary);
}

.embedding-vector {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 0.5rem;
  background-color: var(--background-tertiary);
  padding: 1rem;
  border-radius: 4px;
  font-family: monospace;
}

.vector-dimension {
  display: flex;
  justify-content: space-between;
}

.dimension-index {
  color: var(--text-secondary);
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 1rem;
}

.metrics-grid .metric {
  background-color: var(--background-tertiary);
  padding: 1rem;
  border-radius: 4px;
  text-align: center;
}

.metrics-grid .metric-label {
  font-size: 0.9rem;
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
}

.metrics-grid .metric-value {
  font-size: 1.2rem;
  font-weight: 500;
}

.similarity-bars {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.similarity-item {
  display: flex;
  align-items: center;
}

.similarity-label {
  width: 100px;
  font-weight: 500;
  text-transform: capitalize;
}

.similarity-bar-container {
  flex: 1;
  height: 24px;
  background-color: var(--background-tertiary);
  border-radius: 4px;
  position: relative;
  overflow: hidden;
}

.similarity-bar {
  height: 100%;
  background-color: var(--accent-color);
  border-radius: 4px;
}

.similarity-value {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 0.85rem;
  font-weight: 500;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .field-weights-container {
    grid-template-columns: 1fr;
  }
  
  .embedding-vector {
    grid-template-columns: 1fr;
  }
  
  .metrics-grid {
    grid-template-columns: 1fr;
  }
  
  .similarity-item {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .similarity-label {
    width: 100%;
    margin-bottom: 0.25rem;
  }
}

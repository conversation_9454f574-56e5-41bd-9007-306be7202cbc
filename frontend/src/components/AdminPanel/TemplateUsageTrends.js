// frontend/src/components/AdminPanel/TemplateUsageTrends.js
import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import axios from 'axios';
import { BASE_URL } from '../../utils/apiConfig';
import { selectAuthToken } from '../../redux/slices/authSlice';
import './TemplateUsageTrends.css';

/**
 * Template Usage Trends component
 * Displays usage trends for templates over time
 * 
 * @param {Object} props - Component props
 * @param {string} props.templateId - Optional template ID to filter by
 * @returns {JSX.Element} Template usage trends UI
 */
const TemplateUsageTrends = ({ templateId }) => {
  const token = useSelector(selectAuthToken);
  
  // State for trends data
  const [trends, setTrends] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [period, setPeriod] = useState('monthly');
  const [limit, setLimit] = useState(12);
  
  // Fetch trends data
  useEffect(() => {
    const fetchTrends = async () => {
      setIsLoading(true);
      setError(null);
      
      try {
        const response = await axios.get(`${BASE_URL}/api/templates/usage/trends`, {
          params: {
            template_id: templateId,
            period,
            limit
          },
          headers: { Authorization: `Bearer ${token}` }
        });
        
        setTrends(response.data);
      } catch (error) {
        console.error('Error fetching template usage trends:', error);
        setError('Failed to fetch template usage trends: ' + (error.response?.data?.detail || error.message));
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchTrends();
  }, [token, templateId, period, limit]);
  
  // Handle period change
  const handlePeriodChange = (e) => {
    setPeriod(e.target.value);
  };
  
  // Handle limit change
  const handleLimitChange = (e) => {
    setLimit(parseInt(e.target.value, 10));
  };
  
  // Format date for display
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    
    if (period === 'daily') {
      return date.toLocaleDateString();
    } else if (period === 'weekly') {
      return `Week of ${date.toLocaleDateString()}`;
    } else {
      return date.toLocaleDateString(undefined, { year: 'numeric', month: 'long' });
    }
  };
  
  // Find the maximum usage value for scaling the bars
  const maxUsage = trends.length > 0 
    ? Math.max(...trends.map(trend => trend.total_usage))
    : 0;
  
  // Render loading state
  if (isLoading) {
    return (
      <div className="template-usage-trends">
        <div className="trends-header">
          <h3>Usage Trends</h3>
          <div className="trends-controls">
            <div className="control-group">
              <label htmlFor="period-select">Period:</label>
              <select 
                id="period-select" 
                value={period} 
                onChange={handlePeriodChange}
                disabled
              >
                <option value="daily">Daily</option>
                <option value="weekly">Weekly</option>
                <option value="monthly">Monthly</option>
              </select>
            </div>
            
            <div className="control-group">
              <label htmlFor="limit-select">Show:</label>
              <select 
                id="limit-select" 
                value={limit} 
                onChange={handleLimitChange}
                disabled
              >
                <option value="6">Last 6</option>
                <option value="12">Last 12</option>
                <option value="24">Last 24</option>
              </select>
            </div>
          </div>
        </div>
        
        <div className="loading-indicator">
          <span className="material-icons spinning">refresh</span>
          <p>Loading trends data...</p>
        </div>
      </div>
    );
  }
  
  // Render error state
  if (error) {
    return (
      <div className="template-usage-trends">
        <div className="trends-header">
          <h3>Usage Trends</h3>
          <div className="trends-controls">
            <div className="control-group">
              <label htmlFor="period-select">Period:</label>
              <select 
                id="period-select" 
                value={period} 
                onChange={handlePeriodChange}
              >
                <option value="daily">Daily</option>
                <option value="weekly">Weekly</option>
                <option value="monthly">Monthly</option>
              </select>
            </div>
            
            <div className="control-group">
              <label htmlFor="limit-select">Show:</label>
              <select 
                id="limit-select" 
                value={limit} 
                onChange={handleLimitChange}
              >
                <option value="6">Last 6</option>
                <option value="12">Last 12</option>
                <option value="24">Last 24</option>
              </select>
            </div>
          </div>
        </div>
        
        <div className="error-message">
          <span className="material-icons">error</span>
          <p>{error}</p>
        </div>
      </div>
    );
  }
  
  // Render empty state
  if (trends.length === 0) {
    return (
      <div className="template-usage-trends">
        <div className="trends-header">
          <h3>Usage Trends</h3>
          <div className="trends-controls">
            <div className="control-group">
              <label htmlFor="period-select">Period:</label>
              <select 
                id="period-select" 
                value={period} 
                onChange={handlePeriodChange}
              >
                <option value="daily">Daily</option>
                <option value="weekly">Weekly</option>
                <option value="monthly">Monthly</option>
              </select>
            </div>
            
            <div className="control-group">
              <label htmlFor="limit-select">Show:</label>
              <select 
                id="limit-select" 
                value={limit} 
                onChange={handleLimitChange}
              >
                <option value="6">Last 6</option>
                <option value="12">Last 12</option>
                <option value="24">Last 24</option>
              </select>
            </div>
          </div>
        </div>
        
        <div className="empty-state">
          <span className="material-icons">bar_chart</span>
          <p>No usage data available for the selected period.</p>
        </div>
      </div>
    );
  }
  
  // Render trends data
  return (
    <div className="template-usage-trends">
      <div className="trends-header">
        <h3>Usage Trends</h3>
        <div className="trends-controls">
          <div className="control-group">
            <label htmlFor="period-select">Period:</label>
            <select 
              id="period-select" 
              value={period} 
              onChange={handlePeriodChange}
            >
              <option value="daily">Daily</option>
              <option value="weekly">Weekly</option>
              <option value="monthly">Monthly</option>
            </select>
          </div>
          
          <div className="control-group">
            <label htmlFor="limit-select">Show:</label>
            <select 
              id="limit-select" 
              value={limit} 
              onChange={handleLimitChange}
            >
              <option value="6">Last 6</option>
              <option value="12">Last 12</option>
              <option value="24">Last 24</option>
            </select>
          </div>
        </div>
      </div>
      
      <div className="trends-chart">
        <div className="chart-container">
          <div className="y-axis">
            <div className="axis-label">Usage Count</div>
            <div className="axis-ticks">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="axis-tick">
                  {Math.round(maxUsage * (4 - i) / 4)}
                </div>
              ))}
              <div className="axis-tick">0</div>
            </div>
          </div>
          
          <div className="chart-content">
            <div className="bars-container">
              {trends.map((trend, index) => (
                <div key={index} className="bar-group">
                  <div 
                    className="bar usage-bar" 
                    style={{ 
                      height: `${maxUsage ? (trend.total_usage / maxUsage) * 100 : 0}%` 
                    }}
                    title={`Total Usage: ${trend.total_usage}`}
                  ></div>
                  <div className="bar-label">{formatDate(trend.time_period)}</div>
                </div>
              ))}
            </div>
            
            <div className="x-axis">
              <div className="axis-label">Time Period</div>
            </div>
          </div>
        </div>
        
        <div className="trends-metrics">
          <div className="metric-card">
            <h4>Total Usage</h4>
            <div className="metric-value">
              {trends.reduce((sum, trend) => sum + trend.total_usage, 0)}
            </div>
          </div>
          
          <div className="metric-card">
            <h4>Unique Users</h4>
            <div className="metric-value">
              {Math.max(...trends.map(trend => trend.unique_users))}
            </div>
          </div>
          
          <div className="metric-card">
            <h4>Unique Books</h4>
            <div className="metric-value">
              {Math.max(...trends.map(trend => trend.unique_books))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TemplateUsageTrends;

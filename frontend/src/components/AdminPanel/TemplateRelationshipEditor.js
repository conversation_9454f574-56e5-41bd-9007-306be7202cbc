// frontend/src/components/AdminPanel/TemplateRelationshipEditor.js
import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { BASE_URL } from '../../utils/apiConfig';
import { getHeaders } from '../../services/apiService';
import './TemplateRelationshipEditor.css';

/**
 * Template Relationship Editor component
 * Provides an interface for managing template relationships
 *
 * @param {Object} props - Component props
 * @param {Object} props.template - Template data
 * @param {Array} props.templates - All available templates
 * @param {Function} props.onChange - Function to call when relationships change
 * @returns {JSX.Element} Template relationship editor UI
 */
const TemplateRelationshipEditor = ({ template, templates, onChange }) => {
  // State for relationship definitions
  const [relationships, setRelationships] = useState([]);
  // State for relationship types
  const [relationshipTypes, setRelationshipTypes] = useState([]);
  // State for new relationship
  const [newRelationship, setNewRelationship] = useState({
    target_template_id: '',
    relationship_type_id: '',
    description: '',
    label: ''
  });
  // State for loading
  const [isLoading, setIsLoading] = useState(true);
  // State for error
  const [error, setError] = useState(null);

  // Fetch relationship types on component mount
  useEffect(() => {
    const fetchRelationshipTypes = async () => {
      try {
        const headers = getHeaders();
        const response = await axios.get(`${BASE_URL}/api/relationship-types`, { headers });
        setRelationshipTypes(response.data);
      } catch (error) {
        console.error('Error fetching relationship types:', error);
        setError('Failed to load relationship types. Please try again later.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchRelationshipTypes();
  }, []);

  // Initialize relationships from template
  useEffect(() => {
    if (template && template.relationship_definitions) {
      const validRelationships = template.relationship_definitions.valid_relationships || [];
      setRelationships(validRelationships);
    }
  }, [template]);

  // Handle input change for new relationship
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setNewRelationship(prev => ({
      ...prev,
      [name]: value
    }));

    // Auto-generate label if relationship type and target template are selected
    if (name === 'relationship_type_id' || name === 'target_template_id') {
      const relationshipType = name === 'relationship_type_id'
        ? relationshipTypes.find(rt => rt.relationship_type_id === value)
        : relationshipTypes.find(rt => rt.relationship_type_id === newRelationship.relationship_type_id);

      const targetTemplate = name === 'target_template_id'
        ? templates.find(t => t.template_id === value)
        : templates.find(t => t.template_id === newRelationship.target_template_id);

      if (relationshipType && targetTemplate) {
        setNewRelationship(prev => ({
          ...prev,
          label: `${relationshipType.name} ${targetTemplate.display_name}`
        }));
      }
    }
  };

  // Handle add relationship
  const handleAddRelationship = () => {
    // Validate required fields
    if (!newRelationship.target_template_id || !newRelationship.relationship_type_id) {
      setError('Target template and relationship type are required.');
      return;
    }

    // Find the selected relationship type
    const selectedType = relationshipTypes.find(
      rt => rt.relationship_type_id === newRelationship.relationship_type_id
    );

    if (!selectedType) {
      setError('Invalid relationship type selected.');
      return;
    }

    // Create the new relationship
    const relationship = {
      target_template: newRelationship.target_template_id,
      relationship_type: newRelationship.relationship_type_id,
      inverse_relationship: selectedType.inverse_relationship_type_id,
      label: newRelationship.label || `${selectedType.name}`,
      description: newRelationship.description || ''
    };

    // Add to relationships array
    const updatedRelationships = [...relationships, relationship];
    setRelationships(updatedRelationships);

    // Call onChange with updated relationships
    if (onChange) {
      onChange({ valid_relationships: updatedRelationships });
    }

    // Reset new relationship form
    setNewRelationship({
      target_template_id: '',
      relationship_type_id: '',
      description: '',
      label: ''
    });

    // Clear any errors
    setError(null);
  };

  // Handle remove relationship
  const handleRemoveRelationship = (index) => {
    const updatedRelationships = relationships.filter((_, i) => i !== index);
    setRelationships(updatedRelationships);

    // Call onChange with updated relationships
    if (onChange) {
      onChange({ valid_relationships: updatedRelationships });
    }
  };

  // Find inverse relationship name
  const getInverseRelationshipName = (inverseId) => {
    const inverseType = relationshipTypes.find(rt => rt.relationship_type_id === inverseId);
    return inverseType ? inverseType.name : inverseId;
  };

  // Find template name by ID
  const getTemplateName = (templateId) => {
    const template = templates.find(t => t.template_id === templateId);
    return template ? template.display_name : templateId;
  };

  if (isLoading) {
    return <div className="loading">Loading relationship types...</div>;
  }

  if (error) {
    return <div className="error">{error}</div>;
  }

  return (
    <div className="template-relationship-editor">
      <h3>Template Relationships</h3>

      {relationships.length > 0 ? (
        <div className="relationships-list">
          <h4>Defined Relationships</h4>
          <ul>
            {relationships.map((relationship, index) => (
              <li key={index} className="relationship-item">
                <div className="relationship-info">
                  <strong>{relationship.label}</strong>
                  <p>Target: {getTemplateName(relationship.target_template)}</p>
                  <p>Type: {relationship.relationship_type} (Inverse: {getInverseRelationshipName(relationship.inverse_relationship)})</p>
                  {relationship.description && <p>Description: {relationship.description}</p>}
                </div>
                <button
                  type="button"
                  className="remove-button"
                  onClick={() => handleRemoveRelationship(index)}
                >
                  Remove
                </button>
              </li>
            ))}
          </ul>
        </div>
      ) : (
        <p>No relationships defined for this template yet.</p>
      )}

      <div className="add-relationship">
        <h4>Add New Relationship</h4>

        <div className="form-group">
          <label htmlFor="target_template_id">Target Template</label>
          <select
            id="target_template_id"
            name="target_template_id"
            value={newRelationship.target_template_id}
            onChange={handleInputChange}
          >
            <option value="">Select a template</option>
            {templates.map(template => (
              <option key={template.template_id} value={template.template_id}>
                {template.display_name}
              </option>
            ))}
          </select>
        </div>

        <div className="form-group">
          <label htmlFor="relationship_type_id">Relationship Type</label>
          <select
            id="relationship_type_id"
            name="relationship_type_id"
            value={newRelationship.relationship_type_id}
            onChange={handleInputChange}
          >
            <option value="">Select a relationship type</option>
            {relationshipTypes.map(type => (
              <option key={type.relationship_type_id} value={type.relationship_type_id}>
                {type.name} (Inverse: {getInverseRelationshipName(type.inverse_relationship_type_id)})
              </option>
            ))}
          </select>
        </div>

        <div className="form-group">
          <label htmlFor="label">Label</label>
          <input
            type="text"
            id="label"
            name="label"
            value={newRelationship.label}
            onChange={handleInputChange}
            placeholder="e.g., Contains Planet"
          />
        </div>

        <div className="form-group">
          <label htmlFor="description">Description</label>
          <textarea
            id="description"
            name="description"
            value={newRelationship.description}
            onChange={handleInputChange}
            placeholder="Describe the relationship"
            rows={2}
          />
        </div>

        <button
          type="button"
          onClick={handleAddRelationship}
          className="add-relationship-button"
        >
          Add Relationship
        </button>
      </div>
    </div>
  );
};

export default TemplateRelationshipEditor;

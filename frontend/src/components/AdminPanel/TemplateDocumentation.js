// frontend/src/components/AdminPanel/TemplateDocumentation.js
import React from 'react';
import './TemplateDocumentation.css';

/**
 * Template Documentation component
 * Generates documentation for a template
 * 
 * @param {Object} props - Component props
 * @param {Object} props.template - The template to generate documentation for
 * @returns {JSX.Element} Template documentation UI
 */
const TemplateDocumentation = ({ template }) => {
  if (!template) {
    return <div className="documentation-error">No template provided</div>;
  }
  
  // Format field type for display
  const formatFieldType = (fieldType) => {
    return fieldType.charAt(0).toUpperCase() + fieldType.slice(1).replace(/_/g, ' ');
  };
  
  // Generate field documentation
  const renderFieldDocumentation = (field) => {
    return (
      <div className="field-documentation" key={field.id || field.label}>
        <h4>{field.label}</h4>
        <div className="field-properties">
          <div className="field-property">
            <span className="property-label">Type:</span>
            <span className="property-value">{formatFieldType(field.field_type || field.type)}</span>
          </div>
          
          {field.required && (
            <div className="field-property">
              <span className="property-label">Required:</span>
              <span className="property-value required">Yes</span>
            </div>
          )}
          
          {field.description && (
            <div className="field-property description">
              <span className="property-label">Description:</span>
              <span className="property-value">{field.description}</span>
            </div>
          )}
          
          {(field.field_type === 'select' || field.field_type === 'multiselect' || field.field_type === 'radio') && 
           field.options && field.options.length > 0 && (
            <div className="field-property options">
              <span className="property-label">Options:</span>
              <ul className="options-list">
                {field.options.map((option, index) => (
                  <li key={index}>{typeof option === 'string' ? option : option.label || option.value}</li>
                ))}
              </ul>
            </div>
          )}
          
          {field.default_value !== undefined && field.default_value !== null && (
            <div className="field-property">
              <span className="property-label">Default Value:</span>
              <span className="property-value">
                {typeof field.default_value === 'object' 
                  ? JSON.stringify(field.default_value) 
                  : String(field.default_value)}
              </span>
            </div>
          )}
        </div>
      </div>
    );
  };
  
  // Generate relationship documentation
  const renderRelationshipDocumentation = (relationship) => {
    return (
      <div className="relationship-documentation" key={relationship.target_template_id || relationship.target_template}>
        <h4>Relationship to {relationship.target_template_name || relationship.target_template}</h4>
        <div className="relationship-properties">
          <div className="relationship-property">
            <span className="property-label">Type:</span>
            <span className="property-value">{relationship.relationship_type || relationship.relationship_type_id}</span>
          </div>
          
          {relationship.inverse_relationship && (
            <div className="relationship-property">
              <span className="property-label">Inverse Relationship:</span>
              <span className="property-value">{relationship.inverse_relationship}</span>
            </div>
          )}
          
          {relationship.description && (
            <div className="relationship-property description">
              <span className="property-label">Description:</span>
              <span className="property-value">{relationship.description}</span>
            </div>
          )}
        </div>
      </div>
    );
  };
  
  // Generate template metadata
  const renderTemplateMetadata = () => {
    return (
      <div className="template-metadata">
        <h3>Template Metadata</h3>
        <table className="metadata-table">
          <tbody>
            <tr>
              <th>Template ID:</th>
              <td>{template.template_id || 'N/A'}</td>
            </tr>
            <tr>
              <th>Name:</th>
              <td>{template.template_name}</td>
            </tr>
            <tr>
              <th>Display Name:</th>
              <td>{template.display_name}</td>
            </tr>
            <tr>
              <th>Category:</th>
              <td>{template.category_name || template.category_id}</td>
            </tr>
            {template.version && (
              <tr>
                <th>Version:</th>
                <td>{template.version}</td>
              </tr>
            )}
            {template.parent_template_id && (
              <tr>
                <th>Parent Template:</th>
                <td>{template.parent_template_name || template.parent_template_id}</td>
              </tr>
            )}
            {template.is_system_template !== undefined && (
              <tr>
                <th>System Template:</th>
                <td>{template.is_system_template ? 'Yes' : 'No'}</td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    );
  };
  
  return (
    <div className="template-documentation">
      <div className="documentation-header">
        <h2>{template.display_name}</h2>
        {template.description && <p className="template-description">{template.description}</p>}
      </div>
      
      {renderTemplateMetadata()}
      
      <div className="documentation-content">
        <h3>Fields</h3>
        {template.field_definitions && Object.keys(template.field_definitions).length > 0 ? (
          Object.entries(template.field_definitions).map(([sectionName, fields]) => (
            <div className="section-documentation" key={sectionName}>
              <h3 className="section-name">{sectionName}</h3>
              {fields.map(field => renderFieldDocumentation(field))}
            </div>
          ))
        ) : (
          <p className="no-fields">No fields defined</p>
        )}
        
        {template.relationship_definitions && 
         template.relationship_definitions.valid_relationships && 
         template.relationship_definitions.valid_relationships.length > 0 && (
          <div className="relationships-documentation">
            <h3>Relationships</h3>
            {template.relationship_definitions.valid_relationships.map(relationship => 
              renderRelationshipDocumentation(relationship)
            )}
          </div>
        )}
      </div>
      
      <div className="documentation-footer">
        <p>Documentation generated on {new Date().toLocaleString()}</p>
      </div>
    </div>
  );
};

export default TemplateDocumentation;

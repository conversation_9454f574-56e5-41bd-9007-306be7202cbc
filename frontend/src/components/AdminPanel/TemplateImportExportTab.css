/* frontend/src/components/AdminPanel/TemplateImportExportTab.css */

.template-import-export-tab {
  padding: 20px;
}

.template-import-export-tab h2 {
  margin-top: 0;
  margin-bottom: 20px;
  color: var(--text-primary);
  font-size: 1.5rem;
}

.tab-description {
  margin-bottom: 20px;
  color: var(--text-secondary);
  line-height: 1.5;
  max-width: 800px;
}

.recent-imports {
  margin-top: 30px;
  background-color: var(--card-bg);
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.recent-imports h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: var(--text-primary);
  font-size: 1.2rem;
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 10px;
}

.recent-imports-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 15px;
}

.recent-import-item {
  background-color: var(--bg-secondary);
  border-radius: 6px;
  padding: 15px;
  transition: transform 0.2s, box-shadow 0.2s;
}

.recent-import-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.recent-import-item h4 {
  margin-top: 0;
  margin-bottom: 8px;
  color: var(--text-primary);
  font-size: 1.1rem;
}

.template-description {
  color: var(--text-secondary);
  font-size: 0.9rem;
  margin-bottom: 10px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.template-meta {
  display: flex;
  gap: 15px;
  font-size: 0.8rem;
  color: var(--text-secondary);
}

.template-category, .template-version {
  display: flex;
  align-items: center;
}

.template-meta .material-icons {
  font-size: 16px;
  margin-right: 4px;
}

.template-import-export-help {
  margin-top: 30px;
  background-color: var(--card-bg);
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.template-import-export-help h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: var(--text-primary);
  font-size: 1.2rem;
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 10px;
}

.help-section {
  margin-bottom: 20px;
}

.help-section h4 {
  margin-top: 0;
  margin-bottom: 10px;
  color: var(--text-primary);
  font-size: 1.1rem;
}

.help-section ul {
  margin: 0;
  padding-left: 20px;
  color: var(--text-secondary);
  line-height: 1.5;
}

.help-section li {
  margin-bottom: 5px;
}

.code-example {
  background-color: var(--bg-primary);
  border-radius: 4px;
  padding: 15px;
  overflow-x: auto;
  font-family: monospace;
  font-size: 0.9rem;
  color: var(--text-primary);
  line-height: 1.4;
  margin: 10px 0;
  max-height: 300px;
  overflow-y: auto;
}

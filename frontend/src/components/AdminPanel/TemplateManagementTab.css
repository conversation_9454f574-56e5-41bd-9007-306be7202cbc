/* frontend/src/components/AdminPanel/TemplateManagementTab.css */
.template-management-tab {
  padding: 1rem;
}

.template-management-tab h2 {
  margin-top: 0;
  margin-bottom: 1.5rem;
  font-weight: 500;
}

.template-management-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.template-control-buttons {
  display: flex;
  gap: 10px;
}

/* Make the New Template button more visible */
.template-management-controls .admin-button {
  font-weight: 600;
  padding: 10px 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: transform 0.1s, background-color 0.2s;
}

.template-management-controls .admin-button:first-child {
  background-color: #4caf50; /* Green for better visibility */
}

.template-management-controls .admin-button:first-child:hover {
  background-color: #388e3c;
}

.template-management-controls .admin-button:hover {
  transform: translateY(-1px);
}

.search-and-filters {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  flex: 1;
}

.search-container {
  flex: 1;
  min-width: 200px;
}

.search-input {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--background-primary);
  color: var(--text-primary);
}

.filters-container {
  display: flex;
  gap: 1rem;
}

.filter {
  display: flex;
  align-items: center;
}

.filter label {
  margin-right: 0.5rem;
  white-space: nowrap;
}

.filter select {
  padding: 0.5rem;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--background-primary);
  color: var(--text-primary);
}

.template-management-content {
  display: flex;
  gap: 1.5rem;
  height: calc(100vh - 200px);
}

.templates-list {
  flex: 1;
  min-width: 300px;
  max-width: 800px;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  overflow: hidden;
  background-color: var(--background-secondary);
}

.templates-list-header {
  display: flex;
  background-color: var(--background-tertiary);
  padding: 0.75rem 1rem;
  font-weight: 500;
  border-bottom: 1px solid var(--border-color);
}

.templates-list-body {
  overflow-y: auto;
  max-height: calc(100vh - 250px);
  height: 100%;
}

.template-row {
  display: flex;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid var(--border-color);
  cursor: pointer;
  transition: background-color 0.2s;
}

.template-row:hover {
  background-color: var(--background-tertiary);
}

.template-row.selected {
  background-color: var(--background-tertiary);
  border-left: 3px solid var(--accent-color);
}

.template-column {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.name-column {
  flex: 2;
  min-width: 150px;
}

.category-column {
  flex: 2;
  min-width: 150px;
}

.type-column {
  flex: 1;
  min-width: 80px;
}

.version-column {
  flex: 1;
  min-width: 80px;
}

.usage-column {
  flex: 1;
  min-width: 80px;
  text-align: right;
}

.no-templates {
  padding: 2rem;
  text-align: center;
  color: var(--text-secondary);
}

.template-details,
.template-editor {
  flex: 1;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  overflow: hidden;
  background-color: var(--background-secondary);
  display: flex;
  flex-direction: column;
}

.template-details-header,
.template-editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--background-tertiary);
}

.template-details-header h3,
.template-editor-header h3 {
  margin: 0;
  font-weight: 500;
}

.template-actions {
  display: flex;
  gap: 0.75rem;
}

/* Improve button visibility */
.template-actions .admin-button {
  padding: 8px 16px;
  font-weight: 600;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: transform 0.1s, background-color 0.2s;
  min-width: 100px;
  text-align: center;
}

.template-actions .admin-button:hover {
  transform: translateY(-1px);
}

.template-actions .admin-button.danger {
  background-color: #f44336;
}

.template-actions .admin-button.danger:hover {
  background-color: #d32f2f;
}

.template-details-content,
.template-editor-content {
  padding: 1rem;
  overflow-y: auto;
  flex: 1;
}

.template-info {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.info-item {
  display: flex;
  flex-direction: column;
}

.info-label {
  font-size: 0.85rem;
  color: var(--text-secondary);
  margin-bottom: 0.25rem;
}

.info-value {
  font-weight: 500;
}

.template-description h4 {
  margin-top: 0;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.template-description p {
  margin-top: 0;
  line-height: 1.5;
}

/* Error display */
.admin-panel-error {
  padding: 20px;
  text-align: center;
}

.admin-panel-error h2 {
  margin-bottom: 20px;
}

.admin-panel-error .error-message {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
  padding: 15px;
  background-color: #fff3f3;
  border: 1px solid #ffcdd2;
  border-radius: 5px;
  color: #d32f2f;
}

.admin-panel-error .error-message .material-icons {
  margin-right: 10px;
  font-size: 24px;
}

.admin-panel-error .admin-button {
  display: inline-flex;
  align-items: center;
  padding: 8px 16px;
  background-color: #f1f3f5;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  color: #495057;
  transition: background-color 0.2s;
}

.admin-panel-error .admin-button:hover {
  background-color: #e9ecef;
}

.admin-panel-error .admin-button .material-icons {
  margin-right: 8px;
  font-size: 18px;
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .template-management-content {
    flex-direction: column;
    height: auto;
  }

  .templates-list {
    max-width: none;
  }

  .templates-list-body {
    max-height: 400px;
  }
}

@media (max-width: 768px) {
  .template-management-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .search-and-filters {
    flex-direction: column;
  }

  .filters-container {
    flex-direction: column;
  }

  .template-column.category-column,
  .template-column.type-column {
    display: none;
  }
}

/* Modal styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  width: 500px;
  max-width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.modal-large {
  width: 800px;
  max-width: 95%;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #eee;
}

.modal-header h3 {
  margin: 0;
  font-size: 1.2rem;
  color: #333;
}

.modal-close-button {
  background: none;
  border: none;
  cursor: pointer;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 5px;
}

.modal-close-button:hover {
  color: #333;
}

.modal-body {
  padding: 20px;
  flex-grow: 1;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding: 15px 20px;
  border-top: 1px solid #eee;
}

.file-input {
  display: block;
  width: 100%;
  padding: 10px;
  margin: 10px 0;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: #f9f9f9;
}

.import-error {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  padding: 10px;
  margin-top: 10px;
  background-color: #ffebee;
  border: 1px solid #ffcdd2;
  border-radius: 4px;
  color: #c62828;
}

.import-error .material-icons {
  color: #f44336;
}

// frontend/src/components/AdminPanel/AdminDashboard.js
import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { selectAuthToken } from '../../redux/slices/authSlice';
import axios from 'axios';
import { BASE_URL } from '../../utils/apiConfig';
import './AdminDashboard.css';

/**
 * Admin Dashboard component
 * Displays overview statistics and quick access to common admin tasks
 *
 * @returns {JSX.Element} Admin dashboard UI
 */
const AdminDashboard = () => {
  const token = useSelector(selectAuthToken);

  // Sample state for dashboard metrics
  const [metrics, setMetrics] = useState({
    users: { total: 0, active: 0, new: 0 },
    templates: { total: 0, system: 0, custom: 0 },
    books: { total: 0 },
    system: { status: 'unknown', uptime: '0h 0m' }
  });

  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fetch dashboard metrics
  useEffect(() => {
    const fetchMetrics = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // Try to fetch real data from API
        // If the endpoint is not yet implemented, fall back to mock data
        try {
          const response = await axios.get(`${BASE_URL}/api/admin/dashboard-metrics`, {
            headers: { Authorization: `Bearer ${token}` }
          });

          if (response.data) {
            setMetrics(response.data);
            setIsLoading(false);
            return;
          }
        } catch (apiError) {
          console.log('API endpoint not available yet, using mock data');
        }

        // Use mock data as fallback
        setTimeout(() => {
          setMetrics({
            users: { total: 125, active: 87, new: 12 },
            templates: { total: 147, system: 120, custom: 27 },
            books: { total: 342 },
            system: { status: 'healthy', uptime: '7d 14h 23m' }
          });
          setIsLoading(false);
        }, 1000);
      } catch (error) {
        console.error('Error fetching dashboard metrics:', error);
        setError('Failed to load dashboard metrics: ' + (error.response?.data?.detail || error.message));
        setIsLoading(false);
      }
    };

    if (token) {
      fetchMetrics();
    }
  }, [token]);

  // Render loading state
  if (isLoading) {
    return (
      <div className="admin-panel-loading">
        <div className="spinner"></div>
        <p>Loading dashboard data...</p>
      </div>
    );
  }

  // Render error state
  if (error) {
    return (
      <div className="admin-panel-error">
        <h2>Dashboard</h2>
        <div className="error-message">
          <span className="material-icons">error</span>
          <p>{error}</p>
        </div>
        <button
          className="admin-button"
          onClick={() => window.location.reload()}
        >
          <span className="material-icons">refresh</span>
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="admin-dashboard">
      <h2>Dashboard</h2>

      <div className="admin-grid">
        {/* Users Card */}
        <div className="admin-card">
          <div className="admin-card-header">
            <h3 className="admin-card-title">Users</h3>
            <span className="material-icons">people</span>
          </div>
          <div className="admin-card-content">
            <div className="metric-grid">
              <div className="metric">
                <div className="metric-value">{metrics.users.total}</div>
                <div className="metric-label">Total Users</div>
              </div>
              <div className="metric">
                <div className="metric-value">{metrics.users.active}</div>
                <div className="metric-label">Active Users</div>
              </div>
              <div className="metric">
                <div className="metric-value">{metrics.users.new}</div>
                <div className="metric-label">New Users (30d)</div>
              </div>
            </div>
          </div>
          <div className="admin-card-footer">
            <button className="admin-button" onClick={() => window.location.href = '/admin/users'}>
              Manage Users
            </button>
          </div>
        </div>

        {/* Templates Card */}
        <div className="admin-card">
          <div className="admin-card-header">
            <h3 className="admin-card-title">Templates</h3>
            <span className="material-icons">description</span>
          </div>
          <div className="admin-card-content">
            <div className="metric-grid">
              <div className="metric">
                <div className="metric-value">{metrics.templates.total}</div>
                <div className="metric-label">Total Templates</div>
              </div>
              <div className="metric">
                <div className="metric-value">{metrics.templates.system}</div>
                <div className="metric-label">System Templates</div>
              </div>
              <div className="metric">
                <div className="metric-value">{metrics.templates.custom}</div>
                <div className="metric-label">Custom Templates</div>
              </div>
            </div>
          </div>
          <div className="admin-card-footer">
            <button className="admin-button" onClick={() => window.location.href = '/admin/templates'}>
              Manage Templates
            </button>
          </div>
        </div>

        {/* System Status Card */}
        <div className="admin-card">
          <div className="admin-card-header">
            <h3 className="admin-card-title">System Status</h3>
            <span className="material-icons">monitoring</span>
          </div>
          <div className="admin-card-content">
            <div className="system-status">
              <div className={`status-indicator ${metrics.system.status}`}></div>
              <div className="status-text">
                {metrics.system.status === 'healthy' ? 'All Systems Operational' :
                 metrics.system.status === 'warning' ? 'System Issues Detected' :
                 metrics.system.status === 'critical' ? 'Critical System Errors' : 'Status Unknown'}
              </div>
            </div>
            <div className="uptime">
              <span className="uptime-label">Uptime:</span>
              <span className="uptime-value">{metrics.system.uptime}</span>
            </div>
          </div>
          <div className="admin-card-footer">
            <button className="admin-button" onClick={() => window.location.href = '/admin/system'}>
              System Monitoring
            </button>
          </div>
        </div>

        {/* Quick Actions Card */}
        <div className="admin-card">
          <div className="admin-card-header">
            <h3 className="admin-card-title">Quick Actions</h3>
            <span className="material-icons">flash_on</span>
          </div>
          <div className="admin-card-content">
            <div className="quick-actions">
              <button className="quick-action-button" onClick={() => window.location.href = '/admin/templates/new'}>
                <span className="material-icons">add</span>
                <span>New Template</span>
              </button>
              <button className="quick-action-button" onClick={() => window.location.href = '/admin/embedding'}>
                <span className="material-icons">code</span>
                <span>Test Embeddings</span>
              </button>
              <button className="quick-action-button" onClick={() => window.location.href = '/admin/database'}>
                <span className="material-icons">storage</span>
                <span>Database Tools</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;

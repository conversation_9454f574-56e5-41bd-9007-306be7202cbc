/* frontend/src/components/AdminPanel/TemplateDocumentation.css */
.template-documentation {
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  color: #333;
  max-width: 100%;
  margin: 0 auto;
}

.documentation-header {
  margin-bottom: 2rem;
}

.documentation-header h2 {
  margin-top: 0;
  margin-bottom: 0.5rem;
  font-size: 1.8rem;
  color: #2c3e50;
}

.template-description {
  margin-top: 0.5rem;
  font-size: 1rem;
  color: #555;
  line-height: 1.5;
}

.template-metadata {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.template-metadata h3 {
  margin-top: 0;
  margin-bottom: 1rem;
  font-size: 1.4rem;
  color: #2c3e50;
}

.metadata-table {
  width: 100%;
  border-collapse: collapse;
}

.metadata-table th,
.metadata-table td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid #e9ecef;
}

.metadata-table th {
  font-weight: 600;
  width: 30%;
  color: #495057;
}

.documentation-content {
  margin-bottom: 2rem;
}

.documentation-content h3 {
  margin-top: 0;
  margin-bottom: 1.5rem;
  font-size: 1.4rem;
  color: #2c3e50;
  border-bottom: 2px solid #e9ecef;
  padding-bottom: 0.5rem;
}

.section-documentation {
  margin-bottom: 2rem;
}

.section-name {
  margin-top: 0;
  margin-bottom: 1rem;
  font-size: 1.2rem;
  color: #495057;
  background-color: #f1f3f5;
  padding: 0.75rem 1rem;
  border-radius: 4px;
}

.field-documentation {
  margin-bottom: 1.5rem;
  padding: 1rem;
  background-color: #fff;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.field-documentation h4 {
  margin-top: 0;
  margin-bottom: 0.75rem;
  font-size: 1.1rem;
  color: #343a40;
}

.field-properties {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
}

.field-property {
  display: flex;
  flex-direction: column;
}

.field-property.description,
.field-property.options {
  grid-column: 1 / -1;
}

.property-label {
  font-weight: 600;
  font-size: 0.9rem;
  color: #495057;
  margin-bottom: 0.25rem;
}

.property-value {
  font-size: 0.95rem;
}

.property-value.required {
  color: #e74c3c;
  font-weight: 600;
}

.options-list {
  margin: 0.5rem 0 0;
  padding-left: 1.5rem;
}

.options-list li {
  margin-bottom: 0.25rem;
}

.relationships-documentation {
  margin-top: 2rem;
}

.relationship-documentation {
  margin-bottom: 1.5rem;
  padding: 1rem;
  background-color: #fff;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.relationship-documentation h4 {
  margin-top: 0;
  margin-bottom: 0.75rem;
  font-size: 1.1rem;
  color: #343a40;
}

.relationship-properties {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
}

.relationship-property {
  display: flex;
  flex-direction: column;
}

.relationship-property.description {
  grid-column: 1 / -1;
}

.documentation-footer {
  margin-top: 2rem;
  padding-top: 1rem;
  border-top: 1px solid #e9ecef;
  color: #6c757d;
  font-size: 0.9rem;
  text-align: right;
}

.documentation-error {
  padding: 1rem;
  background-color: #fff3f3;
  border: 1px solid #ffcdd2;
  border-radius: 4px;
  color: #d32f2f;
  text-align: center;
}

.no-fields {
  padding: 1rem;
  background-color: #f8f9fa;
  border-radius: 4px;
  color: #6c757d;
  text-align: center;
  font-style: italic;
}

@media print {
  .template-documentation {
    font-size: 12pt;
  }
  
  .documentation-header h2 {
    font-size: 18pt;
  }
  
  .template-metadata,
  .field-documentation,
  .relationship-documentation {
    break-inside: avoid;
    page-break-inside: avoid;
  }
}

/* frontend/src/components/AdminPanel/SystemMonitoringTab.css */
.system-monitoring-tab {
  padding: 1rem;
}

.system-monitoring-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.system-monitoring-header h2 {
  margin: 0;
  font-weight: 500;
}

.system-monitoring-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.refresh-control {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.refresh-control select {
  padding: 0.5rem;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--background-primary);
  color: var(--text-primary);
}

.last-refreshed {
  margin-bottom: 1.5rem;
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.system-status-overview {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.system-status-card {
  background-color: var(--background-secondary);
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
  border-top: 4px solid transparent;
}

.system-status-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.system-status-card.healthy {
  border-top-color: #4caf50;
}

.system-status-card.warning {
  border-top-color: #ff9800;
}

.system-status-card.critical {
  border-top-color: #f44336;
}

.system-status-card.unknown {
  border-top-color: #9e9e9e;
}

.system-status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.system-status-header h3 {
  margin: 0;
  font-weight: 500;
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.status-indicator.healthy {
  background-color: #4caf50;
}

.status-indicator.warning {
  background-color: #ff9800;
}

.status-indicator.critical {
  background-color: #f44336;
}

.status-indicator.unknown {
  background-color: #9e9e9e;
}

.system-status-metrics {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
}

.metric {
  text-align: center;
}

.metric-value {
  font-size: 1.2rem;
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.metric-value.healthy {
  color: #4caf50;
}

.metric-value.warning {
  color: #ff9800;
}

.metric-value.critical {
  color: #f44336;
}

.metric-value.unknown {
  color: #9e9e9e;
}

.metric-label {
  font-size: 0.85rem;
  color: var(--text-secondary);
}

.system-details {
  margin-top: 2rem;
}

.system-detail-card {
  background-color: var(--background-secondary);
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.system-detail-card h3 {
  margin-top: 0;
  margin-bottom: 1.5rem;
  font-weight: 500;
}

.system-metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.system-metrics-grid .metric {
  text-align: left;
  background-color: var(--background-tertiary);
  padding: 1rem;
  border-radius: 8px;
}

.system-metrics-grid .metric-label {
  margin-bottom: 0.5rem;
}

.system-metrics-grid .metric-value {
  font-size: 1.5rem;
}

.system-actions {
  display: flex;
  gap: 1rem;
  margin-top: 1.5rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .system-monitoring-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .system-monitoring-controls {
    width: 100%;
    justify-content: space-between;
  }
  
  .system-status-metrics {
    grid-template-columns: 1fr;
  }
  
  .system-actions {
    flex-direction: column;
  }
}

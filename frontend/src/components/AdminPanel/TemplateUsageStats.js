// frontend/src/components/AdminPanel/TemplateUsageStats.js
import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { selectAuthToken } from '../../redux/slices/authSlice';
import axios from 'axios';
import { BASE_URL } from '../../utils/apiConfig';
import './TemplateUsageStats.css';

/**
 * Component for displaying template usage statistics
 *
 * @param {Object} props - Component props
 * @param {string} props.templateId - ID of the template to show stats for
 * @returns {JSX.Element} Template usage statistics UI
 */
const TemplateUsageStats = ({ templateId }) => {
  const token = useSelector(selectAuthToken);

  // State for usage statistics
  const [usageStats, setUsageStats] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [totalUsage, setTotalUsage] = useState(0);
  const [uniqueUsers, setUniqueUsers] = useState(0);
  const [uniqueBooks, setUniqueBooks] = useState(0);

  // Fetch usage statistics
  useEffect(() => {
    const fetchUsageStats = async () => {
      if (!templateId) return;

      setIsLoading(true);
      setError(null);

      try {
        // First check if the template exists
        try {
          await axios.get(`${BASE_URL}/api/templates/${templateId}`, {
            headers: { Authorization: `Bearer ${token}` }
          });
        } catch (templateError) {
          console.error('Template not found:', templateError);
          setError('Template not found. It may have been deleted or you may not have access to it.');
          setIsLoading(false);
          return;
        }

        // Then fetch usage statistics
        const response = await axios.get(`${BASE_URL}/api/templates/usage?template_id=${templateId}`, {
          headers: { Authorization: `Bearer ${token}` }
        });

        const stats = response.data || [];
        setUsageStats(stats);

        // Calculate summary statistics
        const total = stats.reduce((sum, stat) => sum + stat.usage_count, 0);
        const users = new Set(stats.map(stat => stat.user_id)).size;
        const books = new Set(stats.map(stat => stat.book_id)).size;

        setTotalUsage(total);
        setUniqueUsers(users);
        setUniqueBooks(books);

        setIsLoading(false);
      } catch (error) {
        console.error('Error fetching usage statistics:', error);
        // Handle 404 errors gracefully
        if (error.response && error.response.status === 404) {
          setError('No usage statistics available for this template yet.');
        } else {
          setError('Failed to load usage statistics: ' + (error.response?.data?.detail || error.message));
        }
        setIsLoading(false);
      }
    };

    fetchUsageStats();
  }, [templateId, token]);

  // Render loading state
  if (isLoading) {
    return (
      <div className="template-usage-stats-loading">
        <div className="spinner"></div>
        <p>Loading usage statistics...</p>
      </div>
    );
  }

  // Render error state
  if (error) {
    return (
      <div className="template-usage-stats-error">
        <p>{error}</p>
      </div>
    );
  }

  // Render empty state
  if (usageStats.length === 0) {
    return (
      <div className="template-usage-stats-empty">
        <p>No usage data available for this template.</p>
      </div>
    );
  }

  return (
    <div className="template-usage-stats">
      <h3>Usage Statistics</h3>

      <div className="usage-summary">
        <div className="usage-metric">
          <div className="metric-value">{totalUsage}</div>
          <div className="metric-label">Total Uses</div>
        </div>

        <div className="usage-metric">
          <div className="metric-value">{uniqueUsers}</div>
          <div className="metric-label">Unique Users</div>
        </div>

        <div className="usage-metric">
          <div className="metric-value">{uniqueBooks}</div>
          <div className="metric-label">Unique Books</div>
        </div>
      </div>

      <div className="usage-details">
        <h4>Usage by Book</h4>

        <table className="usage-table">
          <thead>
            <tr>
              <th>Book</th>
              <th>User</th>
              <th>Uses</th>
              <th>Last Used</th>
            </tr>
          </thead>
          <tbody>
            {usageStats.map(stat => (
              <tr key={`${stat.book_id}-${stat.user_id}`}>
                <td>{stat.book_title || stat.book_id}</td>
                <td>{stat.user_id}</td>
                <td>{stat.usage_count}</td>
                <td>{new Date(stat.last_used_at).toLocaleString()}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default TemplateUsageStats;

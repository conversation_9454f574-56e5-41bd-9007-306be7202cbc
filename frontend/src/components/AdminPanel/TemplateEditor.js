// frontend/src/components/AdminPanel/TemplateEditor.js
import React, { useState, useEffect } from 'react';
import TemplateRelationshipEditor from './TemplateRelationshipEditor';
import './TemplateEditor.css';

/**
 * Template Editor component
 * Provides an interface for creating and editing templates
 *
 * @param {Object} props - Component props
 * @param {Object} props.template - Template data to edit
 * @param {Array} props.categories - Available categories
 * @param {Array} props.templates - All available templates for relationships
 * @param {Function} props.onSave - Function to call when saving
 * @param {Function} props.onCancel - Function to call when canceling
 * @returns {JSX.Element} Template editor UI
 */
const TemplateEditor = ({ template, categories, templates, onSave, onCancel }) => {
  // State for form data
  const [formData, setFormData] = useState({
    template_name: '',
    display_name: '',
    category_id: '',
    parent_template_id: null,
    description: '',
    field_definitions: {},
    relationship_definitions: {},
    is_system_template: false
  });

  // State for field sections
  const [fieldSections, setFieldSections] = useState([]);
  const [currentSection, setCurrentSection] = useState('');
  const [newSectionName, setNewSectionName] = useState('');

  // State for new field
  const [newField, setNewField] = useState({
    id: '',
    label: '',
    type: 'text',
    required: false,
    description: '',
    options: []
  });

  // State for new option
  const [newOption, setNewOption] = useState('');

  // Initialize form data from template
  useEffect(() => {
    if (template) {
      setFormData({
        template_id: template.template_id || '',
        template_name: template.template_name || '',
        display_name: template.display_name || '',
        category_id: template.category_id || '',
        parent_template_id: template.parent_template_id || null,
        description: template.description || '',
        field_definitions: template.field_definitions || {},
        relationship_definitions: template.relationship_definitions || {},
        is_system_template: template.is_system_template || false,
        version: template.version || '1.0'
      });

      // Extract field sections
      const sections = Object.keys(template.field_definitions || {});
      setFieldSections(sections);
      if (sections.length > 0) {
        setCurrentSection(sections[0]);
      }
    }
  }, [template]);

  // Handle input change
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle section change
  const handleSectionChange = (e) => {
    setCurrentSection(e.target.value);
  };

  // Handle add section
  const handleAddSection = () => {
    if (!newSectionName.trim()) return;

    // Check if section already exists
    if (fieldSections.includes(newSectionName)) {
      alert('Section already exists');
      return;
    }

    // Add new section
    setFieldSections(prev => [...prev, newSectionName]);
    setFormData(prev => ({
      ...prev,
      field_definitions: {
        ...prev.field_definitions,
        [newSectionName]: []
      }
    }));
    setCurrentSection(newSectionName);
    setNewSectionName('');
  };

  // Handle new field input change
  const handleNewFieldChange = (e) => {
    const { name, value, type, checked } = e.target;
    setNewField(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  // Handle add field
  const handleAddField = () => {
    if (!newField.id.trim() || !newField.label.trim() || !currentSection) return;

    // Check if field ID already exists
    const existingFields = formData.field_definitions[currentSection] || [];
    if (existingFields.some(field => field.id === newField.id)) {
      alert('Field ID already exists in this section');
      return;
    }

    // Add new field to current section
    const updatedFields = {
      ...formData.field_definitions,
      [currentSection]: [
        ...(formData.field_definitions[currentSection] || []),
        {
          ...newField,
          options: newField.type === 'select' ? newField.options : undefined
        }
      ]
    };

    setFormData(prev => ({
      ...prev,
      field_definitions: updatedFields
    }));

    // Reset new field form
    setNewField({
      id: '',
      label: '',
      type: 'text',
      required: false,
      description: '',
      options: []
    });
  };

  // Handle add option
  const handleAddOption = () => {
    if (!newOption.trim()) return;

    setNewField(prev => ({
      ...prev,
      options: [...(prev.options || []), newOption]
    }));

    setNewOption('');
  };

  // Handle remove option
  const handleRemoveOption = (index) => {
    setNewField(prev => ({
      ...prev,
      options: prev.options.filter((_, i) => i !== index)
    }));
  };

  // Handle remove field
  const handleRemoveField = (sectionName, fieldIndex) => {
    const updatedFields = {
      ...formData.field_definitions,
      [sectionName]: formData.field_definitions[sectionName].filter((_, i) => i !== fieldIndex)
    };

    setFormData(prev => ({
      ...prev,
      field_definitions: updatedFields
    }));
  };

  // Handle remove section
  const handleRemoveSection = (sectionName) => {
    if (!window.confirm(`Are you sure you want to remove the "${sectionName}" section and all its fields?`)) {
      return;
    }

    const { [sectionName]: _, ...remainingSections } = formData.field_definitions;

    setFormData(prev => ({
      ...prev,
      field_definitions: remainingSections
    }));

    setFieldSections(prev => prev.filter(s => s !== sectionName));

    if (currentSection === sectionName) {
      const remainingSectionNames = Object.keys(remainingSections);
      setCurrentSection(remainingSectionNames.length > 0 ? remainingSectionNames[0] : '');
    }
  };

  // Handle relationship changes
  const handleRelationshipChange = (relationshipDefinitions) => {
    setFormData(prev => ({
      ...prev,
      relationship_definitions: relationshipDefinitions
    }));
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();

    // Validate form
    if (!formData.template_name.trim()) {
      alert('Template name is required');
      return;
    }

    if (!formData.display_name.trim()) {
      alert('Display name is required');
      return;
    }

    if (!formData.category_id) {
      alert('Category is required');
      return;
    }

    // Prepare data for submission
    const templateData = {
      ...formData,
      // Convert template_name to snake_case if it's a new template
      template_name: formData.template_id ? formData.template_name :
                    formData.display_name.toLowerCase().replace(/\s+/g, '_')
    };

    // Call onSave with the form data
    onSave(templateData);
  };

  return (
    <div className="template-editor-form">
      <form onSubmit={handleSubmit}>
        <div className="form-section">
          <h3>Basic Information</h3>

          <div className="form-group">
            <label htmlFor="display_name">Display Name</label>
            <input
              type="text"
              id="display_name"
              name="display_name"
              value={formData.display_name}
              onChange={handleInputChange}
              required
            />
          </div>

          <div className="form-group">
            <label htmlFor="category_id">Category</label>
            <select
              id="category_id"
              name="category_id"
              value={formData.category_id}
              onChange={handleInputChange}
              required
            >
              <option value="">Select a category</option>
              {categories.map(category => (
                <option key={category.category_id} value={category.category_id}>
                  {category.name}
                </option>
              ))}
            </select>
          </div>

          <div className="form-group">
            <label htmlFor="description">Description</label>
            <textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              rows={4}
            />
          </div>
        </div>

        <div className="form-section">
          <h3>Fields</h3>

          <div className="section-selector">
            <label htmlFor="current_section">Section:</label>
            <select
              id="current_section"
              value={currentSection}
              onChange={handleSectionChange}
            >
              <option value="" disabled>Select a section</option>
              {fieldSections.map(section => (
                <option key={section} value={section}>{section}</option>
              ))}
            </select>

            {currentSection && (
              <button
                type="button"
                className="remove-button"
                onClick={() => handleRemoveSection(currentSection)}
              >
                Remove Section
              </button>
            )}
          </div>

          <div className="add-section">
            <div className="add-section-label">Add New Section:</div>
            <input
              type="text"
              placeholder="New section name"
              value={newSectionName}
              onChange={(e) => setNewSectionName(e.target.value)}
            />
            <button type="button" onClick={handleAddSection}>Add Section</button>
          </div>

          {currentSection && (
            <div className="fields-list">
              <h4>Fields in {currentSection}</h4>

              {formData.field_definitions[currentSection]?.length > 0 ? (
                <ul>
                  {formData.field_definitions[currentSection].map((field, index) => (
                    <li key={field.id} className="field-item">
                      <div className="field-info">
                        <strong>{field.label}</strong> ({field.id}) - {field.type}
                        {field.required && <span className="required-badge">Required</span>}
                        {field.description && <p>{field.description}</p>}
                        {field.options && field.options.length > 0 && (
                          <div className="field-options">
                            <span>Options: </span>
                            {field.options.join(', ')}
                          </div>
                        )}
                      </div>
                      <button
                        type="button"
                        className="remove-button"
                        onClick={() => handleRemoveField(currentSection, index)}
                      >
                        Remove
                      </button>
                    </li>
                  ))}
                </ul>
              ) : (
                <p>No fields in this section yet.</p>
              )}

              <div className="add-field">
                <h4>Add New Field</h4>

                <div className="form-group">
                  <label htmlFor="new_field_id">Field ID</label>
                  <input
                    type="text"
                    id="new_field_id"
                    name="id"
                    value={newField.id}
                    onChange={handleNewFieldChange}
                    placeholder="e.g., first_name"
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="new_field_label">Field Label</label>
                  <input
                    type="text"
                    id="new_field_label"
                    name="label"
                    value={newField.label}
                    onChange={handleNewFieldChange}
                    placeholder="e.g., First Name"
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="new_field_type">Field Type</label>
                  <select
                    id="new_field_type"
                    name="type"
                    value={newField.type}
                    onChange={handleNewFieldChange}
                  >
                    <option value="text">Text</option>
                    <option value="textarea">Text Area</option>
                    <option value="number">Number</option>
                    <option value="select">Select</option>
                    <option value="checkbox">Checkbox</option>
                    <option value="date">Date</option>
                  </select>
                </div>

                <div className="form-group checkbox-group">
                  <label>
                    <input
                      type="checkbox"
                      name="required"
                      checked={newField.required}
                      onChange={handleNewFieldChange}
                    />
                    Required Field
                  </label>
                </div>

                <div className="form-group">
                  <label htmlFor="new_field_description">Description</label>
                  <textarea
                    id="new_field_description"
                    name="description"
                    value={newField.description}
                    onChange={handleNewFieldChange}
                    placeholder="Field description"
                    rows={2}
                  />
                </div>

                {newField.type === 'select' && (
                  <div className="form-group">
                    <label>Options</label>

                    <div className="options-list">
                      {newField.options.map((option, index) => (
                        <div key={index} className="option-item">
                          <span>{option}</span>
                          <button
                            type="button"
                            className="remove-button"
                            onClick={() => handleRemoveOption(index)}
                          >
                            Remove
                          </button>
                        </div>
                      ))}
                    </div>

                    <div className="add-option">
                      <input
                        type="text"
                        value={newOption}
                        onChange={(e) => setNewOption(e.target.value)}
                        placeholder="New option"
                      />
                      <button type="button" onClick={handleAddOption}>Add Option</button>
                    </div>
                  </div>
                )}

                <button type="button" onClick={handleAddField} className="add-field-button">
                  Add Field
                </button>
              </div>
            </div>
          )}
        </div>

        <div className="form-section">
          <h3>Relationships</h3>
          <TemplateRelationshipEditor
            template={template}
            templates={templates || []}
            onChange={handleRelationshipChange}
          />
        </div>

        <div className="form-actions">
          <button type="button" onClick={onCancel} className="cancel-button">
            Cancel
          </button>
          <button type="submit" className="save-button">
            Save Template
          </button>
        </div>
      </form>
    </div>
  );
};

export default TemplateEditor;

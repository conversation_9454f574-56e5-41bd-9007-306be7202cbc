/* frontend/src/components/AdminPanel/TemplateRelationshipEditor.css */

.template-relationship-editor {
  margin-top: 20px;
  padding: 15px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  background-color: #f9f9f9;
}

.template-relationship-editor h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #333;
  font-size: 1.2rem;
}

.template-relationship-editor h4 {
  margin-top: 15px;
  margin-bottom: 10px;
  color: #555;
  font-size: 1rem;
}

.relationships-list {
  margin-bottom: 20px;
}

.relationship-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 10px;
  margin-bottom: 10px;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.relationship-info {
  flex: 1;
}

.relationship-info strong {
  display: block;
  margin-bottom: 5px;
  color: #333;
}

.relationship-info p {
  margin: 5px 0;
  color: #666;
  font-size: 0.9rem;
}

.add-relationship {
  background-color: #fff;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #555;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 0.9rem;
}

.form-group textarea {
  resize: vertical;
  min-height: 60px;
}

.add-relationship-button {
  background-color: #4caf50;
  color: white;
  border: none;
  padding: 10px 18px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 600;
  font-size: 0.95rem;
  transition: background-color 0.3s, transform 0.1s;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  margin-top: 10px;
}

.add-relationship-button:hover {
  background-color: #388e3c;
  transform: translateY(-1px);
}

.remove-button {
  background-color: #f44336;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.85rem;
  font-weight: bold;
  transition: background-color 0.3s, transform 0.1s;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.remove-button:hover {
  background-color: #d32f2f;
  transform: translateY(-1px);
}

.loading {
  padding: 20px;
  text-align: center;
  color: #666;
}

.error {
  padding: 10px;
  background-color: #ffebee;
  color: #c62828;
  border: 1px solid #ef9a9a;
  border-radius: 4px;
  margin-bottom: 15px;
}

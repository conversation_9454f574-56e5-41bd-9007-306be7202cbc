// frontend/src/components/AdminPanel/TemplateVersionHistory.js
import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import axios from 'axios';
import { BASE_URL } from '../../utils/apiConfig';
import { selectAuthToken } from '../../redux/slices/authSlice';
import './TemplateVersionHistory.css';

/**
 * Template Version History component
 * Displays the version history of a template and allows comparing versions
 * 
 * @param {Object} props - Component props
 * @param {string} props.templateId - The ID of the template
 * @param {function} props.onRestoreVersion - Callback for restoring a version
 * @returns {JSX.Element} Template version history UI
 */
const TemplateVersionHistory = ({ templateId, onRestoreVersion }) => {
  const token = useSelector(selectAuthToken);
  
  // State for version history
  const [versions, setVersions] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedVersions, setSelectedVersions] = useState([]);
  const [showComparison, setShowComparison] = useState(false);
  const [comparisonResult, setComparisonResult] = useState(null);
  
  // Fetch version history
  useEffect(() => {
    const fetchVersionHistory = async () => {
      if (!templateId) return;
      
      setIsLoading(true);
      setError(null);
      
      try {
        const response = await axios.get(`${BASE_URL}/api/templates/${templateId}/versions`, {
          headers: { Authorization: `Bearer ${token}` }
        });
        
        setVersions(response.data);
      } catch (error) {
        console.error('Error fetching template versions:', error);
        setError('Failed to fetch template versions: ' + (error.response?.data?.detail || error.message));
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchVersionHistory();
  }, [token, templateId]);
  
  // Handle version selection
  const handleVersionSelect = (versionId) => {
    if (selectedVersions.includes(versionId)) {
      setSelectedVersions(selectedVersions.filter(id => id !== versionId));
    } else {
      if (selectedVersions.length < 2) {
        setSelectedVersions([...selectedVersions, versionId]);
      }
    }
  };
  
  // Handle compare versions
  const handleCompareVersions = async () => {
    if (selectedVersions.length !== 2) return;
    
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await axios.get(`${BASE_URL}/api/templates/compare-versions`, {
        params: {
          version1: selectedVersions[0],
          version2: selectedVersions[1]
        },
        headers: { Authorization: `Bearer ${token}` }
      });
      
      setComparisonResult(response.data);
      setShowComparison(true);
    } catch (error) {
      console.error('Error comparing template versions:', error);
      setError('Failed to compare template versions: ' + (error.response?.data?.detail || error.message));
    } finally {
      setIsLoading(false);
    }
  };
  
  // Handle restore version
  const handleRestoreVersion = (versionId) => {
    if (onRestoreVersion) {
      onRestoreVersion(versionId);
    }
  };
  
  // Format date for display
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleString();
  };
  
  // Render loading state
  if (isLoading && versions.length === 0) {
    return (
      <div className="template-version-history">
        <h3>Version History</h3>
        <div className="loading-indicator">
          <span className="material-icons spinning">refresh</span>
          <p>Loading version history...</p>
        </div>
      </div>
    );
  }
  
  // Render error state
  if (error && versions.length === 0) {
    return (
      <div className="template-version-history">
        <h3>Version History</h3>
        <div className="error-message">
          <span className="material-icons">error</span>
          <p>{error}</p>
        </div>
      </div>
    );
  }
  
  // Render empty state
  if (versions.length === 0) {
    return (
      <div className="template-version-history">
        <h3>Version History</h3>
        <div className="empty-state">
          <p>No version history available for this template.</p>
        </div>
      </div>
    );
  }
  
  // Render comparison result
  const renderComparisonResult = () => {
    if (!comparisonResult) return null;
    
    return (
      <div className="version-comparison">
        <h4>Version Comparison</h4>
        
        <div className="comparison-header">
          <div className="comparison-version">
            <h5>Version {comparisonResult.version1.version}</h5>
            <p>{formatDate(comparisonResult.version1.created_at)}</p>
          </div>
          <div className="comparison-version">
            <h5>Version {comparisonResult.version2.version}</h5>
            <p>{formatDate(comparisonResult.version2.created_at)}</p>
          </div>
        </div>
        
        <div className="comparison-content">
          {comparisonResult.differences.map((diff, index) => (
            <div key={index} className="comparison-item">
              <div className="comparison-item-label">{diff.field}</div>
              <div className="comparison-values">
                <div className={`comparison-value ${diff.type === 'added' ? 'empty' : ''}`}>
                  {diff.type === 'modified' ? diff.oldValue : (diff.type === 'removed' ? diff.value : '')}
                </div>
                <div className={`comparison-value ${diff.type === 'removed' ? 'empty' : ''}`}>
                  {diff.type === 'modified' ? diff.newValue : (diff.type === 'added' ? diff.value : '')}
                </div>
              </div>
              <div className={`comparison-type ${diff.type}`}>
                {diff.type === 'added' ? 'Added' : (diff.type === 'removed' ? 'Removed' : 'Modified')}
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };
  
  return (
    <div className="template-version-history">
      <div className="version-history-header">
        <h3>Version History</h3>
        
        {selectedVersions.length === 2 && (
          <button 
            className="admin-button secondary"
            onClick={handleCompareVersions}
          >
            Compare Selected Versions
          </button>
        )}
      </div>
      
      <div className="version-list">
        {versions.map((version, index) => (
          <div 
            key={index} 
            className={`version-item ${selectedVersions.includes(version.version_id) ? 'selected' : ''}`}
            onClick={() => handleVersionSelect(version.version_id)}
          >
            <div className="version-info">
              <div className="version-number">Version {version.version}</div>
              <div className="version-date">{formatDate(version.created_at)}</div>
            </div>
            
            <div className="version-actions">
              {index > 0 && (
                <button 
                  className="admin-button small"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleRestoreVersion(version.version_id);
                  }}
                  title="Restore this version"
                >
                  <span className="material-icons">restore</span>
                </button>
              )}
            </div>
          </div>
        ))}
      </div>
      
      {showComparison && renderComparisonResult()}
    </div>
  );
};

export default TemplateVersionHistory;

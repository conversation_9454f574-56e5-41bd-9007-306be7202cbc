// frontend/src/components/AdminPanel/TemplateImportExport.js
import React, { useState } from 'react';
import { useSelector } from 'react-redux';
import { selectAuthToken } from '../../redux/slices/authSlice';
import axios from 'axios';
import { BASE_URL } from '../../utils/apiConfig';
import './TemplateImportExport.css';

/**
 * Template Import/Export component
 * Provides a dedicated interface for importing and exporting templates
 * 
 * @param {Object} props - Component props
 * @param {Function} props.onTemplateImported - Callback when a template is imported
 * @param {Array} props.templates - List of available templates
 * @returns {JSX.Element} Template import/export UI
 */
const TemplateImportExport = ({ onTemplateImported, templates = [] }) => {
  const token = useSelector(selectAuthToken);
  
  // State for import/export
  const [importFile, setImportFile] = useState(null);
  const [importError, setImportError] = useState(null);
  const [importSuccess, setImportSuccess] = useState(null);
  const [isImporting, setIsImporting] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [exportError, setExportError] = useState(null);
  const [selectedTemplateId, setSelectedTemplateId] = useState('');
  const [importedTemplateData, setImportedTemplateData] = useState(null);
  const [validationResult, setValidationResult] = useState(null);
  
  // Handle file selection for import
  const handleFileChange = (e) => {
    const file = e.target.files[0];
    setImportSuccess(null);
    setImportError(null);
    setValidationResult(null);
    setImportedTemplateData(null);
    
    if (file) {
      if (file.type !== 'application/json') {
        setImportError('Please select a JSON file');
        setImportFile(null);
      } else {
        setImportFile(file);
        
        // Read the file to preview its contents
        const reader = new FileReader();
        reader.onload = (e) => {
          try {
            const templateData = JSON.parse(e.target.result);
            setImportedTemplateData(templateData);
            
            // Validate the template data
            validateTemplateData(templateData);
          } catch (error) {
            console.error('Error parsing template file:', error);
            setImportError('Invalid JSON format');
            setImportedTemplateData(null);
          }
        };
        reader.readAsText(file);
      }
    }
  };
  
  // Validate template data
  const validateTemplateData = async (templateData) => {
    try {
      const response = await axios.post(`${BASE_URL}/api/templates/validate`, {
        template_data: templateData
      }, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      setValidationResult(response.data);
      
      if (!response.data.is_valid) {
        setImportError('Template validation failed');
      } else {
        setImportError(null);
      }
    } catch (error) {
      console.error('Error validating template:', error);
      setValidationResult({
        is_valid: false,
        errors: ['Failed to validate template: ' + (error.response?.data?.detail || error.message)]
      });
      setImportError('Failed to validate template');
    }
  };
  
  // Handle import template submission
  const handleImportSubmit = async () => {
    if (!importFile) {
      setImportError('Please select a file to import');
      return;
    }
    
    if (validationResult && !validationResult.is_valid) {
      setImportError('Cannot import invalid template. Please fix the errors and try again.');
      return;
    }
    
    setIsImporting(true);
    setImportError(null);
    setImportSuccess(null);
    
    try {
      // Send to the API
      const response = await axios.post(`${BASE_URL}/api/templates/import`, {
        template_data: importedTemplateData
      }, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      setImportSuccess(`Template "${response.data.display_name}" imported successfully`);
      setImportFile(null);
      setImportedTemplateData(null);
      setValidationResult(null);
      
      // Call the callback if provided
      if (onTemplateImported) {
        onTemplateImported(response.data);
      }
    } catch (error) {
      console.error('Error importing template:', error);
      setImportError('Failed to import template: ' + (error.response?.data?.detail || error.message));
    } finally {
      setIsImporting(false);
    }
  };
  
  // Handle export template
  const handleExportTemplate = async () => {
    if (!selectedTemplateId) {
      setExportError('Please select a template to export');
      return;
    }
    
    setIsExporting(true);
    setExportError(null);
    
    try {
      const response = await axios.get(`${BASE_URL}/api/templates/${selectedTemplateId}/export`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      // Create a JSON file for download
      const templateData = response.data;
      const fileName = `${templateData.template_name}_template.json`;
      const jsonData = JSON.stringify(templateData, null, 2);
      const blob = new Blob([jsonData], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      
      // Create a temporary link and trigger download
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      
      // Clean up
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error exporting template:', error);
      setExportError('Failed to export template: ' + (error.response?.data?.detail || error.message));
    } finally {
      setIsExporting(false);
    }
  };
  
  return (
    <div className="template-import-export">
      <h3>Template Import/Export</h3>
      
      <div className="import-export-container">
        <div className="import-section">
          <h4>Import Template</h4>
          <p>Import a template from a JSON file.</p>
          
          <div className="file-input-container">
            <input
              type="file"
              accept=".json"
              onChange={handleFileChange}
              className="file-input"
              id="template-file-input"
            />
            <label htmlFor="template-file-input" className="file-input-label">
              <span className="material-icons">upload_file</span>
              Select Template File
            </label>
            {importFile && (
              <div className="selected-file">
                <span className="material-icons">description</span>
                <span className="file-name">{importFile.name}</span>
              </div>
            )}
          </div>
          
          {importError && (
            <div className="import-error">
              <span className="material-icons">error</span>
              <p>{importError}</p>
            </div>
          )}
          
          {importSuccess && (
            <div className="import-success">
              <span className="material-icons">check_circle</span>
              <p>{importSuccess}</p>
            </div>
          )}
          
          {validationResult && (
            <div className={`validation-result ${validationResult.is_valid ? 'valid' : 'invalid'}`}>
              <h5>Validation Result</h5>
              {validationResult.is_valid ? (
                <p>Template is valid and ready to import.</p>
              ) : (
                <>
                  <p>Template validation failed:</p>
                  <ul>
                    {validationResult.errors.map((error, index) => (
                      <li key={index}>{error}</li>
                    ))}
                  </ul>
                </>
              )}
            </div>
          )}
          
          {importedTemplateData && (
            <div className="template-preview">
              <h5>Template Preview</h5>
              <div className="preview-item">
                <span className="preview-label">Name:</span>
                <span className="preview-value">{importedTemplateData.display_name || importedTemplateData.template_name}</span>
              </div>
              {importedTemplateData.description && (
                <div className="preview-item">
                  <span className="preview-label">Description:</span>
                  <span className="preview-value">{importedTemplateData.description}</span>
                </div>
              )}
              <div className="preview-item">
                <span className="preview-label">Fields:</span>
                <span className="preview-value">
                  {importedTemplateData.field_definitions ? 
                    Object.values(importedTemplateData.field_definitions).flat().length : 0} fields
                </span>
              </div>
            </div>
          )}
          
          <button
            className="admin-button"
            onClick={handleImportSubmit}
            disabled={!importFile || isImporting || (validationResult && !validationResult.is_valid)}
          >
            {isImporting ? (
              <>
                <div className="button-spinner"></div>
                Importing...
              </>
            ) : (
              <>
                <span className="material-icons">file_upload</span>
                Import Template
              </>
            )}
          </button>
        </div>
        
        <div className="export-section">
          <h4>Export Template</h4>
          <p>Export a template to a JSON file for sharing or backup.</p>
          
          <div className="template-select-container">
            <label htmlFor="template-select">Select Template:</label>
            <select
              id="template-select"
              value={selectedTemplateId}
              onChange={(e) => setSelectedTemplateId(e.target.value)}
              className="template-select"
            >
              <option value="">-- Select a template --</option>
              {templates.map(template => (
                <option key={template.template_id} value={template.template_id}>
                  {template.display_name} {template.is_system_template ? '(System)' : ''}
                </option>
              ))}
            </select>
          </div>
          
          {exportError && (
            <div className="export-error">
              <span className="material-icons">error</span>
              <p>{exportError}</p>
            </div>
          )}
          
          <button
            className="admin-button"
            onClick={handleExportTemplate}
            disabled={!selectedTemplateId || isExporting}
          >
            {isExporting ? (
              <>
                <div className="button-spinner"></div>
                Exporting...
              </>
            ) : (
              <>
                <span className="material-icons">file_download</span>
                Export Template
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default TemplateImportExport;

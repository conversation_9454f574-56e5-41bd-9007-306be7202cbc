// frontend/src/components/AdminPanel/AdminPanel.js
import React from 'react';
import AdminPanelTabs from './AdminPanelTabs';
import AdminDashboard from './AdminDashboard';
import EmbeddingTestingTab from './EmbeddingTestingTab';
import TemplateManagementTab from './TemplateManagementTab';
import TemplateValidationTab from './TemplateValidationTab';
import TemplateImportExportTab from './TemplateImportExportTab';
import SystemMonitoringTab from './SystemMonitoringTab';
import DatabaseManagementTab from './DatabaseManagementTab';
import UserManagementTab from './UserManagementTab';
import './AdminPanel.css';

/**
 * Main Admin Panel component
 * Renders the admin panel UI with tabs for different admin functions
 *
 * @param {Object} props - Component props
 * @param {string} props.activeTab - Currently active tab ID
 * @param {Function} props.onTabChange - Function to call when tab is changed
 * @param {boolean} props.isLoading - Whether data is currently loading
 * @param {string|null} props.error - Error message, if any
 * @returns {JSX.Element} Admin panel UI
 */
const AdminPanel = ({ activeTab, onTabChange, isLoading, error }) => {
  // Define available tabs
  const tabs = [
    { id: 'dashboard', label: 'Dashboard', icon: 'dashboard' },
    { id: 'embedding', label: 'Embedding Testing', icon: 'code' },
    { id: 'templates', label: 'Template Management', icon: 'description' },
    { id: 'template-validation', label: 'Template Validation', icon: 'check_circle' },
    { id: 'template-import-export', label: 'Template Import/Export', icon: 'import_export' },
    { id: 'system', label: 'System Monitoring', icon: 'monitoring' },
    { id: 'database', label: 'Database Management', icon: 'storage' },
    { id: 'users', label: 'User Management', icon: 'people' }
  ];

  // Render the active tab content
  const renderTabContent = () => {
    switch (activeTab) {
      case 'dashboard':
        return <AdminDashboard />;
      case 'embedding':
        return <EmbeddingTestingTab />;
      case 'templates':
        return <TemplateManagementTab />;
      case 'template-validation':
        return <TemplateValidationTab />;
      case 'template-import-export':
        return <TemplateImportExportTab />;
      case 'system':
        return <SystemMonitoringTab />;
      case 'database':
        return <DatabaseManagementTab />;
      case 'users':
        return <UserManagementTab />;
      default:
        return <AdminDashboard />;
    }
  };

  return (
    <div className="admin-panel">
      <div className="admin-panel-header">
        <h1>Admin Panel</h1>
      </div>

      <div className="admin-panel-content">
        <div className="admin-panel-sidebar">
          <AdminPanelTabs
            tabs={tabs}
            activeTab={activeTab}
            onTabChange={onTabChange}
          />
        </div>

        <div className="admin-panel-main">
          {isLoading ? (
            <div className="admin-panel-loading">
              <div className="spinner"></div>
              <p>Loading...</p>
            </div>
          ) : error ? (
            <div className="admin-panel-error">
              <p>{error}</p>
            </div>
          ) : (
            renderTabContent()
          )}
        </div>
      </div>
    </div>
  );
};

export default AdminPanel;

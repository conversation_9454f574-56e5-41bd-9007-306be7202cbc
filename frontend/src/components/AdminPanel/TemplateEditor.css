/* frontend/src/components/AdminPanel/TemplateEditor.css */
.template-editor-form {
  max-width: 100%;
  overflow-y: auto;
}

.form-section {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background-color: var(--background-secondary);
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.form-section h3 {
  margin-top: 0;
  margin-bottom: 1.5rem;
  font-weight: 500;
  color: var(--text-primary);
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 0.5rem;
}

.form-group {
  margin-bottom: 1.25rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.form-group input[type="text"],
.form-group input[type="number"],
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--background-primary);
  color: var(--text-primary);
  font-size: 0.95rem;
}

.form-group textarea {
  resize: vertical;
  min-height: 80px;
}

.checkbox-group {
  display: flex;
  align-items: center;
}

.checkbox-group label {
  display: flex;
  align-items: center;
  margin-bottom: 0;
}

.checkbox-group input[type="checkbox"] {
  margin-right: 0.5rem;
}

.section-selector {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
}

.section-selector label {
  margin-right: 0.5rem;
  margin-bottom: 0;
  font-weight: 500;
}

.section-selector select {
  flex: 1;
  padding: 0.5rem;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--background-primary);
  color: var(--text-primary);
  margin-right: 1rem;
}

.add-section {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin-bottom: 1.5rem;
  padding: 10px;
  background-color: #f5f5f5;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.add-section-label {
  font-weight: 600;
  margin-right: 10px;
  color: #333;
  margin-bottom: 5px;
}

.add-section input {
  flex: 1;
  padding: 0.5rem;
  border: 1px solid #ccc;
  border-radius: 4px 0 0 4px;
  background-color: white;
  color: #333;
  font-size: 0.95rem;
}

.add-section button {
  padding: 0.5rem 1rem;
  background-color: #4caf50; /* Bright green for better visibility */
  color: white;
  border: none;
  border-radius: 0 4px 4px 0;
  cursor: pointer;
  font-weight: 600;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: background-color 0.2s, transform 0.1s;
}

.add-section button:hover {
  background-color: #388e3c;
  transform: translateY(-1px);
}

.fields-list {
  margin-top: 1.5rem;
}

.fields-list h4 {
  margin-top: 0;
  margin-bottom: 1rem;
  font-weight: 500;
}

.fields-list ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.field-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 1rem;
  margin-bottom: 0.5rem;
  background-color: var(--background-tertiary);
  border-radius: 4px;
}

.field-info {
  flex: 1;
}

.field-info p {
  margin: 0.5rem 0 0;
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.required-badge {
  display: inline-block;
  margin-left: 0.5rem;
  padding: 0.2rem 0.5rem;
  background-color: var(--accent-color);
  color: white;
  border-radius: 4px;
  font-size: 0.8rem;
}

.field-options {
  margin-top: 0.5rem;
  font-size: 0.9rem;
}

.add-field {
  margin-top: 2rem;
  padding: 1.5rem;
  background-color: var(--background-tertiary);
  border-radius: 8px;
}

.add-field h4 {
  margin-top: 0;
  margin-bottom: 1rem;
  font-weight: 500;
}

.options-list {
  margin-bottom: 1rem;
}

.option-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  margin-bottom: 0.5rem;
  background-color: var(--background-secondary);
  border-radius: 4px;
}

.add-option {
  display: flex;
  margin-bottom: 1rem;
}

.add-option input {
  flex: 1;
  padding: 0.5rem;
  border: 1px solid var(--border-color);
  border-radius: 4px 0 0 4px;
  background-color: var(--background-primary);
  color: var(--text-primary);
}

.add-option button {
  padding: 0.5rem 1rem;
  background-color: #4caf50; /* Bright green for better visibility */
  color: white;
  border: none;
  border-radius: 0 4px 4px 0;
  cursor: pointer;
  font-weight: 600;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: background-color 0.2s, transform 0.1s;
}

.add-option button:hover {
  background-color: #388e3c;
  transform: translateY(-1px);
}

.add-field-button {
  display: block;
  width: 100%;
  padding: 0.75rem;
  background-color: #4caf50; /* Bright green for better visibility */
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 600;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: background-color 0.2s, transform 0.1s;
}

.add-field-button:hover {
  background-color: #388e3c;
  transform: translateY(-1px);
}

.remove-button {
  padding: 0.25rem 0.5rem;
  background-color: #e53935; /* Bright red for better visibility */
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.85rem;
  font-weight: bold;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 2rem;
}

.cancel-button {
  padding: 0.75rem 1.5rem;
  background-color: #607d8b; /* Blue-gray for better visibility */
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: background-color 0.2s, transform 0.1s;
}

.cancel-button:hover {
  background-color: #455a64;
  transform: translateY(-1px);
}

.save-button {
  padding: 0.75rem 1.5rem;
  background-color: #4caf50; /* Bright green for better visibility */
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: background-color 0.2s, transform 0.1s;
}

.save-button:hover {
  background-color: #388e3c;
  transform: translateY(-1px);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .section-selector {
    flex-direction: column;
    align-items: flex-start;
  }

  .section-selector label {
    margin-bottom: 0.5rem;
  }

  .section-selector select {
    width: 100%;
    margin-right: 0;
    margin-bottom: 0.5rem;
  }

  .add-section {
    flex-direction: column;
    padding: 15px;
    background-color: #f5f5f5;
  }

  .add-section-label {
    width: 100%;
    margin-bottom: 10px;
    font-weight: 600;
  }

  .add-section input {
    width: 100%;
    border-radius: 4px;
    margin-bottom: 0.5rem;
  }

  .add-section button {
    width: 100%;
    border-radius: 4px;
    background-color: #4caf50; /* Ensure the color is applied in mobile view too */
    font-weight: 600;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }

  .add-option {
    flex-direction: column;
  }

  .add-option input {
    width: 100%;
    border-radius: 4px;
    margin-bottom: 0.5rem;
  }

  .add-option button {
    width: 100%;
    border-radius: 4px;
    background-color: #4caf50; /* Ensure the color is applied in mobile view too */
    font-weight: 600;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }

  .field-item {
    flex-direction: column;
  }

  .field-info {
    margin-bottom: 1rem;
  }

  .form-actions {
    flex-direction: column-reverse;
  }

  .cancel-button,
  .save-button {
    width: 100%;
  }
}

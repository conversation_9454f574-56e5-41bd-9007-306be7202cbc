// frontend/src/components/AdminPanel/TemplateValidationTab.js
import React, { useState } from 'react';
import { useSelector } from 'react-redux';
import axios from 'axios';
import { BASE_URL } from '../../utils/apiConfig';
import { selectAuthToken } from '../../redux/slices/authSlice';
import TemplateDocumentation from './TemplateDocumentation';
import TemplatePreview from './TemplatePreview';
import './TemplateValidationTab.css';

/**
 * Template Validation Tab component
 * Provides an interface for validating templates and generating sample data
 *
 * @returns {JSX.Element} Template validation tab UI
 */
const TemplateValidationTab = () => {
  const token = useSelector(selectAuthToken);

  // State for template data
  const [templateData, setTemplateData] = useState('');
  const [isValidating, setIsValidating] = useState(false);
  const [validationResult, setValidationResult] = useState(null);
  const [validationError, setValidationError] = useState(null);

  // State for sample data
  const [isGeneratingSample, setIsGeneratingSample] = useState(false);
  const [sampleData, setSampleData] = useState(null);
  const [sampleError, setSampleError] = useState(null);

  // State for documentation
  const [showDocumentation, setShowDocumentation] = useState(false);
  const [documentationTemplate, setDocumentationTemplate] = useState(null);

  // State for preview
  const [showPreview, setShowPreview] = useState(false);
  const [previewTemplate, setPreviewTemplate] = useState(null);

  // Handle template data change
  const handleTemplateDataChange = (e) => {
    setTemplateData(e.target.value);
    // Reset validation and sample data when template data changes
    setValidationResult(null);
    setValidationError(null);
    setSampleData(null);
    setSampleError(null);
    setShowDocumentation(false);
    setDocumentationTemplate(null);
    setShowPreview(false);
    setPreviewTemplate(null);
  };

  // Handle file upload
  const handleFileUpload = (e) => {
    const file = e.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (event) => {
      try {
        const jsonData = JSON.parse(event.target.result);
        setTemplateData(JSON.stringify(jsonData, null, 2));
        // Reset validation and sample data when template data changes
        setValidationResult(null);
        setValidationError(null);
        setSampleData(null);
        setSampleError(null);
        setShowDocumentation(false);
        setDocumentationTemplate(null);
        setShowPreview(false);
        setPreviewTemplate(null);
      } catch (error) {
        setValidationError('Invalid JSON file. Please upload a valid JSON file.');
      }
    };
    reader.readAsText(file);
  };

  // Handle validate template
  const handleValidateTemplate = async () => {
    setIsValidating(true);
    setValidationResult(null);
    setValidationError(null);

    try {
      // Parse template data
      const parsedData = JSON.parse(templateData);

      // Send validation request
      const response = await axios.post(`${BASE_URL}/api/templates/validate`, {
        template_data: parsedData
      }, {
        headers: { Authorization: `Bearer ${token}` }
      });

      setValidationResult(response.data);
    } catch (error) {
      console.error('Error validating template:', error);
      if (error.name === 'SyntaxError') {
        setValidationError('Invalid JSON format. Please check your template data.');
      } else {
        setValidationError('Failed to validate template: ' + (error.response?.data?.detail || error.message));
      }
    } finally {
      setIsValidating(false);
    }
  };

  // Handle generate sample data
  const handleGenerateSample = async () => {
    setIsGeneratingSample(true);
    setSampleData(null);
    setSampleError(null);

    try {
      // Parse template data
      const parsedData = JSON.parse(templateData);

      // Send sample data generation request
      const response = await axios.post(`${BASE_URL}/api/templates/generate-sample`, {
        template_data: parsedData
      }, {
        headers: { Authorization: `Bearer ${token}` }
      });

      setSampleData(response.data.sample_data);
    } catch (error) {
      console.error('Error generating sample data:', error);
      if (error.name === 'SyntaxError') {
        setSampleError('Invalid JSON format. Please check your template data.');
      } else {
        setSampleError('Failed to generate sample data: ' + (error.response?.data?.detail || error.message));
      }
    } finally {
      setIsGeneratingSample(false);
    }
  };

  // Handle generate documentation
  const handleGenerateDocumentation = () => {
    try {
      // Parse template data
      const parsedData = JSON.parse(templateData);

      // Set documentation template
      setDocumentationTemplate(parsedData);
      setShowDocumentation(true);
    } catch (error) {
      console.error('Error generating documentation:', error);
      if (error.name === 'SyntaxError') {
        setValidationError('Invalid JSON format. Please check your template data.');
      } else {
        setValidationError('Failed to generate documentation: ' + error.message);
      }
    }
  };

  // Handle preview template
  const handlePreviewTemplate = () => {
    try {
      // Parse template data
      const parsedData = JSON.parse(templateData);

      // Check if we have sample data
      if (!sampleData) {
        setValidationError('Please generate sample data first before previewing the template.');
        return;
      }

      // Set preview template
      setPreviewTemplate(parsedData);
      setShowPreview(true);
    } catch (error) {
      console.error('Error previewing template:', error);
      if (error.name === 'SyntaxError') {
        setValidationError('Invalid JSON format. Please check your template data.');
      } else {
        setValidationError('Failed to preview template: ' + error.message);
      }
    }
  };

  // Render validation result
  const renderValidationResult = () => {
    if (!validationResult) return null;

    return (
      <div className={`validation-result ${validationResult.is_valid ? 'valid' : 'invalid'}`}>
        <h4>Validation Result</h4>
        {validationResult.is_valid ? (
          <div className="valid-message">
            <span className="material-icons">check_circle</span>
            <p>Template is valid!</p>
          </div>
        ) : (
          <div className="invalid-message">
            <span className="material-icons">error</span>
            <p>Template is invalid. Please fix the following errors:</p>
            <ul>
              {validationResult.errors.map((error, index) => (
                <li key={index}>{error}</li>
              ))}
            </ul>
          </div>
        )}
      </div>
    );
  };

  // Render sample data
  const renderSampleData = () => {
    if (!sampleData) return null;

    return (
      <div className="sample-data">
        <h4>Sample Data</h4>
        <pre>{JSON.stringify(sampleData, null, 2)}</pre>
      </div>
    );
  };

  return (
    <div className="template-validation-tab">
      <h2>Template Validation & Testing</h2>

      <div className="validation-controls">
        <div className="file-upload-container">
          <label htmlFor="template-file">Upload Template JSON File</label>
          <input
            type="file"
            id="template-file"
            accept=".json"
            onChange={handleFileUpload}
            className="file-input"
          />
        </div>

        <div className="button-container">
          <button
            className="admin-button"
            onClick={handleValidateTemplate}
            disabled={!templateData || isValidating}
          >
            {isValidating ? 'Validating...' : 'Validate Template'}
          </button>

          <button
            className="admin-button secondary"
            onClick={handleGenerateSample}
            disabled={!templateData || isGeneratingSample || (validationResult && !validationResult.is_valid)}
          >
            {isGeneratingSample ? 'Generating...' : 'Generate Sample Data'}
          </button>

          <button
            className="admin-button info"
            onClick={handleGenerateDocumentation}
            disabled={!templateData || (validationResult && !validationResult.is_valid)}
          >
            Generate Documentation
          </button>

          <button
            className="admin-button preview"
            onClick={handlePreviewTemplate}
            disabled={!templateData || !sampleData || (validationResult && !validationResult.is_valid)}
          >
            Preview Template
          </button>
        </div>
      </div>

      <div className="template-validation-content">
        <div className="template-editor-container">
          <h3>Template JSON</h3>
          <textarea
            value={templateData}
            onChange={handleTemplateDataChange}
            placeholder="Paste your template JSON here or upload a file..."
            rows={20}
            className="template-json-editor"
          />
          {validationError && (
            <div className="error-message">
              <span className="material-icons">error</span>
              <p>{validationError}</p>
            </div>
          )}
        </div>

        <div className="validation-results-container">
          {renderValidationResult()}

          {sampleError && (
            <div className="error-message">
              <span className="material-icons">error</span>
              <p>{sampleError}</p>
            </div>
          )}

          {renderSampleData()}
        </div>
      </div>

      {/* Documentation Modal */}
      {showDocumentation && documentationTemplate && (
        <div className="modal-overlay">
          <div className="modal-content documentation-modal">
            <div className="modal-header">
              <h3>Template Documentation</h3>
              <button
                className="modal-close-button"
                onClick={() => setShowDocumentation(false)}
              >
                <span className="material-icons">close</span>
              </button>
            </div>
            <div className="modal-body">
              <TemplateDocumentation template={documentationTemplate} />
            </div>
            <div className="modal-footer">
              <button
                className="admin-button"
                onClick={() => setShowDocumentation(false)}
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Preview Modal */}
      {showPreview && previewTemplate && sampleData && (
        <div className="modal-overlay">
          <div className="modal-content preview-modal">
            <div className="modal-header">
              <h3>Template Preview</h3>
              <button
                className="modal-close-button"
                onClick={() => setShowPreview(false)}
              >
                <span className="material-icons">close</span>
              </button>
            </div>
            <div className="modal-body">
              <TemplatePreview template={previewTemplate} sampleData={sampleData} />
            </div>
            <div className="modal-footer">
              <button
                className="admin-button"
                onClick={() => setShowPreview(false)}
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TemplateValidationTab;

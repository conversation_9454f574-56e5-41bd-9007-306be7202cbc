// frontend/src/components/AdminPanel/AdminPanelTabs.js
import React from 'react';
import './AdminPanelTabs.css';

/**
 * Component for rendering admin panel navigation tabs
 * 
 * @param {Object} props - Component props
 * @param {Array} props.tabs - Array of tab objects with id, label, and icon properties
 * @param {string} props.activeTab - ID of the currently active tab
 * @param {Function} props.onTabChange - Function to call when a tab is clicked
 * @returns {JSX.Element} Admin panel tabs UI
 */
const AdminPanelTabs = ({ tabs, activeTab, onTabChange }) => {
  return (
    <div className="admin-panel-tabs">
      <ul>
        {tabs.map((tab) => (
          <li 
            key={tab.id}
            className={activeTab === tab.id ? 'active' : ''}
            onClick={() => onTabChange(tab.id)}
          >
            <div className="tab-icon">
              <span className="material-icons">{tab.icon}</span>
            </div>
            <span className="tab-label">{tab.label}</span>
          </li>
        ))}
      </ul>
    </div>
  );
};

export default AdminPanelTabs;

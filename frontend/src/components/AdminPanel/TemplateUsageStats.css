/* frontend/src/components/AdminPanel/TemplateUsageStats.css */

.template-usage-stats {
  margin-top: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 5px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.template-usage-stats h3 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 18px;
  color: #333;
}

.template-usage-stats h4 {
  margin-top: 20px;
  margin-bottom: 10px;
  font-size: 16px;
  color: #555;
}

.usage-summary {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.usage-metric {
  flex: 1;
  text-align: center;
  padding: 15px;
  background-color: #fff;
  border-radius: 5px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  margin: 0 5px;
}

.usage-metric:first-child {
  margin-left: 0;
}

.usage-metric:last-child {
  margin-right: 0;
}

.metric-value {
  font-size: 24px;
  font-weight: bold;
  color: #2c7be5;
  margin-bottom: 5px;
}

.metric-label {
  font-size: 14px;
  color: #6c757d;
}

.usage-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 10px;
}

.usage-table th,
.usage-table td {
  padding: 10px;
  text-align: left;
  border-bottom: 1px solid #dee2e6;
}

.usage-table th {
  background-color: #f1f3f5;
  font-weight: 600;
  color: #495057;
}

.usage-table tr:hover {
  background-color: #f8f9fa;
}

.template-usage-stats-loading,
.template-usage-stats-error,
.template-usage-stats-empty {
  padding: 20px;
  text-align: center;
  color: #6c757d;
}

.template-usage-stats-loading .spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid rgba(0, 0, 0, 0.1);
  border-top-color: #2c7be5;
  border-radius: 50%;
  animation: spin 1s infinite linear;
  margin-right: 10px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.template-usage-stats-error {
  color: #dc3545;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .usage-summary {
    flex-direction: column;
  }
  
  .usage-metric {
    margin: 5px 0;
  }
  
  .usage-table th,
  .usage-table td {
    padding: 8px;
    font-size: 14px;
  }
}

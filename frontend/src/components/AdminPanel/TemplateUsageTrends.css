/* frontend/src/components/AdminPanel/TemplateUsageTrends.css */
.template-usage-trends {
  background-color: var(--background-secondary);
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.trends-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.trends-header h3 {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 500;
  color: var(--text-primary);
}

.trends-controls {
  display: flex;
  gap: 1rem;
}

.control-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.control-group label {
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.control-group select {
  padding: 0.25rem 0.5rem;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--background-primary);
  color: var(--text-primary);
  font-size: 0.9rem;
}

.loading-indicator,
.error-message,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 0;
  color: var(--text-secondary);
}

.loading-indicator .material-icons,
.error-message .material-icons,
.empty-state .material-icons {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.loading-indicator p,
.error-message p,
.empty-state p {
  margin: 0;
  text-align: center;
}

.error-message .material-icons {
  color: #f44336;
}

.empty-state .material-icons {
  color: var(--text-secondary);
}

.spinning {
  animation: spin 1.5s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.trends-chart {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.chart-container {
  display: flex;
  height: 300px;
}

.y-axis {
  display: flex;
  flex-direction: column;
  width: 60px;
  padding-right: 10px;
}

.axis-label {
  font-size: 0.8rem;
  color: var(--text-secondary);
  text-align: center;
  margin-bottom: 0.5rem;
  writing-mode: vertical-lr;
  transform: rotate(180deg);
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.axis-ticks {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
  text-align: right;
}

.axis-tick {
  font-size: 0.8rem;
  color: var(--text-secondary);
}

.chart-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  border-left: 1px solid var(--border-color);
  border-bottom: 1px solid var(--border-color);
}

.bars-container {
  flex: 1;
  display: flex;
  align-items: flex-end;
  justify-content: space-around;
  padding: 0 10px;
}

.bar-group {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  max-width: 60px;
}

.bar {
  width: 30px;
  border-radius: 4px 4px 0 0;
  transition: height 0.3s ease;
}

.usage-bar {
  background-color: #4caf50;
}

.bar-label {
  font-size: 0.8rem;
  color: var(--text-secondary);
  margin-top: 0.5rem;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  transform: rotate(-45deg);
  transform-origin: left top;
  margin-left: 15px;
  height: 40px;
}

.x-axis {
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.trends-metrics {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
}

.metric-card {
  flex: 1;
  background-color: var(--background-primary);
  border-radius: 8px;
  padding: 1rem;
  text-align: center;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.metric-card h4 {
  margin: 0 0 0.5rem;
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--text-secondary);
}

.metric-value {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .trends-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .trends-controls {
    width: 100%;
    justify-content: space-between;
  }
  
  .trends-metrics {
    flex-direction: column;
  }
  
  .bar-group {
    max-width: 40px;
  }
  
  .bar {
    width: 20px;
  }
  
  .bar-label {
    font-size: 0.7rem;
  }
}

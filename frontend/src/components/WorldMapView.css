/* frontend/src/components/WorldMapView.css */
.world-map-view {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: var(--background-primary);
  overflow: hidden;
}

.map-controls {
  padding: 15px 20px;
  background: var(--background-primary);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 10;
}

.map-selector {
  display: flex;
  align-items: center;
  gap: 15px;
}

.map-selector h3 {
  margin: 0;
  font-size: 1.2rem;
  color: var(--text-primary);
}

.map-select {
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background: var(--background-secondary);
  color: var(--text-primary);
  min-width: 200px;
}

.create-map-button {
  padding: 8px 16px;
  background: var(--primary);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: background 0.2s;
}

.create-map-button:hover {
  background: var(--button-primary-hover);
}

.element-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.add-element-button {
  padding: 8px 16px;
  background: var(--secondary);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: background 0.2s;
}

.add-element-button:hover {
  background: var(--button-secondary-hover);
}

.add-element-form {
  display: flex;
  align-items: center;
  gap: 10px;
  background: var(--background-secondary);
  padding: 10px;
  border-radius: 4px;
  position: relative;
}

.element-name-input {
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background: var(--background-primary);
  color: var(--text-primary);
  width: 150px;
}

.element-type-select,
.element-category-select {
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background: var(--background-primary);
  color: var(--text-primary);
}

.adding-instructions {
  position: absolute;
  bottom: -30px;
  left: 0;
  background: var(--background-secondary);
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 0.8rem;
  color: var(--text-secondary);
  white-space: nowrap;
}

.map-container {
  flex: 1;
  position: relative;
  overflow: hidden;
  background: var(--background-secondary);
}

.map-canvas {
  width: 100%;
  height: 100%;
  cursor: crosshair;
}

.no-map-selected {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--text-secondary);
  font-size: 1.2rem;
}

/* Responsive styles */
@media (max-width: 768px) {
  .map-controls {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }

  .map-selector {
    flex-direction: column;
    align-items: flex-start;
    width: 100%;
  }

  .map-select {
    width: 100%;
  }

  .element-controls {
    width: 100%;
  }

  .add-element-form {
    flex-direction: column;
    width: 100%;
  }

  .element-name-input,
  .element-type-select,
  .element-category-select {
    width: 100%;
  }

  .adding-instructions {
    position: static;
    margin-top: 10px;
  }
}

// frontend/src/components/CharacterRelationshipGraph.js
import React, { useEffect, useRef } from 'react';
import './CharacterRelationshipGraph.css';
import { BASE_URL } from '../utils/apiConfig';

/**
 * CharacterRelationshipGraph component for visualizing character relationships
 * @param {Object} props - Component props
 * @returns {JSX.Element} CharacterRelationshipGraph UI
 */
const CharacterRelationshipGraph = ({ characters, onSelectCharacter }) => {
  const canvasRef = useRef(null);

  // Process characters and their relationships
  const processRelationships = () => {
    const nodes = characters.map(char => {
      // Log headshot URL for debugging
      if (char.headshot) {
        console.log(`Character ${char.name} headshot URL:`, char.headshot);
      }

      return {
        id: char.id,
        name: char.name,
        headshot: char.headshot,
        x: 0,
        y: 0,
        radius: 30
      };
    });

    const links = [];

    // Create links from relationships
    characters.forEach(char => {
      // Check if relationships exists and is an array
      if (char.relationships && Array.isArray(char.relationships) && char.relationships.length > 0) {
        char.relationships.forEach(rel => {
          // Handle different relationship formats
          const relName = rel.name || rel.relatedCharacterName;
          const relType = rel.type || rel.relationshipType || 'other';
          const strength = rel.strength !== undefined ? rel.strength : 3; // Default to 3 if not specified

          // Find the related character
          const relatedChar = relName ? characters.find(c => c.name === relName) : null;

          if (relatedChar) {
            links.push({
              source: char.id,
              target: relatedChar.id,
              type: relType,
              strength: strength
            });
          }
        });
      }
    });

    return { nodes, links };
  };

  // Draw the relationship graph
  useEffect(() => {
    if (!canvasRef.current || characters.length === 0) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    const { nodes, links } = processRelationships();

    // Set canvas dimensions
    canvas.width = canvas.offsetWidth;
    canvas.height = canvas.offsetHeight;

    // Simple force-directed layout
    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;
    const radius = Math.min(centerX, centerY) * 0.8;

    // Position nodes in a circle
    nodes.forEach((node, i) => {
      const angle = (i / nodes.length) * 2 * Math.PI;
      node.x = centerX + radius * Math.cos(angle);
      node.y = centerY + radius * Math.sin(angle);
    });

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Draw links
    links.forEach(link => {
      const source = nodes.find(n => n.id === link.source);
      const target = nodes.find(n => n.id === link.target);

      if (source && target) {
        ctx.beginPath();
        ctx.moveTo(source.x, source.y);
        ctx.lineTo(target.x, target.y);

        // Set line style based on relationship type
        switch (link.type.toLowerCase()) {
          case 'friend':
            ctx.strokeStyle = '#4CAF50'; // Green
            break;
          case 'enemy':
            ctx.strokeStyle = '#F44336'; // Red
            break;
          case 'family':
            ctx.strokeStyle = '#2196F3'; // Blue
            break;
          case 'lover':
            ctx.strokeStyle = '#E91E63'; // Pink
            break;
          case 'mentor':
            ctx.strokeStyle = '#9C27B0'; // Purple
            break;
          case 'student':
            ctx.strokeStyle = '#673AB7'; // Deep Purple
            break;
          default:
            ctx.strokeStyle = '#9E9E9E'; // Grey
        }

        // Set line width based on relationship strength
        const strength = link.strength !== undefined ? link.strength : 3;
        ctx.lineWidth = 1 + (strength * 0.5); // Scale from 1 to 3.5 based on strength

        // Set line dash based on relationship strength
        if (strength === 0) {
          // Enemy - dotted line
          ctx.setLineDash([2, 2]);
        } else if (strength === 1) {
          // Acquaintance - dashed line
          ctx.setLineDash([5, 3]);
        } else {
          // Friends and closer - solid line
          ctx.setLineDash([]);
        }

        ctx.stroke();

        // Reset line dash for future drawing
        ctx.setLineDash([]);

        // Draw relationship type label
        const midX = (source.x + target.x) / 2;
        const midY = (source.y + target.y) / 2;

        // Get strength label
        const strength = link.strength !== undefined ? link.strength : 3;
        let strengthLabel = "";
        switch (strength) {
          case 0: strengthLabel = "Enemy"; break;
          case 1: strengthLabel = "Acquaintance"; break;
          case 2: strengthLabel = "Casual"; break;
          case 3: strengthLabel = "Close"; break;
          case 4: strengthLabel = "Deep"; break;
          case 5: strengthLabel = "Intimate"; break;
          default: strengthLabel = ""; break;
        }

        // Create label with type and strength
        const labelText = strengthLabel ? `${link.type} (${strengthLabel})` : link.type;

        ctx.fillStyle = 'white';
        ctx.beginPath();
        ctx.ellipse(midX, midY, ctx.measureText(labelText).width / 2 + 10, 10, 0, 0, 2 * Math.PI);
        ctx.fill();

        ctx.fillStyle = '#333';
        ctx.font = '12px Arial';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText(labelText, midX, midY);
      }
    });

    // Draw nodes
    nodes.forEach(node => {
      // Draw circle
      ctx.beginPath();
      ctx.arc(node.x, node.y, node.radius, 0, 2 * Math.PI);
      ctx.fillStyle = '#4a90e2';
      ctx.fill();

      // Draw headshot if available and not in 'generating' state
      if (node.headshot && node.headshot !== 'generating') {
        const img = new Image();
        // Process the headshot URL correctly
        img.src = node.headshot.startsWith('http')
          ? node.headshot
          : `${BASE_URL}${node.headshot}`;

        // Handle image loading
        img.onload = () => {
          ctx.save();
          ctx.beginPath();
          ctx.arc(node.x, node.y, node.radius - 2, 0, 2 * Math.PI);
          ctx.clip();
          ctx.drawImage(img, node.x - node.radius + 2, node.y - node.radius + 2, (node.radius - 2) * 2, (node.radius - 2) * 2);
          ctx.restore();
        };

        // Handle image loading errors
        img.onerror = () => {
          console.error('Error loading headshot in relationship graph:', node.headshot);
          // Draw first letter of name as fallback
          ctx.fillStyle = 'white';
          ctx.font = 'bold 20px Arial';
          ctx.textAlign = 'center';
          ctx.textBaseline = 'middle';
          ctx.fillText(node.name.charAt(0), node.x, node.y);
        };
      } else {
        // Draw first letter of name or loading indicator
        ctx.fillStyle = 'white';
        ctx.font = 'bold 20px Arial';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';

        if (node.headshot === 'generating') {
          // Draw a loading indicator
          ctx.fillText('⟳', node.x, node.y);
        } else {
          // Draw first letter of name
          ctx.fillText(node.name.charAt(0), node.x, node.y);
        }
      }

      // Draw name label
      ctx.fillStyle = 'white';
      ctx.beginPath();
      ctx.roundRect(node.x - ctx.measureText(node.name).width / 2 - 5, node.y + node.radius + 5, ctx.measureText(node.name).width + 10, 20, 5);
      ctx.fill();

      ctx.fillStyle = '#333';
      ctx.font = '12px Arial';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillText(node.name, node.x, node.y + node.radius + 15);
    });

    // Handle click events
    const handleClick = (e) => {
      const rect = canvas.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;

      // Check if a node was clicked
      for (const node of nodes) {
        const dx = x - node.x;
        const dy = y - node.y;
        const distance = Math.sqrt(dx * dx + dy * dy);

        if (distance <= node.radius) {
          const character = characters.find(c => c.id === node.id);
          if (character && onSelectCharacter) {
            onSelectCharacter(character);
          }
          break;
        }
      }
    };

    canvas.addEventListener('click', handleClick);

    return () => {
      canvas.removeEventListener('click', handleClick);
    };
  }, [characters, onSelectCharacter]);

  if (characters.length === 0) {
    return (
      <div className="relationship-graph-empty">
        <p>No characters available to display relationships.</p>
      </div>
    );
  }

  return (
    <div className="character-relationship-graph">
      <h3>Character Relationship Network</h3>
      <div className="graph-container">
        <canvas ref={canvasRef} className="relationship-canvas" />
      </div>
      <div className="relationship-legend">
        <div className="legend-item">
          <span className="legend-color" style={{ backgroundColor: '#4CAF50' }}></span>
          <span>Friend</span>
        </div>
        <div className="legend-item">
          <span className="legend-color" style={{ backgroundColor: '#F44336' }}></span>
          <span>Enemy</span>
        </div>
        <div className="legend-item">
          <span className="legend-color" style={{ backgroundColor: '#2196F3' }}></span>
          <span>Family</span>
        </div>
        <div className="legend-item">
          <span className="legend-color" style={{ backgroundColor: '#E91E63' }}></span>
          <span>Lover</span>
        </div>
        <div className="legend-item">
          <span className="legend-color" style={{ backgroundColor: '#9C27B0' }}></span>
          <span>Mentor</span>
        </div>
        <div className="legend-item">
          <span className="legend-color" style={{ backgroundColor: '#673AB7' }}></span>
          <span>Student</span>
        </div>
        <div className="legend-item">
          <span className="legend-color" style={{ backgroundColor: '#9E9E9E' }}></span>
          <span>Other</span>
        </div>
      </div>
      <p className="graph-help">Click on a character to edit their details.</p>
    </div>
  );
};

export default CharacterRelationshipGraph;

// frontend/src/components/MiniRelationshipMap.js
import React, { useEffect, useRef, useState } from 'react';
import './MiniRelationshipMap.css';
import { getRelationshipTypeLabel, getRelationshipTypes } from '../utils/relationshipTypes';
import { getElementTypeLabel } from '../utils/templateUtils';

/**
 * Component for displaying a mini relationship map for an element
 * @param {Object} props - Component props
 * @returns {JSX.Element} MiniRelationshipMap UI
 */
const MiniRelationshipMap = ({
  element,
  relationships = [],
  relatedElements = [],
  onElementSelect,
  onCreateRelationship,
  maxRelationships = 8
}) => {
  const svgRef = useRef(null);
  const [filterCategory, setFilterCategory] = useState('all');
  const [filterType, setFilterType] = useState('all');
  const [showFilters, setShowFilters] = useState(false);

  // Get the category for a relationship type
  const getRelationshipTypeCategory = (typeId) => {
    const type = getRelationshipTypes().find(t => t.id === typeId);
    return type?.category || 'other';
  };

  // Get unique relationship categories from the current relationships
  const getRelationshipCategories = () => {
    const categories = new Set();
    relationships.forEach(rel => {
      const category = getRelationshipTypeCategory(rel.relationship_type);
      if (category) categories.add(category);
    });
    return ['all', ...Array.from(categories)];
  };

  // Get unique relationship types from the current relationships
  const getRelationshipTypesFromRelationships = () => {
    const types = new Set();
    relationships.forEach(rel => {
      types.add(rel.relationship_type);
    });
    return ['all', ...Array.from(types)];
  };

  // Process relationships and related elements
  const processRelationships = () => {
    if (!element || !relationships || relationships.length === 0) {
      return { nodes: [], links: [] };
    }

    // Create a map of element IDs to elements
    const elementsMap = {};
    relatedElements.forEach(el => {
      elementsMap[el.element_id] = el;
    });

    // Add the current element
    elementsMap[element.element_id] = element;

    // Create nodes and links arrays
    const nodes = [];
    const links = [];

    // Add the current element as the center node
    nodes.push({
      id: element.element_id,
      name: element.name,
      type: element.element_type,
      isCurrent: true
    });

    // Filter relationships based on selected filters
    let filteredRelationships = [...relationships];

    if (filterCategory !== 'all') {
      // Get relationship types in the selected category
      const typesInCategory = getRelationshipTypes().filter(type =>
        type.category === filterCategory
      ).map(type => type.id);

      filteredRelationships = filteredRelationships.filter(rel =>
        typesInCategory.includes(rel.relationship_type)
      );
    }

    if (filterType !== 'all') {
      filteredRelationships = filteredRelationships.filter(rel =>
        rel.relationship_type === filterType
      );
    }

    // Sort relationships by importance
    const sortedRelationships = [...filteredRelationships].sort((a, b) => {
      // Prioritize relationships with elements that have higher importance
      const aElement = a.direction === 'outgoing'
        ? elementsMap[a.target_element_id]
        : elementsMap[a.source_element_id];

      const bElement = b.direction === 'outgoing'
        ? elementsMap[b.target_element_id]
        : elementsMap[b.source_element_id];

      const aImportance = aElement?.importance || 'medium';
      const bImportance = bElement?.importance || 'medium';

      const importanceValues = { high: 3, medium: 2, low: 1 };

      return importanceValues[bImportance] - importanceValues[aImportance];
    });

    // Limit to max relationships
    const limitedRelationships = sortedRelationships.slice(0, maxRelationships);

    // Process each relationship
    limitedRelationships.forEach(relationship => {
      const isOutgoing = relationship.direction === 'outgoing';
      const relatedElementId = isOutgoing
        ? relationship.target_element_id
        : relationship.source_element_id;

      const relatedElement = elementsMap[relatedElementId];

      if (relatedElement) {
        // Add the related element as a node if not already added
        if (!nodes.some(node => node.id === relatedElementId)) {
          nodes.push({
            id: relatedElementId,
            name: relatedElement.name,
            type: relatedElement.element_type,
            isCurrent: false
          });
        }

        // Add the link
        links.push({
          source: isOutgoing ? element.element_id : relatedElementId,
          target: isOutgoing ? relatedElementId : element.element_id,
          type: relationship.relationship_type,
          id: relationship.relationship_id,
          category: getRelationshipTypeCategory(relationship.relationship_type)
        });
      }
    });

    return { nodes, links };
  };

  // Draw the relationship map
  useEffect(() => {
    if (!svgRef.current) return;

    const { nodes, links } = processRelationships();

    if (nodes.length <= 1) return;

    const svg = svgRef.current;
    const width = svg.clientWidth;
    const height = svg.clientHeight;
    const centerX = width / 2;
    const centerY = height / 2;

    // Clear previous content
    while (svg.firstChild) {
      svg.removeChild(svg.firstChild);
    }

    // Create a group for all elements
    const g = document.createElementNS('http://www.w3.org/2000/svg', 'g');
    svg.appendChild(g);

    // Draw links first (so they're behind nodes)
    links.forEach(link => {
      const sourceNode = nodes.find(n => n.id === link.source);
      const targetNode = nodes.find(n => n.id === link.target);

      if (!sourceNode || !targetNode) return;

      // Calculate positions
      const sourceX = sourceNode.isCurrent ? centerX : centerX + (Math.random() - 0.5) * 150;
      const sourceY = sourceNode.isCurrent ? centerY : centerY + (Math.random() - 0.5) * 150;

      const targetX = targetNode.isCurrent ? centerX : centerX + (Math.random() - 0.5) * 150;
      const targetY = targetNode.isCurrent ? centerY : centerY + (Math.random() - 0.5) * 150;

      // Create link path
      const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
      path.setAttribute('d', `M ${sourceX} ${sourceY} L ${targetX} ${targetY}`);
      path.setAttribute('class', 'relationship-link');
      path.setAttribute('data-type', link.type);
      path.setAttribute('data-category', link.category || 'other');

      // Add tooltip with relationship details
      const title = document.createElementNS('http://www.w3.org/2000/svg', 'title');
      title.textContent = `${sourceNode.name} ${getRelationshipTypeLabel(link.type)} ${targetNode.name}`;
      path.appendChild(title);

      g.appendChild(path);

      // Create link label
      const labelX = (sourceX + targetX) / 2;
      const labelY = (sourceY + targetY) / 2;

      const labelBg = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
      labelBg.setAttribute('x', labelX - 30);
      labelBg.setAttribute('y', labelY - 8);
      labelBg.setAttribute('width', 60);
      labelBg.setAttribute('height', 16);
      labelBg.setAttribute('rx', 3);
      labelBg.setAttribute('class', 'relationship-label-bg');
      labelBg.setAttribute('data-category', link.category || 'other');
      g.appendChild(labelBg);

      const label = document.createElementNS('http://www.w3.org/2000/svg', 'text');
      label.setAttribute('x', labelX);
      label.setAttribute('y', labelY);
      label.setAttribute('class', 'relationship-label');
      label.setAttribute('text-anchor', 'middle');
      label.setAttribute('data-category', link.category || 'other');
      label.textContent = getRelationshipTypeLabel(link.type);
      g.appendChild(label);
    });

    // Draw nodes
    nodes.forEach(node => {
      // Calculate position
      const x = node.isCurrent ? centerX : centerX + (Math.random() - 0.5) * 150;
      const y = node.isCurrent ? centerY : centerY + (Math.random() - 0.5) * 150;

      // Create node group
      const nodeGroup = document.createElementNS('http://www.w3.org/2000/svg', 'g');
      nodeGroup.setAttribute('class', `node ${node.isCurrent ? 'current' : ''}`);
      nodeGroup.setAttribute('data-id', node.id);
      nodeGroup.setAttribute('transform', `translate(${x}, ${y})`);

      if (!node.isCurrent) {
        nodeGroup.addEventListener('click', () => {
          if (onElementSelect) {
            onElementSelect(node.id);
          }
        });
      }

      // Create node circle
      const circle = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
      circle.setAttribute('r', node.isCurrent ? 30 : 25);
      circle.setAttribute('class', 'node-circle');
      nodeGroup.appendChild(circle);

      // Create node label
      const label = document.createElementNS('http://www.w3.org/2000/svg', 'text');
      label.setAttribute('class', 'node-label');
      label.setAttribute('text-anchor', 'middle');
      label.setAttribute('dy', '0.3em');
      label.textContent = node.name.length > 10 ? node.name.substring(0, 10) + '...' : node.name;
      nodeGroup.appendChild(label);

      // Create node type label
      const typeLabel = document.createElementNS('http://www.w3.org/2000/svg', 'text');
      typeLabel.setAttribute('class', 'node-type-label');
      typeLabel.setAttribute('text-anchor', 'middle');
      typeLabel.setAttribute('dy', '1.8em');
      typeLabel.textContent = getElementTypeLabel(node.type);
      nodeGroup.appendChild(typeLabel);

      g.appendChild(nodeGroup);
    });

  }, [element, relationships, relatedElements, onElementSelect, maxRelationships, filterCategory, filterType]);

  // If no relationships
  if (!relationships || relationships.length === 0) {
    return (
      <div className="mini-relationship-map empty">
        <div className="map-header">
          <h3 className="map-title">Relationships</h3>
          {onCreateRelationship && (
            <button
              className="add-relationship-button"
              onClick={onCreateRelationship}
            >
              Add Relationship
            </button>
          )}
        </div>
        <p>No relationships found for this element.</p>
      </div>
    );
  }

  return (
    <div className="mini-relationship-map">
      <div className="map-header">
        <h3 className="map-title">Relationships</h3>
        <div className="map-header-actions">
          <button
            className="filter-toggle-button"
            onClick={() => setShowFilters(!showFilters)}
          >
            {showFilters ? 'Hide Filters' : 'Show Filters'}
          </button>
          {onCreateRelationship && (
            <button
              className="add-relationship-button"
              onClick={onCreateRelationship}
            >
              Add Relationship
            </button>
          )}
        </div>
      </div>

      {showFilters && (
        <div className="map-filters">
          <div className="filter-group">
            <label>Category:</label>
            <select
              value={filterCategory}
              onChange={e => setFilterCategory(e.target.value)}
            >
              <option value="all">All Categories</option>
              {getRelationshipCategories().filter(c => c !== 'all').map(category => (
                <option key={category} value={category}>
                  {category.charAt(0).toUpperCase() + category.slice(1)}
                </option>
              ))}
            </select>
          </div>

          <div className="filter-group">
            <label>Type:</label>
            <select
              value={filterType}
              onChange={e => setFilterType(e.target.value)}
            >
              <option value="all">All Types</option>
              {getRelationshipTypesFromRelationships().filter(t => t !== 'all').map(type => (
                <option key={type} value={type}>
                  {getRelationshipTypeLabel(type)}
                </option>
              ))}
            </select>
          </div>
        </div>
      )}

      <div className="map-container">
        <svg ref={svgRef} className="relationship-svg" viewBox="0 0 300 200" preserveAspectRatio="xMidYMid meet"></svg>
      </div>

      <div className="map-legend">
        <div className="legend-title">Legend</div>
        <div className="legend-items">
          {getRelationshipCategories().filter(c => c !== 'all').map(category => (
            <div key={category} className={`legend-item ${category}`}>
              <div className={`legend-color ${category}`}></div>
              <div className="legend-label">{category.charAt(0).toUpperCase() + category.slice(1)}</div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default MiniRelationshipMap;

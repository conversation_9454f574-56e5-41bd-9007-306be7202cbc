// frontend/src/components/BookRequiredWrapper.js
import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { Navigate } from 'react-router-dom';
import { selectAllBooks, selectCurrentBook, selectBooksLoading } from '../redux/slices/bookSlice';
import loadingImage from '../assets/loading-circle.gif';

/**
 * A minimal wrapper component that ensures a book is selected before rendering its children.
 * If no books are available, it redirects to the Books page.
 * Book selection is handled by the bookSelectionMiddleware.
 */
const BookRequiredWrapper = ({ children }) => {
  const books = useSelector(selectAllBooks);
  const selectedBook = useSelector(selectCurrentBook);
  const isLoading = useSelector(selectBooksLoading);
  const [shouldRedirect, setShouldRedirect] = useState(false);
  const [initialLoadComplete, setInitialLoadComplete] = useState(false);

  // Combined useEffect for all book-related logic
  useEffect(() => {
    // If books are still loading, don't do anything yet
    if (isLoading) {
      return;
    }

    // Once loading is complete, mark initial load as done
    if (!initialLoadComplete) {
      setInitialLoadComplete(true);
    }

    // If no books are available after loading is complete, set redirect flag
    if (!isLoading && books && books.length === 0 && initialLoadComplete) {
      const timer = setTimeout(() => {
        console.log('BookRequiredWrapper: No books available after delay, redirecting to Books page');
        setShouldRedirect(true);
      }, 500); // 500ms delay to prevent flickering

      return () => clearTimeout(timer);
    }

    // If a book is selected, save it to localStorage
    if (selectedBook && selectedBook.book_id) {
      localStorage.setItem('currentBookId', selectedBook.book_id);
      console.log('BookRequiredWrapper - Saved current book ID to localStorage:', selectedBook.book_id);
    }
  }, [books, isLoading, initialLoadComplete, selectedBook]);

  // If we should redirect, do so
  if (shouldRedirect) {
    return <Navigate to="/books" />;
  }

  // If books are still loading or we're waiting for the delay, show a loading indicator
  if (isLoading || !initialLoadComplete) {
    return (
      <div className="loading-container" style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <div style={{ textAlign: 'center' }}>
          <img src={loadingImage} alt="Loading" style={{ width: '50px', height: '50px' }} />
          <p>Loading books...</p>
        </div>
      </div>
    );
  }

  // If books exist but no book is selected, show a loading message
  if (!selectedBook) {
    return (
      <div className="loading-container" style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <div style={{ textAlign: 'center' }}>
          <img src={loadingImage} alt="Loading" style={{ width: '50px', height: '50px' }} />
          <p>Selecting book...</p>
        </div>
      </div>
    );
  }

  return children;
};

export default BookRequiredWrapper;

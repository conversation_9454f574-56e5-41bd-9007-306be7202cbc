// frontend/src/components/WorldElementContextPanel.js
import React, { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { 
  selectAllElementsForRelationshipView, 
  selectCurrentBookId
} from '../redux/slices/worldBuildingSlice';
import { getElementTypeLabel } from '../utils/templateUtils';
import './WorldElementContextPanel.css';

/**
 * Component for displaying relevant world elements based on the content being written
 * @param {Object} props - Component props
 * @param {string} props.content - The content being written
 * @param {Function} props.onElementSelect - Callback when an element is selected
 * @returns {JSX.Element} WorldElementContextPanel UI
 */
const WorldElementContextPanel = ({ 
  content = '', 
  onElementSelect 
}) => {
  const dispatch = useDispatch();
  const allElements = useSelector(selectAllElementsForRelationshipView);
  const bookId = useSelector(selectCurrentBookId);
  
  // State for relevant elements
  const [relevantElements, setRelevantElements] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [activeFilter, setActiveFilter] = useState('all');
  
  // Find relevant elements when content changes
  useEffect(() => {
    if (!content) {
      setRelevantElements([]);
      return;
    }
    
    findRelevantElements(content);
  }, [content, allElements]);
  
  // Find elements that are relevant to the content
  const findRelevantElements = async (text) => {
    setIsLoading(true);
    
    try {
      // In a real implementation, this would use NER (Named Entity Recognition)
      // and vector embeddings to find relevant elements. For now, we'll use a
      // simple keyword matching approach.
      
      // Extract potential entity names from the text
      const words = text.split(/\s+/);
      const potentialEntities = words.filter(word => 
        word.length > 3 && /^[A-Z]/.test(word)
      );
      
      // Find elements that match the potential entities
      const matches = [];
      
      allElements.forEach(element => {
        // Check if the element name is mentioned in the text
        const nameMatches = text.toLowerCase().includes(element.name.toLowerCase());
        
        // Check if any attributes of the element are mentioned in the text
        let attributeMatches = false;
        if (element.attributes) {
          Object.values(element.attributes).forEach(value => {
            if (typeof value === 'string' && value.length > 3) {
              if (text.toLowerCase().includes(value.toLowerCase())) {
                attributeMatches = true;
              }
            }
          });
        }
        
        // Add the element if it matches
        if (nameMatches || attributeMatches) {
          matches.push({
            element,
            relevance: nameMatches ? 0.8 : 0.5, // Higher relevance for name matches
            matchType: nameMatches ? 'name' : 'attribute'
          });
        }
      });
      
      // Sort by relevance
      matches.sort((a, b) => b.relevance - a.relevance);
      
      setRelevantElements(matches);
    } catch (error) {
      console.error('Error finding relevant elements:', error);
    } finally {
      setIsLoading(false);
    }
  };
  
  // Filter elements by type
  const getFilteredElements = () => {
    if (activeFilter === 'all') {
      return relevantElements;
    }
    
    return relevantElements.filter(item => 
      item.element.element_type === activeFilter
    );
  };
  
  // Get unique element types from relevant elements
  const getElementTypes = () => {
    const types = new Set();
    relevantElements.forEach(item => {
      types.add(item.element.element_type);
    });
    return Array.from(types);
  };
  
  // Render the context panel UI
  return (
    <div className="world-element-context-panel">
      <div className="context-panel-header">
        <h3>World Context</h3>
        {isLoading && <div className="loading-indicator">Analyzing...</div>}
      </div>
      
      {relevantElements.length > 0 ? (
        <>
          <div className="element-type-filters">
            <button 
              className={`type-filter ${activeFilter === 'all' ? 'active' : ''}`}
              onClick={() => setActiveFilter('all')}
            >
              All
            </button>
            {getElementTypes().map(type => (
              <button 
                key={type}
                className={`type-filter ${activeFilter === type ? 'active' : ''}`}
                onClick={() => setActiveFilter(type)}
              >
                {getElementTypeLabel(type)}
              </button>
            ))}
          </div>
          
          <div className="relevant-elements-list">
            {getFilteredElements().map((item, index) => (
              <div 
                key={item.element.element_id}
                className={`relevant-element-item ${item.matchType}`}
                onClick={() => onElementSelect(item.element.element_id)}
              >
                <div className="element-header">
                  <div className="element-name">{item.element.name}</div>
                  <div className="element-type">{getElementTypeLabel(item.element.element_type)}</div>
                </div>
                <div className="element-relevance">
                  <div 
                    className="relevance-bar" 
                    style={{ width: `${item.relevance * 100}%` }}
                  ></div>
                </div>
                <div className="element-preview">
                  {item.element.attributes?.description?.substring(0, 100)}
                  {item.element.attributes?.description?.length > 100 ? '...' : ''}
                </div>
              </div>
            ))}
          </div>
        </>
      ) : (
        <div className="no-relevant-elements">
          {content ? 'No relevant world elements found.' : 'Start writing to see relevant world elements.'}
        </div>
      )}
    </div>
  );
};

export default WorldElementContextPanel;

/* frontend/src/components/WorldBuildingHeader.css */
.world-building-header {
  padding: 15px 20px;
  background: var(--background-primary);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: var(--shadow-sm);
  z-index: 10;
}

.world-building-title {
  display: flex;
  flex-direction: column;
}

.world-building-title h2 {
  margin: 0 0 5px 0;
  font-size: 1.5rem;
  color: var(--text-primary);
}

.world-building-genre {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.change-genre-link {
  color: var(--primary);
  text-decoration: none;
  font-weight: 500;
}

.change-genre-link:hover {
  text-decoration: underline;
}

.view-mode-controls {
  display: flex;
  gap: 10px;
}

.view-mode-button {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  background: var(--background-tertiary);
  color: var(--text-primary);
  cursor: pointer;
  transition: all 0.2s ease;
}

.view-mode-button.active {
  background: var(--primary);
  color: white;
}

.view-mode-button:hover:not(.active) {
  background: var(--background-secondary);
}

.view-mode-button.ai-button {
  background: var(--primary);
  color: white;
}

.view-mode-button.ai-button:hover:not(.active) {
  background: var(--button-primary-hover);
}

.view-mode-button.ai-button.active {
  background: var(--secondary);
  color: white;
}

/* Responsive styles */
@media (max-width: 768px) {
  .world-building-header {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }

  .view-mode-controls {
    width: 100%;
  }

  .view-mode-button {
    flex: 1;
    text-align: center;
  }
}

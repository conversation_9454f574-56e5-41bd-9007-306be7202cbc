import React, { useState, useEffect } from 'react';
import './ChapterSidebar.css';

// Simple chapter item component (no drag and drop)
function ChapterItem({ chapter, isSelected, onSelect }) {
  return (
    <div
      className={`chapter-item ${isSelected ? 'selected' : ''}`}
      onClick={() => onSelect(chapter)}
    >
      <span className="chapter-title">{chapter.title}</span>
    </div>
  );
}

function ChapterSidebar({ chapters, currentChapter, onChapterSelect, onAddChapter }) {
  const [isAddingChapter, setIsAddingChapter] = useState(false);
  const [newChapterTitle, setNewChapterTitle] = useState('');

  const handleChapterSelect = (chapter) => {
    if (onChapterSelect) {
      onChapterSelect(chapter);
    }
  };

  const handleAddChapter = () => {
    setIsAddingChapter(true);
  };

  const handleCancelAdd = () => {
    setIsAddingChapter(false);
    setNewChapterTitle('');
  };

  const handleSubmitNewChapter = async (e) => {
    e.preventDefault();

    if (!newChapterTitle.trim()) {
      return;
    }

    try {
      if (onAddChapter) {
        await onAddChapter(newChapterTitle.trim());
        setNewChapterTitle('');
        setIsAddingChapter(false);
      }
    } catch (error) {
      console.error('Failed to add chapter:', error);
      alert(`Failed to add chapter: ${error.message}`);
    }
  };

  return (
    <div className="chapter-sidebar">
      <div className="chapter-sidebar-header">
        <h3>Chapters</h3>
        {!isAddingChapter && (
          <button className="add-chapter-button" onClick={handleAddChapter}>
            + Add Chapter
          </button>
        )}
      </div>

      {isAddingChapter && (
        <div className="add-chapter-form-container">
          <form onSubmit={handleSubmitNewChapter} className="add-chapter-form">
            <input
              type="text"
              value={newChapterTitle}
              onChange={(e) => setNewChapterTitle(e.target.value)}
              placeholder="Chapter title"
              autoFocus
              required
            />
            <div className="form-buttons">
              <button type="submit" className="save-button">Add</button>
              <button type="button" className="cancel-button" onClick={handleCancelAdd}>Cancel</button>
            </div>
          </form>
        </div>
      )}

      <div className="chapters-list">
        {chapters && chapters.length > 0 ? (
          chapters.map(chapter => (
            <ChapterItem
              key={chapter.id}
              chapter={chapter}
              isSelected={currentChapter?.id === chapter.id}
              onSelect={handleChapterSelect}
            />
          ))
        ) : (
          <div className="no-chapters">No chapters available</div>
        )}
      </div>
    </div>
  );
}

export default ChapterSidebar;
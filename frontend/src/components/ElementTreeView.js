// frontend/src/components/ElementTreeView.js
import React, { useState, useEffect } from 'react';
import './ElementTreeView.css';
import { getElementTypeLabel } from '../utils/templateUtils';

/**
 * Component for displaying a hierarchical tree view of elements
 * @param {Object} props - Component props
 * @returns {JSX.Element} ElementTreeView UI
 */
const ElementTreeView = ({
  elements = [],
  selectedElementId,
  onElementSelect,
  expandedByDefault = true
}) => {
  // State for tracking expanded nodes
  const [expandedNodes, setExpandedNodes] = useState({});

  // Initialize expanded state when elements change
  useEffect(() => {
    if (elements.length === 0) return;

    // Create initial expanded state
    const initialExpanded = {};

    // If a specific element is selected, expand its ancestors
    if (selectedElementId) {
      // Find the element and its ancestors
      const findAncestors = (elementId, path = []) => {
        const element = elements.find(e => e.element_id === elementId);
        if (!element) return path;

        path.unshift(element.element_id);

        if (element.parent_id) {
          return findAncestors(element.parent_id, path);
        }

        return path;
      };

      const ancestorPath = findAncestors(selectedElementId);

      // Expand all ancestors
      ancestorPath.forEach(id => {
        initialExpanded[id] = true;
      });
    } else {
      // If no element is selected, use the expandedByDefault setting
      elements.forEach(element => {
        if (element.has_children) {
          initialExpanded[element.element_id] = expandedByDefault;
        }
      });
    }

    setExpandedNodes(initialExpanded);
  }, [elements, selectedElementId, expandedByDefault]);

  // Toggle node expansion
  const toggleExpand = (elementId, event) => {
    event.stopPropagation();
    setExpandedNodes(prev => ({
      ...prev,
      [elementId]: !prev[elementId]
    }));
  };

  // Handle element selection
  const handleSelect = (elementId) => {
    if (onElementSelect) {
      onElementSelect(elementId);
    }
  };

  // Build the tree structure
  const buildTree = () => {
    // Create a map of parent IDs to child elements
    const childrenMap = {};

    elements.forEach(element => {
      const parentId = element.parent_id || 'root';
      if (!childrenMap[parentId]) {
        childrenMap[parentId] = [];
      }
      childrenMap[parentId].push(element);
    });

    // Recursive function to render a node and its children
    const renderNode = (element, level = 0) => {
      const hasChildren = element.has_children && childrenMap[element.element_id]?.length > 0;
      const isExpanded = expandedNodes[element.element_id];
      const isSelected = element.element_id === selectedElementId;

      return (
        <div key={element.element_id} className="tree-node-container" style={{ paddingLeft: `${level * 16}px` }}>
          <div
            className={`tree-node ${isSelected ? 'selected' : ''}`}
            onClick={() => handleSelect(element.element_id)}
          >
            {hasChildren && (
              <span
                className={`expand-toggle ${isExpanded ? 'expanded' : ''}`}
                onClick={(e) => toggleExpand(element.element_id, e)}
              >
                {isExpanded ? '▼' : '►'}
              </span>
            )}
            <span className="node-name">{element.name}</span>
            <span className="node-type">{getElementTypeLabel(element.element_type)}</span>
          </div>

          {hasChildren && isExpanded && (
            <div className="tree-children">
              {childrenMap[element.element_id].map(child => renderNode(child, level + 1))}
            </div>
          )}
        </div>
      );
    };

    // Render root elements
    const rootElements = childrenMap['root'] || [];
    return rootElements.map(element => renderNode(element));
  };

  return (
    <div className="element-tree-view">
      <div className="tree-header">
        <h3>Element Hierarchy</h3>
        <button
          className="expand-all-button"
          onClick={() => {
            // Toggle all nodes based on whether most are currently expanded
            const expandedCount = Object.values(expandedNodes).filter(Boolean).length;
            const shouldExpand = expandedCount < elements.length / 2;

            const newExpandedState = {};
            elements.forEach(element => {
              if (element.has_children) {
                newExpandedState[element.element_id] = shouldExpand;
              }
            });

            setExpandedNodes(newExpandedState);
          }}
        >
          {Object.values(expandedNodes).filter(Boolean).length > elements.length / 2
            ? 'Collapse All'
            : 'Expand All'}
        </button>
      </div>

      <div className="tree-content">
        {elements.length === 0 ? (
          <div className="empty-tree">No elements available</div>
        ) : (
          buildTree()
        )}
      </div>
    </div>
  );
};

export default ElementTreeView;

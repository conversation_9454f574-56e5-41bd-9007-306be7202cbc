// frontend/src/components/TitleBar.js
import React, { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { selectCurrentBook, fetchAllBooks, selectBook, fetchBookById } from '../redux/slices/bookSlice';
import './TitleBar.css';

const TitleBar = React.memo(({ currentPage, currentChapter, onBookSelect }) => {
  const navigate = useNavigate();

  // Redux state
  const dispatch = useDispatch();
  const selectedBook = useSelector(selectCurrentBook);

  // We don't need to fetch books here anymore
  // This was causing the book details to be overwritten
  // The App component is responsible for fetching books on initialization

  const title = selectedBook
    ? currentPage === 'write'
      ? `${selectedBook.title} - ${currentChapter ? currentChapter.title : 'Select a Chapter'}`
      : currentPage === 'plot'
      ? `${selectedBook.title} - Plot`
      : currentPage === 'world'
      ? `${selectedBook.title} - World`
      : currentPage === 'characters'
      ? `${selectedBook.title} - Characters`
      : currentPage === 'brainstorm'
      ? `${selectedBook.title} - Brainstorm`
      : selectedBook.title
    : 'Secure Story';

  // Handle click to navigate to books page
  const handleGoToBooks = () => {
    navigate('/books');
  };

  console.debug('TitleBar rendered with:', { selectedBook: selectedBook?.book_id });

  return (
    <div className="title-bar">
      <div className="title-bar-content">
        <h2>{title}</h2>
        <div className="book-info" onClick={handleGoToBooks}>
          {selectedBook ? (
            <div className="current-book">
              <span className="book-title">{selectedBook.title}</span>
              <span className="change-book">Change Book</span>
            </div>
          ) : (
            <div className="no-book">
              <span className="select-book">Select a Book</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
});

export default TitleBar;
/* frontend/src/components/MagicSystemVisualizer.css */
.magic-system-visualizer {
  display: flex;
  flex-direction: column;
  background-color: var(--background-primary);
  border-radius: 8px;
  box-shadow: var(--shadow-md);
  padding: 20px;
  height: 100%;
  overflow: hidden;
  position: relative;
}

/* Energy Bar */
.magic-system-energy-bar {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding: 10px;
  background-color: var(--background-secondary);
  border-radius: 8px;
}

.energy-label {
  font-weight: bold;
  margin-right: 10px;
  color: var(--text-primary);
}

.energy-bar-container {
  flex: 1;
  height: 20px;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 10px;
  overflow: hidden;
  margin: 0 10px;
}

.energy-bar-fill {
  height: 100%;
  transition: width 0.5s ease, background-color 0.5s ease;
}

.energy-value {
  font-weight: bold;
  min-width: 50px;
  text-align: right;
  color: var(--text-primary);
}

.restore-energy-button {
  background-color: var(--primary);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 5px 10px;
  margin-left: 10px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.restore-energy-button:hover {
  background-color: var(--primary-dark);
}

/* Tabs */
.magic-system-tabs {
  display: flex;
  margin-bottom: 20px;
  border-bottom: 1px solid var(--border-color);
}

.tab-button {
  padding: 10px 20px;
  background: none;
  border: none;
  border-bottom: 3px solid transparent;
  color: var(--text-secondary);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.tab-button:hover {
  color: var(--text-primary);
}

.tab-button.active {
  color: var(--primary);
  border-bottom-color: var(--primary);
}

/* Content Area */
.magic-system-content {
  flex: 1;
  overflow-y: auto;
  padding-right: 10px;
}

/* Overview Tab */
.magic-system-overview {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.magic-system-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.magic-system-title {
  display: flex;
  align-items: center;
  gap: 15px;
}

.magic-system-title h2 {
  margin: 0;
  color: var(--text-primary);
}

.magic-system-energy-type {
  padding: 5px 10px;
  border-radius: 20px;
  color: white;
  font-size: 0.8rem;
  font-weight: bold;
  text-transform: uppercase;
}

.acquisition-badge {
  padding: 5px 10px;
  border-radius: 4px;
  background-color: var(--background-secondary);
  color: var(--text-primary);
  font-size: 0.9rem;
  text-transform: capitalize;
}

.magic-system-stats {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.stat-item {
  background-color: var(--background-secondary);
  padding: 10px 15px;
  border-radius: 8px;
  min-width: 120px;
}

.stat-label {
  font-size: 0.8rem;
  color: var(--text-secondary);
  margin-bottom: 5px;
}

.stat-value {
  font-weight: 500;
  color: var(--text-primary);
  text-transform: capitalize;
}

.magic-system-details {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.detail-section {
  background-color: var(--background-secondary);
  padding: 15px;
  border-radius: 8px;
}

.detail-section h3, .detail-section h4 {
  margin-top: 0;
  margin-bottom: 10px;
  color: var(--text-primary);
}

.detail-section p {
  margin: 0;
  color: var(--text-secondary);
  line-height: 1.5;
}

/* Spells Tab */
.magic-system-spells, .magic-system-powers, .magic-system-artifacts {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  height: 100%;
}

.spells-list, .powers-list, .artifacts-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
  overflow-y: auto;
  padding-right: 10px;
}

.spell-card, .power-card, .artifact-card {
  background-color: var(--background-secondary);
  border-radius: 8px;
  padding: 15px;
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
  border-left: 4px solid transparent;
}

.spell-card:hover, .power-card:hover, .artifact-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.spell-card.selected, .power-card.selected, .artifact-card.selected {
  border-left-color: var(--primary);
  background-color: var(--background-hover);
}

.spell-header, .power-header, .artifact-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.spell-header h3, .power-header h3, .artifact-header h3 {
  margin: 0;
  color: var(--text-primary);
}

.spell-difficulty {
  padding: 3px 8px;
  border-radius: 4px;
  color: white;
  font-size: 0.8rem;
  text-transform: capitalize;
}

.spell-details, .power-details, .artifact-details {
  display: flex;
  flex-direction: column;
  gap: 5px;
  margin-bottom: 10px;
}

.spell-stat, .power-stat, .artifact-stat {
  display: flex;
  justify-content: space-between;
  font-size: 0.9rem;
}

.spell-description, .power-description, .artifact-description {
  margin-bottom: 15px;
  color: var(--text-secondary);
  font-size: 0.9rem;
  line-height: 1.4;
}

.cast-spell-button, .use-power-button {
  background-color: var(--primary);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 12px;
  cursor: pointer;
  transition: background-color 0.2s;
  width: 100%;
}

.cast-spell-button:hover, .use-power-button:hover {
  background-color: var(--primary-dark);
}

.cast-spell-button:disabled, .use-power-button:disabled {
  background-color: var(--text-disabled);
  cursor: not-allowed;
}

.spell-detail-view, .power-detail-view, .artifact-detail-view {
  background-color: var(--background-secondary);
  border-radius: 8px;
  padding: 20px;
  overflow-y: auto;
  height: 100%;
}

/* Animation */
.magic-effect-animation {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  animation: fadeIn 0.3s ease-in-out, fadeOut 0.3s ease-in-out 1.7s;
}

.magic-effect-inner {
  padding: 30px 60px;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 10px;
  text-align: center;
  animation: pulseEffect 1.5s ease-in-out;
}

.magic-effect-name {
  font-size: 2rem;
  font-weight: bold;
  color: #333;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.8);
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeOut {
  from { opacity: 1; }
  to { opacity: 0; }
}

@keyframes pulseEffect {
  0% { transform: scale(0.8); opacity: 0.5; }
  50% { transform: scale(1.2); opacity: 1; }
  100% { transform: scale(1); opacity: 1; }
}

/* No data state */
.no-data {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: var(--text-secondary);
  font-style: italic;
  background-color: var(--background-secondary);
  border-radius: 8px;
}

/* Dark theme adjustments */
.theme-dark .magic-system-visualizer {
  background-color: var(--background-primary-dark);
}

.theme-dark .magic-system-energy-bar,
.theme-dark .stat-item,
.theme-dark .detail-section,
.theme-dark .spell-card,
.theme-dark .power-card,
.theme-dark .artifact-card,
.theme-dark .spell-detail-view,
.theme-dark .power-detail-view,
.theme-dark .artifact-detail-view,
.theme-dark .no-data {
  background-color: var(--background-secondary-dark);
}

.theme-dark .magic-effect-inner {
  background-color: rgba(30, 30, 30, 0.9);
}

.theme-dark .magic-effect-name {
  color: #fff;
  text-shadow: 0 0 10px rgba(0, 0, 0, 0.8);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .magic-system-spells, .magic-system-powers, .magic-system-artifacts {
    grid-template-columns: 1fr;
  }
  
  .spell-detail-view, .power-detail-view, .artifact-detail-view {
    margin-top: 20px;
  }
}

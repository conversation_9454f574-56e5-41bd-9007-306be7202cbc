/* frontend/src/components/CharacterSidebar.css */
.character-sidebar {
  position: fixed;
  top: 80px;
  left: 0;
  height: calc(100vh - 80px);
  width: 300px;
  background-color: #f5f5f5;
  border-right: 1px solid #ddd;
  transition: transform 0.3s ease;
  z-index: 100;
}

.character-sidebar.closed {
  transform: translateX(-290px);
}

.sidebar-handle {
  position: absolute;
  top: 20px;
  right: -30px;
  width: 30px;
  height: 60px;
  background-color: #007bff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 0 5px 5px 0;
  z-index: 101; /* Ensure handle is above other elements */
}

.sidebar-content {
  padding: 15px;
  overflow-y: auto;
  height: 100%;
}

.sidebar-content h3 {
  margin-bottom: 15px;
}

.sidebar-content input,
.sidebar-content textarea {
  width: 100%;
  padding: 8px;
  margin-bottom: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.sidebar-buttons {
  display: flex;
  gap: 10px;
  margin-top: 15px;
}

.sidebar-buttons button {
  flex: 1;
  padding: 8px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

/* AI Modal Styles */
.loading-spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border-left-color: #09f;
  animation: spin 1s linear infinite;
  margin: 20px auto;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.ai-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.ai-modal-content {
  background-color: white;
  padding: 20px;
  border-radius: 5px;
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
}

.ai-buttons {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
}

.ai-buttons button {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.ai-buttons button:nth-child(1) {
  background-color: #4CAF50;
  color: white;
}

.ai-buttons button:nth-child(2) {
  background-color: #f44336;
  color: white;
}

.ai-buttons button:nth-child(3) {
  background-color: #2196F3;
  color: white;
}

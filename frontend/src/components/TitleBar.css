/* frontend/src/components/TitleBar.css */
.title-bar {
  width: 100%;
  height: 50px;
  border-bottom: 1px solid var(--title-bar-border, var(--border-color));
  display: flex;
  align-items: center;
  padding: 0 20px;
  background-color: var(--title-bar-background, var(--background-secondary));
  color: var(--title-bar-text, var(--text-primary));
}

.title-bar-content {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-bar h2 {
  margin: 0;
  font-size: 18px;
  color: var(--title-bar-text, var(--text-primary));
}

.book-info {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 4px;
  transition: background-color 0.2s;
  background-color: var(--book-info-background, var(--background-tertiary));
}

.book-info:hover {
  background-color: var(--book-info-hover, var(--background-secondary));
}

.current-book {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.book-title {
  font-weight: 500;
  color: var(--book-title-color, var(--text-primary));
  font-size: 14px;
}

.change-book {
  font-size: 12px;
  color: var(--change-book-color, var(--primary));
  text-decoration: underline;
}

.no-book {
  color: var(--text-secondary);
  font-size: 14px;
  font-style: italic;
}
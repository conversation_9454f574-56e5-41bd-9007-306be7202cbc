.character-panel {
  width: 280px;
  border-left: 1px solid #ddd;
  padding: 15px;
  overflow-y: auto;
  background-color: #f5f5f5;
}

.character-panel-header {
  margin-bottom: 15px;
}

.character-search {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-top: 10px;
}

.characters-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.character-card {
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow: hidden;
}

.character-header {
  padding: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  background-color: #f9f9f9;
}

.character-header h4 {
  margin: 0;
}

.expand-icon {
  font-size: 0.8em;
}

.character-details {
  padding: 10px;
  font-size: 0.9em;
  border-top: 1px solid #eee;
}

.character-details > div {
  margin-bottom: 8px;
}

.character-details ul {
  margin: 5px 0;
  padding-left: 20px;
}

.no-characters {
  font-style: italic;
  color: #888;
  text-align: center;
  margin-top: 20px;
}
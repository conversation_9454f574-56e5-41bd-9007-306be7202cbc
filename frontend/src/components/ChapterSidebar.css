.chapter-sidebar {
  width: 250px;
  border-right: 1px solid var(--chapter-sidebar-border, var(--border-color));
  padding: 15px;
  overflow-y: auto;
  background-color: var(--chapter-sidebar-background, var(--background-secondary));
  display: flex;
  flex-direction: column;
  color: var(--text-primary);
}

.chapter-sidebar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.chapter-sidebar-header h3 {
  margin: 0;
}

.add-chapter-button {
  background-color: var(--button-primary-background);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 5px 10px;
  font-size: 12px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.add-chapter-button:hover {
  background-color: var(--button-primary-hover);
}

/* Add chapter form */
.add-chapter-form-container {
  padding: 10px;
  background-color: var(--background-tertiary);
  border-radius: 4px;
  margin-bottom: 10px;
  border: 1px solid var(--border-color);
}

.add-chapter-form {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.add-chapter-form input {
  padding: 8px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  font-size: 14px;
  background-color: var(--background-primary);
  color: var(--text-primary);
}

.form-buttons {
  display: flex;
  gap: 10px;
}

.save-button {
  background-color: var(--button-primary-background);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  cursor: pointer;
  font-size: 14px;
}

.save-button:hover {
  background-color: var(--button-primary-hover);
}

/* Cancel button styling is now in index.css */

.chapters-list {
  display: flex;
  flex-direction: column;
  gap: 5px;
  margin-top: 10px;
}

.chapter-item {
  padding: 8px 10px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
  gap: 8px;
  user-select: none;
  border: 1px solid transparent;
  background-color: var(--chapter-item-background, var(--background-tertiary));
  color: var(--text-primary);
}

.chapter-item:hover {
  background-color: var(--chapter-item-hover, var(--background-secondary));
}

.chapter-item.selected {
  background-color: #4a90e2;
  color: white;
}

.theme-dark .chapter-item.selected {
  background-color: #BB86FC;
  color: white;
}

/* Drag handle removed */

.chapter-title {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.no-chapters {
  font-style: italic;
  color: var(--text-secondary);
  text-align: center;
  margin-top: 20px;
}
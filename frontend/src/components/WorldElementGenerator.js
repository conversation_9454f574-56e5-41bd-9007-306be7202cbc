// frontend/src/components/WorldElementGenerator.js
import React, { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { generateWorldElements, fetchWorldElements } from '../services/worldBuildingApiService';
import {
  selectCurrentBookId,
  selectCategories,
  selectElementsForCategory
} from '../redux/slices/worldBuildingSlice';
import './WorldElementGenerator.css';

/**
 * Component for generating world elements using AI
 * @param {Object} props - Component props
 * @returns {JSX.Element} WorldElementGenerator UI
 */
const WorldElementGenerator = ({ categoryId, onElementsGenerated }) => {
  // const dispatch = useDispatch(); // Commented out as it's not currently used
  const bookId = useSelector(selectCurrentBookId);
  const categories = useSelector(selectCategories);
  const categoryElements = useSelector(state =>
    categoryId ? selectElementsForCategory(state) : []
  );

  // State for the generator
  const [prompt, setPrompt] = useState('');
  const [count, setCount] = useState(1);
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState(null);
  const [categoryInfo, setCategoryInfo] = useState(null);
  const [promptSuggestions, setPromptSuggestions] = useState([]);
  const [batchMode, setBatchMode] = useState(false);
  const [showTemplates, setShowTemplates] = useState(false);
  const [templates, setTemplates] = useState([]);
  const [generatedPreview, setGeneratedPreview] = useState([]);
  const [showPreview, setShowPreview] = useState(false);
  const [showVisualAids, setShowVisualAids] = useState(false);
  const [parentElements, setParentElements] = useState([]);
  const [selectedParentId, setSelectedParentId] = useState('');
  // Removed hierarchyMode state as it's no longer needed
  const [nestedMode, setNestedMode] = useState(false);
  const [subElementCount, setSubElementCount] = useState(3);

  // New state variables for multi-level hierarchical generation
  const [nestingDepth, setNestingDepth] = useState(1);
  const [level1Count, setLevel1Count] = useState(3);
  const [level2Count, setLevel2Count] = useState(2);
  const [level3Count, setLevel3Count] = useState(2);
  const [multiLevelMode, setMultiLevelMode] = useState(false);

  // State for tracking detected magic type in prompts
  const [detectedMagicType, setDetectedMagicType] = useState('magic_system');
  const [detectedMagicTypes, setDetectedMagicTypes] = useState(['magic_system']);

  // Variable to store detected types in the current scope
  let detectedTypes = [];

  // Visual aids for different categories
  const visualAids = {
    'cat_physical_locations': [
      { title: 'City Layout', description: 'Example of a city with districts and landmarks', url: 'https://i.imgur.com/JKYf1LM.jpg' },
      { title: 'Natural Landscape', description: 'Example of geographical features', url: 'https://i.imgur.com/8XO3Ld4.jpg' }
    ],
    'cat_social_cultures': [
      { title: 'Cultural Elements', description: 'Visual representation of cultural components', url: 'https://i.imgur.com/pYy0Uph.jpg' }
    ],
    'cat_social_governments': [
      { title: 'Government Structure', description: 'Example hierarchy of government bodies', url: 'https://i.imgur.com/QZHl9FJ.jpg' }
    ],
    'cat_metaphysical_religion': [
      { title: 'Pantheon Structure', description: 'Example of deity relationships', url: 'https://i.imgur.com/L3Ncfj6.jpg' }
    ]
  };

  // Get category information when categoryId changes
  useEffect(() => {
    if (categoryId && categories.byId[categoryId]) {
      setCategoryInfo(categories.byId[categoryId]);

      // Set prompt suggestions based on category
      const suggestions = getCategorySuggestions(categoryId, categories.byId[categoryId]);
      setPromptSuggestions(suggestions);

      // Set templates based on category
      const categoryTemplates = getCategoryTemplates(categoryId, categories.byId[categoryId]);
      setTemplates(categoryTemplates);

      // Fetch potential parent elements for this category
      if (bookId) {
        fetchPotentialParents(bookId, categoryId);
      }
    } else {
      setCategoryInfo(null);
      setPromptSuggestions([]);
      setTemplates([]);
      setParentElements([]);
    }
  }, [categoryId, categories, bookId]);

  // Fetch potential parent elements for the selected category
  const fetchPotentialParents = async (bookId, categoryId) => {
    try {
      // The backend returns an array of elements directly
      const elements = await fetchWorldElements(bookId, categoryId);
      console.log('Fetched potential parents:', elements);

      if (Array.isArray(elements)) {
        // Filter to only include elements that can be parents
        const potentialParents = elements.filter(element =>
          // Include elements that don't have a parent or are top-level parents
          !element.parent_id || element.has_children
        );
        setParentElements(potentialParents);
      } else {
        console.error('Unexpected response structure:', elements);
        setParentElements([]);
      }
    } catch (error) {
      console.error('Error fetching potential parent elements:', error);
      setParentElements([]);
    }
  };

  // Get templates based on category
  const getCategoryTemplates = (catId, category) => {
    if (!category) return [];

    const categoryName = category.name?.toLowerCase() || '';

    // Default templates
    const defaultTemplates = [
      {
        name: "Basic Element",
        description: "A simple element with name and description",
        fields: [
          { name: "name", label: "Name", type: "text", placeholder: "Element name" },
          { name: "description", label: "Description", type: "textarea", placeholder: "Brief description" }
        ]
      }
    ];

    // Category-specific templates
    const specificTemplates = {
      'cat_metaphysical_superpowers': [
        {
          name: "Superpower",
          description: "An extraordinary ability or power",
          fields: [
            { name: "name", label: "Name", type: "text", placeholder: "Power name" },
            { name: "ability", label: "Ability", type: "textarea", placeholder: "What the user can do with this power" },
            { name: "activation", label: "Activation Method", type: "text", placeholder: "How the power is activated" },
            { name: "limitations", label: "Limitations", type: "textarea", placeholder: "Limitations of this power" },
            { name: "side_effects", label: "Side Effects", type: "textarea", placeholder: "Any side effects from using this power" },
            { name: "description", label: "Description", type: "textarea", placeholder: "General description" }
          ]
        }
      ],
      'cat_physical_aliens': [
        {
          name: "Alien Species",
          description: "A non-human intelligent species",
          fields: [
            { name: "name", label: "Name", type: "text", placeholder: "Species name" },
            { name: "appearance", label: "Appearance", type: "textarea", placeholder: "Physical characteristics" },
            { name: "biology", label: "Biology", type: "textarea", placeholder: "Biological traits and adaptations" },
            { name: "habitat", label: "Habitat", type: "text", placeholder: "Native environment" },
            { name: "society", label: "Society", type: "textarea", placeholder: "Social structure and culture" },
            { name: "abilities", label: "Special Abilities", type: "textarea", placeholder: "Unique capabilities" },
            { name: "description", label: "Description", type: "textarea", placeholder: "General description" }
          ]
        }
      ],
      'cat_physical_monsters': [
        {
          name: "Monster/Creature",
          description: "A supernatural or magical creature",
          fields: [
            { name: "name", label: "Name", type: "text", placeholder: "Creature name" },
            { name: "appearance", label: "Appearance", type: "textarea", placeholder: "Physical appearance" },
            { name: "behavior", label: "Behavior", type: "textarea", placeholder: "How it behaves" },
            { name: "habitat", label: "Habitat", type: "text", placeholder: "Where it lives" },
            { name: "abilities", label: "Abilities", type: "textarea", placeholder: "Special abilities" },
            { name: "weaknesses", label: "Weaknesses", type: "textarea", placeholder: "Vulnerabilities" },
            { name: "description", label: "Description", type: "textarea", placeholder: "General description" }
          ]
        }
      ],
      'cat_technological_scifi': [
        {
          name: "Advanced Technology",
          description: "Futuristic and advanced technological systems",
          fields: [
            { name: "name", label: "Name", type: "text", placeholder: "Technology name" },
            { name: "function", label: "Function", type: "textarea", placeholder: "What it does" },
            { name: "components", label: "Components", type: "textarea", placeholder: "Key components and systems" },
            { name: "power_source", label: "Power Source", type: "text", placeholder: "How it's powered" },
            { name: "limitations", label: "Limitations", type: "textarea", placeholder: "Technological limitations" },
            { name: "impact", label: "Societal Impact", type: "textarea", placeholder: "How it affects society" },
            { name: "description", label: "Description", type: "textarea", placeholder: "General description" }
          ]
        }
      ],
      'cat_metaphysical_cosmology': [
        {
          name: "Cosmic Structure",
          description: "A cosmic structure or celestial system",
          fields: [
            { name: "name", label: "Name", type: "text", placeholder: "Structure name" },
            { name: "structure_type", label: "Structure Type", type: "text", placeholder: "Universe, dimension, galaxy, etc." },
            { name: "physical_laws", label: "Physical Laws", type: "textarea", placeholder: "Laws of physics that govern this structure" },
            { name: "origin", label: "Origin/Creation", type: "textarea", placeholder: "How it was created or formed" },
            { name: "notable_features", label: "Notable Features", type: "textarea", placeholder: "Distinctive characteristics" },
            { name: "inhabitants", label: "Inhabitants", type: "textarea", placeholder: "Beings or entities that inhabit this structure" },
            { name: "description", label: "Description", type: "textarea", placeholder: "General description" }
          ]
        },
        {
          name: "Celestial Body",
          description: "A star, planet, moon, or other celestial object",
          fields: [
            { name: "name", label: "Name", type: "text", placeholder: "Body name" },
            { name: "body_type", label: "Body Type", type: "text", placeholder: "Star, planet, moon, etc." },
            { name: "composition", label: "Composition", type: "textarea", placeholder: "What it's made of" },
            { name: "orbit", label: "Orbit/Movement", type: "textarea", placeholder: "How it moves through space" },
            { name: "special_properties", label: "Special Properties", type: "textarea", placeholder: "Unique characteristics" },
            { name: "significance", label: "Cultural Significance", type: "textarea", placeholder: "Importance to inhabitants" },
            { name: "description", label: "Description", type: "textarea", placeholder: "General description" }
          ]
        },
        {
          name: "Cosmic Phenomenon",
          description: "An unusual cosmic event or phenomenon",
          fields: [
            { name: "name", label: "Name", type: "text", placeholder: "Phenomenon name" },
            { name: "phenomenon_type", label: "Phenomenon Type", type: "text", placeholder: "Energy, temporal, spatial, etc." },
            { name: "cause", label: "Cause/Origin", type: "textarea", placeholder: "What causes this phenomenon" },
            { name: "effects", label: "Effects", type: "textarea", placeholder: "What effects it has" },
            { name: "frequency", label: "Frequency/Duration", type: "text", placeholder: "How often it occurs and how long it lasts" },
            { name: "detection", label: "Detection Methods", type: "textarea", placeholder: "How it can be detected or observed" },
            { name: "description", label: "Description", type: "textarea", placeholder: "General description" }
          ]
        }
      ],
      'cat_technological_infrastructure': [
        {
          name: "Infrastructure System",
          description: "A system that supports society's functioning",
          fields: [
            { name: "name", label: "Name", type: "text", placeholder: "System name" },
            { name: "system_type", label: "System Type", type: "text", placeholder: "Transportation, energy, water, etc." },
            { name: "coverage", label: "Coverage Area", type: "text", placeholder: "Area covered by this system" },
            { name: "technology_level", label: "Technology Level", type: "text", placeholder: "Level of technological advancement" },
            { name: "maintenance", label: "Maintenance", type: "textarea", placeholder: "How it's maintained and by whom" },
            { name: "access", label: "Access/Availability", type: "textarea", placeholder: "Who has access and how available it is" },
            { name: "vulnerabilities", label: "Vulnerabilities", type: "textarea", placeholder: "Weaknesses or vulnerabilities" },
            { name: "description", label: "Description", type: "textarea", placeholder: "General description" }
          ]
        },
        {
          name: "Facility",
          description: "A building or installation with specific function",
          fields: [
            { name: "name", label: "Name", type: "text", placeholder: "Facility name" },
            { name: "facility_type", label: "Facility Type", type: "text", placeholder: "Power plant, water treatment, etc." },
            { name: "location", label: "Location", type: "text", placeholder: "Where it's located" },
            { name: "capacity", label: "Capacity/Output", type: "text", placeholder: "What it produces or processes" },
            { name: "staff", label: "Staff/Personnel", type: "textarea", placeholder: "Who operates this facility" },
            { name: "security", label: "Security Measures", type: "textarea", placeholder: "How it's protected" },
            { name: "special_features", label: "Special Features", type: "textarea", placeholder: "Unique capabilities" },
            { name: "description", label: "Description", type: "textarea", placeholder: "General description" }
          ]
        },
        {
          name: "Route/Network",
          description: "A transportation or communication network",
          fields: [
            { name: "name", label: "Name", type: "text", placeholder: "Network name" },
            { name: "network_type", label: "Network Type", type: "text", placeholder: "Road, rail, data, etc." },
            { name: "coverage", label: "Coverage/Extent", type: "textarea", placeholder: "Areas connected" },
            { name: "capacity", label: "Capacity/Traffic", type: "text", placeholder: "Volume it can handle" },
            { name: "condition", label: "Condition/Quality", type: "text", placeholder: "Current state of repair" },
            { name: "access_restrictions", label: "Access Restrictions", type: "textarea", placeholder: "Who can use it" },
            { name: "hazards", label: "Hazards/Challenges", type: "textarea", placeholder: "Dangers when using" },
            { name: "description", label: "Description", type: "textarea", placeholder: "General description" }
          ]
        }
      ],
      'cat_technological_sciences': [
        {
          name: "Field of Study",
          description: "A scientific or academic discipline",
          fields: [
            { name: "name", label: "Name", type: "text", placeholder: "Field name" },
            { name: "field_type", label: "Field Type", type: "text", placeholder: "Natural science, social science, etc." },
            { name: "key_concepts", label: "Key Concepts", type: "textarea", placeholder: "Core principles and ideas" },
            { name: "practitioners", label: "Practitioners", type: "textarea", placeholder: "Who studies this field" },
            { name: "institutions", label: "Institutions", type: "textarea", placeholder: "Organizations dedicated to this field" },
            { name: "applications", label: "Practical Applications", type: "textarea", placeholder: "How knowledge is applied" },
            { name: "controversies", label: "Controversies/Limitations", type: "textarea", placeholder: "Debates or limitations" },
            { name: "description", label: "Description", type: "textarea", placeholder: "General description" }
          ]
        },
        {
          name: "Knowledge Repository",
          description: "A collection or storage of knowledge",
          fields: [
            { name: "name", label: "Name", type: "text", placeholder: "Repository name" },
            { name: "repository_type", label: "Repository Type", type: "text", placeholder: "Library, database, school, etc." },
            { name: "contents", label: "Contents/Collection", type: "textarea", placeholder: "What knowledge is stored" },
            { name: "location", label: "Location", type: "text", placeholder: "Where it's located" },
            { name: "access", label: "Access Restrictions", type: "textarea", placeholder: "Who can access it and how" },
            { name: "organization", label: "Organization System", type: "textarea", placeholder: "How knowledge is organized" },
            { name: "caretakers", label: "Caretakers", type: "textarea", placeholder: "Who maintains it" },
            { name: "description", label: "Description", type: "textarea", placeholder: "General description" }
          ]
        }
      ],
      'cat_social_economics': [
        {
          name: "Economic System",
          description: "A system for production and exchange of goods",
          fields: [
            { name: "name", label: "Name", type: "text", placeholder: "System name" },
            { name: "system_type", label: "System Type", type: "text", placeholder: "Feudal, mercantile, capitalist, etc." },
            { name: "currency", label: "Currency", type: "text", placeholder: "What currency is used" },
            { name: "resources", label: "Key Resources", type: "textarea", placeholder: "Important resources" },
            { name: "wealth_distribution", label: "Wealth Distribution", type: "textarea", placeholder: "How wealth is distributed" },
            { name: "trade_partners", label: "Trade Partners", type: "textarea", placeholder: "Who they trade with" },
            { name: "economic_classes", label: "Economic Classes", type: "textarea", placeholder: "Social classes based on wealth" },
            { name: "description", label: "Description", type: "textarea", placeholder: "General description" }
          ]
        },
        {
          name: "Trade Route",
          description: "A path for commercial exchange",
          fields: [
            { name: "name", label: "Name", type: "text", placeholder: "Route name" },
            { name: "route_type", label: "Route Type", type: "text", placeholder: "Land, sea, air, etc." },
            { name: "goods", label: "Traded Goods", type: "textarea", placeholder: "What is traded" },
            { name: "endpoints", label: "Endpoints", type: "text", placeholder: "Connected locations" },
            { name: "dangers", label: "Dangers", type: "textarea", placeholder: "Risks along the route" },
            { name: "control", label: "Control", type: "text", placeholder: "Who controls the route" },
            { name: "description", label: "Description", type: "textarea", placeholder: "General description" }
          ]
        },
        {
          name: "Market",
          description: "A place or system for buying and selling",
          fields: [
            { name: "name", label: "Name", type: "text", placeholder: "Market name" },
            { name: "market_type", label: "Market Type", type: "text", placeholder: "Local, regional, black market, etc." },
            { name: "location", label: "Location", type: "text", placeholder: "Where it's located" },
            { name: "goods", label: "Available Goods", type: "textarea", placeholder: "What can be bought/sold" },
            { name: "schedule", label: "Schedule", type: "text", placeholder: "When it operates" },
            { name: "regulations", label: "Regulations", type: "textarea", placeholder: "Rules governing the market" },
            { name: "description", label: "Description", type: "textarea", placeholder: "General description" }
          ]
        }
      ],
      'cat_physical_climate': [
        {
          name: "Planetary Climate System",
          description: "A global climate system for an entire world",
          fields: [
            { name: "name", label: "Name", type: "text", placeholder: "Climate system name" },
            { name: "axial_tilt", label: "Axial Tilt", type: "text", placeholder: "What is the axial tilt of the planet? (affects seasons)" },
            { name: "orbital_characteristics", label: "Orbital Characteristics", type: "textarea", placeholder: "Describe the orbital path, year length, and any unusual orbital features" },
            { name: "global_patterns", label: "Global Climate Patterns", type: "textarea", placeholder: "Describe the major global climate patterns and zones" },
            { name: "atmospheric_composition", label: "Atmospheric Composition", type: "textarea", placeholder: "What is the atmosphere composed of?" },
            { name: "number_of_seasons", label: "Number of Seasons", type: "text", placeholder: "How many seasons does this world have?" },
            { name: "global_phenomena", label: "Global Weather Phenomena", type: "textarea", placeholder: "Describe any planet-wide weather events or cycles" },
            { name: "magical_influences", label: "Magical/Supernatural Influences", type: "textarea", placeholder: "Are there any magical or supernatural forces affecting the global climate?" },
            { name: "description", label: "Description", type: "textarea", placeholder: "General description" }
          ]
        },
        {
          name: "Regional Climate System",
          description: "A climate system for a specific region",
          fields: [
            { name: "name", label: "Name", type: "text", placeholder: "Climate name" },
            { name: "parent_system", label: "Parent Climate System", type: "text", placeholder: "What planetary climate system does this region belong to?" },
            { name: "region", label: "Geographic Region", type: "text", placeholder: "What geographic region does this climate system cover?" },
            { name: "climate_type", label: "Climate Type", type: "text", placeholder: "Tropical, temperate, polar, etc." },
            { name: "temperature_range", label: "Temperature Range", type: "text", placeholder: "Typical temperatures" },
            { name: "precipitation", label: "Precipitation", type: "textarea", placeholder: "Rainfall, snowfall, and other precipitation patterns" },
            { name: "seasons", label: "Seasons", type: "textarea", placeholder: "Seasonal patterns" },
            { name: "unique_features", label: "Unique Features", type: "textarea", placeholder: "Unusual aspects" },
            { name: "impact", label: "Impact on Life", type: "textarea", placeholder: "Effects on inhabitants" },
            { name: "affected_locations", label: "Affected Locations", type: "textarea", placeholder: "What specific locations are affected by this climate system?" },
            { name: "description", label: "Description", type: "textarea", placeholder: "General description" }
          ]
        },
        {
          name: "Weather Phenomenon",
          description: "A specific weather event or pattern",
          fields: [
            { name: "name", label: "Name", type: "text", placeholder: "Phenomenon name" },
            { name: "phenomenon_type", label: "Phenomenon Type", type: "text", placeholder: "Storm, wind, precipitation, etc." },
            { name: "parent_climate", label: "Parent Climate", type: "text", placeholder: "Which climate system produces this phenomenon?" },
            { name: "frequency", label: "Frequency", type: "text", placeholder: "How often it occurs" },
            { name: "duration", label: "Duration", type: "text", placeholder: "How long it lasts" },
            { name: "effects", label: "Effects", type: "textarea", placeholder: "Impact on environment and people" },
            { name: "prediction", label: "Prediction Methods", type: "textarea", placeholder: "How it's forecast" },
            { name: "description", label: "Description", type: "textarea", placeholder: "General description" }
          ]
        },
        {
          name: "Season",
          description: "A distinct period in the annual climate cycle",
          fields: [
            { name: "name", label: "Name", type: "text", placeholder: "Season name" },
            { name: "parent_climate", label: "Parent Climate", type: "text", placeholder: "Which climate system does this season belong to?" },
            { name: "duration", label: "Duration", type: "text", placeholder: "How long it lasts" },
            { name: "temperature", label: "Temperature", type: "text", placeholder: "Typical temperatures" },
            { name: "precipitation", label: "Precipitation", type: "text", placeholder: "Rainfall, snow, etc." },
            { name: "cultural_significance", label: "Cultural Significance", type: "textarea", placeholder: "Cultural practices" },
            { name: "ecological_effects", label: "Ecological Effects", type: "textarea", placeholder: "Effects on nature" },
            { name: "description", label: "Description", type: "textarea", placeholder: "General description" }
          ]
        }
      ],
      'cat_physical_locations': [
        {
          name: "City/Town",
          description: "A populated settlement with governance and landmarks",
          fields: [
            { name: "name", label: "Name", type: "text", placeholder: "City name" },
            { name: "population", label: "Population", type: "text", placeholder: "Approximate population" },
            { name: "government", label: "Government", type: "text", placeholder: "Type of government" },
            { name: "economy", label: "Economy", type: "text", placeholder: "Main industries and trade" },
            { name: "description", label: "Description", type: "textarea", placeholder: "Notable features, history, etc." },
            { name: "landmarks", label: "Landmarks", type: "textarea", placeholder: "Important locations within" }
          ]
        },
        {
          name: "Natural Location",
          description: "A geographical feature or natural area",
          fields: [
            { name: "name", label: "Name", type: "text", placeholder: "Location name" },
            { name: "type", label: "Type", type: "text", placeholder: "Mountain, forest, lake, etc." },
            { name: "climate", label: "Climate", type: "text", placeholder: "Weather patterns and seasons" },
            { name: "flora_fauna", label: "Flora & Fauna", type: "text", placeholder: "Notable plants and animals" },
            { name: "description", label: "Description", type: "textarea", placeholder: "Physical characteristics" },
            { name: "significance", label: "Significance", type: "textarea", placeholder: "Cultural or strategic importance" }
          ]
        },
        {
          name: "Building/Structure",
          description: "A specific building or architectural structure",
          fields: [
            { name: "name", label: "Name", type: "text", placeholder: "Structure name" },
            { name: "type", label: "Type", type: "text", placeholder: "Castle, temple, bridge, etc." },
            { name: "age", label: "Age", type: "text", placeholder: "How old is it?" },
            { name: "architecture", label: "Architecture", type: "text", placeholder: "Architectural style and materials" },
            { name: "purpose", label: "Purpose", type: "text", placeholder: "What it's used for" },
            { name: "description", label: "Description", type: "textarea", placeholder: "Physical appearance and details" },
            { name: "history", label: "History", type: "textarea", placeholder: "Notable events and significance" }
          ]
        }
      ],
      'cat_social_cultures': [
        {
          name: "Culture/Society",
          description: "A distinct cultural group with shared customs and values",
          fields: [
            { name: "name", label: "Name", type: "text", placeholder: "Culture name" },
            { name: "location", label: "Location", type: "text", placeholder: "Where they're found" },
            { name: "values", label: "Core Values", type: "textarea", placeholder: "What they value most" },
            { name: "customs", label: "Customs", type: "textarea", placeholder: "Unique traditions and practices" },
            { name: "language", label: "Language", type: "text", placeholder: "Languages spoken" },
            { name: "arts", label: "Arts", type: "text", placeholder: "Music, visual arts, literature" },
            { name: "cuisine", label: "Cuisine", type: "text", placeholder: "Food and dining customs" },
            { name: "description", label: "Description", type: "textarea", placeholder: "General description" }
          ]
        },
        {
          name: "Ethnic Group",
          description: "A specific ethnic group with distinct identity",
          fields: [
            { name: "name", label: "Name", type: "text", placeholder: "Group name" },
            { name: "appearance", label: "Appearance", type: "text", placeholder: "Physical characteristics" },
            { name: "homeland", label: "Homeland", type: "text", placeholder: "Traditional territory" },
            { name: "history", label: "History", type: "textarea", placeholder: "Origins and key historical events" },
            { name: "relations", label: "Relations", type: "text", placeholder: "Relations with other groups" },
            { name: "description", label: "Description", type: "textarea", placeholder: "General description" }
          ]
        }
      ],
      'cat_social_factions': [
        {
          name: "Organization/Faction",
          description: "A formal or informal group with shared goals",
          fields: [
            { name: "name", label: "Name", type: "text", placeholder: "Organization name" },
            { name: "type", label: "Type", type: "text", placeholder: "Guild, secret society, military order, etc." },
            { name: "leader", label: "Leadership", type: "text", placeholder: "Leader or leadership structure" },
            { name: "headquarters", label: "Headquarters", type: "text", placeholder: "Base of operations" },
            { name: "membership", label: "Membership", type: "text", placeholder: "Who can join and how" },
            { name: "resources", label: "Resources", type: "text", placeholder: "Wealth, influence, and assets" },
            { name: "goals", label: "Goals", type: "textarea", placeholder: "Primary objectives" },
            { name: "methods", label: "Methods", type: "textarea", placeholder: "How they operate" },
            { name: "description", label: "Description", type: "textarea", placeholder: "General description" }
          ]
        },
        {
          name: "Political Party",
          description: "A political organization with specific ideology",
          fields: [
            { name: "name", label: "Name", type: "text", placeholder: "Party name" },
            { name: "ideology", label: "Ideology", type: "text", placeholder: "Political beliefs" },
            { name: "leadership", label: "Leadership", type: "text", placeholder: "Key figures" },
            { name: "supporters", label: "Supporters", type: "text", placeholder: "Base of support" },
            { name: "platform", label: "Platform", type: "textarea", placeholder: "Key policies and positions" },
            { name: "description", label: "Description", type: "textarea", placeholder: "General description" }
          ]
        }
      ],
      'cat_social_governments': [
        {
          name: "Government System",
          description: "A system of governance and authority",
          fields: [
            { name: "name", label: "Name", type: "text", placeholder: "Government name" },
            { name: "type", label: "Type", type: "text", placeholder: "Monarchy, republic, etc." },
            { name: "ruler", label: "Ruler/Leader", type: "text", placeholder: "Current ruler or leadership" },
            { name: "structure", label: "Structure", type: "textarea", placeholder: "Branches and hierarchy" },
            { name: "territory", label: "Territory", type: "text", placeholder: "Areas under control" },
            { name: "military", label: "Military", type: "text", placeholder: "Armed forces" },
            { name: "description", label: "Description", type: "textarea", placeholder: "General description" }
          ]
        }
      ],
      'cat_social_laws': [
        {
          name: "Legal System",
          description: "A system of laws and enforcement",
          fields: [
            { name: "name", label: "Name", type: "text", placeholder: "Legal system name" },
            { name: "jurisdiction", label: "Jurisdiction", type: "text", placeholder: "Where these laws apply" },
            { name: "enforcement", label: "Enforcement", type: "text", placeholder: "How laws are enforced" },
            { name: "courts", label: "Courts", type: "text", placeholder: "Judicial system" },
            { name: "key_laws", label: "Key Laws", type: "textarea", placeholder: "Important or unique laws" },
            { name: "punishments", label: "Punishments", type: "textarea", placeholder: "Common penalties" },
            { name: "description", label: "Description", type: "textarea", placeholder: "General description" }
          ]
        },
        {
          name: "Law/Regulation",
          description: "A specific law or set of regulations",
          fields: [
            { name: "name", label: "Name", type: "text", placeholder: "Law name" },
            { name: "purpose", label: "Purpose", type: "text", placeholder: "Why it exists" },
            { name: "text", label: "Text", type: "textarea", placeholder: "The actual law text" },
            { name: "penalties", label: "Penalties", type: "text", placeholder: "Consequences for breaking" },
            { name: "description", label: "Description", type: "textarea", placeholder: "General description" }
          ]
        }
      ],
      'cat_metaphysical_religion': [
        {
          name: "Religion/Faith",
          description: "A system of spiritual beliefs and practices",
          fields: [
            { name: "name", label: "Name", type: "text", placeholder: "Religion name" },
            { name: "deities", label: "Deities", type: "text", placeholder: "Major gods/goddesses" },
            { name: "practices", label: "Practices", type: "textarea", placeholder: "Rituals, prayers, holidays" },
            { name: "beliefs", label: "Core Beliefs", type: "textarea", placeholder: "Central tenets and beliefs" },
            { name: "clergy", label: "Clergy", type: "text", placeholder: "Religious leaders and hierarchy" },
            { name: "holy_sites", label: "Holy Sites", type: "text", placeholder: "Sacred locations" },
            { name: "texts", label: "Sacred Texts", type: "text", placeholder: "Religious writings" },
            { name: "description", label: "Description", type: "textarea", placeholder: "General description" }
          ]
        },
        {
          name: "Deity/Divine Being",
          description: "A god, goddess, or divine entity",
          fields: [
            { name: "name", label: "Name", type: "text", placeholder: "Deity name" },
            { name: "domain", label: "Domain", type: "text", placeholder: "What they govern (e.g., war, love)" },
            { name: "appearance", label: "Appearance", type: "text", placeholder: "How they're depicted" },
            { name: "symbols", label: "Symbols", type: "text", placeholder: "Associated symbols" },
            { name: "worshippers", label: "Worshippers", type: "text", placeholder: "Who follows them" },
            { name: "mythology", label: "Mythology", type: "textarea", placeholder: "Stories and legends" },
            { name: "description", label: "Description", type: "textarea", placeholder: "General description" }
          ]
        }
      ],
      'cat_technological_tools': [
        {
          name: "Technology/Invention",
          description: "A technological innovation or device",
          fields: [
            { name: "name", label: "Name", type: "text", placeholder: "Technology name" },
            { name: "creator", label: "Creator", type: "text", placeholder: "Who invented it" },
            { name: "function", label: "Function", type: "text", placeholder: "What it does" },
            { name: "materials", label: "Materials", type: "text", placeholder: "What it's made of" },
            { name: "availability", label: "Availability", type: "text", placeholder: "How common or rare" },
            { name: "impact", label: "Impact", type: "textarea", placeholder: "Effect on society" },
            { name: "description", label: "Description", type: "textarea", placeholder: "General description" }
          ]
        },
        {
          name: "Weapon/Armor",
          description: "A weapon, armor, or military technology",
          fields: [
            { name: "name", label: "Name", type: "text", placeholder: "Item name" },
            { name: "type", label: "Type", type: "text", placeholder: "Sword, bow, plate armor, etc." },
            { name: "materials", label: "Materials", type: "text", placeholder: "What it's made of" },
            { name: "effectiveness", label: "Effectiveness", type: "text", placeholder: "Strengths and weaknesses" },
            { name: "users", label: "Users", type: "text", placeholder: "Who typically uses it" },
            { name: "description", label: "Description", type: "textarea", placeholder: "General description" }
          ]
        }
      ]
    };

    // Return category-specific templates if available, otherwise use default
    if (specificTemplates[catId]) {
      return specificTemplates[catId];
    } else if (categoryName.includes('location')) {
      return specificTemplates['cat_physical_locations'];
    } else {
      return defaultTemplates;
    }
  };

  // Get prompt suggestions based on category
  const getCategorySuggestions = (catId, category) => {
    if (!category) return [];

    const categoryName = category.name?.toLowerCase() || '';

    // Default suggestions
    const defaultSuggestions = [
      "Create a detailed and unique element that fits my world's theme",
      "Generate something that would be interesting for my story"
    ];

    // Category-specific suggestions
    const specificSuggestions = {
      'cat_physical_flora_fauna': [
        "Create a realistic animal native to a forest environment",
        "Generate a domesticated animal with unique traits",
        "Create a predatory animal with interesting hunting behaviors",
        "Generate a herd animal with complex social structures",
        "Create an animal that has adapted to an extreme environment"
      ],
      'cat_metaphysical_superpowers': [
        "Create a unique superpower with interesting limitations",
        "Generate a power that evolves or changes with the user's emotional state",
        "Create a superpower that has unexpected side effects",
        "Generate a power that requires specific conditions to activate"
      ],
      'cat_physical_aliens': [
        "Create an alien species adapted to an extreme environment",
        "Generate a non-humanoid alien species with unique biology",
        "Create an alien species with an unusual social structure",
        "Generate a species with unique communication methods"
      ],
      'cat_physical_monsters': [
        "Create a terrifying monster with unique hunting strategies",
        "Generate a creature with magical or supernatural abilities",
        "Create a monster with an unusual weakness or vulnerability",
        "Generate a creature that changes form or evolves"
      ],
      'cat_technological_scifi': [
        "Create advanced technology that interfaces with the human mind",
        "Generate a revolutionary propulsion or energy system",
        "Create a technology with both beneficial and dangerous applications",
        "Generate a device that manipulates fundamental forces of nature"
      ],
      'cat_physical_locations': [
        "Create a mysterious hidden location with unique features",
        "Generate a bustling city with distinct districts and landmarks",
        "Create a dangerous frontier outpost with strategic importance",
        "Generate a sacred site with cultural significance"
      ],
      'cat_physical_geography': [
        "Create a unique geographical feature that affects local climate",
        "Generate a natural wonder that attracts visitors from far away",
        "Create a treacherous mountain pass that's vital for trade",
        "Generate an unusual ecosystem with distinctive flora and fauna"
      ],
      'cat_social_cultures': [
        "Create a culture with unique customs and traditions",
        "Generate a society with an unusual social hierarchy",
        "Create a culture with distinctive art, music, and cuisine",
        "Generate a nomadic people with adaptable survival strategies"
      ],
      'cat_social_factions': [
        "Create a secretive organization with hidden influence",
        "Generate a guild or faction with specific expertise",
        "Create a rebel group with a compelling cause",
        "Generate a powerful family dynasty with complex relationships"
      ],
      'cat_social_governments': [
        "Create a government system with unique checks and balances",
        "Generate a ruling council with competing interests",
        "Create a monarchy with unusual succession rules",
        "Generate a city-state with innovative governance"
      ],
      'cat_social_laws': [
        "Create a set of laws that reflect cultural values",
        "Generate legal traditions with interesting historical origins",
        "Create a controversial law with unintended consequences",
        "Generate a legal system with unique enforcement mechanisms"
      ],
      'cat_metaphysical_magic': [
        "Create a magic system with specific costs and limitations",
        "Generate a rare magical phenomenon with unpredictable effects",
        "Create a school of magic with specialized techniques",
        "Generate magical artifacts with interesting histories"
      ],
      'cat_metaphysical_religion': [
        "Create a religion with unique beliefs and practices",
        "Generate a pantheon of deities with complex relationships",
        "Create a religious order with specific duties and traditions",
        "Generate a set of religious texts with different interpretations"
      ],
      'cat_technological_tools': [
        "Create technology that solves a specific problem",
        "Generate tools unique to a particular profession",
        "Create devices that combine magic and technology",
        "Generate weapons with strategic advantages and disadvantages"
      ],
      'cat_metaphysical_cosmology': [
        "Create a unique cosmic structure with its own physical laws",
        "Generate a celestial body with unusual properties",
        "Create a cosmic phenomenon that affects space and time",
        "Generate a star system with habitable worlds"
      ],
      'cat_technological_infrastructure': [
        "Create an infrastructure system that connects distant regions",
        "Generate a facility that serves a vital societal function",
        "Create a transportation network with unique features",
        "Generate a communication system that works in extreme conditions"
      ],
      'cat_technological_sciences': [
        "Create a field of study unique to your world",
        "Generate a knowledge repository with valuable information",
        "Create a scientific discipline with practical applications",
        "Generate a research institution with specialized focus"
      ],
      'cat_social_economics': [
        "Create an economic system with unique exchange mechanisms",
        "Generate a trade route that connects diverse cultures",
        "Create a market with unusual goods and services",
        "Generate a currency with special properties"
      ],
      'cat_physical_climate': [
        "Create a planetary climate system with unique orbital characteristics",
        "Generate a regional climate system with extreme seasonal variations",
        "Create a weather phenomenon unique to your world",
        "Generate a season with cultural significance",
        "Create a climate system that affects specific geographical regions",
        "Generate a hierarchical climate system with regional variations"
      ]
    };

    // Return category-specific suggestions if available, otherwise use default
    if (specificSuggestions[catId]) {
      return specificSuggestions[catId];
    } else if (categoryName.includes('location')) {
      return specificSuggestions['cat_physical_locations'];
    } else {
      return defaultSuggestions;
    }
  };

  // Apply a suggestion to the prompt
  const applySuggestion = (suggestion) => {
    setPrompt(suggestion);
  };

  // Handle adding the previewed elements to the world
  const handleAddToWorld = () => {
    if (generatedPreview.length > 0 && onElementsGenerated) {
      if (multiLevelMode) {
        // For multi-level mode, we need to handle the hierarchical structure

        // Group elements by level
        const elementsByLevel = {};
        generatedPreview.forEach(element => {
          const level = element._level || 0;
          if (!elementsByLevel[level]) elementsByLevel[level] = [];
          elementsByLevel[level].push(element);
        });

        // Create a special structure to indicate multi-level hierarchy
        const multiLevelStructure = {
          _is_multi_level_structure: true,
          elements: generatedPreview,
          elementsByLevel: elementsByLevel,
          maxLevel: Math.max(...Object.keys(elementsByLevel).map(Number))
        };

        // Pass the multi-level structure to the handler
        onElementsGenerated(multiLevelStructure);
      } else if (nestedMode) {
        // For nested mode, we need to handle parent-child relationships
        // First, find the parent element
        const parentElement = generatedPreview.find(elem => elem.is_parent);
        const subElements = generatedPreview.filter(elem => elem._is_sub_element);

        if (parentElement && subElements.length > 0) {
          // Create a special structure to indicate nested elements
          const nestedStructure = {
            _is_nested_structure: true,
            parent: parentElement,
            children: subElements
          };

          // Pass the nested structure to the handler
          onElementsGenerated(nestedStructure);
        } else {
          // Fallback if structure is incomplete
          onElementsGenerated(generatedPreview);
        }
      } else {
        // Standard handling for non-nested elements
        onElementsGenerated(generatedPreview);
      }

      // Reset state
      setGeneratedPreview([]);
      setShowPreview(false);
      setPrompt('');
    }
  };

  // Handle regenerating elements
  const handleRegenerate = () => {
    setShowPreview(false);
    setGeneratedPreview([]);
    // Keep the prompt, just trigger generation again
    handleGenerate();
  };

  // Handle canceling the preview
  const handleCancelPreview = () => {
    setShowPreview(false);
    setGeneratedPreview([]);
  };

  // Handle prompt change
  const handlePromptChange = (e) => {
    setPrompt(e.target.value);
  };

  // Handle count change
  const handleCountChange = (e) => {
    const value = parseInt(e.target.value, 10);
    setCount(Math.min(Math.max(1, value), 20)); // Ensure count is between 1 and 20
  };

  // Handle generation
  const handleGenerate = async () => {
    if (!prompt.trim()) {
      setError('Please enter a prompt');
      return;
    }

    if (!categoryId) {
      setError('Please select a category');
      return;
    }

    try {
      setIsGenerating(true);
      setError(null);

      // The backend now handles all prompt enhancement, so we just use the original prompt
      // We'll keep the context string for backward compatibility but won't add template instructions

      // Add context from existing elements - include ALL elements without truncation
      const contextElements = categoryElements; // Include all elements, no limit
      let contextString = '';

      if (contextElements.length > 0) {
        contextString = `\n\nConsider the following existing ${categoryInfo?.name || 'elements'} in my world:\n`;
        contextElements.forEach(element => {
          contextString += `- ${element.name}: ${element.description || 'No description'}\n`;
        });
      }

      // Add the context to the prompt
      let enhancedPrompt = prompt + contextString;

      // Add parent element context if a parent is selected (no need for hierarchy mode flag)
      let parentContext = '';
      let selectedParent = null;

      if (selectedParentId && !multiLevelMode) { // Multi-level mode handles parent context separately
        selectedParent = parentElements.find(elem => elem.element_id === selectedParentId);
        if (selectedParent) {
          parentContext = `\n\nCreate these elements as sub-elements or children of the following parent element:\n` +
            `Parent: ${selectedParent.name}\n` +
            `Description: ${selectedParent.description || 'No description'}\n` +
            `Type: ${selectedParent.element_type || 'generic'}\n\n` +
            `The generated elements should be logically related to this parent and make sense as its sub-components, parts, or children.`;
        }
      }

      // Add appropriate mode enhancements
      if (multiLevelMode) {
        // Multi-level mode: generate a complex hierarchical structure

        // Start with base prompt
        let basePrompt = '';

        // If we have a selected parent
        if (selectedParentId) {
          const parentElement = parentElements.find(e => e.element_id === selectedParentId);

          basePrompt = `Generate a hierarchical structure under the existing parent "${parentElement.name}" (${parentElement.element_type || 'generic'}).

Parent context: ${parentElement.description || 'No description available'}

IMPORTANT: All generated elements should be created as sub-elements or children of this parent element.
The hierarchical structure should start with elements that are direct children of "${parentElement.name}".

`;
        } else {
          basePrompt = `Generate a new hierarchical structure from scratch.

`;
        }

        // Add nesting depth instructions
        if (nestingDepth === 1) {
          basePrompt += `Create ${level1Count} elements that form a coherent group.`;
        } else if (nestingDepth === 2) {
          basePrompt += `Create a 2-level hierarchy with:
- ${level1Count} top-level elements that form a coherent group
- Each top-level element should have ${level2Count} sub-elements that relate specifically to their parent

All elements should be logically connected and form a coherent structure.`;
        } else if (nestingDepth === 3) {
          basePrompt += `Create a deep 3-level hierarchy with:
- ${level1Count} top-level elements that form a coherent group
- Each top-level element should have ${level2Count} sub-elements
- Each sub-element should have ${level3Count} sub-sub-elements

All elements should be logically connected and form a coherent structure.`;
        }

        // Add domain-specific instructions
        if (categoryInfo?.name?.toLowerCase().includes('planet') || categoryInfo?.name?.toLowerCase().includes('location')) {
          basePrompt += `\n\nFor locations, consider geographical features, climate, inhabitants, and strategic importance.`;
        } else if (categoryInfo?.name?.toLowerCase().includes('technology') && !categoryInfo?.name?.toLowerCase().includes('infrastructure') && !categoryInfo?.name?.toLowerCase().includes('sciences')) {
          basePrompt += `\n\nFor technology, consider components, power sources, interfaces, limitations, and how they integrate with other systems.`;
        } else if (categoryInfo?.name?.toLowerCase().includes('cosmos') || categoryInfo?.name?.toLowerCase().includes('cosmology')) {
          basePrompt += `\n\nFor cosmic structures, consider physical laws, scale, origin, notable features, and inhabitants. For celestial bodies, consider composition, orbit, special properties, and significance. For cosmic phenomena, consider causes, effects, frequency, and detection methods.`;
        } else if (categoryInfo?.name?.toLowerCase().includes('infrastructure')) {
          basePrompt += `\n\nFor infrastructure systems, consider coverage area, technology level, maintenance, access, and vulnerabilities. For facilities, consider location, capacity, staff, security, and special features. For routes and networks, consider coverage, capacity, condition, access restrictions, and hazards.`;
        } else if (categoryInfo?.name?.toLowerCase().includes('science') || categoryInfo?.name?.toLowerCase().includes('knowledge')) {
          basePrompt += `\n\nFor fields of study, consider key concepts, practitioners, institutions, applications, and controversies. For knowledge repositories, consider contents, location, access, organization system, and caretakers. For advanced technologies, consider function, development, availability, limitations, and societal impact.`;
        } else if (categoryInfo?.name?.toLowerCase().includes('economic') || categoryInfo?.name?.toLowerCase().includes('trade')) {
          basePrompt += `\n\nFor economic systems, consider system type, currency, resources, wealth distribution, trade partners, and economic classes. For trade routes, consider route type, goods, endpoints, dangers, and control. For markets, consider market type, location, goods, schedule, and regulations.`;
        } else if (categoryInfo?.name?.toLowerCase().includes('climate') || categoryInfo?.name?.toLowerCase().includes('weather')) {
          basePrompt += `\n\nFor planetary climate systems, consider axial tilt, orbital characteristics, global patterns, atmospheric composition, number of seasons, global phenomena, and magical influences. For regional climate systems, consider climate type, temperature range, precipitation, seasons, unique features, and impact on life. For weather phenomena, consider phenomenon type, frequency, duration, effects, and prediction methods. For seasons, consider duration, temperature, precipitation, cultural significance, and ecological effects.`;
        } else if (categoryInfo?.name?.toLowerCase().includes('magic')) {
          // Check if the prompt specifically asks for spells, magical powers, or artifacts
          const promptLower = prompt.toLowerCase();

          // Reset the detectedTypes array
          detectedTypes = [];

          // Initialize counters for each type
          let spellCount = 0;
          let powerCount = 0;
          let artifactCount = 0;

          // Check for specific counts in the prompt using regex
          const spellMatch = promptLower.match(/(\d+)\s+(spell|spells)/);
          const powerMatch = promptLower.match(/(\d+)\s+(power|powers|magical power|magical powers)/);
          const artifactMatch = promptLower.match(/(\d+)\s+(artifact|artifacts|magical artifact|magical artifacts)/);

          // Extract counts if found
          if (spellMatch) {
            spellCount = parseInt(spellMatch[1]);
            detectedTypes.push('spell');
            console.log(`Detected request for ${spellCount} spells`);
          } else if (promptLower.includes('spell') || promptLower.includes('spells')) {
            detectedTypes.push('spell');
          }

          if (powerMatch) {
            powerCount = parseInt(powerMatch[1]);
            detectedTypes.push('magical_power');
            console.log(`Detected request for ${powerCount} magical powers`);
          } else if (promptLower.includes('power') || promptLower.includes('powers') ||
              promptLower.includes('ability') || promptLower.includes('abilities')) {
            detectedTypes.push('magical_power');
          }

          if (artifactMatch) {
            artifactCount = parseInt(artifactMatch[1]);
            detectedTypes.push('magical_artifact');
            console.log(`Detected request for ${artifactCount} artifacts`);
          } else if (promptLower.includes('artifact') || promptLower.includes('artifacts') ||
              promptLower.includes('item') || promptLower.includes('items')) {
            detectedTypes.push('magical_artifact');
          }

          // If no specific types detected, default to magic system
          if (detectedTypes.length === 0) {
            detectedTypes.push('magic_system');
          }

          // Store the detected types for later use
          // Using the state setter from useState
          setDetectedMagicTypes(detectedTypes);

          // If only one type is detected, use that as the primary type
          if (detectedTypes.length === 1) {
            setDetectedMagicType(detectedTypes[0]);
          } else {
            // If multiple types are detected, set a special "mixed" type
            setDetectedMagicType('mixed');
          }

          // Add specific instructions about the number of each type
          if (spellCount > 0 || powerCount > 0 || artifactCount > 0) {
            basePrompt += `\n\nIMPORTANT: Generate exactly:`;
            if (spellCount > 0) {
              basePrompt += `\n- ${spellCount} spell${spellCount > 1 ? 's' : ''}`;
            }
            if (powerCount > 0) {
              basePrompt += `\n- ${powerCount} magical power${powerCount > 1 ? 's' : ''}`;
            }
            if (artifactCount > 0) {
              basePrompt += `\n- ${artifactCount} magical artifact${artifactCount > 1 ? 's' : ''}`;
            }
            basePrompt += `\n\nEnsure your response includes exactly these numbers of each type.`;
          }

          // Add appropriate instructions based on detected types
          if (detectedTypes.includes('spell')) {
            basePrompt += `\n\nGenerate spells with detailed information about their effects, casting time, components, duration, range, power cost, difficulty level, and any side effects or visual manifestations.`;
          }

          if (detectedTypes.includes('magical_power')) {
            basePrompt += `\n\nGenerate magical powers with detailed information about their abilities, activation methods, power sources, limitations, and mastery levels.`;
          }

          if (detectedTypes.includes('magical_artifact')) {
            basePrompt += `\n\nGenerate magical artifacts with detailed information about their origins, powers, activation methods, appearance, and limitations.`;
          }

          if (detectedTypes.includes('magic_system')) {
            basePrompt += `\n\nFor magic systems, include detailed information about the source of magic, energy type, acquisition method, rules, limitations, cost, practitioners, and rarity.`;
          }
        }

        // Add the user's prompt and context
        enhancedPrompt = `${basePrompt}\n\n${prompt}${contextString}`;

        // Add format instructions
        if (categoryInfo?.name?.toLowerCase().includes('cosmos') || categoryInfo?.name?.toLowerCase().includes('cosmology')) {
          // Format for cosmic structures
          enhancedPrompt += `\n\nReturn the results as a JSON object with this nested structure:
{
  "elements": [
    {
      "name": "Cosmic Structure Name",
      "description": "Detailed description of the cosmic structure",
      "element_type": "cosmic_structure",
      "custom_fields": {
        "structure_type": "Universe, dimension, galaxy, solar system, etc.",
        "physical_laws": "Laws of physics that govern this structure",
        "origin": "How it was created or formed",
        "age": "Age or timeline of this structure",
        "notable_features": "Distinctive characteristics",
        "inhabitants": "Beings or entities that inhabit this structure"
      },
      "children": [
        {
          "name": "Celestial Body Name",
          "description": "Description of this celestial body",
          "element_type": "celestial_body",
          "custom_fields": {
            "body_type": "Star, planet, moon, asteroid, black hole, etc.",
            "composition": "What it's made of",
            "orbit": "How it moves through space",
            "size": "Size and mass information",
            "special_properties": "Unique characteristics",
            "significance": "Importance to inhabitants"
          }
        },
        {
          "name": "Cosmic Phenomenon Name",
          "description": "Description of this cosmic phenomenon",
          "element_type": "cosmic_phenomenon",
          "custom_fields": {
            "phenomenon_type": "Energy, temporal, spatial, gravitational, etc.",
            "cause": "What causes this phenomenon",
            "effects": "What effects it has",
            "frequency": "How often it occurs and how long it lasts",
            "detection": "How it can be detected or observed"
          }
        }
      ]
    }
  ]
}`;
        } else if (categoryInfo?.name?.toLowerCase().includes('infrastructure')) {
          // Format for infrastructure systems
          enhancedPrompt += `\n\nReturn the results as a JSON object with this nested structure:
{
  "elements": [
    {
      "name": "Infrastructure System Name",
      "description": "Detailed description of the infrastructure system",
      "element_type": "infrastructure_system",
      "custom_fields": {
        "system_type": "Transportation, energy, water, communication, etc.",
        "coverage": "Area covered by this system",
        "technology_level": "Level of technological advancement",
        "maintenance": "How it's maintained and by whom",
        "access": "Who has access and how available it is",
        "vulnerabilities": "Weaknesses or vulnerabilities"
      },
      "children": [
        {
          "name": "Facility Name",
          "description": "Description of this facility",
          "element_type": "facility",
          "custom_fields": {
            "facility_type": "Power plant, water treatment, etc.",
            "location": "Where it's located",
            "capacity": "What it produces or processes",
            "staff": "Who operates this facility",
            "security": "How it's protected",
            "special_features": "Unique capabilities"
          }
        },
        {
          "name": "Route/Network Name",
          "description": "Description of this route or network",
          "element_type": "route_network",
          "custom_fields": {
            "network_type": "Road, rail, data, etc.",
            "coverage": "Areas connected",
            "capacity": "Volume it can handle",
            "condition": "Current state of repair",
            "access_restrictions": "Who can use it",
            "hazards": "Dangers when using"
          }
        }
      ]
    }
  ]
}`;
        } else if (categoryInfo?.name?.toLowerCase().includes('science') || categoryInfo?.name?.toLowerCase().includes('knowledge')) {
          // Format for fields of study
          enhancedPrompt += `\n\nReturn the results as a JSON object with this nested structure:
{
  "elements": [
    {
      "name": "Field of Study Name",
      "description": "Detailed description of the field of study",
      "element_type": "field_of_study",
      "custom_fields": {
        "field_type": "Natural science, social science, formal science, etc.",
        "key_concepts": "Core principles and ideas",
        "practitioners": "Who studies this field",
        "institutions": "Organizations dedicated to this field",
        "applications": "How knowledge is applied",
        "controversies": "Debates or limitations"
      },
      "children": [
        {
          "name": "Advanced Technology Name",
          "description": "Description of this technology",
          "element_type": "advanced_technology",
          "custom_fields": {
            "tech_type": "Tool, weapon, transportation, communication, etc.",
            "function": "What it does",
            "development": "How it was developed",
            "availability": "How common or rare it is",
            "limitations": "Drawbacks or limitations",
            "impact": "How it affects society"
          }
        },
        {
          "name": "Knowledge Repository Name",
          "description": "Description of this repository",
          "element_type": "knowledge_repository",
          "custom_fields": {
            "repository_type": "Library, database, school, etc.",
            "contents": "What knowledge is stored",
            "location": "Where it's located",
            "access": "Who can access it and how",
            "organization": "How knowledge is organized",
            "caretakers": "Who maintains it"
          }
        }
      ]
    }
  ]
}`;
        } else if (categoryInfo?.name?.toLowerCase().includes('economic') || categoryInfo?.name?.toLowerCase().includes('trade')) {
          // Format for economic systems
          enhancedPrompt += `\n\nReturn the results as a JSON object with this nested structure:
{
  "elements": [
    {
      "name": "Economic System Name",
      "description": "Detailed description of the economic system",
      "element_type": "economic_system",
      "custom_fields": {
        "system_type": "Feudal, mercantile, capitalist, etc.",
        "currency": "What currency is used",
        "resources": "Important resources",
        "wealth_distribution": "How wealth is distributed",
        "trade_partners": "Who they trade with",
        "economic_classes": "Social classes based on wealth"
      },
      "children": [
        {
          "name": "Trade Route Name",
          "description": "Description of this trade route",
          "element_type": "trade_route",
          "custom_fields": {
            "route_type": "Land, sea, air, etc.",
            "goods": "What is traded",
            "endpoints": "Connected locations",
            "dangers": "Risks along the route",
            "control": "Who controls the route"
          }
        },
        {
          "name": "Market Name",
          "description": "Description of this market",
          "element_type": "market",
          "custom_fields": {
            "market_type": "Local, regional, black market, etc.",
            "location": "Where it's located",
            "goods": "What can be bought/sold",
            "schedule": "When it operates",
            "regulations": "Rules governing the market"
          }
        }
      ]
    }
  ]
}`;
        } else if (categoryInfo?.name?.toLowerCase().includes('animal') || (categoryInfo?.name?.toLowerCase().includes('flora') && categoryInfo?.name?.toLowerCase().includes('fauna'))) {
          // Format for animals
          enhancedPrompt += `\n\nReturn the results as a JSON object with this nested structure:
{
  "elements": [
    {
      "name": "Animal Name",
      "description": "Detailed description of this animal",
      "element_type": "animal",
      "custom_fields": {
        "animal_type": "Mammal, bird, reptile, etc.",
        "habitat": "Where this animal lives",
        "appearance": "Physical characteristics",
        "behavior": "Behavioral patterns",
        "diet": "What this animal eats",
        "lifecycle": "Reproduction, lifespan, etc."
      }
    },
    ... additional animals ...
  ]
}`;
        } else if (categoryInfo?.name?.toLowerCase().includes('climate') || categoryInfo?.name?.toLowerCase().includes('weather')) {
          // Format for climate systems
          enhancedPrompt += `\n\nReturn the results as a JSON object with this nested structure:
{
  "elements": [
    {
      "name": "Planetary Climate System Name",
      "description": "Detailed description of the global climate system",
      "element_type": "planetary_climate_system",
      "custom_fields": {
        "axial_tilt": "What is the axial tilt of the planet? (affects seasons)",
        "orbital_characteristics": "Describe the orbital path, year length, and any unusual orbital features",
        "global_patterns": "Describe the major global climate patterns and zones",
        "atmospheric_composition": "What is the atmosphere composed of?",
        "number_of_seasons": "How many seasons does this world have?",
        "global_phenomena": "Describe any planet-wide weather events or cycles",
        "magical_influences": "Are there any magical or supernatural forces affecting the global climate?"
      },
      "children": [
        {
          "name": "Regional Climate System Name",
          "description": "Description of this regional climate system",
          "element_type": "climate_system",
          "custom_fields": {
            "parent_system": "What planetary climate system does this region belong to?",
            "region": "What geographic region does this climate system cover?",
            "climate_type": "Tropical, temperate, polar, etc.",
            "temperature_range": "Typical temperatures",
            "precipitation": "Rainfall, snowfall, and other precipitation patterns",
            "seasons": "Seasonal patterns",
            "unique_features": "Unusual aspects",
            "impact": "Effects on inhabitants",
            "affected_locations": "What specific locations are affected by this climate system?"
          },
          "children": [
            {
              "name": "Weather Phenomenon Name",
              "description": "Description of this weather phenomenon",
              "element_type": "weather_phenomenon",
              "custom_fields": {
                "phenomenon_type": "Storm, wind, precipitation, etc.",
                "parent_climate": "Which climate system produces this phenomenon?",
                "frequency": "How often it occurs",
                "duration": "How long it lasts",
                "effects": "Impact on environment and people",
                "prediction": "How it's forecast"
              }
            },
            {
              "name": "Season Name",
              "description": "Description of this season",
              "element_type": "season",
              "custom_fields": {
                "parent_climate": "Which climate system does this season belong to?",
                "duration": "How long it lasts",
                "temperature": "Typical temperatures",
                "precipitation": "Rainfall, snow, etc.",
                "cultural_significance": "Cultural practices",
                "ecological_effects": "Effects on nature"
              }
            }
          ]
        }
      ]
    }
  ]
}`;
        } else if (categoryInfo?.name?.toLowerCase().includes('magic')) {
          // Special format for magic-related elements with custom fields
          if (detectedMagicType === 'mixed') {
            // Format for mixed types (multiple types detected)
            enhancedPrompt += `\n\nReturn the results as a JSON object with this nested structure:
{
  "elements": [`;

            // Add examples for each detected type
            let exampleCount = 0;

            if (detectedTypes.includes('spell')) {
              if (exampleCount > 0) enhancedPrompt += ',';
              enhancedPrompt += `
    {
      "name": "Spell Name",
      "description": "Detailed description of what the spell does and its effects",
      "element_type": "spell",
      "custom_fields": {
        "effect": "What the spell does when cast",
        "casting_time": "How long it takes to cast",
        "components": "Required components (verbal, somatic, material)",
        "duration": "How long the effect lasts",
        "range": "The range of the spell",
        "power_cost": "Magical energy required",
        "difficulty": "One of: novice, apprentice, adept, expert, master, legendary",
        "side_effects": "Any side effects or consequences",
        "visual_effect": "What the spell looks like when cast"
      }
    }`;
              exampleCount++;
            }

            if (detectedTypes.includes('magical_power')) {
              if (exampleCount > 0) enhancedPrompt += ',';
              enhancedPrompt += `
    {
      "name": "Magical Power Name",
      "description": "Detailed description of the magical power",
      "element_type": "magical_power",
      "custom_fields": {
        "ability": "What the user can do with this power",
        "activation": "How the power is activated",
        "power_source": "Specific source that powers this ability",
        "limitations": "Limitations of this power",
        "mastery_levels": "Different levels of mastery for this power",
        "side_effects": "Any side effects from using this power",
        "manifestation": "How the power visibly manifests"
      }
    }`;
              exampleCount++;
            }

            if (detectedTypes.includes('magical_artifact')) {
              if (exampleCount > 0) enhancedPrompt += ',';
              enhancedPrompt += `
    {
      "name": "Magical Artifact Name",
      "description": "Detailed description of the magical artifact",
      "element_type": "magical_artifact",
      "custom_fields": {
        "origin": "Where the artifact came from and who created it",
        "powers": "Magical powers the artifact possesses",
        "activation": "How the artifact is activated",
        "appearance": "What the artifact looks like",
        "limitations": "Limitations of the artifact",
        "materials": "What the artifact is made of",
        "attunement": "Requirements to attune to or use the artifact",
        "history": "Notable historical events involving the artifact"
      }
    }`;
              exampleCount++;
            }

            if (detectedTypes.includes('magic_system')) {
              if (exampleCount > 0) enhancedPrompt += ',';
              enhancedPrompt += `
    {
      "name": "Magic System Name",
      "description": "Detailed description of the magic system",
      "element_type": "magic_system",
      "custom_fields": {
        "source": "Where the magic comes from",
        "energy_type": "One of: mana, life_force, elemental, divine, cosmic, soul, blood, nature, other",
        "acquisition": "One of: innate, learned, granted, inherited, artifact, ritual, mixed",
        "rules": "Core rules of the magic system",
        "limitations": "Limitations of the magic",
        "cost": "What price users pay to use this magic",
        "practitioners": "Who can use this magic",
        "rarity": "One of: common, uncommon, rare, very_rare, unique"
      }
    }`;
            }

            // Close the elements array
            enhancedPrompt += `
  ]
}`;

            // Add instructions for distributing elements
            enhancedPrompt += `\n\nIMPORTANT: Distribute the elements among the detected types (${detectedTypes.join(', ')}) based on the user's prompt. Each element should have the appropriate element_type and custom_fields for its type.`;

          } else if (detectedMagicType === 'spell') {
            // Format for spells
            enhancedPrompt += `\n\nReturn the results as a JSON object with this nested structure:
{
  "elements": [
    {
      "name": "Spell Name",
      "description": "Detailed description of what the spell does and its effects",
      "element_type": "spell",
      "custom_fields": {
        "effect": "What the spell does when cast",
        "casting_time": "How long it takes to cast",
        "components": "Required components (verbal, somatic, material)",
        "duration": "How long the effect lasts",
        "range": "The range of the spell",
        "power_cost": "Magical energy required",
        "difficulty": "One of: novice, apprentice, adept, expert, master, legendary",
        "side_effects": "Any side effects or consequences",
        "visual_effect": "What the spell looks like when cast"
      },
      "children": [
        {
          "name": "Spell Variant",
          "description": "A variation of the parent spell",
          "element_type": "spell",
          "custom_fields": {
            "effect": "How this variant differs from the base spell",
            "casting_time": "Casting time for this variant",
            "components": "Components for this variant",
            "duration": "Duration for this variant",
            "power_cost": "Power cost for this variant",
            "difficulty": "Difficulty level for this variant"
          }
        }
      ]
    }
  ]
}`;
          } else if (detectedMagicType === 'magical_power') {
            // Format for magical powers
            enhancedPrompt += `\n\nReturn the results as a JSON object with this nested structure:
{
  "elements": [
    {
      "name": "Magical Power Name",
      "description": "Detailed description of the magical power",
      "element_type": "magical_power",
      "custom_fields": {
        "ability": "What the user can do with this power",
        "activation": "How the power is activated",
        "power_source": "Specific source that powers this ability",
        "limitations": "Limitations of this power",
        "mastery_levels": "Different levels of mastery for this power",
        "side_effects": "Any side effects from using this power",
        "manifestation": "How the power visibly manifests"
      },
      "children": [
        {
          "name": "Power Variant",
          "description": "A variation or advanced form of the parent power",
          "element_type": "magical_power",
          "custom_fields": {
            "ability": "How this variant differs from the base power",
            "activation": "Activation method for this variant",
            "limitations": "Specific limitations for this variant",
            "mastery_level": "Required mastery level to use this variant"
          }
        }
      ]
    }
  ]
}`;
          } else if (detectedMagicType === 'magical_artifact') {
            // Format for magical artifacts
            enhancedPrompt += `\n\nReturn the results as a JSON object with this nested structure:
{
  "elements": [
    {
      "name": "Magical Artifact Name",
      "description": "Detailed description of the magical artifact",
      "element_type": "magical_artifact",
      "custom_fields": {
        "origin": "Where the artifact came from and who created it",
        "powers": "Magical powers the artifact possesses",
        "activation": "How the artifact is activated",
        "appearance": "What the artifact looks like",
        "limitations": "Limitations of the artifact",
        "materials": "What the artifact is made of",
        "attunement": "Requirements to attune to or use the artifact",
        "history": "Notable historical events involving the artifact"
      },
      "children": [
        {
          "name": "Related Artifact",
          "description": "An artifact related to the parent artifact",
          "element_type": "magical_artifact",
          "custom_fields": {
            "powers": "Powers of this related artifact",
            "activation": "How this artifact is activated",
            "appearance": "What this artifact looks like",
            "relationship": "How it relates to the parent artifact"
          }
        }
      ]
    }
  ]
}`;
          } else {
            // Default format for magic systems
            enhancedPrompt += `\n\nReturn the results as a JSON object with this nested structure:
{
  "elements": [
    {
      "name": "Magic System Name",
      "description": "Detailed description of the magic system",
      "element_type": "magic_system",
      "custom_fields": {
        "source": "Where the magic comes from",
        "energy_type": "One of: mana, life_force, elemental, divine, cosmic, soul, blood, nature, other",
        "acquisition": "One of: innate, learned, granted, inherited, artifact, ritual, mixed",
        "rules": "Core rules of the magic system",
        "limitations": "Limitations of the magic",
        "cost": "What price users pay to use this magic",
        "practitioners": "Who can use this magic",
        "rarity": "One of: common, uncommon, rare, very_rare, unique"
      },
      "children": [
        {
          "name": "Spell Name",
          "description": "Description of a spell in this magic system",
          "element_type": "spell",
          "custom_fields": {
            "effect": "What the spell does when cast",
            "casting_time": "How long it takes to cast",
            "components": "Required components (verbal, somatic, material)",
            "duration": "How long the effect lasts",
            "range": "The range of the spell",
            "power_cost": "Magical energy required",
            "difficulty": "One of: novice, apprentice, adept, expert, master, legendary"
          }
        },
        {
          "name": "Magical Power Name",
          "description": "Description of a magical power in this system",
          "element_type": "magical_power",
          "custom_fields": {
            "ability": "What the user can do with this power",
            "activation": "How the power is activated",
            "power_source": "Specific source that powers this ability",
            "limitations": "Limitations of this power",
            "mastery_levels": "Different levels of mastery for this power"
          }
        },
        {
          "name": "Magical Artifact Name",
          "description": "Description of a magical artifact in this system",
          "element_type": "magical_artifact",
          "custom_fields": {
            "origin": "Where the artifact came from and who created it",
            "powers": "Magical powers the artifact possesses",
            "activation": "How the artifact is activated",
            "appearance": "What the artifact looks like",
            "limitations": "Limitations of the artifact"
          }
        }
      ]
    }
  ]
}`;
          }
        } else {
          // Standard format for other element types
          enhancedPrompt += `\n\nReturn the results as a JSON object with this nested structure:
{
  "elements": [
    {
      "name": "Top Level Element 1",
      "description": "Description",
      "element_type": "type",
      "children": [
        {
          "name": "Second Level Element 1-1",
          "description": "Description",
          "element_type": "type",
          "children": [
            {
              "name": "Third Level Element 1-1-1",
              "description": "Description",
              "element_type": "type"
            }
          ]
        }
      ]
    }
  ]
}`;
        }
      } else if (nestedMode) {
        // Nested mode: generate a parent element with sub-elements
        // The backend now handles the template instructions
        enhancedPrompt = prompt + contextString;

        // The backend now handles all format instructions
        if (false) { // Disabled - backend handles format instructions
          // Special format for magic-related elements with custom fields
          if (detectedMagicType === 'mixed') {
            // For mixed types, choose the most appropriate parent type based on the prompt
            enhancedPrompt += `\n\nReturn the results as a JSON object with this structure:
{
  "parent": {
    "name": "Parent Element Name",
    "description": "Detailed description of the parent element",
    "element_type": "CHOOSE_APPROPRIATE_TYPE",
    "custom_fields": {
      // Include appropriate custom fields based on the element_type
    }
  },
  "sub_elements": [
    // Include sub-elements of various types based on the prompt
  ]
}`;

            // Add examples for each detected type as sub-elements
            enhancedPrompt += `\n\nBased on the detected types in your prompt (${detectedTypes.join(', ')}), choose the most appropriate type for the parent element, and then include sub-elements of various types. Here are examples of the different element types and their custom fields:`;

            if (detectedTypes.includes('spell')) {
              enhancedPrompt += `\n\nSpell example:
{
  "name": "Spell Name",
  "description": "Detailed description of what the spell does and its effects",
  "element_type": "spell",
  "custom_fields": {
    "effect": "What the spell does when cast",
    "casting_time": "How long it takes to cast",
    "components": "Required components (verbal, somatic, material)",
    "duration": "How long the effect lasts",
    "range": "The range of the spell",
    "power_cost": "Magical energy required",
    "difficulty": "One of: novice, apprentice, adept, expert, master, legendary",
    "side_effects": "Any side effects or consequences",
    "visual_effect": "What the spell looks like when cast"
  }
}`;
            }

            if (detectedTypes.includes('magical_power')) {
              enhancedPrompt += `\n\nMagical Power example:
{
  "name": "Magical Power Name",
  "description": "Detailed description of the magical power",
  "element_type": "magical_power",
  "custom_fields": {
    "ability": "What the user can do with this power",
    "activation": "How the power is activated",
    "power_source": "Specific source that powers this ability",
    "limitations": "Limitations of this power",
    "mastery_levels": "Different levels of mastery for this power",
    "side_effects": "Any side effects from using this power",
    "manifestation": "How the power visibly manifests"
  }
}`;
            }

            if (detectedTypes.includes('magical_artifact')) {
              enhancedPrompt += `\n\nMagical Artifact example:
{
  "name": "Magical Artifact Name",
  "description": "Detailed description of the magical artifact",
  "element_type": "magical_artifact",
  "custom_fields": {
    "origin": "Where the artifact came from and who created it",
    "powers": "Magical powers the artifact possesses",
    "activation": "How the artifact is activated",
    "appearance": "What the artifact looks like",
    "limitations": "Limitations of the artifact",
    "materials": "What the artifact is made of",
    "attunement": "Requirements to attune to or use the artifact",
    "history": "Notable historical events involving the artifact"
  }
}`;
            }

            if (detectedTypes.includes('magic_system')) {
              enhancedPrompt += `\n\nMagic System example:
{
  "name": "Magic System Name",
  "description": "Detailed description of the magic system",
  "element_type": "magic_system",
  "custom_fields": {
    "source": "Where the magic comes from",
    "energy_type": "One of: mana, life_force, elemental, divine, cosmic, soul, blood, nature, other",
    "acquisition": "One of: innate, learned, granted, inherited, artifact, ritual, mixed",
    "rules": "Core rules of the magic system",
    "limitations": "Limitations of the magic",
    "cost": "What price users pay to use this magic",
    "practitioners": "Who can use this magic",
    "rarity": "One of: common, uncommon, rare, very_rare, unique"
  }
}`;
            }

          } else if (detectedMagicType === 'spell') {
            // Format for spells
            enhancedPrompt += `\n\nReturn the results as a JSON object with this structure:
{
  "parent": {
    "name": "Parent Spell Name",
    "description": "Detailed description of what the spell does and its effects",
    "element_type": "spell",
    "custom_fields": {
      "effect": "What the spell does when cast",
      "casting_time": "How long it takes to cast",
      "components": "Required components (verbal, somatic, material)",
      "duration": "How long the effect lasts",
      "range": "The range of the spell",
      "power_cost": "Magical energy required",
      "difficulty": "One of: novice, apprentice, adept, expert, master, legendary",
      "side_effects": "Any side effects or consequences",
      "visual_effect": "What the spell looks like when cast"
    }
  },
  "sub_elements": [
    {
      "name": "Spell Variant 1",
      "description": "A variation of the parent spell",
      "element_type": "spell",
      "custom_fields": {
        "effect": "How this variant differs from the base spell",
        "casting_time": "Casting time for this variant",
        "components": "Components for this variant",
        "duration": "Duration for this variant",
        "power_cost": "Power cost for this variant",
        "difficulty": "Difficulty level for this variant"
      }
    },
    ... additional spell variants ...
  ]
}`;
          } else if (detectedMagicType === 'magical_power') {
            // Format for magical powers
            enhancedPrompt += `\n\nReturn the results as a JSON object with this structure:
{
  "parent": {
    "name": "Parent Magical Power Name",
    "description": "Detailed description of the magical power",
    "element_type": "magical_power",
    "custom_fields": {
      "ability": "What the user can do with this power",
      "activation": "How the power is activated",
      "power_source": "Specific source that powers this ability",
      "limitations": "Limitations of this power",
      "mastery_levels": "Different levels of mastery for this power",
      "side_effects": "Any side effects from using this power",
      "manifestation": "How the power visibly manifests"
    }
  },
  "sub_elements": [
    {
      "name": "Power Variant 1",
      "description": "A variation or advanced form of the parent power",
      "element_type": "magical_power",
      "custom_fields": {
        "ability": "How this variant differs from the base power",
        "activation": "Activation method for this variant",
        "limitations": "Specific limitations for this variant",
        "mastery_level": "Required mastery level to use this variant"
      }
    },
    ... additional power variants ...
  ]
}`;
          } else if (detectedMagicType === 'magical_artifact') {
            // Format for magical artifacts
            enhancedPrompt += `\n\nReturn the results as a JSON object with this structure:
{
  "parent": {
    "name": "Parent Magical Artifact Name",
    "description": "Detailed description of the magical artifact",
    "element_type": "magical_artifact",
    "custom_fields": {
      "origin": "Where the artifact came from and who created it",
      "powers": "Magical powers the artifact possesses",
      "activation": "How the artifact is activated",
      "appearance": "What the artifact looks like",
      "limitations": "Limitations of the artifact",
      "materials": "What the artifact is made of",
      "attunement": "Requirements to attune to or use the artifact",
      "history": "Notable historical events involving the artifact"
    }
  },
  "sub_elements": [
    {
      "name": "Related Artifact 1",
      "description": "An artifact related to the parent artifact",
      "element_type": "magical_artifact",
      "custom_fields": {
        "powers": "Powers of this related artifact",
        "activation": "How this artifact is activated",
        "appearance": "What this artifact looks like",
        "relationship": "How it relates to the parent artifact"
      }
    },
    ... additional related artifacts ...
  ]
}`;
          } else {
            // Default format for magic systems
            enhancedPrompt += `\n\nReturn the results as a JSON object with this structure:
{
  "parent": {
    "name": "Parent Magic System Name",
    "description": "Detailed description of the magic system",
    "element_type": "magic_system",
    "custom_fields": {
      "source": "Where the magic comes from",
      "energy_type": "One of: mana, life_force, elemental, divine, cosmic, soul, blood, nature, other",
      "acquisition": "One of: innate, learned, granted, inherited, artifact, ritual, mixed",
      "rules": "Core rules of the magic system",
      "limitations": "Limitations of the magic",
      "cost": "What price users pay to use this magic",
      "practitioners": "Who can use this magic",
      "rarity": "One of: common, uncommon, rare, very_rare, unique"
    }
  },
  "sub_elements": [
    {
      "name": "Magical Power Name 1",
      "description": "Description of a magical power in this system",
      "element_type": "magical_power",
      "custom_fields": {
        "ability": "What the user can do with this power",
        "activation": "How the power is activated",
        "power_source": "Specific source that powers this ability",
        "limitations": "Limitations of this power",
        "mastery_levels": "Different levels of mastery for this power"
      }
    },
    {
      "name": "Magical Power Name 2",
      "description": "Description of another magical power in this system",
      "element_type": "magical_power",
      "custom_fields": {
        "ability": "What the user can do with this power",
        "activation": "How the power is activated",
        "power_source": "Specific source that powers this ability",
        "limitations": "Limitations of this power",
        "mastery_levels": "Different levels of mastery for this power"
      }
    },
    {
      "name": "Magical Power Name 3",
      "description": "Description of a third magical power in this system",
      "element_type": "magical_power",
      "custom_fields": {
        "ability": "What the user can do with this power",
        "activation": "How the power is activated",
        "power_source": "Specific source that powers this ability",
        "limitations": "Limitations of this power",
        "mastery_levels": "Different levels of mastery for this power"
      }
    },
    {
      "name": "Magical Artifact Name 1",
      "description": "Description of a magical artifact in this system",
      "element_type": "magical_artifact",
      "custom_fields": {
        "origin": "Where the artifact came from and who created it",
        "powers": "Magical powers the artifact possesses",
        "activation": "How the artifact is activated",
        "appearance": "What the artifact looks like",
        "limitations": "Limitations of the artifact"
      }
    },
    {
      "name": "Magical Artifact Name 2",
      "description": "Description of another magical artifact in this system",
      "element_type": "magical_artifact",
      "custom_fields": {
        "origin": "Where the artifact came from and who created it",
        "powers": "Magical powers the artifact possesses",
        "activation": "How the artifact is activated",
        "appearance": "What the artifact looks like",
        "limitations": "Limitations of the artifact"
      }
    }
  ]
}

IMPORTANT:
1. Pay careful attention to the number and types of elements requested in the user's prompt.
2. If the user asks for multiple elements of the same type (e.g., "3 magical powers and 2 artifacts"), you MUST include EXACTLY that number in your response.
3. The examples above are just templates - you should generate the exact number of each element type as requested.
4. For a magic system with sub-elements, include ALL requested sub-elements in the "sub_elements" array.
5. Do NOT limit yourself to just 3 sub-elements - include as many as the user requested.
6. Ensure your JSON response is complete and properly formatted with all opening brackets having matching closing brackets.`;
          }
        } else if (categoryInfo?.name?.toLowerCase().includes('cosmos') || categoryInfo?.name?.toLowerCase().includes('cosmology')) {
          // Format for cosmic structures in nested mode
          enhancedPrompt += `\n\nReturn the results as a JSON object with this structure:
{
  "parent": {
    "name": "Cosmic Structure Name",
    "description": "Detailed description of the cosmic structure",
    "element_type": "cosmic_structure",
    "custom_fields": {
      "structure_type": "Universe, dimension, galaxy, solar system, etc.",
      "physical_laws": "Laws of physics that govern this structure",
      "origin": "How it was created or formed",
      "age": "Age or timeline of this structure",
      "notable_features": "Distinctive characteristics",
      "inhabitants": "Beings or entities that inhabit this structure"
    }
  },
  "sub_elements": [
    {
      "name": "Celestial Body Name",
      "description": "Description of this celestial body",
      "element_type": "celestial_body",
      "custom_fields": {
        "body_type": "Star, planet, moon, asteroid, black hole, etc.",
        "composition": "What it's made of",
        "orbit": "How it moves through space",
        "size": "Size and mass information",
        "special_properties": "Unique characteristics",
        "significance": "Importance to inhabitants"
      }
    },
    {
      "name": "Cosmic Phenomenon Name",
      "description": "Description of this cosmic phenomenon",
      "element_type": "cosmic_phenomenon",
      "custom_fields": {
        "phenomenon_type": "Energy, temporal, spatial, gravitational, etc.",
        "cause": "What causes this phenomenon",
        "effects": "What effects it has",
        "frequency": "How often it occurs and how long it lasts",
        "detection": "How it can be detected or observed"
      }
    }
  ]
}`;
        } else if (categoryInfo?.name?.toLowerCase().includes('infrastructure')) {
          // Format for infrastructure systems in nested mode
          enhancedPrompt += `\n\nReturn the results as a JSON object with this structure:
{
  "parent": {
    "name": "Infrastructure System Name",
    "description": "Detailed description of the infrastructure system",
    "element_type": "infrastructure_system",
    "custom_fields": {
      "system_type": "Transportation, energy, water, communication, etc.",
      "coverage": "Area covered by this system",
      "technology_level": "Level of technological advancement",
      "maintenance": "How it's maintained and by whom",
      "access": "Who has access and how available it is",
      "vulnerabilities": "Weaknesses or vulnerabilities"
    }
  },
  "sub_elements": [
    {
      "name": "Facility Name",
      "description": "Description of this facility",
      "element_type": "facility",
      "custom_fields": {
        "facility_type": "Power plant, water treatment, etc.",
        "location": "Where it's located",
        "capacity": "What it produces or processes",
        "staff": "Who operates this facility",
        "security": "How it's protected",
        "special_features": "Unique capabilities"
      }
    },
    {
      "name": "Route/Network Name",
      "description": "Description of this route or network",
      "element_type": "route_network",
      "custom_fields": {
        "network_type": "Road, rail, data, etc.",
        "coverage": "Areas connected",
        "capacity": "Volume it can handle",
        "condition": "Current state of repair",
        "access_restrictions": "Who can use it",
        "hazards": "Dangers when using"
      }
    }
  ]
}`;
        } else if (categoryInfo?.name?.toLowerCase().includes('science') || categoryInfo?.name?.toLowerCase().includes('knowledge')) {
          // Format for fields of study in nested mode
          enhancedPrompt += `\n\nReturn the results as a JSON object with this structure:
{
  "parent": {
    "name": "Field of Study Name",
    "description": "Detailed description of the field of study",
    "element_type": "field_of_study",
    "custom_fields": {
      "field_type": "Natural science, social science, formal science, etc.",
      "key_concepts": "Core principles and ideas",
      "practitioners": "Who studies this field",
      "institutions": "Organizations dedicated to this field",
      "applications": "How knowledge is applied",
      "controversies": "Debates or limitations"
    }
  },
  "sub_elements": [
    {
      "name": "Advanced Technology Name",
      "description": "Description of this technology",
      "element_type": "advanced_technology",
      "custom_fields": {
        "tech_type": "Tool, weapon, transportation, communication, etc.",
        "function": "What it does",
        "development": "How it was developed",
        "availability": "How common or rare it is",
        "limitations": "Drawbacks or limitations",
        "impact": "How it affects society"
      }
    },
    {
      "name": "Knowledge Repository Name",
      "description": "Description of this repository",
      "element_type": "knowledge_repository",
      "custom_fields": {
        "repository_type": "Library, database, school, etc.",
        "contents": "What knowledge is stored",
        "location": "Where it's located",
        "access": "Who can access it and how",
        "organization": "How knowledge is organized",
        "caretakers": "Who maintains it"
      }
    }
  ]
}`;
        } else if (categoryInfo?.name?.toLowerCase().includes('economic') || categoryInfo?.name?.toLowerCase().includes('trade')) {
          // Format for economic systems in nested mode
          enhancedPrompt += `\n\nReturn the results as a JSON object with this structure:
{
  "parent": {
    "name": "Economic System Name",
    "description": "Detailed description of the economic system",
    "element_type": "economic_system",
    "custom_fields": {
      "system_type": "Feudal, mercantile, capitalist, etc.",
      "currency": "What currency is used",
      "resources": "Important resources",
      "wealth_distribution": "How wealth is distributed",
      "trade_partners": "Who they trade with",
      "economic_classes": "Social classes based on wealth"
    }
  },
  "sub_elements": [
    {
      "name": "Trade Route Name",
      "description": "Description of this trade route",
      "element_type": "trade_route",
      "custom_fields": {
        "route_type": "Land, sea, air, etc.",
        "goods": "What is traded",
        "endpoints": "Connected locations",
        "dangers": "Risks along the route",
        "control": "Who controls the route"
      }
    },
    {
      "name": "Market Name",
      "description": "Description of this market",
      "element_type": "market",
      "custom_fields": {
        "market_type": "Local, regional, black market, etc.",
        "location": "Where it's located",
        "goods": "What can be bought/sold",
        "schedule": "When it operates",
        "regulations": "Rules governing the market"
      }
    }
  ]
}`;
        } else if (categoryInfo?.name?.toLowerCase().includes('climate') || categoryInfo?.name?.toLowerCase().includes('weather')) {
          // Format for climate systems in nested mode
          enhancedPrompt += `\n\nReturn the results as a JSON object with this structure:
{
  "parent": {
    "name": "Climate System Name",
    "description": "Detailed description of the climate system",
    "element_type": "climate_system",
    "custom_fields": {
      "climate_type": "Tropical, temperate, polar, etc.",
      "temperature_range": "Typical temperatures",
      "precipitation": "Rainfall, snow, etc.",
      "seasons": "Seasonal patterns",
      "unique_features": "Unusual aspects",
      "impact": "Effects on inhabitants"
    }
  },
  "sub_elements": [
    {
      "name": "Weather Phenomenon Name",
      "description": "Description of this weather phenomenon",
      "element_type": "weather_phenomenon",
      "custom_fields": {
        "phenomenon_type": "Storm, wind, precipitation, etc.",
        "frequency": "How often it occurs",
        "duration": "How long it lasts",
        "effects": "Impact on environment and people",
        "prediction": "How it's forecast"
      }
    },
    {
      "name": "Season Name",
      "description": "Description of this season",
      "element_type": "season",
      "custom_fields": {
        "duration": "How long it lasts",
        "temperature": "Typical temperatures",
        "precipitation": "Rainfall, snow, etc.",
        "cultural_significance": "Cultural practices",
        "ecological_effects": "Effects on nature"
      }
    }
  ]
}`;
        } else {
          // Standard format for other element types
          enhancedPrompt += `\n\nReturn the results as a JSON object with this structure:
{
  "parent": {
    "name": "Parent Name",
    "description": "Detailed description",
    "element_type": "appropriate type"
  },
  "sub_elements": [
    {
      "name": "Sub-element 1 Name",
      "description": "Description that shows relationship to parent",
      "element_type": "appropriate type"
    },
    {
      "name": "Additional Element",
      "description": "Description of another element in this system",
      "element_type": "spell",
      "custom_fields": {
        "effect": "What this element does",
        "casting_time": "How long it takes to use",
        "components": "Required components",
        "duration": "How long the effect lasts",
        "range": "The range of effect",
        "power_cost": "Energy required",
        "difficulty": "Difficulty level"
      }
    }
  ]
}

IMPORTANT: Ensure your JSON response is complete and properly formatted. Each opening bracket must have a corresponding closing bracket. Do not use ellipses or placeholders in the actual JSON structure.`;
        }
      } else if (batchMode && count > 1) {
        // Batch mode: generate related elements at the same level
        enhancedPrompt = `Generate ${count} related ${categoryInfo?.name || 'elements'} that form a coherent group or set. They should be connected to each other in a meaningful way. ${prompt}${contextString}${parentContext}`;

        // Add format instructions for batch mode
        if (categoryInfo?.name?.toLowerCase().includes('cosmos') || categoryInfo?.name?.toLowerCase().includes('cosmology')) {
          // Format for cosmic structures in batch mode
          enhancedPrompt += `\n\nReturn the results as a JSON array with this structure:
[
  {
    "name": "Cosmic Structure Name",
    "description": "Detailed description of the cosmic structure",
    "element_type": "cosmic_structure",
    "custom_fields": {
      "structure_type": "Universe, dimension, galaxy, solar system, etc.",
      "physical_laws": "Laws of physics that govern this structure",
      "origin": "How it was created or formed",
      "age": "Age or timeline of this structure",
      "notable_features": "Distinctive characteristics",
      "inhabitants": "Beings or entities that inhabit this structure"
    }
  },
  {
    "name": "Celestial Body Name",
    "description": "Description of this celestial body",
    "element_type": "celestial_body",
    "custom_fields": {
      "body_type": "Star, planet, moon, asteroid, black hole, etc.",
      "composition": "What it's made of",
      "orbit": "How it moves through space",
      "size": "Size and mass information",
      "special_properties": "Unique characteristics",
      "significance": "Importance to inhabitants"
    }
  },
  ... additional cosmic elements ...
]`;
        } else if (categoryInfo?.name?.toLowerCase().includes('infrastructure')) {
          // Format for infrastructure systems in batch mode
          enhancedPrompt += `\n\nReturn the results as a JSON array with this structure:
[
  {
    "name": "Infrastructure System Name",
    "description": "Detailed description of the infrastructure system",
    "element_type": "infrastructure_system",
    "custom_fields": {
      "system_type": "Transportation, energy, water, communication, etc.",
      "coverage": "Area covered by this system",
      "technology_level": "Level of technological advancement",
      "maintenance": "How it's maintained and by whom",
      "access": "Who has access and how available it is",
      "vulnerabilities": "Weaknesses or vulnerabilities"
    }
  },
  {
    "name": "Facility Name",
    "description": "Description of this facility",
    "element_type": "facility",
    "custom_fields": {
      "facility_type": "Power plant, water treatment, etc.",
      "location": "Where it's located",
      "capacity": "What it produces or processes",
      "staff": "Who operates this facility",
      "security": "How it's protected",
      "special_features": "Unique capabilities"
    }
  },
  ... additional infrastructure elements ...
]`;
        } else if (categoryInfo?.name?.toLowerCase().includes('science') || categoryInfo?.name?.toLowerCase().includes('knowledge')) {
          // Format for fields of study in batch mode
          enhancedPrompt += `\n\nReturn the results as a JSON array with this structure:
[
  {
    "name": "Field of Study Name",
    "description": "Detailed description of the field of study",
    "element_type": "field_of_study",
    "custom_fields": {
      "field_type": "Natural science, social science, formal science, etc.",
      "key_concepts": "Core principles and ideas",
      "practitioners": "Who studies this field",
      "institutions": "Organizations dedicated to this field",
      "applications": "How knowledge is applied",
      "controversies": "Debates or limitations"
    }
  },
  {
    "name": "Knowledge Repository Name",
    "description": "Description of this repository",
    "element_type": "knowledge_repository",
    "custom_fields": {
      "repository_type": "Library, database, school, etc.",
      "contents": "What knowledge is stored",
      "location": "Where it's located",
      "access": "Who can access it and how",
      "organization": "How knowledge is organized",
      "caretakers": "Who maintains it"
    }
  },
  ... additional knowledge elements ...
]`;
        } else if (categoryInfo?.name?.toLowerCase().includes('economic') || categoryInfo?.name?.toLowerCase().includes('trade')) {
          // Format for economic systems in batch mode
          enhancedPrompt += `\n\nReturn the results as a JSON array with this structure:
[
  {
    "name": "Economic System Name",
    "description": "Detailed description of the economic system",
    "element_type": "economic_system",
    "custom_fields": {
      "system_type": "Feudal, mercantile, capitalist, etc.",
      "currency": "What currency is used",
      "resources": "Important resources",
      "wealth_distribution": "How wealth is distributed",
      "trade_partners": "Who they trade with",
      "economic_classes": "Social classes based on wealth"
    }
  },
  {
    "name": "Trade Route Name",
    "description": "Description of this trade route",
    "element_type": "trade_route",
    "custom_fields": {
      "route_type": "Land, sea, air, etc.",
      "goods": "What is traded",
      "endpoints": "Connected locations",
      "dangers": "Risks along the route",
      "control": "Who controls the route"
    }
  },
  ... additional economic elements ...
]`;
        } else if (categoryInfo?.name?.toLowerCase().includes('animal') || (categoryInfo?.name?.toLowerCase().includes('flora') && categoryInfo?.name?.toLowerCase().includes('fauna'))) {
          // Format for animals in batch mode
          enhancedPrompt += `\n\nReturn the results as a JSON array with this structure:
[
  {
    "name": "Animal Name",
    "description": "Detailed description of this animal",
    "element_type": "animal",
    "custom_fields": {
      "animal_type": "Mammal, bird, reptile, etc.",
      "habitat": "Where this animal lives",
      "appearance": "Physical characteristics",
      "behavior": "Behavioral patterns",
      "diet": "What this animal eats",
      "lifecycle": "Reproduction, lifespan, etc."
    }
  },
  ... additional animals ...
]`;
        } else if (categoryInfo?.name?.toLowerCase().includes('climate') || categoryInfo?.name?.toLowerCase().includes('weather')) {
          // Format for climate systems in batch mode
          enhancedPrompt += `\n\nReturn the results as a JSON array with this structure:
[
  {
    "name": "Climate System Name",
    "description": "Detailed description of the climate system",
    "element_type": "climate_system",
    "custom_fields": {
      "climate_type": "Tropical, temperate, polar, etc.",
      "temperature_range": "Typical temperatures",
      "precipitation": "Rainfall, snow, etc.",
      "seasons": "Seasonal patterns",
      "unique_features": "Unusual aspects",
      "impact": "Effects on inhabitants"
    }
  },
  {
    "name": "Weather Phenomenon Name",
    "description": "Description of this weather phenomenon",
    "element_type": "weather_phenomenon",
    "custom_fields": {
      "phenomenon_type": "Storm, wind, precipitation, etc.",
      "frequency": "How often it occurs",
      "duration": "How long it lasts",
      "effects": "Impact on environment and people",
      "prediction": "How it's forecast"
    }
  },
  ... additional climate elements ...
]`;
        } else if (categoryInfo?.name?.toLowerCase().includes('magic')) {
          if (detectedMagicType === 'mixed') {
            // Format for mixed types (multiple types detected)
            enhancedPrompt += `\n\nReturn the results as a JSON array with this structure:
[
  // Include elements of various types based on the prompt
]`;

            // Add examples for each detected type
            enhancedPrompt += `\n\nBased on the detected types in your prompt (${detectedTypes.join(', ')}), include elements of various types. Here are examples of the different element types and their custom fields:`;

            if (detectedTypes.includes('spell')) {
              enhancedPrompt += `\n\nSpell example:
{
  "name": "Spell Name",
  "description": "Detailed description of what the spell does and its effects",
  "element_type": "spell",
  "custom_fields": {
    "effect": "What the spell does when cast",
    "casting_time": "How long it takes to cast",
    "components": "Required components (verbal, somatic, material)",
    "duration": "How long the effect lasts",
    "range": "The range of the spell",
    "power_cost": "Magical energy required",
    "difficulty": "One of: novice, apprentice, adept, expert, master, legendary",
    "side_effects": "Any side effects or consequences",
    "visual_effect": "What the spell looks like when cast"
  }
}`;
            }

            if (detectedTypes.includes('magical_power')) {
              enhancedPrompt += `\n\nMagical Power example:
{
  "name": "Magical Power Name",
  "description": "Detailed description of the magical power",
  "element_type": "magical_power",
  "custom_fields": {
    "ability": "What the user can do with this power",
    "activation": "How the power is activated",
    "power_source": "Specific source that powers this ability",
    "limitations": "Limitations of this power",
    "mastery_levels": "Different levels of mastery for this power",
    "side_effects": "Any side effects from using this power",
    "manifestation": "How the power visibly manifests"
  }
}`;
            }

            if (detectedTypes.includes('magical_artifact')) {
              enhancedPrompt += `\n\nMagical Artifact example:
{
  "name": "Magical Artifact Name",
  "description": "Detailed description of the magical artifact",
  "element_type": "magical_artifact",
  "custom_fields": {
    "origin": "Where the artifact came from and who created it",
    "powers": "Magical powers the artifact possesses",
    "activation": "How the artifact is activated",
    "appearance": "What the artifact looks like",
    "limitations": "Limitations of the artifact",
    "materials": "What the artifact is made of",
    "attunement": "Requirements to attune to or use the artifact",
    "history": "Notable historical events involving the artifact"
  }
}`;
            }

            if (detectedTypes.includes('magic_system')) {
              enhancedPrompt += `\n\nMagic System example:
{
  "name": "Magic System Name",
  "description": "Detailed description of the magic system",
  "element_type": "magic_system",
  "custom_fields": {
    "source": "Where the magic comes from",
    "energy_type": "One of: mana, life_force, elemental, divine, cosmic, soul, blood, nature, other",
    "acquisition": "One of: innate, learned, granted, inherited, artifact, ritual, mixed",
    "rules": "Core rules of the magic system",
    "limitations": "Limitations of the magic",
    "cost": "What price users pay to use this magic",
    "practitioners": "Who can use this magic",
    "rarity": "One of: common, uncommon, rare, very_rare, unique"
  }
}`;
            }

            // Add instructions for distributing elements
            enhancedPrompt += `\n\nIMPORTANT: Distribute the elements among the detected types (${detectedTypes.join(', ')}) based on the user's prompt. Each element should have the appropriate element_type and custom_fields for its type.`;

          } else if (detectedMagicType === 'spell') {
            // Format for spells
            enhancedPrompt += `\n\nReturn the results as a JSON array with this structure:
[
  {
    "name": "Spell 1",
    "description": "Detailed description of what the spell does and its effects",
    "element_type": "spell",
    "custom_fields": {
      "effect": "What the spell does when cast",
      "casting_time": "How long it takes to cast",
      "components": "Required components (verbal, somatic, material)",
      "duration": "How long the effect lasts",
      "range": "The range of the spell",
      "power_cost": "Magical energy required",
      "difficulty": "One of: novice, apprentice, adept, expert, master, legendary",
      "side_effects": "Any side effects or consequences",
      "visual_effect": "What the spell looks like when cast"
    }
  },
  ... additional spells ...
]`;
          } else if (detectedMagicType === 'magical_power') {
            // Format for magical powers
            enhancedPrompt += `\n\nReturn the results as a JSON array with this structure:
[
  {
    "name": "Magical Power 1",
    "description": "Detailed description of the magical power",
    "element_type": "magical_power",
    "custom_fields": {
      "ability": "What the user can do with this power",
      "activation": "How the power is activated",
      "power_source": "Specific source that powers this ability",
      "limitations": "Limitations of this power",
      "mastery_levels": "Different levels of mastery for this power",
      "side_effects": "Any side effects from using this power",
      "manifestation": "How the power visibly manifests"
    }
  },
  ... additional magical powers ...
]`;
          } else if (detectedMagicType === 'magical_artifact') {
            // Format for magical artifacts
            enhancedPrompt += `\n\nReturn the results as a JSON array with this structure:
[
  {
    "name": "Magical Artifact 1",
    "description": "Detailed description of the magical artifact",
    "element_type": "magical_artifact",
    "custom_fields": {
      "origin": "Where the artifact came from and who created it",
      "powers": "Magical powers the artifact possesses",
      "activation": "How the artifact is activated",
      "appearance": "What the artifact looks like",
      "limitations": "Limitations of the artifact",
      "materials": "What the artifact is made of",
      "attunement": "Requirements to attune to or use the artifact",
      "history": "Notable historical events involving the artifact"
    }
  },
  ... additional magical artifacts ...
]`;
          } else {
            // Default format for magic systems
            enhancedPrompt += `\n\nReturn the results as a JSON array with this structure:
[
  {
    "name": "Magic System 1",
    "description": "Detailed description of the magic system",
    "element_type": "magic_system",
    "custom_fields": {
      "source": "Where the magic comes from",
      "energy_type": "One of: mana, life_force, elemental, divine, cosmic, soul, blood, nature, other",
      "acquisition": "One of: innate, learned, granted, inherited, artifact, ritual, mixed",
      "rules": "Core rules of the magic system",
      "limitations": "Limitations of the magic",
      "cost": "What price users pay to use this magic",
      "practitioners": "Who can use this magic",
      "rarity": "One of: common, uncommon, rare, very_rare, unique"
    }
  },
  ... additional magic systems ...
]`;
          }
        }
      } else {
        // Standard mode: generate independent elements
        enhancedPrompt = `${prompt}${contextString}${parentContext}`;

        // Add format instructions for standard mode
        if (categoryInfo?.name?.toLowerCase().includes('cosmos') || categoryInfo?.name?.toLowerCase().includes('cosmology')) {
          // Format for cosmic structures in standard mode
          enhancedPrompt += `\n\nReturn the result as a JSON object with this structure:
{
  "name": "Cosmic Structure Name",
  "description": "Detailed description of the cosmic structure",
  "element_type": "cosmic_structure",
  "custom_fields": {
    "structure_type": "Universe, dimension, galaxy, solar system, etc.",
    "physical_laws": "Laws of physics that govern this structure",
    "origin": "How it was created or formed",
    "age": "Age or timeline of this structure",
    "notable_features": "Distinctive characteristics",
    "inhabitants": "Beings or entities that inhabit this structure"
  }
}`;
        } else if (categoryInfo?.name?.toLowerCase().includes('infrastructure')) {
          // Format for infrastructure systems in standard mode
          enhancedPrompt += `\n\nReturn the result as a JSON object with this structure:
{
  "name": "Infrastructure System Name",
  "description": "Detailed description of the infrastructure system",
  "element_type": "infrastructure_system",
  "custom_fields": {
    "system_type": "Transportation, energy, water, communication, etc.",
    "coverage": "Area covered by this system",
    "technology_level": "Level of technological advancement",
    "maintenance": "How it's maintained and by whom",
    "access": "Who has access and how available it is",
    "vulnerabilities": "Weaknesses or vulnerabilities"
  }
}`;
        } else if (categoryInfo?.name?.toLowerCase().includes('science') || categoryInfo?.name?.toLowerCase().includes('knowledge')) {
          // Format for fields of study in standard mode
          enhancedPrompt += `\n\nReturn the result as a JSON object with this structure:
{
  "name": "Field of Study Name",
  "description": "Detailed description of the field of study",
  "element_type": "field_of_study",
  "custom_fields": {
    "field_type": "Natural science, social science, formal science, etc.",
    "key_concepts": "Core principles and ideas",
    "practitioners": "Who studies this field",
    "institutions": "Organizations dedicated to this field",
    "applications": "How knowledge is applied",
    "controversies": "Debates or limitations"
  }
}`;
        } else if (categoryInfo?.name?.toLowerCase().includes('economic') || categoryInfo?.name?.toLowerCase().includes('trade')) {
          // Format for economic systems in standard mode
          enhancedPrompt += `\n\nReturn the result as a JSON object with this structure:
{
  "name": "Economic System Name",
  "description": "Detailed description of the economic system",
  "element_type": "economic_system",
  "custom_fields": {
    "system_type": "Feudal, mercantile, capitalist, etc.",
    "currency": "What currency is used",
    "resources": "Important resources",
    "wealth_distribution": "How wealth is distributed",
    "trade_partners": "Who they trade with",
    "economic_classes": "Social classes based on wealth"
  }
}`;
        } else if (categoryInfo?.name?.toLowerCase().includes('climate') || categoryInfo?.name?.toLowerCase().includes('weather')) {
          // Format for climate systems in standard mode
          enhancedPrompt += `\n\nReturn the result as a JSON object with this structure:
{
  "name": "Climate System Name",
  "description": "Detailed description of the climate system",
  "element_type": "climate_system",
  "custom_fields": {
    "climate_type": "Tropical, temperate, polar, etc.",
    "temperature_range": "Typical temperatures",
    "precipitation": "Rainfall, snow, etc.",
    "seasons": "Seasonal patterns",
    "unique_features": "Unusual aspects",
    "impact": "Effects on inhabitants"
  }
}`;
        } else if (categoryInfo?.name?.toLowerCase().includes('magic') && count === 1) {
          if (detectedMagicType === 'mixed') {
            // For mixed types in standard mode, choose the most appropriate type based on the prompt
            enhancedPrompt += `\n\nBased on the detected types in your prompt (${detectedTypes.join(', ')}), choose the most appropriate type for the element. Here are examples of the different element types and their custom fields:`;

            if (detectedTypes.includes('spell')) {
              enhancedPrompt += `\n\nSpell example:
{
  "name": "Spell Name",
  "description": "Detailed description of what the spell does and its effects",
  "element_type": "spell",
  "custom_fields": {
    "effect": "What the spell does when cast",
    "casting_time": "How long it takes to cast",
    "components": "Required components (verbal, somatic, material)",
    "duration": "How long the effect lasts",
    "range": "The range of the spell",
    "power_cost": "Magical energy required",
    "difficulty": "One of: novice, apprentice, adept, expert, master, legendary",
    "side_effects": "Any side effects or consequences",
    "visual_effect": "What the spell looks like when cast"
  }
}`;
            }

            if (detectedTypes.includes('magical_power')) {
              enhancedPrompt += `\n\nMagical Power example:
{
  "name": "Magical Power Name",
  "description": "Detailed description of the magical power",
  "element_type": "magical_power",
  "custom_fields": {
    "ability": "What the user can do with this power",
    "activation": "How the power is activated",
    "power_source": "Specific source that powers this ability",
    "limitations": "Limitations of this power",
    "mastery_levels": "Different levels of mastery for this power",
    "side_effects": "Any side effects from using this power",
    "manifestation": "How the power visibly manifests"
  }
}`;
            }

            if (detectedTypes.includes('magical_artifact')) {
              enhancedPrompt += `\n\nMagical Artifact example:
{
  "name": "Magical Artifact Name",
  "description": "Detailed description of the magical artifact",
  "element_type": "magical_artifact",
  "custom_fields": {
    "origin": "Where the artifact came from and who created it",
    "powers": "Magical powers the artifact possesses",
    "activation": "How the artifact is activated",
    "appearance": "What the artifact looks like",
    "limitations": "Limitations of the artifact",
    "materials": "What the artifact is made of",
    "attunement": "Requirements to attune to or use the artifact",
    "history": "Notable historical events involving the artifact"
  }
}`;
            }

            if (detectedTypes.includes('magic_system')) {
              enhancedPrompt += `\n\nMagic System example:
{
  "name": "Magic System Name",
  "description": "Detailed description of the magic system",
  "element_type": "magic_system",
  "custom_fields": {
    "source": "Where the magic comes from",
    "energy_type": "One of: mana, life_force, elemental, divine, cosmic, soul, blood, nature, other",
    "acquisition": "One of: innate, learned, granted, inherited, artifact, ritual, mixed",
    "rules": "Core rules of the magic system",
    "limitations": "Limitations of the magic",
    "cost": "What price users pay to use this magic",
    "practitioners": "Who can use this magic",
    "rarity": "One of: common, uncommon, rare, very_rare, unique"
  }
}`;
            }

            // Add instructions for choosing the appropriate type
            enhancedPrompt += `\n\nIMPORTANT: Choose the most appropriate element_type from the detected types (${detectedTypes.join(', ')}) based on the user's prompt. The element should have the appropriate element_type and custom_fields for its type.`;

          } else if (detectedMagicType === 'spell') {
            // Format for a single spell
            enhancedPrompt += `\n\nReturn the result as a JSON object with this structure:
{
  "name": "Spell Name",
  "description": "Detailed description of what the spell does and its effects",
  "element_type": "spell",
  "custom_fields": {
    "effect": "What the spell does when cast",
    "casting_time": "How long it takes to cast",
    "components": "Required components (verbal, somatic, material)",
    "duration": "How long the effect lasts",
    "range": "The range of the spell",
    "power_cost": "Magical energy required",
    "difficulty": "One of: novice, apprentice, adept, expert, master, legendary",
    "side_effects": "Any side effects or consequences",
    "visual_effect": "What the spell looks like when cast"
  }
}`;
          } else if (detectedMagicType === 'magical_power') {
            // Format for a single magical power
            enhancedPrompt += `\n\nReturn the result as a JSON object with this structure:
{
  "name": "Magical Power Name",
  "description": "Detailed description of the magical power",
  "element_type": "magical_power",
  "custom_fields": {
    "ability": "What the user can do with this power",
    "activation": "How the power is activated",
    "power_source": "Specific source that powers this ability",
    "limitations": "Limitations of this power",
    "mastery_levels": "Different levels of mastery for this power",
    "side_effects": "Any side effects from using this power",
    "manifestation": "How the power visibly manifests"
  }
}`;
          } else if (detectedMagicType === 'magical_artifact') {
            // Format for a single magical artifact
            enhancedPrompt += `\n\nReturn the result as a JSON object with this structure:
{
  "name": "Magical Artifact Name",
  "description": "Detailed description of the magical artifact",
  "element_type": "magical_artifact",
  "custom_fields": {
    "origin": "Where the artifact came from and who created it",
    "powers": "Magical powers the artifact possesses",
    "activation": "How the artifact is activated",
    "appearance": "What the artifact looks like",
    "limitations": "Limitations of the artifact",
    "materials": "What the artifact is made of",
    "attunement": "Requirements to attune to or use the artifact",
    "history": "Notable historical events involving the artifact"
  }
}`;
          } else {
            // Default format for a single magic system
            enhancedPrompt += `\n\nReturn the result as a JSON object with this structure:
{
  "name": "Magic System Name",
  "description": "Detailed description of the magic system",
  "element_type": "magic_system",
  "custom_fields": {
    "source": "Where the magic comes from",
    "energy_type": "One of: mana, life_force, elemental, divine, cosmic, soul, blood, nature, other",
    "acquisition": "One of: innate, learned, granted, inherited, artifact, ritual, mixed",
    "rules": "Core rules of the magic system",
    "limitations": "Limitations of the magic",
    "cost": "What price users pay to use this magic",
    "practitioners": "Who can use this magic",
    "rarity": "One of: common, uncommon, rare, very_rare, unique"
  }
}`;
          }
        }
      }

      // Add parent relationship instructions (only if not in nested mode or multi-level mode)
      // Note: We're not using enhancedPrompt anymore as we're passing the original prompt to the backend
      // The backend will handle adding the parent relationship instructions

      // Call the API with the appropriate parameters
      const response = await generateWorldElements(
        bookId,
        categoryId,
        prompt, // Use the original user prompt without template instructions
        multiLevelMode ? 1 : (nestedMode ? 1 : count), // In nested or multi-level mode, we're only generating 1 parent element
        selectedParentId || null, // Always pass the selectedParentId if it exists
        nestedMode || multiLevelMode, // Pass the nestedMode or multiLevelMode flag to the API
        subElementCount, // Number of sub-elements for nested mode
        nestingDepth, // Depth of hierarchy for multi-level mode
        [level1Count, level2Count, level3Count] // Counts for each level in multi-level mode
      );

      // Process the response based on the mode
      if (multiLevelMode && response.elements) {
        // For multi-level mode, we need to process the hierarchical structure

        // Helper function to process hierarchical elements
        const processHierarchy = (elements, parentId = null, parentName = null, level = 0) => {
          return elements.flatMap(element => {
            // Create the current element with parent info
            const currentElement = {
              ...element,
              parent_id: parentId,
              parent_name: parentName,
              _level: level,
              _is_generated: true,
              category: categoryId
            };

            // If this element has children, process them too
            if (element.children && element.children.length > 0) {
              const tempId = 'temp_' + element.name.replace(/\s+/g, '_').toLowerCase();

              // Mark this element as having children
              currentElement.has_children = true;

              const children = processHierarchy(
                element.children,
                tempId, // Temporary ID
                element.name,
                level + 1
              );

              // Return this element and all its children
              return [currentElement, ...children];
            }

            // Just return this element if no children
            return [currentElement];
          });
        };

        // Process the hierarchical elements
        const processedElements = processHierarchy(
          response.elements,
          selectedParentId ? selectedParentId : null,
          selectedParentId ? parentElements.find(e => e.element_id === selectedParentId)?.name : null
        );

        // Set the processed elements for preview
        setGeneratedPreview(processedElements);

      } else if (nestedMode && response.parent && response.sub_elements) {
        // For nested mode, we got a parent and sub-elements
        // Format the parent element
        const parentElement = {
          ...response.parent,
          is_parent: true,
          has_children: true,
          _is_generated: true,
          category: categoryId
        };

        // Format the sub-elements with parent reference
        const subElements = response.sub_elements.map(subElem => ({
          ...subElem,
          parent_id: 'temp_parent_id', // Will be replaced when saved
          parent_name: parentElement.name,
          _is_sub_element: true,
          _is_generated: true,
          category: categoryId
        }));

        // Combine parent and children for preview
        setGeneratedPreview([parentElement, ...subElements]);
      } else if (selectedParentId) {
        // For standard mode with a selected parent
        const selectedParent = parentElements.find(elem => elem.element_id === selectedParentId);

        // Check if response is an array or has a different structure
        if (Array.isArray(response)) {
          const elementsWithParentInfo = response.map(elem => ({
            ...elem,
            parent_id: selectedParentId,
            parent_name: selectedParent.name,
            _is_generated: true,
            category: categoryId
          }));
          setGeneratedPreview(elementsWithParentInfo);
        } else if (response.elements && Array.isArray(response.elements)) {
          // Handle response with elements array
          const elementsWithParentInfo = response.elements.map(elem => ({
            ...elem,
            parent_id: selectedParentId,
            parent_name: selectedParent.name,
            _is_generated: true,
            category: categoryId
          }));
          setGeneratedPreview(elementsWithParentInfo);
        } else if (response.elements && response.elements[0] && response.elements[0].parent && response.elements[0].sub_elements) {
          // Handle nested structure inside elements array
          const parentElement = {
            ...response.elements[0].parent,
            is_parent: true,
            has_children: true,
            _is_generated: true,
            category: categoryId,
            parent_id: selectedParentId,
            parent_name: selectedParent.name
          };

          const subElements = response.elements[0].sub_elements.map(subElem => ({
            ...subElem,
            parent_id: 'temp_parent_id', // Will be replaced when saved
            parent_name: parentElement.name,
            _is_sub_element: true,
            _is_generated: true,
            category: categoryId
          }));

          setGeneratedPreview([parentElement, ...subElements]);
        } else {
          console.error('Unexpected response format:', response);
          setError('Received an unexpected response format from the server');
        }
      } else {
        // Standard or batch mode
        if (Array.isArray(response)) {
          // Handle array response
          setGeneratedPreview(response.map(elem => ({
            ...elem,
            _is_generated: true,
            category: categoryId
          })));
        } else if (response.elements && Array.isArray(response.elements)) {
          // Handle response with elements array
          setGeneratedPreview(response.elements.map(elem => ({
            ...elem,
            _is_generated: true,
            category: categoryId
          })));
        } else if (response.elements && response.elements[0] && response.elements[0].parent && response.elements[0].sub_elements) {
          // Handle nested structure inside elements array
          const parentElement = {
            ...response.elements[0].parent,
            is_parent: true,
            has_children: true,
            _is_generated: true,
            category: categoryId
          };

          const subElements = response.elements[0].sub_elements.map(subElem => ({
            ...subElem,
            parent_id: 'temp_parent_id', // Will be replaced when saved
            parent_name: parentElement.name,
            _is_sub_element: true,
            _is_generated: true,
            category: categoryId
          }));

          setGeneratedPreview([parentElement, ...subElements]);
        } else {
          console.error('Unexpected response format:', response);
          setError('Received an unexpected response format from the server');
        }
      }

      setShowPreview(true);

      // Don't clear the prompt yet - user might want to regenerate
    } catch (error) {
      console.error('Error generating elements:', error);
      setError(error.message || 'Failed to generate elements');
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div className="world-element-generator">
      <h3>Generate Elements with AI</h3>

      {categoryInfo && (
        <div className="category-info">
          <h4>Generating: {categoryInfo.name}</h4>
          <p>{categoryInfo.description}</p>
          {categoryElements.length > 0 && (
            <div className="context-info">
              <span className="context-badge">{categoryElements.length} existing {categoryInfo.name.toLowerCase()}</span>
              <span className="context-note">AI will consider existing elements for consistency</span>
            </div>
          )}
        </div>
      )}

      {!showPreview ? (
        // Generation Form
        <div className="generator-form">
          <div className="generation-options">
            <button
              type="button"
              className={`option-button ${!showTemplates ? 'active' : ''}`}
              onClick={() => setShowTemplates(false)}
              disabled={isGenerating}
            >
              Free-form Prompt
            </button>
            <button
              type="button"
              className={`option-button ${showTemplates ? 'active' : ''}`}
              onClick={() => setShowTemplates(true)}
              disabled={isGenerating || templates.length === 0}
            >
              Use Templates
            </button>
          </div>

          {!showTemplates ? (
            <div className="form-group">
              <label htmlFor="prompt">Prompt:</label>
              <textarea
                id="prompt"
                value={prompt}
                onChange={handlePromptChange}
                placeholder={categoryInfo
                  ? `Describe the ${categoryInfo.name.toLowerCase()} you want to generate...`
                  : "Describe the elements you want to generate..."}
                rows={3}
                disabled={isGenerating}
              />
            </div>
          ) : (
            <div className="template-selector">
              <label>Select a Template:</label>
              <div className="template-cards">
                {templates.map((template, index) => (
                  <div
                    key={index}
                    className="template-card"
                    onClick={() => {
                      const templatePrompt = `Generate a ${template.name.toLowerCase()} with the following details:\n` +
                        template.fields.map(field => `- ${field.label}: [${field.placeholder}]`).join('\n');
                      setPrompt(templatePrompt);
                      setShowTemplates(false);
                    }}
                  >
                    <h4>{template.name}</h4>
                    <p className="template-description">{template.description}</p>
                    <div className="template-fields">
                      {template.fields.slice(0, 4).map((field, fieldIndex) => (
                        <div key={fieldIndex} className="template-field">
                          <span className="field-label">{field.label}</span>
                        </div>
                      ))}
                      {template.fields.length > 4 && (
                        <div className="template-field-more">
                          +{template.fields.length - 4} more fields
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {promptSuggestions.length > 0 && (
            <div className="prompt-suggestions">
              <h5>Suggested prompts:</h5>
              <div className="suggestion-buttons">
                {promptSuggestions.map((suggestion, index) => (
                  <button
                    key={index}
                    type="button"
                    className="suggestion-button"
                    onClick={() => applySuggestion(suggestion)}
                    disabled={isGenerating}
                  >
                    {suggestion}
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Mode toggles in a single row */}
          <div className="form-group mode-toggles-row">
            <div className="mode-toggles">
              <div className="batch-mode-toggle">
                <label className="toggle-label">
                  <input
                    type="checkbox"
                    checked={batchMode}
                    onChange={() => {
                      setBatchMode(!batchMode);
                      if (!batchMode) {
                        setNestedMode(false); // Turn off nested mode when enabling batch mode
                        setMultiLevelMode(false); // Turn off multi-level mode when enabling batch mode
                      }
                    }}
                    disabled={isGenerating || nestedMode || multiLevelMode}
                  />
                  <span className="toggle-text">Batch Mode</span>
                </label>
                <div className="tooltip">
                  <span className="tooltip-icon">?</span>
                  <span className="tooltip-text">
                    Batch mode generates related elements that form a coherent group
                  </span>
                </div>
              </div>

              <div className="multi-level-mode-toggle">
                <label className="toggle-label">
                  <input
                    type="checkbox"
                    checked={multiLevelMode}
                    onChange={() => {
                      setMultiLevelMode(!multiLevelMode);
                      if (!multiLevelMode) {
                        // Turn off other modes when enabling multi-level mode
                        setBatchMode(false);
                        setNestedMode(false);
                      } else {
                        // Reset nesting depth when turning off
                        setNestingDepth(1);
                      }
                    }}
                    disabled={isGenerating || batchMode || nestedMode}
                  />
                  <span className="toggle-text">Multi-Level Hierarchy</span>
                </label>
                <div className="tooltip">
                  <span className="tooltip-icon">?</span>
                  <span className="tooltip-text">
                    Generate complex hierarchical structures with multiple levels of nesting
                  </span>
                </div>
              </div>

              <div className="nested-mode-toggle">
                <label className="toggle-label">
                  <input
                    type="checkbox"
                    checked={nestedMode}
                    onChange={() => {
                      setNestedMode(!nestedMode);
                      if (!nestedMode) {
                        // Turn off other modes when enabling nested mode
                        setBatchMode(false);
                        setMultiLevelMode(false);
                      }
                    }}
                    disabled={isGenerating || multiLevelMode || batchMode}
                  />
                  <span className="toggle-text">Nested Mode</span>
                </label>
                <div className="tooltip">
                  <span className="tooltip-icon">?</span>
                  <span className="tooltip-text">
                    Nested mode generates a parent element with sub-elements in one go
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Number of elements - only shown when multi-level mode is OFF */}
          {!multiLevelMode && (
            <div className="form-group">
              <label htmlFor="count">Number of elements (1-20):</label>
              <div className="count-row">
                <input
                  id="count"
                  type="number"
                  min="1"
                  max="20"
                  value={count}
                  onChange={handleCountChange}
                  disabled={isGenerating || nestedMode}
                />
              </div>
            </div>
          )}

          {multiLevelMode && (
            <div className="form-group">
              <div className="nesting-configuration">
                <div className="nesting-level-control">
                  <label htmlFor="nesting-depth">Nesting Depth:</label>
                  <select
                    id="nesting-depth"
                    value={nestingDepth}
                    onChange={(e) => setNestingDepth(parseInt(e.target.value, 10))}
                    disabled={isGenerating}
                  >
                    <option value="1">1 level (parent + children)</option>
                    <option value="2">2 levels (parent + children + grandchildren)</option>
                    <option value="3">3 levels (deep hierarchy)</option>
                  </select>
                </div>

                <div className="elements-per-level">
                  <label>Elements per level:</label>
                  <div className="level-counts">
                    <div className="level-count">
                      <span>Level 1:</span>
                      <input
                        type="number"
                        min="1"
                        max="10"
                        value={level1Count}
                        onChange={(e) => setLevel1Count(Math.min(Math.max(1, parseInt(e.target.value, 10)), 10))}
                        disabled={isGenerating}
                      />
                    </div>

                    {nestingDepth >= 2 && (
                      <div className="level-count">
                        <span>Level 2:</span>
                        <input
                          type="number"
                          min="1"
                          max="10"
                          value={level2Count}
                          onChange={(e) => setLevel2Count(Math.min(Math.max(1, parseInt(e.target.value, 10)), 10))}
                          disabled={isGenerating}
                        />
                      </div>
                    )}

                    {nestingDepth >= 3 && (
                      <div className="level-count">
                        <span>Level 3:</span>
                        <input
                          type="number"
                          min="1"
                          max="5"
                          value={level3Count}
                          onChange={(e) => setLevel3Count(Math.min(Math.max(1, parseInt(e.target.value, 10)), 5))}
                          disabled={isGenerating}
                        />
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Sub-Element Count for Nested Mode */}
          {nestedMode && (
            <div className="form-group">
              <div className="sub-element-count">
                <label htmlFor="sub-element-count">Number of sub-elements (1-20):</label>
                <input
                  id="sub-element-count"
                  type="number"
                  min="1"
                  max="20"
                  value={subElementCount}
                  onChange={(e) => setSubElementCount(Math.min(Math.max(1, parseInt(e.target.value, 10)), 20))}
                  disabled={isGenerating}
                />
              </div>
            </div>
          )}

          {/* Parent Selector (always visible) */}
          <div className="form-group">
            <div className="parent-selector-container">
              <label htmlFor="parent-element">Parent Element (optional):</label>
              <select
                id="parent-element"
                value={selectedParentId}
                onChange={(e) => setSelectedParentId(e.target.value)}
                disabled={isGenerating || (nestedMode && !multiLevelMode)}
              >
                <option value="">-- No parent (create top-level element) --</option>
                {parentElements.map(element => (
                  <option key={element.element_id} value={element.element_id}>
                    {element.name} ({element.element_type})
                  </option>
                ))}
              </select>

              {parentElements.length === 0 && (
                <p className="no-parents-message">
                  No potential parent elements found. Create some elements first.
                </p>
              )}

              {selectedParentId && (
                <div className="selected-parent-info">
                  <h5>Selected Parent:</h5>
                  <p>
                    {parentElements.find(e => e.element_id === selectedParentId)?.name}
                  </p>
                </div>
              )}
            </div>
          </div>

          {error && <div className="error-message">{error}</div>}

          <button
            className="generate-button"
            onClick={handleGenerate}
            disabled={isGenerating || !prompt.trim() || !categoryId}
          >
            {isGenerating ? 'Generating...' : 'Generate Elements'}
          </button>
        </div>
      ) : (
        // Preview of generated elements
        <div className="preview-container">
          <h4>Preview Generated Elements</h4>
          <p className="preview-description">Review these elements before adding them to your world:</p>

          <div className="preview-elements">
            {/* Group elements by hierarchy for multi-level mode */}
            {multiLevelMode ? (
              <div className="hierarchical-preview">
                {/* Group elements by level */}
                {Array.from(new Set(generatedPreview.map(e => e._level || 0))).sort().map(level => (
                  <div key={`level-${level}`} className="hierarchy-level">
                    <h4 className="level-header">Level {level + 1}</h4>

                    <div className="level-elements">
                      {generatedPreview
                        .filter(e => (e._level || 0) === level)
                        .map((element, index) => (
                          <div
                            key={`element-${level}-${index}`}
                            className={`preview-element level-${level}-element`}
                          >
                            {element.parent_name && (
                              <div className="parent-info">
                                <span className="parent-label">Parent:</span>
                                <span className="parent-value">{element.parent_name}</span>
                              </div>
                            )}

                            <h5>{element.name}</h5>
                            <p>{element.description}</p>

                            {element.element_type && (
                              <div className="element-type">
                                <span className="type-label">Type:</span>
                                <span className="type-value">{element.element_type}</span>
                              </div>
                            )}

                            {element.custom_fields && Object.keys(element.custom_fields).length > 0 && (
                              <div className="element-custom-fields">
                                {Object.entries(element.custom_fields).map(([key, value]) => (
                                  <div key={key} className="custom-field">
                                    <span className="field-name">{key.replace(/_/g, ' ')}:</span>
                                    <span className="field-value">{value}</span>
                                  </div>
                                ))}
                              </div>
                            )}

                            {element.has_children && (
                              <div className="children-indicator">
                                <span className="children-icon">↓</span>
                                <span className="children-text">Has sub-elements</span>
                              </div>
                            )}
                          </div>
                        ))}
                    </div>
                  </div>
                ))}
              </div>
            ) : nestedMode ? (
              <div className="nested-preview">
                {/* Find and display the parent element first */}
                {generatedPreview.filter(element => element.is_parent).map((parentElement, index) => (
                  <div key={`parent-${index}`} className="preview-element parent-element">
                    <div className="element-badge">Parent Element</div>
                    <h5>{parentElement.name}</h5>
                    <p>{parentElement.description}</p>
                    {parentElement.element_type && (
                      <div className="element-type">
                        <span className="type-label">Type:</span>
                        <span className="type-value">{parentElement.element_type}</span>
                      </div>
                    )}
                    {parentElement.custom_fields && Object.keys(parentElement.custom_fields).length > 0 && (
                      <div className="element-custom-fields">
                        {Object.entries(parentElement.custom_fields).map(([key, value]) => (
                          <div key={key} className="custom-field">
                            <span className="field-name">{key.replace(/_/g, ' ')}:</span>
                            <span className="field-value">{value}</span>
                          </div>
                        ))}
                      </div>
                    )}

                    {/* Display sub-elements under this parent */}
                    <div className="sub-elements-preview">
                      <h6>Sub-Elements:</h6>
                      <div className="sub-elements-list">
                        {generatedPreview.filter(element => element._is_sub_element).map((subElement, subIndex) => (
                          <div key={`sub-${subIndex}`} className="preview-element sub-element">
                            <h5>{subElement.name}</h5>
                            <p>{subElement.description}</p>
                            {subElement.element_type && (
                              <div className="element-type">
                                <span className="type-label">Type:</span>
                                <span className="type-value">{subElement.element_type}</span>
                              </div>
                            )}
                            {subElement.custom_fields && Object.keys(subElement.custom_fields).length > 0 && (
                              <div className="element-custom-fields">
                                {Object.entries(subElement.custom_fields).map(([key, value]) => (
                                  <div key={key} className="custom-field">
                                    <span className="field-name">{key.replace(/_/g, ' ')}:</span>
                                    <span className="field-value">{value}</span>
                                  </div>
                                ))}
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              // Standard display for non-nested elements
              generatedPreview.map((element, index) => (
                <div key={index} className="preview-element">
                  <h5>{element.name}</h5>
                  {element.parent_name && (
                    <div className="parent-info">
                      <span className="parent-label">Parent:</span>
                      <span className="parent-value">{element.parent_name}</span>
                    </div>
                  )}
                  <p>{element.description}</p>
                  {element.element_type && (
                    <div className="element-type">
                      <span className="type-label">Type:</span>
                      <span className="type-value">{element.element_type}</span>
                    </div>
                  )}
                  {element.custom_fields && Object.keys(element.custom_fields).length > 0 && (
                    <div className="element-custom-fields">
                      {Object.entries(element.custom_fields).map(([key, value]) => (
                        <div key={key} className="custom-field">
                          <span className="field-name">{key.replace(/_/g, ' ')}:</span>
                          <span className="field-value">{value}</span>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              ))
            )}
          </div>

          <div className="preview-actions">
            <button
              className="add-button"
              onClick={handleAddToWorld}
            >
              Add to World
            </button>
            <button
              className="regenerate-button"
              onClick={handleRegenerate}
            >
              Regenerate
            </button>
            <button
              className="cancel-button"
              onClick={handleCancelPreview}
            >
              Cancel
            </button>
          </div>
        </div>
      )}

      {!showPreview && (
        <div className="generator-help-section">
          <div className="generator-tips">
            <h4>Tips for better results:</h4>
            <ul>
              <li>Be specific about the type of elements you want</li>
              <li>Include details about your world's setting and genre</li>
              <li>Specify any naming conventions or themes</li>
              <li>Mention relationships to existing elements</li>
              {categoryInfo && categoryInfo.name.includes('Location') && (
                <li>For locations, describe geography, climate, and inhabitants</li>
              )}
              {categoryInfo && categoryInfo.name.includes('Character') && (
                <li>For characters, include personality traits, motivations, and relationships</li>
              )}
              {categoryInfo && categoryInfo.name.includes('Magic') && (
                <li>For magic systems, define sources, limitations, and consequences</li>
              )}
            </ul>
          </div>

          {categoryId && visualAids[categoryId] && (
            <div className="visual-aids-section">
              <div className="visual-aids-header">
                <h4>Visual Examples</h4>
                <button
                  className="toggle-visual-aids"
                  onClick={() => setShowVisualAids(!showVisualAids)}
                >
                  {showVisualAids ? 'Hide Examples' : 'Show Examples'}
                </button>
              </div>

              {showVisualAids && (
                <div className="visual-aids-gallery">
                  {visualAids[categoryId].map((aid, index) => (
                    <div key={index} className="visual-aid-card">
                      <img src={aid.url} alt={aid.title} className="visual-aid-image" />
                      <div className="visual-aid-info">
                        <h5>{aid.title}</h5>
                        <p>{aid.description}</p>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default WorldElementGenerator;

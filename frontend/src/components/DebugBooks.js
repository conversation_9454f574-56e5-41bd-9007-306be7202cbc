// frontend/src/components/DebugBooks.js
import React, { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { fetchAllBooks } from '../redux/slices/bookSlice';
import { BASE_URL } from '../utils/apiConfig';

/**
 * Debug component to check if books are being loaded correctly
 */
const DebugBooks = () => {
  const dispatch = useDispatch();
  const booksState = useSelector(state => state.books);
  const authState = useSelector(state => state.auth);

  useEffect(() => {
    // Manually fetch books
    dispatch(fetchAllBooks())
      .then(result => {
        console.log('DebugBooks: Books fetched successfully:', result);
      })
      .catch(error => {
        console.error('DebugBooks: Error fetching books:', error);
      });
  }, [dispatch]);

  const handleManualFetch = () => {
    fetch(`${BASE_URL}/books`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    })
    .then(response => {
      console.log('Books API response:', response);
      return response.json();
    })
    .then(data => {
      console.log('Books data:', data);
      alert('Books data: ' + JSON.stringify(data));
    })
    .catch(error => {
      console.error('Error fetching books:', error);
      alert('Error fetching books: ' + error.message);
    });
  };

  const handleCreateBook = () => {
    const bookData = {
      title: 'Test Book ' + new Date().toISOString(),
      author: 'Test Author',
      genre: 'Test Genre'
    };

    fetch(`${BASE_URL}/books`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      },
      body: JSON.stringify(bookData)
    })
    .then(response => {
      console.log('Create book API response:', response);
      return response.json();
    })
    .then(data => {
      console.log('Created book data:', data);
      alert('Created book: ' + JSON.stringify(data));
      // Refresh books
      dispatch(fetchAllBooks());
    })
    .catch(error => {
      console.error('Error creating book:', error);
      alert('Error creating book: ' + error.message);
    });
  };

  return (
    <div style={{ padding: '20px' }}>
      <h2>Debug Books</h2>

      <div style={{ marginBottom: '20px' }}>
        <h3>Authentication State</h3>
        <pre>{JSON.stringify(authState, null, 2)}</pre>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h3>Books State</h3>
        <pre>{JSON.stringify(booksState, null, 2)}</pre>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h3>Actions</h3>
        <button
          onClick={handleManualFetch}
          style={{ marginRight: '10px', padding: '10px' }}
        >
          Manual Fetch Books
        </button>

        <button
          onClick={handleCreateBook}
          style={{ padding: '10px' }}
        >
          Create Test Book
        </button>
      </div>
    </div>
  );
};

export default DebugBooks;

// frontend/src/components/WritePage.js
import React, { useState, useEffect } from 'react';
import TextEditor from './TextEditor';
import ChapterSidebarContainer from '../containers/ChapterSidebarContainer';
import EventSidebarContainer from '../containers/EventSidebarContainer';
import ReferencePanelReduxContainer from '../containers/ReferencePanelReduxContainer';
import WorldElementContextPanel from './WorldElementContextPanel';
import { useNavigate } from 'react-router-dom';
import './WritePage.css';

/**
 * Presentation component for WritePage
 * @param {Object} props - Component props
 * @returns {JSX.Element} Write page UI
 */
const WritePage = React.memo((props) => {
  const {
    currentChapter,
    chapters,
    chapterContent,

    isLoading,
    isSaving,
    showReferencePanel,
    showEventSidebar,
    showChapterSidebar,
    errorMessage,

    handleContentChange,
    handleChapterSelect,
    toggleReferencePanel,
    toggleEventSidebar,
    toggleChapterSidebar,
    clearError
  } = props;

  const navigate = useNavigate();
  const [editorContent, setEditorContent] = useState('');

  // Update editor content when chapterContent changes
  useEffect(() => {
    if (chapterContent) {
      try {
        // If chapterContent is a JSON string (Draft.js format), extract the text
        const contentObj = JSON.parse(chapterContent);
        if (contentObj.blocks) {
          const plainText = contentObj.blocks.map(block => block.text).join('\n');
          setEditorContent(plainText);
        } else {
          setEditorContent(chapterContent);
        }
      } catch (e) {
        // If not valid JSON, use as is
        setEditorContent(chapterContent);
      }
    } else {
      setEditorContent('');
    }
  }, [chapterContent]);

  // Handle element selection
  const handleElementSelect = (elementId) => {
    // Navigate to the world building page with the selected element
    navigate(`/world-building?element=${elementId}`);
  };

  return (
    <div className="write-page">
      <div className="write-page-header">
        <h2>Write</h2>
        <div className="write-page-actions">
          <button
            className={`toggle-reference-button ${showReferencePanel ? 'active' : ''}`}
            onClick={toggleReferencePanel}
            title="Toggle Reference Panel"
          >
            Reference
          </button>
        </div>
      </div>

      <div className="write-page-content">
        {/* Always show chapter sidebar */}
        <ChapterSidebarContainer />

        <div className="editor-container">
          {!currentChapter ? (
            <div className="no-chapter-selected">
              <h3>No Chapter Selected</h3>
              <p>Please select a chapter from the sidebar to start writing.</p>
              {chapters && chapters.length === 0 && (
                <p>No chapters available. Create chapters in the Plot page first.</p>
              )}
            </div>
          ) : (
            <>
              <div className="editor-header">
                <h3>{currentChapter.title}</h3>
                {isSaving && <span className="saving-indicator">Saving...</span>}
              </div>

              {isLoading ? (
                <div className="loading-indicator">Loading chapter content...</div>
              ) : (
                <TextEditor
                  content={chapterContent}
                  onChange={handleContentChange}
                  placeholder="Start writing your chapter here..."
                />
              )}
            </>
          )}

          {errorMessage && (
            <div className="error-message">
              <p>{errorMessage}</p>
              <button onClick={clearError}>Dismiss</button>
            </div>
          )}
        </div>

        {/* Always show event sidebar */}
        <EventSidebarContainer
          currentChapter={currentChapter}
        />

        {/* Reference panel with slide-out animation */}
        <div className={`reference-panel ${showReferencePanel ? 'visible' : ''}`}>
          <ReferencePanelReduxContainer />
        </div>

        {/* World element context panel */}
        <div className="world-context-panel">
          <WorldElementContextPanel
            content={editorContent}
            onElementSelect={handleElementSelect}
          />
        </div>
      </div>
    </div>
  );
});

export default WritePage;

/* frontend/src/components/WorldElementEnhancer.css */
.world-element-enhancer {
  background: var(--background-secondary);
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.world-element-enhancer h3 {
  margin: 0 0 15px 0;
  color: var(--text-primary);
  font-size: 1.2rem;
}

.world-element-enhancer.empty-state {
  text-align: center;
  padding: 30px 20px;
}

.world-element-enhancer.empty-state p {
  color: var(--text-secondary);
  margin: 0;
}

.enhancement-types {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 15px;
}

.enhancement-type-button {
  padding: 8px 12px;
  background: var(--background-tertiary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.2s;
}

.enhancement-type-button:hover:not(:disabled) {
  background: var(--background-primary);
  border-color: var(--primary);
}

.enhancement-type-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.enhancer-form {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.form-group label {
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.form-group textarea {
  padding: 10px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background: var(--background-primary);
  color: var(--text-primary);
  font-family: inherit;
  resize: vertical;
}

.error-message {
  color: var(--error);
  font-size: 0.9rem;
  padding: 5px 0;
}

.enhance-button {
  padding: 10px 16px;
  background: var(--primary);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: background 0.2s;
  align-self: flex-start;
}

.enhance-button:hover:not(:disabled) {
  background: var(--button-primary-hover);
}

.enhance-button:disabled {
  background: var(--background-tertiary);
  color: var(--text-secondary);
  cursor: not-allowed;
}

/* Dark theme adjustments */
.theme-dark .world-element-enhancer {
  background: var(--background-tertiary);
}

.theme-dark .enhancement-type-button {
  background: var(--background-primary);
}

.theme-dark .enhancement-type-button:hover:not(:disabled) {
  background: var(--background-secondary);
}

/* Responsive styles */
@media (max-width: 768px) {
  .world-element-enhancer {
    padding: 15px;
  }
  
  .enhancement-types {
    flex-direction: column;
  }
}

/* frontend/src/components/WorldElementContextPanel.css */
.world-element-context-panel {
  background-color: var(--background-secondary);
  border-radius: 6px;
  padding: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.context-panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.context-panel-header h3 {
  margin: 0;
  font-size: 1.1rem;
  color: var(--text-primary);
}

.loading-indicator {
  font-size: 0.8rem;
  color: var(--text-secondary);
  font-style: italic;
}

.element-type-filters {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 16px;
}

.type-filter {
  padding: 4px 8px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--background-primary);
  color: var(--text-primary);
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.type-filter:hover {
  background-color: var(--background-hover);
}

.type-filter.active {
  background-color: var(--accent-color-light);
  color: var(--accent-color-dark);
  border-color: var(--accent-color);
}

.relevant-elements-list {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.relevant-element-item {
  padding: 12px;
  background-color: var(--background-primary);
  border-radius: 4px;
  border: 1px solid var(--border-color);
  cursor: pointer;
  transition: all 0.2s ease;
}

.relevant-element-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.relevant-element-item.name {
  border-left: 3px solid var(--accent-color);
}

.relevant-element-item.attribute {
  border-left: 3px solid var(--text-tertiary);
}

.element-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.element-name {
  font-weight: 500;
  color: var(--text-primary);
}

.element-type {
  font-size: 0.8rem;
  color: var(--text-secondary);
  padding: 2px 6px;
  background-color: var(--background-tertiary);
  border-radius: 3px;
}

.element-relevance {
  height: 4px;
  background-color: var(--background-tertiary);
  border-radius: 2px;
  margin-bottom: 8px;
  overflow: hidden;
}

.relevance-bar {
  height: 100%;
  background-color: var(--accent-color);
  border-radius: 2px;
}

.element-preview {
  font-size: 0.9rem;
  color: var(--text-secondary);
  line-height: 1.4;
  max-height: 60px;
  overflow: hidden;
}

.no-relevant-elements {
  padding: 20px;
  text-align: center;
  color: var(--text-tertiary);
  background-color: var(--background-primary);
  border-radius: 4px;
  border: 1px dashed var(--border-color);
  font-style: italic;
}

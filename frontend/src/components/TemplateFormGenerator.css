/* frontend/src/components/TemplateFormGenerator.css */
.template-form-generator {
  margin-bottom: 20px;
}

.basic-fields {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--border-color);
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: var(--text-primary);
}

.form-group input[type="text"],
.form-group input[type="number"],
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  font-size: 14px;
  background-color: var(--background-primary);
  color: var(--text-primary);
}

.form-group textarea {
  resize: vertical;
  min-height: 80px;
}

.form-group input[type="checkbox"] {
  margin-right: 8px;
}

.required-marker {
  color: #f44336;
  margin-left: 4px;
}

.field-description {
  font-size: 0.8rem;
  color: var(--text-tertiary);
  margin-top: 4px;
  font-style: italic;
}

.field-group {
  margin-bottom: 24px;
}

.group-title {
  font-size: 1rem;
  margin: 0 0 12px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--border-color-light);
  color: var(--text-primary);
}

.group-fields {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.template-form-error {
  padding: 16px;
  background-color: #ffebee;
  border: 1px solid #ffcdd2;
  border-radius: 4px;
  color: #c62828;
  margin-bottom: 20px;
}

@media (max-width: 768px) {
  .group-fields {
    grid-template-columns: 1fr;
  }
}

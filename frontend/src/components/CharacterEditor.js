// frontend/src/components/CharacterEditor.js
import React, { useState, useEffect } from 'react';
import './CharacterEditor.css';
import { BASE_URL } from '../utils/apiConfig';

/**
 * Helper function to get a label for relationship strength
 * @param {number} strength - Relationship strength (0-5)
 * @returns {string} Label describing the relationship strength
 */
const getStrengthLabel = (strength) => {
  switch (parseInt(strength, 10)) {
    case 0: return "Enemy";
    case 1: return "Acquaintance";
    case 2: return "Casual friend";
    case 3: return "Close friend";
    case 4: return "Deep bond";
    case 5: return "Intimate connection";
    default: return "Close friend";
  }
};

/**
 * CharacterEditor component for creating and editing characters
 * @param {Object} props - Component props
 * @returns {JSX.Element} CharacterEditor UI
 */
const CharacterEditor = ({
  character,
  characters,
  onSave,
  onCancel,
  onGenerateHeadshot,
  onDeleteHeadshot,
  onGenerateName,
  onGenerateAge,
  onGenerateRace,
  onGenerateDescription,
  onGenerateTraits,
  onGenerateBackstory,
  onGenerateArc,
  onGenerateRelationships,
  onGenerateGenderIdentity,
  onGenerateSexualOrientation,
  isLoading
}) => {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    traits: '',
    age: '',
    role: '',
    relationships: '',
    race: '',
    headshot: '',
    backstory: '',
    arc: '',
    gender_identity: '',
    sexual_orientation: ''
  });
  const [errors, setErrors] = useState({});
  const [deleteHeadshotConfirm, setDeleteHeadshotConfirm] = useState(false);
  const [previewMode, setPreviewMode] = useState(false);

  // Initialize form data when character changes
  useEffect(() => {
    if (character) {
      console.log('CharacterEditor: character prop changed:', character);
      console.log('CharacterEditor: received headshot:', character.headshot);

      // Preserve the current headshot if it's in 'generating' state
      // This prevents the form from resetting during headshot generation
      const currentHeadshot = formData.headshot === 'generating' ? 'generating' : character.headshot || '';

      // Log the URL that will be used for the image
      if (character.headshot) {
        const imageUrl = character.headshot === 'generating'
          ? ''
          : (character.headshot.startsWith('http')
              ? character.headshot
              : (character.headshot.startsWith('/')
                  ? `${BASE_URL}${character.headshot}`
                  : character.headshot));
        console.log('CharacterEditor: Image URL that will be used:', imageUrl);
        console.log('CharacterEditor: BASE_URL is:', BASE_URL);
      }

      // Format relationships
      let formattedRelationships = '';
      if (Array.isArray(character.relationships)) {
        formattedRelationships = character.relationships.map(r => {
          const name = r.name || r.relatedCharacterName || 'Unknown';
          const type = r.type || r.relationshipType || 'Unknown';
          const description = r.description || '';
          const strength = r.strength !== undefined ? r.strength : 3; // Default to 3 if not specified

          // Include strength in the formatted string
          return `${name}:${type}:${description}:${strength}`;
        }).join('\n');
        console.log('CharacterEditor: formatted relationships from array with strength:', formattedRelationships);
      } else if (typeof character.relationships === 'string') {
        // If relationships is already a string, use it directly
        formattedRelationships = character.relationships;
        console.log('CharacterEditor: relationships already a string:', formattedRelationships);
      }

      const newFormData = {
        id: character.id || null,
        name: character.name || '',
        description: character.description || '',
        traits: Array.isArray(character.traits) ? character.traits.join('\n') : (typeof character.traits === 'string' ? character.traits : ''),
        age: character.age || '',
        role: character.role || '',
        relationships: formattedRelationships,
        race: character.race || '',
        headshot: currentHeadshot,
        backstory: character.backstory || '',
        arc: character.arc || '',
        gender_identity: character.gender_identity || '',
        sexual_orientation: character.sexual_orientation || ''
      };

      console.log('CharacterEditor: setting new form data:', newFormData);
      setFormData(newFormData);
    }
  }, [character]); // Only depend on character changes

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear error for this field if it exists
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: null
      }));
    }
  };

  // Validate form before submission
  const validateForm = () => {
    const newErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Description is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    // Format the data for saving
    const characterData = {
      ...formData,
      id: formData.id || undefined,
      traits: formData.traits.split('\n').filter(t => t.trim() !== ''),
      relationships: formData.relationships.split('\n')
        .filter(r => r.trim() !== '')
        .map(r => {
          const parts = r.split(':').map(s => s.trim());
          const name = parts[0];
          const type = parts[1] || 'Friend';

          // Check if the third part is a number (strength) or description
          let description = '';
          let strength = 3; // Default to 3 (close friend)

          if (parts.length >= 3) {
            // Try to parse the third part as a number
            const thirdPartAsNumber = parseInt(parts[2], 10);

            if (!isNaN(thirdPartAsNumber) && thirdPartAsNumber >= 0 && thirdPartAsNumber <= 5) {
              // If third part is a valid strength number (0-5)
              strength = thirdPartAsNumber;
              // Description is the fourth part if it exists
              description = parts[3] || '';
            } else {
              // If third part is not a valid strength number, it's the description
              description = parts[2] || '';
              // Check if fourth part is a valid strength
              if (parts.length >= 4) {
                const fourthPartAsNumber = parseInt(parts[3], 10);
                if (!isNaN(fourthPartAsNumber) && fourthPartAsNumber >= 0 && fourthPartAsNumber <= 5) {
                  strength = fourthPartAsNumber;
                }
              }
            }
          }

          // If no strength specified, infer from relationship type
          if (parts.length < 4 || isNaN(parseInt(parts[3], 10))) {
            if (type.toLowerCase() === 'enemy') strength = 0;
            else if (type.toLowerCase() === 'acquaintance') strength = 1;
            else if (type.toLowerCase() === 'friend') strength = 2;
            else if (type.toLowerCase() === 'close friend') strength = 3;
            else if (type.toLowerCase() === 'best friend') strength = 4;
            else if (type.toLowerCase() === 'lover' || type.toLowerCase() === 'spouse') strength = 5;
          }

          // Find the character ID for this name
          let relatedCharacterId = null;
          if (characters && Array.isArray(characters)) {
            const relatedCharacter = characters.find(c => c.name === name);
            if (relatedCharacter) {
              relatedCharacterId = relatedCharacter.id;
            }
          }

          // Return the relationship in the format expected by the backend
          return {
            name: name,                           // For frontend display
            type: type,                           // For frontend display
            description: description,             // For frontend display
            strength: strength,                   // Relationship strength (0-5)
            relatedCharacterId: relatedCharacterId, // For backend database
            relationshipType: type,               // For backend database
            relatedCharacterName: name            // For backend reference
          };
        })
    };

    console.log('Saving character with formatted relationships:', characterData.relationships);
    onSave(characterData);
  };

  // Handle headshot generation
  const handleGenerateHeadshot = () => {
    if (!formData.name || !formData.description) {
      setErrors(prev => ({
        ...prev,
        headshot: 'Name and description are required to generate a headshot'
      }));
      return;
    }

    // Prepare the character data for headshot generation
    // Convert traits from string to array
    const characterData = {
      ...formData,
      traits: formData.traits.split('\n').filter(t => t.trim() !== '')
    };

    console.log('Sending character data for headshot generation:', characterData);
    onGenerateHeadshot(characterData);
  };

  // Handle headshot deletion
  const handleDeleteHeadshot = () => {
    onDeleteHeadshot();
    setFormData(prev => ({
      ...prev,
      headshot: ''
    }));
    setDeleteHeadshotConfirm(false);
  };

  // Toggle preview mode
  const togglePreviewMode = () => {
    setPreviewMode(prev => {
      const newPreviewMode = !prev;
      // Scroll to top when entering preview mode
      if (newPreviewMode) {
        setTimeout(() => {
          const previewContent = document.querySelector('.preview-content');
          if (previewContent) {
            previewContent.scrollTop = 0;
          }
        }, 0);
      }
      return newPreviewMode;
    });
  };

  // Render character preview
  const renderPreview = () => {
    const previewData = {
      ...formData,
      traits: formData.traits.split('\n').filter(t => t.trim() !== ''),
      relationships: formData.relationships.split('\n')
        .filter(r => r.trim() !== '')
        .map(r => {
          const parts = r.split(':').map(s => s.trim());
          const name = parts[0];
          const type = parts[1] || 'Friend';

          // Check if the third part is a number (strength) or description
          let description = '';
          let strength = 3; // Default to 3 (close friend)

          if (parts.length >= 3) {
            // Try to parse the third part as a number
            const thirdPartAsNumber = parseInt(parts[2], 10);

            if (!isNaN(thirdPartAsNumber) && thirdPartAsNumber >= 0 && thirdPartAsNumber <= 5) {
              // If third part is a valid strength number (0-5)
              strength = thirdPartAsNumber;
              // Description is the fourth part if it exists
              description = parts[3] || '';
            } else {
              // If third part is not a valid strength number, it's the description
              description = parts[2] || '';
              // Check if fourth part is a valid strength
              if (parts.length >= 4) {
                const fourthPartAsNumber = parseInt(parts[3], 10);
                if (!isNaN(fourthPartAsNumber) && fourthPartAsNumber >= 0 && fourthPartAsNumber <= 5) {
                  strength = fourthPartAsNumber;
                }
              }
            }
          }

          // If no strength specified, infer from relationship type
          if (parts.length < 4 || isNaN(parseInt(parts[3], 10))) {
            if (type.toLowerCase() === 'enemy') strength = 0;
            else if (type.toLowerCase() === 'acquaintance') strength = 1;
            else if (type.toLowerCase() === 'friend') strength = 2;
            else if (type.toLowerCase() === 'close friend') strength = 3;
            else if (type.toLowerCase() === 'best friend') strength = 4;
            else if (type.toLowerCase() === 'lover' || type.toLowerCase() === 'spouse') strength = 5;
          }

          // Find the character ID for this name
          let relatedCharacterId = null;
          if (characters && Array.isArray(characters)) {
            const relatedCharacter = characters.find(c => c.name === name);
            if (relatedCharacter) {
              relatedCharacterId = relatedCharacter.id;
            }
          }

          return {
            name,
            type,
            description,
            strength,
            relatedCharacterId,
            relationshipType: type,
            relatedCharacterName: name
          };
        })
    };

    return (
      <div className="character-preview">
        <div className="preview-header">
          <h3>Preview: {previewData.name || 'New Character'}</h3>
          <button type="button" onClick={togglePreviewMode} className="preview-toggle-button">
            Back to Edit
          </button>
        </div>

        <div className="preview-content">
          <div className="preview-main">
            {previewData.headshot ? (
              <div className="preview-headshot">
                {previewData.headshot === 'generating' ? (
                  <div className="headshot-generating">
                    <div className="headshot-loading-spinner"></div>
                    <p>Generating headshot...</p>
                  </div>
                ) : (
                  <img
                    src={previewData.headshot === 'generating' ? '' : (previewData.headshot.startsWith('http') ? previewData.headshot : `${BASE_URL}${previewData.headshot}`)}
                    alt={previewData.name}
                    onError={(e) => {
                      console.error('Error loading headshot in preview:', previewData.headshot);
                      e.target.onerror = null;
                      e.target.src = 'https://via.placeholder.com/150?text=' + encodeURIComponent((previewData.name || '?').charAt(0));
                    }}
                  />
                )}
              </div>
            ) : (
              <div className="preview-headshot placeholder">
                <span>{previewData.name ? previewData.name.charAt(0) : '?'}</span>
              </div>
            )}

            <div className="preview-info">
              <h2>{previewData.name || 'Unnamed Character'}</h2>

              {previewData.role && (
                <div className="preview-detail">
                  <span className="preview-label">Role:</span> {previewData.role}
                </div>
              )}

              {previewData.age && (
                <div className="preview-detail">
                  <span className="preview-label">Age:</span> {previewData.age}
                </div>
              )}

              {previewData.race && (
                <div className="preview-detail">
                  <span className="preview-label">Race:</span> {previewData.race}
                </div>
              )}

              {previewData.gender_identity && (
                <div className="preview-detail">
                  <span className="preview-label">Gender Identity:</span> {previewData.gender_identity}
                </div>
              )}

              {previewData.sexual_orientation && (
                <div className="preview-detail">
                  <span className="preview-label">Sexual Orientation:</span> {previewData.sexual_orientation}
                </div>
              )}


            </div>
          </div>

          {previewData.description && (
            <div className="preview-section">
              <h4>Physical Description</h4>
              <p>{previewData.description}</p>
            </div>
          )}

          {previewData.traits && previewData.traits.length > 0 && (
            <div className="preview-section">
              <h4>Traits</h4>
              <div className="preview-traits">
                {previewData.traits.map((trait, index) => (
                  <span key={index} className="preview-trait">{trait}</span>
                ))}
              </div>
            </div>
          )}

          {previewData.relationships && previewData.relationships.length > 0 && (
            <div className="preview-section">
              <h4>Relationships</h4>
              <ul className="preview-relationships">
                {previewData.relationships.map((rel, index) => (
                  <li key={index}>
                    <strong>{rel.name}</strong>: {rel.type}
                    <span className="relationship-strength">({getStrengthLabel(rel.strength)})</span>
                    {rel.description && <div className="preview-relationship-description">{rel.description}</div>}
                  </li>
                ))}
              </ul>
            </div>
          )}

          {previewData.backstory && (
            <div className="preview-section">
              <h4>Backstory</h4>
              <p>{previewData.backstory}</p>
            </div>
          )}

          {previewData.arc && (
            <div className="preview-section">
              <h4>Character Arc</h4>
              <p>{previewData.arc}</p>
            </div>
          )}
        </div>

        <div className="preview-actions">
          <button type="button" onClick={handleSubmit} className="save-button">
            {formData.id ? 'Save Changes' : 'Create Character'}
          </button>
          <button type="button" onClick={onCancel} className="cancel-button">
            Cancel
          </button>
        </div>
      </div>
    );
  };

  // Render form
  const renderForm = () => {
    return (
      <form className="character-editor-form">
        <div className="form-header">
          <h3>{formData.id ? 'Edit Character' : 'Create New Character'}</h3>
          <div className="form-header-actions">
            <button type="button" onClick={togglePreviewMode} className="preview-button">
              Preview
            </button>
            <button type="button" onClick={onCancel} className="close-button">
              ×
            </button>
          </div>
        </div>



        <div className="form-content">
          <div className="form-columns">
            <div className="form-column">
              <div className="form-group">
                <label htmlFor="name">
                  Name *
                  <button
                    type="button"
                    className="generate-button-inline"
                    onClick={() => onGenerateName(formData)}
                    disabled={isLoading}
                    title="Generate Name"
                  >
                    {isLoading ? <div className="loading-spinner"></div> : <span className="generate-icon">↻</span>}
                  </button>
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  className={errors.name ? 'error' : ''}
                />
                {errors.name && <div className="error-message">{errors.name}</div>}
              </div>

              <div className="form-group">
                <label htmlFor="role">Role</label>
                <select
                  id="role"
                  name="role"
                  value={formData.role}
                  onChange={handleInputChange}
                >
                  <option value="">Select Role</option>
                  <option value="protagonist">Protagonist</option>
                  <option value="antagonist">Antagonist</option>
                  <option value="supporting">Supporting Character</option>
                  <option value="mentor">Mentor</option>
                  <option value="sidekick">Sidekick</option>
                  <option value="love_interest">Love Interest</option>
                  <option value="foil">Foil</option>
                  <option value="other">Other</option>
                </select>
              </div>

              <div className="form-group">
                <label htmlFor="age">
                  Age
                  <button
                    type="button"
                    className="generate-button-inline"
                    onClick={() => onGenerateAge(formData)}
                    disabled={isLoading}
                    title="Generate Age"
                  >
                    {isLoading ? <div className="loading-spinner"></div> : <span className="generate-icon">↻</span>}
                  </button>
                </label>
                <input
                  type="text"
                  id="age"
                  name="age"
                  value={formData.age}
                  onChange={handleInputChange}
                />
              </div>

              <div className="form-group">
                <label htmlFor="race">
                  Race
                  <button
                    type="button"
                    className="generate-button-inline"
                    onClick={() => onGenerateRace(formData)}
                    disabled={isLoading}
                    title="Generate Race"
                  >
                    {isLoading ? <div className="loading-spinner"></div> : <span className="generate-icon">↻</span>}
                  </button>
                </label>
                <input
                  type="text"
                  id="race"
                  name="race"
                  value={formData.race}
                  onChange={handleInputChange}
                />
              </div>

              <div className="form-group">
                <label htmlFor="gender_identity">
                  Gender Identity
                  <button
                    type="button"
                    className="generate-button-inline"
                    onClick={() => onGenerateGenderIdentity(formData)}
                    disabled={isLoading}
                    title="Generate Gender Identity"
                  >
                    {isLoading ? <div className="loading-spinner"></div> : <span className="generate-icon">↻</span>}
                  </button>
                </label>
                <input
                  type="text"
                  id="gender_identity"
                  name="gender_identity"
                  value={formData.gender_identity}
                  onChange={handleInputChange}
                />
              </div>

              <div className="form-group">
                <label htmlFor="sexual_orientation">
                  Sexual Orientation
                  <button
                    type="button"
                    className="generate-button-inline"
                    onClick={() => onGenerateSexualOrientation(formData)}
                    disabled={isLoading}
                    title="Generate Sexual Orientation"
                  >
                    {isLoading ? <div className="loading-spinner"></div> : <span className="generate-icon">↻</span>}
                  </button>
                </label>
                <input
                  type="text"
                  id="sexual_orientation"
                  name="sexual_orientation"
                  value={formData.sexual_orientation}
                  onChange={handleInputChange}
                />
              </div>


            </div>

            <div className="form-column">
              <div className="headshot-section">
                <label>Character Headshot</label>

                {formData.headshot ? (
                  <div className="headshot-preview">
                    {formData.headshot === 'generating' ? (
                      <div className="headshot-generating">
                        <div className="headshot-loading-spinner"></div>
                        <p>Generating headshot...</p>
                        <button
                          type="button"
                          className="cancel-generation-button"
                          onClick={handleDeleteHeadshot}
                        >
                          Cancel Generation
                        </button>
                      </div>
                    ) : (
                      <>
                        {/* Don't try to render an image if we're in generating state */}
                        {formData.headshot !== 'generating' && (
                          <img
                            src={(formData.headshot.startsWith('http')
                                  ? formData.headshot
                                  : (formData.headshot.startsWith('/')
                                      ? (() => {
                                          // Properly encode the URL
                                          const pathParts = formData.headshot.split('/');
                                          const fileName = pathParts[pathParts.length - 1];
                                          const basePath = pathParts.slice(0, pathParts.length - 1).join('/');
                                          const encodedFileName = encodeURIComponent(fileName);
                                          const encodedPath = `${basePath}/${encodedFileName}`;
                                          return `${BASE_URL}${encodedPath}`;
                                        })()
                                      : formData.headshot))}
                            alt={formData.name || 'Character'}
                            onError={(e) => {
                              console.error('Error loading headshot:', formData.headshot);
                              console.error('Attempted URL:', e.target.src);
                              e.target.onerror = null;
                              e.target.src = 'https://via.placeholder.com/150?text=' + encodeURIComponent((formData.name || '?').charAt(0));
                            }}
                          />
                        )}

                        {deleteHeadshotConfirm ? (
                          <div className="delete-confirm">
                            <p>Delete this headshot?</p>
                            <div className="confirm-buttons">
                              <button type="button" onClick={handleDeleteHeadshot}>Yes</button>
                              <button type="button" onClick={() => setDeleteHeadshotConfirm(false)}>No</button>
                            </div>
                          </div>
                        ) : (
                          <button
                            type="button"
                            className="delete-headshot-button"
                            onClick={() => setDeleteHeadshotConfirm(true)}
                          >
                            Delete Headshot
                          </button>
                        )}
                      </>
                    )}
                  </div>
                ) : (
                  <div className="generate-headshot">
                    <div className="headshot-placeholder">
                      <span>{formData.name ? formData.name.charAt(0) : '?'}</span>
                    </div>
                    <button
                      type="button"
                      className="generate-headshot-button"
                      onClick={handleGenerateHeadshot}
                      disabled={isLoading || !formData.name || !formData.description}
                    >
                      {isLoading ? (
                        <>
                          <span className="loading-spinner-small"></span>
                          Generating...
                        </>
                      ) : 'Generate Headshot'}
                    </button>
                    <div className="headshot-style-info">
                      Using style from profile settings
                    </div>
                    {errors.headshot && <div className="error-message">{errors.headshot}</div>}
                  </div>
                )}
              </div>
            </div>
          </div>

          <div className="form-group full-width">
            <label htmlFor="description">
              Physical Description *
              <button
                type="button"
                className="generate-button-inline"
                onClick={() => onGenerateDescription(formData)}
                disabled={isLoading}
                title="Generate Physical Description"
              >
                {isLoading ? <div className="loading-spinner"></div> : <span className="generate-icon">↻</span>}
              </button>
            </label>
            <textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              rows={3}
              className={errors.description ? 'error' : ''}
              placeholder="Detailed physical appearance including face, body, clothing, and distinctive features."
            />
            {errors.description && <div className="error-message">{errors.description}</div>}
          </div>

          <div className="form-group full-width">
            <label htmlFor="traits">
              Traits (one per line)
              <button
                type="button"
                className="generate-button-inline"
                onClick={() => {
                  console.log('Generate traits button clicked with formData:', formData);
                  console.log('Current traits value:', formData.traits);
                  onGenerateTraits(formData);
                  // Add a timeout to check if the traits field is updated after the API call
                  setTimeout(() => {
                    console.log('Traits field after API call (timeout):', document.getElementById('traits').value);
                  }, 2000);
                }}
                disabled={isLoading}
                title="Generate Traits"
              >
                {isLoading ? <div className="loading-spinner"></div> : <span className="generate-icon">↻</span>}
              </button>
            </label>
            <textarea
              id="traits"
              name="traits"
              value={formData.traits}
              onChange={handleInputChange}
              rows={3}
              placeholder="Brave\nLoyal\nIntelligent"
            />
          </div>

          <div className="form-group full-width">
            <label htmlFor="relationships">
              Relationships (one per line, format: Name:Type:Description)
              <button
                type="button"
                className="generate-button-inline"
                onClick={() => {
                  console.log('Generate relationships button clicked with formData:', formData);
                  onGenerateRelationships(formData);
                }}
                disabled={isLoading || !characters || characters.length <= 1}
                title="Generate Relationships"
              >
                {isLoading ? <div className="loading-spinner"></div> : <span className="generate-icon">↻</span>}
              </button>
            </label>
            <textarea
              id="relationships"
              name="relationships"
              value={formData.relationships}
              onChange={handleInputChange}
              rows={4}
              placeholder="John:Friend:John has been a loyal friend since childhood:3\nSarah:Mentor:Sarah taught the character everything they know:4\nMike:Enemy:Mike and the character have a long-standing rivalry:0"
            />
            <div className="helper-text">
              Format: Name:Type:Description:Strength OR Name:Type:Strength:Description
            </div>
            <div className="strength-legend">
              <div>Strength levels (0-5):</div>
              <div>0: Enemy, 1: Acquaintance, 2: Casual friend, 3: Close friend, 4: Deep bond, 5: Intimate connection</div>
              <div className="format-examples">
                Examples:<br/>
                John:Friend:Has known the character since childhood:3<br/>
                Sarah:Mentor:4:Taught the character everything they know<br/>
                Mike:Enemy:0:Long-standing rivalry
              </div>
            </div>
          </div>

          <div className="form-group full-width">
            <label htmlFor="backstory">
              Backstory
              <button
                type="button"
                className="generate-button-inline"
                onClick={() => onGenerateBackstory(formData)}
                disabled={isLoading}
                title="Generate Backstory"
              >
                {isLoading ? <div className="loading-spinner"></div> : <span className="generate-icon">↻</span>}
              </button>
            </label>
            <textarea
              id="backstory"
              name="backstory"
              value={formData.backstory}
              onChange={handleInputChange}
              rows={4}
              placeholder="Character's history and background..."
            />
          </div>

          <div className="form-group full-width">
            <label htmlFor="arc">
              Character Arc
              <button
                type="button"
                className="generate-button-inline"
                onClick={() => onGenerateArc(formData)}
                disabled={isLoading}
                title="Generate Character Arc"
              >
                {isLoading ? <div className="loading-spinner"></div> : <span className="generate-icon">↻</span>}
              </button>
            </label>
            <textarea
              id="arc"
              name="arc"
              value={formData.arc}
              onChange={handleInputChange}
              rows={4}
              placeholder="Character's development and growth throughout the story..."
            />
          </div>
        </div>

        <div className="form-actions">
          <button type="button" onClick={togglePreviewMode} className="preview-button">
            Preview
          </button>
          <button type="button" onClick={onCancel} className="cancel-button">
            Cancel
          </button>
        </div>
      </form>
    );
  };

  return (
    <div className="character-editor">
      {previewMode ? renderPreview() : renderForm()}
    </div>
  );
};

export default CharacterEditor;

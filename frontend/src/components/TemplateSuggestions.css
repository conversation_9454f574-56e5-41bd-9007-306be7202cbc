/* frontend/src/components/TemplateSuggestions.css */

.template-suggestions {
  margin: 15px 0;
  border-radius: 8px;
  background-color: var(--card-background);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
}

.template-suggestions.expanded {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.suggestions-header {
  padding: 12px 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: var(--background-secondary);
  border-bottom: 1px solid var(--border-color);
  cursor: pointer;
}

.suggestions-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.suggestion-count {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background-color: var(--primary-color);
  color: white;
  font-size: 12px;
  font-weight: 600;
  width: 20px;
  height: 20px;
  border-radius: 50%;
}

.expand-button {
  background: none;
  border: none;
  font-size: 14px;
  color: var(--text-color);
  cursor: pointer;
  padding: 5px;
}

.suggestions-content {
  padding: 15px;
  max-height: 400px;
  overflow-y: auto;
}

.suggestions-loading {
  text-align: center;
  padding: 20px;
  color: var(--text-secondary);
  font-style: italic;
}

.suggestions-error {
  padding: 10px 15px;
  margin-bottom: 15px;
  background-color: var(--error-background);
  color: var(--error-color);
  border-radius: 4px;
  font-size: 14px;
}

.suggestions-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 15px;
}

.suggestion-card {
  background-color: var(--background-color);
  border-radius: 6px;
  border-left: 4px solid;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  padding: 15px;
  display: flex;
  flex-direction: column;
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.suggestion-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.suggestion-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.suggestion-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  flex: 1;
}

.suggestion-score {
  font-size: 12px;
  font-weight: 600;
  color: var(--success-color);
  background-color: var(--success-background);
  padding: 2px 6px;
  border-radius: 10px;
  white-space: nowrap;
}

.suggestion-relevance {
  font-size: 14px;
  color: var(--text-secondary);
  margin-bottom: 10px;
  flex: 1;
  font-style: italic;
}

.suggestion-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: var(--text-tertiary);
}

.suggestion-category {
  font-style: italic;
}

.no-suggestions {
  text-align: center;
  padding: 20px;
  color: var(--text-secondary);
  font-style: italic;
}

/* frontend/src/components/WorldBuildingPage.css */
.world-building-page {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 50px); /* Full viewport height minus header */
  background: var(--background-secondary);
  overflow: hidden;
}

.world-building-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.world-elements-container {
  display: flex;
  flex: 1;
  overflow: hidden;
  position: relative;
  background: var(--background-primary);
}

.world-ai-panel-container {
  position: absolute;
  top: 0;
  right: 0;
  width: 350px;
  height: 100%;
  z-index: 100;
  box-shadow: var(--shadow-lg);
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from { transform: translateX(100%); }
  to { transform: translateX(0); }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 20px;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 5px solid var(--background-tertiary);
  border-top: 5px solid var(--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 20px;
  text-align: center;
}

.error-container h3 {
  color: var(--error);
  margin-bottom: 10px;
}

.error-container p {
  margin-bottom: 20px;
  max-width: 600px;
}

/* Responsive styles */
@media (max-width: 1200px) {
  .world-elements-container {
    flex-wrap: wrap;
  }

  .world-categories-column,
  .world-elements-column {
    width: 50%;
  }

  .world-element-details-container {
    width: 100%;
    border-top: 1px solid var(--border-color);
  }
}

@media (max-width: 768px) {
  .world-elements-container {
    flex-direction: column;
  }

  .world-categories-column,
  .world-elements-column,
  .world-element-details-container {
    width: 100%;
  }

  .world-ai-panel-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
  }
}

// frontend/src/components/WorldElementDetails.js
import React, { useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';
import './WorldElementDetails.css';
import SubElementsList from './SubElementsList';
import FilteredSubElementsList from './FilteredSubElementsList';
import CreateSubElementModal from './CreateSubElementModal';
import MagicSystemModal from './MagicSystemModal';
import HierarchicalBreadcrumb from './HierarchicalBreadcrumb';
import ElementTreeView from './ElementTreeView';
import TemplateDisplay from './TemplateDisplay';
import TemplateFormGenerator from './TemplateFormGenerator';
import MiniRelationshipMap from './MiniRelationshipMap';
import WorldElementRelationshipManager from './WorldElementRelationshipManager';
import RelationshipAnalytics from './RelationshipAnalytics';
import { getTemplateForElementType, getAvailableSubElementTypes, getElementTypeLabel } from '../utils/templateUtils';
import { getRelationshipTypeLabel } from '../utils/relationshipTypes';
import templateRegistry from '../utils/TemplateRegistry';
import { fetchChildElementsThunk } from '../redux/slices/worldBuildingSlice';

/**
 * WorldElementDetails component for displaying detailed information about a world element
 * @param {Object} props - Component props
 * @returns {JSX.Element} WorldElementDetails UI
 */
const WorldElementDetails = ({
  element,
  ancestors = [],
  childElements = [],
  relationships = [],
  allElements = [],
  isLoading,
  error,
  onUpdateElement,
  onClearError,
  onCreateSubElement,
  onSelectElement,
  onFetchChildElements,
  onDeleteElement,
  onRelationshipCreated,
  onRelationshipUpdated,
  onRelationshipDeleted
}) => {
  const dispatch = useDispatch();
  const [isEditing, setIsEditing] = useState(false);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showMagicSystemModal, setShowMagicSystemModal] = useState(false);
  const [editFormValues, setEditFormValues] = useState(null);
  const [selectedSubElementType, setSelectedSubElementType] = useState(null);
  const [availableSubElementTypes, setAvailableSubElementTypes] = useState([]);
  const [relatedElements, setRelatedElements] = useState([]);
  const [activeTab, setActiveTab] = useState('details'); // 'details', 'relationships', 'children'

  // Get related elements for the relationship map
  useEffect(() => {
    if (!element || !relationships || !allElements) return;

    const relatedElementIds = relationships.map(rel =>
      rel.direction === 'outgoing' ? rel.target_element_id : rel.source_element_id
    );

    const filteredElements = allElements.filter(el =>
      relatedElementIds.includes(el.element_id)
    );

    setRelatedElements(filteredElements);
  }, [element, relationships, allElements]);

  // Initialize edit form values when element changes
  useEffect(() => {
    if (element) {
      setEditFormValues({
        name: element.name,
        description: element.description,
        tags: element.tags,
        importance: element.importance || 'medium',
        custom_fields: element.custom_fields || {}
      });

      // Get available sub-element types
      const subTypes = getAvailableSubElementTypes(element.element_type);
      setAvailableSubElementTypes(subTypes);

      // If no sub-element type is selected, select the first one
      if (subTypes.length > 0 && !selectedSubElementType) {
        setSelectedSubElementType(subTypes[0]);
      }
    }
  }, [element]);

  // Handle form value changes
  const handleFormChange = (values) => {
    setEditFormValues(values);
  };

  // Handle element update
  const handleUpdateElement = () => {
    if (!element || !editFormValues) return;

    // Prepare tags array
    let tags = editFormValues.tags;
    if (typeof tags === 'string') {
      tags = tags.split(',').map(tag => tag.trim()).filter(Boolean);
    }

    const updatedElement = {
      ...element,
      name: editFormValues.name,
      description: editFormValues.description,
      tags,
      importance: editFormValues.importance,
      custom_fields: editFormValues.custom_fields
    };

    onUpdateElement(updatedElement);
    setIsEditing(false);
  };

  // Handle sub-element type selection
  const handleSubElementTypeSelect = (type) => {
    setSelectedSubElementType(type);
  };

  // Handle create sub-element modal open
  const handleOpenCreateModal = () => {
    setShowCreateModal(true);
  };

  // Handle create sub-element modal close
  const handleCloseCreateModal = () => {
    setShowCreateModal(false);
  };

  // Handle create sub-element
  const handleCreateSubElement = (subElement) => {
    onCreateSubElement(subElement);
    setShowCreateModal(false);
  };

  // Handle magic system modal open
  const handleOpenMagicSystemModal = () => {
    setShowMagicSystemModal(true);
  };

  // Handle magic system modal close
  const handleCloseMagicSystemModal = () => {
    setShowMagicSystemModal(false);
  };

  // Handle element deletion
  const handleDeleteElement = () => {
    if (window.confirm(`Are you sure you want to delete "${element.name}"? This action cannot be undone.`)) {
      onDeleteElement(element.element_id);
    }
  };

  // If no element is selected
  if (!element) {
    return (
      <div className="world-element-details empty">
        <p>Select an element to view its details</p>
      </div>
    );
  }

  return (
    <div className="world-element-details">
      {/* Error message */}
      {error && (
        <div className="error-message">
          <p>{error}</p>
          <button onClick={onClearError}>Dismiss</button>
        </div>
      )}

      {/* Breadcrumb navigation */}
      <HierarchicalBreadcrumb
        element={element}
        ancestors={ancestors}
        onNavigate={onSelectElement}
      />

      {/* Element content */}
      <div className="element-content">
        {/* Sidebar with tree view */}
        <div className="element-sidebar">
          <ElementTreeView
            elements={[...ancestors, element, ...childElements]}
            selectedElementId={element.element_id}
            onElementSelect={onSelectElement}
          />

          {/* Create sub-element button */}
          <button
            className="create-sub-element-button"
            onClick={handleOpenCreateModal}
          >
            Create Child Element
          </button>

          {/* Delete element button */}
          <button
            className="delete-element-button"
            onClick={handleDeleteElement}
          >
            Delete Element
          </button>
        </div>

        {/* Main content */}
        <div className="element-main-content">
          {isEditing ? (
            /* Edit mode */
            <div className="element-edit-form">
              <TemplateFormGenerator
                templateId={element.element_type}
                initialValues={editFormValues}
                onChange={handleFormChange}
              />

              <div className="form-actions">
                <button
                  className="cancel-button"
                  onClick={() => setIsEditing(false)}
                >
                  Cancel
                </button>
                <button
                  className="save-button"
                  onClick={handleUpdateElement}
                  disabled={!editFormValues?.name}
                >
                  Save Changes
                </button>
              </div>
            </div>
          ) : (
            /* View mode */
            <>
              {/* Tab navigation */}
              <div className="element-tabs">
                <button
                  className={`element-tab ${activeTab === 'details' ? 'active' : ''}`}
                  onClick={() => setActiveTab('details')}
                >
                  Details
                </button>
                <button
                  className={`element-tab ${activeTab === 'relationships' ? 'active' : ''}`}
                  onClick={() => setActiveTab('relationships')}
                >
                  Relationships {relationships.length > 0 && `(${relationships.length})`}
                </button>
                {childElements.length > 0 && (
                  <button
                    className={`element-tab ${activeTab === 'children' ? 'active' : ''}`}
                    onClick={() => setActiveTab('children')}
                  >
                    Child Elements ({childElements.length})
                  </button>
                )}
              </div>

              {/* Tab content */}
              <div className="element-tab-content">
                {/* Details tab */}
                {activeTab === 'details' && (
                  <TemplateDisplay
                    element={element}
                    onEdit={() => setIsEditing(true)}
                  />
                )}

                {/* Relationships tab */}
                {activeTab === 'relationships' && (
                  <div className="relationships-tab-content">
                    <WorldElementRelationshipManager
                      element={element}
                      relationships={relationships}
                      allElements={allElements}
                      onRelationshipCreated={onRelationshipCreated}
                      onRelationshipUpdated={onRelationshipUpdated}
                      onRelationshipDeleted={onRelationshipDeleted}
                      onSelectElement={onSelectElement}
                    />

                    {/* Relationship map */}
                    <MiniRelationshipMap
                      element={element}
                      relationships={relationships}
                      relatedElements={relatedElements}
                      onElementSelect={onSelectElement}
                      onCreateRelationship={() => setIsCreating(true)}
                    />

                    {/* Relationship analytics */}
                    <RelationshipAnalytics
                      relationships={relationships}
                      onCreateRelationship={(sourceElement, targetElement, relationshipType) => {
                        // Set up the new relationship
                        setNewRelationship({
                          isIncoming: false,
                          targetElementId: targetElement.element_id,
                          relationshipType: relationshipType,
                          description: ''
                        });

                        // Show the relationship creation form
                        setIsCreating(true);
                      }}
                      onSelectElement={onSelectElement}
                    />
                  </div>
                )}

                {/* Child elements tab */}
                {activeTab === 'children' && childElements.length > 0 && (
                  <div className="child-elements-section">
                    <h3>Child Elements</h3>
                    <div className="child-elements-list">
                      {availableSubElementTypes.length > 1 ? (
                        <FilteredSubElementsList
                          childElements={childElements}
                          availableTypes={availableSubElementTypes}
                          selectedType={selectedSubElementType}
                          onTypeSelect={handleSubElementTypeSelect}
                          onElementSelect={onSelectElement}
                        />
                      ) : (
                        <SubElementsList
                          childElements={childElements}
                          onElementSelect={onSelectElement}
                        />
                      )}
                    </div>
                  </div>
                )}
              </div>
            </>
          )}
        </div>
      </div>

      {/* Create sub-element modal */}
      {showCreateModal && (
        <CreateSubElementModal
          parentElement={element}
          onClose={handleCloseCreateModal}
          onCreateElement={handleCreateSubElement}
        />
      )}

      {/* Magic system modal */}
      {showMagicSystemModal && (
        <MagicSystemModal
          onClose={handleCloseMagicSystemModal}
        />
      )}
    </div>
  );
};

export default WorldElementDetails;

// frontend/src/components/WorldBuildingPage.js
import React, { useState } from 'react';
import './WorldBuildingPage.css';
import WorldBuildingHeader from './WorldBuildingHeader';
import WorldBuildingTabs from './WorldBuildingTabs';
import WorldCategoriesColumn from './WorldCategoriesColumn';
import WorldElementsColumn from './WorldElementsColumn';
import WorldElementDetails from './WorldElementDetails';

import WorldRelationshipView from './WorldRelationshipView';
import WorldAIPanel from './WorldAIPanel';

/**
 * Presentation component for the World Building page
 * @param {Object} props - Component props
 * @returns {JSX.Element} WorldBuildingPage UI
 */
const WorldBuildingPage = ({
  isLoading,
  error,
  ui,
  visibleCategories,
  allCategories,
  customizations,
  activeCategory,
  currentBook,
  rootElements,
  childElements,
  selectedElement,
  elementRelationships,
  allElements,
  allRelationships,
  onTabChange,
  onCategoryChange,
  onCategorySelect,
  onViewModeChange,
  onColumnTabChange,
  onTemplateSelect,
  onToggleRelationshipMode,
  onElementSelect,
  onClearSelection,
  onUpdateCustomization,
  onResetCustomizations,
  onCreateElement,
  onFetchChildElements,
  onUpdateElement,
  onDeleteElement,
  onRelationshipCreated,
  onRelationshipUpdated,
  onRelationshipDeleted
}) => {
  // State for AI panel
  const [showAIPanel, setShowAIPanel] = useState(false);

  // Toggle AI panel
  const handleToggleAIPanel = () => {
    setShowAIPanel(!showAIPanel);
  };

  // We'll implement AI operations in a future update

  // Handle element generation
  const handleElementsGenerated = (elements) => {
    // This would be implemented to add the generated elements to the Redux store
    console.log('Elements generated:', elements);
  };

  // Handle element enhancement
  const handleElementEnhanced = (element) => {
    // This would be implemented to update the enhanced element in the Redux store
    console.log('Element enhanced:', element);
  };

  // Handle relationship suggestions
  const handleRelationshipsSuggested = (relationships) => {
    // This would be implemented to add the suggested relationships to the Redux store
    console.log('Relationships suggested:', relationships);
  };

  // Handle consistency analysis
  const handleAnalysisComplete = (analysis) => {
    // This would be implemented to display the analysis results
    console.log('Analysis complete:', analysis);
  };

  // Map generation removed
  // Show loading state
  if (isLoading && !activeCategory) {
    return (
      <div className="world-building-page">
        <WorldBuildingHeader
          currentBook={currentBook}
          onToggleRelationshipMode={onToggleRelationshipMode}
          relationshipMode={ui.relationshipMode}
        />
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>Loading world data...</p>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="world-building-page">
        <WorldBuildingHeader
          currentBook={currentBook}
          onToggleRelationshipMode={onToggleRelationshipMode}
          relationshipMode={ui.relationshipMode}
        />
        <div className="error-container">
          <h3>Error loading world data</h3>
          <p>{error}</p>
          <button className="btn btn-primary" onClick={onClearSelection}>
            Retry
          </button>
        </div>
      </div>
    );
  }

  // Debug log relationships
  console.log('WorldBuildingPage - allRelationships:', allRelationships);
  console.log('WorldBuildingPage - elementRelationships:', elementRelationships);

  // Ensure elementRelationships has the necessary data for the View Related Element button
  const enhancedElementRelationships = elementRelationships.map(relationship => {
    // Find the other element (source or target) based on the relationship direction
    const otherElementId = relationship.direction === 'outgoing'
      ? relationship.target_id || relationship.target_element_id
      : relationship.source_id || relationship.source_element_id;

    // Find the other element in allElements
    const otherElement = allElements.find(elem => elem.element_id === otherElementId);

    // Log for debugging
    console.log('Enhancing relationship:', {
      relationship,
      otherElementId,
      otherElement,
      otherElementName: relationship.direction === 'outgoing' ? relationship.target_name : relationship.source_name
    });

    return {
      ...relationship,
      otherElement: otherElement || {
        element_id: otherElementId,
        name: relationship.direction === 'outgoing' ? relationship.target_name : relationship.source_name
      }
    };
  });

  return (
    <div className="world-building-page">
      <WorldBuildingHeader
        currentBook={currentBook}
        onToggleRelationshipMode={onToggleRelationshipMode}
        onToggleAIPanel={handleToggleAIPanel}
        relationshipMode={ui.relationshipMode}
        showAIPanel={showAIPanel}
      />

      <WorldBuildingTabs
        activeTab={ui.activeTab}
        activeCategory={ui.activeCategory}
        onTabChange={onTabChange}
        onCategoryChange={onCategoryChange}
        currentBook={currentBook}
      />

      <div className="world-building-content">
        {ui.relationshipMode ? (
          <WorldRelationshipView
            elements={allElements}
            relationships={allRelationships}
          />
        ) : (
          <div className="world-elements-container">
            <WorldCategoriesColumn
              visibleCategories={visibleCategories}
              allCategories={allCategories}
              customizations={customizations}
              activeCategory={activeCategory}
              onCategorySelect={onCategorySelect}
              onUpdateCustomization={onUpdateCustomization}
              onResetCustomizations={onResetCustomizations}
            />
            <WorldElementsColumn
              activeCategory={activeCategory}
              elements={rootElements}
              selectedElement={selectedElement}
              relationships={allRelationships}
              columnTab={ui.columnTab}
              selectedTemplateId={ui.selectedTemplateId}
              onElementSelect={onElementSelect}
              onCreateElement={onCreateElement}
              onDeleteElement={onDeleteElement}
              onColumnTabChange={onColumnTabChange}
              onTemplateSelect={onTemplateSelect}
            />
            <WorldElementDetails
              element={selectedElement}
              childElements={childElements}
              relationships={enhancedElementRelationships}
              isLoading={isLoading}
              error={error}
              onUpdateElement={onUpdateElement}
              onClearError={() => console.log('Clear error')}
              onCreateSubElement={(data, customFields) => {
                // Ensure we're using the correct field name for the backend
                const elementData = { ...data };
                if (selectedElement?.category) {
                  elementData.category = selectedElement.category;
                }
                return onCreateElement(elementData, selectedElement?.element_id, data.element_type, customFields);
              }}
              onSelectElement={onElementSelect}
              onFetchChildElements={onFetchChildElements}
              onDeleteElement={onDeleteElement}
              onRelationshipCreated={onRelationshipCreated}
              onRelationshipUpdated={onRelationshipUpdated}
              onRelationshipDeleted={onRelationshipDeleted}
              allElements={allElements}
            />
            {showAIPanel && (
              <div className="world-ai-panel-container">
                <WorldAIPanel
                  activeCategory={activeCategory}
                  onElementsGenerated={handleElementsGenerated}
                  onAnalysisComplete={handleAnalysisComplete}
                />
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default WorldBuildingPage;

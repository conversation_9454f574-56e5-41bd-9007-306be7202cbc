/* frontend/src/components/Character3DRelationshipGraph.css */
.character-relationship-graph {
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-top: 30px;
}

.character-relationship-graph h3 {
  margin: 0 0 15px;
  color: #2d3748;
  font-size: 1.2rem;
  text-align: center;
}

.graph-container {
  width: 100%;
  height: 400px;
  position: relative;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
  background-color: #f8fafc;
}

.relationship-canvas {
  width: 100%;
  height: 100%;
  display: block;
}

.relationship-legend {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 15px;
  margin-top: 15px;
  padding: 10px;
  background-color: #f8fafc;
  border-radius: 8px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 0.9rem;
  color: #4a5568;
}

.legend-color {
  width: 15px;
  height: 5px;
  border-radius: 2px;
}

.strength-legend {
  margin-top: 15px;
  padding: 10px;
  background-color: #f8fafc;
  border-radius: 8px;
}

.strength-legend h4 {
  margin: 0 0 10px;
  font-size: 1rem;
  color: #2d3748;
  text-align: center;
}

.strength-item {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 5px;
  font-size: 0.85rem;
  color: #4a5568;
}

.strength-line {
  display: inline-block;
  width: 30px;
}

.graph-help {
  text-align: center;
  color: #718096;
  font-size: 0.9rem;
  margin: 10px 0 0;
  font-style: italic;
}

.relationship-graph-empty {
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 30px;
  margin-top: 30px;
  text-align: center;
  color: #718096;
}

// frontend/src/components/CharacterCard.js
import React from 'react';
import './CharacterCard.css';
import { BASE_URL } from '../utils/apiConfig';

/**
 * CharacterCard component for displaying a character in a card format
 * @param {Object} props - Component props
 * @returns {JSX.Element} CharacterCard UI
 */
const CharacterCard = ({
  character,
  isCompared,
  onEdit,
  onCompare,
  onDelete
}) => {
  // Handle card click
  const handleCardClick = () => {
    onEdit(character);
  };

  // Handle compare button click
  const handleCompareClick = (e) => {
    e.stopPropagation();
    onCompare(character);
  };

  // Handle delete button click
  const handleDeleteClick = (e) => {
    e.stopPropagation();
    if (window.confirm(`Are you sure you want to delete ${character.name}?`)) {
      onDelete(character.id);
    }
  };

  // Helper function to get a label for relationship strength
  const getStrengthLabel = (strength) => {
    switch (parseInt(strength, 10)) {
      case 0: return "Enemy";
      case 1: return "Acquaintance";
      case 2: return "Casual friend";
      case 3: return "Close friend";
      case 4: return "Deep bond";
      case 5: return "Intimate connection";
      default: return "Close friend";
    }
  };

  // Format relationships for display
  const formatRelationships = () => {
    // Check if relationships exists and is an array
    if (!character.relationships || !Array.isArray(character.relationships) || character.relationships.length === 0) {
      return 'None';
    }

    // Return a JSX element with formatted relationships
    return (
      <div className="relationship-list">
        {character.relationships.map((rel, index) => {
          // Handle different relationship formats
          const name = rel.name || rel.relatedCharacterName || rel.character || 'Unknown';
          let type = rel.type || rel.relationshipType || rel.relationship || 'Unknown';
          let description = rel.description || '';
          let strength = rel.strength !== undefined ? rel.strength : 3; // Default to 3 if not specified

          // Get the strength label
          const strengthLabel = getStrengthLabel(strength);

          // Check if type contains a description in the format "type <description>"
          if (!description && typeof type === 'string' && type.includes('<') && type.includes('>')) {
            const match = type.match(/([^<]+)<([^>]+)>/);
            if (match) {
              type = match[1].trim();
              description = match[2].trim();
            }
          }

          return (
            <div key={index} className="relationship-item">
              <div className="relationship-header">
                <span className="relationship-name">{name}</span>
                <span className="relationship-type">({type})</span>
              </div>
              <div className="relationship-strength">
                <span className="strength-label">{strengthLabel}</span>
                <div className="strength-bar">
                  <div
                    className="strength-fill"
                    style={{
                      width: `${(parseInt(strength, 10) / 5) * 100}%`,
                      backgroundColor: parseInt(strength, 10) === 0 ? '#F44336' : '#4CAF50'
                    }}
                  ></div>
                </div>
              </div>
              {description && (
                <div className="relationship-description">{description}</div>
              )}
            </div>
          );
        })}
      </div>
    );
  };

  return (
    <div
      className={`character-card ${isCompared ? 'compared' : ''}`}
      onClick={handleCardClick}
    >
      <div className="character-card-header">
        <h3>{character.name}</h3>
        <div className="character-card-actions">
          <button
            className={`compare-button ${isCompared ? 'active' : ''}`}
            onClick={handleCompareClick}
            title={isCompared ? 'Remove from comparison' : 'Add to comparison'}
          >
            {isCompared ? '✓' : '⇄'}
          </button>
          <button
            className="delete-button"
            onClick={handleDeleteClick}
            title="Delete character"
          >
            ✕
          </button>
        </div>
      </div>

      <div className="character-card-content">
        <div className="character-card-main">
          {character.headshot ? (
            <div className="character-headshot">
              {character.headshot === 'generating' ? (
                <div className="headshot-generating">
                  <div className="headshot-loading-spinner"></div>
                  <p>Generating...</p>
                </div>
              ) : (
                <img
                  src={character.headshot.startsWith('http')
                    ? character.headshot
                    : (character.headshot.startsWith('/')
                        ? (() => {
                            // Properly encode the URL
                            const pathParts = character.headshot.split('/');
                            const fileName = pathParts[pathParts.length - 1];
                            const basePath = pathParts.slice(0, pathParts.length - 1).join('/');
                            const encodedFileName = encodeURIComponent(fileName);
                            const encodedPath = `${basePath}/${encodedFileName}`;
                            return `${BASE_URL}${encodedPath}`;
                          })()
                        : character.headshot)}
                  alt={character.name}
                  onError={(e) => {
                    console.error('Error loading headshot:', character.headshot);
                    console.error('Attempted URL:', e.target.src);
                    e.target.onerror = null;
                    e.target.src = 'https://via.placeholder.com/150?text=' + encodeURIComponent(character.name.charAt(0));
                  }}
                />
              )}
            </div>
          ) : (
            <div className="character-headshot placeholder">
              <span>{character.name.charAt(0)}</span>
            </div>
          )}

          <div className="character-info">
            {character.role && (
              <div className="character-role">
                <span className="label">Role:</span> {character.role}
              </div>
            )}

            {character.age && (
              <div className="character-age">
                <span className="label">Age:</span> {character.age}
              </div>
            )}

            {character.race && (
              <div className="character-race">
                <span className="label">Race:</span> {character.race}
              </div>
            )}

            {character.gender_identity ? (
              <div className="character-gender-identity">
                <span className="label">Gender Identity:</span> {character.gender_identity}
              </div>
            ) : null}

            {character.sexual_orientation ? (
              <div className="character-sexual-orientation">
                <span className="label">Sexual Orientation:</span> {character.sexual_orientation}
              </div>
            ) : null}
          </div>
        </div>

        {character.description && (
          <div className="character-description">
            <span className="label">Description:</span>
            <p>{character.description}</p>
          </div>
        )}

        {character.traits && character.traits.length > 0 && (
          <div className="character-traits">
            <span className="label">Traits:</span>
            <div className="traits-list">
              {character.traits.map((trait, index) => (
                <span key={index} className="trait-tag">{trait}</span>
              ))}
            </div>
          </div>
        )}

        <div className="character-relationships">
          <span className="label">Relationships:</span>
          {formatRelationships()}
        </div>

        {character.backstory && (
          <div className="character-backstory">
            <span className="label">Backstory:</span>
            <p>{character.backstory}</p>
          </div>
        )}

        {character.arc && (
          <div className="character-arc">
            <span className="label">Character Arc:</span>
            <p>{character.arc}</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default CharacterCard;

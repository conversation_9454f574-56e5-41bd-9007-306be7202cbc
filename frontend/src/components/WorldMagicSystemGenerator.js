// frontend/src/components/WorldMagicSystemGenerator.js
import React, { useState } from 'react';
import GenerateMagicSystemModal from './GenerateMagicSystemModal';
import './WorldMagicSystemGenerator.css';

/**
 * Component for generating magic systems using AI
 * @param {Object} props - Component props
 * @returns {JSX.Element} WorldMagicSystemGenerator UI
 */
const WorldMagicSystemGenerator = ({ onMagicSystemsGenerated }) => {
  const [showModal, setShowModal] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedSystems, setGeneratedSystems] = useState([]);

  // Open the generate modal
  const handleOpenModal = () => {
    setShowModal(true);
  };

  // Close the generate modal
  const handleCloseModal = () => {
    setShowModal(false);
  };

  // Handle magic system generation
  const handleGenerateMagicSystems = async (formData) => {
    setIsGenerating(true);
    
    try {
      // This would be replaced with an actual API call
      // For now, we'll simulate a response
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Sample generated magic systems
      const systems = Array(formData.quantity).fill(null).map((_, index) => ({
        id: `generated-${Date.now()}-${index}`,
        name: `${formData.theme || formData.genre} Magic System ${index + 1}`,
        description: `A ${formData.complexity} complexity, ${formData.powerLevel} power level magic system for ${formData.genre} settings.`,
        element_type: 'magic_system',
        custom_fields: {
          source: `Generated based on ${formData.theme || formData.genre}`,
          energy_type: getRandomEnergyType(formData.genre),
          acquisition: getRandomAcquisitionMethod(),
          rules: `This magic system follows rules appropriate for a ${formData.complexity} system in the ${formData.genre} genre.`,
          limitations: formData.constraints || `Standard limitations for a ${formData.powerLevel} power level system.`,
          cost: `The cost is proportional to the ${formData.powerLevel} power level.`,
          practitioners: `Typical practitioners in a ${formData.genre} setting.`,
          rarity: getRandomRarity(formData.powerLevel),
          cultural_impact: `This magic system has a significant impact on the culture of the world.`,
          visual_effects: `Visual effects appropriate for the ${formData.genre} genre.`
        }
      }));
      
      setGeneratedSystems(systems);
      onMagicSystemsGenerated(systems);
      setShowModal(false);
    } catch (error) {
      console.error('Error generating magic systems:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  // Helper function to get a random energy type based on genre
  const getRandomEnergyType = (genre) => {
    const energyTypes = {
      fantasy: ['mana', 'elemental', 'divine', 'soul'],
      'sci-fi': ['cosmic', 'elemental', 'other'],
      'urban-fantasy': ['mana', 'life_force', 'soul'],
      steampunk: ['elemental', 'other'],
      horror: ['blood', 'soul', 'other'],
      mythological: ['divine', 'nature', 'soul'],
      'post-apocalyptic': ['nature', 'elemental', 'other']
    };
    
    const options = energyTypes[genre] || ['mana', 'elemental', 'divine', 'cosmic', 'soul', 'blood', 'nature', 'other'];
    return options[Math.floor(Math.random() * options.length)];
  };

  // Helper function to get a random acquisition method
  const getRandomAcquisitionMethod = () => {
    const methods = ['innate', 'learned', 'granted', 'inherited', 'artifact', 'ritual', 'mixed'];
    return methods[Math.floor(Math.random() * methods.length)];
  };

  // Helper function to get a random rarity based on power level
  const getRandomRarity = (powerLevel) => {
    const rarityMap = {
      low: ['common', 'uncommon'],
      medium: ['uncommon', 'rare'],
      high: ['rare', 'very_rare'],
      cosmic: ['very_rare', 'unique']
    };
    
    const options = rarityMap[powerLevel] || ['uncommon', 'rare'];
    return options[Math.floor(Math.random() * options.length)];
  };

  // Handle adding a magic system to the world
  const handleAddToWorld = (system) => {
    onMagicSystemsGenerated([system]);
  };

  return (
    <div className="world-magic-system-generator">
      <div className="generator-header">
        <h3>Magic System Generator</h3>
        <p>Generate complete magic systems for your world using AI</p>
      </div>

      <button 
        className="generate-button"
        onClick={handleOpenModal}
      >
        Generate New Magic System
      </button>

      {generatedSystems.length > 0 && (
        <div className="generated-systems">
          <h4>Generated Magic Systems</h4>
          <div className="systems-list">
            {generatedSystems.map(system => (
              <div key={system.id} className="system-card">
                <div className="system-header">
                  <h5>{system.name}</h5>
                  <span className={`energy-type ${system.custom_fields.energy_type}`}>
                    {system.custom_fields.energy_type.replace('_', ' ')}
                  </span>
                </div>
                <p className="system-description">{system.description}</p>
                <div className="system-details">
                  <div className="detail-item">
                    <span className="detail-label">Acquisition:</span>
                    <span className="detail-value">{system.custom_fields.acquisition.replace('_', ' ')}</span>
                  </div>
                  <div className="detail-item">
                    <span className="detail-label">Rarity:</span>
                    <span className="detail-value">{system.custom_fields.rarity.replace('_', ' ')}</span>
                  </div>
                </div>
                <button 
                  className="add-to-world-button"
                  onClick={() => handleAddToWorld(system)}
                >
                  Add to World
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {showModal && (
        <GenerateMagicSystemModal
          onClose={handleCloseModal}
          onGenerate={handleGenerateMagicSystems}
          isGenerating={isGenerating}
        />
      )}
    </div>
  );
};

export default WorldMagicSystemGenerator;

/* frontend/src/components/SubElementsList.css */
.sub-elements-list {
  margin-top: 10px;
}

.sub-element-item {
  padding: 12px;
  margin-bottom: 10px;
  border-radius: 6px;
  background-color: var(--background-secondary);
  cursor: pointer;
  transition: all 0.2s;
  border-left: 4px solid var(--primary);
  position: relative; /* For absolute positioning of delete button */
}

.sub-element-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

/* Delete button styling */
.sub-element-delete-button {
  position: absolute;
  top: 5px;
  right: 5px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: var(--background-tertiary);
  color: var(--text-secondary);
  border: none;
  font-size: 18px;
  line-height: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 2;
  opacity: 0.7;
  transition: all 0.2s;
}

.sub-element-delete-button:hover {
  opacity: 1;
  background-color: var(--error);
  color: white;
}

/* Confirmation dialog styling */
.delete-confirmation-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.delete-confirmation-dialog {
  background-color: var(--background-primary);
  border-radius: 8px;
  padding: 20px;
  width: 400px;
  max-width: 90%;
  box-shadow: var(--shadow-lg);
}

.delete-confirmation-dialog h3 {
  margin-top: 0;
  color: var(--text-primary);
}

.delete-confirmation-dialog p {
  margin-bottom: 20px;
  color: var(--text-secondary);
}

.delete-confirmation-dialog p.warning {
  color: var(--error);
  font-weight: bold;
}

.confirmation-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.confirmation-buttons button {
  padding: 8px 16px;
  border-radius: 4px;
  border: none;
  cursor: pointer;
  font-weight: 500;
}

.confirmation-buttons .cancel-button {
  background-color: var(--background-tertiary);
  color: var(--text-secondary);
}

.confirmation-buttons .delete-button {
  background-color: var(--error);
  color: white;
}

.sub-element-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
}

.sub-element-title {
  display: flex;
  align-items: center;
  gap: 5px;
}

.sub-element-title h4 {
  margin: 0;
  font-size: 1rem;
  color: var(--text-primary);
}

.children-indicator {
  font-size: 0.7rem;
  color: var(--text-secondary);
}

.element-type-label {
  font-size: 0.7rem;
  padding: 2px 6px;
  border-radius: 10px;
  background-color: var(--background-tertiary);
  color: var(--text-secondary);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Specific styling for magic system element types */
.element-type-label[data-element-type="magical_power"] {
  background-color: #7e57c2; /* Purple */
  color: white;
}

.element-type-label[data-element-type="magical_artifact"] {
  background-color: #ff9800; /* Orange */
  color: white;
}

.element-type-label[data-element-type="spell"] {
  background-color: #26a69a; /* Teal */
  color: white;
}

.sub-element-description {
  margin: 0;
  font-size: 0.85rem;
  color: var(--text-secondary);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.no-sub-elements {
  padding: 20px;
  text-align: center;
  color: var(--text-secondary);
  background-color: var(--background-secondary);
  border-radius: 6px;
  margin-top: 10px;
}

/* Element type styling */
.sub-element-item[data-element-type="organization"] {
  border-left-color: #2196f3;
}

.sub-element-item[data-element-type="law"] {
  border-left-color: #9c27b0;
}

.sub-element-item[data-element-type="law_clause"] {
  border-left-color: #673ab7;
}

.sub-element-item[data-element-type="geography"] {
  border-left-color: #4caf50;
}

.sub-element-item[data-element-type="location"] {
  border-left-color: #ff9800;
}

.sub-element-item[data-element-type="magic_system"] {
  border-left-color: #e91e63;
}

.sub-element-item[data-element-type="magical_power"] {
  border-left-color: #7e57c2; /* Purple */
}

.sub-element-item[data-element-type="magical_artifact"] {
  border-left-color: #ff9800; /* Orange */
}

.sub-element-item[data-element-type="spell"] {
  border-left-color: #26a69a; /* Teal */
}

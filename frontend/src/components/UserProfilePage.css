/* frontend/src/components/UserProfilePage.css */

.user-profile-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  height: calc(100vh - 60px); /* Adjust for header height */
  overflow-y: auto;
}

.user-profile-page h2 {
  margin-bottom: 20px;
  color: #333;
}

.error-message {
  background-color: #ffebee;
  border: 1px solid #ffcdd2;
  border-radius: 4px;
  padding: 10px 15px;
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.error-message p {
  color: #c62828;
  margin: 0;
}

.error-message button {
  background: none;
  border: none;
  color: #c62828;
  cursor: pointer;
  font-weight: bold;
}

.profile-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
}

@media (max-width: 768px) {
  .profile-container {
    grid-template-columns: 1fr;
  }
}

/* AI Settings Section */
.ai-settings-section {
  grid-column: 1 / -1; /* Span all columns */
  margin-top: 30px;
}

.profile-section,
.settings-section {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.profile-section h3,
.settings-section h3 {
  margin-top: 0;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
  color: #333;
}

.loading-indicator {
  text-align: center;
  padding: 20px;
  color: #666;
}

/* Profile Form Styles */
.profile-form .form-group {
  margin-bottom: 15px;
}

.profile-form label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #555;
}

.profile-form input,
.profile-form textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.profile-form textarea {
  resize: vertical;
  min-height: 80px;
}

.avatar-preview {
  margin-top: 10px;
  text-align: center;
}

.avatar-preview img {
  max-width: 100px;
  max-height: 100px;
  border-radius: 50%;
  border: 1px solid #ddd;
}

.form-actions {
  display: flex;
  gap: 10px;
  margin-top: 20px;
}

.save-button,
.edit-button {
  background-color: #4caf50;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
}

.cancel-button {
  background-color: #f5f5f5;
  color: #333;
  border: 1px solid #ddd;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
}

/* Profile Details Styles */
.profile-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.avatar {
  margin-right: 15px;
}

.avatar img {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
  border: 1px solid #ddd;
}

.profile-info {
  flex: 1;
}

.profile-info h4 {
  margin: 0 0 5px 0;
  font-size: 18px;
}

.profile-info .email {
  color: #666;
  margin: 0;
}

.bio h5 {
  margin: 0 0 5px 0;
  font-size: 16px;
  color: #555;
}

.bio p {
  margin: 0;
  line-height: 1.5;
  color: #666;
}

/* Settings Styles */
.settings-form .form-group {
  margin-bottom: 15px;
}

.settings-form label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #555;
}

.settings-form select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  background-color: white;
}

.form-group.checkbox {
  display: flex;
  align-items: center;
}

.form-group.checkbox label {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.form-group.checkbox input {
  width: auto;
  margin-right: 8px;
}

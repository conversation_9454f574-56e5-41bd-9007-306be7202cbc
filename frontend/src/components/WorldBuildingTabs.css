/* frontend/src/components/WorldBuildingTabs.css */
.world-building-tabs {
  display: none; /* Hide the tabs container since it's now empty */
}

.category-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.category-button {
  padding: 4px 10px;
  border: 1px solid var(--border-color);
  border-radius: 16px;
  background: var(--background-primary);
  color: var(--text-primary);
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.category-button.active {
  background: var(--primary);
  color: white;
  border-color: var(--primary);
}

.category-button:hover:not(.active) {
  background: var(--background-secondary);
}

.category-button.clear {
  background: var(--background-tertiary);
  border-color: var(--border-color);
}

/* Genre info removed */

/* Category-specific colors */
.category-button[title*="Cosmology & Physical Environment"] {
  border-color: #4caf50;
  color: #4caf50;
}

.category-button[title*="Cosmology & Physical Environment"].active {
  background: #4caf50;
  color: white;
  border-color: #4caf50;
}

.category-button[title*="Cultural & Social Systems"] {
  border-color: #2196f3;
  color: #2196f3;
}

.category-button[title*="Cultural & Social Systems"].active {
  background: #2196f3;
  color: white;
  border-color: #2196f3;
}

.category-button[title*="Economic & Material Systems"] {
  border-color: #ffc107;
  color: #ffc107;
}

.category-button[title*="Economic & Material Systems"].active {
  background: #ffc107;
  color: white;
  border-color: #ffc107;
}

.category-button[title*="Knowledge & Technology Section"] {
  border-color: #9c27b0;
  color: #9c27b0;
}

.category-button[title*="Knowledge & Technology Section"].active {
  background: #9c27b0;
  color: white;
  border-color: #9c27b0;
}

.category-button[title*="Temporal & Historical Elements"] {
  border-color: #ff5722;
  color: #ff5722;
}

.category-button[title*="Temporal & Historical Elements"].active {
  background: #ff5722;
  color: white;
  border-color: #ff5722;
}

.category-button[title*="Magical & Supernatural Systems"] {
  border-color: #e91e63;
  color: #e91e63;
}

.category-button[title*="Magical & Supernatural Systems"].active {
  background: #e91e63;
  color: white;
  border-color: #e91e63;
}

.category-button[title*="Interactive Systems & Game Mechanics"] {
  border-color: #00bcd4;
  color: #00bcd4;
}

.category-button[title*="Interactive Systems & Game Mechanics"].active {
  background: #00bcd4;
  color: white;
  border-color: #00bcd4;
}

/* Responsive styles */
@media (max-width: 768px) {
  .category-buttons {
    overflow-x: auto;
    padding-bottom: 5px;
    -webkit-overflow-scrolling: touch;
  }

  .category-button {
    flex-shrink: 0;
  }
}

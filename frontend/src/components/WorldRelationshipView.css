/* frontend/src/components/WorldRelationshipView.css */
.world-relationship-view {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: var(--background-primary);
  overflow: hidden;
}

.relationship-controls {
  padding: 15px 20px;
  background: var(--background-primary);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 10;
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.control-group h3 {
  margin: 0;
  font-size: 1.2rem;
  color: var(--text-primary);
}

.filter-controls {
  display: flex;
  gap: 10px;
  align-items: center;
}

.search-input {
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background: var(--background-secondary);
  color: var(--text-primary);
  width: 200px;
}

.category-filter {
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background: var(--background-secondary);
  color: var(--text-primary);
}

.action-buttons {
  display: flex;
  gap: 10px;
  align-items: center;
}

.create-relationship-button {
  padding: 8px 16px;
  background: var(--primary);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: background 0.2s;
}

.create-relationship-button:hover {
  background: var(--button-primary-hover);
}

.relationship-status {
  font-size: 0.9rem;
  color: var(--text-secondary);
  margin-right: 10px;
}

.relationship-type-select {
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background: var(--background-secondary);
  color: var(--text-primary);
}

.relationship-graph {
  flex: 1;
  width: 100%;
  height: 100%;
  background: var(--background-secondary);
}

/* React Flow custom styling */
.react-flow__node {
  padding: 10px;
  border-radius: 5px;
  width: 150px;
  font-size: 12px;
  text-align: center;
  border-width: 2px;
  border-style: solid;
}

.react-flow__node-input {
  background: #f6f8fa;
  border-color: #c9d1d9;
}

.react-flow__node-output {
  background: #f6f8fa;
  border-color: #c9d1d9;
}

/* Element type styling */
/* Physical types */
.react-flow__node[data-element-type="geography"] {
  background: rgba(3, 169, 244, 0.1);
  border-color: #03A9F4; /* Lighter blue */
}

.react-flow__node[data-element-type="location"] {
  background: rgba(0, 188, 212, 0.1);
  border-color: #00BCD4; /* Blue-teal */
}

.react-flow__node[data-element-type="species"] {
  background: rgba(79, 195, 247, 0.1);
  border-color: #4FC3F7; /* Sky blue */
}

/* Social types */
.react-flow__node[data-element-type="organization"] {
  background: rgba(126, 87, 194, 0.1);
  border-color: #7E57C2; /* Soft purple */
}

.react-flow__node[data-element-type="law"] {
  background: rgba(233, 30, 99, 0.1);
  border-color: #E91E63; /* Pink */
}

.react-flow__node[data-element-type="law_clause"] {
  background: rgba(236, 64, 122, 0.1);
  border-color: #EC407A; /* Light pink */
}

/* Metaphysical types */
.react-flow__node[data-element-type="magic_system"] {
  background: rgba(255, 87, 34, 0.1);
  border-color: #FF5722; /* Deep orange */
}

.react-flow__node[data-element-type="magical_power"] {
  background: rgba(255, 112, 67, 0.1);
  border-color: #FF7043; /* Lighter orange-red */
}

.react-flow__node[data-element-type="magical_artifact"] {
  background: rgba(255, 171, 64, 0.1);
  border-color: #FFAB40; /* Amber */
}

.react-flow__node[data-element-type="spell"] {
  background: rgba(255, 110, 64, 0.1);
  border-color: #FF6E40; /* Orange-red */
}

/* Technological types */
.react-flow__node[data-element-type="technology"] {
  background: rgba(102, 187, 106, 0.1);
  border-color: #66BB6A; /* Light green */
}

.react-flow__node[data-element-type="infrastructure"] {
  background: rgba(129, 199, 132, 0.1);
  border-color: #81C784; /* Pale green */
}

.react-flow__node[data-element-type="sciences"] {
  background: rgba(38, 166, 154, 0.1);
  border-color: #26A69A; /* Teal-green */
}

/* Category styling (fallback) */
/* Physical Categories */
.react-flow__node[data-category="physical_geography"],
.react-flow__node[data-category="cat_physical_geography"],
.node-physical_geography,
.node-cat_physical_geography {
  background: rgba(3, 169, 244, 0.1);
  border-color: #03A9F4; /* Lighter blue */
}

.react-flow__node[data-category="physical_locations"],
.react-flow__node[data-category="cat_physical_locations"],
.node-physical_locations,
.node-cat_physical_locations {
  background: rgba(0, 188, 212, 0.1);
  border-color: #00BCD4; /* Blue-teal */
}

.react-flow__node[data-category="physical_climate"],
.react-flow__node[data-category="cat_physical_climate"],
.node-physical_climate,
.node-cat_physical_climate {
  background: rgba(33, 150, 243, 0.1);
  border-color: #2196F3; /* Blue */
}

.react-flow__node[data-category="physical_flora_fauna"],
.react-flow__node[data-category="cat_physical_flora_fauna"],
.node-physical_flora_fauna,
.node-cat_physical_flora_fauna {
  background: rgba(79, 195, 247, 0.1);
  border-color: #4FC3F7; /* Sky blue */
}

.react-flow__node[data-category="physical_monsters"],
.react-flow__node[data-category="cat_physical_monsters"],
.react-flow__node[data-category="physical_aliens"],
.react-flow__node[data-category="cat_physical_aliens"],
.node-physical_monsters,
.node-cat_physical_monsters,
.node-physical_aliens,
.node-cat_physical_aliens {
  background: rgba(3, 169, 244, 0.1);
  border-color: #03A9F4; /* Lighter blue */
}

/* Social Categories */
.react-flow__node[data-category="social_organizations"],
.react-flow__node[data-category="cat_social_organizations"],
.node-social_organizations,
.node-cat_social_organizations {
  background: rgba(126, 87, 194, 0.1);
  border-color: #7E57C2; /* Soft purple */
}

.react-flow__node[data-category="social_cultures"],
.react-flow__node[data-category="cat_social_cultures"],
.node-social_cultures,
.node-cat_social_cultures {
  background: rgba(186, 104, 200, 0.1);
  border-color: #BA68C8; /* Lavender */
}

.react-flow__node[data-category="social_laws"],
.react-flow__node[data-category="cat_social_laws"],
.node-social_laws,
.node-cat_social_laws {
  background: rgba(233, 30, 99, 0.1);
  border-color: #E91E63; /* Pink */
}

.react-flow__node[data-category="social_economics"],
.react-flow__node[data-category="cat_social_economics"],
.node-social_economics,
.node-cat_social_economics {
  background: rgba(156, 39, 176, 0.1);
  border-color: #9C27B0; /* Purple */
}

/* Metaphysical Categories */
.react-flow__node[data-category="metaphysical_magic"],
.react-flow__node[data-category="cat_metaphysical_magic"],
.node-metaphysical_magic,
.node-cat_metaphysical_magic {
  background: rgba(255, 87, 34, 0.1);
  border-color: #FF5722; /* Deep orange */
}

.react-flow__node[data-category="metaphysical_religion"],
.react-flow__node[data-category="cat_metaphysical_religion"],
.node-metaphysical_religion,
.node-cat_metaphysical_religion {
  background: rgba(255, 152, 0, 0.1);
  border-color: #FF9800; /* Orange */
}

.react-flow__node[data-category="metaphysical_cosmology"],
.react-flow__node[data-category="cat_metaphysical_cosmology"],
.node-metaphysical_cosmology,
.node-cat_metaphysical_cosmology {
  background: rgba(255, 171, 64, 0.1);
  border-color: #FFAB40; /* Amber */
}

.react-flow__node[data-category="metaphysical_superpowers"],
.react-flow__node[data-category="cat_metaphysical_superpowers"],
.node-metaphysical_superpowers,
.node-cat_metaphysical_superpowers {
  background: rgba(255, 112, 67, 0.1);
  border-color: #FF7043; /* Lighter orange-red */
}

/* Technological Categories */
.react-flow__node[data-category="technological_tools"],
.react-flow__node[data-category="cat_technological_tools"],
.node-technological_tools,
.node-cat_technological_tools {
  background: rgba(76, 175, 80, 0.1);
  border-color: #4CAF50; /* Green */
}

.react-flow__node[data-category="technological_infrastructure"],
.react-flow__node[data-category="cat_technological_infrastructure"],
.node-technological_infrastructure,
.node-cat_technological_infrastructure {
  background: rgba(102, 187, 106, 0.1);
  border-color: #66BB6A; /* Light green */
}

.react-flow__node[data-category="technological_sciences"],
.react-flow__node[data-category="cat_technological_sciences"],
.node-technological_sciences,
.node-cat_technological_sciences {
  background: rgba(38, 166, 154, 0.1);
  border-color: #26A69A; /* Teal-green */
}

.react-flow__node[data-category="technological_scifi"],
.react-flow__node[data-category="cat_technological_scifi"],
.node-technological_scifi,
.node-cat_technological_scifi {
  background: rgba(129, 199, 132, 0.1);
  border-color: #81C784; /* Pale green */
}

.react-flow__edge-path {
  stroke-width: 2;
}

.react-flow__edge-text {
  font-size: 12px;
  fill: var(--text-primary);
  font-weight: 500;
}

.react-flow__edge-textbg {
  fill: var(--background-primary);
  opacity: 0.8;
}

.react-flow__controls {
  background: var(--background-primary);
  border-radius: 8px;
  box-shadow: var(--shadow-md);
}

.react-flow__controls-button {
  background: var(--background-secondary);
  border-color: var(--border-color);
  color: var(--text-primary);
}

.react-flow__minimap {
  background-color: var(--background-primary);
  border-radius: 8px;
  box-shadow: var(--shadow-md);
}

.legend {
  background: var(--background-primary);
  padding: 10px;
  border-radius: 8px;
  box-shadow: var(--shadow-md);
  font-size: 0.8rem;
  max-height: 80vh;
  overflow-y: auto;
}

.legend h4 {
  margin: 0 0 8px 0;
  font-size: 0.9rem;
  color: var(--text-primary);
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 5px;
}

.legend-section {
  margin-bottom: 15px;
}

.legend-section h5 {
  margin: 0 0 5px 0;
  font-size: 0.8rem;
  color: var(--text-secondary);
}

.legend-item {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}

.legend-color {
  width: 15px;
  height: 15px;
  border-radius: 3px;
  margin-right: 8px;
}

/* Physical Categories */
.legend-color.physical_locations,
.legend-color.cat_physical_locations {
  background-color: #00BCD4; /* Blue-teal */
}

.legend-color.physical_geography,
.legend-color.cat_physical_geography {
  background-color: #03A9F4; /* Lighter blue */
}

.legend-color.physical_climate,
.legend-color.cat_physical_climate {
  background-color: #2196F3; /* Blue */
}

.legend-color.physical_flora_fauna,
.legend-color.cat_physical_flora_fauna {
  background-color: #4FC3F7; /* Sky blue */
}

.legend-color.physical_monsters,
.legend-color.cat_physical_monsters,
.legend-color.cat_physical_aliens {
  background-color: #ff5722;
}

/* Social Categories */
.legend-color.social_organizations,
.legend-color.cat_social_organizations {
  background-color: #7E57C2; /* Soft purple */
}

.legend-color.social_cultures,
.legend-color.cat_social_cultures {
  background-color: #BA68C8; /* Lavender */
}

.legend-color.social_laws,
.legend-color.cat_social_laws {
  background-color: #E91E63; /* Pink */
}

.legend-color.social_economics,
.legend-color.cat_social_economics {
  background-color: #9C27B0; /* Purple */
}

/* Metaphysical Categories */
.legend-color.metaphysical_magic,
.legend-color.cat_metaphysical_magic {
  background-color: #FF5722; /* Deep orange */
}

.legend-color.metaphysical_religion,
.legend-color.cat_metaphysical_religion {
  background-color: #FF9800; /* Orange */
}

.legend-color.metaphysical_cosmology,
.legend-color.cat_metaphysical_cosmology {
  background-color: #FFAB40; /* Amber */
}

.legend-color.metaphysical_superpowers,
.legend-color.cat_metaphysical_superpowers {
  background-color: #FF7043; /* Lighter orange-red */
}

/* Technological Categories */
.legend-color.technological_tools,
.legend-color.cat_technological_tools {
  background-color: #4CAF50; /* Green */
}

.legend-color.technological_infrastructure,
.legend-color.cat_technological_infrastructure {
  background-color: #66BB6A; /* Light green */
}

.legend-color.technological_sciences,
.legend-color.cat_technological_sciences {
  background-color: #26A69A; /* Teal-green */
}

.legend-color.technological_scifi,
.legend-color.cat_technological_scifi {
  background-color: #81C784; /* Pale green */
}

/* Relationship Types */
.legend-color.parent-child {
  background-color: #BB86FC;
  border: 1px dashed #9966CC;
}

.legend-color.user-created {
  background-color: #FF5722;
}

/* Responsive styles */
@media (max-width: 768px) {
  .relationship-controls {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }

  .filter-controls {
    flex-direction: column;
    width: 100%;
  }

  .search-input {
    width: 100%;
  }

  .action-buttons {
    width: 100%;
    justify-content: space-between;
  }
}

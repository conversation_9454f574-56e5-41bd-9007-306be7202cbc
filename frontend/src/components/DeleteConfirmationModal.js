// frontend/src/components/DeleteConfirmationModal.js
import React from 'react';
import './DeleteConfirmationModal.css';

/**
 * Modal component for confirming element deletion
 * @param {Object} props - Component props
 * @returns {JSX.Element} DeleteConfirmationModal UI
 */
const DeleteConfirmationModal = ({
  isOpen,
  elementName,
  onConfirm,
  onCancel
}) => {
  if (!isOpen) return null;

  return (
    <div className="delete-confirmation-overlay">
      <div className="delete-confirmation-modal">
        <h3>Delete Confirmation</h3>
        <p>Are you sure you want to delete <strong>{elementName}</strong>?</p>
        <p className="warning-text">This action cannot be undone.</p>
        
        <div className="modal-actions">
          <button className="cancel-button" onClick={onCancel}>
            Cancel
          </button>
          <button className="delete-button" onClick={onConfirm}>
            Delete
          </button>
        </div>
      </div>
    </div>
  );
};

export default DeleteConfirmationModal;

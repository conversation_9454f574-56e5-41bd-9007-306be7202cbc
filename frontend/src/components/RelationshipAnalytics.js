// frontend/src/components/RelationshipAnalytics.js
import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { 
  selectAllElementsForRelationshipView, 
  selectTemplateByType,
  selectCurrentBookId
} from '../redux/slices/worldBuildingSlice';
import { getRelationshipTypeLabel } from '../utils/relationshipTypes';
import './RelationshipAnalytics.css';

/**
 * Component for analyzing relationships between elements
 * @param {Object} props - Component props
 * @param {Array} props.relationships - All relationships in the world
 * @param {Function} props.onCreateRelationship - Callback to create a relationship
 * @param {Function} props.onSelectElement - Callback to select an element
 * @returns {JSX.Element} RelationshipAnalytics UI
 */
const RelationshipAnalytics = ({ 
  relationships = [], 
  onCreateRelationship,
  onSelectElement
}) => {
  const allElements = useSelector(selectAllElementsForRelationshipView);
  const getTemplateByType = useSelector(selectTemplateByType);
  const bookId = useSelector(selectCurrentBookId);
  
  // State for analytics
  const [missingRelationships, setMissingRelationships] = useState([]);
  const [inconsistentRelationships, setInconsistentRelationships] = useState([]);
  const [suggestions, setSuggestions] = useState([]);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [activeTab, setActiveTab] = useState('missing');
  
  // Analyze relationships when component mounts or relationships change
  useEffect(() => {
    analyzeRelationships();
  }, [relationships, allElements]);
  
  // Analyze relationships for patterns, gaps, and inconsistencies
  const analyzeRelationships = () => {
    setIsAnalyzing(true);
    
    // Find missing relationships
    const missing = findMissingRelationships();
    setMissingRelationships(missing);
    
    // Find inconsistent relationships
    const inconsistent = findInconsistentRelationships();
    setInconsistentRelationships(inconsistent);
    
    // Generate suggestions
    const newSuggestions = generateSuggestions(missing, inconsistent);
    setSuggestions(newSuggestions);
    
    setIsAnalyzing(false);
  };
  
  // Find missing relationships based on templates
  const findMissingRelationships = () => {
    const missing = [];
    
    // Group elements by type
    const elementsByType = {};
    allElements.forEach(element => {
      const type = element.element_type;
      if (!elementsByType[type]) {
        elementsByType[type] = [];
      }
      elementsByType[type].push(element);
    });
    
    // Check each element type
    Object.entries(elementsByType).forEach(([sourceType, sourceElements]) => {
      // Get the template for this element type
      const sourceTemplate = getTemplateByType(sourceType);
      
      // Skip if no template or no relationship definitions
      if (!sourceTemplate || !sourceTemplate.relationship_definitions) {
        return;
      }
      
      // Get expected relationships from the template
      const expectedRelationships = sourceTemplate.relationship_definitions.valid_relationships || [];
      
      // For each source element
      sourceElements.forEach(sourceElement => {
        // For each expected relationship
        expectedRelationships.forEach(expected => {
          const targetType = expected.target_template || expected.target_template_id;
          const relationshipType = expected.relationship_type || expected.relationship_type_id;
          
          // Skip if no target type or relationship type
          if (!targetType || !relationshipType) {
            return;
          }
          
          // Get target elements of this type
          const targetElements = elementsByType[targetType] || [];
          
          // For each target element
          targetElements.forEach(targetElement => {
            // Skip self-relationships
            if (sourceElement.element_id === targetElement.element_id) {
              return;
            }
            
            // Check if this relationship exists
            const relationshipExists = relationships.some(rel => 
              (rel.source_element_id === sourceElement.element_id && 
               rel.target_element_id === targetElement.element_id && 
               (rel.relationship_type === relationshipType || rel.type === relationshipType)) ||
              (rel.source_element_id === targetElement.element_id && 
               rel.target_element_id === sourceElement.element_id && 
               (rel.relationship_type === expected.inverse_relationship || rel.type === expected.inverse_relationship))
            );
            
            // If not, add to missing relationships
            if (!relationshipExists) {
              missing.push({
                sourceElement,
                targetElement,
                relationshipType,
                description: expected.description || `${sourceElement.name} ${getRelationshipTypeLabel(relationshipType)} ${targetElement.name}`
              });
            }
          });
        });
      });
    });
    
    return missing;
  };
  
  // Find inconsistent relationships
  const findInconsistentRelationships = () => {
    const inconsistent = [];
    
    // Check for bidirectional relationships with inconsistent types
    relationships.forEach(rel1 => {
      relationships.forEach(rel2 => {
        // Check if this is a bidirectional relationship
        if (rel1.source_element_id === rel2.target_element_id && 
            rel1.target_element_id === rel2.source_element_id) {
          
          // Get the relationship types
          const type1 = rel1.relationship_type || rel1.type;
          const type2 = rel2.relationship_type || rel2.type;
          
          // Get the relationship type objects
          const typeObj1 = { id: type1, label: getRelationshipTypeLabel(type1) };
          const typeObj2 = { id: type2, label: getRelationshipTypeLabel(type2) };
          
          // Check if the types are inconsistent
          // (This is a simplified check - in a real implementation, we would check against the inverse relationship)
          if (type1 !== type2) {
            // Find the source and target elements
            const sourceElement = allElements.find(el => el.element_id === rel1.source_element_id);
            const targetElement = allElements.find(el => el.element_id === rel1.target_element_id);
            
            if (sourceElement && targetElement) {
              inconsistent.push({
                relationship1: rel1,
                relationship2: rel2,
                sourceElement,
                targetElement,
                type1: typeObj1,
                type2: typeObj2
              });
            }
          }
        }
      });
    });
    
    return inconsistent;
  };
  
  // Generate suggestions based on missing and inconsistent relationships
  const generateSuggestions = (missing, inconsistent) => {
    const suggestions = [];
    
    // Add suggestions for missing relationships
    missing.slice(0, 5).forEach(item => {
      suggestions.push({
        type: 'missing',
        message: `Consider adding a relationship: ${item.sourceElement.name} ${getRelationshipTypeLabel(item.relationshipType)} ${item.targetElement.name}`,
        action: () => {
          if (onCreateRelationship) {
            onCreateRelationship(item.sourceElement, item.targetElement, item.relationshipType);
          }
        },
        sourceElement: item.sourceElement,
        targetElement: item.targetElement
      });
    });
    
    // Add suggestions for inconsistent relationships
    inconsistent.forEach(item => {
      suggestions.push({
        type: 'inconsistent',
        message: `Inconsistent bidirectional relationship between ${item.sourceElement.name} and ${item.targetElement.name}`,
        action: () => {
          // This would open a dialog to fix the inconsistency
          console.log('Fix inconsistency', item);
        },
        sourceElement: item.sourceElement,
        targetElement: item.targetElement
      });
    });
    
    return suggestions;
  };
  
  // Render the analytics UI
  return (
    <div className="relationship-analytics">
      <div className="analytics-header">
        <h3>Relationship Analytics</h3>
        <button 
          className="analyze-button"
          onClick={analyzeRelationships}
          disabled={isAnalyzing}
        >
          {isAnalyzing ? 'Analyzing...' : 'Refresh Analysis'}
        </button>
      </div>
      
      <div className="analytics-tabs">
        <button 
          className={`analytics-tab ${activeTab === 'missing' ? 'active' : ''}`}
          onClick={() => setActiveTab('missing')}
        >
          Missing Relationships ({missingRelationships.length})
        </button>
        <button 
          className={`analytics-tab ${activeTab === 'inconsistent' ? 'active' : ''}`}
          onClick={() => setActiveTab('inconsistent')}
        >
          Inconsistencies ({inconsistentRelationships.length})
        </button>
        <button 
          className={`analytics-tab ${activeTab === 'suggestions' ? 'active' : ''}`}
          onClick={() => setActiveTab('suggestions')}
        >
          Suggestions ({suggestions.length})
        </button>
      </div>
      
      <div className="analytics-content">
        {activeTab === 'missing' && (
          <div className="missing-relationships">
            <h4>Missing Relationships</h4>
            {missingRelationships.length > 0 ? (
              <ul className="missing-list">
                {missingRelationships.map((item, index) => (
                  <li key={index} className="missing-item">
                    <div className="missing-description">
                      <span 
                        className="element-name clickable" 
                        onClick={() => onSelectElement(item.sourceElement.element_id)}
                      >
                        {item.sourceElement.name}
                      </span>
                      <span className="relationship-type">
                        {getRelationshipTypeLabel(item.relationshipType)}
                      </span>
                      <span 
                        className="element-name clickable" 
                        onClick={() => onSelectElement(item.targetElement.element_id)}
                      >
                        {item.targetElement.name}
                      </span>
                    </div>
                    <button 
                      className="create-relationship-button"
                      onClick={() => onCreateRelationship(item.sourceElement, item.targetElement, item.relationshipType)}
                    >
                      Add
                    </button>
                  </li>
                ))}
              </ul>
            ) : (
              <p className="no-items">No missing relationships found.</p>
            )}
          </div>
        )}
        
        {activeTab === 'inconsistent' && (
          <div className="inconsistent-relationships">
            <h4>Inconsistent Relationships</h4>
            {inconsistentRelationships.length > 0 ? (
              <ul className="inconsistent-list">
                {inconsistentRelationships.map((item, index) => (
                  <li key={index} className="inconsistent-item">
                    <div className="inconsistent-description">
                      <div className="relationship-pair">
                        <span 
                          className="element-name clickable" 
                          onClick={() => onSelectElement(item.sourceElement.element_id)}
                        >
                          {item.sourceElement.name}
                        </span>
                        <span className="relationship-type">
                          {item.type1.label}
                        </span>
                        <span 
                          className="element-name clickable" 
                          onClick={() => onSelectElement(item.targetElement.element_id)}
                        >
                          {item.targetElement.name}
                        </span>
                      </div>
                      <div className="relationship-pair">
                        <span 
                          className="element-name clickable" 
                          onClick={() => onSelectElement(item.targetElement.element_id)}
                        >
                          {item.targetElement.name}
                        </span>
                        <span className="relationship-type">
                          {item.type2.label}
                        </span>
                        <span 
                          className="element-name clickable" 
                          onClick={() => onSelectElement(item.sourceElement.element_id)}
                        >
                          {item.sourceElement.name}
                        </span>
                      </div>
                    </div>
                    <button className="fix-relationship-button">
                      Fix
                    </button>
                  </li>
                ))}
              </ul>
            ) : (
              <p className="no-items">No inconsistent relationships found.</p>
            )}
          </div>
        )}
        
        {activeTab === 'suggestions' && (
          <div className="relationship-suggestions">
            <h4>Suggestions</h4>
            {suggestions.length > 0 ? (
              <ul className="suggestions-list">
                {suggestions.map((suggestion, index) => (
                  <li key={index} className={`suggestion-item ${suggestion.type}`}>
                    <div className="suggestion-message">
                      {suggestion.message}
                    </div>
                    <button 
                      className="apply-suggestion-button"
                      onClick={suggestion.action}
                    >
                      Apply
                    </button>
                  </li>
                ))}
              </ul>
            ) : (
              <p className="no-items">No suggestions available.</p>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default RelationshipAnalytics;

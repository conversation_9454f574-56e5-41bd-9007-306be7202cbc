// frontend/src/components/WorldAIPanel.js
import React, { useState } from 'react';
import WorldElementGenerator from './WorldElementGenerator';
import WorldConsistencyAnalyzer from './WorldConsistencyAnalyzer';
import './WorldAIPanel.css';

/**
 * Component that integrates all AI features for the World Building page
 * @param {Object} props - Component props
 * @returns {JSX.Element} WorldAIPanel UI
 */
const WorldAIPanel = ({
  activeCategory,
  onElementsGenerated,
  onAnalysisComplete
}) => {
  // State for active tab
  const [activeTab, setActiveTab] = useState('generate');

  // Handle tab change
  const handleTabChange = (tab) => {
    setActiveTab(tab);
  };

  // Render tab content
  const renderTabContent = () => {
    switch (activeTab) {
      case 'generate':
        return (
          <WorldElementGenerator
            categoryId={activeCategory?.category_id}
            onElementsGenerated={onElementsGenerated}
          />
        );
      case 'analyze':
        return (
          <WorldConsistencyAnalyzer
            onAnalysisComplete={onAnalysisComplete}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="world-ai-panel">
      <div className="ai-panel-header">
        <h2>AI Assistant</h2>
        <p>Use AI to enhance your world building</p>
      </div>

      <div className="ai-tabs">
        <button
          className={`ai-tab ${activeTab === 'generate' ? 'active' : ''}`}
          onClick={() => handleTabChange('generate')}
        >
          Generate
        </button>
        <button
          className={`ai-tab ${activeTab === 'analyze' ? 'active' : ''}`}
          onClick={() => handleTabChange('analyze')}
        >
          Analyze
        </button>
      </div>

      <div className="ai-tab-content">
        {renderTabContent()}
      </div>
    </div>
  );
};

export default WorldAIPanel;

// frontend/src/components/BookList.js
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import './BookList.css';

/**
 * BookList component for displaying and managing books
 * @param {Object} props - Component props
 * @returns {JSX.Element} BookList UI
 */
const BookList = ({
  books,
  selectedBook,
  isLoading,
  isSaving,
  error,
  onSelectBook,
  onCreateBook,
  onUpdateBook,
  onDeleteBook,
  onClearError
}) => {
  const navigate = useNavigate();
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [editingBookId, setEditingBookId] = useState(null);
  const [formData, setFormData] = useState({
    title: '',
    author: '',
    genre: '',
    otherGenre: '',
    description: ''
  });

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Get the effective genre (either selected genre or otherGenre if "other" is selected)
  const getEffectiveGenre = () => {
    return formData.genre === 'other' && formData.otherGenre ? formData.otherGenre : formData.genre;
  };

  // Handle create book form submission
  const handleCreateSubmit = (e) => {
    e.preventDefault();
    // Use the effective genre in the submitted data
    const submittedData = {
      ...formData,
      genre: getEffectiveGenre()
    };
    onCreateBook(submittedData);
    setFormData({
      title: '',
      author: '',
      genre: '',
      otherGenre: '',
      description: ''
    });
    setShowCreateForm(false);
  };

  // Handle edit book form submission
  const handleEditSubmit = (e) => {
    e.preventDefault();
    // Use the effective genre in the submitted data
    const submittedData = {
      ...formData,
      genre: getEffectiveGenre()
    };
    onUpdateBook({ bookId: editingBookId, bookData: submittedData });
    setFormData({
      title: '',
      author: '',
      genre: '',
      otherGenre: '',
      description: ''
    });
    setEditingBookId(null);
  };

  // Start editing a book
  const handleStartEdit = (book) => {
    setEditingBookId(book.book_id);
    // Check if the book's genre is one of our predefined options
    const predefinedGenres = ['fantasy', 'sci-fi', 'mystery', 'romance', 'thriller', 'horror', 'historical', 'contemporary', 'non-fiction'];
    const isOtherGenre = book.genre && !predefinedGenres.includes(book.genre.toLowerCase());

    setFormData({
      title: book.title || '',
      author: book.author || '',
      genre: isOtherGenre ? 'other' : (book.genre || ''),
      otherGenre: isOtherGenre ? book.genre : '',
      description: book.description || ''
    });
  };

  // Cancel editing
  const handleCancelEdit = () => {
    setEditingBookId(null);
    setFormData({
      title: '',
      author: '',
      genre: '',
      otherGenre: '',
      description: ''
    });
  };

  // Handle book deletion
  const handleDeleteBook = (bookId) => {
    if (window.confirm('Are you sure you want to delete this book? This action cannot be undone.')) {
      onDeleteBook(bookId);
    }
  };

  // Render create book form
  const renderCreateForm = () => (
    <div className="book-form-container">
      <h3>Create New Book</h3>
      <form onSubmit={handleCreateSubmit} className="book-form">
        <div className="form-group">
          <label htmlFor="title">Title</label>
          <input
            type="text"
            id="title"
            name="title"
            value={formData.title}
            onChange={handleInputChange}
            required
          />
        </div>

        <div className="form-group">
          <label htmlFor="author">Author</label>
          <input
            type="text"
            id="author"
            name="author"
            value={formData.author}
            onChange={handleInputChange}
            required
          />
        </div>

        <div className="form-group">
          <label htmlFor="genre">Genre</label>
          <select
            id="genre"
            name="genre"
            value={formData.genre}
            onChange={handleInputChange}
            required
          >
            <option value="">Select Genre</option>
            <option value="fantasy">Fantasy</option>
            <option value="sci-fi">Science Fiction</option>
            <option value="mystery">Mystery</option>
            <option value="romance">Romance</option>
            <option value="thriller">Thriller</option>
            <option value="horror">Horror</option>
            <option value="historical">Historical</option>
            <option value="contemporary">Contemporary</option>
            <option value="non-fiction">Non-Fiction</option>
            <option value="other">Other</option>
          </select>
        </div>

        {formData.genre === 'other' && (
          <div className="form-group">
            <label htmlFor="otherGenre">Specify Genre</label>
            <input
              type="text"
              id="otherGenre"
              name="otherGenre"
              value={formData.otherGenre}
              onChange={handleInputChange}
              placeholder="Enter custom genre"
              required
            />
          </div>
        )}

        <div className="form-group">
          <label htmlFor="description">Synopsis</label>
          <textarea
            id="description"
            name="description"
            value={formData.description}
            onChange={handleInputChange}
            rows={4}
          />
        </div>

        <div className="form-actions">
          <button type="submit" className="save-button" disabled={isSaving}>
            {isSaving ? 'Creating...' : 'Create Book'}
          </button>
          <button
            type="button"
            className="cancel-button"
            onClick={() => setShowCreateForm(false)}
            disabled={isSaving}
          >
            Cancel
          </button>
        </div>
      </form>
    </div>
  );

  // Render edit book form
  const renderEditForm = () => {
    const book = books.find(b => b.book_id === editingBookId);
    if (!book) return null;

    return (
      <div className="book-form-container">
        <h3>Edit Book: {book.title}</h3>
        <form onSubmit={handleEditSubmit} className="book-form">
          <div className="form-group">
            <label htmlFor="edit-title">Title</label>
            <input
              type="text"
              id="edit-title"
              name="title"
              value={formData.title}
              onChange={handleInputChange}
              required
            />
          </div>

          <div className="form-group">
            <label htmlFor="edit-author">Author</label>
            <input
              type="text"
              id="edit-author"
              name="author"
              value={formData.author}
              onChange={handleInputChange}
              required
            />
          </div>

          <div className="form-group">
            <label htmlFor="edit-genre">Genre</label>
            <select
              id="edit-genre"
              name="genre"
              value={formData.genre}
              onChange={handleInputChange}
              required
            >
              <option value="">Select Genre</option>
              <option value="fantasy">Fantasy</option>
              <option value="sci-fi">Science Fiction</option>
              <option value="mystery">Mystery</option>
              <option value="romance">Romance</option>
              <option value="thriller">Thriller</option>
              <option value="horror">Horror</option>
              <option value="historical">Historical</option>
              <option value="contemporary">Contemporary</option>
              <option value="non-fiction">Non-Fiction</option>
              <option value="other">Other</option>
            </select>
          </div>

          {formData.genre === 'other' && (
            <div className="form-group">
              <label htmlFor="edit-otherGenre">Specify Genre</label>
              <input
                type="text"
                id="edit-otherGenre"
                name="otherGenre"
                value={formData.otherGenre}
                onChange={handleInputChange}
                placeholder="Enter custom genre"
                required
              />
            </div>
          )}

          <div className="form-group">
            <label htmlFor="edit-description">Synopsis</label>
            <textarea
              id="edit-description"
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              rows={4}
            />
          </div>

          <div className="form-actions">
            <button type="submit" className="save-button" disabled={isSaving}>
              {isSaving ? 'Saving...' : 'Save Changes'}
            </button>
            <button
              type="button"
              className="cancel-button"
              onClick={handleCancelEdit}
              disabled={isSaving}
            >
              Cancel
            </button>
          </div>
        </form>
      </div>
    );
  };

  // Render book card
  const renderBookCard = (book) => {
    const isSelected = selectedBook && selectedBook.book_id === book.book_id;

    // Check if book has complete details
    const hasCompleteDetails = book.title &&
                              typeof book.author !== 'undefined' &&
                              typeof book.genre !== 'undefined' &&
                              typeof book.description !== 'undefined';

    return (
      <div
        key={book.book_id}
        className={`book-card ${isSelected ? 'selected' : ''} ${!hasCompleteDetails ? 'loading-details' : ''}`}
        onClick={() => {
          onSelectBook(book);
          // Save to localStorage for persistence
          localStorage.setItem('lastBookId', book.book_id);
        }}
      >
        <div className="book-card-content">
          <h3 className="book-title">{book.title}</h3>
          {hasCompleteDetails ? (
            <>
              <p className="book-author">by {book.author || 'Unknown'}</p>
              {book.genre && <p className="book-genre">{book.genre}</p>}
              {book.description && <p className="book-description">{book.description}</p>}
            </>
          ) : (
            <div className="book-loading-details">
              <p>Loading book details...</p>
            </div>
          )}
        </div>

        <div className="book-card-actions">
          <button
            className="edit-button"
            onClick={(e) => {
              e.stopPropagation();
              handleStartEdit(book);
            }}
          >
            Edit
          </button>
          <button
            className="delete-button"
            onClick={(e) => {
              e.stopPropagation();
              handleDeleteBook(book.book_id);
            }}
          >
            Delete
          </button>
          <button
            className="select-button"
            onClick={(e) => {
              e.stopPropagation();
              // Select the book - this will save to localStorage in the container
              onSelectBook(book);

              // Wait a moment before navigating to ensure the book is selected
              setTimeout(() => {
                // Navigate to the world page
                navigate('/world');
              }, 100);
            }}
          >
            Select & Go to World
          </button>
        </div>
      </div>
    );
  };

  return (
    <div className="book-list-container">
      <div className="book-list-header">
        <h2>Your Books</h2>
        <button
          className="create-book-button"
          onClick={() => setShowCreateForm(true)}
          disabled={showCreateForm || editingBookId !== null}
        >
          Create New Book
        </button>
      </div>

      {error && (
        <div className="error-message">
          <p>{error}</p>
          <button onClick={onClearError}>Dismiss</button>
        </div>
      )}

      {showCreateForm && renderCreateForm()}
      {editingBookId !== null && renderEditForm()}

      {isLoading ? (
        <div className="loading-indicator">Loading books...</div>
      ) : books.length === 0 ? (
        <div className="empty-state">
          <p>You don't have any books yet.</p>
          <p>Create your first book to get started!</p>
        </div>
      ) : (
        <div className="book-grid">
          {books.map(book => renderBookCard(book))}
        </div>
      )}
    </div>
  );
};

export default BookList;

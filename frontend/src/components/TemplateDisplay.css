/* frontend/src/components/TemplateDisplay.css */
.template-display {
  margin-bottom: 20px;
  background-color: var(--background-secondary);
  border-radius: 6px;
  padding: 16px;
  overflow-y: auto;
  max-height: 100%;
}

.element-header {
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--border-color);
}

.element-name {
  margin: 0 0 8px 0;
  color: var(--text-primary);
  font-size: 1.5rem;
}

.element-meta {
  display: flex;
  align-items: center;
  gap: 12px;
}

.element-type {
  background-color: var(--primary-light);
  color: var(--primary);
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
}

.template-version {
  color: var(--text-tertiary);
  font-size: 0.8rem;
}

.basic-fields {
  margin-bottom: 24px;
}

.description-field {
  margin-bottom: 16px;
}

.description-field h3 {
  margin: 0 0 8px 0;
  font-size: 1rem;
  color: var(--text-secondary);
}

.description-content {
  line-height: 1.5;
  color: var(--text-primary);
}

.tags-field {
  margin-bottom: 16px;
}

.tags-field h3 {
  margin: 0 0 8px 0;
  font-size: 1rem;
  color: var(--text-secondary);
}

.tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag {
  background-color: var(--background-tertiary);
  color: var(--text-secondary);
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
}

.importance-field h3 {
  margin: 0 0 8px 0;
  font-size: 1rem;
  color: var(--text-secondary);
}

.importance-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
}

.importance-badge.low {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.importance-badge.medium {
  background-color: #fff8e1;
  color: #f57f17;
}

.importance-badge.high {
  background-color: #ffebee;
  color: #c62828;
}

.field-group {
  margin-bottom: 16px;
}

.group-title {
  margin: 0 0 12px 0;
  padding: 8px 0;
  font-size: 1rem;
  color: var(--text-primary);
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid var(--border-color-light);
}

.group-title .expand-icon {
  font-size: 0.8rem;
  color: var(--text-tertiary);
}

.group-fields {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.field-item {
  margin-bottom: 12px;
}

.field-label {
  font-weight: 500;
  color: var(--text-secondary);
  margin-bottom: 4px;
  font-size: 0.9rem;
}

.field-value {
  color: var(--text-primary);
}

.textarea-value {
  line-height: 1.5;
  white-space: pre-wrap;
}

.checkbox-value {
  font-weight: 500;
}

.edit-element-button {
  margin-top: 16px;
  padding: 8px 16px;
  background-color: var(--primary);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
}

.edit-element-button:hover {
  background-color: var(--primary-dark);
}

.template-display-error {
  padding: 16px;
  background-color: #ffebee;
  border: 1px solid #ffcdd2;
  border-radius: 4px;
  color: #c62828;
  margin-bottom: 20px;
}

@media (max-width: 768px) {
  .group-fields {
    grid-template-columns: 1fr;
  }
}

/* frontend/src/components/AISidebar.css */
.ai-sidebar {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 20;
  transition: all 0.3s ease;
}

.ai-sidebar.closed {
  width: 60px;
  height: 60px;
  border-radius: 30px;
  background: #007bff;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  cursor: pointer;
}

.ai-sidebar.open {
  width: 300px;
  height: 400px;
  border-radius: 12px;
  background: #ffffff;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
}

.ai-toggle {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 24px;
  font-weight: bold;
  display: none;
}

.ai-sidebar.closed .ai-toggle {
  display: block;
}

.ai-close {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 20px;
  height: 20px;
  display: none; /* Hidden when closed */
  background: #ff4d4d;
  color: white;
  border-radius: 10px;
  text-align: center;
  line-height: 20px;
  font-size: 14px;
  cursor: pointer;
  transition: background 0.2s;
}

.ai-sidebar.open .ai-close {
  display: block; /* Visible when open */
}

.ai-close:hover {
  background: #cc0000;
}

.ai-content {
  flex: 1;
  display: none;
  padding: 15px;
  overflow-y: auto;
}

.ai-sidebar.open .ai-content {
  display: flex;
  flex-direction: column;
}

.ai-responses {
  flex: 1;
  overflow-y: auto;
  margin-bottom: 10px;
  gap: 10px;
}

.ai-response {
  width: 95%;
  max-height: 100px;
  overflow-y: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
  padding: 8px;
  background: #f5f5f5;
  border-radius: 8px;
  transition: background 0.2s;
}

.ai-response:hover {
  background: #e0e0e0;
}

.ai-suggestion {
  width: 95%;
  padding: 5px 10px;
  cursor: pointer;
  text-align: center;
  background: #007bff;
  color: white;
  border-radius: 6px;
}

.ai-empty-state {
  color: #6c757d;
  text-align: center;
  padding: 20px;
  font-style: italic;
}

.ai-prompt-container {
  width: 100%;
  padding-top: 10px;
  border-top: 1px solid #e0e0e0;
}

.ai-prompt-container input {
  width: 100%;
  padding: 8px;
  border: 1px solid #d0d0d0;
  border-radius: 4px;
  font-size: 14px;
}

.ai-action-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 10px;
  width: 100%;
}

.refine-btn, .connect-btn {
  width: 95%;
  padding: 6px;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background 0.2s;
  margin: 0 auto;
}

.refine-btn {
  background: #28a745;
}

.refine-btn:hover {
  background: #218838;
}

.connect-btn {
  background: #6610f2;
}

.connect-btn:hover {
  background: #520dc2;
}
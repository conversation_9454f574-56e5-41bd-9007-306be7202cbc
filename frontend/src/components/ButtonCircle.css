/* frontend/src/components/ButtonCircle.css */
.button-circle {
  width: 40px;
  height: 40px;
  border-radius: 20px; /* Ensures circular shape */
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #007bff;
  color: white;
  border: none; /* Remove any border that might square it */
  cursor: pointer;
  transition: background 0.2s, transform 0.1s;
  overflow: hidden; /* Prevent content from squaring it */
}

.theme-dark .button-circle {
  background: #BB86FC;
}

.button-circle:hover {
  background: #0056b3;
  transform: scale(1.1);
}

.theme-dark .button-circle:hover {
  background: #A66EFC;
  transform: scale(1.1);
}
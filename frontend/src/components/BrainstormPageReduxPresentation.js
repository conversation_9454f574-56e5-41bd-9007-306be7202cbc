// frontend/src/components/BrainstormPageReduxPresentation.js
import React, { useState, useCallback, useMemo, useEffect } from 'react';
import ReactFlow, { Background, Controls, MiniMap, Panel, useNodesState, useEdgesState } from 'reactflow';
import 'reactflow/dist/style.css';
import './BrainstormPage.css';
import CharacterSelector from './CharacterSelector';
import LocationSelector from './LocationSelector';
// Use a simple loading spinner instead of an image
const LoadingSpinner = () => (
  <div style={{
    display: 'inline-block',
    width: '50px',
    height: '50px',
    border: '5px solid rgba(0, 0, 0, 0.1)',
    borderRadius: '50%',
    borderTop: '5px solid #0d6efd',
    animation: 'spin 1s linear infinite',
  }}></div>
);

// Add the animation to the document
const styleSheet = document.createElement('style');
styleSheet.innerText = `@keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }`;
document.head.appendChild(styleSheet);

// Custom node component for brainstorm cards
const CardNode = ({ data, selected }) => {
  const nodeTypeColors = {
    idea: '#D8E2C8', // beige
    character: '#F5C4B4', // light green
    location: '#F8F0E3', // light blue
    event: '#C7E4E2', // light red
    default: '#E8C9BF' // light gray
  };

  const backgroundColor = nodeTypeColors[data.type] || nodeTypeColors.default;

  return (
    <div
      className={`card-node ${selected ? 'selected' : ''}`}
      style={{
        background: backgroundColor,
        border: selected ? '2px solid #ff0072' : '1px solid #ccc',
        borderRadius: '8px',
        padding: '10px',
        minWidth: '180px',
        maxWidth: '250px',
        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
      }}
    >
      <div className="card-type" style={{
        fontWeight: 'bold',
        fontSize: '12px',
        textTransform: 'uppercase',
        marginBottom: '5px',
        color: '#666'
      }}>
        {data.type || 'idea'}
      </div>
      <div className="card-title" style={{
        fontWeight: 'bold',
        fontSize: '16px',
        marginBottom: '8px',
        borderBottom: '1px solid #ddd',
        paddingBottom: '5px'
      }}>
        {data.label || data.title || 'Untitled'}
      </div>
      <div className="card-content" style={{
        fontSize: '14px',
        marginBottom: '10px',
        wordBreak: 'break-word'
      }}>
        {data.content}
      </div>
      {data.characters && data.characters.length > 0 && (
        <>
          <div style={{ fontSize: '0.8em', color: '#6c757d', marginTop: '5px', marginBottom: '3px' }}>
            Characters:
          </div>
          <div className="card-characters" style={{
            display: 'flex',
            flexWrap: 'wrap',
            gap: '5px',
            marginBottom: '5px'
          }}>
            {data.characters.map((character, index) => (
              <span key={index} style={{
                background: '#d1e7dd',
                padding: '2px 6px',
                borderRadius: '10px',
                fontSize: '12px'
              }}>
                {character}
              </span>
            ))}
          </div>
        </>
      )}
      {data.locations && data.locations.length > 0 && (
        <>
          <div style={{ fontSize: '0.8em', color: '#6c757d', marginTop: '5px', marginBottom: '3px' }}>
            World Elements:
          </div>
          <div className="card-world-elements" style={{
            display: 'flex',
            flexWrap: 'wrap',
            gap: '5px'
          }}>
            {data.locations.map((location, index) => (
              <span key={index} style={{
                background: '#cfe2ff',
                padding: '2px 6px',
                borderRadius: '10px',
                fontSize: '12px'
              }}>
                {location}
              </span>
            ))}
          </div>
        </>
      )}
      {data.onEdit && data.onDelete && (
        <div className="card-actions" style={{
          display: 'flex',
          justifyContent: 'flex-end',
          marginTop: '10px',
          gap: '5px'
        }}>
          <button
            onClick={data.onEdit}
            style={{
              background: '#6c757d',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              padding: '4px 8px',
              fontSize: '12px',
              cursor: 'pointer'
            }}
          >
            Edit
          </button>
          <button
            onClick={data.onDelete}
            style={{
              background: '#dc3545',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              padding: '4px 8px',
              fontSize: '12px',
              cursor: 'pointer'
            }}
          >
            Delete
          </button>
        </div>
      )}
    </div>
  );
};

// Memoize the CardNode component
const MemoizedCardNode = React.memo(CardNode);

/**
 * Presentational component for BrainstormPage
 * @param {Object} props - Component props
 * @returns {JSX.Element} BrainstormPage UI
 */
const BrainstormPageReduxPresentation = ({
  onPageChange,
  onCardSelect,
  onCreateCard,
  onUpdateCard,
  onDeleteCard,
  onNodePositionChange,
  onGenerateIdea,
  onConnectIdeas,
  onClearError,
  onSendToPlot,
  onSendMultipleToPlot,
  currentPage,
  pages,
  cards,
  nodes,
  edges,
  selectedCard,
  isLoading,
  error,
  characters = [],
  worldElements = [],
  categories = [],
  onCreateCharacter,
  onCreateLocation
}) => {
  // State for card form modal
  const [showCardModal, setShowCardModal] = useState(false);
  const [cardFormData, setCardFormData] = useState({
    title: '',
    content: '',
    type: 'idea',
    characters: [],
    locations: []
  });
  const [editingCardId, setEditingCardId] = useState(null);

  // State for multi-select
  const [selectedNodes, setSelectedNodes] = useState([]);
  const [isMultiSelectMode, setIsMultiSelectMode] = useState(false);
  const [localSelectedCard, setSelectedCard] = useState(selectedCard);

  // ReactFlow state
  const [reactFlowInstance, setReactFlowInstance] = useState(null);

  // Use ReactFlow's internal state management
  const [rfNodes, setRfNodes, onNodesChange] = useNodesState([]);
  const [rfEdges, setRfEdges, onEdgesChange] = useEdgesState([]);

  // Node types configuration for ReactFlow
  const nodeTypes = useMemo(() => ({ cardNode: MemoizedCardNode }), []);

  // Handle page button click
  const handlePageButtonClick = (page) => {
    console.log('[DEBUG] Switching to page:', page);
    // Clear the selected nodes when switching pages
    setSelectedNodes([]);
    // Reset the node and edge states
    setRfNodes([]);
    setRfEdges([]);

    // Delay the page change slightly to ensure clean state transition
    setTimeout(() => {
      onPageChange(page);

      // We'll reset the viewport after the nodes are loaded in the useEffect below
    }, 50);
  };

  // Handle create card button click
  const handleCreateCardClick = () => {
    console.log('[DEBUG] Opening create card modal');
    console.log('[DEBUG] Characters available:', characters);
    console.log('[DEBUG] World elements available:', worldElements);

    // If no characters or world elements are available, show a message
    if ((!characters || characters.length === 0) && (!worldElements || worldElements.length === 0)) {
      console.log('[DEBUG] No characters or world elements available');
      alert('Loading characters and world elements. Please try again in a moment.');
      return;
    }

    setEditingCardId(null);
    setCardFormData({
      title: '',
      content: '',
      type: 'idea',
      characters: [],
      locations: [],
      page: currentPage
    });
    setShowCardModal(true);
  };

  // Handle edit card
  const handleEditCard = useCallback((cardId) => {
    const card = cards.find(c => c.id === cardId);
    if (card) {
      setEditingCardId(cardId);
      setCardFormData({
        title: card.title || '',
        content: card.content || '',
        type: card.type || 'idea',
        characters: card.characters || [],
        locations: card.locations || [],
        page: card.page || currentPage
      });
      setShowCardModal(true);
    }
  }, [cards, currentPage]);

  // Handle form submit
  const handleFormSubmit = (e) => {
    e.preventDefault();

    if (editingCardId) {
      onUpdateCard(editingCardId, cardFormData);
    } else {
      // Add position data if creating a new card
      const position = reactFlowInstance ? {
        x: Math.random() * (reactFlowInstance.width / 2) + 100,
        y: Math.random() * (reactFlowInstance.height / 2) + 100
      } : { x: 250, y: 250 };

      onCreateCard({
        ...cardFormData,
        position,
        page: currentPage
      });

      // Center the view after a short delay to ensure the new card is visible
      setTimeout(() => {
        if (reactFlowInstance) {
          console.log('[DEBUG] Centering view after creating new card');
          reactFlowInstance.fitView({ padding: 0.2, includeHiddenNodes: false });
        }
      }, 300);
    }

    setShowCardModal(false);
  };

  // Handle generate idea button click
  const handleGenerateIdeaClick = () => {
    console.log('[DEBUG] Generating AI idea for current page:', currentPage);
    onGenerateIdea(currentPage);

    // Center the view after a short delay to allow new nodes to be added
    setTimeout(() => {
      if (reactFlowInstance) {
        console.log('[DEBUG] Centering view after generating AI ideas');
        reactFlowInstance.fitView({ padding: 0.2, includeHiddenNodes: false });
      }
    }, 1000); // Longer delay to ensure API call completes and nodes are added
  };

  // Handle node drag stop
  const onNodeDragStop = useCallback((_event, node) => {
    console.log('Node drag stop:', node.id, node.position);

    // Validate position data
    if (!node.position || typeof node.position.x !== 'number' || typeof node.position.y !== 'number' ||
        isNaN(node.position.x) || isNaN(node.position.y)) {
      console.error('Invalid node position:', node.position);
      return;
    }

    // Ensure position values are valid numbers
    const validPosition = {
      x: Math.round(node.position.x) || 0,
      y: Math.round(node.position.y) || 0
    };

    // Update the position in Redux
    onNodePositionChange(node.id, validPosition);
  }, [onNodePositionChange]);

  // Handle node click
  const onNodeClick = useCallback((event, node) => {
    console.log('[DEBUG] Node clicked:', node);

    // Check if Ctrl key is pressed for multi-select
    if (event.ctrlKey || isMultiSelectMode) {
      setIsMultiSelectMode(true);

      // Toggle selection of the node
      let updatedSelectedNodes;
      if (selectedNodes.some(n => n.id === node.id)) {
        updatedSelectedNodes = selectedNodes.filter(n => n.id !== node.id);
        setSelectedNodes(updatedSelectedNodes);
      } else {
        updatedSelectedNodes = [...selectedNodes, node];
        setSelectedNodes(updatedSelectedNodes);
      }

      // Dispatch a custom event to notify the AISidebarContainer
      const customEvent = new CustomEvent('selectedNodesChanged', {
        detail: { selectedNodes: updatedSelectedNodes }
      });
      window.dispatchEvent(customEvent);
    } else {
      // Regular single selection
      setSelectedNodes([]);
      onCardSelect(node.id);

      // Find the card in the cards array
      const card = cards.find(c => c.id === node.id);
      if (card) {
        console.log('[DEBUG] Selected card:', card);
        // Set the selected card directly
        setSelectedCard(card);
      }

      // Dispatch a custom event to clear selected nodes
      const customEvent = new CustomEvent('selectedNodesChanged', {
        detail: { selectedNodes: [] }
      });
      window.dispatchEvent(customEvent);
    }
  }, [onCardSelect, selectedNodes, isMultiSelectMode, cards]);

  // Handle keyboard events for multi-select
  useEffect(() => {
    const handleKeyDown = (e) => {
      // Skip keyboard shortcuts if a modal is open or if the target is an input element
      if (showCardModal ||
          e.target.tagName === 'INPUT' ||
          e.target.tagName === 'TEXTAREA' ||
          e.target.tagName === 'SELECT' ||
          e.target.isContentEditable) {
        return;
      }

      // Ctrl+A to select all nodes
      if (e.ctrlKey && e.key === 'a') {
        e.preventDefault();
        setIsMultiSelectMode(true);
        setSelectedNodes(rfNodes);

        // Dispatch a custom event to notify the AISidebarContainer
        const customEvent = new CustomEvent('selectedNodesChanged', {
          detail: { selectedNodes: rfNodes }
        });
        window.dispatchEvent(customEvent);
      }

      // Escape to clear selection
      if (e.key === 'Escape') {
        setIsMultiSelectMode(false);
        setSelectedNodes([]);

        // Dispatch a custom event to clear selected nodes
        const customEvent = new CustomEvent('selectedNodesChanged', {
          detail: { selectedNodes: [] }
        });
        window.dispatchEvent(customEvent);
      }

      // Delete or Backspace to delete selected nodes
      if ((e.key === 'Delete' || e.key === 'Backspace') && selectedNodes.length > 0) {
        e.preventDefault();
        console.log('[DEBUG] Deleting selected nodes with keyboard shortcut');
        selectedNodes.forEach(node => {
          onDeleteCard(node.id);
        });
        setSelectedNodes([]);

        // Dispatch a custom event to clear selected nodes
        const customEvent = new CustomEvent('selectedNodesChanged', {
          detail: { selectedNodes: [] }
        });
        window.dispatchEvent(customEvent);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [rfNodes, selectedNodes, onDeleteCard, showCardModal]);

  // Enhance nodes with edit and delete handlers
  const enhancedNodes = useMemo(() => {
    return nodes.map(node => ({
      ...node,
      data: {
        ...node.data,
        onEdit: () => handleEditCard(node.id),
        onDelete: () => onDeleteCard(node.id)
      }
    }));
  }, [nodes, onDeleteCard, handleEditCard]);

  // Filter nodes and edges by current page
  const currentPageNodes = useMemo(() => {
    return enhancedNodes.filter(node => {
      const card = cards.find(c => c.id === node.id);
      return card && card.page === currentPage;
    });
  }, [enhancedNodes, cards, currentPage]);

  const currentPageEdges = useMemo(() => {
    return edges.filter(edge => {
      const sourceCard = cards.find(c => c.id === edge.source);
      const targetCard = cards.find(c => c.id === edge.target);
      return sourceCard && targetCard &&
             sourceCard.page === currentPage &&
             targetCard.page === currentPage;
    });
  }, [edges, cards, currentPage]);

  // Track page changes for viewport centering
  const [lastPage, setLastPage] = useState(currentPage);

  // Update ReactFlow's internal state when our filtered nodes/edges change
  useEffect(() => {
    // Use a debounced function to reduce the frequency of updates
    const updateNodes = () => {
      // Clear nodes when switching pages
      setRfNodes([]);

      // Only set nodes if we have valid nodes for the current page
      if (currentPageNodes.length > 0) {
        console.log('[DEBUG] Setting nodes in ReactFlow for page', currentPage, ':', currentPageNodes);

        // Create a Set of node IDs to track duplicates
        const nodeIds = new Set();

        // Ensure all nodes have valid positions and filter out duplicates
        const validNodes = currentPageNodes
          .filter(node => {
            // Skip nodes without IDs
            if (!node.id) {
              console.warn('[DEBUG] Skipping node without ID:', node);
              return false;
            }

            // Check for duplicate IDs
            if (nodeIds.has(node.id)) {
              console.warn('[DEBUG] Skipping duplicate node:', node.id);
              return false;
            }

            // Add this ID to the set
            nodeIds.add(node.id);
            return true;
          })
          .map(node => ({
            ...node,
            position: {
              x: typeof node.position?.x === 'number' && !isNaN(node.position.x) ? node.position.x : 100,
              y: typeof node.position?.y === 'number' && !isNaN(node.position.y) ? node.position.y : 100
            }
          }));

        console.log('[DEBUG] Setting', validNodes.length, 'valid nodes after filtering');

        // Use requestAnimationFrame for smoother updates
        requestAnimationFrame(() => {
          setRfNodes(validNodes);
        });

        // Only center the view when the page changes or on initial load
        if (currentPage !== lastPage && reactFlowInstance) {
          console.log('[DEBUG] Page changed from', lastPage, 'to', currentPage, '- centering view');
          // Schedule a viewport reset after nodes are rendered
          requestAnimationFrame(() => {
            reactFlowInstance.fitView({ padding: 0.2, includeHiddenNodes: false, duration: 200 });
          });

          // Update the last page
          setLastPage(currentPage);
        }
      }
    };

    // Use a small timeout to debounce the updates
    const timeoutId = setTimeout(updateNodes, 50);
    return () => clearTimeout(timeoutId);
  }, [currentPageNodes, setRfNodes, currentPage, reactFlowInstance, lastPage]);

  useEffect(() => {
    // Use a debounced function to reduce the frequency of updates
    const updateEdges = () => {
      if (currentPageEdges.length > 0) {
        console.log('Setting edges in ReactFlow:', currentPageEdges);
        // Use requestAnimationFrame for smoother updates
        requestAnimationFrame(() => {
          setRfEdges(currentPageEdges);
        });
      }
    };

    // Use a small timeout to debounce the updates
    const timeoutId = setTimeout(updateEdges, 50);
    return () => clearTimeout(timeoutId);
  }, [currentPageEdges, setRfEdges]);

  // Sync selectedCard prop with local state
  useEffect(() => {
    if (selectedCard) {
      console.log('[DEBUG] Syncing selectedCard prop with local state:', selectedCard);
      setSelectedCard(selectedCard);
    }
  }, [selectedCard]);

  // Memoize the page buttons to prevent unnecessary re-renders
  const pageButtons = useMemo(() => {
    return pages.map(page => (
      <button
        key={page}
        className={`page-tab ${currentPage === page ? 'active' : ''}`}
        style={{
          padding: '8px 16px',
          border: 'none',
          borderRadius: '4px',
          background: currentPage === page ? '#0d6efd' : '#f8f9fa',
          color: currentPage === page ? 'white' : '#212529',
          cursor: 'pointer'
        }}
        onClick={() => handlePageButtonClick(page)}
      >
        {page}
      </button>
    ));
  }, [pages, currentPage, handlePageButtonClick]);

  return (
    <div className="brainstorm-page" style={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      <div className="brainstorm-header" style={{ padding: '10px', borderBottom: '1px solid #ddd' }}>
        <h2>Brainstorm</h2>
        <div className="page-navigation" style={{ display: 'flex', gap: '10px', marginTop: '10px' }}>
          {pageButtons}
        </div>
      </div>

      {error && (
        <div className="error-message" style={{
          padding: '10px',
          margin: '10px',
          background: '#f8d7da',
          color: '#721c24',
          borderRadius: '4px',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}>
          <p style={{ margin: 0 }}>{error}</p>
          <button
            onClick={onClearError}
            style={{
              background: 'transparent',
              border: 'none',
              color: '#721c24',
              cursor: 'pointer',
              fontWeight: 'bold'
            }}
          >
            ×
          </button>
        </div>
      )}

      <div className="brainstorm-content" style={{ flex: 1, position: 'relative' }}>
        {isLoading ? (
          <div className="loading" style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: '100%'
          }}>
            <div style={{ textAlign: 'center' }}>
              <LoadingSpinner />
              <p>Loading brainstorm data...</p>
            </div>
          </div>
        ) : (
          <div style={{ width: '100%', height: '100%' }}>
            <ReactFlow
              nodes={rfNodes}
              edges={rfEdges}
              onNodesChange={onNodesChange}
              onEdgesChange={onEdgesChange}
              onNodeDragStop={onNodeDragStop}
              onNodeClick={onNodeClick}
              nodeTypes={nodeTypes}
              onInit={(instance) => {
                setReactFlowInstance(instance);
                // Center the view on nodes when ReactFlow is initialized
                // Use requestAnimationFrame for better performance
                requestAnimationFrame(() => {
                  instance.fitView({ padding: 0.2, includeHiddenNodes: false });
                });
              }}
              fitView
              fitViewOptions={{ padding: 0.2, includeHiddenNodes: false, duration: 200 }}
              defaultViewport={{ x: 0, y: 0, zoom: 1 }}
              minZoom={0.2}
              maxZoom={2}
              snapToGrid={true}
              snapGrid={[15, 15]}
            >
              <Controls />
              <MiniMap />
              <Background color="#f8f9fa" gap={16} />
              <Panel position="top-right">
                <div style={{ display: 'flex', gap: '10px' }}>
                  <button
                    onClick={handleCreateCardClick}
                    style={{
                      background: '#0d6efd',
                      color: 'white',
                      border: 'none',
                      borderRadius: '4px',
                      padding: '8px 16px',
                      cursor: 'pointer'
                    }}
                  >
                    Add Card
                  </button>
                  <button
                    onClick={handleGenerateIdeaClick}
                    style={{
                      background: '#6c757d',
                      color: 'white',
                      border: 'none',
                      borderRadius: '4px',
                      padding: '8px 16px',
                      cursor: 'pointer'
                    }}
                  >
                    Generate AI Idea
                  </button>
                  {selectedNodes.length >= 2 && selectedNodes.length <= 5 && (
                    <button
                      onClick={() => {
                        console.log('[DEBUG] Connect Ideas button clicked');
                        console.log('[DEBUG] Selected nodes:', selectedNodes);
                        onConnectIdeas(selectedNodes, currentPage);

                        // Center the view after a short delay to allow new nodes to be added
                        setTimeout(() => {
                          if (reactFlowInstance) {
                            console.log('[DEBUG] Centering view after connecting ideas');
                            reactFlowInstance.fitView({ padding: 0.2, includeHiddenNodes: false });
                          }
                        }, 1000); // Longer delay to ensure API call completes and nodes are added
                      }}
                      style={{
                        background: '#6610f2',
                        color: 'white',
                        border: 'none',
                        borderRadius: '4px',
                        padding: '8px 16px',
                        cursor: 'pointer'
                      }}
                    >
                      Connect {selectedNodes.length} Ideas
                    </button>
                  )}
                  {selectedNodes.length > 0 ? (
                    <button
                      onClick={() => {
                        console.log('[DEBUG] Send to Plot button clicked (multi-select)');
                        console.log('[DEBUG] Selected nodes:', selectedNodes);

                        // Extract node IDs from selected nodes
                        const nodeIds = selectedNodes.map(node => node.id);
                        console.log('[DEBUG] Sending nodes to plot:', nodeIds);

                        // Use the new handleSendMultipleToPlot function
                        if (typeof onSendMultipleToPlot === 'function') {
                          // If the container provides the new function, use it
                          onSendMultipleToPlot(nodeIds);
                        } else {
                          // Fallback to the old method
                          selectedNodes.forEach(node => {
                            console.log('[DEBUG] Sending node to plot (fallback):', node);
                            onSendToPlot(node.id, true);
                          });

                          // Show success message after all cards are sent
                          alert(`${selectedNodes.length} cards sent to Plot Bank`);
                        }
                        setSelectedNodes([]);
                        setIsMultiSelectMode(false);
                      }}
                      style={{
                        background: '#28a745',
                        color: 'white',
                        border: 'none',
                        borderRadius: '4px',
                        padding: '8px 16px',
                        cursor: 'pointer'
                      }}
                    >
                      Send to Plot ({selectedNodes.length})
                    </button>
                  ) : localSelectedCard ? (
                    <button
                      onClick={() => {
                        console.log('[DEBUG] Send to Plot button clicked (single card)');
                        console.log('[DEBUG] Selected card:', localSelectedCard);
                        onSendToPlot(localSelectedCard.id);
                      }}
                      style={{
                        background: '#28a745',
                        color: 'white',
                        border: 'none',
                        borderRadius: '4px',
                        padding: '8px 16px',
                        cursor: 'pointer'
                      }}
                    >
                      Send to Plot
                    </button>
                  ) : null}

                  {/* Multi-select mode indicator */}
                  {isMultiSelectMode && (
                    <span style={{
                      background: '#e9ecef',
                      padding: '8px 16px',
                      borderRadius: '4px',
                      color: '#495057',
                      fontWeight: 'bold'
                    }}>
                      Multi-Select Mode
                    </span>
                  )}

                  {/* Help button for keyboard shortcuts */}
                  <button
                    onClick={() => alert('Keyboard Shortcuts:\n\n• Ctrl+Click: Select/deselect a node\n• Ctrl+A: Select all nodes\n• Delete/Backspace: Delete selected node(s)\n• Escape: Clear selection')}
                    style={{
                      background: '#f8f9fa',
                      color: '#212529',
                      border: '1px solid #dee2e6',
                      borderRadius: '4px',
                      padding: '8px 16px',
                      cursor: 'pointer'
                    }}
                  >
                    ? Shortcuts
                  </button>
                </div>
              </Panel>
            </ReactFlow>
          </div>
        )}
      </div>

      {/* Card Form Modal */}
      {showCardModal && (
        <div className="modal-overlay" style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'rgba(0, 0, 0, 0.5)',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          zIndex: 1000
        }}>
          <div className="modal-content" style={{
            background: 'white',
            padding: '20px',
            borderRadius: '8px',
            width: '500px',
            maxWidth: '90%'
          }}>
            <h3>{editingCardId ? 'Edit Card' : 'Create New Card'}</h3>
            <form onSubmit={handleFormSubmit}>
              <div className="form-group" style={{ marginBottom: '15px' }}>
                <label style={{ display: 'block', marginBottom: '5px' }}>Title</label>
                <input
                  type="text"
                  value={cardFormData.title}
                  onChange={e => setCardFormData({...cardFormData, title: e.target.value})}
                  style={{
                    width: '100%',
                    padding: '8px',
                    borderRadius: '4px',
                    border: '1px solid #ced4da'
                  }}
                  required
                />
              </div>
              <div className="form-group" style={{ marginBottom: '15px' }}>
                <label style={{ display: 'block', marginBottom: '5px' }}>Content</label>
                <textarea
                  value={cardFormData.content}
                  onChange={e => setCardFormData({...cardFormData, content: e.target.value})}
                  style={{
                    width: '100%',
                    padding: '8px',
                    borderRadius: '4px',
                    border: '1px solid #ced4da',
                    minHeight: '100px'
                  }}
                  required
                />
              </div>
              <div className="form-group" style={{ marginBottom: '15px' }}>
                <label style={{ display: 'block', marginBottom: '5px' }}>Type</label>
                <select
                  value={cardFormData.type}
                  onChange={e => setCardFormData({...cardFormData, type: e.target.value})}
                  style={{
                    width: '100%',
                    padding: '8px',
                    borderRadius: '4px',
                    border: '1px solid #ced4da'
                  }}
                >
                  <option value="idea">Idea</option>
                  <option value="character">Character</option>
                  <option value="location">Location</option>
                  <option value="event">Event</option>
                </select>
              </div>
              <div className="form-group" style={{ marginBottom: '15px' }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '5px' }}>
                  <label>Characters</label>
                  <span style={{ fontSize: '0.8em', color: '#6c757d' }}>
                    New characters will be created automatically
                  </span>
                </div>
                {console.log('Characters passed to selector:', characters)}
                <CharacterSelector
                  characters={characters}
                  selectedCharacters={cardFormData.characters}
                  onChange={(selectedCharacters) => setCardFormData({
                    ...cardFormData,
                    characters: selectedCharacters
                  })}
                  onCreateCharacter={onCreateCharacter}
                />
              </div>
              <div className="form-group" style={{ marginBottom: '15px' }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '5px' }}>
                  <label>World Elements</label>
                  <span style={{ fontSize: '0.8em', color: '#6c757d' }}>
                    New elements will be created automatically
                  </span>
                </div>
                {console.log('World elements passed to selector:', worldElements)}
                <LocationSelector
                  worldElements={worldElements}
                  selectedLocations={cardFormData.locations}
                  categories={categories}
                  onChange={(selectedLocations) => setCardFormData({
                    ...cardFormData,
                    locations: selectedLocations
                  })}
                  onCreateLocation={onCreateLocation}
                />
              </div>
              <div className="form-actions" style={{ display: 'flex', justifyContent: 'flex-end', gap: '10px' }}>
                <button
                  type="button"
                  onClick={() => setShowCardModal(false)}
                  className="cancel-button"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  style={{
                    padding: '8px 16px',
                    borderRadius: '4px',
                    border: 'none',
                    background: '#0d6efd',
                    color: 'white',
                    cursor: 'pointer'
                  }}
                >
                  {editingCardId ? 'Update' : 'Create'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

// Export with React.memo to prevent unnecessary re-renders
export default React.memo(BrainstormPageReduxPresentation);

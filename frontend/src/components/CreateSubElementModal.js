// frontend/src/components/CreateSubElementModal.js
import React, { useState, useEffect } from 'react';
import './CreateSubElementModal.css';
import TemplateFormGenerator from './TemplateFormGenerator';
import { useTemplateRegistry } from '../utils/TemplateRegistry';
import { getAvailableSubElementTypes } from '../utils/templateUtils';

const CreateSubElementModal = ({ parentElement, onClose, onCreateElement }) => {
  const [selectedTemplateId, setSelectedTemplateId] = useState('');
  const [formValues, setFormValues] = useState({
    name: '',
    description: '',
    tags: '',
    importance: 'medium',
    custom_fields: {}
  });
  const [availableTemplates, setAvailableTemplates] = useState([]);
  const [step, setStep] = useState('select-template'); // 'select-template' or 'fill-form'

  // Use the template registry hook to get templates from Redux
  const templateRegistry = useTemplateRegistry();

  // Get available templates based on parent element type
  useEffect(() => {
    if (parentElement) {
      const subElementTypes = getAvailableSubElementTypes(parentElement.element_type);
      const templates = subElementTypes.map(type => templateRegistry.getTemplate(type)).filter(Boolean);
      setAvailableTemplates(templates);
    }
  }, [parentElement, templateRegistry]);

  // Handle template selection
  const handleTemplateSelect = (templateId) => {
    setSelectedTemplateId(templateId);
    setStep('fill-form');
  };

  // Handle form value changes
  const handleFormChange = (values) => {
    setFormValues(values);
  };

  // Handle element creation
  const handleCreateElement = () => {
    if (!formValues.name || !selectedTemplateId) return;

    // Prepare tags array
    let tags = formValues.tags;
    if (typeof tags === 'string') {
      tags = tags.split(',').map(tag => tag.trim()).filter(Boolean);
    }

    const newElement = {
      name: formValues.name,
      description: formValues.description,
      tags,
      importance: formValues.importance,
      element_type: selectedTemplateId,
      parent_id: parentElement.element_id,
      custom_fields: formValues.custom_fields
    };

    onCreateElement(newElement);
  };

  return (
    <div className="modal-overlay">
      <div className="create-sub-element-modal">
        <div className="modal-header">
          <h2>{step === 'select-template' ? 'Select Template' : 'Create New Element'}</h2>
          <button className="close-button" onClick={onClose}>×</button>
        </div>

        <div className="modal-content">
          {step === 'select-template' ? (
            /* Template selection step */
            <div className="template-selection">
              <p>Select a template for the new element:</p>

              <div className="templates-grid">
                {availableTemplates.map(template => (
                  <div
                    key={template.id}
                    className="template-card"
                    onClick={() => handleTemplateSelect(template.id)}
                  >
                    <h3>{template.label || template.id}</h3>
                    {template.description && (
                      <p>{template.description}</p>
                    )}
                    <span className="template-type">{template.category || 'General'}</span>
                    {template.version && (
                      <span className="template-version">v{template.version}</span>
                    )}
                  </div>
                ))}
              </div>
            </div>
          ) : (
            /* Form filling step */
            <div className="element-form">
              <TemplateFormGenerator
                templateId={selectedTemplateId}
                initialValues={formValues}
                onChange={handleFormChange}
              />
            </div>
          )}
        </div>

        <div className="modal-footer">
          {step === 'select-template' ? (
            <button className="cancel-button" onClick={onClose}>Cancel</button>
          ) : (
            <>
              <button
                className="back-button"
                onClick={() => setStep('select-template')}
              >
                Back to Templates
              </button>
              <button
                className="create-button"
                onClick={handleCreateElement}
                disabled={!formValues.name}
              >
                Create Element
              </button>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default CreateSubElementModal;

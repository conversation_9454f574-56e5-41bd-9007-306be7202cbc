// frontend/src/components/MagicSystemVisualizer.js
import React, { useState } from 'react';
import './MagicSystemVisualizer.css';

/**
 * Component for visualizing a magic system and its elements
 * @param {Object} props - Component props
 * @returns {JSX.Element} MagicSystemVisualizer UI
 */
const MagicSystemVisualizer = ({ magicSystem, spells = [], powers = [], artifacts = [] }) => {
  const [activeTab, setActiveTab] = useState('overview');
  const [selectedElement, setSelectedElement] = useState(null);

  // Energy type colors
  const energyColors = {
    mana: '#3498db',
    life_force: '#2ecc71',
    elemental: '#e74c3c',
    divine: '#f1c40f',
    cosmic: '#9b59b6',
    soul: '#1abc9c',
    blood: '#c0392b',
    nature: '#27ae60',
    other: '#7f8c8d'
  };

  // Difficulty colors
  const difficultyColors = {
    novice: '#2ecc71',
    apprentice: '#3498db',
    adept: '#f1c40f',
    expert: '#e67e22',
    master: '#9b59b6',
    legendary: '#e74c3c'
  };

  // Get energy color based on magic system's energy type
  const getEnergyColor = () => {
    if (!magicSystem || !magicSystem.custom_fields) return energyColors.other;
    return energyColors[magicSystem.custom_fields.energy_type] || energyColors.other;
  };

  // Get difficulty color
  const getDifficultyColor = (difficulty) => {
    return difficultyColors[difficulty] || difficultyColors.novice;
  };

  // Handle selecting an element
  const handleSelectElement = (element) => {
    setSelectedElement(element);
  };

  // Render overview tab
  const renderOverview = () => {
    if (!magicSystem || !magicSystem.custom_fields) {
      return <div className="no-data">No magic system data available</div>;
    }

    const {
      source,
      energy_type,
      acquisition,
      rules,
      limitations,
      cost,
      practitioners,
      rarity,
      cultural_impact,
      visual_effects
    } = magicSystem.custom_fields;

    return (
      <div className="magic-system-overview">
        <div className="magic-system-header">
          <div className="magic-system-title">
            <h2>{magicSystem.name}</h2>
            <div className="magic-system-energy-type" style={{ backgroundColor: getEnergyColor() }}>
              {energy_type ? energy_type.replace('_', ' ').toUpperCase() : 'Unknown Energy'}
            </div>
          </div>
          <div className="magic-system-acquisition">
            {acquisition && (
              <div className="acquisition-badge">
                {acquisition.replace('_', ' ')}
              </div>
            )}
          </div>
        </div>

        <div className="magic-system-stats">
          <div className="stat-item">
            <div className="stat-label">Rarity</div>
            <div className="stat-value">{rarity ? rarity.replace('_', ' ') : 'Unknown'}</div>
          </div>
          <div className="stat-item">
            <div className="stat-label">Practitioners</div>
            <div className="stat-value">{practitioners || 'Unknown'}</div>
          </div>
          <div className="stat-item">
            <div className="stat-label">Source</div>
            <div className="stat-value">{source || 'Unknown'}</div>
          </div>
        </div>

        <div className="magic-system-details">
          {rules && (
            <div className="detail-section">
              <h3>Core Rules</h3>
              <p>{rules}</p>
            </div>
          )}

          {limitations && (
            <div className="detail-section">
              <h3>Limitations</h3>
              <p>{limitations}</p>
            </div>
          )}

          {cost && (
            <div className="detail-section">
              <h3>Cost/Price</h3>
              <p>{cost}</p>
            </div>
          )}

          {cultural_impact && (
            <div className="detail-section">
              <h3>Cultural Impact</h3>
              <p>{cultural_impact}</p>
            </div>
          )}

          {visual_effects && (
            <div className="detail-section">
              <h3>Visual Effects</h3>
              <p>{visual_effects}</p>
            </div>
          )}
        </div>
      </div>
    );
  };

  // Render spells tab
  const renderSpells = () => {
    if (!spells || spells.length === 0) {
      return <div className="no-data">No spells available</div>;
    }

    return (
      <div className="magic-system-spells">
        <div className="spells-list">
          {spells.map(spell => (
            <div
              key={spell.element_id}
              className={`spell-card ${selectedElement && selectedElement.element_id === spell.element_id ? 'selected' : ''}`}
              onClick={() => handleSelectElement(spell)}
            >
              <div className="spell-header">
                <h3>{spell.name}</h3>
                <div
                  className="spell-difficulty"
                  style={{ backgroundColor: getDifficultyColor(spell.custom_fields.difficulty) }}
                >
                  {spell.custom_fields.difficulty}
                </div>
              </div>
              <div className="spell-details">
                <div className="spell-stat">
                  <span className="stat-label">Casting Time:</span>
                  <span className="stat-value">{spell.custom_fields.casting_time}</span>
                </div>
                <div className="spell-stat">
                  <span className="stat-label">Duration:</span>
                  <span className="stat-value">{spell.custom_fields.duration}</span>
                </div>
                <div className="spell-stat">
                  <span className="stat-label">Power Cost:</span>
                  <span className="stat-value">{spell.custom_fields.power_cost}</span>
                </div>
              </div>
              <div className="spell-description">
                <p>{spell.description}</p>
              </div>
              {/* Spell card content */}
            </div>
          ))}
        </div>

        {selectedElement && selectedElement.custom_fields && (
          <div className="spell-detail-view">
            <h3>{selectedElement.name}</h3>
            <div className="detail-section">
              <h4>Effect</h4>
              <p>{selectedElement.custom_fields.effect}</p>
            </div>
            {selectedElement.custom_fields.components && (
              <div className="detail-section">
                <h4>Components</h4>
                <p>{selectedElement.custom_fields.components}</p>
              </div>
            )}
            {selectedElement.custom_fields.range && (
              <div className="detail-section">
                <h4>Range</h4>
                <p>{selectedElement.custom_fields.range}</p>
              </div>
            )}
            {selectedElement.custom_fields.side_effects && (
              <div className="detail-section">
                <h4>Side Effects</h4>
                <p>{selectedElement.custom_fields.side_effects}</p>
              </div>
            )}
            {selectedElement.custom_fields.visual_effect && (
              <div className="detail-section">
                <h4>Visual Effect</h4>
                <p>{selectedElement.custom_fields.visual_effect}</p>
              </div>
            )}
          </div>
        )}
      </div>
    );
  };

  // Render powers tab
  const renderPowers = () => {
    if (!powers || powers.length === 0) {
      return <div className="no-data">No magical powers available</div>;
    }

    return (
      <div className="magic-system-powers">
        <div className="powers-list">
          {powers.map(power => (
            <div
              key={power.element_id}
              className={`power-card ${selectedElement && selectedElement.element_id === power.element_id ? 'selected' : ''}`}
              onClick={() => handleSelectElement(power)}
            >
              <div className="power-header">
                <h3>{power.name}</h3>
              </div>
              <div className="power-details">
                <div className="power-stat">
                  <span className="stat-label">Activation:</span>
                  <span className="stat-value">{power.custom_fields.activation}</span>
                </div>
                <div className="power-stat">
                  <span className="stat-label">Energy Consumption:</span>
                  <span className="stat-value">{power.custom_fields.energy_consumption}</span>
                </div>
              </div>
              <div className="power-description">
                <p>{power.description}</p>
              </div>
              {/* Power card content */}
            </div>
          ))}
        </div>

        {selectedElement && selectedElement.custom_fields && (
          <div className="power-detail-view">
            <h3>{selectedElement.name}</h3>
            <div className="detail-section">
              <h4>Ability</h4>
              <p>{selectedElement.custom_fields.ability}</p>
            </div>
            <div className="detail-section">
              <h4>Power Source</h4>
              <p>{selectedElement.custom_fields.power_source}</p>
            </div>
            {selectedElement.custom_fields.limitations && (
              <div className="detail-section">
                <h4>Limitations</h4>
                <p>{selectedElement.custom_fields.limitations}</p>
              </div>
            )}
            {selectedElement.custom_fields.mastery_levels && (
              <div className="detail-section">
                <h4>Mastery Levels</h4>
                <p>{selectedElement.custom_fields.mastery_levels}</p>
              </div>
            )}
            {selectedElement.custom_fields.side_effects && (
              <div className="detail-section">
                <h4>Side Effects</h4>
                <p>{selectedElement.custom_fields.side_effects}</p>
              </div>
            )}
            {selectedElement.custom_fields.visual_manifestation && (
              <div className="detail-section">
                <h4>Visual Manifestation</h4>
                <p>{selectedElement.custom_fields.visual_manifestation}</p>
              </div>
            )}
          </div>
        )}
      </div>
    );
  };

  // Render artifacts tab
  const renderArtifacts = () => {
    if (!artifacts || artifacts.length === 0) {
      return <div className="no-data">No magical artifacts available</div>;
    }

    return (
      <div className="magic-system-artifacts">
        <div className="artifacts-list">
          {artifacts.map(artifact => (
            <div
              key={artifact.element_id}
              className={`artifact-card ${selectedElement && selectedElement.element_id === artifact.element_id ? 'selected' : ''}`}
              onClick={() => handleSelectElement(artifact)}
            >
              <div className="artifact-header">
                <h3>{artifact.name}</h3>
              </div>
              <div className="artifact-details">
                <div className="artifact-stat">
                  <span className="stat-label">Activation:</span>
                  <span className="stat-value">{artifact.custom_fields.activation}</span>
                </div>
              </div>
              <div className="artifact-description">
                <p>{artifact.description}</p>
              </div>
            </div>
          ))}
        </div>

        {selectedElement && selectedElement.custom_fields && (
          <div className="artifact-detail-view">
            <h3>{selectedElement.name}</h3>
            {selectedElement.custom_fields.origin && (
              <div className="detail-section">
                <h4>Origin</h4>
                <p>{selectedElement.custom_fields.origin}</p>
              </div>
            )}
            <div className="detail-section">
              <h4>Powers</h4>
              <p>{selectedElement.custom_fields.powers}</p>
            </div>
            <div className="detail-section">
              <h4>Appearance</h4>
              <p>{selectedElement.custom_fields.appearance}</p>
            </div>
            {selectedElement.custom_fields.limitations && (
              <div className="detail-section">
                <h4>Limitations</h4>
                <p>{selectedElement.custom_fields.limitations}</p>
              </div>
            )}
            {selectedElement.custom_fields.requirements && (
              <div className="detail-section">
                <h4>Usage Requirements</h4>
                <p>{selectedElement.custom_fields.requirements}</p>
              </div>
            )}
            {selectedElement.custom_fields.side_effects && (
              <div className="detail-section">
                <h4>Side Effects</h4>
                <p>{selectedElement.custom_fields.side_effects}</p>
              </div>
            )}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="magic-system-visualizer">

      <div className="magic-system-tabs">
        <button
          className={`tab-button ${activeTab === 'overview' ? 'active' : ''}`}
          onClick={() => setActiveTab('overview')}
        >
          Overview
        </button>
        <button
          className={`tab-button ${activeTab === 'spells' ? 'active' : ''}`}
          onClick={() => setActiveTab('spells')}
        >
          Spells ({spells.length})
        </button>
        <button
          className={`tab-button ${activeTab === 'powers' ? 'active' : ''}`}
          onClick={() => setActiveTab('powers')}
        >
          Powers ({powers.length})
        </button>
        <button
          className={`tab-button ${activeTab === 'artifacts' ? 'active' : ''}`}
          onClick={() => setActiveTab('artifacts')}
        >
          Artifacts ({artifacts.length})
        </button>
      </div>

      <div className="magic-system-content">
        {activeTab === 'overview' && renderOverview()}
        {activeTab === 'spells' && renderSpells()}
        {activeTab === 'powers' && renderPowers()}
        {activeTab === 'artifacts' && renderArtifacts()}
      </div>

      {/* Content area */}
    </div>
  );
};

export default MagicSystemVisualizer;

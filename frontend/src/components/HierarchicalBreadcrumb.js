// frontend/src/components/HierarchicalBreadcrumb.js
import React, { useState, useEffect } from 'react';
import './HierarchicalBreadcrumb.css';
import { getElementTypeLabel } from '../utils/templateUtils';

/**
 * Component for displaying and navigating the element hierarchy
 * @param {Object} props - Component props
 * @returns {JSX.Element} HierarchicalBreadcrumb UI
 */
const HierarchicalBreadcrumb = ({
  element,
  ancestors = [],
  onNavigate,
  showHome = true
}) => {
  const [breadcrumbs, setBreadcrumbs] = useState([]);

  // Build breadcrumb path when element or ancestors change
  useEffect(() => {
    if (!element) {
      setBreadcrumbs(showHome ? [{ name: 'Home', id: 'home' }] : []);
      return;
    }

    // Start with home if enabled
    const crumbs = showHome ? [{ name: 'Home', id: 'home' }] : [];

    // Add World Building
    crumbs.push({ name: 'World', id: 'world' });

    // Add category if available
    if (element.category) {
      crumbs.push({
        name: element.category_name || element.category,
        id: element.category
      });
    }

    // Add ancestors in order
    if (ancestors && ancestors.length > 0) {
      ancestors.forEach(ancestor => {
        crumbs.push({
          name: ancestor.name,
          id: ancestor.element_id,
          type: ancestor.element_type
        });
      });
    }

    // Add current element
    crumbs.push({
      name: element.name,
      id: element.element_id,
      type: element.element_type,
      isCurrent: true
    });

    setBreadcrumbs(crumbs);
  }, [element, ancestors, showHome]);

  // Handle breadcrumb click
  const handleClick = (crumb) => {
    if (crumb.isCurrent) return;

    if (onNavigate) {
      onNavigate(crumb.id);
    }
  };

  return (
    <div className="hierarchical-breadcrumb">
      {breadcrumbs.map((crumb, index) => (
        <React.Fragment key={crumb.id}>
          <span
            className={`breadcrumb-item ${crumb.isCurrent ? 'current' : ''}`}
            onClick={() => handleClick(crumb)}
          >
            {crumb.name}
            {crumb.type && !crumb.isCurrent && (
              <span className="breadcrumb-type">
                {getElementTypeLabel(crumb.type)}
              </span>
            )}
          </span>
          {index < breadcrumbs.length - 1 && (
            <span className="breadcrumb-separator">›</span>
          )}
        </React.Fragment>
      ))}
    </div>
  );
};

export default HierarchicalBreadcrumb;

/* frontend/src/components/WorldConsistencyAnalyzer.css */
.world-consistency-analyzer {
  background: var(--background-secondary);
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.world-consistency-analyzer h3 {
  margin: 0 0 15px 0;
  color: var(--text-primary);
  font-size: 1.2rem;
}

.analyzer-description {
  margin-bottom: 15px;
}

.analyzer-description p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.error-message {
  color: var(--error);
  font-size: 0.9rem;
  padding: 5px 0;
  margin-bottom: 10px;
}

.analyze-button {
  padding: 10px 16px;
  background: var(--primary);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: background 0.2s;
  margin-bottom: 20px;
}

.analyze-button:hover:not(:disabled) {
  background: var(--button-primary-hover);
}

.analyze-button:disabled {
  background: var(--background-tertiary);
  color: var(--text-secondary);
  cursor: not-allowed;
}

.analysis-results {
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid var(--border-color);
}

.analysis-results h4 {
  margin: 0 0 15px 0;
  color: var(--text-primary);
  font-size: 1.1rem;
}

.overall-score {
  margin-bottom: 20px;
}

.score-label {
  display: block;
  margin-bottom: 5px;
  color: var(--text-primary);
  font-weight: 500;
}

.score-bar {
  height: 20px;
  background: var(--background-tertiary);
  border-radius: 10px;
  overflow: hidden;
  position: relative;
}

.score-fill {
  height: 100%;
  background: linear-gradient(to right, #ff9800, #4caf50);
  border-radius: 10px;
  position: relative;
}

.score-fill::after {
  content: attr(data-score);
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: white;
  font-weight: bold;
  font-size: 0.8rem;
  text-shadow: 0 0 2px rgba(0, 0, 0, 0.5);
}

.analysis-section {
  margin-bottom: 20px;
  padding: 15px;
  border-radius: 6px;
}

.analysis-section h5 {
  margin: 0 0 10px 0;
  color: var(--text-primary);
  font-size: 1rem;
}

.analysis-section ul {
  margin: 0;
  padding-left: 20px;
}

.analysis-section li {
  margin-bottom: 8px;
  line-height: 1.4;
}

.strengths {
  background: rgba(76, 175, 80, 0.1);
  border-left: 4px solid #4caf50;
}

.inconsistencies {
  background: rgba(244, 67, 54, 0.1);
  border-left: 4px solid #f44336;
}

.suggestions {
  background: rgba(33, 150, 243, 0.1);
  border-left: 4px solid #2196f3;
}

/* Dark theme adjustments */
.theme-dark .world-consistency-analyzer {
  background: var(--background-tertiary);
}

.theme-dark .strengths {
  background: rgba(76, 175, 80, 0.05);
}

.theme-dark .inconsistencies {
  background: rgba(244, 67, 54, 0.05);
}

.theme-dark .suggestions {
  background: rgba(33, 150, 243, 0.05);
}

/* Responsive styles */
@media (max-width: 768px) {
  .world-consistency-analyzer {
    padding: 15px;
  }
}

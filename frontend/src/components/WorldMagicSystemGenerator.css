/* frontend/src/components/WorldMagicSystemGenerator.css */
.world-magic-system-generator {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 15px;
}

.generator-header {
  margin-bottom: 10px;
}

.generator-header h3 {
  margin: 0 0 5px 0;
  color: var(--text-primary);
  font-size: 1.2rem;
}

.generator-header p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.generate-button {
  padding: 12px 20px;
  background-color: var(--primary);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
  align-self: flex-start;
}

.generate-button:hover {
  background-color: var(--primary-dark);
}

.generated-systems {
  margin-top: 20px;
}

.generated-systems h4 {
  margin: 0 0 15px 0;
  color: var(--text-primary);
  font-size: 1.1rem;
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 8px;
}

.systems-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
  max-height: 400px;
  overflow-y: auto;
  padding-right: 10px;
}

.system-card {
  background-color: var(--background-secondary);
  border-radius: 8px;
  padding: 15px;
  box-shadow: var(--shadow-sm);
  transition: transform 0.2s, box-shadow 0.2s;
}

.system-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.system-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.system-header h5 {
  margin: 0;
  color: var(--text-primary);
  font-size: 1rem;
}

.energy-type {
  padding: 3px 8px;
  border-radius: 12px;
  color: white;
  font-size: 0.8rem;
  text-transform: capitalize;
}

.energy-type.mana {
  background-color: #3498db;
}

.energy-type.life_force {
  background-color: #2ecc71;
}

.energy-type.elemental {
  background-color: #e74c3c;
}

.energy-type.divine {
  background-color: #f1c40f;
}

.energy-type.cosmic {
  background-color: #9b59b6;
}

.energy-type.soul {
  background-color: #1abc9c;
}

.energy-type.blood {
  background-color: #c0392b;
}

.energy-type.nature {
  background-color: #27ae60;
}

.energy-type.other {
  background-color: #7f8c8d;
}

.system-description {
  margin: 0 0 15px 0;
  color: var(--text-secondary);
  font-size: 0.9rem;
  line-height: 1.4;
}

.system-details {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 15px;
}

.detail-item {
  background-color: rgba(0, 0, 0, 0.05);
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 0.85rem;
  display: flex;
  align-items: center;
  gap: 5px;
}

.detail-label {
  color: var(--text-secondary);
  font-weight: 500;
}

.detail-value {
  color: var(--text-primary);
  text-transform: capitalize;
}

.add-to-world-button {
  padding: 8px 12px;
  background-color: var(--primary);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s;
  width: 100%;
}

.add-to-world-button:hover {
  background-color: var(--primary-dark);
}

/* Dark theme adjustments */
.theme-dark .system-card {
  background-color: var(--background-secondary-dark);
}

.theme-dark .detail-item {
  background-color: rgba(255, 255, 255, 0.05);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .systems-list {
    max-height: 300px;
  }
}

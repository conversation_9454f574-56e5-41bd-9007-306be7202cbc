/* frontend/src/components/TextEditor.css */
.text-editor-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  border: 1px solid var(--editor-border, var(--border-color));
  border-radius: 4px;
  background-color: var(--editor-background, var(--background-primary));
  color: var(--text-primary);
}

.text-editor {
  flex: 1;
  padding: 15px;
  font-size: 16px;
  line-height: 1.6;
  border: none;
  resize: none;
  outline: none;
  font-family: 'Georgia', serif;
  min-height: 300px;
  overflow-y: auto;
  direction: ltr; /* Ensure text direction is left-to-right */
  text-align: left;
  background-color: var(--editor-background, var(--background-primary));
  color: var(--text-primary);
}

.text-editor:focus {
  outline: none;
}

.text-editor-container.read-only .text-editor {
  background-color: var(--background-tertiary);
  cursor: default;
}

.formatting-toolbar {
  display: flex;
  padding: 8px;
  border-bottom: 1px solid var(--editor-toolbar-border, var(--border-color));
  background-color: var(--editor-toolbar-background, var(--background-tertiary));
  flex-wrap: wrap;
  color: var(--text-primary);
}

.toolbar-group {
  display: flex;
  margin-right: 15px;
  border-right: 1px solid var(--editor-toolbar-border, var(--border-color));
  padding-right: 15px;
}

.toolbar-group:last-child {
  border-right: none;
}

.toolbar-button {
  background: var(--editor-toolbar-button, var(--background-tertiary));
  border: 1px solid transparent;
  border-radius: 3px;
  padding: 4px 8px;
  margin-right: 2px;
  cursor: pointer;
  font-size: 14px;
  color: var(--text-primary);
}

.toolbar-button:hover {
  background-color: var(--editor-toolbar-button-hover, var(--background-secondary));
}

.toolbar-button.active {
  background-color: var(--editor-toolbar-button-active, var(--primary));
  border-color: var(--border-color);
  color: white;
}

.font-select {
  padding: 4px 8px;
  border: 1px solid var(--border-color);
  border-radius: 3px;
  background-color: var(--background-primary);
  font-size: 14px;
  min-width: 100px;
  color: var(--text-primary);
}

.font-select:focus {
  outline: none;
  border-color: var(--primary);
}

.text-editor-footer {
  display: flex;
  justify-content: flex-end;
  padding: 8px 15px;
  border-top: 1px solid var(--editor-toolbar-border, var(--border-color));
  background-color: var(--editor-toolbar-background, var(--background-tertiary));
  font-size: 12px;
  color: var(--text-secondary);
}

.word-count {
  font-size: 12px;
  color: var(--text-secondary);
}

/* Draft.js specific styles */
.public-DraftStyleDefault-ul {
  margin-left: 20px;
}

.public-DraftStyleDefault-ol {
  margin-left: 20px;
}

.public-DraftStyleDefault-header-one {
  font-size: 24px;
  font-weight: bold;
  margin: 20px 0 10px 0;
}

.public-DraftStyleDefault-header-two {
  font-size: 20px;
  font-weight: bold;
  margin: 15px 0 10px 0;
}

.public-DraftStyleDefault-header-three {
  font-size: 16px;
  font-weight: bold;
  margin: 10px 0 5px 0;
}

.loading-indicator {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.plot-events-container {
  width: 300px;
  overflow-y: auto;
  padding: 10px;
  background-color: #f5f5f5;
  border-radius: 5px;
}

.plot-events-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.plot-event-card {
  padding: 10px;
  background-color: white;
  border-radius: 5px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.2s;
}

.plot-event-card:hover {
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.selected-event {
  border-left: 4px solid #4a90e2;
  background-color: #f0f7ff;
}

.event-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
  font-size: 0.8em;
  color: #666;
}

.event-type {
  font-weight: bold;
  color: #1890ff;
}

.event-characters {
  color: #666;
  font-style: italic;
}

.event-text {
  font-size: 0.9em;
}

.event-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 5px;
}

.event-action-btn {
  padding: 3px 8px;
  background: #f0f0f0;
  border: 1px solid #ccc;
  border-radius: 3px;
  font-size: 12px;
  cursor: pointer;
}

.event-action-btn:hover {
  background: #e0e0e0;
}

.chapter-events {
  margin-top: 10px;
  border-top: 1px solid #ddd;
  padding-top: 10px;
}

.chapter-events h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
}

.editor-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  border: 1px solid #ddd;
  border-radius: 5px;
  overflow: hidden;
}

.format-toolbar {
  display: flex;
  padding: 10px;
  border-bottom: 1px solid #ddd;
  background-color: #f9f9f9;
  flex-wrap: wrap;
  gap: 10px;
}

.toolbar-section {
  display: flex;
  gap: 5px;
  align-items: center;
}

.toolbar-section:not(:last-child) {
  margin-right: 15px;
  padding-right: 15px;
  border-right: 1px solid #ddd;
}

.toolbar-section button {
  padding: 5px 10px;
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 3px;
  cursor: pointer;
  font-size: 0.9em;
}

.toolbar-section button:hover {
  background-color: #f0f0f0;
}

.stats {
  margin-left: auto;
  color: #666;
  font-size: 0.9em;
}

.stats span {
  margin-left: 10px;
}

.draft-editor-wrapper {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  background-color: white;
}

.DraftEditor-root {
  height: 100%;
}

.editor-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  border-top: 1px solid #ddd;
  background-color: #f9f9f9;
}

.chapter-navigation {
  display: flex;
  align-items: center;
  gap: 10px;
}

.nav-button {
  padding: 5px 10px;
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 3px;
  cursor: pointer;
}

.chapter-indicator, .page-indicator {
  font-size: 0.9em;
  color: #666;
}

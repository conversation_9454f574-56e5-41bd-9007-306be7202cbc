/* frontend/src/components/RelationshipAnalytics.css */
.relationship-analytics {
  margin-bottom: 20px;
  background-color: var(--background-secondary);
  border-radius: 6px;
  padding: 16px;
  overflow-y: auto;
}

.analytics-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.analytics-header h3 {
  margin: 0;
  font-size: 1.1rem;
  color: var(--text-primary);
}

.analyze-button {
  padding: 6px 12px;
  background-color: var(--accent-color);
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.analyze-button:hover {
  background-color: var(--accent-color-dark);
  transform: translateY(-1px);
}

.analyze-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.analytics-tabs {
  display: flex;
  border-bottom: 1px solid var(--border-color);
  margin-bottom: 16px;
}

.analytics-tab {
  padding: 8px 16px;
  background: none;
  border: none;
  border-bottom: 2px solid transparent;
  cursor: pointer;
  font-size: 0.9rem;
  color: var(--text-secondary);
  transition: all 0.2s ease;
}

.analytics-tab:hover {
  color: var(--text-primary);
  background-color: var(--background-tertiary);
}

.analytics-tab.active {
  color: var(--primary);
  border-bottom: 2px solid var(--primary);
  background-color: var(--background-tertiary);
}

.analytics-content {
  padding: 8px 0;
}

.analytics-content h4 {
  margin-top: 0;
  margin-bottom: 12px;
  font-size: 1rem;
  color: var(--text-primary);
}

.missing-list,
.inconsistent-list,
.suggestions-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.missing-item,
.inconsistent-item,
.suggestion-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background-color: var(--background-primary);
  border-radius: 4px;
  border: 1px solid var(--border-color);
}

.missing-description,
.inconsistent-description,
.suggestion-message {
  flex: 1;
}

.missing-description {
  display: flex;
  align-items: center;
  gap: 8px;
}

.inconsistent-description {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.relationship-pair {
  display: flex;
  align-items: center;
  gap: 8px;
}

.element-name {
  font-weight: 500;
  color: var(--text-primary);
}

.element-name.clickable {
  cursor: pointer;
  text-decoration: underline;
  text-decoration-style: dotted;
  text-underline-offset: 3px;
}

.element-name.clickable:hover {
  color: var(--primary);
}

.relationship-type {
  padding: 2px 6px;
  background-color: var(--background-tertiary);
  border-radius: 3px;
  font-size: 0.8rem;
  color: var(--text-secondary);
}

.create-relationship-button,
.fix-relationship-button,
.apply-suggestion-button {
  padding: 4px 8px;
  border: none;
  border-radius: 4px;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.create-relationship-button,
.apply-suggestion-button {
  background-color: var(--accent-color-light);
  color: var(--accent-color-dark);
}

.create-relationship-button:hover,
.apply-suggestion-button:hover {
  background-color: var(--accent-color);
  color: white;
}

.fix-relationship-button {
  background-color: #ffebee;
  color: #c62828;
}

.fix-relationship-button:hover {
  background-color: #ef5350;
  color: white;
}

.suggestion-item.missing {
  border-left: 3px solid var(--accent-color);
}

.suggestion-item.inconsistent {
  border-left: 3px solid #ef5350;
}

.no-items {
  padding: 16px;
  text-align: center;
  color: var(--text-tertiary);
  background-color: var(--background-primary);
  border-radius: 4px;
  border: 1px dashed var(--border-color);
  font-style: italic;
}

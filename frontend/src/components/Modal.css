/* frontend/src/components/Modal.css */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  overflow-y: auto;
  padding: 20px;
}

.modal-content {
  background-color: var(--background-primary, white);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  animation: modalFadeIn 0.2s ease-out;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid var(--border-color, #eee);
  position: sticky;
  top: 0;
  background-color: var(--background-primary, white);
  z-index: 10;
}

.modal-header h2 {
  margin: 0;
  font-size: 1.3rem;
  color: var(--text-primary, #333);
}

.modal-close-button {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--text-secondary, #666);
  transition: color 0.2s;
}

.modal-close-button:hover {
  color: var(--text-primary, #333);
}

.modal-body {
  padding: 20px;
  overflow-y: auto;
}

/* Dark theme adjustments */
.theme-dark .modal-content {
  background-color: var(--background-primary-dark, #333);
}

.theme-dark .modal-header {
  background-color: var(--background-primary-dark, #333);
  border-bottom-color: var(--border-color-dark, #444);
}

.theme-dark .modal-close-button {
  color: var(--text-secondary-dark, #aaa);
}

.theme-dark .modal-close-button:hover {
  color: var(--text-primary-dark, #eee);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .modal-content {
    width: 95%;
    max-height: 95vh;
  }
  
  .modal-header {
    padding: 12px 15px;
  }
  
  .modal-body {
    padding: 15px;
  }
}

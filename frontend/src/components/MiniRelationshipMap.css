/* frontend/src/components/MiniRelationshipMap.css */
.mini-relationship-map {
  margin-bottom: 20px;
  background-color: var(--background-secondary);
  border-radius: 6px;
  padding: 16px;
  overflow-y: auto;
}

.map-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.map-header-actions {
  display: flex;
  gap: 8px;
}

.map-title {
  margin: 0;
  font-size: 1rem;
  color: var(--text-primary);
}

.filter-toggle-button,
.add-relationship-button {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.filter-toggle-button {
  background-color: var(--background-tertiary);
  color: var(--text-primary);
}

.filter-toggle-button:hover {
  background-color: var(--background-tertiary-hover);
}

.add-relationship-button {
  background-color: var(--accent-color);
  color: white;
}

.add-relationship-button:hover {
  background-color: var(--accent-color-dark);
  transform: translateY(-1px);
}

.map-filters {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
  padding: 12px;
  background-color: var(--background-tertiary);
  border-radius: 4px;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-group label {
  font-size: 0.85rem;
  color: var(--text-secondary);
}

.filter-group select {
  padding: 4px 8px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--background-primary);
  color: var(--text-primary);
  font-size: 0.85rem;
}

.map-container {
  height: 200px;
  width: 100%;
  position: relative;
  overflow: hidden;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--background-primary);
}

.relationship-svg {
  width: 100%;
  height: 100%;
}

.relationship-link {
  stroke: var(--border-color);
  stroke-width: 2;
  fill: none;
  transition: stroke-width 0.2s, stroke 0.2s;
}

.relationship-link:hover {
  stroke-width: 3;
  stroke: var(--accent-color);
}

/* Category-specific link colors */
.relationship-link[data-category="physical"] {
  stroke: #81c784;
}

.relationship-link[data-category="social"] {
  stroke: #64b5f6;
}

.relationship-link[data-category="causal"] {
  stroke: #ffb74d;
}

.relationship-link[data-category="temporal"] {
  stroke: #ba68c8;
}

.relationship-link[data-category="conflict"] {
  stroke: #e57373;
}

.relationship-link[data-category="alliance"] {
  stroke: #7986cb;
}

.relationship-label-bg {
  fill: var(--background-primary);
  stroke: var(--border-color);
  stroke-width: 1;
}

.relationship-label-bg[data-category="physical"] {
  fill: #e8f5e9;
  stroke: #81c784;
}

.relationship-label-bg[data-category="social"] {
  fill: #e3f2fd;
  stroke: #64b5f6;
}

.relationship-label-bg[data-category="causal"] {
  fill: #fff3e0;
  stroke: #ffb74d;
}

.relationship-label-bg[data-category="temporal"] {
  fill: #f3e5f5;
  stroke: #ba68c8;
}

.relationship-label-bg[data-category="conflict"] {
  fill: #ffebee;
  stroke: #e57373;
}

.relationship-label-bg[data-category="alliance"] {
  fill: #e8eaf6;
  stroke: #7986cb;
}

.relationship-label {
  font-size: 8px;
  fill: var(--text-primary);
  pointer-events: none;
  font-weight: 500;
}

.relationship-label[data-category="physical"] {
  fill: #2e7d32;
}

.relationship-label[data-category="social"] {
  fill: #1565c0;
}

.relationship-label[data-category="causal"] {
  fill: #e65100;
}

.relationship-label[data-category="temporal"] {
  fill: #6a1b9a;
}

.relationship-label[data-category="conflict"] {
  fill: #c62828;
}

.relationship-label[data-category="alliance"] {
  fill: #283593;
}

.node {
  cursor: pointer;
}

.node.current {
  cursor: default;
}

.node-circle {
  fill: var(--background-tertiary);
  stroke: var(--border-color);
  stroke-width: 2;
  transition: fill 0.2s;
}

.node:hover .node-circle {
  fill: var(--primary-light);
}

.node.current .node-circle {
  fill: var(--primary-light);
  stroke: var(--primary);
  stroke-width: 3;
}

.node-label {
  font-size: 10px;
  fill: var(--text-primary);
  pointer-events: none;
  font-weight: 500;
}

.node-type-label {
  font-size: 8px;
  fill: var(--text-tertiary);
  pointer-events: none;
}

.map-legend {
  margin-top: 16px;
  padding: 12px;
  background-color: var(--background-tertiary);
  border-radius: 4px;
}

.legend-title {
  font-size: 0.85rem;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 8px;
}

.legend-items {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 6px;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
  background-color: var(--border-color);
}

.legend-color.physical {
  background-color: #81c784;
}

.legend-color.social {
  background-color: #64b5f6;
}

.legend-color.causal {
  background-color: #ffb74d;
}

.legend-color.temporal {
  background-color: #ba68c8;
}

.legend-color.conflict {
  background-color: #e57373;
}

.legend-color.alliance {
  background-color: #7986cb;
}

.legend-label {
  font-size: 0.8rem;
  color: var(--text-secondary);
}

.mini-relationship-map.empty {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 150px;
  color: var(--text-tertiary);
  font-style: italic;
}

.mini-relationship-map.empty p {
  margin-top: 20px;
}

/* frontend/src/components/BooksPage.css */

.books-page {
  height: 100%;
  position: relative;
}

.books-layout {
  display: flex;
  height: 100%;
  transition: transform 0.3s ease;
}

.books-sidebar {
  flex: 1;
  overflow-y: auto;
  background-color: #f9f9f9;
}

.books-details {
  flex: 2;
  overflow-y: auto;
  background-color: white;
}

.toggle-details-button {
  display: none;
  position: fixed;
  bottom: 20px;
  right: 20px;
  background-color: #4caf50;
  color: white;
  border: none;
  padding: 10px 15px;
  border-radius: 30px;
  cursor: pointer;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  z-index: 100;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .books-layout {
    flex-direction: column;
  }
  
  .books-sidebar,
  .books-details {
    flex: none;
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
  }
  
  .books-layout.show-details .books-sidebar {
    transform: translateX(-100%);
  }
  
  .books-layout:not(.show-details) .books-details {
    transform: translateX(100%);
  }
  
  .toggle-details-button {
    display: block;
  }
}

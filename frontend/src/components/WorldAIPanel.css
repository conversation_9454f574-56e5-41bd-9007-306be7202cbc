/* frontend/src/components/WorldAIPanel.css */
.world-ai-panel {
  background: var(--background-primary);
  border-radius: 8px;
  box-shadow: var(--shadow-md);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.ai-panel-header {
  padding: 20px;
  background: var(--primary);
  color: white;
}

.ai-panel-header h2 {
  margin: 0 0 5px 0;
  font-size: 1.5rem;
}

.ai-panel-header p {
  margin: 0;
  opacity: 0.8;
  font-size: 0.9rem;
}

.ai-tabs {
  display: flex;
  background: var(--background-secondary);
  border-bottom: 1px solid var(--border-color);
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

.ai-tab {
  padding: 12px 16px;
  background: transparent;
  border: none;
  border-bottom: 3px solid transparent;
  color: var(--text-secondary);
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
  white-space: nowrap;
}

.ai-tab:hover {
  color: var(--text-primary);
  background: rgba(0, 0, 0, 0.05);
}

.ai-tab.active {
  color: var(--primary);
  border-bottom-color: var(--primary);
}

.ai-tab-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

/* Dark theme adjustments */
.theme-dark .ai-panel-header {
  background: var(--primary-dark);
}

.theme-dark .ai-tab:hover {
  background: rgba(255, 255, 255, 0.05);
}

/* Responsive styles */
@media (max-width: 768px) {
  .ai-panel-header {
    padding: 15px;
  }
  
  .ai-tab {
    padding: 10px 12px;
    font-size: 0.9rem;
  }
  
  .ai-tab-content {
    padding: 15px;
  }
}

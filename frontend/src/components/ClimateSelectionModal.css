.climate-selection-modal {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  max-width: 600px;
  width: 90%;
  padding: 20px;
  position: relative;
  z-index: 1001;
}

.selection-options {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
  gap: 20px;
}

.selection-option {
  flex: 1;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.selection-option:hover {
  border-color: #03A9F4;
  box-shadow: 0 2px 8px rgba(3, 169, 244, 0.2);
  transform: translateY(-2px);
}

.option-icon {
  font-size: 48px;
  margin-bottom: 10px;
}

.selection-option h3 {
  margin: 10px 0;
  color: #333;
}

.selection-option p {
  color: #666;
  font-size: 14px;
  margin: 0;
}

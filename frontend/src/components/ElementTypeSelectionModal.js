// frontend/src/components/ElementTypeSelectionModal.js
import React from 'react';
import './ElementTypeSelectionModal.css';

/**
 * Modal for selecting element types for categories with multiple options
 * @param {Object} props - Component props
 * @returns {JSX.Element} ElementTypeSelectionModal UI
 */
const ElementTypeSelectionModal = ({ 
  title = 'Select Element Type',
  options = [],
  onSelect,
  onClose
}) => {
  return (
    <div className="modal-overlay">
      <div className="element-type-selection-modal">
        <div className="modal-header">
          <h2>{title}</h2>
          <button className="close-button" onClick={onClose}>×</button>
        </div>

        <div className="selection-options">
          {options.map(option => (
            <div
              key={option.value}
              className="selection-option"
              onClick={() => onSelect(option.value)}
            >
              <div className="option-icon">{option.icon}</div>
              <h3>{option.label}</h3>
              <p>{option.description}</p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ElementTypeSelectionModal;

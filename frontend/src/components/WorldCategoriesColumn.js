// frontend/src/components/WorldCategoriesColumn.js
import React from 'react';
import './WorldCategoriesColumn.css';
import WorldCategoryFilter from './WorldCategoryFilter';
import { getColorForCategory } from '../utils/colorMapping';

/**
 * Component for displaying world building categories
 * @param {Object} props - Component props
 * @returns {JSX.Element} WorldCategoriesColumn UI
 */
const WorldCategoriesColumn = ({
  visibleCategories,
  allCategories,
  customizations,
  activeCategory,
  onCategorySelect,
  onUpdateCustomization,
  onResetCustomizations
}) => {
  return (
    <div className="world-categories-column">
      <div className="categories-column-header">
        <h3>Categories</h3>
      </div>

      <WorldCategoryFilter
        categories={allCategories || []}
        customizations={customizations || []}
        onUpdateCustomization={onUpdateCustomization}
        onResetCustomizations={onResetCustomizations}
      />

      <div className="categories-list">
        {visibleCategories && visibleCategories.length > 0 ? (
          visibleCategories.map(category => (
            <div
              key={category.category_id}
              className={`category-item ${activeCategory && activeCategory.category_id === category.category_id ? 'active' : ''} ${category.category_id}-category`}
              onClick={() => onCategorySelect(category.category_id)}
              style={{
                borderLeftColor: getColorForCategory(category.category_id)
              }}
            >
              <div
                className={`category-icon ${category.category_id}`}
                style={{
                  backgroundColor: getColorForCategory(category.category_id),
                  color: 'white'
                }}
              >
                {getCategoryIcon(category.category_id)}
              </div>

              <div className="category-info">
                <h4>{category.name}</h4>
                <p>{category.description}</p>
              </div>
            </div>
          ))
        ) : (
          <div className="no-categories">
            <p>No categories found. Try enabling more categories using the filter above.</p>
          </div>
        )}
      </div>
    </div>
  );
};

/**
 * Helper function to get an icon for a category based on its category ID
 * @param {string} categoryId - The category ID
 * @returns {string} An emoji icon
 */
const getCategoryIcon = (categoryId) => {
  switch (categoryId) {
    case 'cat_cosmology_physical':
      return '🌍';
    case 'cat_cultural_social':
      return '👥';
    case 'cat_economic_material':
      return '💰';
    case 'cat_knowledge_technology':
      return '⚙️';
    case 'cat_temporal_historical':
      return '⏳';
    case 'cat_magical_supernatural':
      return '✨';
    case 'cat_interactive_game':
      return '🎮';
    default:
      return '📁';
  }
};

export default WorldCategoriesColumn;

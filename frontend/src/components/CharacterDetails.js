// frontend/src/components/CharacterDetails.js
import React, { useState } from 'react';
import './CharacterDetails.css';

/**
 * Helper function to get a label for relationship strength
 * @param {number} strength - Relationship strength (0-5)
 * @returns {string} Label describing the relationship strength
 */
const getStrengthLabel = (strength) => {
  switch (parseInt(strength, 10)) {
    case 0: return "Enemy";
    case 1: return "Acquaintance";
    case 2: return "Casual friend";
    case 3: return "Close friend";
    case 4: return "Deep bond";
    case 5: return "Intimate connection";
    default: return "Close friend";
  }
};

/**
 * CharacterDetails component for displaying detailed information about a character
 * @param {Object} props - Component props
 * @returns {JSX.Element} CharacterDetails UI
 */
const CharacterDetails = ({
  character,
  characters,
  relationships,
  isLoading,
  error,
  onUpdateCharacter,
  onAddRelationship,
  onUpdateRelationship,
  onRemoveRelationship,
  onClearError
}) => {
  const [activeTab, setActiveTab] = useState('info');
  const [editMode, setEditMode] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    role: '',
    description: '',
    traits: '',
    goals: '',
    backstory: ''
  });
  const [newRelationship, setNewRelationship] = useState({
    relatedCharacterId: '',
    relationshipType: '',
    strength: 3 // Default to 3 (close friend)
  });

  // Initialize form data when character changes
  React.useEffect(() => {
    if (character) {
      setFormData({
        name: character.name || '',
        role: character.role || '',
        description: character.description || '',
        traits: character.traits || '',
        goals: character.goals || '',
        backstory: character.backstory || ''
      });
    }
  }, [character]);

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle edit form submission
  const handleEditSubmit = (e) => {
    e.preventDefault();
    onUpdateCharacter({ characterId: character.id, characterData: formData });
    setEditMode(false);
  };

  // Handle new relationship form submission
  const handleAddRelationship = (e) => {
    e.preventDefault();
    onAddRelationship({
      characterId: character.id,
      relatedCharacterId: newRelationship.relatedCharacterId,
      relationshipType: newRelationship.relationshipType,
      strength: newRelationship.strength
    });
    setNewRelationship({
      relatedCharacterId: '',
      relationshipType: '',
      strength: 3 // Reset to default
    });
  };

  // Handle relationship update
  const handleUpdateRelationship = (relationshipId, newType, newStrength) => {
    onUpdateRelationship({
      characterId: character.id,
      relationshipId,
      relationshipType: newType,
      strength: newStrength
    });
  };

  // Handle relationship removal
  const handleRemoveRelationship = (relationshipId) => {
    if (window.confirm('Are you sure you want to remove this relationship?')) {
      onRemoveRelationship({
        characterId: character.id,
        relationshipId
      });
    }
  };

  if (isLoading) {
    return (
      <div className="character-details-container">
        <div className="loading-indicator">Loading character details...</div>
      </div>
    );
  }

  if (!character) {
    return (
      <div className="character-details-container">
        <div className="no-character-selected">
          <h2>No Character Selected</h2>
          <p>Please select a character to view details.</p>
        </div>
      </div>
    );
  }

  // Render character info tab
  const renderInfoTab = () => {
    if (editMode) {
      return (
        <form onSubmit={handleEditSubmit} className="character-edit-form">
          <div className="form-group">
            <label htmlFor="name">Name</label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              required
            />
          </div>

          <div className="form-group">
            <label htmlFor="role">Role</label>
            <select
              id="role"
              name="role"
              value={formData.role}
              onChange={handleInputChange}
              required
            >
              <option value="">Select Role</option>
              <option value="protagonist">Protagonist</option>
              <option value="antagonist">Antagonist</option>
              <option value="supporting">Supporting Character</option>
              <option value="mentor">Mentor</option>
              <option value="sidekick">Sidekick</option>
              <option value="love_interest">Love Interest</option>
              <option value="foil">Foil</option>
              <option value="other">Other</option>
            </select>
          </div>

          <div className="form-group">
            <label htmlFor="description">Description</label>
            <textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              rows={3}
              placeholder="Physical appearance, mannerisms, etc."
            />
          </div>

          <div className="form-group">
            <label htmlFor="traits">Traits</label>
            <textarea
              id="traits"
              name="traits"
              value={formData.traits}
              onChange={handleInputChange}
              rows={2}
              placeholder="Personality traits, strengths, weaknesses"
            />
          </div>

          <div className="form-group">
            <label htmlFor="goals">Goals</label>
            <textarea
              id="goals"
              name="goals"
              value={formData.goals}
              onChange={handleInputChange}
              rows={2}
              placeholder="Character's motivations and objectives"
            />
          </div>

          <div className="form-group">
            <label htmlFor="backstory">Backstory</label>
            <textarea
              id="backstory"
              name="backstory"
              value={formData.backstory}
              onChange={handleInputChange}
              rows={4}
              placeholder="Character's history and background"
            />
          </div>

          <div className="form-actions">
            <button type="submit" className="save-button">
              Save Changes
            </button>
            <button
              type="button"
              className="cancel-button"
              onClick={() => setEditMode(false)}
            >
              Cancel
            </button>
          </div>
        </form>
      );
    }

    return (
      <div className="character-info">
        <div className="character-header">
          <div>
            <h2>{character.name}</h2>
            {character.role && <span className="character-role">{character.role}</span>}
          </div>
          <button
            className="edit-button"
            onClick={() => setEditMode(true)}
          >
            Edit
          </button>
        </div>

        {character.description && (
          <div className="info-section">
            <h3>Description</h3>
            <p>{character.description}</p>
          </div>
        )}

        {character.traits && (
          <div className="info-section">
            <h3>Traits</h3>
            <p>{character.traits}</p>
          </div>
        )}

        {character.goals && (
          <div className="info-section">
            <h3>Goals</h3>
            <p>{character.goals}</p>
          </div>
        )}

        {character.backstory && (
          <div className="info-section">
            <h3>Backstory</h3>
            <p>{character.backstory}</p>
          </div>
        )}
      </div>
    );
  };

  // Render relationships tab
  const renderRelationshipsTab = () => {
    return (
      <div className="character-relationships">
        <h3>Character Relationships</h3>

        <form onSubmit={handleAddRelationship} className="add-relationship-form">
          <div className="form-row">
            <div className="form-group">
              <label htmlFor="relatedCharacterId">Character</label>
              <select
                id="relatedCharacterId"
                name="relatedCharacterId"
                value={newRelationship.relatedCharacterId}
                onChange={(e) => setNewRelationship(prev => ({ ...prev, relatedCharacterId: e.target.value }))}
                required
              >
                <option value="">Select Character</option>
                {characters
                  .filter(char => char.id !== character.id)
                  .map(char => (
                    <option key={char.id} value={char.id}>{char.name}</option>
                  ))
                }
              </select>
            </div>

            <div className="form-group">
              <label htmlFor="relationshipType">Relationship</label>
              <select
                id="relationshipType"
                name="relationshipType"
                value={newRelationship.relationshipType}
                onChange={(e) => setNewRelationship(prev => ({ ...prev, relationshipType: e.target.value }))}
                required
              >
                <option value="">Select Relationship</option>
                <option value="friend">Friend</option>
                <option value="enemy">Enemy</option>
                <option value="family">Family</option>
                <option value="lover">Lover</option>
                <option value="mentor">Mentor</option>
                <option value="student">Student</option>
                <option value="colleague">Colleague</option>
                <option value="rival">Rival</option>
                <option value="other">Other</option>
              </select>
            </div>

            <div className="form-group">
              <label htmlFor="relationshipStrength">
                Strength: {getStrengthLabel(newRelationship.strength)}
              </label>
              <input
                type="range"
                id="relationshipStrength"
                name="relationshipStrength"
                min="0"
                max="5"
                value={newRelationship.strength}
                onChange={(e) => setNewRelationship(prev => ({
                  ...prev,
                  strength: parseInt(e.target.value, 10)
                }))}
              />
              <div className="strength-labels">
                <span>Enemy</span>
                <span>Intimate</span>
              </div>
            </div>

            <button type="submit" className="add-button">
              Add
            </button>
          </div>
        </form>

        {relationships.length === 0 ? (
          <div className="empty-relationships">
            <p>No relationships defined for this character.</p>
          </div>
        ) : (
          <div className="relationships-list">
            {relationships.map(rel => {
              const relatedCharacter = characters.find(char => char.id === rel.relatedCharacterId);
              if (!relatedCharacter) return null;

              return (
                <div key={rel.id} className="relationship-item">
                  <div className="relationship-info">
                    <span className="related-character">{relatedCharacter.name}</span>
                    <span className="relationship-type">
                      <select
                        value={rel.relationshipType}
                        onChange={(e) => handleUpdateRelationship(rel.id, e.target.value, rel.strength)}
                      >
                        <option value="friend">Friend</option>
                        <option value="enemy">Enemy</option>
                        <option value="family">Family</option>
                        <option value="lover">Lover</option>
                        <option value="mentor">Mentor</option>
                        <option value="student">Student</option>
                        <option value="colleague">Colleague</option>
                        <option value="rival">Rival</option>
                        <option value="other">Other</option>
                      </select>
                    </span>
                    <div className="relationship-strength">
                      <label>
                        Strength: {getStrengthLabel(rel.strength || 3)}
                      </label>
                      <input
                        type="range"
                        min="0"
                        max="5"
                        value={rel.strength || 3}
                        onChange={(e) => handleUpdateRelationship(
                          rel.id,
                          rel.relationshipType,
                          parseInt(e.target.value, 10)
                        )}
                      />
                    </div>
                  </div>
                  <button
                    className="remove-button"
                    onClick={() => handleRemoveRelationship(rel.id)}
                  >
                    Remove
                  </button>
                </div>
              );
            })}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="character-details-container">
      {error && (
        <div className="error-message">
          <p>{error}</p>
          <button onClick={onClearError}>Dismiss</button>
        </div>
      )}

      <div className="character-tabs">
        <button
          className={`tab-button ${activeTab === 'info' ? 'active' : ''}`}
          onClick={() => setActiveTab('info')}
        >
          Information
        </button>
        <button
          className={`tab-button ${activeTab === 'relationships' ? 'active' : ''}`}
          onClick={() => setActiveTab('relationships')}
        >
          Relationships
        </button>
      </div>

      <div className="tab-content">
        {activeTab === 'info' && renderInfoTab()}
        {activeTab === 'relationships' && renderRelationshipsTab()}
      </div>
    </div>
  );
};

export default CharacterDetails;

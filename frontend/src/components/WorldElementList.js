// frontend/src/components/WorldElementList.js
import React, { useState } from 'react';
import './WorldElementList.css';

/**
 * WorldElementList component for displaying and managing world elements
 * @param {Object} props - Component props
 * @returns {JSX.Element} WorldElementList UI
 */
const WorldElementList = ({
  elements,
  categories,
  selectedElement,
  isLoading,
  isSaving,
  error,
  onSelectElement,
  onCreateElement,
  onUpdateElement,
  onDeleteElement,
  onGenerateElement,
  onClearError
}) => {
  const [activeCategory, setActiveCategory] = useState(categories[0] || '');
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [showGenerateForm, setShowGenerateForm] = useState(false);
  const [editingElementId, setEditingElementId] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    category: activeCategory,
    description: '',
    attributes: {}
  });
  const [generatePrompt, setGeneratePrompt] = useState('');
  const [generateCount, setGenerateCount] = useState(1);

  // Ensure elements is an array and filter by active category
  const elementsArray = Array.isArray(elements) ? elements : [];
  const filteredElements = elementsArray.filter(elem => elem.category === activeCategory);

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle attribute input changes
  const handleAttributeChange = (key, value) => {
    setFormData(prev => ({
      ...prev,
      attributes: {
        ...prev.attributes,
        [key]: value
      }
    }));
  };

  // Handle create element form submission
  const handleCreateSubmit = (e) => {
    e.preventDefault();
    onCreateElement({
      ...formData,
      category: activeCategory
    });
    resetForm();
  };

  // Handle edit element form submission
  const handleEditSubmit = (e) => {
    e.preventDefault();
    onUpdateElement({
      elementId: editingElementId,
      elementData: formData
    });
    resetForm();
  };

  // Handle generate element form submission
  const handleGenerateSubmit = (e) => {
    e.preventDefault();
    onGenerateElement({
      prompt: generatePrompt,
      category: activeCategory,
      count: generateCount
    });
    setGeneratePrompt('');
    setGenerateCount(1);
    setShowGenerateForm(false);
  };

  // Reset form state
  const resetForm = () => {
    setFormData({
      name: '',
      category: activeCategory,
      description: '',
      attributes: {}
    });
    setEditingElementId(null);
    setShowCreateForm(false);
  };

  // Start editing an element
  const handleStartEdit = (element) => {
    setEditingElementId(element.id);
    setFormData({
      name: element.name || '',
      category: element.category || activeCategory,
      description: element.description || '',
      attributes: element.attributes || {}
    });
    setShowCreateForm(false);
    setShowGenerateForm(false);
  };

  // Handle element deletion
  const handleDeleteElement = (elementId) => {
    if (window.confirm('Are you sure you want to delete this element? This action cannot be undone.')) {
      onDeleteElement(elementId);
    }
  };

  // Get attribute fields based on category
  const getAttributeFields = () => {
    switch (activeCategory) {
      case 'places':
        return [
          { key: 'location', label: 'Location', type: 'text' },
          { key: 'climate', label: 'Climate', type: 'text' },
          { key: 'population', label: 'Population', type: 'text' },
          { key: 'importance', label: 'Importance', type: 'text' }
        ];
      case 'character_types':
        return [
          { key: 'traits', label: 'Common Traits', type: 'text' },
          { key: 'role', label: 'Role in Society', type: 'text' },
          { key: 'abilities', label: 'Special Abilities', type: 'text' }
        ];
      case 'magic_systems':
        return [
          { key: 'rules', label: 'Rules', type: 'text' },
          { key: 'limitations', label: 'Limitations', type: 'text' },
          { key: 'source', label: 'Source of Power', type: 'text' }
        ];
      case 'technology':
        return [
          { key: 'function', label: 'Function', type: 'text' },
          { key: 'availability', label: 'Availability', type: 'text' },
          { key: 'impact', label: 'Impact on Society', type: 'text' }
        ];
      case 'world_name':
        return [
          { key: 'age', label: 'Age/Era', type: 'text' },
          { key: 'size', label: 'Size', type: 'text' },
          { key: 'history', label: 'Brief History', type: 'textarea' }
        ];
      case 'groups_societies':
        return [
          { key: 'structure', label: 'Structure', type: 'text' },
          { key: 'values', label: 'Values', type: 'text' },
          { key: 'conflicts', label: 'Conflicts', type: 'text' }
        ];
      case 'species':
        return [
          { key: 'traits', label: 'Physical Traits', type: 'text' },
          { key: 'abilities', label: 'Special Abilities', type: 'text' },
          { key: 'lifespan', label: 'Lifespan', type: 'text' },
          { key: 'habitat', label: 'Habitat', type: 'text' }
        ];
      default:
        return [
          { key: 'notes', label: 'Notes', type: 'textarea' }
        ];
    }
  };

  // Render create/edit element form
  const renderElementForm = () => {
    const isEditing = editingElementId !== null;
    const title = isEditing ? 'Edit Element' : `Create New ${getCategoryLabel(activeCategory)}`;
    const submitText = isEditing ? 'Save Changes' : 'Create Element';
    const submitAction = isEditing ? handleEditSubmit : handleCreateSubmit;
    const attributeFields = getAttributeFields();

    return (
      <div className="element-form-container">
        <h3>{title}</h3>
        <form onSubmit={submitAction} className="element-form">
          <div className="form-group">
            <label htmlFor="name">Name</label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              required
            />
          </div>

          <div className="form-group">
            <label htmlFor="description">Description</label>
            <textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              rows={3}
              placeholder="Describe this element"
            />
          </div>

          {attributeFields.map(field => (
            <div className="form-group" key={field.key}>
              <label htmlFor={`attr-${field.key}`}>{field.label}</label>
              {field.type === 'textarea' ? (
                <textarea
                  id={`attr-${field.key}`}
                  value={formData.attributes[field.key] || ''}
                  onChange={(e) => handleAttributeChange(field.key, e.target.value)}
                  rows={3}
                />
              ) : (
                <input
                  type={field.type}
                  id={`attr-${field.key}`}
                  value={formData.attributes[field.key] || ''}
                  onChange={(e) => handleAttributeChange(field.key, e.target.value)}
                />
              )}
            </div>
          ))}

          <div className="form-actions">
            <button type="submit" className="save-button" disabled={isSaving}>
              {isSaving ? 'Saving...' : submitText}
            </button>
            <button
              type="button"
              className="cancel-button"
              onClick={resetForm}
              disabled={isSaving}
            >
              Cancel
            </button>
          </div>
        </form>
      </div>
    );
  };

  // Render generate element form
  const renderGenerateForm = () => {
    return (
      <div className="element-form-container">
        <h3>Generate {getCategoryLabel(activeCategory)} with AI</h3>
        <form onSubmit={handleGenerateSubmit} className="element-form">
          <div className="form-group">
            <label htmlFor="generatePrompt">Element Prompt</label>
            <textarea
              id="generatePrompt"
              name="generatePrompt"
              value={generatePrompt}
              onChange={(e) => setGeneratePrompt(e.target.value)}
              rows={4}
              placeholder={`Describe the ${getCategoryLabel(activeCategory).toLowerCase()} you want to generate`}
              required
            />
          </div>

          <div className="form-group">
            <label htmlFor="generateCount">Number of Elements to Generate (1-5)</label>
            <div className="count-selector">
              <input
                type="range"
                id="generateCount"
                name="generateCount"
                min="1"
                max="5"
                value={generateCount}
                onChange={(e) => setGenerateCount(parseInt(e.target.value))}
              />
              <span className="count-display">{generateCount}</span>
            </div>
          </div>

          <div className="form-actions">
            <button type="submit" className="save-button" disabled={isLoading}>
              {isLoading ? 'Generating...' : `Generate ${generateCount > 1 ? generateCount + ' ' : ''}${getCategoryLabel(activeCategory)}${generateCount > 1 ? 's' : ''}`}
            </button>
            <button
              type="button"
              className="cancel-button"
              onClick={() => setShowGenerateForm(false)}
              disabled={isLoading}
            >
              Cancel
            </button>
          </div>
        </form>
      </div>
    );
  };

  // Get category label
  const getCategoryLabel = (category) => {
    const labels = {
      'places': 'Place',
      'character_types': 'Character Type',
      'magic_systems': 'Magic System',
      'technology': 'Technology',
      'world_name': 'World Information',
      'groups_societies': 'Group/Society',
      'species': 'Species'
    };
    return labels[category] || 'Element';
  };

  // Render element card
  const renderElementCard = (element) => {
    const isSelected = selectedElement && selectedElement.id === element.id;

    return (
      <div
        key={element.id}
        className={`element-card ${isSelected ? 'selected' : ''}`}
        onClick={() => onSelectElement(element)}
      >
        <div className="element-card-content">
          <h3 className="element-name">{element.name}</h3>
          {element.description && <p className="element-description">{element.description}</p>}

          {element.attributes && Object.keys(element.attributes).length > 0 && (
            <div className="element-attributes">
              {Object.entries(element.attributes).map(([key, value]) => (
                <div key={key} className="attribute-item">
                  <span className="attribute-label">{key}:</span>
                  <span className="attribute-value">{value}</span>
                </div>
              ))}
            </div>
          )}
        </div>

        <div className="element-card-actions">
          <button
            className="edit-button"
            onClick={(e) => {
              e.stopPropagation();
              handleStartEdit(element);
            }}
          >
            Edit
          </button>
          <button
            className="delete-button"
            onClick={(e) => {
              e.stopPropagation();
              handleDeleteElement(element.id);
            }}
          >
            Delete
          </button>
        </div>
      </div>
    );
  };

  return (
    <div className="world-element-list-container">
      <div className="category-tabs">
        {categories.map(category => (
          <button
            key={category}
            className={`category-tab ${category === activeCategory ? 'active' : ''}`}
            onClick={() => {
              setActiveCategory(category);
              resetForm();
            }}
          >
            {getCategoryLabel(category)}
          </button>
        ))}
      </div>

      <div className="element-list-header">
        <h2>{getCategoryLabel(activeCategory)}s</h2>
        <div className="element-actions">
          <button
            className="create-element-button"
            onClick={() => {
              setShowCreateForm(true);
              setShowGenerateForm(false);
              setEditingElementId(null);
              setFormData(prev => ({
                ...prev,
                category: activeCategory
              }));
            }}
            disabled={showCreateForm || editingElementId !== null || showGenerateForm}
          >
            Create {getCategoryLabel(activeCategory)}
          </button>
          <button
            className="generate-element-button"
            onClick={() => {
              setShowGenerateForm(true);
              setShowCreateForm(false);
              setEditingElementId(null);
            }}
            disabled={showCreateForm || editingElementId !== null || showGenerateForm}
          >
            Generate with AI
          </button>
        </div>
      </div>

      {error && (
        <div className="error-message">
          <p>{error}</p>
          <button onClick={onClearError}>Dismiss</button>
        </div>
      )}

      {showCreateForm && renderElementForm()}
      {showGenerateForm && renderGenerateForm()}
      {editingElementId !== null && renderElementForm()}

      {isLoading && !showGenerateForm ? (
        <div className="loading-indicator">Loading elements...</div>
      ) : filteredElements.length === 0 ? (
        <div className="empty-state">
          <p>You don't have any {getCategoryLabel(activeCategory).toLowerCase()}s yet.</p>
          <p>Create your first {getCategoryLabel(activeCategory).toLowerCase()} to get started!</p>
        </div>
      ) : (
        <div className="element-grid">
          {filteredElements.map(element => renderElementCard(element))}
        </div>
      )}
    </div>
  );
};

export default WorldElementList;

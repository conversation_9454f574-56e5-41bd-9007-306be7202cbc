// frontend/src/components/WorldRelationshipView.js
import React, { useState, useCallback, useEffect, useMemo, useRef } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import ReactFlow, {
  MiniMap,
  Controls,
  Background,
  useNodesState,
  useEdgesState,
  addEdge,
  Panel,
  getBezierPath
} from 'reactflow';
import WorldElementNode from './WorldElementNode';
import 'reactflow/dist/style.css';
import './WorldRelationshipView.css';
import { getColorForType, getColorForCategory } from '../utils/colorMapping';
import { updateNodePositions, selectNodePositions, saveUserRelationship, removeUserRelationship, selectUserRelationships, selectCurrentBookId } from '../redux/slices/worldBuildingSlice';
import { createWorldRelationship, deleteWorldRelationship } from '../services/worldBuildingApiService';

/**
 * Component for displaying and managing relationships between world elements
 * @param {Object} props - Component props
 * @returns {JSX.Element} WorldRelationshipView UI
 */
const WorldRelationshipView = ({ elements = [], relationships = [] }) => {
  const dispatch = useDispatch();
  const savedNodePositions = useSelector(selectNodePositions);
  const userRelationships = useSelector(selectUserRelationships);
  const bookId = useSelector(selectCurrentBookId);

  // Reference to track if positions have been saved
  const positionsSaved = useRef(false);

  // State for nodes and edges
  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);

  // State for selected elements
  const [selectedElements, setSelectedElements] = useState([]);

  // State for relationship creation
  const [isCreatingRelationship, setIsCreatingRelationship] = useState(false);
  const [sourceElement, setSourceElement] = useState(null);

  // State for relationship type
  const [relationshipType, setRelationshipType] = useState('connected_to');

  // State for filters
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');

  // Relationship types based on element types
  const relationshipTypes = useMemo(() => [
    // Organization relationships
    { id: 'enforces', label: 'Enforces', sourceTypes: ['organization'], targetTypes: ['law'] },
    { id: 'creates', label: 'Creates', sourceTypes: ['organization'], targetTypes: ['law'] },
    { id: 'amends', label: 'Amends', sourceTypes: ['organization'], targetTypes: ['law'] },
    { id: 'contains', label: 'Contains', sourceTypes: ['law'], targetTypes: ['law_clause'] },
    { id: 'references', label: 'References', sourceTypes: ['law'], targetTypes: ['law_clause'] },
    { id: 'located_in', label: 'Located In', sourceTypes: ['location'], targetTypes: ['geography'] },
    { id: 'borders', label: 'Borders', sourceTypes: ['geography'], targetTypes: ['geography'] },

    // Generic relationships for all types
    { id: 'related_to', label: 'Related To', sourceTypes: [], targetTypes: [] },
    { id: 'influences', label: 'Influences', sourceTypes: [], targetTypes: [] },
    { id: 'opposes', label: 'Opposes', sourceTypes: [], targetTypes: [] },
    { id: 'connected_to', label: 'Connected To', sourceTypes: [], targetTypes: [] }
  ], []);

  // We'll use empty arrays directly when needed

  // Helper function to determine element type from category if not explicitly set
  const determineElementType = (element) => {
    // If element_type is already set and not 'generic', use it
    if (element.element_type && element.element_type !== 'generic') {
      return element.element_type;
    }

    // Try to determine from category
    const category = element.category_id || element.category;
    if (!category) {
      return 'generic';
    }

    // Handle categories with 'cat_' prefix
    let cleanCategory = category;
    if (category.startsWith('cat_')) {
      cleanCategory = category.substring(4); // Remove 'cat_' prefix
    }

    // Map categories directly to element types
    if (cleanCategory === 'physical_geography') return 'geography';
    if (cleanCategory === 'physical_locations') return 'location';
    if (cleanCategory === 'physical_climate') return 'climate';
    if (cleanCategory === 'physical_flora_fauna') return 'flora_fauna';
    if (cleanCategory === 'physical_monsters') return 'monster';
    if (cleanCategory === 'physical_aliens') return 'alien';
    if (cleanCategory === 'social_organizations') return 'organization';
    if (cleanCategory === 'social_cultures') return 'culture';
    if (cleanCategory === 'social_laws') return 'law';
    if (cleanCategory === 'social_economics') return 'economics';
    if (cleanCategory === 'metaphysical_magic') return 'magic_system';
    if (cleanCategory === 'metaphysical_religion') return 'religion';
    if (cleanCategory === 'metaphysical_cosmology') return 'cosmology';
    if (cleanCategory === 'metaphysical_superpowers') return 'superpower';
    if (cleanCategory === 'technological_tools') return 'tool';
    if (cleanCategory === 'technological_infrastructure') return 'infrastructure';
    if (cleanCategory === 'technological_sciences') return 'science';
    if (cleanCategory === 'technological_scifi') return 'scifi_tech';

    // Extract the type from the category (e.g., 'physical_locations' -> 'location')
    const parts = cleanCategory.split('_');
    if (parts.length > 1) {
      // Use singular form for the element type
      const categoryName = parts[1];
      // Handle special cases and plurals
      if (categoryName === 'locations') return 'location';
      if (categoryName === 'geography') return 'geography';
      if (categoryName === 'climate') return 'climate';
      if (categoryName === 'flora_fauna') return 'flora_fauna';
      if (categoryName === 'monsters') return 'monster';
      if (categoryName === 'aliens') return 'alien';
      if (categoryName === 'organizations') return 'organization';
      if (categoryName === 'cultures') return 'culture';
      if (categoryName === 'laws') return 'law';
      if (categoryName === 'economics') return 'economics';
      if (categoryName === 'magic') return 'magic_system';
      if (categoryName === 'religion') return 'religion';
      if (categoryName === 'cosmology') return 'cosmology';
      if (categoryName === 'superpowers') return 'superpower';
      if (categoryName === 'tools') return 'tool';
      if (categoryName === 'infrastructure') return 'infrastructure';
      if (categoryName === 'sciences') return 'science';
      if (categoryName === 'scifi') return 'scifi_tech';
      // Remove trailing 's' for other plurals
      return categoryName.endsWith('s') ? categoryName.slice(0, -1) : categoryName;
    }

    return 'generic';
  };

  // Custom edge for parent-child relationships
  const ParentChildEdge = ({ id, source, target, style, data, ...props }) => {
    const [edgePath, labelX, labelY] = getBezierPath({
      sourceX: props.sourceX,
      sourceY: props.sourceY,
      sourcePosition: props.sourcePosition,
      targetX: props.targetX,
      targetY: props.targetY,
      targetPosition: props.targetPosition,
    });

    return (
      <>
        <path
          id={id}
          className="react-flow__edge-path parent-child-edge"
          d={edgePath}
          strokeWidth={2}
          stroke="#BB86FC"
          strokeDasharray="5,5"
        />
        <text>
          <textPath
            href={`#${id}`}
            style={{ fontSize: '12px', fill: '#BB86FC' }}
            startOffset="50%"
            textAnchor="middle"
            dominantBaseline="middle"
          >
            Contains
          </textPath>
        </text>
      </>
    );
  };

  // Custom edge for user-created relationships
  const UserCreatedEdge = ({ id, source, target, style, label, data, ...props }) => {
    const [edgePath, labelX, labelY] = getBezierPath({
      sourceX: props.sourceX,
      sourceY: props.sourceY,
      sourcePosition: props.sourcePosition,
      targetX: props.targetX,
      targetY: props.targetY,
      targetPosition: props.targetPosition,
    });

    return (
      <>
        <path
          id={id}
          className="react-flow__edge-path user-created-edge"
          d={edgePath}
          strokeWidth={2}
          stroke="#FF5722"
        />
        <text>
          <textPath
            href={`#${id}`}
            style={{ fontSize: '12px', fill: '#FF5722' }}
            startOffset="50%"
            textAnchor="middle"
            dominantBaseline="middle"
          >
            {label}
          </textPath>
        </text>
      </>
    );
  };

  // Define node types for React Flow
  const nodeTypes = useMemo(() => ({
    worldElement: WorldElementNode
  }), []);

  // Define edge types for React Flow
  const edgeTypes = useMemo(() => ({
    'parent-child': ParentChildEdge,
    'user-created': UserCreatedEdge
  }), []);

  // Initialize nodes and edges from props
  useEffect(() => {
    if (elements && elements.length > 0) {
      // Group elements by parent-child relationships
      const rootElements = elements.filter(element => !element.parent_id);
      const childElements = elements.filter(element => element.parent_id);

      // Create a map of parent IDs to their child elements
      const childrenByParent = {};
      childElements.forEach(child => {
        if (!childrenByParent[child.parent_id]) {
          childrenByParent[child.parent_id] = [];
        }
        childrenByParent[child.parent_id].push(child);
      });

      // Position calculation helper function
      const calculatePosition = (element, index, level = 0, parentX = 0, parentY = 0) => {
        const elementId = element.element_id;

        // Use saved position if available
        if (savedNodePositions && savedNodePositions[elementId]) {
          return savedNodePositions[elementId];
        }

        // Base position for root elements
        if (level === 0) {
          return {
            x: (index % 4) * 400 + 200, // More spacing between nodes
            y: Math.floor(index / 4) * 300 + 150 // More spacing between rows
          };
        }

        // Position child elements relative to their parent
        const childCount = childrenByParent[element.parent_id]?.length || 1;
        const angle = (index / childCount) * 2 * Math.PI;
        const radius = 250; // Increased distance from parent

        return {
          x: parentX + radius * Math.cos(angle),
          y: parentY + radius * Math.sin(angle)
        };
      };

      // Transform elements into nodes
      let processedNodes = [];

      // Process root elements first
      rootElements.forEach((element, index) => {
        const position = calculatePosition(element, index);

        processedNodes.push({
          id: element.element_id,
          type: 'worldElement',
          data: {
            label: element.name,
            category: element.category_id || element.category,
            elementType: determineElementType(element),
            isConnectable: true,
            element: element,
            isParent: !!childrenByParent[element.element_id]
          },
          className: `node-${element.category_id || element.category}`,
          position
        });
      });

      // Process child elements
      childElements.forEach((element, index) => {
        // Find parent node to position relative to it
        const parentNode = processedNodes.find(node => node.id === element.parent_id);

        if (parentNode) {
          const parentChildren = childrenByParent[element.parent_id] || [];
          const childIndex = parentChildren.findIndex(child => child.element_id === element.element_id);
          const position = calculatePosition(
            element,
            childIndex,
            1,
            parentNode.position.x,
            parentNode.position.y
          );

          processedNodes.push({
            id: element.element_id,
            type: 'worldElement',
            data: {
              label: element.name,
              category: element.category_id || element.category,
              elementType: determineElementType(element),
              isConnectable: true,
              element: element,
              isChild: true,
              parentId: element.parent_id,
              isParent: !!childrenByParent[element.element_id]
            },
            className: `node-${element.category_id || element.category}`,
            position
          });
        }
      });

      // Create edges for explicit relationships
      let processedEdges = relationships && relationships.length > 0
        ? relationships.map(relationship => ({
            id: relationship.relationship_id,
            source: relationship.source_id,
            target: relationship.target_id,
            label: relationship.type,
            data: { relationship }
          }))
        : [];

      // Add user-created relationships
      if (userRelationships && userRelationships.length > 0) {
        userRelationships.forEach(relationship => {
          // Check if both source and target nodes exist
          const sourceExists = processedNodes.some(node => node.id === relationship.source);
          const targetExists = processedNodes.some(node => node.id === relationship.target);

          if (sourceExists && targetExists) {
            // Check if this edge already exists
            const edgeExists = processedEdges.some(edge =>
              edge.source === relationship.source && edge.target === relationship.target
            );

            if (!edgeExists) {
              processedEdges.push({
                id: relationship.id || `e${relationship.source}-${relationship.target}`,
                source: relationship.source,
                target: relationship.target,
                label: relationship.label,
                type: 'user-created',
                style: { stroke: '#FF5722' }, // Distinctive color for user-created relationships
                data: { isUserCreated: true, relationship }
              });
            }
          }
        });
      }

      // Add parent-child relationship edges
      childElements.forEach(child => {
        const edgeId = `parent-child-${child.parent_id}-${child.element_id}`;

        // Check if this edge already exists in the relationships
        const edgeExists = processedEdges.some(edge =>
          edge.source === child.parent_id && edge.target === child.element_id
        );

        if (!edgeExists) {
          processedEdges.push({
            id: edgeId,
            source: child.parent_id,
            target: child.element_id,
            label: 'Contains',
            type: 'parent-child',
            animated: true,
            style: { stroke: '#BB86FC' },
            data: { isParentChild: true }
          });
        }
      });

      setNodes(processedNodes);
      setEdges(processedEdges);
    } else {
      // Use empty arrays if no elements are provided
      setNodes([]);
      setEdges([]);
    }
  }, [elements, relationships, userRelationships, savedNodePositions, setNodes, setEdges]);

  // Custom handler for node changes that saves positions
  const handleNodesChange = useCallback((changes) => {
    // Apply changes to nodes
    onNodesChange(changes);

    // Check if any of the changes are position changes
    const positionChanges = changes.filter(change =>
      change.type === 'position' && !change.dragging
    );

    // If there are position changes and they're not from dragging, save them
    if (positionChanges.length > 0) {
      // Wait for the next render cycle to ensure nodes have updated positions
      setTimeout(() => {
        // Get the current positions of all nodes
        const positions = {};

        // Get updated nodes with their current positions
        const currentNodes = document.querySelectorAll('.react-flow__node');
        currentNodes.forEach(nodeEl => {
          const nodeId = nodeEl.getAttribute('data-id');
          if (nodeId) {
            // Get the transform style and extract x,y coordinates
            const transform = nodeEl.style.transform;
            const match = transform.match(/translate\(([\d.-]+)px,\s*([\d.-]+)px\)/i);

            if (match && match.length === 3) {
              const x = parseFloat(match[1]);
              const y = parseFloat(match[2]);
              positions[nodeId] = { x, y };
            }
          }
        });

        // Save positions to Redux
        if (Object.keys(positions).length > 0) {
          console.log('Saving node positions:', positions);
          dispatch(updateNodePositions(positions));
          positionsSaved.current = true;
        }
      }, 100);
    }
  }, [onNodesChange, dispatch]);

  // Handle connection between nodes
  const onConnect = useCallback((params) => {
    // Create a unique ID for the new edge
    const edgeId = `e${params.source}-${params.target}`;
    const relationshipLabel = relationshipTypes.find(type => type.id === relationshipType)?.label || 'Connected To';

    // Create the new edge with the selected relationship type
    const newEdge = {
      ...params,
      id: edgeId,
      label: relationshipLabel,
      type: 'user-created',
      style: { stroke: '#FF5722' }, // Distinctive color for user-created relationships
      data: { isUserCreated: true }
    };

    // Add the edge to the graph
    setEdges((eds) => addEdge(newEdge, eds));

    // Save the relationship to Redux
    dispatch(saveUserRelationship({
      id: edgeId,
      source: params.source,
      target: params.target,
      label: relationshipLabel,
      type: relationshipType
    }));

    // Save the relationship to the backend
    if (bookId) {
      const relationshipData = {
        source_element_id: params.source,
        target_element_id: params.target,
        relationship_type: relationshipType,
        description: ''
      };

      createWorldRelationship(bookId, relationshipData)
        .then(response => {
          console.log('Relationship saved to backend:', response);
        })
        .catch(error => {
          console.error('Error saving relationship to backend:', error);
        });
    }

    // Reset relationship creation state
    setIsCreatingRelationship(false);
    setSourceElement(null);
  }, [relationshipType, relationshipTypes, setEdges, dispatch, bookId]);

  // State for available relationship types
  const [availableRelationshipTypes, setAvailableRelationshipTypes] = useState([]);

  // Handle edge selection
  const onEdgeClick = useCallback((event, edge) => {
    // Only allow deleting user-created relationships
    if (edge.type === 'user-created' || !edge.type) {
      if (window.confirm(`Are you sure you want to delete this ${edge.label || 'Connected To'} relationship?`)) {
        // Get the relationship ID
        const relationshipId = edge.id;

        // Remove the edge from the graph
        setEdges((eds) => eds.filter(e => e.id !== relationshipId));

        // Dispatch action to remove from Redux state
        dispatch(removeUserRelationship(relationshipId));

        // Remove from localStorage
        if (bookId) {
          try {
            const relationshipsKey = `worldBuilding_userRelationships_${bookId}`;
            const savedRelationships = JSON.parse(localStorage.getItem(relationshipsKey) || '[]');
            const updatedRelationships = savedRelationships.filter(r => r.id !== relationshipId);
            localStorage.setItem(relationshipsKey, JSON.stringify(updatedRelationships));
          } catch (error) {
            console.error('Error updating localStorage:', error);
          }
        }

        // Delete from backend
        if (bookId) {
          deleteWorldRelationship(bookId, relationshipId)
            .then(response => {
              console.log('Relationship deleted from backend:', response);
            })
            .catch(error => {
              console.error('Error deleting relationship from backend:', error);
            });
        }
      }
    }
  }, [bookId, setEdges, dispatch]);

  // Handle node selection
  const onNodeClick = useCallback((event, node) => {
    if (isCreatingRelationship) {
      if (!sourceElement) {
        // First node selected as source
        setSourceElement(node);
        // Set default relationship type to the first available one
        const sourceType = node.data.elementType;
        const availableTypes = relationshipTypes.filter(type =>
          type.sourceTypes.length === 0 || type.sourceTypes.includes(sourceType)
        );
        setAvailableRelationshipTypes(availableTypes);
        if (availableTypes.length > 0) {
          setRelationshipType(availableTypes[0].id);
        }
      } else {
        // Second node selected as target, create the relationship
        const sourceType = sourceElement.data.elementType;
        const targetType = node.data.elementType;

        // Get available relationship types for this source-target pair
        const availableTypes = getAvailableRelationshipTypes(sourceType, targetType);

        // If no relationship types are available, show an alert
        if (availableTypes.length === 0) {
          alert(`No valid relationship types between ${sourceType || 'this source'} and ${targetType || 'this target'}`);
          return;
        }

        // Use the current relationship type if it's valid, otherwise use the first available one
        const validRelationshipType = availableTypes.find(type => type.id === relationshipType) || availableTypes[0];

        const edgeId = `e${sourceElement.id}-${node.id}`;
        const newEdge = {
          id: edgeId,
          source: sourceElement.id,
          target: node.id,
          label: validRelationshipType.label,
          type: 'user-created',
          style: { stroke: '#FF5722' }, // Distinctive color for user-created relationships
          data: {
            isUserCreated: true,
            relationshipType: validRelationshipType.id,
            sourceType,
            targetType
          }
        };

        // Add the edge to the graph
        setEdges((eds) => [...eds, newEdge]);

        // Save the relationship to Redux
        dispatch(saveUserRelationship({
          id: edgeId,
          source: sourceElement.id,
          target: node.id,
          label: validRelationshipType.label,
          type: validRelationshipType.id
        }));

        // Save the relationship to the backend
        if (bookId) {
          const relationshipData = {
            source_element_id: sourceElement.id,
            target_element_id: node.id,
            relationship_type: validRelationshipType.id,
            description: ''
          };

          createWorldRelationship(bookId, relationshipData)
            .then(response => {
              console.log('Relationship saved to backend:', response);
            })
            .catch(error => {
              console.error('Error saving relationship to backend:', error);
            });
        }

        // Reset relationship creation state
        setIsCreatingRelationship(false);
        setSourceElement(null);
        setAvailableRelationshipTypes([]);
      }
    } else {
      // Normal selection
      setSelectedElements([node]);
    }
  }, [isCreatingRelationship, sourceElement, relationshipType, bookId, dispatch, setEdges]);

  // Start creating a relationship
  const handleCreateRelationship = () => {
    setIsCreatingRelationship(true);
    setSourceElement(null);
    setAvailableRelationshipTypes([]);
  };

  // Cancel relationship creation
  const handleCancelRelationship = () => {
    setIsCreatingRelationship(false);
    setSourceElement(null);
    setAvailableRelationshipTypes([]);
  };

  // Get available relationship types based on source and target element types
  const getAvailableRelationshipTypes = useCallback((sourceType, targetType) => {
    if (!sourceType || !targetType) {
      return relationshipTypes.filter(type => type.sourceTypes.length === 0 && type.targetTypes.length === 0);
    }

    return relationshipTypes.filter(type => {
      // If sourceTypes is empty, it's a generic relationship that works with any source
      const sourceMatches = type.sourceTypes.length === 0 || type.sourceTypes.includes(sourceType);
      // If targetTypes is empty, it's a generic relationship that works with any target
      const targetMatches = type.targetTypes.length === 0 || type.targetTypes.includes(targetType);

      return sourceMatches && targetMatches;
    });
  }, [relationshipTypes]);

  // Filter nodes based on search term and category
  const filteredNodes = useMemo(() => {
    return nodes.filter(node => {
      const matchesSearch = node.data.label.toLowerCase().includes(searchTerm.toLowerCase());

      // Check if the node's category matches the filter
      let matchesCategory = categoryFilter === 'all';

      if (!matchesCategory && node.data.category && categoryFilter) {
        // Get the node's category, handling both formats (with or without 'cat_' prefix)
        const nodeCategory = node.data.category.toLowerCase();
        const filter = categoryFilter.toLowerCase();

        // Direct match (exact category ID match)
        if (nodeCategory === filter) {
          matchesCategory = true;
        }
        // Handle legacy data without 'cat_' prefix
        else if (nodeCategory === filter.replace('cat_', '')) {
          matchesCategory = true;
        }
        // Handle data with 'cat_' prefix when filter doesn't have it
        else if ('cat_' + nodeCategory === filter) {
          matchesCategory = true;
        }
        // Match by aspect (physical, social, etc.)
        else if (filter.includes('_') && nodeCategory.includes('_')) {
          // Extract aspect from filter (e.g., 'physical' from 'cat_physical_locations')
          const filterParts = filter.split('_');
          const filterAspect = filterParts.length > 1 ? filterParts[1] : filterParts[0];

          // Extract aspect from node category
          const nodeCategoryParts = nodeCategory.split('_');
          const nodeAspect = nodeCategoryParts.length > 1 ?
            (nodeCategoryParts[0] === 'cat' ? nodeCategoryParts[1] : nodeCategoryParts[0]) :
            nodeCategoryParts[0];

          // Match if aspects match
          if (nodeAspect === filterAspect) {
            matchesCategory = true;
          }
        }
      }

      return matchesSearch && matchesCategory;
    });
  }, [nodes, searchTerm, categoryFilter]);

  return (
    <div className="world-relationship-view">
      <div className="relationship-controls">
        <div className="control-group">
          <h3>Relationship View</h3>
          <div className="filter-controls">
            <input
              type="text"
              placeholder="Search elements..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="search-input"
            />
            <select
              value={categoryFilter}
              onChange={(e) => setCategoryFilter(e.target.value)}
              className="category-filter"
            >
              <option value="all">All Categories</option>
              <optgroup label="Physical">
                <option value="cat_physical_locations">Locations</option>
                <option value="cat_physical_geography">Geography</option>
                <option value="cat_physical_climate">Climate & Weather</option>
                <option value="cat_physical_flora_fauna">Flora & Fauna</option>
                <option value="cat_physical_monsters">Monsters & Creatures</option>
                <option value="cat_physical_aliens">Alien Species</option>
              </optgroup>
              <optgroup label="Social">
                <option value="cat_social_organizations">Organizations</option>
                <option value="cat_social_cultures">Cultures & Societies</option>
                <option value="cat_social_laws">Laws & Governance</option>
                <option value="cat_social_economics">Economics & Trade</option>
              </optgroup>
              <optgroup label="Metaphysical">
                <option value="cat_metaphysical_magic">Magic Systems</option>
                <option value="cat_metaphysical_religion">Religion & Beliefs</option>
                <option value="cat_metaphysical_cosmology">Cosmology</option>
                <option value="cat_metaphysical_superpowers">Superpowers</option>
              </optgroup>
              <optgroup label="Technological">
                <option value="cat_technological_tools">Tools & Technology</option>
                <option value="cat_technological_infrastructure">Infrastructure</option>
                <option value="cat_technological_sciences">Sciences & Knowledge</option>
                <option value="cat_technological_scifi">Advanced Technology</option>
              </optgroup>
            </select>
          </div>
        </div>

        <div className="action-buttons">
          {isCreatingRelationship ? (
            <>
              <div className="relationship-status">
                {sourceElement ? (
                  <span>Select target element for relationship from: <strong>{sourceElement.data.label}</strong></span>
                ) : (
                  <span>Select source element for relationship</span>
                )}
              </div>
              <select
                value={relationshipType}
                onChange={(e) => setRelationshipType(e.target.value)}
                className="relationship-type-select"
                disabled={!sourceElement}
              >
                {sourceElement ? (
                  availableRelationshipTypes.length > 0 ? (
                    availableRelationshipTypes.map(type => (
                      <option key={type.id} value={type.id}>{type.label}</option>
                    ))
                  ) : (
                    <option value="">No available relationships</option>
                  )
                ) : (
                  <option value="">Select source first</option>
                )}
              </select>
              <button
                className="cancel-button"
                onClick={handleCancelRelationship}
              >
                Cancel
              </button>
            </>
          ) : (
            <button
              className="create-relationship-button"
              onClick={handleCreateRelationship}
            >
              Create Relationship
            </button>
          )}
        </div>
      </div>

      <div className="relationship-graph">
        <ReactFlow
          nodes={filteredNodes}
          edges={edges}
          onNodesChange={handleNodesChange}
          onEdgesChange={onEdgesChange}
          onConnect={onConnect}
          onNodeClick={onNodeClick}
          onEdgeClick={onEdgeClick}
          nodeTypes={nodeTypes}
          edgeTypes={edgeTypes}
          fitView
          minZoom={0.2}
          maxZoom={2}
          defaultViewport={{ x: 0, y: 0, zoom: 1 }}
          fitViewOptions={{ padding: 0.3 }} // Add more padding around nodes
        >
          <Controls />
          <MiniMap nodeStrokeWidth={3} zoomable pannable />
          <Background variant="dots" gap={12} size={1} />
          <Panel position="top-right">
            <div className="legend">
              <h4>Legend</h4>
              <div className="legend-section">
                <h5>Physical</h5>
                <div className="legend-item">
                  <div className="legend-color cat_physical_locations"></div>
                  <span>Locations</span>
                </div>
                <div className="legend-item">
                  <div className="legend-color cat_physical_geography"></div>
                  <span>Geography</span>
                </div>
                <div className="legend-item">
                  <div className="legend-color cat_physical_climate"></div>
                  <span>Climate & Weather</span>
                </div>
                <div className="legend-item">
                  <div className="legend-color cat_physical_flora_fauna"></div>
                  <span>Flora & Fauna</span>
                </div>
                <div className="legend-item">
                  <div className="legend-color cat_physical_monsters"></div>
                  <span>Monsters & Creatures</span>
                </div>
              </div>

              <div className="legend-section">
                <h5>Social</h5>
                <div className="legend-item">
                  <div className="legend-color cat_social_organizations"></div>
                  <span>Organizations</span>
                </div>
                <div className="legend-item">
                  <div className="legend-color cat_social_cultures"></div>
                  <span>Cultures & Societies</span>
                </div>
                <div className="legend-item">
                  <div className="legend-color cat_social_laws"></div>
                  <span>Laws & Governance</span>
                </div>
                <div className="legend-item">
                  <div className="legend-color cat_social_economics"></div>
                  <span>Economics & Trade</span>
                </div>
              </div>

              <div className="legend-section">
                <h5>Metaphysical</h5>
                <div className="legend-item">
                  <div className="legend-color cat_metaphysical_magic"></div>
                  <span>Magic Systems</span>
                </div>
                <div className="legend-item">
                  <div className="legend-color cat_metaphysical_religion"></div>
                  <span>Religion & Beliefs</span>
                </div>
                <div className="legend-item">
                  <div className="legend-color cat_metaphysical_cosmology"></div>
                  <span>Cosmology</span>
                </div>
                <div className="legend-item">
                  <div className="legend-color cat_metaphysical_superpowers"></div>
                  <span>Superpowers</span>
                </div>
              </div>

              <div className="legend-section">
                <h5>Technological</h5>
                <div className="legend-item">
                  <div className="legend-color cat_technological_tools"></div>
                  <span>Tools & Technology</span>
                </div>
                <div className="legend-item">
                  <div className="legend-color cat_technological_infrastructure"></div>
                  <span>Infrastructure</span>
                </div>
                <div className="legend-item">
                  <div className="legend-color cat_technological_sciences"></div>
                  <span>Sciences & Knowledge</span>
                </div>
                <div className="legend-item">
                  <div className="legend-color cat_technological_scifi"></div>
                  <span>Advanced Technology</span>
                </div>
              </div>

              <div className="legend-section">
                <h5>Relationship Types</h5>
                <div className="legend-item">
                  <div className="legend-color parent-child"></div>
                  <span>Contains</span>
                </div>
                <div className="legend-item">
                  <div className="legend-color user-created"></div>
                  <span>User Created</span>
                </div>
              </div>
            </div>
          </Panel>
        </ReactFlow>
      </div>
    </div>
  );
};

export default WorldRelationshipView;

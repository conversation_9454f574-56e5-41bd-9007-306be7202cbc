/* frontend/src/components/WorldCategoriesColumn.css */
.world-categories-column {
  display: flex;
  flex-direction: column;
  width: 300px;
  height: 100%;
  border-right: 1px solid var(--border-color);
  background-color: var(--background-primary);
}

.categories-column-header {
  padding: 15px;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--background-secondary);
}

.categories-column-header h3 {
  margin: 0;
  font-size: 1.1rem;
  color: var(--text-primary);
}

.categories-list {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
}

.category-item {
  display: flex;
  align-items: flex-start;
  padding: 8px 10px;
  border-radius: 6px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.2s;
  background: var(--background-secondary);
  position: relative;
  border-left: 3px solid transparent;
}

.category-item:hover {
  background: var(--background-tertiary);
}

.category-item.active {
  background: var(--background-tertiary);
  border-left: 4px solid var(--primary);
  padding-left: 14px; /* Adjust padding to account for thicker border */
}

.category-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  margin-right: 10px;
  font-size: 1rem;
}

.category-icon.physical {
  background: rgba(33, 150, 243, 0.1);
  color: #2196f3;
}

.category-icon.social {
  background: rgba(156, 39, 176, 0.1);
  color: #9c27b0;
}

.category-icon.metaphysical {
  background: rgba(255, 152, 0, 0.1);
  color: #ff9800;
}

.category-icon.technological {
  background: rgba(76, 175, 80, 0.1);
  color: #4caf50;
}

.category-info {
  flex: 1;
}

.category-info h4 {
  margin: 0 0 3px 0;
  font-size: 0.95rem;
  color: var(--text-primary);
}

.category-info p {
  margin: 0;
  font-size: 0.8rem;
  color: var(--text-secondary);
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Category background colors by aspect type */
.category-item.physical-category {
  background: linear-gradient(to right, var(--background-secondary), rgba(33, 150, 243, 0.1));
  border-left: 3px solid #2196f3;
}

.category-item.social-category {
  background: linear-gradient(to right, var(--background-secondary), rgba(156, 39, 176, 0.1));
  border-left: 3px solid #9c27b0;
}

.category-item.metaphysical-category {
  background: linear-gradient(to right, var(--background-secondary), rgba(255, 152, 0, 0.1));
  border-left: 3px solid #ff9800;
}

.category-item.technological-category {
  background: linear-gradient(to right, var(--background-secondary), rgba(76, 175, 80, 0.1));
  border-left: 3px solid #4caf50;
}

/* Hover states for categories by aspect type */
.category-item.physical-category:hover {
  background: linear-gradient(to right, var(--background-tertiary), rgba(33, 150, 243, 0.2));
}

.category-item.social-category:hover {
  background: linear-gradient(to right, var(--background-tertiary), rgba(156, 39, 176, 0.2));
}

.category-item.metaphysical-category:hover {
  background: linear-gradient(to right, var(--background-tertiary), rgba(255, 152, 0, 0.2));
}

.category-item.technological-category:hover {
  background: linear-gradient(to right, var(--background-tertiary), rgba(76, 175, 80, 0.2));
}

/* Active states for categories by aspect type */
.category-item.physical-category.active {
  background: linear-gradient(to right, var(--background-tertiary), rgba(33, 150, 243, 0.3));
  border-left: 4px solid #2196f3;
  padding-left: 14px; /* Adjust padding to account for thicker border */
}

.category-item.social-category.active {
  background: linear-gradient(to right, var(--background-tertiary), rgba(156, 39, 176, 0.3));
  border-left: 4px solid #9c27b0;
  padding-left: 14px; /* Adjust padding to account for thicker border */
}

.category-item.metaphysical-category.active {
  background: linear-gradient(to right, var(--background-tertiary), rgba(255, 152, 0, 0.3));
  border-left: 4px solid #ff9800;
  padding-left: 14px; /* Adjust padding to account for thicker border */
}

.category-item.technological-category.active {
  background: linear-gradient(to right, var(--background-tertiary), rgba(76, 175, 80, 0.3));
  border-left: 4px solid #4caf50;
  padding-left: 14px; /* Adjust padding to account for thicker border */
}

/* Legacy category styles (keeping for backward compatibility) */
.category-item.universal-category {
  background: linear-gradient(to right, var(--background-secondary), rgba(76, 175, 80, 0.1));
  border-left: 3px solid #4caf50;
}

.category-item.genre-category {
  background: linear-gradient(to right, var(--background-secondary), rgba(255, 152, 0, 0.1));
  border-left: 3px solid #ff9800;
}

/* Hover states for legacy categories */
.category-item.universal-category:hover {
  background: linear-gradient(to right, var(--background-tertiary), rgba(76, 175, 80, 0.2));
}

.category-item.genre-category:hover {
  background: linear-gradient(to right, var(--background-tertiary), rgba(255, 152, 0, 0.2));
}

/* Active states for legacy categories */
.category-item.universal-category.active {
  background: linear-gradient(to right, var(--background-tertiary), rgba(76, 175, 80, 0.3));
  border-left: 4px solid #4caf50;
  padding-left: 14px; /* Adjust padding to account for thicker border */
}

.category-item.genre-category.active {
  background: linear-gradient(to right, var(--background-tertiary), rgba(255, 152, 0, 0.3));
  border-left: 4px solid #ff9800;
  padding-left: 14px; /* Adjust padding to account for thicker border */
}

.no-categories {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  text-align: center;
  color: var(--text-secondary);
  padding: 0 20px;
}

/* Dark theme adjustments */
.theme-dark .world-categories-column {
  background-color: var(--background-secondary);
}

.theme-dark .categories-column-header {
  background-color: var(--background-tertiary);
}

/* Responsive styles */
@media (max-width: 768px) {
  .world-categories-column {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid var(--border-color);
  }
}

/* frontend/src/components/TemplateSelectionModal.css */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.template-selection-modal {
  background-color: var(--background-primary);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  max-width: 800px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  padding: 20px;
  position: relative;
  z-index: 1001;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid var(--border-color);
}

.modal-header h2 {
  margin: 0;
  font-size: 1.5rem;
  color: var(--text-primary);
}

.close-button {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--text-secondary);
  transition: color 0.2s;
}

.close-button:hover {
  color: var(--text-primary);
}

.template-search {
  margin-bottom: 20px;
}

.template-search input {
  width: 100%;
  padding: 10px 15px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  font-size: 1rem;
  background-color: var(--background-secondary);
  color: var(--text-primary);
}

.template-search input:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.1);
}

.popular-templates,
.template-list {
  margin-bottom: 20px;
}

.popular-templates h3,
.template-list h3 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 1.2rem;
  color: var(--text-primary);
}

.template-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 15px;
}

.template-card {
  background-color: var(--background-secondary);
  border-radius: 6px;
  padding: 15px;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  height: 100%;
}

.template-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  border-color: var(--primary);
}

.template-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.template-card-header h3 {
  margin: 0;
  font-size: 1.1rem;
  color: var(--text-primary);
}

.system-template {
  border-left: 3px solid var(--primary);
}

.user-template {
  border-left: 3px solid var(--secondary);
}

.custom-badge {
  font-size: 0.7rem;
  padding: 2px 6px;
  border-radius: 10px;
  background-color: var(--secondary);
  color: white;
  font-weight: bold;
}

.template-description {
  margin: 0 0 15px 0;
  font-size: 0.9rem;
  color: var(--text-secondary);
  flex-grow: 1;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.template-fields {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  margin-bottom: 15px;
}

.template-field {
  font-size: 0.8rem;
  padding: 3px 8px;
  border-radius: 12px;
  background-color: var(--background-tertiary);
  color: var(--text-secondary);
}

.template-field-more {
  font-size: 0.8rem;
  padding: 3px 8px;
  border-radius: 12px;
  background-color: var(--background-tertiary);
  color: var(--text-secondary);
  font-style: italic;
}

.template-footer {
  display: flex;
  justify-content: space-between;
  font-size: 0.8rem;
  color: var(--text-secondary);
}

.template-version {
  background-color: rgba(33, 150, 243, 0.1);
  padding: 2px 6px;
  border-radius: 10px;
  color: var(--primary);
}

.template-usage {
  font-style: italic;
}

.template-loading,
.template-error,
.no-templates {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 30px;
  color: var(--text-secondary);
  text-align: center;
}

.template-error {
  color: var(--error);
}

/* Dark theme adjustments */
.theme-dark .template-selection-modal {
  background-color: var(--background-secondary);
}

.theme-dark .template-card {
  background-color: var(--background-tertiary);
}

/* Responsive styles */
@media (max-width: 768px) {
  .template-selection-modal {
    width: 95%;
    max-height: 90vh;
  }
  
  .template-cards {
    grid-template-columns: 1fr;
  }
}

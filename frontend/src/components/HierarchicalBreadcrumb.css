/* frontend/src/components/HierarchicalBreadcrumb.css */
.hierarchical-breadcrumb {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  padding: 8px 0;
  margin-bottom: 16px;
  font-size: 0.9rem;
  color: var(--text-secondary);
  background-color: var(--background-secondary);
  border-radius: 4px;
  padding: 8px 12px;
}

.breadcrumb-item {
  cursor: pointer;
  transition: color 0.2s;
  display: inline-flex;
  align-items: center;
}

.breadcrumb-item:hover {
  color: var(--primary);
  text-decoration: underline;
}

.breadcrumb-item.current {
  color: var(--text-primary);
  font-weight: 600;
  cursor: default;
}

.breadcrumb-item.current:hover {
  text-decoration: none;
}

.breadcrumb-separator {
  margin: 0 8px;
  color: var(--text-tertiary);
}

.breadcrumb-type {
  font-size: 0.7rem;
  background-color: var(--background-tertiary);
  color: var(--text-tertiary);
  padding: 2px 6px;
  border-radius: 10px;
  margin-left: 5px;
}

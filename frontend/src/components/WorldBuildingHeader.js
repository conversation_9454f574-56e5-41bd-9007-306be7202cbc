// frontend/src/components/WorldBuildingHeader.js
import React from 'react';
import { Link } from 'react-router-dom';
import './WorldBuildingHeader.css';

/**
 * Header component for the World Building page
 * @param {Object} props - Component props
 * @returns {JSX.Element} WorldBuildingHeader UI
 */
const WorldBuildingHeader = ({
  currentBook,
  onToggleRelationshipMode,
  onToggleAIPanel,
  relationshipMode,
  showAIPanel
}) => {
  return (
    <div className="world-building-header">
      <div className="world-building-title">
        <h2>World Building</h2>

        {currentBook && (
          <div className="world-building-genre">
            <span>Current Genre: <strong>{currentBook.genre || 'Not set'}</strong></span>
            <Link to={`/books/${currentBook.book_id}`} className="change-genre-link">
              Change Genre
            </Link>
          </div>
        )}
      </div>

      <div className="view-mode-controls">
        <button
          className={`view-mode-button ${!relationshipMode ? 'active' : ''}`}
          onClick={() => {
            if (relationshipMode) onToggleRelationshipMode();
          }}
        >
          Elements
        </button>

        <button
          className={`view-mode-button ${relationshipMode ? 'active' : ''}`}
          onClick={onToggleRelationshipMode}
        >
          Relationships
        </button>

        <button
          className={`view-mode-button ai-button ${showAIPanel ? 'active' : ''}`}
          onClick={onToggleAIPanel}
        >
          AI Assistant
        </button>
      </div>
    </div>
  );
};

export default WorldBuildingHeader;

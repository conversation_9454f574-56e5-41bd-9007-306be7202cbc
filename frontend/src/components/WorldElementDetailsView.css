/* frontend/src/components/WorldElementDetailsView.css */
.world-element-details {
  flex: 1;
  background: var(--background-primary);
  overflow-y: auto;
  padding: 20px;
}

.placeholder-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  padding: 0 20px;
}

.placeholder-content h3 {
  margin-bottom: 15px;
  color: var(--text-primary);
}

.placeholder-content p {
  margin-bottom: 10px;
  color: var(--text-secondary);
  max-width: 500px;
}

.placeholder-content button {
  margin-top: 20px;
}

.category-details-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.category-icon.large {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  font-size: 1.8rem;
}

.category-icon.physical {
  background: rgba(76, 175, 80, 0.2);
  color: #4caf50;
}

.category-icon.social {
  background: rgba(33, 150, 243, 0.2);
  color: #2196f3;
}

.category-icon.metaphysical {
  background: rgba(156, 39, 176, 0.2);
  color: #9c27b0;
}

.category-icon.technological {
  background: rgba(255, 152, 0, 0.2);
  color: #ff9800;
}

.category-details-title {
  flex: 1;
}

.category-details-title h3 {
  margin: 0 0 5px 0;
  color: var(--text-primary);
  font-size: 1.5rem;
}

.category-details-title p {
  margin: 0 0 10px 0;
  color: var(--text-secondary);
}

.category-badges {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.category-badge {
  font-size: 0.8rem;
  padding: 3px 8px;
  border-radius: 12px;
  display: inline-flex;
  align-items: center;
}

.category-badge.universal {
  background: rgba(33, 150, 243, 0.2);
  color: #2196f3;
}

.category-badge.genre-specific {
  background: rgba(156, 39, 176, 0.2);
  color: #9c27b0;
}

.category-badge.aspect.physical {
  background: rgba(76, 175, 80, 0.2);
  color: #4caf50;
}

.category-badge.aspect.social {
  background: rgba(33, 150, 243, 0.2);
  color: #2196f3;
}

.category-badge.aspect.metaphysical {
  background: rgba(156, 39, 176, 0.2);
  color: #9c27b0;
}

.category-badge.aspect.technological {
  background: rgba(255, 152, 0, 0.2);
  color: #ff9800;
}

.category-customization {
  background: var(--background-secondary);
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
}

.category-customization h4 {
  margin: 0 0 15px 0;
  color: var(--text-primary);
  font-size: 1.1rem;
}

.customization-option {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.toggle-switch {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 20px;
  margin-right: 10px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--background-tertiary);
  transition: .4s;
  border-radius: 20px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 2px;
  bottom: 2px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

input:checked + .toggle-slider {
  background-color: var(--primary);
}

input:checked + .toggle-slider:before {
  transform: translateX(20px);
}

.default-view-type {
  margin-top: 15px;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.elements-placeholder {
  background: var(--background-secondary);
  border-radius: 8px;
  padding: 15px;
  text-align: center;
}

.elements-placeholder h4 {
  margin: 0 0 15px 0;
  color: var(--text-primary);
  font-size: 1.1rem;
}

.elements-placeholder p {
  margin: 0 0 15px 0;
  color: var(--text-secondary);
}

/* Responsive styles */
@media (max-width: 768px) {
  .category-details-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .category-icon.large {
    margin-bottom: 15px;
  }
}

// frontend/src/components/FilteredSubElementsList.js
import React, { useState } from 'react';
import './FilteredSubElementsList.css';
import SubElementsList from './SubElementsList';
import { getColorForType } from '../utils/colorMapping';

/**
 * Component for displaying a filtered list of sub-elements
 * @param {Object} props - Component props
 * @returns {JSX.Element} FilteredSubElementsList UI
 */
const FilteredSubElementsList = ({ elements, onElementSelect, onDeleteElement }) => {
  const [activeFilter, setActiveFilter] = useState('all');

  // Filter elements based on the active filter
  const filteredElements = activeFilter === 'all'
    ? elements
    : elements.filter(element => element.element_type === activeFilter);

  // Count elements by type
  const spellsCount = elements.filter(element => element.element_type === 'spell').length;
  const powersCount = elements.filter(element => element.element_type === 'magical_power').length;
  const artifactsCount = elements.filter(element => element.element_type === 'magical_artifact').length;

  return (
    <div className="filtered-sub-elements-list">
      <div className="filter-tabs">
        <button
          className={`filter-tab ${activeFilter === 'all' ? 'active' : ''}`}
          onClick={() => setActiveFilter('all')}
          style={activeFilter === 'all' ? {
            backgroundColor: getColorForType('metaphysical'),
            color: 'white',
            borderColor: getColorForType('metaphysical')
          } : {}}
        >
          All ({elements.length})
        </button>
        <button
          className={`filter-tab ${activeFilter === 'spell' ? 'active' : ''}`}
          onClick={() => setActiveFilter('spell')}
          disabled={spellsCount === 0}
          style={activeFilter === 'spell' ? {
            backgroundColor: getColorForType('spell'),
            color: 'white',
            borderColor: getColorForType('spell')
          } : spellsCount > 0 ? {
            borderColor: getColorForType('spell'),
            color: getColorForType('spell')
          } : {}}
        >
          Spells ({spellsCount})
        </button>
        <button
          className={`filter-tab ${activeFilter === 'magical_power' ? 'active' : ''}`}
          onClick={() => setActiveFilter('magical_power')}
          disabled={powersCount === 0}
          style={activeFilter === 'magical_power' ? {
            backgroundColor: getColorForType('magical_power'),
            color: 'white',
            borderColor: getColorForType('magical_power')
          } : powersCount > 0 ? {
            borderColor: getColorForType('magical_power'),
            color: getColorForType('magical_power')
          } : {}}
        >
          Powers ({powersCount})
        </button>
        <button
          className={`filter-tab ${activeFilter === 'magical_artifact' ? 'active' : ''}`}
          onClick={() => setActiveFilter('magical_artifact')}
          disabled={artifactsCount === 0}
          style={activeFilter === 'magical_artifact' ? {
            backgroundColor: getColorForType('magical_artifact'),
            color: 'white',
            borderColor: getColorForType('magical_artifact')
          } : artifactsCount > 0 ? {
            borderColor: getColorForType('magical_artifact'),
            color: getColorForType('magical_artifact')
          } : {}}
        >
          Artifacts ({artifactsCount})
        </button>
      </div>

      <SubElementsList
        elements={filteredElements}
        onElementSelect={onElementSelect}
        onDeleteElement={onDeleteElement}
      />
    </div>
  );
};

export default FilteredSubElementsList;

/* frontend/src/components/BrainstormPage.css */
.brainstorm-page {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 50px); /* Full viewport height minus header */
  background: #f9f9f9;
  overflow: hidden;
}

.brainstorm-header {
  padding: 10px 20px;
  background: #ffffff;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  flex-direction: column;
  z-index: 10;
}

.brainstorm-header h2 {
  margin: 0 0 10px 0;
  font-size: 1.5rem;
  color: #333;
}

.page-navigation {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.page-tab {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  background: #f8f9fa;
  color: #212529;
  cursor: pointer;
  transition: all 0.2s ease;
}

.page-tab.active {
  background: #0d6efd;
  color: white;
}

.brainstorm-content {
  flex: 1;
  position: relative;
  overflow: hidden;
}

/* ReactFlow customizations */
.react-flow__node {
  transition: box-shadow 0.2s ease, transform 0.2s ease;
}

.react-flow__node:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.react-flow__node.selected {
  box-shadow: 0 0 0 2px #ff0072, 0 4px 12px rgba(0, 0, 0, 0.15);
}

.react-flow__handle {
  width: 8px;
  height: 8px;
  background-color: #0d6efd;
  border: 1px solid white;
}

.react-flow__handle:hover {
  background-color: #0a58ca;
}

/* Card node styling */
.card-node {
  border-radius: 8px;
  padding: 10px;
  min-width: 180px;
  max-width: 250px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  background: #f8f9fa;
  transition: all 0.2s ease;
}

.card-node.selected {
  border: 2px solid #ff0072;
}

.card-node .card-type {
  font-weight: bold;
  font-size: 12px;
  text-transform: uppercase;
  margin-bottom: 5px;
  color: #666;
}

.card-node .card-title {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 8px;
  border-bottom: 1px solid #ddd;
  padding-bottom: 5px;
}

.card-node .card-content {
  font-size: 14px;
  margin-bottom: 10px;
  word-break: break-word;
}

.card-node .card-characters,
.card-node .card-locations {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  margin-bottom: 5px;
}

.card-node .card-characters span {
  background: #d1e7dd;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 12px;
}

.card-node .card-locations span {
  background: #cfe2ff;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 12px;
}

.card-node .card-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
  gap: 5px;
}

.card-node .card-actions button {
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 12px;
  cursor: pointer;
}

.card-node .card-actions button:last-child {
  background: #dc3545;
}

/* Modal styling */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  padding: 20px;
  border-radius: 8px;
  width: 500px;
  max-width: 90%;
}

.modal-content h3 {
  margin-top: 0;
  margin-bottom: 20px;
  color: #333;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 8px;
  border-radius: 4px;
  border: 1px solid #ced4da;
}

.form-group textarea {
  min-height: 100px;
  resize: vertical;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.form-actions button {
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
}

.form-actions button[type="button"] {
  border: 1px solid #ced4da;
  background: #0d6efd;
  color: #ff4053;
  font-weight: bold;
}

.form-actions button[type="submit"] {
  border: none;
  background: #0d6efd;
  color: white;
}

/* Loading indicator */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  flex-direction: column;
}

.loading img {
  width: 50px;
  height: 50px;
  margin-bottom: 10px;
}

/* Error message */
.error-message {
  padding: 10px;
  margin: 10px;
  background: #f8d7da;
  color: #721c24;
  border-radius: 4px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.error-message p {
  margin: 0;
}

.error-message button {
  background: transparent;
  border: none;
  color: #721c24;
  cursor: pointer;
  font-weight: bold;
  font-size: 1.2rem;
}
/* frontend/src/components/FilteredSubElementsList.css */
.filtered-sub-elements-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-top: 10px;
  padding: 15px;
  background-color: var(--background-secondary);
  border-radius: 8px;
  box-shadow: var(--shadow-sm);
}

.filter-tabs {
  display: flex;
  gap: 8px;
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 10px;
  margin-bottom: 10px;
  overflow-x: auto;
}

.filter-tab {
  padding: 8px 15px;
  background: none;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--text-secondary);
  transition: all 0.2s;
  white-space: nowrap;
}

.filter-tab:hover:not(:disabled) {
  background-color: var(--background-hover);
  color: var(--text-primary);
}

.filter-tab.active {
  background-color: var(--primary);
  color: white;
}

.filter-tab:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Dark theme adjustments */
.theme-dark .filter-tab {
  color: var(--text-secondary-dark);
}

.theme-dark .filter-tab:hover:not(:disabled) {
  background-color: var(--background-hover-dark);
  color: var(--text-primary-dark);
}

/* Responsive styles */
@media (max-width: 768px) {
  .filter-tabs {
    flex-wrap: wrap;
  }
}

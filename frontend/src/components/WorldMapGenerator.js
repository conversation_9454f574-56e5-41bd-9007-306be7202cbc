// frontend/src/components/WorldMapGenerator.js
import React, { useState } from 'react';
import { useSelector } from 'react-redux';
import { generateMap } from '../services/worldBuildingApiService';
import { selectCurrentBookId } from '../redux/slices/worldBuildingSlice';
import './WorldMapGenerator.css';

/**
 * Component for generating maps using AI
 * @param {Object} props - Component props
 * @returns {JSX.Element} WorldMapGenerator UI
 */
const WorldMapGenerator = ({ onMapGenerated }) => {
  const bookId = useSelector(selectCurrentBookId);
  
  // State for the generator
  const [prompt, setPrompt] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState(null);
  
  // Preset prompts
  const presetPrompts = [
    { id: 'fantasy-kingdom', label: 'Fantasy Kingdom', prompt: 'Generate a map of a medieval fantasy kingdom with mountains, forests, and a coastal region.' },
    { id: 'sci-fi-planet', label: 'Sci-Fi Planet', prompt: 'Create a map of a futuristic colony on an alien planet with different biomes and settlement areas.' },
    { id: 'city-map', label: 'City Map', prompt: 'Design a detailed map of a major city with districts, landmarks, and transportation routes.' },
    { id: 'dungeon', label: 'Dungeon/Cave System', prompt: 'Create a map of an underground dungeon or cave system with chambers, corridors, and hazards.' },
    { id: 'archipelago', label: 'Island Archipelago', prompt: 'Generate a map of an island archipelago with different sized islands, reefs, and shipping routes.' }
  ];
  
  // Handle prompt change
  const handlePromptChange = (e) => {
    setPrompt(e.target.value);
  };
  
  // Handle preset selection
  const handlePresetSelect = (presetPrompt) => {
    setPrompt(presetPrompt);
  };
  
  // Handle map generation
  const handleGenerateMap = async () => {
    if (!prompt.trim()) {
      setError('Please enter a prompt');
      return;
    }
    
    try {
      setIsGenerating(true);
      setError(null);
      
      const generatedMap = await generateMap(bookId, prompt);
      
      if (onMapGenerated && typeof onMapGenerated === 'function') {
        onMapGenerated(generatedMap);
      }
      
      // Clear the prompt after successful generation
      setPrompt('');
    } catch (error) {
      console.error('Error generating map:', error);
      setError(error.message || 'Failed to generate map');
    } finally {
      setIsGenerating(false);
    }
  };
  
  return (
    <div className="world-map-generator">
      <h3>Generate Map with AI</h3>
      
      <div className="preset-prompts">
        <h4>Preset Map Types:</h4>
        <div className="preset-buttons">
          {presetPrompts.map(preset => (
            <button
              key={preset.id}
              className="preset-button"
              onClick={() => handlePresetSelect(preset.prompt)}
              disabled={isGenerating}
            >
              {preset.label}
            </button>
          ))}
        </div>
      </div>
      
      <div className="generator-form">
        <div className="form-group">
          <label htmlFor="map-prompt">Map Description:</label>
          <textarea
            id="map-prompt"
            value={prompt}
            onChange={handlePromptChange}
            placeholder="Describe the map you want to generate..."
            rows={4}
            disabled={isGenerating}
          />
        </div>
        
        {error && <div className="error-message">{error}</div>}
        
        <button
          className="generate-button"
          onClick={handleGenerateMap}
          disabled={isGenerating || !prompt.trim()}
        >
          {isGenerating ? 'Generating Map...' : 'Generate Map'}
        </button>
      </div>
      
      <div className="generator-tips">
        <h4>Tips for better maps:</h4>
        <ul>
          <li>Be specific about the type of terrain and features you want</li>
          <li>Mention the scale of the map (continent, region, city, etc.)</li>
          <li>Include key landmarks or points of interest</li>
          <li>Specify any special geographical features</li>
          <li>Mention the style or aesthetic you prefer</li>
        </ul>
      </div>
    </div>
  );
};

export default WorldMapGenerator;

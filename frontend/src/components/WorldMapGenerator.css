/* frontend/src/components/WorldMapGenerator.css */
.world-map-generator {
  background: var(--background-secondary);
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.world-map-generator h3 {
  margin: 0 0 15px 0;
  color: var(--text-primary);
  font-size: 1.2rem;
}

.preset-prompts {
  margin-bottom: 20px;
}

.preset-prompts h4 {
  margin: 0 0 10px 0;
  color: var(--text-primary);
  font-size: 1rem;
}

.preset-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.preset-button {
  padding: 8px 12px;
  background: var(--background-tertiary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.2s;
}

.preset-button:hover:not(:disabled) {
  background: var(--background-primary);
  border-color: var(--primary);
}

.preset-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.generator-form {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.form-group label {
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.form-group textarea {
  padding: 10px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background: var(--background-primary);
  color: var(--text-primary);
  font-family: inherit;
  resize: vertical;
}

.error-message {
  color: var(--error);
  font-size: 0.9rem;
  padding: 5px 0;
}

.generate-button {
  padding: 10px 16px;
  background: var(--primary);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: background 0.2s;
  align-self: flex-start;
}

.generate-button:hover:not(:disabled) {
  background: var(--button-primary-hover);
}

.generate-button:disabled {
  background: var(--background-tertiary);
  color: var(--text-secondary);
  cursor: not-allowed;
}

.generator-tips {
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid var(--border-color);
}

.generator-tips h4 {
  margin: 0 0 10px 0;
  color: var(--text-primary);
  font-size: 1rem;
}

.generator-tips ul {
  margin: 0;
  padding-left: 20px;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.generator-tips li {
  margin-bottom: 5px;
}

/* Dark theme adjustments */
.theme-dark .world-map-generator {
  background: var(--background-tertiary);
}

.theme-dark .preset-button {
  background: var(--background-primary);
}

.theme-dark .preset-button:hover:not(:disabled) {
  background: var(--background-secondary);
}

/* Responsive styles */
@media (max-width: 768px) {
  .world-map-generator {
    padding: 15px;
  }
  
  .preset-buttons {
    flex-direction: column;
  }
}

/* frontend/src/components/DeleteConfirmationModal.css */
.delete-confirmation-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.delete-confirmation-modal {
  background-color: var(--background-primary, white);
  border-radius: 8px;
  padding: 20px;
  width: 400px;
  max-width: 90%;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.delete-confirmation-modal h3 {
  margin-top: 0;
  color: var(--text-primary, #333);
  font-size: 1.2rem;
}

.delete-confirmation-modal p {
  margin-bottom: 15px;
  color: var(--text-secondary, #555);
}

.warning-text {
  color: #f44336 !important;
  font-weight: 500;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.cancel-button {
  background-color: var(--background-secondary, #f5f5f5);
  color: var(--text-primary, #333);
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
}

.delete-button {
  background-color: #f44336;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
}

.cancel-button:hover {
  background-color: var(--background-tertiary, #e0e0e0);
}

.delete-button:hover {
  background-color: #d32f2f;
}

/* Dark theme adjustments */
.theme-dark .delete-confirmation-modal {
  background-color: var(--background-secondary, #333);
  border: 1px solid var(--border-color, #444);
}

.theme-dark .cancel-button {
  background-color: var(--background-tertiary, #444);
  color: var(--text-primary, #eee);
}

.theme-dark .cancel-button:hover {
  background-color: var(--background-quaternary, #555);
}

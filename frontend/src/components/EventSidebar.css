.event-sidebar {
  width: 250px;
  border-left: 1px solid var(--event-sidebar-border, var(--border-color));
  padding: 15px;
  overflow-y: auto;
  background-color: var(--event-sidebar-background, var(--background-secondary));
  color: var(--text-primary);
}

.events-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.event-card {
  padding: 10px;
  border: 1px solid var(--event-card-border, var(--border-color));
  border-radius: 4px;
  background-color: var(--event-card-background, var(--background-primary));
  cursor: pointer;
  transition: all 0.2s;
  color: var(--text-primary);
}

.event-card:hover {
  box-shadow: var(--shadow-md);
  background-color: var(--event-card-hover, var(--background-tertiary));
}

.event-card.selected {
  border-color: var(--event-card-selected, var(--primary));
  background-color: var(--background-tertiary);
}

.event-type {
  font-weight: bold;
  font-size: 0.9em;
  color: var(--text-secondary);
  margin-bottom: 5px;
}

.event-description {
  font-size: 0.95em;
  margin-bottom: 5px;
}

.event-tags {
  margin-top: 8px;
  font-size: 0.85em;
}

.tag-label {
  color: var(--text-secondary);
  font-size: 0.85em;
  margin-right: 5px;
}

.tag-bubbles {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  margin-top: 3px;
}

.character-bubble, .location-bubble {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 0.8em;
  white-space: nowrap;
}

.character-bubble {
  background-color: #e6f7ff;
  border: 1px solid #1890ff;
  color: #1890ff;
}

.location-bubble {
  background-color: #f6ffed;
  border: 1px solid #52c41a;
  color: #52c41a;
}

.more-bubble {
  font-size: 0.75em;
  padding: 2px 5px;
}

.no-events {
  font-style: italic;
  color: var(--text-secondary);
  text-align: center;
  margin-top: 20px;
}
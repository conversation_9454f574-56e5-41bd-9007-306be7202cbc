// frontend/src/components/WorldElementNode.js
import React, { memo } from 'react';
import { <PERSON>le, Position } from 'reactflow';
import './WorldElementNode.css';
import { getColorForType, getMainCategoryForType, getColorForAspectType } from '../utils/colorMapping';

/**
 * Custom node component for world elements in the relationship view
 * @param {Object} props - Component props from React Flow
 * @returns {JSX.Element} Custom node component
 */
const WorldElementNode = ({ data, isConnectable, selected }) => {
  const { label, category, elementType, isParent, isChild, parentId } = data;

  // Determine node classes based on parent-child relationships and category
  const nodeClasses = [
    'world-element-node',
    category,
    elementType || '',
    selected ? 'selected' : '',
    isParent ? 'parent-node' : '',
    isChild ? 'child-node' : ''
  ].filter(Boolean).join(' ');

  // For debugging
  console.log(`Node ${label} - Category: ${category}, ElementType: ${elementType}, Classes: ${nodeClasses}`);

  // Get the appropriate color based on element type or category
  const nodeColor = elementType ? getColorForType(elementType) :
                   category ? getColorForAspectType(category.replace('cat_', '')) :
                   '#2196F3'; // Default blue

  // We'll use CSS classes instead of inline styles for the node border and background
  // This ensures ReactFlow's styling works properly

  return (
    <div
      className={nodeClasses}
      data-element-type={elementType}
      data-category={category}
      data-parent-id={parentId}
      // No inline style for border and background
    >
      <Handle
        type="target"
        position={Position.Top}
        isConnectable={isConnectable}
        className="node-handle"
        style={{ borderColor: nodeColor }}
      />

      <div className="node-content">
        <div className="node-icon">
          {getCategoryIcon(category, elementType)}
          {isParent && <span className="parent-indicator">👨‍👧‍👦</span>}
        </div>
        <div className="node-label">{label}</div>
        {elementType && elementType !== 'generic' && (
          <div
            className="node-type"
            style={{
              backgroundColor: nodeColor,
              color: 'white'
            }}
          >
            {formatElementType(elementType)}
          </div>
        )}
        <div className="node-category">{formatCategory(category)}</div>
        {isChild && (
          <div className="child-indicator">Sub-element</div>
        )}
      </div>

      <Handle
        type="source"
        position={Position.Bottom}
        isConnectable={isConnectable}
        className="node-handle"
        style={{ borderColor: nodeColor }}
      />
    </div>
  );
};

/**
 * Helper function to get an icon for a category and element type
 * @param {string} category - The category
 * @param {string} elementType - The element type
 * @returns {string} An emoji icon
 */
const getCategoryIcon = (category, elementType) => {
  // First check element type for more specific icons
  if (elementType) {
    switch (elementType) {
      // Physical categories
      case 'settlement':
        return '🏙️';
      case 'region':
        return '🗺️';
      case 'landform':
        return '⛰️';
      case 'waterbody':
        return '🌊';
      case 'geography':
        return '🌄';
      case 'location':
        return '📍';
      case 'plant':
        return '🌱';
      case 'animal':
        return '🐕';
      case 'creature':
        return '🐉';
      case 'monster':
        return '👹';
      case 'alien_species':
        return '👽';
      case 'alien_creature':
        return '🦠';
      case 'planetary_climate_system':
        return '🌍';
      case 'climate_system':
        return '🌦️';

      // Social categories
      case 'organization':
        return '🏢';
      case 'interplanetary_coalition':
        return '🌌';
      case 'government':
        return '🏛️';
      case 'galactic_authority':
        return '⚖️';
      case 'economic_system':
        return '💰';
      case 'interstellar_market':
        return '🛒';
      case 'historical_period':
        return '📜';
      case 'major_event':
        return '💥';
      case 'culture':
        return '🎭';
      case 'character':
        return '👤';
      case 'species':
        return '🐉';

      // Metaphysical categories
      case 'magic_system':
        return '✨';
      case 'magical_tradition':
        return '🧙';
      case 'religion':
        return '🙏';
      case 'cosmic_cult':
        return '🌠';
      case 'cosmic_structure':
        return '🌌';
      case 'interdimensional_gateway':
        return '🌀';
      case 'magical_power':
        return '🔮';
      case 'superpower':
        return '⚡';

      // Technological categories
      case 'technology':
        return '🔧';
      case 'advanced_device':
        return '📱';
      case 'infrastructure_system':
        return '🏗️';
      case 'interstellar_network':
        return '📡';
      case 'field_of_study':
        return '🔬';
      case 'scientific_institution':
        return '🏛️';
      case 'advanced_technology':
        return '🤖';
      case 'terraforming_system':
        return '🌏';
      case 'device':
        return '🔧';
      case 'transportation':
        return '🚂';
      case 'law':
        return '⚖️';
      case 'law_clause':
        return '📜';
    }
  }

  // Fall back to category icons
  switch (category) {
    case 'physical_geography':
      return '🌄';
    case 'physical_locations':
      return '🌍';
    case 'physical_climate':
      return '🌦️';
    case 'physical_flora':
      return '🌱';
    case 'physical_fauna':
      return '🦊';
    case 'metaphysical_magic':
      return '✨';
    case 'metaphysical_religion':
      return '🙏';
    case 'metaphysical_cosmology':
      return '🌌';
    case 'technological_devices':
      return '🔧';
    case 'technological_systems':
      return '⚙️';
    case 'technological_transportation':
      return '🚂';
    case 'social_organizations':
      return '👥';
    case 'social_species':
      return '🐉';
    case 'social_characters':
      return '👤';
    case 'social_cultures':
      return '🎭';
    case 'social_governments':
      return '🏛️';
    case 'world_name':
      return '🏛️';
    default:
      return '📁';
  }
};

/**
 * Helper function to format element type
 * @param {string} elementType - The element type
 * @returns {string} Formatted element type
 */
const formatElementType = (elementType) => {
  if (!elementType) return '';

  // Don't display 'generic' as a type
  if (elementType === 'generic') return '';

  // Convert snake_case to Title Case
  return elementType
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
};

/**
 * Helper function to format category name
 * @param {string} category - The category
 * @returns {string} Formatted category name
 */
const formatCategory = (category) => {
  if (!category) return '';

  // Handle categories with 'cat_' prefix
  let cleanCategory = category;
  if (category.startsWith('cat_')) {
    cleanCategory = category.substring(4); // Remove 'cat_' prefix
  }

  // Convert snake_case to Title Case
  const parts = cleanCategory.split('_');

  // If it's a two-part category like 'physical_geography', format it nicely
  if (parts.length > 1 && ['physical', 'social', 'metaphysical', 'technological'].includes(parts[0])) {
    // Format the second part (e.g., 'geography' -> 'Geography')
    const secondPart = parts[1].charAt(0).toUpperCase() + parts[1].slice(1);

    // Map the first part to a more descriptive prefix
    let prefix = '';
    switch (parts[0]) {
      case 'physical':
        prefix = '🌍 ';
        break;
      case 'social':
        prefix = '👥 ';
        break;
      case 'metaphysical':
        prefix = '✨ ';
        break;
      case 'technological':
        prefix = '⚙️ ';
        break;
      default:
        prefix = '';
    }

    return prefix + secondPart;
  }

  return parts
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
};

export default memo(WorldElementNode);

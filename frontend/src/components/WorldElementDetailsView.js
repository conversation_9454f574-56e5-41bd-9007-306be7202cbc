// frontend/src/components/WorldElementDetailsView.js
import React, { useState } from 'react';
import './WorldElementDetails.css';

/**
 * Component for displaying and managing details of a world element or category
 * @param {Object} props - Component props
 * @returns {JSX.Element} WorldElementDetails UI
 */
const WorldElementDetails = ({
  activeCategory,
  onUpdateCustomization,
  onResetCustomizations
}) => {
  // State for customization settings
  const [isEnabled, setIsEnabled] = useState(true);

  // Update local state when active category changes
  React.useEffect(() => {
    if (activeCategory) {
      setIsEnabled(activeCategory.is_enabled !== false);
    }
  }, [activeCategory]);

  // Handle customization toggle
  const handleToggleEnabled = () => {
    const newValue = !isEnabled;
    setIsEnabled(newValue);

    if (activeCategory) {
      onUpdateCustomization(activeCategory.category_id, newValue, activeCategory.display_order || 0);
    }
  };

  // If no category is selected, show a placeholder
  if (!activeCategory) {
    return (
      <div className="world-element-details">
        <div className="placeholder-content">
          <h3>World Building</h3>
          <p>Select a category to view and manage its elements.</p>
          <p>You can customize which categories are visible and create elements within each category.</p>

          <button
            className="btn btn-primary"
            onClick={onResetCustomizations}
          >
            Reset All Customizations
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="world-element-details">
      <div className="category-details-header">
        <div className={`category-icon large ${activeCategory.category_id}`}>
          {getCategoryIcon(activeCategory.category_id)}
        </div>

        <div className="category-details-title">
          <h3>{activeCategory.name}</h3>
          <p>{activeCategory.description}</p>

          <div className="category-badges">
            {activeCategory.is_universal && (
              <span className="category-badge universal">Universal</span>
            )}

            {!activeCategory.is_universal && activeCategory.applicable_genres && (
              <span className="category-badge genre-specific">
                Genre: {formatGenres(activeCategory.applicable_genres)}
              </span>
            )}

            <span className={`category-badge category ${activeCategory.category_id}`}>
              {formatCategoryName(activeCategory.category_id)}
            </span>
          </div>
        </div>
      </div>

      <div className="category-customization">
        <h4>Category Settings</h4>

        <div className="customization-option">
          <label className="toggle-switch">
            <input
              type="checkbox"
              checked={isEnabled}
              onChange={handleToggleEnabled}
            />
            <span className="toggle-slider"></span>
          </label>
          <span>Show this category in World Building</span>
        </div>

        <div className="default-view-type">
          <span>Default View Type: </span>
          <strong>{formatViewType(activeCategory.default_view_type)}</strong>
        </div>
      </div>

      <div className="elements-placeholder">
        <h4>Elements</h4>
        <p>No elements created yet.</p>
        <button className="btn btn-primary">Create New Element</button>
      </div>
    </div>
  );
};

/**
 * Helper function to get an icon for a category based on its category ID
 * @param {string} categoryId - The category ID
 * @returns {string} An emoji icon
 */
const getCategoryIcon = (categoryId) => {
  switch (categoryId) {
    case 'cat_cosmology_physical':
      return '🌍';
    case 'cat_cultural_social':
      return '👥';
    case 'cat_economic_material':
      return '💰';
    case 'cat_knowledge_technology':
      return '⚙️';
    case 'cat_temporal_historical':
      return '⏳';
    case 'cat_magical_supernatural':
      return '✨';
    case 'cat_interactive_game':
      return '🎮';
    default:
      return '📁';
  }
};

/**
 * Helper function to format category name
 * @param {string} categoryId - The category ID
 * @returns {string} Formatted category name
 */
const formatCategoryName = (categoryId) => {
  switch (categoryId) {
    case 'cat_cosmology_physical':
      return 'Cosmology & Physical Environment';
    case 'cat_cultural_social':
      return 'Cultural & Social Systems';
    case 'cat_economic_material':
      return 'Economic & Material Systems';
    case 'cat_knowledge_technology':
      return 'Knowledge & Technology Section';
    case 'cat_temporal_historical':
      return 'Temporal & Historical Elements';
    case 'cat_magical_supernatural':
      return 'Magical & Supernatural Systems';
    case 'cat_interactive_game':
      return 'Interactive Systems & Game Mechanics';
    default:
      return categoryId.replace('cat_', '').replace('_', ' ').toUpperCase();
  }
};

/**
 * Helper function to format view type
 * @param {string} viewType - The view type
 * @returns {string} Formatted view type
 */
const formatViewType = (viewType) => {
  switch (viewType) {
    case 'card':
      return 'Card View';
    case 'table':
      return 'Table View';
    case 'document':
      return 'Document View';
    default:
      return viewType;
  }
};

/**
 * Helper function to format genres array
 * @param {Array} genres - Array of genres
 * @returns {string} Formatted genres string
 */
const formatGenres = (genres) => {
  if (!genres || genres.length === 0) return 'None';

  return genres.map(genre => {
    // Convert snake_case to Title Case
    return genre
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }).join(', ');
};

export default WorldElementDetails;

import React, { useMemo } from 'react';
import './EventCard.css';

const EventCard = ({ event, onClick, isDragging }) => {
  // Extract metadata from description if it exists
  const { id, description: fullDescription } = event;

  // Get metadata directly from the event object
  const { description, characters, locations, eventType } = useMemo(() => {
    console.log('[DEBUG] Event in EventCard:', event);

    // Use the event properties directly if they exist
    const extractedCharacters = event.characters || [];
    const extractedLocations = event.locations || [];
    const extractedType = event.type || 'idea';
    let cleanDescription = fullDescription || '';

    // For backward compatibility, also check if metadata is in the description
    if (fullDescription && fullDescription.includes('METADATA:')) {
      try {
        const parts = fullDescription.split('---\n');
        cleanDescription = parts[0].trim();
      } catch (error) {
        console.error('[DEBUG] Error cleaning description:', error);
      }
    }

    console.log('[DEBUG] Extracted data:', {
      characters: extractedCharacters,
      locations: extractedLocations,
      type: extractedType
    });

    return {
      description: cleanDescription,
      characters: extractedCharacters,
      locations: extractedLocations,
      eventType: extractedType
    };
  }, [fullDescription, event]);

  // Determine the event type color
  const getTypeColor = () => {
    switch (eventType?.toLowerCase()) {
      case 'introduction':
        return '#007bff';
      case 'conflict':
        return '#ff4d4d';
      case 'reveal':
        return '#28a745';
      case 'climax':
        return '#ffcc00';
      case 'resolution':
        return '#6c757d';
      case 'idea':
        return '#9c27b0'; // Purple for ideas
      default:
        return '#ff00ff'; // Custom/default color
    }
  };

  return (
    <div
      className={`event-card ${isDragging ? 'dragging' : ''}`}
      onClick={onClick}
      style={{ borderLeftColor: getTypeColor() }}
    >
      <div className="event-card-header">
        <span className="event-type" style={{ backgroundColor: getTypeColor() }}>
          {eventType || 'Custom'}
        </span>
        <span className="event-id">{id.substring(0, 8)}</span>
      </div>

      <div className="event-description">
        {description}
      </div>

      {(characters.length > 0 || locations.length > 0) && (
        <div className="event-metadata">
          {characters.length > 0 && (
            <div className="event-characters">
              <strong>Characters:</strong>
              <div className="tag-list">
                {characters.map((char, index) => (
                  <span key={`char-${index}`} className="character-tag">
                    {typeof char === 'string' ? char : char.name}
                  </span>
                ))}
              </div>
            </div>
          )}

          {locations.length > 0 && (
            <div className="event-locations">
              <strong>Locations:</strong>
              <div className="tag-list">
                {locations.map((loc, index) => (
                  <span key={`loc-${index}`} className="location-tag">
                    {typeof loc === 'string' ? loc : loc.name}
                  </span>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default EventCard;

import React, { useState, useEffect } from 'react';
// We don't need useNavigate since BookRequiredWrapper handles navigation
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragOverlay
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  useSortable,
  verticalListSortingStrategy
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import './PlotPage.css';

// Sortable Chapter component
const SortableChapter = ({
  chapter,
  index,
  isSelected,
  isEditing,
  onChapterSelect,
  startEditingChapter,
  onDeleteChapter,
  editChapterTitle,
  setEditChapterTitle,
  saveChapterEdit,
  setEditingChapterId,
  events,
  setShowNewEventForm,
  setShowAiPromptForm,
  activeDroppableId,
  // Event-related props to pass down to SortableEvent
  onEventSelect,
  startEditingEvent,
  onDeleteEvent,
  editEventTitle,
  editEventDescription,
  setEditEventTitle,
  setEditEventDescription,
  saveEventEdit,
  setEditingEventId,
  editingEventId,
  selectedEvent,
  onAssignEventToChapter,
  chapters,
  bookId
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging
  } = useSortable({ id: chapter.id, data: { type: 'chapter' } });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  // Get events for this chapter
  const chapterEvents = events.filter(event => event.chapter_id === chapter.id);

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`chapter-container ${isSelected ? 'selected' : ''} ${isDragging ? 'dragging' : ''}`}
    >
      <div className="chapter-header">
        <div className="chapter-drag-handle" {...attributes} {...listeners}>
          <span className="drag-icon">↕</span>
        </div>
        <div className="chapter-header-content" onClick={() => !isEditing && onChapterSelect(chapter.id)}>
          {isEditing ? (
            <div className="chapter-edit-form">
              <input
                type="text"
                value={editChapterTitle}
                onChange={(e) => setEditChapterTitle(e.target.value)}
                placeholder="Chapter title"
              />
              <div className="chapter-edit-actions">
                <button onClick={saveChapterEdit}>Save</button>
                <button onClick={() => setEditingChapterId(null)}>Cancel</button>
              </div>
            </div>
          ) : (
            <>
              <h3>{chapter.title}</h3>
              <div className="chapter-actions">
                <button onClick={() => startEditingChapter(chapter)}>Edit</button>
                <button onClick={() => {
                  if (bookId && chapter.id) {
                    console.log('[DEBUG] Deleting chapter:', { bookId, chapterId: chapter.id });
                    onDeleteChapter({ bookId, chapterId: chapter.id });
                  } else {
                    console.error('[DEBUG] Missing required IDs for chapter deletion:', { bookId, chapterId: chapter.id });
                  }
                }}>Delete</button>
              </div>
            </>
          )}
        </div>
      </div>

      <div className={`chapter-events ${activeDroppableId === chapter.id ? 'drop-active drop-over' : ''}`}>
        {chapterEvents.length > 0 ? (
          <SortableContext
            items={chapterEvents.map(event => event.id)}
            strategy={verticalListSortingStrategy}
            id={chapter.id}
          >
            {chapterEvents.map((event) => (
              <SortableEvent
                key={event.id}
                event={event}
                containerId={chapter.id}
                onEventSelect={onEventSelect}
                startEditingEvent={startEditingEvent}
                onDeleteEvent={onDeleteEvent}
                editEventTitle={editEventTitle}
                editEventDescription={editEventDescription}
                setEditEventTitle={setEditEventTitle}
                setEditEventDescription={setEditEventDescription}
                saveEventEdit={saveEventEdit}
                setEditingEventId={setEditingEventId}
                editingEventId={editingEventId}
                selectedEvent={selectedEvent}
                onAssignEventToChapter={onAssignEventToChapter}
                chapters={chapters}
                bookId={bookId}
              />
            ))}
          </SortableContext>
        ) : (
          <div className="empty-chapter">No events in this chapter</div>
        )}
      </div>

      {isSelected && (
        <div className="chapter-footer">
          <button onClick={() => setShowNewEventForm(true)}>Add Event</button>
          <button onClick={() => setShowAiPromptForm(true)}>Generate with AI</button>
        </div>
      )}
    </div>
  );
};

// Sortable Event component
const SortableEvent = ({ event, containerId, onEventSelect, startEditingEvent, onDeleteEvent, editEventTitle, editEventDescription, setEditEventTitle, setEditEventDescription, saveEventEdit, setEditingEventId, editingEventId, selectedEvent, onAssignEventToChapter, chapters, bookId }) => {
  // Debug log to check if metadata is being passed properly
  console.log('[DEBUG] Event in SortableEvent:', {
    id: event.id,
    title: event.title,
    characters: event.characters,
    locations: event.locations,
    type: event.type
  });
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging
  } = useSortable({
    id: event.id,
    data: {
      type: 'event',
      containerId: containerId
    }
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  const isEditing = editingEventId === event.id;
  const isSelected = selectedEvent && selectedEvent.id === event.id;

  // Define color scheme based on event type (matching Brainstorm page)
  const nodeTypeColors = {
    idea: '#D8E2C8', // beige
    character: '#F5C4B4', // light green
    location: '#F8F0E3', // light blue
    event: '#C7E4E2', // light red
    default: '#E8C9BF' // light gray
  };

  // Get background color based on event type
  const backgroundColor = event.type ? nodeTypeColors[event.type.toLowerCase()] || nodeTypeColors.default : nodeTypeColors.default;

  // Merge styles
  const cardStyle = {
    ...style,
    backgroundColor: backgroundColor,
    borderColor: isSelected ? '#ff0072' : '#ccc',
  };

  console.log('[DEBUG] Event card data:', {
    id: event.id,
    title: event.title,
    type: event.type,
    characters: event.characters,
    locations: event.locations,
    backgroundColor
  });

  // COMPREHENSIVE DEBUG INFO
  console.log(`
  =====================================================
  🔍 DETAILED EVENT CARD DEBUG INFO 🔍
  =====================================================
  Event ID: ${event.id}
  Title: ${event.title}
  Description: ${event.description}
  Type: ${event.type}
  Chapter ID: ${event.chapter_id || 'None (Unassigned)'}
  Source Node ID: ${event.source_node_id || 'N/A'}

  Characters (${Array.isArray(event.characters) ? event.characters.length : 'N/A'}):
  ${Array.isArray(event.characters) && event.characters.length > 0
    ? event.characters.map(char => `    - ${typeof char === 'string' ? char : JSON.stringify(char)}`).join('\n')
    : '    None'}

  Locations (${Array.isArray(event.locations) ? event.locations.length : 'N/A'}):
  ${Array.isArray(event.locations) && event.locations.length > 0
    ? event.locations.map(loc => `    - ${typeof loc === 'string' ? loc : JSON.stringify(loc)}`).join('\n')
    : '    None'}

  Raw Characters Data: ${JSON.stringify(event.characters)}
  Raw Locations Data: ${JSON.stringify(event.locations)}

  Full Event Object: ${JSON.stringify(event, null, 2)}
  =====================================================
  `);

  // Also log as object for easier inspection in browser console
  console.log('[DEBUG] Event full data:', {
    ...event,
    charactersInfo: {
      type: Array.isArray(event.characters) ? 'array' : typeof event.characters,
      length: Array.isArray(event.characters) ? event.characters.length : 'N/A',
      content: event.characters
    },
    locationsInfo: {
      type: Array.isArray(event.locations) ? 'array' : typeof event.locations,
      length: Array.isArray(event.locations) ? event.locations.length : 'N/A',
      content: event.locations
    }
  });

  return (
    <div
      ref={setNodeRef}
      style={cardStyle}
      className={`event-card ${isSelected ? 'selected' : ''} ${isDragging ? 'dragging' : ''}`}
      onClick={() => !isEditing && onEventSelect && onEventSelect(event.id)}
    >
      {isEditing ? (
        <div className="event-edit-form">
          <input
            type="text"
            value={editEventTitle}
            onChange={(e) => setEditEventTitle(e.target.value)}
            placeholder="Event title"
          />
          <textarea
            value={editEventDescription}
            onChange={(e) => setEditEventDescription(e.target.value)}
            placeholder="Event description"
          />
          <div className="event-edit-actions">
            <button onClick={saveEventEdit}>Save</button>
            <button onClick={() => setEditingEventId(null)}>Cancel</button>
          </div>
        </div>
      ) : (
        <>
          <div className="event-header">
            <div className="event-drag-handle" {...attributes} {...listeners}>
              <span className="drag-icon">↕</span>
            </div>
            <div className="event-header-content">
              <div className="event-title-row">
                <h4>{event.title}</h4>
                {event.type && (
                  <span
                    className="event-type-badge"
                    style={{
                      backgroundColor: nodeTypeColors[event.type.toLowerCase()] || nodeTypeColors.default,
                      border: `1px solid ${event.type ? 'rgba(0,0,0,0.1)' : '#ccc'}`
                    }}
                  >
                    {event.type}
                  </span>
                )}
              </div>
              <div className="event-actions">
                <button onClick={() => startEditingEvent && startEditingEvent(event)}>Edit</button>
                <button onClick={() => {
                  if (onDeleteEvent && bookId && event.id) {
                    console.log('[DEBUG] Deleting event:', { bookId, eventId: event.id });
                    onDeleteEvent({ bookId, eventId: event.id });
                  } else {
                    console.error('[DEBUG] Missing required IDs for event deletion:', { bookId, eventId: event.id });
                  }
                }}>Delete</button>
                {/* Dropdown assignment box removed as requested */}
                {/* Unassign button removed as requested */}
              </div>
            </div>
          </div>
          <p>{event.description}</p>

          {/* Display characters, locations, and type if available */}
          <div className="event-metadata" style={{ marginTop: '10px' }}>
            {event.characters && event.characters.length > 0 && (
              <div className="event-characters" style={{ marginBottom: '8px' }}>
                <strong style={{ display: 'block', marginBottom: '4px', fontSize: '0.85em', color: '#555' }}>Characters:</strong>
                <div className="tag-list" style={{ display: 'flex', flexWrap: 'wrap', gap: '4px' }}>
                  {event.characters.map((char, index) => (
                    <span
                      key={`char-${index}`}
                      className="character-tag"
                      style={{
                        backgroundColor: '#e3f2fd',
                        color: '#0d47a1',
                        padding: '2px 6px',
                        borderRadius: '4px',
                        fontSize: '0.85em',
                        border: '1px solid rgba(13, 71, 161, 0.2)'
                      }}
                    >
                      {typeof char === 'string' ? char : char.name}
                    </span>
                  ))}
                </div>
              </div>
            )}

            {event.locations && event.locations.length > 0 && (
              <div className="event-locations" style={{ marginBottom: '8px' }}>
                <strong style={{ display: 'block', marginBottom: '4px', fontSize: '0.85em', color: '#555' }}>Locations:</strong>
                <div className="tag-list" style={{ display: 'flex', flexWrap: 'wrap', gap: '4px' }}>
                  {event.locations.map((loc, index) => (
                    <span
                      key={`loc-${index}`}
                      className="location-tag"
                      style={{
                        backgroundColor: '#e8f5e9',
                        color: '#1b5e20',
                        padding: '2px 6px',
                        borderRadius: '4px',
                        fontSize: '0.85em',
                        border: '1px solid rgba(27, 94, 32, 0.2)'
                      }}
                    >
                      {typeof loc === 'string' ? loc : loc.name}
                    </span>
                  ))}
                </div>
              </div>
            )}

            {event.type && (
              <div className="event-type-tag" style={{ marginBottom: '4px' }}>
                <strong style={{ fontSize: '0.85em', color: '#555' }}>Type:</strong>
                <span style={{
                  backgroundColor: nodeTypeColors[event.type.toLowerCase()] || nodeTypeColors.default,
                  color: '#333',
                  padding: '2px 6px',
                  borderRadius: '4px',
                  fontSize: '0.85em',
                  marginLeft: '4px',
                  border: '1px solid rgba(0, 0, 0, 0.1)'
                }}>
                  {event.type}
                </span>
              </div>
            )}
          </div>
        </>
      )}
    </div>
  );
};

const PlotPageDndKit = ({
  // Book data
  bookId,
  bookTitle,
  chapters,
  events,
  selectedChapter,
  selectedEvent,
  error,
  isLoading,
  // Actions
  onFetchChapters,
  onFetchEvents,
  onAddChapter,
  onUpdateChapter,
  onDeleteChapter,
  onReorderChapters,
  onAssignEventToChapter,
  onAddEventToChapter,
  onGenerateAIEvent,
  onClearSelectedEvent,
  onClearSelectedChapter,
  onClearError,
  onImportFromBrainstorm,
  // Form state passed from container
  showNewChapterForm,
  setShowNewChapterForm,
  newChapterTitle,
  setNewChapterTitle,
  showNewEventForm,
  setShowNewEventForm,
  newEventTitle,
  setNewEventTitle,
  newEventDescription,
  setNewEventDescription,
  showAiPromptForm,
  setShowAiPromptForm,
  aiPrompt,
  setAiPrompt,
  // Event actions
  onAddEvent,
  onUpdateEvent,
  onDeleteEvent,
  onEventSelect,
  onChapterSelect,
}) => {

  // Local state for editing events and chapters
  const [editingEventId, setEditingEventId] = useState(null);
  const [editingChapterId, setEditingChapterId] = useState(null);
  const [editEventTitle, setEditEventTitle] = useState('');
  const [editEventDescription, setEditEventDescription] = useState('');
  const [editChapterTitle, setEditChapterTitle] = useState('');
  const [activeId, setActiveId] = useState(null);
  const [activeItem, setActiveItem] = useState(null);

  // Set up sensors for drag and drop
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Fetch chapters and events when component mounts or bookId changes
  useEffect(() => {
    console.log('[DEBUG] PlotPageDndKit: useEffect triggered with bookId:', bookId);
    if (bookId) {
      console.log('[DEBUG] PlotPageDndKit: Fetching data for book:', bookId);
      onFetchChapters(bookId);
      onFetchEvents(bookId);
    } else {
      console.log('[DEBUG] PlotPageDndKit: No bookId available, not navigating');
      // We'll let the BookRequiredWrapper handle the navigation
    }
  }, [bookId, onFetchChapters, onFetchEvents]);

  // Start editing a chapter
  const startEditingChapter = (chapter) => {
    setEditingChapterId(chapter.id);
    setEditChapterTitle(chapter.title);
  };

  // Save chapter edit
  const saveChapterEdit = () => {
    if (editingChapterId && editChapterTitle.trim()) {
      onUpdateChapter(editingChapterId, { title: editChapterTitle });
      setEditingChapterId(null);
      setEditChapterTitle('');
    }
  };

  // Start editing an event
  const startEditingEvent = (event) => {
    setEditingEventId(event.id);
    setEditEventTitle(event.title);
    setEditEventDescription(event.description);
  };

  // Save event edit
  const saveEventEdit = () => {
    if (editingEventId && editEventTitle.trim()) {
      onUpdateEvent(editingEventId, {
        title: editEventTitle,
        description: editEventDescription,
      });
      setEditingEventId(null);
      setEditEventTitle('');
      setEditEventDescription('');
    }
  };

  // Add a new chapter
  const addChapter = () => {
    if (newChapterTitle.trim()) {
      console.log('[DEBUG] Adding chapter with bookId:', bookId);
      console.log('[DEBUG] Chapter title:', newChapterTitle);

      // Calculate the next sequence number based on existing chapters
      const nextSequenceNumber = chapters && chapters.length > 0
        ? Math.max(...chapters.map(ch => ch.sequence_number || 0)) + 1
        : 1;

      console.log('[DEBUG] Next sequence number:', nextSequenceNumber);

      // Create the proper chapter data object with sequence_number
      const chapterData = {
        title: newChapterTitle,
        sequence_number: nextSequenceNumber
      };

      // Call onAddChapter with the correct parameters
      onAddChapter({ bookId, chapterData });

      setNewChapterTitle('');
      setShowNewChapterForm(false);
    }
  };

  // Add a new event
  const addEvent = () => {
    if (newEventTitle.trim()) {
      const chapterId = selectedChapter ? selectedChapter.id : null;
      onAddEvent(bookId, {
        title: newEventTitle,
        description: newEventDescription,
        chapter_id: chapterId,
      });
      setNewEventTitle('');
      setNewEventDescription('');
      setShowNewEventForm(false);
    }
  };

  // Generate an AI event
  const generateAIEvent = () => {
    if (aiPrompt.trim() && selectedChapter) {
      onGenerateAIEvent(bookId, selectedChapter.id, aiPrompt);
      setAiPrompt('');
      setShowAiPromptForm(false);
    }
  };

  // Get unassigned events
  const unassignedEvents = events.filter(event => !event.chapter_id);

  // State for tracking drag over status
  const [activeDroppableId, setActiveDroppableId] = useState(null);

  // Handle drag start
  const handleDragStart = (event) => {
    const { active } = event;
    setActiveId(active.id);

    // Find the active item (chapter or event)
    if (active.data.current.type === 'chapter') {
      const chapter = chapters.find(ch => ch.id === active.id);
      setActiveItem({ ...chapter, type: 'chapter' });
    } else if (active.data.current.type === 'event') {
      const eventItem = events.find(ev => ev.id === active.id);
      setActiveItem({ ...eventItem, type: 'event' });
    }
  };

  // Handle drag over
  const handleDragOver = (event) => {
    const { active, over } = event;

    if (!over) {
      setActiveDroppableId(null);
      return;
    }

    // Only highlight when dragging events
    if (active.data.current.type !== 'event') {
      return;
    }

    // Determine the droppable container ID
    let containerId;
    if (over.data.current?.type === 'chapter') {
      containerId = over.id;
    } else if (over.data.current?.type === 'event') {
      containerId = over.data.current.containerId;
    } else if (over.id === 'unassigned') {
      containerId = 'unassigned';
    }

    setActiveDroppableId(containerId);
  };

  // Handle drag end
  const handleDragEnd = (event) => {
    const { active, over } = event;

    if (!over) {
      setActiveId(null);
      setActiveItem(null);
      setActiveDroppableId(null);
      return;
    }

    // Handle chapter reordering
    if (active.data.current.type === 'chapter' && over.data.current?.type === 'chapter') {
      const oldIndex = chapters.findIndex(chapter => chapter.id === active.id);
      const newIndex = chapters.findIndex(chapter => chapter.id === over.id);

      if (oldIndex !== newIndex) {
        console.log('[DEBUG] Reordering chapters:', { oldIndex, newIndex, bookId });

        if (bookId) {
          const newChapterOrder = arrayMove(chapters, oldIndex, newIndex);
          onReorderChapters({
            bookId,
            chapters: newChapterOrder.map((chapter, index) => ({
              ...chapter,
              sequence_number: index + 1
            }))
          });
        } else {
          console.error('[DEBUG] Missing bookId for chapter reordering');
        }
      }
    }

    // Handle event moving between containers
    if (active.data.current.type === 'event') {
      const eventId = active.id;
      const sourceContainerId = active.data.current.containerId;
      const destinationContainerId = over.data.current?.type === 'chapter'
        ? over.id
        : (over.data.current?.containerId || (over.id === 'unassigned' ? '' : over.id));

      // If moving to a different container
      if (sourceContainerId !== destinationContainerId) {
        console.log('[DEBUG] Moving event', eventId, 'from', sourceContainerId, 'to', destinationContainerId);
        console.log('[DEBUG] Book ID:', bookId);

        // Make sure we have all the required IDs
        if (bookId && eventId) {
          console.log('[DEBUG] Calling onAssignEventToChapter with:', {
            bookId,
            eventId,
            chapterId: destinationContainerId === 'unassigned' ? '' : destinationContainerId
          });

          // Get the original event data before assignment
          const originalEvent = events.find(e => e.id === eventId);
          console.log('[DEBUG] Original event before assignment:', originalEvent);

          // Create a promise to track when the assignment is complete
          onAssignEventToChapter({
            bookId,
            eventId,
            chapterId: destinationContainerId === 'unassigned' ? '' : destinationContainerId
          })
          .then(resultAction => {
            console.log('[DEBUG] Assignment completed with result:', resultAction);

            if (resultAction.type.endsWith('/fulfilled')) {
              // Get the updated event data from the result
              const { updatedEvent } = resultAction.payload;
              console.log('[DEBUG] Updated event from API response:', updatedEvent);

              // Force a re-render by setting state
              setActiveId(null);
              setActiveItem(null);
              setActiveDroppableId(null);

              // Force a refresh of the events list to ensure we have the latest data
              if (typeof onFetchEvents === 'function') {
                onFetchEvents(bookId);
              }
            }
          })
          .catch(error => {
            console.error('[DEBUG] Error during assignment:', error);
            setActiveId(null);
            setActiveItem(null);
            setActiveDroppableId(null);
          });

          // Return early to prevent the immediate clearing of active state
          return;
        } else {
          console.error('[DEBUG] Missing required IDs for event assignment:', {
            bookId,
            eventId,
            destinationContainerId
          });
        }
      }
      // If reordering within the same container, we could implement that here
    }

    setActiveId(null);
    setActiveItem(null);
    setActiveDroppableId(null);
  };

  // Render error message
  const renderError = () => (
    <div className="error-message">
      <p>{error}</p>
      <button onClick={onClearError}>Dismiss</button>
    </div>
  );

  // Render new chapter form
  const renderNewChapterForm = () => (
    <div className="modal">
      <div className="modal-content">
        <h2>Add New Chapter</h2>
        <input
          type="text"
          value={newChapterTitle}
          onChange={(e) => setNewChapterTitle(e.target.value)}
          placeholder="Chapter title"
        />
        <div className="modal-actions">
          <button onClick={addChapter}>Add</button>
          <button onClick={() => setShowNewChapterForm(false)}>Cancel</button>
        </div>
      </div>
    </div>
  );

  // Render new event form
  const renderNewEventForm = () => (
    <div className="modal">
      <div className="modal-content">
        <h2>Add New Event</h2>
        <input
          type="text"
          value={newEventTitle}
          onChange={(e) => setNewEventTitle(e.target.value)}
          placeholder="Event title"
        />
        <textarea
          value={newEventDescription}
          onChange={(e) => setNewEventDescription(e.target.value)}
          placeholder="Event description"
        />
        <div className="modal-actions">
          <button onClick={addEvent}>Add</button>
          <button onClick={() => setShowNewEventForm(false)}>Cancel</button>
        </div>
      </div>
    </div>
  );

  // Render AI prompt form
  const renderAiPromptForm = () => (
    <div className="modal">
      <div className="modal-content">
        <h2>Generate Event with AI</h2>
        <textarea
          value={aiPrompt}
          onChange={(e) => setAiPrompt(e.target.value)}
          placeholder="Describe the event you want to generate..."
        />
        <div className="modal-actions">
          <button onClick={generateAIEvent}>Generate</button>
          <button onClick={() => setShowAiPromptForm(false)}>Cancel</button>
        </div>
      </div>
    </div>
  );

  // Render unassigned events
  const renderUnassignedEvents = () => (
    <div className="unassigned-events-container" data-testid="unassigned-container">
      <h3>Unassigned Events</h3>
      <div className={`unassigned-events ${activeDroppableId === 'unassigned' ? 'drop-active drop-over' : ''}`} data-testid="unassigned-droppable">
        {unassignedEvents.length > 0 ? (
          <SortableContext
            items={unassignedEvents.map(event => event.id)}
            strategy={verticalListSortingStrategy}
            id="unassigned"
          >
            {unassignedEvents.map((event) => (
              <SortableEvent
                key={event.id}
                event={event}
                containerId="unassigned"
                onEventSelect={onEventSelect}
                startEditingEvent={startEditingEvent}
                onDeleteEvent={onDeleteEvent}
                editEventTitle={editEventTitle}
                editEventDescription={editEventDescription}
                setEditEventTitle={setEditEventTitle}
                setEditEventDescription={setEditEventDescription}
                saveEventEdit={saveEventEdit}
                setEditingEventId={setEditingEventId}
                editingEventId={editingEventId}
                selectedEvent={selectedEvent}
                onAssignEventToChapter={onAssignEventToChapter}
                chapters={chapters}
                bookId={bookId}
              />
            ))}
          </SortableContext>
        ) : (
          <div className="empty-unassigned">No unassigned events</div>
        )}
      </div>
      <div className="unassigned-footer">
        {/* Buttons removed as requested */}
      </div>
    </div>
  );

  // Debug log to check events data
  console.log('[DEBUG] All events in PlotPageDndKit:', events.map(event => ({
    id: event.id,
    title: event.title,
    characters: event.characters,
    locations: event.locations,
    type: event.type
  })));

  return (
    <div className="plot-page">
      <div className="plot-header">
        <h1>{bookTitle} - Plot</h1>
        <div className="plot-actions">
          <button onClick={() => setShowNewChapterForm(true)}>Add Chapter</button>
          {/* Debug button commented out as requested
          <button
            className="debug-button"
            onClick={() => console.log('[DEBUG] Current book state:', { chapters, events })}
          >
            Debug Book State
          </button>
          */}
        </div>
      </div>

      {error && renderError()}

      {isLoading ? (
        <div className="loading">Loading plot data...</div>
      ) : (
        <DndContext
          sensors={sensors}
          collisionDetection={closestCenter}
          onDragStart={handleDragStart}
          onDragOver={handleDragOver}
          onDragEnd={handleDragEnd}
        >
          <div className="plot-content">
            <div className="chapters-container">
              <SortableContext
                items={chapters.map(chapter => chapter.id)}
                strategy={verticalListSortingStrategy}
              >
                {chapters.length > 0 ? (
                  chapters.map((chapter, index) => (
                    <SortableChapter
                      key={chapter.id}
                      chapter={chapter}
                      index={index}
                      isSelected={selectedChapter && selectedChapter.id === chapter.id}
                      isEditing={editingChapterId === chapter.id}
                      onChapterSelect={onChapterSelect}
                      startEditingChapter={startEditingChapter}
                      onDeleteChapter={onDeleteChapter}
                      editChapterTitle={editChapterTitle}
                      setEditChapterTitle={setEditChapterTitle}
                      saveChapterEdit={saveChapterEdit}
                      setEditingChapterId={setEditingChapterId}
                      events={events}
                      setShowNewEventForm={setShowNewEventForm}
                      setShowAiPromptForm={setShowAiPromptForm}
                      activeDroppableId={activeDroppableId}
                      // Event-related props to pass down to SortableEvent
                      onEventSelect={onEventSelect}
                      startEditingEvent={startEditingEvent}
                      onDeleteEvent={onDeleteEvent}
                      editEventTitle={editEventTitle}
                      editEventDescription={editEventDescription}
                      setEditEventTitle={setEditEventTitle}
                      setEditEventDescription={setEditEventDescription}
                      saveEventEdit={saveEventEdit}
                      setEditingEventId={setEditingEventId}
                      editingEventId={editingEventId}
                      selectedEvent={selectedEvent}
                      onAssignEventToChapter={onAssignEventToChapter}
                      chapters={chapters}
                      bookId={bookId}
                    />
                  ))
                ) : (
                  <div className="empty-chapters">No chapters yet. Create your first chapter to get started.</div>
                )}
              </SortableContext>
            </div>

            {renderUnassignedEvents()}
          </div>

          <DragOverlay>
            {activeId && activeItem && (
              activeItem.type === 'chapter' ? (
                <div className="chapter-container dragging">
                  <div className="chapter-header">
                    <h3>{activeItem.title}</h3>
                  </div>
                </div>
              ) : (
                <div className="event-card dragging">
                  <div className="event-header">
                    <h4>{activeItem.title}</h4>
                  </div>
                  <p>{activeItem.description}</p>
                </div>
              )
            )}
          </DragOverlay>
        </DndContext>
      )}

      {showNewChapterForm && renderNewChapterForm()}
      {showNewEventForm && renderNewEventForm()}
      {showAiPromptForm && renderAiPromptForm()}
    </div>
  );
};

export default PlotPageDndKit;

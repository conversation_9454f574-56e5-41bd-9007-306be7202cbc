// frontend/src/components/ThemeProvider.js
import React, { useEffect } from 'react';
import { useSelector } from 'react-redux';
import { selectTheme } from '../redux/slices/authSlice';
import '../styles/themes.css';

/**
 * ThemeProvider component that applies the selected theme to the application
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components
 * @returns {JSX.Element} ThemeProvider component
 */
const ThemeProvider = ({ children }) => {
  const theme = useSelector(selectTheme);
  
  useEffect(() => {
    // Apply the theme class to the root element
    document.documentElement.className = `theme-${theme}`;
    
    // Also update the meta theme-color for mobile browsers
    const metaThemeColor = document.querySelector('meta[name="theme-color"]');
    if (metaThemeColor) {
      const themeColors = {
        light: '#FFFFFF',
        dark: '#121212',
        sepia: '#F5EFE0'
      };
      metaThemeColor.setAttribute('content', themeColors[theme] || themeColors.light);
    }
    
    console.debug('ThemeProvider: Applied theme', theme);
  }, [theme]);
  
  return children;
};

export default ThemeProvider;

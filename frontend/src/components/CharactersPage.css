/* frontend/src/components/CharactersPage.css */
.characters-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  height: calc(100vh - 60px); /* Adjust for header height */
  overflow-y: auto;
  overflow-x: hidden;
}

.characters-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.characters-header h2 {
  margin: 0;
  color: #2d3748;
  font-size: 1.8rem;
}

.characters-actions {
  display: flex;
  align-items: center;
  gap: 15px;
}

.view-toggle {
  display: flex;
  background-color: #f0f4f8;
  border-radius: 6px;
  overflow: hidden;
}

.view-toggle-button {
  background: none;
  border: none;
  padding: 8px 12px;
  cursor: pointer;
  font-size: 1.1rem;
  color: #4a5568;
  transition: background-color 0.2s ease;
}

.view-toggle-button.active {
  background-color: #4a90e2;
  color: white;
}

.view-icon {
  font-size: 1.2rem;
}

.create-character-button {
  background-color: #4a90e2;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 10px 15px;
  font-size: 0.95rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
  display: flex;
  align-items: center;
  gap: 5px;
}

.create-character-button:hover {
  background-color: #3a7bc8;
}

.create-character-button::before {
  content: "+";
  font-size: 1.2rem;
  font-weight: bold;
}

.characters-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50px 0;
  color: #4a5568;
}

.spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top: 4px solid #4a90e2;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.no-characters {
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 40px;
  text-align: center;
  margin-bottom: 30px;
}

.no-characters-icon {
  font-size: 4rem;
  margin-bottom: 15px;
  color: #a0aec0;
}

.no-characters h3 {
  margin: 0 0 10px;
  color: #2d3748;
  font-size: 1.5rem;
}

.no-characters p {
  margin: 0 0 25px;
  color: #718096;
}

.no-characters-actions {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.create-button {
  background-color: #4a90e2;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 12px 20px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.create-button:hover {
  background-color: #3a7bc8;
}

.or-divider {
  color: #a0aec0;
  font-size: 0.9rem;
  position: relative;
  width: 100%;
  text-align: center;
  margin: 10px 0;
}

.or-divider::before,
.or-divider::after {
  content: "";
  position: absolute;
  top: 50%;
  width: 40%;
  height: 1px;
  background-color: #e2e8f0;
}

.or-divider::before {
  left: 0;
}

.or-divider::after {
  right: 0;
}

.ai-character-generator {
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 30px;
}

.ai-character-generator h3 {
  margin: 0 0 10px;
  color: #2d3748;
  font-size: 1.3rem;
}

.ai-character-generator p {
  margin: 0 0 15px;
  color: #718096;
}

.generator-form {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.generator-form textarea {
  padding: 12px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 0.95rem;
  resize: vertical;
  transition: border-color 0.2s ease;
}

.generator-form textarea:focus {
  border-color: #4a90e2;
  outline: none;
  box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
}

.generator-info {
  background-color: #f8fafc;
  border-radius: 6px;
  padding: 12px 15px;
  margin-bottom: 5px;
}

.generator-info p {
  margin: 0 0 8px;
  font-size: 0.9rem;
  color: #4a5568;
}

.generator-info ul {
  margin: 0;
  padding-left: 20px;
}

.generator-info li {
  font-size: 0.85rem;
  color: #4a5568;
  margin-bottom: 4px;
}

.generator-suggestions {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.generator-suggestions p {
  margin: 0;
  font-size: 0.9rem;
  font-weight: 600;
  color: #4a5568;
}

.suggestion-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.suggestion-buttons button {
  background-color: #f0f4f8;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 0.85rem;
  color: #4a5568;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.suggestion-buttons button:hover {
  background-color: #e2e8f0;
}

.generate-button {
  background-color: #4a90e2;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
  align-self: flex-end;
}

.generate-button:hover:not(:disabled) {
  background-color: #3a7bc8;
}

.generate-button:disabled {
  background-color: #a0aec0;
  cursor: not-allowed;
}

.character-comparison {
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin: 30px 0;
}

.comparison-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.comparison-header h3 {
  margin: 0;
  color: #2d3748;
  font-size: 1.3rem;
}

.clear-comparison-button {
  background-color: #e2e8f0;
  color: #4a5568;
  border: none;
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.clear-comparison-button:hover {
  background-color: #cbd5e0;
}

.comparison-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.comparison-character {
  background-color: #f8fafc;
  border-radius: 8px;
  overflow: hidden;
}

.comparison-character-header {
  background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
  color: white;
  padding: 15px;
}

.comparison-character-header h4 {
  margin: 0 0 5px;
  font-size: 1.2rem;
}

.comparison-role {
  font-size: 0.9rem;
  opacity: 0.9;
}

.comparison-character-content {
  padding: 15px;
  max-height: 500px;
  overflow-y: auto;
  scrollbar-width: thin;
}

.comparison-main {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
}

.comparison-headshot {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.comparison-headshot img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.comparison-headshot.placeholder {
  background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 2rem;
  font-weight: bold;
}

.comparison-details {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.comparison-item {
  font-size: 0.9rem;
  color: #4a5568;
}

.comparison-label {
  font-weight: 600;
  color: #4a5568;
}

.comparison-section {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #e2e8f0;
}

.comparison-section p {
  margin: 5px 0 0;
  font-size: 0.9rem;
  color: #4a5568;
  line-height: 1.5;
}

.comparison-traits {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  margin-top: 5px;
}

.comparison-trait {
  background-color: #e2e8f0;
  border-radius: 12px;
  color: #4a5568;
  font-size: 0.8rem;
  padding: 3px 10px;
}

.comparison-relationships {
  margin: 5px 0 0;
  padding-left: 20px;
  font-size: 0.9rem;
  color: #4a5568;
}

.comparison-relationships li {
  margin-bottom: 10px;
}

.comparison-relationship-strength {
  margin-top: 4px;
  margin-bottom: 4px;
  display: flex;
  flex-direction: column;
  width: 100%;
}

.comparison-relationship-strength .strength-label {
  font-size: 0.85rem;
  color: #4a5568;
  margin-bottom: 3px;
}

.comparison-relationship-strength .strength-bar {
  height: 4px;
  background-color: #e2e8f0;
  border-radius: 2px;
  overflow: hidden;
  width: 100%;
}

.comparison-relationship-strength .strength-fill {
  height: 100%;
  background-color: #4CAF50;
  border-radius: 2px;
  transition: width 0.3s ease;
}

.comparison-relationship-description {
  margin-top: 4px;
  font-size: 0.85rem;
  color: #718096;
  font-style: italic;
  padding-left: 10px;
  border-left: 2px solid #e2e8f0;
  line-height: 1.4;
}

.character-editor-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.ai-generator-section {
  margin-top: 40px;
  border-top: 1px solid #e2e8f0;
  padding-top: 30px;
}

.ai-generator-section h3 {
  margin: 0 0 20px;
  color: #2d3748;
  font-size: 1.3rem;
  text-align: center;
}

/* AI Character Generation Loading Overlay */
.ai-generation-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1100; /* Higher than character editor overlay */
}

.ai-generation-modal {
  background-color: white;
  border-radius: 8px;
  padding: 30px;
  text-align: center;
  max-width: 400px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  animation: fadeIn 0.3s ease;
}

.ai-generation-modal h3 {
  margin: 15px 0 10px;
  color: #2d3748;
  font-size: 1.4rem;
}

.ai-generation-modal p {
  margin: 0 0 5px;
  color: #4a5568;
}

.ai-generation-modal .generation-tip {
  color: #718096;
  font-size: 0.9rem;
  font-style: italic;
  margin-top: 15px;
}

.loading-spinner-small {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s linear infinite;
  margin-right: 8px;
  vertical-align: middle;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

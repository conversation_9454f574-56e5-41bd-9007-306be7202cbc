// frontend/src/components/Character3DRelationshipGraph.js
import React, { useEffect, useRef } from 'react';
import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';
import './Character3DRelationshipGraph.css';
import { BASE_URL } from '../utils/apiConfig';

/**
 * Character3DRelationshipGraph component for visualizing character relationships in 3D
 * @param {Object} props - Component props
 * @returns {JSX.Element} Character3DRelationshipGraph UI
 */
const Character3DRelationshipGraph = ({ characters, onSelectCharacter }) => {
  const containerRef = useRef(null);
  const rendererRef = useRef(null);
  const sceneRef = useRef(null);
  const cameraRef = useRef(null);
  const controlsRef = useRef(null);
  const characterNodesRef = useRef({});
  const relationshipLinesRef = useRef([]);
  const frameIdRef = useRef(null);

  // Process characters and their relationships
  const processRelationships = () => {
    const nodes = characters.map(char => {
      return {
        id: char.id,
        name: char.name,
        headshot: char.headshot,
        position: new THREE.Vector3(0, 0, 0) // Will be set in layout
      };
    });

    const links = [];

    // Create links from relationships
    characters.forEach(char => {
      // Check if relationships exists and is an array
      if (char.relationships && Array.isArray(char.relationships) && char.relationships.length > 0) {
        char.relationships.forEach(rel => {
          // Handle different relationship formats
          const relName = rel.name || rel.relatedCharacterName;
          const relType = rel.type || rel.relationshipType || 'other';
          const strength = rel.strength !== undefined ? parseInt(rel.strength, 10) : 3; // Default to 3 if not specified

          // Find the related character
          const relatedChar = relName ? characters.find(c => c.name === relName) : null;

          if (relatedChar) {
            links.push({
              source: char.id,
              target: relatedChar.id,
              type: relType,
              strength: strength
            });
          }
        });
      }
    });

    return { nodes, links };
  };

  // Initialize the 3D scene
  const initScene = () => {
    if (!containerRef.current) return;

    // Create scene
    const scene = new THREE.Scene();
    scene.background = new THREE.Color(0xf8fafc);
    sceneRef.current = scene;

    // Create camera
    const camera = new THREE.PerspectiveCamera(
      60,
      containerRef.current.clientWidth / containerRef.current.clientHeight,
      0.1,
      1000
    );
    camera.position.z = 15;
    cameraRef.current = camera;

    // Create renderer
    const renderer = new THREE.WebGLRenderer({ antialias: true });
    renderer.setSize(
      containerRef.current.clientWidth,
      containerRef.current.clientHeight
    );
    containerRef.current.appendChild(renderer.domElement);
    rendererRef.current = renderer;

    // Add orbit controls
    const controls = new OrbitControls(camera, renderer.domElement);
    controls.enableDamping = true;
    controls.dampingFactor = 0.25;
    controlsRef.current = controls;

    // Add lights
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
    scene.add(ambientLight);

    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(10, 10, 10);
    scene.add(directionalLight);

    // Process relationships
    const { nodes, links } = processRelationships();

    // Position nodes in a circle
    const radius = Math.max(5, nodes.length * 0.8);
    nodes.forEach((node, i) => {
      const angle = (i / nodes.length) * 2 * Math.PI;
      node.position.x = radius * Math.cos(angle);
      node.position.y = radius * Math.sin(angle);
      node.position.z = 0;
    });

    // Create character nodes
    nodes.forEach(node => {
      // Create node sphere
      const geometry = new THREE.SphereGeometry(1, 32, 32);
      const material = new THREE.MeshStandardMaterial({ color: 0x4a90e2 });
      const mesh = new THREE.Mesh(geometry, material);
      mesh.position.copy(node.position);
      mesh.userData = { id: node.id, type: 'character' };
      scene.add(mesh);

      // Create character headshot texture
      if (node.headshot && node.headshot !== 'generating') {
        const textureLoader = new THREE.TextureLoader();
        const headshot = node.headshot.startsWith('http')
          ? node.headshot
          : `${BASE_URL}${node.headshot}`;

        textureLoader.load(
          headshot,
          (texture) => {
            material.map = texture;
            material.needsUpdate = true;
          },
          undefined,
          (error) => {
            console.error('Error loading headshot texture:', error);
          }
        );
      }

      // Create text label
      const canvas = document.createElement('canvas');
      canvas.width = 256;
      canvas.height = 64;
      const context = canvas.getContext('2d');
      context.fillStyle = 'white';
      context.fillRect(0, 0, canvas.width, canvas.height);
      context.font = 'bold 24px Arial';
      context.fillStyle = 'black';
      context.textAlign = 'center';
      context.textBaseline = 'middle';
      context.fillText(node.name, canvas.width / 2, canvas.height / 2);

      const texture = new THREE.CanvasTexture(canvas);
      const spriteMaterial = new THREE.SpriteMaterial({ map: texture });
      const sprite = new THREE.Sprite(spriteMaterial);
      sprite.position.copy(node.position);
      sprite.position.y -= 1.5;
      sprite.scale.set(3, 0.75, 1);
      scene.add(sprite);

      // Store node reference
      characterNodesRef.current[node.id] = {
        mesh,
        sprite,
        position: node.position.clone()
      };
    });

    // Create relationship lines
    links.forEach(link => {
      const sourceNode = characterNodesRef.current[link.source];
      const targetNode = characterNodesRef.current[link.target];

      if (sourceNode && targetNode) {
        // Create line geometry
        const points = [
          sourceNode.position.clone(),
          targetNode.position.clone()
        ];
        const geometry = new THREE.BufferGeometry().setFromPoints(points);

        // Set line color based on relationship type
        const color = getColorForRelationType(link.type);

        // Set line style based on relationship strength
        const lineWidth = getLineWidthForStrength(link.strength);
        const dashSize = getDashSizeForStrength(link.strength);
        const gapSize = getGapSizeForStrength(link.strength);

        // Create line material
        const material = new THREE.LineDashedMaterial({
          color: color,
          linewidth: lineWidth, // Note: linewidth only works in WebGL 2.0
          dashSize: dashSize,
          gapSize: gapSize,
          scale: 1 // Scale of the dashes
        });

        // Create line
        const line = new THREE.Line(geometry, material);
        line.computeLineDistances(); // Required for dashed lines
        scene.add(line);

        // Create relationship label
        const canvas = document.createElement('canvas');
        canvas.width = 256;
        canvas.height = 48;
        const context = canvas.getContext('2d');
        context.fillStyle = 'white';
        context.fillRect(0, 0, canvas.width, canvas.height);
        context.font = '16px Arial';
        context.fillStyle = 'black';
        context.textAlign = 'center';
        context.textBaseline = 'middle';

        // Add relationship type and strength label
        const strengthLabel = getStrengthLabel(link.strength);
        context.fillText(`${link.type} (${strengthLabel})`, canvas.width / 2, canvas.height / 2);

        const texture = new THREE.CanvasTexture(canvas);
        const spriteMaterial = new THREE.SpriteMaterial({ map: texture });
        const sprite = new THREE.Sprite(spriteMaterial);

        // Position the label at the midpoint of the line
        const midpoint = new THREE.Vector3().addVectors(
          sourceNode.position,
          targetNode.position
        ).multiplyScalar(0.5);

        sprite.position.copy(midpoint);
        sprite.scale.set(4, 1, 1);
        scene.add(sprite);

        // Store line reference
        relationshipLinesRef.current.push({
          line,
          sprite,
          source: link.source,
          target: link.target,
          strength: link.strength
        });
      }
    });

    // Add raycaster for interaction
    const raycaster = new THREE.Raycaster();
    const mouse = new THREE.Vector2();

    const handleClick = (event) => {
      // Calculate mouse position in normalized device coordinates
      const rect = renderer.domElement.getBoundingClientRect();
      mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
      mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;

      // Update the picking ray with the camera and mouse position
      raycaster.setFromCamera(mouse, camera);

      // Calculate objects intersecting the picking ray
      const intersects = raycaster.intersectObjects(scene.children);

      // Check if we hit a character node
      for (let i = 0; i < intersects.length; i++) {
        const object = intersects[i].object;
        if (object.userData && object.userData.type === 'character') {
          const character = characters.find(c => c.id === object.userData.id);
          if (character && onSelectCharacter) {
            onSelectCharacter(character);
          }
          break;
        }
      }
    };

    renderer.domElement.addEventListener('click', handleClick);

    // Animation loop
    const animate = () => {
      frameIdRef.current = requestAnimationFrame(animate);

      // Update controls
      if (controlsRef.current) {
        controlsRef.current.update();
      }

      // Render scene
      renderer.render(scene, camera);
    };

    // Start animation
    animate();

    // Cleanup function
    return () => {
      if (frameIdRef.current) {
        cancelAnimationFrame(frameIdRef.current);
      }

      renderer.domElement.removeEventListener('click', handleClick);

      if (containerRef.current && rendererRef.current) {
        containerRef.current.removeChild(rendererRef.current.domElement);
      }

      // Dispose of Three.js objects
      Object.values(characterNodesRef.current).forEach(node => {
        node.mesh.geometry.dispose();
        node.mesh.material.dispose();
        node.sprite.material.dispose();
      });

      relationshipLinesRef.current.forEach(link => {
        link.line.geometry.dispose();
        link.line.material.dispose();
        link.sprite.material.dispose();
      });
    };
  };

  // Helper function to get color for relationship type
  const getColorForRelationType = (type) => {
    const colorMap = {
      friend: 0x4CAF50,    // Green
      enemy: 0xF44336,     // Red
      family: 0x2196F3,    // Blue
      lover: 0xE91E63,     // Pink
      mentor: 0x9C27B0,    // Purple
      student: 0x673AB7,   // Deep Purple
      colleague: 0x00BCD4, // Cyan
      rival: 0xFF5722,     // Deep Orange
      other: 0x9E9E9E      // Gray
    };

    return colorMap[type.toLowerCase()] || colorMap.other;
  };

  // Helper function to get a label for relationship strength
  const getStrengthLabel = (strength) => {
    switch (parseInt(strength, 10)) {
      case 0: return "Enemy";
      case 1: return "Acquaintance";
      case 2: return "Casual friend";
      case 3: return "Close friend";
      case 4: return "Deep bond";
      case 5: return "Intimate connection";
      default: return "Close friend";
    }
  };

  // Helper function to get line width based on relationship strength
  const getLineWidthForStrength = (strength) => {
    // Scale: 0-5 to line width
    return 1 + (strength * 0.4); // 1 to 3 range
  };

  // Helper function to get dash size based on relationship strength
  const getDashSizeForStrength = (strength) => {
    // For enemies (0), use dotted line
    if (strength === 0) return 0.1;

    // For acquaintances (1), use dashed line
    if (strength === 1) return 0.3;

    // For all others (2-5), use solid line
    return 1000; // Very large value to make it appear solid
  };

  // Helper function to get gap size based on relationship strength
  const getGapSizeForStrength = (strength) => {
    // For enemies (0), use dotted line with small gaps
    if (strength === 0) return 0.1;

    // For acquaintances (1), use dashed line with medium gaps
    if (strength === 1) return 0.3;

    // For all others (2-5), use solid line (no gaps)
    return 0; // No gaps
  };

  // Initialize scene on component mount
  useEffect(() => {
    const cleanup = initScene();

    // Handle window resize
    const handleResize = () => {
      if (
        containerRef.current &&
        rendererRef.current &&
        cameraRef.current
      ) {
        const width = containerRef.current.clientWidth;
        const height = containerRef.current.clientHeight;

        rendererRef.current.setSize(width, height);
        cameraRef.current.aspect = width / height;
        cameraRef.current.updateProjectionMatrix();
      }
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      if (cleanup) cleanup();
    };
  }, [characters, onSelectCharacter]);

  if (characters.length === 0) {
    return (
      <div className="relationship-graph-empty">
        <p>No characters available to display relationships.</p>
      </div>
    );
  }

  return (
    <div className="character-relationship-graph">
      <h3>Character Relationship Network</h3>
      <div className="graph-container" ref={containerRef}></div>
      <div className="relationship-legend">
        <div className="legend-item">
          <span className="legend-color" style={{ backgroundColor: '#4CAF50' }}></span>
          <span>Friend</span>
        </div>
        <div className="legend-item">
          <span className="legend-color" style={{ backgroundColor: '#F44336' }}></span>
          <span>Enemy</span>
        </div>
        <div className="legend-item">
          <span className="legend-color" style={{ backgroundColor: '#2196F3' }}></span>
          <span>Family</span>
        </div>
        <div className="legend-item">
          <span className="legend-color" style={{ backgroundColor: '#E91E63' }}></span>
          <span>Lover</span>
        </div>
        <div className="legend-item">
          <span className="legend-color" style={{ backgroundColor: '#9C27B0' }}></span>
          <span>Mentor</span>
        </div>
        <div className="legend-item">
          <span className="legend-color" style={{ backgroundColor: '#673AB7' }}></span>
          <span>Student</span>
        </div>
      </div>
      <div className="strength-legend">
        <h4>Relationship Strength</h4>
        <div className="strength-item">
          <span className="strength-line" style={{ height: '1px', borderTop: '1px dotted #333' }}></span>
          <span>0: Enemy, hostile connection</span>
        </div>
        <div className="strength-item">
          <span className="strength-line" style={{ height: '1px', borderTop: '1px dashed #333' }}></span>
          <span>1: Acquaintance, minimal interaction</span>
        </div>
        <div className="strength-item">
          <span className="strength-line" style={{ height: '1px', borderTop: '1px solid #333' }}></span>
          <span>2: Casual friend, occasional contact</span>
        </div>
        <div className="strength-item">
          <span className="strength-line" style={{ height: '2px', backgroundColor: '#333' }}></span>
          <span>3: Close friend, regular interaction</span>
        </div>
        <div className="strength-item">
          <span className="strength-line" style={{ height: '3px', backgroundColor: '#333' }}></span>
          <span>4: Deep bond, trust, frequent support</span>
        </div>
        <div className="strength-item">
          <span className="strength-line" style={{ height: '4px', backgroundColor: '#333' }}></span>
          <span>5: Intimate, profound connection</span>
        </div>
      </div>
      <p className="graph-help">Click on a character to edit their details. Drag to rotate, scroll to zoom.</p>
    </div>
  );
};

export default Character3DRelationshipGraph;

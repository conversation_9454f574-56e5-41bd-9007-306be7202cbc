// frontend/src/components/BookDetailsPage.js
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useBookStats } from '../hooks/useBookStats';
import './BookDetailsPage.css';

/**
 * BookDetailsPage component for displaying detailed information about a book
 * @param {Object} props - Component props
 * @returns {JSX.Element} BookDetailsPage UI
 */
const BookDetailsPage = ({
  book,
  metadata,
  isLoading,
  error,
  onNavigateToWorld,
  onNavigateToCharacters,
  onNavigateToPlot,
  onNavigateToBrainstorm,
  onNavigateToWrite,
  onClearError,
  onUpdateBook
}) => {
  const navigate = useNavigate();

  // State for editing mode
  const [isEditing, setIsEditing] = useState(false);
  const [editedBook, setEditedBook] = useState({
    title: '',
    author: '',
    genre: '',
    otherGenre: '',
    description: ''
  });

  // Get book statistics using our custom hook
  // Always call hooks at the top level, even if we don't use the result immediately
  const bookStats = useBookStats(book?.book_id);

  // Extract stats and loading state from the hook result
  const { isLoading: statsLoading, ...stats } = bookStats;

  // Initialize form when entering edit mode
  const handleEditClick = () => {
    // Check if the book's genre is one of our predefined options
    const predefinedGenres = ['fantasy', 'sci-fi', 'mystery', 'romance', 'thriller', 'horror', 'historical', 'contemporary', 'non-fiction'];
    const isOtherGenre = book.genre && !predefinedGenres.includes(book.genre.toLowerCase());

    setEditedBook({
      title: book.title || '',
      author: book.author || '',
      genre: isOtherGenre ? 'other' : (book.genre || ''),
      otherGenre: isOtherGenre ? book.genre : '',
      description: book.description || ''
    });
    setIsEditing(true);
  };

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setEditedBook(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Get the effective genre (either selected genre or otherGenre if "other" is selected)
  const getEffectiveGenre = () => {
    return editedBook.genre === 'other' && editedBook.otherGenre ? editedBook.otherGenre : editedBook.genre;
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();
    // Use the effective genre in the submitted data
    const submittedData = {
      ...editedBook,
      genre: getEffectiveGenre()
    };
    onUpdateBook(book.book_id, submittedData);
    setIsEditing(false);
  };

  // Cancel editing
  const handleCancel = () => {
    setIsEditing(false);
  };

  if (isLoading) {
    return (
      <div className="book-details-container">
        <div className="loading-indicator">Loading book details...</div>
      </div>
    );
  }

  // If no book is selected, show a message
  if (!book) {
    return (
      <div className="book-details-container">
        <div className="no-book-selected">
          <h2>No Book Selected</h2>
          <p>Please select a book to view its details.</p>
          <button onClick={() => navigate('/books')}>Go to Books</button>
        </div>
      </div>
    );
  }

  // If book data is incomplete, show a loading message
  if (!book.title || typeof book.author === 'undefined' || typeof book.genre === 'undefined') {
    return (
      <div className="book-details-container">
        <div className="loading-indicator">
          <h2>Loading Book Details</h2>
          <p>The complete book data is being loaded. Please wait a moment...</p>
        </div>
      </div>
    );
  }

  console.log('BookDetailsPage: Book ID:', book?.book_id);
  console.log('BookDetailsPage: Stats from hook:', stats);
  console.log('BookDetailsPage: Stats loading:', statsLoading);

  return (
    <div className="book-details-container">
      {error && (
        <div className="error-message">
          <p>{error}</p>
          <button onClick={onClearError}>Dismiss</button>
        </div>
      )}

      {isEditing ? (
        <div className="book-edit-form">
          <form onSubmit={handleSubmit}>
            <div className="form-group">
              <label htmlFor="title">Title</label>
              <input
                type="text"
                id="title"
                name="title"
                value={editedBook.title}
                onChange={handleInputChange}
                required
              />
            </div>

            <div className="form-group">
              <label htmlFor="author">Author</label>
              <input
                type="text"
                id="author"
                name="author"
                value={editedBook.author}
                onChange={handleInputChange}
              />
            </div>

            <div className="form-group">
              <label htmlFor="genre">Genre</label>
              <select
                id="genre"
                name="genre"
                value={editedBook.genre}
                onChange={handleInputChange}
                required
              >
                <option value="">Select Genre</option>
                <option value="fantasy">Fantasy</option>
                <option value="sci-fi">Science Fiction</option>
                <option value="mystery">Mystery</option>
                <option value="romance">Romance</option>
                <option value="thriller">Thriller</option>
                <option value="horror">Horror</option>
                <option value="historical">Historical</option>
                <option value="contemporary">Contemporary</option>
                <option value="non-fiction">Non-Fiction</option>
                <option value="other">Other</option>
              </select>
            </div>

            {editedBook.genre === 'other' && (
              <div className="form-group">
                <label htmlFor="otherGenre">Specify Genre</label>
                <input
                  type="text"
                  id="otherGenre"
                  name="otherGenre"
                  value={editedBook.otherGenre}
                  onChange={handleInputChange}
                  placeholder="Enter custom genre"
                  required
                />
              </div>
            )}

            <div className="form-group">
              <label htmlFor="description">Synopsis</label>
              <textarea
                id="description"
                name="description"
                value={editedBook.description}
                onChange={handleInputChange}
                rows="4"
              />
            </div>

            <div className="form-actions">
              <button type="submit" className="save-button">Save</button>
              <button type="button" className="cancel-button" onClick={handleCancel}>Cancel</button>
            </div>
          </form>
        </div>
      ) : (
        <>
          <div className="book-header">
            <div className="book-info">
              <h1>{book.title}</h1>
              {book.author && <p className="book-author">by {book.author}</p>}
              {book.genre && <span className="book-genre">{book.genre}</span>}
            </div>

            <div className="book-actions">
              <button
                className="edit-button"
                onClick={handleEditClick}
              >
                Edit
              </button>
              <button
                className="write-button"
                onClick={onNavigateToWrite}
              >
                Write
              </button>
            </div>
          </div>

          {book.description && (
            <div className="book-description">
              <h3>Synopsis</h3>
              <p>{book.description}</p>
            </div>
          )}
        </>
      )}

      <div className="book-stats">
        <h3>Book Statistics</h3>
        <div className="stats-grid">
          <div className="stat-item">
            <span className="stat-value">{isLoading || statsLoading ? '...' : (stats.wordCount || 0).toLocaleString()}</span>
            <span className="stat-label">Words</span>
          </div>
          <div className="stat-item">
            <span className="stat-value">{isLoading || statsLoading ? '...' : stats.chapterCount}</span>
            <span className="stat-label">Chapters</span>
          </div>
          <div className="stat-item">
            <span className="stat-value">{isLoading || statsLoading ? '...' : stats.characterCount}</span>
            <span className="stat-label">Characters</span>
          </div>
          <div className="stat-item">
            <span className="stat-value">{isLoading || statsLoading ? '...' : stats.locationCount}</span>
            <span className="stat-label">World Elements</span>
          </div>
          <div className="stat-item">
            <span className="stat-value">{isLoading || statsLoading ? '...' : stats.lastEdited}</span>
            <span className="stat-label">Last Edited</span>
          </div>
        </div>
      </div>

      <div className="book-navigation">
        <h3>Book Navigation</h3>
        <div className="navigation-grid">
          <div className="navigation-item" onClick={onNavigateToWorld}>
            <div className="navigation-icon world-icon">🌍</div>
            <h4>World Building</h4>
            <p>Develop your world, locations, and systems</p>
          </div>
          <div className="navigation-item" onClick={onNavigateToCharacters}>
            <div className="navigation-icon character-icon">👤</div>
            <h4>Characters</h4>
            <p>Create and manage your characters</p>
          </div>
          <div className="navigation-item" onClick={onNavigateToPlot}>
            <div className="navigation-icon plot-icon">📋</div>
            <h4>Plot</h4>
            <p>Organize your story's plot and chapters</p>
          </div>
          <div className="navigation-item" onClick={onNavigateToBrainstorm}>
            <div className="navigation-icon brainstorm-icon">💡</div>
            <h4>Brainstorm</h4>
            <p>Generate and organize ideas</p>
          </div>
          <div className="navigation-item" onClick={onNavigateToWrite}>
            <div className="navigation-icon write-icon">✍️</div>
            <h4>Write</h4>
            <p>Start writing your book</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BookDetailsPage;

/* frontend/src/components/CharacterCard.css */
.character-card {
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  overflow: hidden;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
}

.character-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.character-card.compared {
  border: 2px solid #4a90e2;
  box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.3);
}

.character-card-header {
  background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
  color: white;
  padding: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.character-card-header h3 {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.character-card-actions {
  display: flex;
  gap: 8px;
}

.character-card-actions button {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 4px;
  color: white;
  cursor: pointer;
  font-size: 0.9rem;
  height: 28px;
  width: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.2s ease;
}

.character-card-actions button:hover {
  background: rgba(255, 255, 255, 0.4);
}

.compare-button.active {
  background: rgba(255, 255, 255, 0.5);
}

.character-card-content {
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  flex: 1;
}

.character-card-main {
  display: flex;
  gap: 15px;
}

.character-headshot {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.character-headshot img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.character-headshot.placeholder {
  background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.character-headshot.placeholder span {
  color: white;
  font-size: 2rem;
  font-weight: bold;
}

/* Headshot generating state */
.character-headshot .headshot-generating {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f0f4f8;
  border-radius: 50%;
}

.character-headshot .headshot-loading-spinner {
  border: 3px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top: 3px solid #4a90e2;
  width: 20px;
  height: 20px;
  animation: spin 1s linear infinite;
  margin-bottom: 5px;
}

.character-headshot .headshot-generating p {
  font-size: 0.7rem;
  color: #4a5568;
  margin: 0;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.character-info {
  display: flex;
  flex-direction: column;
  gap: 5px;
  flex: 1;
}

.character-role, .character-age, .character-race {
  font-size: 0.9rem;
}

.character-description {
  margin-top: 5px;
}

.character-description p {
  margin: 5px 0 0;
  font-size: 0.9rem;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.character-traits {
  margin-top: 5px;
}

.traits-list {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  margin-top: 5px;
}

.trait-tag {
  background-color: #f0f4f8;
  border-radius: 12px;
  color: #4a5568;
  font-size: 0.8rem;
  padding: 3px 10px;
}

.character-relationships,
.character-backstory,
.character-arc {
  margin-top: 5px;
}

.character-backstory p,
.character-arc p {
  margin: 5px 0 0;
  font-size: 0.9rem;
  line-height: 1.4;
  display: block;
  width: 100%;
  clear: both;
  overflow: hidden;
  text-overflow: ellipsis;
}

.relationship-list {
  margin: 5px 0 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.relationship-item {
  background-color: #f8f9fa;
  border-radius: 6px;
  padding: 6px 10px;
}

.relationship-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.relationship-name {
  font-weight: 600;
  font-size: 0.85rem;
  color: #4a5568;
}

.relationship-type {
  font-size: 0.8rem;
  color: #718096;
}

.relationship-strength {
  margin-top: 5px;
  display: flex;
  flex-direction: column;
  width: 100%;
}

.strength-label {
  font-size: 0.8rem;
  color: #4a5568;
  margin-bottom: 3px;
}

.strength-bar {
  height: 4px;
  background-color: #e2e8f0;
  border-radius: 2px;
  overflow: hidden;
  width: 100%;
}

.strength-fill {
  height: 100%;
  background-color: #4CAF50;
  border-radius: 2px;
  transition: width 0.3s ease;
}

.relationship-description {
  margin-top: 5px;
  font-size: 0.8rem;
  color: #718096;
  font-style: italic;
  line-height: 1.3;
  display: block;
  width: 100%;
  clear: both;
  padding-left: 5px;
  border-left: 2px solid #e2e8f0;
}

.character-backstory,
.character-arc {
  border-top: 1px solid #f0f0f0;
  padding-top: 8px;
  clear: both;
  position: relative;
  margin-top: 12px;
  width: 100%;
  display: block;
}

.character-arc {
  margin-top: 15px;
}

.label {
  font-weight: 600;
  color: #4a5568;
  font-size: 0.85rem;
}

// frontend/src/components/ErrorBoundary.js
import React from 'react';
import * as Sentry from '@sentry/react';

/**
 * ErrorBoundary component to catch and handle rendering errors
 */
class ErrorBoundary extends React.Component {
  state = { hasError: false, error: null };

  /**
   * Updates state when an error is caught
   * @param {Error} error - The caught error
   * @returns {Object} New state
   */
  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  /**
   * Logs error details to console and Sentry
   * @param {Error} error - The caught error
   * @param {Object} errorInfo - Additional error info (e.g., stack trace)
   */
  componentDidCatch(error, errorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    Sentry.captureException(error, { extra: errorInfo });
  }

  /**
   * Renders fallback UI on error, otherwise renders children
   * @returns {JSX.Element} Rendered content
   */
  render() {
    if (this.state.hasError) {
      return (
        <div style={{ padding: '20px', textAlign: 'center' }}>
          <h1>Something went wrong.</h1>
          <p>{this.state.error?.message || 'An unexpected error occurred.'}</p>
          <button
            onClick={() => {
              this.setState({ hasError: false, error: null });
              console.debug('ErrorBoundary reset');
            }}
            style={{ padding: '10px 20px', marginTop: '10px' }}
          >
            Try Again
          </button>
        </div>
      );
    }
    return this.props.children;
  }
}

export default ErrorBoundary;
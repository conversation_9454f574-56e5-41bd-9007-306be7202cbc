// frontend/src/components/CharacterSelector.js
import React, { useState, useEffect } from 'react';

/**
 * Component for selecting characters from a list or adding new ones
 * @param {Object} props - Component props
 * @param {Array} props.characters - List of available characters
 * @param {Array} props.selectedCharacters - List of currently selected characters
 * @param {Function} props.onChange - Function to call when selection changes
 * @param {Function} props.onCreateCharacter - Function to call when creating a new character
 * @returns {JSX.Element} Character selector component
 */
const CharacterSelector = ({ characters, selectedCharacters = [], onChange, onCreateCharacter }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [newCharacter, setNewCharacter] = useState('');
  const [showAddForm, setShowAddForm] = useState(false);

  // Log characters for debugging
  console.log('Available characters for selection:', characters);

  console.log('CharacterSelector - received characters:', characters);

  // Filter characters based on search term
  const filteredCharacters = characters && characters.length > 0 ? characters.filter(character =>
    character && character.name && character.name.toLowerCase().includes(searchTerm.toLowerCase())
  ) : [];

  // Handle character selection
  const handleCharacterSelect = (character) => {
    const characterName = typeof character === 'string' ? character : character.name;

    // Check if character is already selected
    if (selectedCharacters.includes(characterName)) {
      // Remove character if already selected
      onChange(selectedCharacters.filter(name => name !== characterName));
    } else {
      // Add character if not already selected
      onChange([...selectedCharacters, characterName]);
    }
  };

  // Handle adding a new character
  const handleAddCharacter = async (e) => {
    // Prevent default only if this is an event from a form element
    if (e && e.preventDefault) {
      e.preventDefault();
    }

    if (newCharacter.trim()) {
      const characterName = newCharacter.trim();
      console.log('Adding new character:', characterName);

      // If onCreateCharacter is provided, call it to create the character in the database
      if (onCreateCharacter) {
        try {
          // Create the character and get the result
          const result = await onCreateCharacter(characterName);
          console.log('Character created:', result);

          // Select the new character
          handleCharacterSelect(characterName);
        } catch (error) {
          console.error('Failed to create character:', error);
          // Still select the character even if creation failed
          handleCharacterSelect(characterName);
        }
      } else {
        // Just select the character if no creation function is provided
        handleCharacterSelect(characterName);
      }

      // Reset the form
      setNewCharacter('');
      setShowAddForm(false);
    }
  };

  return (
    <div className="character-selector">
      <div className="search-container" style={{ marginBottom: '10px' }}>
        <input
          type="text"
          placeholder="Search characters..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          style={{
            width: '100%',
            padding: '8px 12px',
            borderRadius: '4px',
            border: '1px solid #ced4da',
            fontSize: '14px'
          }}
        />
      </div>

      {/* Character list */}
      <div className="character-list" style={{
        maxHeight: '150px',
        overflowY: 'auto',
        border: '1px solid #ced4da',
        borderRadius: '4px',
        marginBottom: '10px'
      }}>
        {filteredCharacters.length > 0 ? (
          filteredCharacters.map(character => (
            <div
              key={character.id || character.name}
              className={`character-item ${selectedCharacters.includes(character.name) ? 'selected' : ''}`}
              onClick={() => handleCharacterSelect(character)}
              style={{
                padding: '6px 8px',
                cursor: 'pointer',
                borderBottom: '1px solid #eee',
                backgroundColor: selectedCharacters.includes(character.name) ? '#e3f2fd' : 'transparent',
                display: 'flex',
                alignItems: 'center',
                width: '100%',
                justifyContent: 'flex-start'
              }}
            >
              <input
                type="checkbox"
                checked={selectedCharacters.includes(character.name)}
                onChange={() => {}} // Handled by the div click
                style={{
                  marginRight: '8px',
                  flexShrink: 0,
                  width: '16px',
                  height: '16px'
                }}
              />
              <div style={{
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
                display: 'flex',
                flexDirection: 'column',
                flex: 1,
                textAlign: 'left'
              }}>
                <span style={{ fontWeight: '500' }}>{character.name}</span>
                {character.role && (
                  <span style={{ fontSize: '0.8em', color: '#6c757d' }}>
                    {character.role}
                  </span>
                )}
              </div>
            </div>
          ))
        ) : (
          <div style={{ padding: '8px', color: '#6c757d', textAlign: 'center', width: '100%' }}>
            {searchTerm ? 'No characters found' : 'No characters available'}
          </div>
        )}
      </div>

      {/* Selected characters */}
      {selectedCharacters.length > 0 && (
        <div className="selected-characters" style={{ marginBottom: '10px' }}>
          <div style={{ fontWeight: 'bold', marginBottom: '5px' }}>Selected:</div>
          <div style={{ display: 'flex', flexWrap: 'wrap', gap: '5px' }}>
            {selectedCharacters.map(name => (
              <div
                key={name}
                className="selected-tag"
                style={{
                  backgroundColor: '#e3f2fd',
                  color: '#0d47a1',
                  padding: '4px 8px',
                  borderRadius: '4px',
                  fontSize: '0.85em',
                  display: 'flex',
                  alignItems: 'center',
                  border: '1px solid rgba(13, 71, 161, 0.2)',
                  margin: '2px'
                }}
              >
                <span style={{ marginRight: '4px' }}>{name}</span>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleCharacterSelect(name);
                  }}
                  style={{
                    background: 'none',
                    border: 'none',
                    cursor: 'pointer',
                    marginLeft: '2px',
                    fontSize: '14px',
                    fontWeight: 'bold',
                    color: '#0d47a1',
                    padding: '0 2px',
                    lineHeight: '14px'
                  }}
                  aria-label="Remove"
                >
                  ×
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Add new character input */}
      {showAddForm ? (
        <div style={{ marginTop: '10px' }}>
          <div style={{ display: 'flex', gap: '5px' }}>
            <input
              type="text"
              placeholder="New character name"
              value={newCharacter}
              onChange={(e) => setNewCharacter(e.target.value)}
              style={{
                flex: 1,
                padding: '8px',
                borderRadius: '4px',
                border: '1px solid #ced4da'
              }}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault();
                  handleAddCharacter(e);
                }
              }}
            />
            <button
              type="button"
              onClick={handleAddCharacter}
              style={{
                padding: '8px 16px',
                borderRadius: '4px',
                border: 'none',
                background: '#0d6efd',
                color: 'white',
                cursor: 'pointer'
              }}
            >
              Add
            </button>
            <button
              type="button"
              onClick={() => setShowAddForm(false)}
              className="cancel-button"
            >
              Cancel
            </button>
          </div>
        </div>
      ) : (
        <button
          onClick={() => setShowAddForm(true)}
          style={{
            padding: '8px 16px',
            borderRadius: '4px',
            border: '1px solid #0d6efd',
            background: 'transparent',
            color: '#0d6efd',
            cursor: 'pointer',
            width: '100%'
          }}
        >
          + Add New Character (will be created in Characters page)
        </button>
      )}
    </div>
  );
};

export default CharacterSelector;

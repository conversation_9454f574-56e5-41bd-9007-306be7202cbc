// frontend/src/components/TemplateFormModal.js
import React, { useState, useEffect } from 'react';
import './TemplateFormModal.css';
import { getHeaders } from '../services/apiService';
import { BASE_URL } from '../utils/apiConfig';

/**
 * Modal for creating a new element from a template
 * @param {Object} props - Component props
 * @returns {JSX.Element} TemplateFormModal UI
 */
const TemplateFormModal = ({ template, onClose, onSubmit }) => {
  // State for form data
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    tags: '',
    importance: 'medium'
  });

  // State for custom fields
  const [customFields, setCustomFields] = useState({});

  // State for relationship fields
  const [relationshipFields, setRelationshipFields] = useState({});

  // State for available related elements (for dropdowns)
  const [relatedElements, setRelatedElements] = useState({});

  // State for loading
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Extract field definitions from template and remove duplicates with standard fields
  const rawFieldDefinitions = template?.field_definitions?.fields || [];
  const fieldDefinitions = rawFieldDefinitions.filter(field =>
    !['name', 'description', 'tags', 'importance'].includes(field.id)
  );

  // Extract relationship definitions from template
  const relationshipDefinitions = template?.relationship_definitions?.valid_relationships || [];

  // Helper function to format field names
  const formatFieldName = (fieldId) => {
    // Handle null or undefined values
    if (!fieldId) return 'Unknown Field';

    // Convert snake_case to Title Case
    return fieldId
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ')
      .replace(/Id$|Ids$/, '') // Remove trailing "Id" or "Ids"
      .trim();
  };

  // Check if a field is an ID field that should be a dropdown
  const isIdField = (fieldId) => {
    // Handle null or undefined values
    if (!fieldId) return false;

    return fieldId.endsWith('_id') ||
           fieldId.endsWith('_ids') ||
           fieldId.includes('_id_');
  };

  // Get template type from ID field
  const getTemplateTypeFromIdField = (fieldId) => {
    // Handle null or undefined values
    if (!fieldId) return null;

    // Extract the entity type from the field ID
    // e.g., planet_id -> planet, region_ids -> region
    const parts = fieldId.split('_');
    for (let i = 0; i < parts.length; i++) {
      if (parts[i] === 'id' || parts[i] === 'ids') {
        return parts[i-1]; // Return the part before "id" or "ids"
      }
    }
    return null;
  };

  // Fetch elements for relationship fields and ID fields
  useEffect(() => {
    // Skip if template is not provided
    if (!template) {
      return;
    }

    // Create a flag to track if the component is still mounted
    let isMounted = true;

    // Create an AbortController to cancel fetch requests if needed
    const controller = new AbortController();
    const signal = controller.signal;

    const fetchRelatedElements = async () => {
      // Skip if already loading
      if (loading) {
        return;
      }

      // Set loading state only if component is still mounted
      if (isMounted) {
        setLoading(true);
      }

      const headers = getHeaders();
      const elementsMap = {};

      try {
        // Get the current book ID from localStorage
        let bookId = localStorage.getItem('currentBookId');

        // If not found in localStorage, try to get it from the URL
        if (!bookId) {
          const urlPath = window.location.pathname;
          const bookIdMatch = urlPath.match(/\/books\/([^\/]+)/);
          if (bookIdMatch && bookIdMatch[1]) {
            bookId = bookIdMatch[1];
            console.log('TemplateFormModal - Retrieved book ID from URL:', bookId);
            // Save it to localStorage for future use
            localStorage.setItem('currentBookId', bookId);
          }
        }

        if (!bookId) {
          console.error('No current book ID found in localStorage or URL');
          if (isMounted) {
            setError('No current book selected. Please select a book first or refresh the page.');
            setLoading(false);
          }
          return;
        }

        // Collect all template types we need to fetch
        const templateTypes = new Set();

        // Add relationship template types
        if (relationshipDefinitions && relationshipDefinitions.length > 0) {
          for (const relationship of relationshipDefinitions) {
            if (relationship.target_template) {
              templateTypes.add(relationship.target_template);
            }
          }
        }

        // Add ID field template types
        if (fieldDefinitions && fieldDefinitions.length > 0) {
          for (const field of fieldDefinitions) {
            if (isIdField(field.id)) {
              const templateType = getTemplateTypeFromIdField(field.id);
              if (templateType) {
                templateTypes.add(templateType);
              }
            }
          }
        }

        // Log the template types we're going to fetch
        console.log('TemplateFormModal - Fetching elements for template types:', Array.from(templateTypes));

        // Fetch elements for each template type
        for (const templateType of templateTypes) {
          // Skip if the component has been unmounted
          if (!isMounted) {
            return;
          }

          try {
            // Use the updated API endpoint with template_id filter
            const response = await fetch(
              `${BASE_URL}/books/${bookId}/world-building/elements?template_id=${templateType}`,
              {
                headers,
                signal // Add the abort signal to the fetch request
              }
            );

            if (!response.ok) {
              console.warn(`Failed to fetch elements for template ${templateType}: ${response.status}`);
              continue;
            }

            const data = await response.json();
            elementsMap[templateType] = data;
          } catch (err) {
            // Ignore aborted fetch errors
            if (err.name === 'AbortError') {
              console.log(`Fetch for template ${templateType} was aborted`);
              continue;
            }

            console.error(`Error fetching elements for template ${templateType}:`, err);
          }
        }

        // Update state only if the component is still mounted
        if (isMounted) {
          setRelatedElements(elementsMap);
        }
      } catch (err) {
        console.error('Error fetching related elements:', err);
        if (isMounted) {
          setError('Failed to load related elements. Some dropdown options may be missing.');
        }
      } finally {
        // Update loading state only if the component is still mounted
        if (isMounted) {
          setLoading(false);
        }
      }
    };

    // Call the fetch function
    fetchRelatedElements();

    // Cleanup function to run when the component unmounts or the effect runs again
    return () => {
      isMounted = false;
      controller.abort(); // Abort any in-progress fetch requests
    };
  }, [template, loading]); // Only depend on the template prop and loading state

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle custom field changes
  const handleCustomFieldChange = (fieldId, value) => {
    setCustomFields(prev => ({
      ...prev,
      [fieldId]: value
    }));
  };

  // Handle relationship field changes
  const handleRelationshipChange = (relationshipId, elementId) => {
    setRelationshipFields(prev => ({
      ...prev,
      [relationshipId]: elementId
    }));
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();

    // Process tags
    const tags = formData.tags
      .split(',')
      .map(tag => tag.trim())
      .filter(tag => tag.length > 0);

    // Create element data
    const elementData = {
      name: formData.name,
      description: formData.description,
      tags,
      importance: formData.importance,
      template_id: template.template_id,
      template_version: template.version || '1.0',
      element_type: template.template_id, // Using template_id as element_type
      category: template.category_id, // Add category from template
      custom_fields: { ...customFields } // Include custom fields
    };

    // Process relationships
    const relationships = [];

    // Add relationships from relationship fields
    Object.entries(relationshipFields).forEach(([relationshipId, elementId]) => {
      if (elementId) { // Only add if an element was selected
        const relationship = relationshipDefinitions.find(r =>
          r.target_template === relationshipId ||
          r.relationship_type === relationshipId
        );

        if (relationship) {
          relationships.push({
            target_element_id: elementId,
            relationship_type: relationship.relationship_type || 'related_to',
            description: relationship.description || ''
          });
        }
      }
    });

    // Add relationships from ID fields in custom fields
    Object.entries(customFields).forEach(([fieldId, value]) => {
      if (isIdField(fieldId) && value) {
        // Extract the entity type from the field ID
        const entityType = getTemplateTypeFromIdField(fieldId);
        if (entityType) {
          // Determine relationship type based on field name
          let relationshipType = 'related_to';
          if (fieldId.includes('parent')) {
            relationshipType = 'belongs_to';
          } else if (fieldId.includes('child')) {
            relationshipType = 'contains';
          } else if (fieldId.includes('location')) {
            relationshipType = 'located_in';
          }

          relationships.push({
            target_element_id: value,
            relationship_type: relationshipType,
            description: `${formatFieldName(fieldId)} relationship`
          });
        }
      }
    });

    // Add relationships to element data if there are any
    if (relationships.length > 0) {
      elementData.relationships = relationships;
    }

    // Call onSubmit with the element data
    onSubmit(elementData);
  };

  return (
    <div className="modal-overlay">
      <div className="template-form-modal">
        <div className="modal-header">
          <h2>Create {template.display_name}</h2>
          <button className="close-button" onClick={onClose}>×</button>
        </div>

        {loading && <div className="loading-indicator">Loading related elements...</div>}
        {error && <div className="error-message">{error}</div>}

        <form onSubmit={handleSubmit}>
          <div className="form-group">
            <label htmlFor="name">Name</label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              placeholder={`Enter ${template.display_name.toLowerCase()} name`}
              required
            />
          </div>

          <div className="form-group">
            <label htmlFor="description">Description</label>
            <textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              placeholder={`Describe this ${template.display_name.toLowerCase()}`}
              rows={4}
            />
          </div>

          <div className="form-group">
            <label htmlFor="tags">Tags (comma separated)</label>
            <input
              type="text"
              id="tags"
              name="tags"
              value={formData.tags}
              onChange={handleInputChange}
              placeholder="fantasy, magic, important, etc."
            />
          </div>

          <div className="form-group">
            <label htmlFor="importance">Importance</label>
            <select
              id="importance"
              name="importance"
              value={formData.importance}
              onChange={handleInputChange}
            >
              <option value="low">Low</option>
              <option value="medium">Medium</option>
              <option value="high">High</option>
            </select>
          </div>

          {/* Template-specific fields */}
          {fieldDefinitions.length > 0 && (
            <div className="template-fields">
              <h3>Template Fields</h3>
              {fieldDefinitions.map(field => {
                // Check if this is an ID field that should be a dropdown
                const isIdDropdown = isIdField(field.id);
                const templateType = isIdDropdown ? getTemplateTypeFromIdField(field.id) : null;
                const availableElements = templateType ? relatedElements[templateType] || [] : [];

                // Format the field label
                const fieldLabel = field.label || (field.id ? formatFieldName(field.id) : 'Unknown Field');

                return (
                  <div className="form-group" key={field.id}>
                    <label htmlFor={`custom-${field.id}`}>
                      {fieldLabel}
                      {field.required && <span className="required">*</span>}
                    </label>
                    {isIdDropdown && availableElements.length > 0 ? (
                      // Render dropdown for ID fields
                      <select
                        id={`custom-${field.id}`}
                        value={customFields[field.id] || ''}
                        onChange={(e) => handleCustomFieldChange(field.id, e.target.value)}
                        required={field.required}
                      >
                        <option value="">Select {fieldLabel}</option>
                        {availableElements.map(element => (
                          <option key={element.element_id} value={element.element_id}>
                            {element.name}
                          </option>
                        ))}
                      </select>
                    ) : field.type === 'select' && field.options ? (
                      // Render dropdown for fields with options
                      <select
                        id={`custom-${field.id}`}
                        value={customFields[field.id] || ''}
                        onChange={(e) => handleCustomFieldChange(field.id, e.target.value)}
                        required={field.required}
                      >
                        <option value="">Select {fieldLabel}</option>
                        {field.options.map(option => (
                          <option key={option} value={option}>
                            {option}
                          </option>
                        ))}
                      </select>
                    ) : field.type === 'textarea' ? (
                      // Render textarea for textarea fields
                      <textarea
                        id={`custom-${field.id}`}
                        value={customFields[field.id] || ''}
                        onChange={(e) => handleCustomFieldChange(field.id, e.target.value)}
                        placeholder={field.description || `Enter ${fieldLabel.toLowerCase()}`}
                        required={field.required}
                        rows={3}
                      />
                    ) : (
                      // Render input for other fields
                      <input
                        type={field.type === 'number' ? 'number' : 'text'}
                        id={`custom-${field.id}`}
                        value={customFields[field.id] || ''}
                        onChange={(e) => handleCustomFieldChange(field.id, e.target.value)}
                        placeholder={field.description || `Enter ${fieldLabel.toLowerCase()}`}
                        required={field.required}
                      />
                    )}
                  </div>
                );
              })}
            </div>
          )}

          {/* Relationship fields */}
          {relationshipDefinitions.length > 0 && (
            <div className="relationship-fields">
              <h3>Relationships</h3>
              {relationshipDefinitions.map(relationship => {
                // Format the relationship label
                const relationshipLabel = relationship.label ||
                  (relationship.target_template ?
                    `Related ${formatFieldName(relationship.target_template)}` :
                    'Related Element');

                // Format the description
                const relationshipDescription = relationship.description ||
                  (relationship.target_template ?
                    `Select a ${formatFieldName(relationship.target_template)} to relate to this element` :
                    'Select an element to relate to this element');

                // Generate a unique key for this relationship
                const relationshipKey = relationship.target_template ||
                  relationship.relationship_type ||
                  `relationship_${Math.random().toString(36).substr(2, 9)}`;

                return (
                  <div className="form-group" key={relationshipKey}>
                    <label htmlFor={`relationship-${relationshipKey}`}>
                      {relationshipLabel}
                    </label>
                    <select
                      id={`relationship-${relationshipKey}`}
                      value={relationshipFields[relationshipKey] || ''}
                      onChange={(e) => handleRelationshipChange(
                        relationship.target_template || relationshipKey,
                        e.target.value
                      )}
                    >
                      <option value="">None</option>
                      {relationship.target_template && relatedElements[relationship.target_template]?.map(element => (
                        <option key={element.element_id} value={element.element_id}>
                          {element.name}
                        </option>
                      ))}
                    </select>
                    <p className="relationship-description">
                      {relationshipDescription}
                    </p>
                  </div>
                );
              })}
            </div>
          )}

          <div className="form-actions">
            <button type="submit" className="create-button">Create {template.display_name}</button>
            <button type="button" className="cancel-button" onClick={onClose}>Cancel</button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default TemplateFormModal;

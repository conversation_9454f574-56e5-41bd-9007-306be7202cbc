// frontend/src/components/WorldCategoryFilter.js
import React, { useState, useEffect } from 'react';
import './WorldCategoryFilter.css';
import { getColorForCategory } from '../utils/colorMapping';

/**
 * Component for filtering world building categories
 * @param {Object} props - Component props
 * @returns {JSX.Element} WorldCategoryFilter UI
 */
const WorldCategoryFilter = ({
  categories,
  customizations,
  onUpdateCustomization,
  onResetCustomizations
}) => {
  const [showFilter, setShowFilter] = useState(false);

  // Group categories by category ID
  const categoriesByCategory = categories.reduce((acc, category) => {
    const categoryId = category.category_id;
    if (!acc[categoryId]) {
      acc[categoryId] = [];
    }
    acc[categoryId].push(category);
    return acc;
  }, {});

  // Sort categories for consistent display
  const sortedCategories = [
    'cat_cosmology_physical',
    'cat_cultural_social',
    'cat_economic_material',
    'cat_knowledge_technology',
    'cat_temporal_historical',
    'cat_magical_supernatural',
    'cat_interactive_game'
  ].filter(
    category => categoriesByCategory[category] && categoriesByCategory[category].length > 0
  );

  // Handle checkbox change
  const handleToggleCategory = (categoryId, isCurrentlyEnabled) => {
    // Find the current customization to preserve display_order
    const customization = customizations.find(c => c.category_id === categoryId);
    const displayOrder = customization ? customization.display_order : 0;

    // Call the update function
    onUpdateCustomization(categoryId, !isCurrentlyEnabled, displayOrder);
  };

  // Get enabled state for a category
  const isCategoryEnabled = (categoryId) => {
    const customization = customizations.find(c => c.category_id === categoryId);
    return customization ? customization.is_enabled : true;
  };

  // Get categories for a category ID
  const getCategoriesForId = (categoryId) => {
    if (!categoriesByCategory[categoryId]) return [];
    return [...categoriesByCategory[categoryId]];
  };

  return (
    <div className="world-category-filter">
      <button
        className="filter-toggle-button"
        onClick={() => setShowFilter(!showFilter)}
      >
        {showFilter ? 'Hide Category Filter' : 'Show Category Filter'}
      </button>

      {showFilter && (
        <div className="filter-panel">
          <div className="filter-header">
            <h3>Category Visibility</h3>
            <button
              className="reset-button"
              onClick={onResetCustomizations}
            >
              Reset to Defaults
            </button>
          </div>

          <div className="filter-content">
            {sortedCategories.map(categoryId => (
              <div key={categoryId} className="category-group">
                <h4
                  className={`category-title ${categoryId}`}
                  style={{
                    color: getColorForCategory(categoryId),
                    borderBottomColor: getColorForCategory(categoryId)
                  }}
                >
                  {categoryId.replace('cat_', '').replace(/_/g, ' ').split(' ').map(word =>
                    word.charAt(0).toUpperCase() + word.slice(1)
                  ).join(' ')}
                </h4>
                <div className="category-checkboxes">
                  {getCategoriesForId(categoryId).map(category => (
                    <div key={category.category_id} className="category-checkbox">
                      <label>
                        <input
                          type="checkbox"
                          checked={isCategoryEnabled(category.category_id)}
                          onChange={() => handleToggleCategory(
                            category.category_id,
                            isCategoryEnabled(category.category_id)
                          )}
                        />
                        <span>{category.name}</span>
                      </label>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default WorldCategoryFilter;

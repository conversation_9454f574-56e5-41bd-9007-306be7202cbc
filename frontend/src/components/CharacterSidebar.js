// frontend/src/components/CharacterSidebar.js
import React, { useState, useCallback } from 'react';
import { BASE_URL } from '../utils/apiConfig';
import './CharacterSidebar.css';

const CharacterSidebar = ({ characters, selectedBook, onAddCharacter, onGenerateAICharacter, onDeleteCharacter, onRefreshCharacters }) => {
  // Start with sidebar open for now to ensure it's visible
  const [isOpen, setIsOpen] = useState(true);
  const [showAIModal, setShowAIModal] = useState(false);
  const [newCharacter, setNewCharacter] = useState({
    name: '',
    description: '',
    traits: '',
    age: '',
    role: '',
    parent: '',
    sibling: '',
    relationships: '',
    headshot: '',
    race: ''
  });
  const [aiSuggestion, setAiSuggestion] = useState(null);

  // Toggle sidebar visibility
  const toggleSidebar = useCallback(() => setIsOpen(prev => !prev), []);

  // Handle manual input changes
  const handleInputChange = useCallback((e) => {
    const { name, value } = e.target;
    setNewCharacter(prev => ({ ...prev, [name]: value }));
  }, []);

  // Generate AI character
  const handleGenerateAICharacter = useCallback(async () => {
    if (!selectedBook) return;

    // Create a prompt for the AI character generation
    const settingTypes = 'fantasy'; // Default to fantasy if no setting types available
    const existingCharacters = characters.length > 0
      ? characters.map(char => `${char.name} (Role: ${char.role || 'unknown'}, Race: ${char.race || 'unknown'})`).join('; ')
      : 'None';

    const prompt = `Create a unique character for a ${settingTypes}-style book. Provide a name, avoid repetition and exclude these first names unless relatives: (${characters.map(c => c.name).join(', ')}), a description, a list of 3-5 traits, an age, a role, a parent, a sibling, and 1-3 relationships as an array of objects (e.g., [{"name": "Char1", "type": "Friend"}]) using names from this list where possible: ${existingCharacters}, and a race. Return as JSON with fields: name, description, traits, age, role, parent, sibling, relationships, race.`;

    console.debug('Generating AI character with prompt:', prompt);

    try {
      // Use the onGenerateAICharacter prop to generate the character
      const generatedCharacter = await onGenerateAICharacter(prompt);

      if (generatedCharacter) {
        setAiSuggestion(generatedCharacter);
      } else {
        setAiSuggestion(null);
        alert('Failed to generate character');
      }
    } catch (error) {
      console.error('Error generating AI character:', error);
      setAiSuggestion(null);
      alert(`Failed to generate character: ${error.message || 'Unknown error'}`);
    }
  }, [selectedBook, characters, onGenerateAICharacter]);

  // Open AI modal and generate character
  const openAIGenerationModal = useCallback(() => {
    setShowAIModal(true);
    // Call the generation function after showing the modal
    handleGenerateAICharacter();
  }, [handleGenerateAICharacter]);

  // Close AI modal
  const closeAIModal = useCallback(() => {
    setShowAIModal(false);
    setAiSuggestion(null);
  }, []);

  // Delete character
  const handleDeleteCharacter = useCallback(async (characterId) => {
    const character = characters.find(c => c.id === characterId);
    if (!character) return;

    const confirmDelete = window.confirm(`Are you sure you want to delete "${character.name}"?`);
    if (!confirmDelete || !selectedBook) {
      console.debug('Deletion cancelled or missing book');
      return;
    }

    try {
      await onDeleteCharacter(characterId);
    } catch (error) {
      console.error('Error deleting character:', error);
      alert(`Failed to delete character: ${error.message || 'Unknown error'}`);
    }
  }, [selectedBook, characters, onDeleteCharacter]);

  // Accept AI-generated character
  const handleAcceptAI = useCallback(async () => {
    if (!aiSuggestion || !selectedBook) return;

    try {
      // Add the character using the prop function
      await onAddCharacter(aiSuggestion);

      // Close the modal and clear the suggestion
      setAiSuggestion(null);
      setShowAIModal(false);

      // Refresh characters if needed
      if (onRefreshCharacters) {
        onRefreshCharacters();
      }
    } catch (error) {
      console.error('Error accepting AI character:', error);
      alert(`Failed to save character: ${error.message || 'Unknown error'}`);
    }
  }, [aiSuggestion, selectedBook, onAddCharacter, onRefreshCharacters]);

  // Add manually created character
  const handleAddCharacter = useCallback(async () => {
    if (!newCharacter.name.trim() || !selectedBook) return;

    const characterData = {
      name: newCharacter.name.trim(),
      description: newCharacter.description,
      traits: newCharacter.traits.split(',').map(t => t.trim()).filter(t => t),
      age: newCharacter.age,
      role: newCharacter.role,
      parent: newCharacter.parent,
      sibling: newCharacter.sibling,
      relationships: newCharacter.relationships.split(',').map(r => {
        const [name, type] = r.split(':').map(s => s.trim());
        return { name, type: type || 'Friend' };
      }).filter(r => r.name),
      headshot: newCharacter.headshot,
      race: newCharacter.race
    };

    console.debug('Adding character:', characterData);

    try {
      // Use the onAddCharacter prop to add the character
      await onAddCharacter(characterData);

      // Reset the form
      setNewCharacter({
        name: '',
        description: '',
        traits: '',
        age: '',
        role: '',
        parent: '',
        sibling: '',
        relationships: '',
        headshot: '',
        race: ''
      });

      // Refresh characters if needed
      if (onRefreshCharacters) {
        onRefreshCharacters();
      }
    } catch (error) {
      console.error('Error adding character:', error);
      alert(`Failed to add character: ${error.message || 'Unknown error'}`);
    }
  }, [newCharacter, selectedBook, onAddCharacter, onRefreshCharacters]);

  return (
    <>
      <div className={`character-sidebar ${isOpen ? 'open' : 'closed'}`}>
        <div className="sidebar-handle" onClick={toggleSidebar}>
          <span>{isOpen ? '◀' : '▶'}</span>
        </div>

        <div className="sidebar-content">
          <h3>Create Character</h3>
          <input
            type="text"
            name="name"
            placeholder="Character Name"
            value={newCharacter.name}
            onChange={handleInputChange}
          />
          <textarea
            name="description"
            value={newCharacter.description}
            onChange={handleInputChange}
            placeholder="Description"
          />
          <input
            type="text"
            name="traits"
            value={newCharacter.traits}
            onChange={handleInputChange}
            placeholder="Traits (comma-separated)"
          />
          <input
            type="text"
            name="age"
            value={newCharacter.age}
            onChange={handleInputChange}
            placeholder="Age"
          />
          <input
            type="text"
            name="role"
            value={newCharacter.role}
            onChange={handleInputChange}
            placeholder="Role"
          />
          <input
            type="text"
            name="parent"
            value={newCharacter.parent}
            onChange={handleInputChange}
            placeholder="Parent"
          />
          <input
            type="text"
            name="sibling"
            value={newCharacter.sibling}
            onChange={handleInputChange}
            placeholder="Sibling"
          />
          <input
            type="text"
            name="relationships"
            value={newCharacter.relationships}
            onChange={handleInputChange}
            placeholder="Relationships (e.g., Finn:Friend)"
          />
          <input
            type="text"
            name="race"
            value={newCharacter.race}
            onChange={handleInputChange}
            placeholder="Race"
          />
          <input
            type="text"
            name="headshot"
            value={newCharacter.headshot}
            onChange={handleInputChange}
            placeholder="Headshot URL"
          />
          <div className="sidebar-buttons">
            <button onClick={handleAddCharacter}>Add Character</button>
            <button onClick={openAIGenerationModal}>Generate AI Character</button>
          </div>
        </div>
      </div>

      {/* AI Character Generation Modal */}
      {showAIModal && (
        <div className="ai-modal">
          <div className="ai-modal-content">
            <h3>AI Character Generation</h3>
            {!aiSuggestion ? (
              <div className="ai-loading">
                <p>Generating character...</p>
                {/* Add a spinner or loading indicator */}
                <div className="loading-spinner"></div>
              </div>
            ) : (
              <div className="ai-suggestion">
                <h4>{aiSuggestion.name}</h4>
                <p><strong>Description:</strong> {aiSuggestion.description}</p>
                <p><strong>Traits:</strong> {Array.isArray(aiSuggestion.traits) ? aiSuggestion.traits.join(', ') : 'None'}</p>
                <p><strong>Age:</strong> {aiSuggestion.age || 'Unknown'}</p>
                <p><strong>Role:</strong> {aiSuggestion.role || 'Unknown'}</p>
                <p><strong>Parent:</strong> {aiSuggestion.parent || 'None'}</p>
                <p><strong>Sibling:</strong> {aiSuggestion.sibling || 'None'}</p>
                <p><strong>Relationships:</strong> {Array.isArray(aiSuggestion.relationships) && aiSuggestion.relationships.length > 0 ? aiSuggestion.relationships.map(r => `${r.name}: ${r.type}`).join(', ') : 'None'}</p>
                <p><strong>Race:</strong> {aiSuggestion.race || 'Unknown'}</p>
                <div className="ai-buttons">
                  <button onClick={handleAcceptAI}>Accept</button>
                  <button onClick={closeAIModal}>Reject</button>
                  <button onClick={handleGenerateAICharacter}>Regenerate</button>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </>
  );
};

export default CharacterSidebar;

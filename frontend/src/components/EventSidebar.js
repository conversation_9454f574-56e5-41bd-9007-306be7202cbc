
import React from 'react';
import './EventSidebar.css';

function EventSidebar({ events, selectedEvent, setSelectedEvent }) {
  if (!events || events.length === 0) {
    return (
      <div className="event-sidebar">
        <h3>Chapter Events</h3>
        <div className="no-events">No events for this chapter</div>
      </div>
    );
  }

  return (
    <div className="event-sidebar">
      <h3>Chapter Events</h3>
      <div className="events-list">
        {events.map((event, index) => (
          <div 
            key={`event-${event.id || index}`} 
            className={`event-card ${selectedEvent?.id === event.id ? 'selected' : ''}`}
            onClick={() => setSelectedEvent(event)}
          >
            <div className="event-type">{event.type}</div>
            <div className="event-description">{event.description || event.text || 'No content'}</div>
            {event.characters && event.characters.length > 0 && (
              <div className="event-tags">
                <span className="tag-label">Characters:</span>
                <div className="tag-bubbles">
                  {event.characters.slice(0, 3).map((character, idx) => (
                    <span key={`char-${idx}`} className="character-bubble">
                      {character.length > 10 ? character.substring(0, 8) + '...' : character}
                    </span>
                  ))}
                  {event.characters.length > 3 && (
                    <span className="more-bubble character-bubble">+{event.characters.length - 3}</span>
                  )}
                </div>
              </div>
            )}
            {event.locations && event.locations.length > 0 && (
              <div className="event-tags">
                <span className="tag-label">Locations:</span>
                <div className="tag-bubbles">
                  {event.locations.slice(0, 3).map((location, idx) => (
                    <span key={`loc-${idx}`} className="location-bubble">
                      {location.length > 10 ? location.substring(0, 8) + '...' : location}
                    </span>
                  ))}
                  {event.locations.length > 3 && (
                    <span className="more-bubble location-bubble">+{event.locations.length - 3}</span>
                  )}
                </div>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
}

export default EventSidebar;

// frontend/src/components/WorldElementsView.js
import React, { useState, useEffect } from 'react';
import './WorldElementsView.css';
import CreateElementModal from './CreateElementModal';

/**
 * Component for displaying world elements in different view modes
 * @param {Object} props - Component props
 * @returns {JSX.Element} WorldElementsView UI
 */
const WorldElementsView = ({
  visibleCategories,
  activeCategory,
  viewMode,
  elements = [],
  onCategorySelect,
  onViewModeChange,
  onElementSelect,
  onCreateElement
}) => {
  // State for tracking if we're showing categories or elements
  const [showingElements, setShowingElements] = useState(false);
  const [showCreateModal, setShowCreateModal] = useState(false);

  // When active category changes, update the showing elements state
  useEffect(() => {
    setShowingElements(!!activeCategory);
  }, [activeCategory]);

  // Handle back button click
  const handleBackClick = () => {
    setShowingElements(false);
    onCategorySelect(null);
  };

  // Handle create button click
  const handleCreateClick = () => {
    setShowCreateModal(true);
  };

  // Handle modal close
  const handleModalClose = () => {
    setShowCreateModal(false);
  };

  // Handle element creation
  const handleElementCreate = (elementData, customFields) => {
    onCreateElement(elementData, null, elementData.element_type, customFields);
    setShowCreateModal(false);
  };
  return (
    <div className="world-elements-view">
      {showCreateModal && activeCategory && (
        <CreateElementModal
          categoryId={activeCategory.category_id}
          categoryName={activeCategory.name}
          onClose={handleModalClose}
          onCreate={handleElementCreate}
        />
      )}
      <div className="world-elements-header">
        {showingElements ? (
          <div className="header-with-back">
            <button className="back-button" onClick={handleBackClick}>
              ← Back
            </button>
            <h3>{activeCategory?.name || 'Elements'}</h3>
          </div>
        ) : (
          <h3>Categories</h3>
        )}

        <div className="view-mode-selector">
          <button
            className={`view-mode-option ${viewMode === 'card' ? 'active' : ''}`}
            onClick={() => onViewModeChange('card')}
            title="Card View"
          >
            <span className="icon">🗃️</span>
          </button>

          <button
            className={`view-mode-option ${viewMode === 'table' ? 'active' : ''}`}
            onClick={() => onViewModeChange('table')}
            title="Table View"
          >
            <span className="icon">📊</span>
          </button>

          <button
            className={`view-mode-option ${viewMode === 'document' ? 'active' : ''}`}
            onClick={() => onViewModeChange('document')}
            title="Document View"
          >
            <span className="icon">📄</span>
          </button>
        </div>
      </div>

      {showingElements ? (
        <div className="elements-list">
          {elements && elements.length > 0 ? (
            elements.map(element => (
              <div
                key={element.element_id}
                className="element-item"
                onClick={() => onElementSelect(element.element_id)}
              >
                <div className="element-header">
                  <h4>{element.name}</h4>
                  {element.importance && (
                    <span className={`importance-badge ${element.importance}`}>
                      {element.importance}
                    </span>
                  )}
                </div>

                <p className="element-description">{element.description}</p>

                {element.tags && element.tags.length > 0 && (
                  <div className="element-tags">
                    {element.tags.map(tag => (
                      <span key={tag} className="element-tag">{tag}</span>
                    ))}
                  </div>
                )}
              </div>
            ))
          ) : (
            <div className="no-elements">
              <p>No elements found in this category. Create a new element or use AI to generate some.</p>
              <button className="create-element-button" onClick={handleCreateClick}>Create New Element</button>
            </div>
          )}
        </div>
      ) : (
        <div className="categories-list">
          {visibleCategories && visibleCategories.length > 0 ? (
            visibleCategories.map(category => (
              <div
                key={category.category_id}
                className={`category-item ${activeCategory && activeCategory.category_id === category.category_id ? 'active' : ''}`}
                onClick={() => onCategorySelect(category.category_id)}
              >
                <div className={`category-icon ${category.aspect_type}`}>
                  {getCategoryIcon(category.aspect_type)}
                </div>

                <div className="category-info">
                  <h4>{category.name}</h4>
                  <p>{category.description}</p>
                </div>

                {category.is_universal && (
                  <span className="category-badge universal">Universal</span>
                )}

                {!category.is_universal && category.applicable_genres && (
                  <span className="category-badge genre-specific">Genre</span>
                )}
              </div>
            ))
          ) : (
            <div className="no-categories">
              <p>No categories found. Try changing the filters or create a new category.</p>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

/**
 * Helper function to get an icon for a category based on its aspect type
 * @param {string} aspectType - The aspect type
 * @returns {string} An emoji icon
 */
const getCategoryIcon = (aspectType) => {
  switch (aspectType) {
    case 'physical':
      return '🌍';
    case 'social':
      return '👥';
    case 'metaphysical':
      return '✨';
    case 'technological':
      return '⚙️';
    default:
      return '📁';
  }
};

export default WorldElementsView;

// frontend/src/components/ReferencePanel.js
import React from 'react';
import './ReferencePanel.css';
import { BASE_URL } from '../utils/apiConfig';

/**
 * Helper function to get a label for relationship strength
 * @param {number} strength - Relationship strength (0-5)
 * @returns {string} Label describing the relationship strength
 */
const getStrengthLabel = (strength) => {
  switch (parseInt(strength, 10)) {
    case 0: return "Enemy";
    case 1: return "Acquaintance";
    case 2: return "Casual friend";
    case 3: return "Close friend";
    case 4: return "Deep bond";
    case 5: return "Intimate connection";
    default: return "Close friend";
  }
};

/**
 * Presentation component for ReferencePanel
 * @param {Object} props - Component props
 * @returns {JSX.Element} Reference panel UI
 */
const ReferencePanel = React.memo((props) => {
  const {
    activeTab,
    loading,
    searchTerm,
    characters,
    worldElements,
    onTabChange,
    onSearch
  } = props;

  // Determine which items to display based on the active tab
  const filteredItems = activeTab === 'characters' ? characters || [] : worldElements || [];

  // Debug output
  console.log('ReferencePanel - activeTab:', activeTab);
  console.log('ReferencePanel - worldElements:', worldElements);
  console.log('ReferencePanel - filteredItems:', filteredItems);

  // Render character item
  const renderCharacterItem = (character) => {
    // Check if relationships exist and is an array
    const hasRelationships = character.relationships &&
                           Array.isArray(character.relationships) &&
                           character.relationships.length > 0;

    return (
      <div key={character.id} className="reference-item character-item">
        <div className="reference-item-header">
          <h4>{character.name}</h4>
          {character.role && <span className="character-role">{character.role}</span>}
        </div>

        {character.headshot && (
          <div className="character-headshot">
            <img
              src={character.headshot.startsWith('http') ? character.headshot : `${BASE_URL}${character.headshot}`}
              alt={character.name}
              onError={(e) => {
                console.error('Error loading headshot in ReferencePanel:', character.headshot);
                e.target.onerror = null;
                e.target.src = 'https://via.placeholder.com/150?text=' + encodeURIComponent(character.name.charAt(0));
              }}
            />
          </div>
        )}

        {/* Display basic info */}
        <div className="character-basic-info">
          {character.age && (
            <div className="info-item">
              <strong>Age:</strong> {character.age}
            </div>
          )}
          {character.race && (
            <div className="info-item">
              <strong>Race:</strong> {character.race}
            </div>
          )}
        </div>



        {/* Display backstory if available */}
        {character.backstory && (
          <div className="character-backstory">
            <strong>Backstory:</strong>
            <p>{character.backstory}</p>
          </div>
        )}

        {/* Display character arc if available */}
        {character.arc && (
          <div className="character-arc">
            <strong>Character Arc:</strong>
            <p>{character.arc}</p>
          </div>
        )}

        {character.traits && character.traits.length > 0 && (
          <div className="character-traits">
            <strong>Traits:</strong>
            <ul>
              {character.traits.map((trait, index) => (
                <li key={index}>{trait}</li>
              ))}
            </ul>
          </div>
        )}

        {hasRelationships && (
          <div className="character-relationships">
            <strong>Relationships:</strong>
            <ul>
              {character.relationships.map((rel, index) => {
                // Handle different relationship formats
                const name = rel.name || rel.relatedCharacterName || rel.character || 'Unknown';
                let type = rel.type || rel.relationshipType || rel.relationship || 'Unknown';
                let description = rel.description || '';
                let strength = rel.strength !== undefined ? rel.strength : 3; // Default to 3 if not specified

                // Get the strength label
                const strengthLabel = getStrengthLabel(strength);

                // Check if type contains a description in the format "type <description>"
                if (!description && typeof type === 'string' && type.includes('<') && type.includes('>')) {
                  const match = type.match(/([^<]+)<([^>]+)>/);
                  if (match) {
                    type = match[1].trim();
                    description = match[2].trim();
                  }
                }

                return (
                  <li key={index}>
                    <strong>{name}</strong>: {type}
                    <div className="relationship-strength">
                      <span className="strength-label">{strengthLabel}</span>
                      <div className="strength-bar">
                        <div
                          className="strength-fill"
                          style={{
                            width: `${(parseInt(strength, 10) / 5) * 100}%`,
                            backgroundColor: parseInt(strength, 10) === 0 ? '#F44336' : '#4CAF50'
                          }}
                        ></div>
                      </div>
                    </div>
                    {description && <div className="relationship-description">{description}</div>}
                  </li>
                );
              })}
            </ul>
          </div>
        )}


      </div>
    );
  };

  // Render world element item
  const renderWorldElementItem = (element) => {
    // Format category name for display
    const formatCategoryName = (category) => {
      if (!category) return '';
      return category
        .replace('cat_', '')
        .replace(/_/g, ' ')
        .split(' ')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');
    };

    // Get custom fields from either attributes or custom_fields
    const customFields = element.custom_fields || element.attributes || {};

    // Get element ID from either id or element_id
    const elementId = element.element_id || element.id;

    return (
      <div key={elementId} className="reference-item world-item">
        <div className="reference-item-header">
          <h4>{element.name}</h4>
          {element.category && (
            <span className="element-category">
              {formatCategoryName(element.category)}
            </span>
          )}
        </div>

        {element.description && (
          <div className="reference-item-description">
            <p>{element.description}</p>
          </div>
        )}

        {/* Show parent element if available */}
        {element.parent_id && element.parent_name && (
          <div className="element-parent">
            <strong>Parent:</strong> {element.parent_name}
          </div>
        )}

        {/* Show relationships if available */}
        {element.relationships && element.relationships.length > 0 && (
          <div className="element-relationships">
            <strong>Relationships:</strong>
            <ul>
              {element.relationships.map((rel, index) => (
                <li key={index}>
                  <strong>{rel.target_name || rel.source_name || 'Related to'}:</strong> {rel.relationship_type || 'connected'}
                  {rel.description && <div className="relationship-description">{rel.description}</div>}
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* Show custom fields/attributes */}
        {customFields && Object.keys(customFields).length > 0 && (
          <div className="element-attributes">
            <strong>Attributes:</strong>
            <ul>
              {Object.entries(customFields).map(([key, value]) => {
                // Skip empty values or internal fields
                if (!value || key.startsWith('_')) return null;

                // Format the value based on type
                let displayValue = value;
                if (typeof value === 'object') {
                  displayValue = JSON.stringify(value);
                }

                return (
                  <li key={key}><strong>{key}:</strong> {displayValue}</li>
                );
              }).filter(Boolean)}
            </ul>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="reference-panel-content">
      <div className="reference-panel-header">
        <h3>Reference</h3>
        <div className="reference-tabs">
          <button
            className={`tab-button ${activeTab === 'characters' ? 'active' : ''}`}
            onClick={() => onTabChange('characters')}
          >
            Characters
          </button>
          <button
            className={`tab-button ${activeTab === 'world' ? 'active' : ''}`}
            onClick={() => onTabChange('world')}
          >
            World
          </button>
        </div>
      </div>

      <div className="reference-search-container">
        <div className="reference-search">
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => onSearch(e.target.value)}
            placeholder={`Search ${activeTab}...`}
          />
        </div>
        {activeTab === 'world' && (
          <button
            className="detailed-add-button"
            onClick={props.onDetailedAddClick}
          >
            Detailed Add
          </button>
        )}
      </div>

      <div className="reference-content">
        {loading ? (
          <div className="loading-indicator">Loading...</div>
        ) : filteredItems.length > 0 ? (
          <div className="reference-items">
            {activeTab === 'characters' && filteredItems.map(character => renderCharacterItem(character))}
            {activeTab === 'world' && filteredItems.map(element => renderWorldElementItem(element))}
          </div>
        ) : (
          <div className="no-items">
            {searchTerm ? (
              <p>No {activeTab} found matching "{searchTerm}".</p>
            ) : (
              <p>No {activeTab} available.</p>
            )}
          </div>
        )}
      </div>
    </div>
  );
});

export default ReferencePanel;

// frontend/src/components/WorldElementRelationshipManager.js
import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { selectCurrentBookId, selectTemplateByType } from '../redux/slices/worldBuildingSlice';
import { createWorldRelationship, updateWorldRelationship, deleteWorldRelationship } from '../services/worldBuildingApiService';
import { getRelationshipTypeLabel, getRelationshipTypes, getRelationshipTypeById, getSuggestedRelationshipTypes } from '../utils/relationshipTypes';
import './WorldElementRelationshipManager.css';

/**
 * Component for managing relationships for a world element
 * @param {Object} props - Component props
 * @param {Object} props.element - The current element
 * @param {Array} props.relationships - The element's relationships
 * @param {Array} props.allElements - All elements in the world
 * @param {Function} props.onRelationshipCreated - Callback when a relationship is created
 * @param {Function} props.onRelationshipUpdated - Callback when a relationship is updated
 * @param {Function} props.onRelationshipDeleted - Callback when a relationship is deleted
 * @param {Function} props.onSelectElement - Callback to select an element
 * @returns {JSX.Element} WorldElementRelationshipManager UI
 */
const WorldElementRelationshipManager = ({
  element,
  relationships = [],
  allElements = [],
  onRelationshipCreated,
  onRelationshipUpdated,
  onRelationshipDeleted,
  onSelectElement
}) => {
  const bookId = useSelector(selectCurrentBookId);
  const getTemplateByType = useSelector(selectTemplateByType);

  // State for the relationship manager
  const [isCreating, setIsCreating] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [filteredElements, setFilteredElements] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');

  // State for the new relationship
  const [newRelationship, setNewRelationship] = useState({
    targetElementId: '',
    relationshipType: '',
    description: '',
    isIncoming: false
  });

  // Get all available relationship types
  const relationshipTypes = getRelationshipTypes();

  // Group relationship types by category
  const groupedRelationshipTypes = React.useMemo(() => {
    return relationshipTypes.reduce((groups, type) => {
      const category = type.category || 'other';
      if (!groups[category]) {
        groups[category] = [];
      }
      groups[category].push(type);
      return groups;
    }, {});
  }, [relationshipTypes]);

  // Get suggested relationship types based on element types and templates
  const getRelationshipSuggestions = (sourceElement, targetElement) => {
    if (!sourceElement || !targetElement) return [];

    // Get the element types
    const sourceType = sourceElement.element_type;
    const targetType = targetElement.element_type;

    // Get the templates for the source and target elements
    const sourceTemplate = getTemplateByType(sourceType);
    const targetTemplate = getTemplateByType(targetType);

    // Get suggested relationship types using the utility function
    return getSuggestedRelationshipTypes(sourceType, targetType, sourceTemplate, targetTemplate);
  };

  // Filter elements based on search term and category
  useEffect(() => {
    if (!allElements || allElements.length === 0) return;

    let filtered = [...allElements];

    // Remove the current element from the list
    filtered = filtered.filter(el => el.element_id !== element.element_id);

    // Filter by search term
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(el =>
        el.name.toLowerCase().includes(term) ||
        (el.description && el.description.toLowerCase().includes(term))
      );
    }

    // Filter by category
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(el => el.category === selectedCategory);
    }

    setFilteredElements(filtered);
  }, [allElements, element, searchTerm, selectedCategory]);

  // Get unique categories from all elements
  const categories = React.useMemo(() => {
    if (!allElements || allElements.length === 0) return [];

    const uniqueCategories = [...new Set(allElements.map(el => el.category))];
    return uniqueCategories.filter(Boolean).sort();
  }, [allElements]);

  // Handle creating a new relationship
  const handleCreateRelationship = async () => {
    if (!newRelationship.targetElementId || !newRelationship.relationshipType) {
      setError('Please select a target element and relationship type');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Determine source and target based on direction
      const sourceId = newRelationship.isIncoming ? newRelationship.targetElementId : element.element_id;
      const targetId = newRelationship.isIncoming ? element.element_id : newRelationship.targetElementId;

      const relationshipData = {
        source_element_id: sourceId,
        target_element_id: targetId,
        relationship_type: newRelationship.relationshipType,
        description: newRelationship.description || ''
      };

      const response = await createWorldRelationship(bookId, relationshipData);

      if (onRelationshipCreated) {
        onRelationshipCreated(response);
      }

      // Reset form
      setNewRelationship({
        targetElementId: '',
        relationshipType: '',
        description: '',
        isIncoming: false
      });

      setIsCreating(false);
    } catch (err) {
      console.error('Error creating relationship:', err);
      setError(err.message || 'Failed to create relationship');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle updating a relationship
  const handleUpdateRelationship = async (relationshipId, updatedData) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await updateWorldRelationship(bookId, relationshipId, updatedData);

      if (onRelationshipUpdated) {
        onRelationshipUpdated(response);
      }
    } catch (err) {
      console.error('Error updating relationship:', err);
      setError(err.message || 'Failed to update relationship');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle deleting a relationship
  const handleDeleteRelationship = async (relationshipId) => {
    if (!window.confirm('Are you sure you want to delete this relationship?')) {
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      await deleteWorldRelationship(bookId, relationshipId);

      if (onRelationshipDeleted) {
        onRelationshipDeleted(relationshipId);
      }
    } catch (err) {
      console.error('Error deleting relationship:', err);
      setError(err.message || 'Failed to delete relationship');
    } finally {
      setIsLoading(false);
    }
  };

  // Render the relationship creation form
  const renderRelationshipForm = () => {
    // Get the target element if selected
    const targetElement = newRelationship.targetElementId
      ? allElements.find(el => el.element_id === newRelationship.targetElementId)
      : null;

    // Get suggested relationship types if target is selected
    const suggestedTypes = targetElement
      ? getRelationshipSuggestions(element, targetElement)
      : [];

    return (
      <div className="relationship-form">
        <h4>Create New Relationship</h4>

        {/* Direction toggle */}
        <div className="form-section">
          <label className="section-label">Direction</label>
          <div className="direction-toggle">
            <button
              className={`direction-button ${!newRelationship.isIncoming ? 'active' : ''}`}
              onClick={() => setNewRelationship({...newRelationship, isIncoming: false})}
              title={`From ${element.name} to target element`}
            >
              <span className="direction-arrow">→</span>
              <span>Outgoing: {element.name} → Target</span>
            </button>
            <button
              className={`direction-button ${newRelationship.isIncoming ? 'active' : ''}`}
              onClick={() => setNewRelationship({...newRelationship, isIncoming: true})}
              title={`From target element to ${element.name}`}
            >
              <span className="direction-arrow">←</span>
              <span>Incoming: Target → {element.name}</span>
            </button>
          </div>
        </div>

        {/* Target element selection */}
        <div className="form-section">
          <label className="section-label">Target Element</label>
          <div className="element-search">
            <input
              type="text"
              placeholder="Search elements by name..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="search-input"
            />

            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="category-select"
            >
              <option value="all">All Categories</option>
              {categories.map(category => (
                <option key={category} value={category}>{category}</option>
              ))}
            </select>
          </div>

          <div className="element-list">
            {filteredElements.length > 0 ? (
              filteredElements.map(el => (
                <div
                  key={el.element_id}
                  className={`element-item ${newRelationship.targetElementId === el.element_id ? 'selected' : ''}`}
                  onClick={() => setNewRelationship({...newRelationship, targetElementId: el.element_id, relationshipType: ''})}
                >
                  <div className="element-name">{el.name}</div>
                  <div className="element-type">{el.element_type}</div>
                  <div className="element-category">{el.category}</div>
                </div>
              ))
            ) : (
              <div className="no-elements">No matching elements found</div>
            )}
          </div>
        </div>

        {/* Relationship type selection - only show if target element is selected */}
        {targetElement && (
          <div className="form-section">
            <label className="section-label">Relationship Type</label>

            {/* Suggested relationship types */}
            {suggestedTypes.length > 0 && (
              <div className="suggested-relationships">
                <h5>Suggested Relationships</h5>
                <div className="relationship-buttons">
                  {suggestedTypes.map(type => (
                    <button
                      key={type.id}
                      className={`relationship-button suggested ${type.isFromTemplate ? 'template-based' : ''} ${newRelationship.relationshipType === type.id ? 'active' : ''}`}
                      onClick={() => setNewRelationship({...newRelationship, relationshipType: type.id})}
                      title={type.description}
                    >
                      {type.label}
                    </button>
                  ))}
                </div>
              </div>
            )}

            {/* All relationship types by category */}
            <div className="relationship-categories">
              {Object.entries(groupedRelationshipTypes).map(([category, types]) => (
                <div key={category} className="relationship-category">
                  <h5 className="category-header">{category.charAt(0).toUpperCase() + category.slice(1)}</h5>
                  <div className="relationship-buttons">
                    {types.map(type => (
                      <button
                        key={type.id}
                        className={`relationship-button ${category} ${newRelationship.relationshipType === type.id ? 'active' : ''}`}
                        onClick={() => setNewRelationship({...newRelationship, relationshipType: type.id})}
                        title={type.description}
                      >
                        {type.label}
                      </button>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Description field */}
        <div className="form-section">
          <label className="section-label">Description (Optional)</label>
          <textarea
            value={newRelationship.description}
            onChange={(e) => setNewRelationship({...newRelationship, description: e.target.value})}
            placeholder="Describe this relationship..."
            rows={3}
            className="description-textarea"
          />
        </div>

        {/* Relationship preview */}
        {targetElement && newRelationship.relationshipType && (
          <div className="relationship-preview">
            <h5>Preview</h5>
            <div className="preview-content">
              <span className="preview-element">
                {newRelationship.isIncoming ? targetElement.name : element.name}
              </span>
              <span className="preview-arrow">→</span>
              <span className="preview-relationship">
                {getRelationshipTypeLabel(newRelationship.relationshipType)}
              </span>
              <span className="preview-arrow">→</span>
              <span className="preview-element">
                {newRelationship.isIncoming ? element.name : targetElement.name}
              </span>
            </div>
          </div>
        )}

        {error && <div className="error-message">{error}</div>}

        {/* Form actions */}
        <div className="form-actions">
          <button
            className="cancel-button"
            onClick={() => setIsCreating(false)}
            disabled={isLoading}
          >
            Cancel
          </button>
          <button
            className="create-button"
            onClick={handleCreateRelationship}
            disabled={isLoading || !newRelationship.targetElementId || !newRelationship.relationshipType}
          >
            {isLoading ? 'Creating...' : 'Create Relationship'}
          </button>
        </div>
      </div>
    );
  };

  return (
    <div className="world-element-relationship-manager">
      <div className="relationship-manager-header">
        <h3>Relationships</h3>
        {!isCreating && (
          <button
            className="add-relationship-button"
            onClick={() => setIsCreating(true)}
          >
            Add Relationship
          </button>
        )}
      </div>

      {isCreating ? (
        renderRelationshipForm()
      ) : (
        <div className="relationships-list">
          {relationships.length > 0 ? (
            relationships.map(relationship => (
              <div key={relationship.relationship_id} className="relationship-item">
                <div className="relationship-header">
                  <span className={`relationship-direction ${relationship.direction}`}>
                    {relationship.direction === 'outgoing' ? element.name : relationship.otherElement?.name || 'Unknown'}
                  </span>
                  <span className="relationship-type" data-type={relationship.type || relationship.relationship_type}>
                    {getRelationshipTypeLabel(relationship.type || relationship.relationship_type)}
                  </span>
                  <span className="relationship-other">
                    {relationship.direction === 'outgoing'
                      ? (relationship.otherElement?.name || relationship.target_name || 'Unknown')
                      : (relationship.otherElement?.name || relationship.source_name || 'Unknown')}
                  </span>
                </div>

                {relationship.description && (
                  <div className="relationship-description">
                    {relationship.description}
                  </div>
                )}

                <div className="relationship-actions">
                  <button
                    className="view-related-button"
                    onClick={() => {
                      const elementIdToView = relationship.direction === 'outgoing'
                        ? relationship.target_id || relationship.target_element_id
                        : relationship.source_id || relationship.source_element_id;

                      if (elementIdToView && onSelectElement) {
                        onSelectElement(elementIdToView);
                      }
                    }}
                  >
                    View Related Element
                  </button>
                  <button
                    className="delete-relationship-button"
                    onClick={() => handleDeleteRelationship(relationship.relationship_id)}
                  >
                    Delete
                  </button>
                </div>
              </div>
            ))
          ) : (
            <div className="no-relationships">
              <p>No relationships found for this element.</p>
              <p>Create a relationship to connect this element to others in your world.</p>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default WorldElementRelationshipManager;

/* frontend/src/components/TemplateBrowser.css */
.template-browser {
  display: flex;
  flex-direction: column;
  background-color: var(--background-primary);
  overflow: hidden;
  height: 100%;
}

/* Template section headers removed */

.template-browser-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  border-bottom: 1px solid var(--border-color);
}

.template-browser-search {
  flex: 1;
  margin-right: 10px;
  display: flex;
  gap: 10px;
}

.template-browser-search input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  font-size: 0.9rem;
  background-color: var(--background-secondary);
  color: var(--text-primary);
}

.semantic-search-button {
  padding: 8px 12px;
  background-color: var(--background-tertiary, #f5f5f5);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  font-size: 0.9rem;
  cursor: pointer;
  white-space: nowrap;
  transition: all 0.2s ease;
  color: var(--text-primary);
}

.semantic-search-button:hover {
  background-color: var(--background-hover, #e0e0e0);
}

.create-template-button {
  padding: 8px 12px;
  background-color: var(--primary);
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 0.9rem;
  cursor: pointer;
  white-space: nowrap;
  transition: background-color 0.2s;
}

.create-template-button:hover {
  background-color: var(--primary-dark, #1976d2);
}

.template-browser-search input:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.1);
}

.template-browser-content {
  padding: 8px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 8px;
  flex: 1;
  max-height: calc(100vh - 150px); /* Ensure scrolling */
  scrollbar-width: thin; /* For Firefox */
  scrollbar-color: var(--border-color) transparent; /* For Firefox */
}

/* Custom scrollbar styling for Webkit browsers */
.template-browser-content::-webkit-scrollbar {
  width: 8px;
}

.template-browser-content::-webkit-scrollbar-track {
  background: transparent;
}

.template-browser-content::-webkit-scrollbar-thumb {
  background-color: var(--border-color);
  border-radius: 4px;
  border: 2px solid transparent;
}

.template-browser-content::-webkit-scrollbar-thumb:hover {
  background-color: var(--text-secondary);
}

.template-cards {
  display: grid;
  grid-template-columns: repeat(2, 1fr); /* Changed to always show 2 columns */
  gap: 10px;
}

.template-card {
  background-color: var(--background-secondary);
  border-radius: 6px;
  padding: 8px 10px;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid var(--border-color);
  border-left: 3px solid var(--primary);
  display: flex;
  flex-direction: column;
}

.template-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  border-color: var(--primary);
}

.template-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
}

.template-card-header h4 {
  margin: 0;
  font-size: 0.95rem;
  color: var(--text-primary);
}

.template-custom-badge {
  font-size: 0.7rem;
  padding: 2px 6px;
  border-radius: 10px;
  background-color: var(--secondary);
  color: white;
  font-weight: bold;
}

.template-description {
  margin: 0 0 6px 0;
  font-size: 0.8rem;
  color: var(--text-secondary);
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  flex: 1;
}

.template-card-footer {
  display: flex;
  justify-content: space-between;
  font-size: 0.75rem;
  color: var(--text-secondary);
  margin-top: auto;
}

.template-version {
  background-color: rgba(33, 150, 243, 0.1);
  padding: 2px 6px;
  border-radius: 10px;
  color: var(--primary);
}

.template-usage {
  font-style: italic;
}

.template-browser-loading,
.template-browser-error,
.template-browser-empty {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  color: var(--text-secondary);
  text-align: center;
  min-height: 100px;
}

.template-browser-error {
  color: var(--error);
  flex-direction: column;
}

.template-browser-error-recovery {
  margin-top: 10px;
  padding: 10px;
  background-color: var(--background-tertiary);
  border-radius: 4px;
  color: var(--text-secondary);
  font-weight: normal;
  font-size: 0.9rem;
}

/* Dark theme adjustments */
.theme-dark .template-browser {
  background-color: var(--background-secondary);
}

.theme-dark .template-card {
  background-color: var(--background-tertiary);
}

/* Responsive styles */
@media (max-width: 768px) {
  .template-cards {
    grid-template-columns: 1fr;
  }
}

// frontend/src/components/Sidebar.js
import React, { useCallback } from 'react';
import { useSelector } from 'react-redux';
import { selectCurrentBook } from '../redux/slices/bookSlice';
import './Sidebar.css';

/**
 * Sidebar component for chapter navigation
 * @param {Object} props - Component props
 * @param {boolean} props.isOpen - Sidebar visibility state
 * @param {Function} props.setIsOpen - Toggles sidebar visibility
 * @param {Function} props.onChapterSelect - Selects a chapter
 * @param {Object|null} props.currentChapter - Currently selected chapter
 * @param {Array} props.chapters - List of chapters
 * @returns {JSX.Element} Sidebar UI
 */
const Sidebar = React.memo(({ isOpen, setIsOpen, onChapterSelect, currentChapter, chapters }) => {
  const selectedBook = useSelector(selectCurrentBook);

  /**
   * Toggles sidebar visibility
   */
  const toggleSidebar = useCallback(() => {
    console.debug('Toggling sidebar, current state:', isOpen);
    setIsOpen(prev => !prev);
  }, [isOpen, setIsOpen]);

  // Debugging log for render
  console.debug('Sidebar rendered with:', { selectedBook: selectedBook?.book_id, chapters: chapters?.length });

  if (!selectedBook) return null; // Hide sidebar if no book is selected

  return (
    <div className={`sidebar ${isOpen ? 'open' : ''}`}>
      <div className="handle" onClick={toggleSidebar} />
      <div className="sidebar-content">
        <h3>{selectedBook.title} - Chapters</h3>
        <ul>
          {chapters.map((chapter) => (
            <li
              key={chapter.id}
              style={{
                cursor: 'pointer',
                padding: '5px',
                background: currentChapter?.id === chapter.id ? '#e6f0ff' : 'transparent',
              }}
              onClick={() => onChapterSelect(chapter)}
            >
              {chapter.title}
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
});

export default Sidebar;
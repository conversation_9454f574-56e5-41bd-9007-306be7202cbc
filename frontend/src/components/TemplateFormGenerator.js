// frontend/src/components/TemplateFormGenerator.js
import React, { useState, useEffect } from 'react';
import './TemplateFormGenerator.css';
import { useTemplateRegistry } from '../utils/TemplateRegistry';

/**
 * Component for generating forms based on element templates
 * @param {Object} props - Component props
 * @returns {JSX.Element} TemplateFormGenerator UI
 */
const TemplateFormGenerator = ({
  templateId,
  initialValues = {},
  onChange,
  showBasicFields = true,
  groupFields = true
}) => {
  // State for form values
  const [formValues, setFormValues] = useState({
    name: initialValues.name || '',
    description: initialValues.description || '',
    tags: Array.isArray(initialValues.tags)
      ? initialValues.tags.join(', ')
      : initialValues.tags || '',
    importance: initialValues.importance || 'medium',
    custom_fields: initialValues.custom_fields || {}
  });

  // Use the template registry hook to get templates from Redux
  const templateRegistry = useTemplateRegistry();

  // Get template
  const template = templateRegistry.getTemplate(templateId);

  // Group fields by category if enabled
  const getGroupedFields = () => {
    if (!template || !groupFields) return { 'Details': template?.fields || [] };

    const groups = {};

    template.fields.forEach(field => {
      const category = field.category || 'Details';
      if (!groups[category]) {
        groups[category] = [];
      }
      groups[category].push(field);
    });

    return groups;
  };

  // Handle basic field changes
  const handleBasicFieldChange = (e) => {
    const { name, value } = e.target;

    setFormValues(prev => {
      const newValues = {
        ...prev,
        [name]: value
      };

      if (onChange) {
        onChange(newValues);
      }

      return newValues;
    });
  };

  // Handle custom field changes
  const handleCustomFieldChange = (fieldId, value) => {
    setFormValues(prev => {
      const newCustomFields = {
        ...prev.custom_fields,
        [fieldId]: value
      };

      const newValues = {
        ...prev,
        custom_fields: newCustomFields
      };

      if (onChange) {
        onChange(newValues);
      }

      return newValues;
    });
  };

  // Render a field based on its type
  const renderField = (field) => {
    const value = formValues.custom_fields[field.id] || '';

    switch (field.type) {
      case 'textarea':
        return (
          <textarea
            id={`custom-${field.id}`}
            value={value}
            onChange={(e) => handleCustomFieldChange(field.id, e.target.value)}
            rows={3}
            placeholder={field.placeholder}
            required={field.required}
          />
        );

      case 'select':
        return (
          <select
            id={`custom-${field.id}`}
            value={value}
            onChange={(e) => handleCustomFieldChange(field.id, e.target.value)}
            required={field.required}
          >
            <option value="">Select {field.label}</option>
            {field.options && field.options.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        );

      case 'checkbox':
        return (
          <input
            type="checkbox"
            id={`custom-${field.id}`}
            checked={value === true || value === 'true'}
            onChange={(e) => handleCustomFieldChange(field.id, e.target.checked)}
          />
        );

      case 'number':
        return (
          <input
            type="number"
            id={`custom-${field.id}`}
            value={value}
            onChange={(e) => handleCustomFieldChange(field.id, e.target.value)}
            placeholder={field.placeholder}
            required={field.required}
            min={field.min}
            max={field.max}
            step={field.step || 1}
          />
        );

      default:
        return (
          <input
            type="text"
            id={`custom-${field.id}`}
            value={value}
            onChange={(e) => handleCustomFieldChange(field.id, e.target.value)}
            placeholder={field.placeholder}
            required={field.required}
          />
        );
    }
  };

  // If no template is found
  if (!template) {
    return (
      <div className="template-form-error">
        Template not found: {templateId}
      </div>
    );
  }

  // Get grouped fields
  const groupedFields = getGroupedFields();

  return (
    <div className="template-form-generator">
      {/* Basic fields */}
      {showBasicFields && (
        <div className="basic-fields">
          <div className="form-group">
            <label htmlFor="name">Name</label>
            <input
              type="text"
              id="name"
              name="name"
              value={formValues.name}
              onChange={handleBasicFieldChange}
              placeholder="Enter name"
              required
            />
          </div>

          <div className="form-group">
            <label htmlFor="description">Description</label>
            <textarea
              id="description"
              name="description"
              value={formValues.description}
              onChange={handleBasicFieldChange}
              placeholder="Enter description"
              rows={4}
            />
          </div>

          <div className="form-group">
            <label htmlFor="tags">Tags (comma-separated)</label>
            <input
              type="text"
              id="tags"
              name="tags"
              value={formValues.tags}
              onChange={handleBasicFieldChange}
              placeholder="e.g. important, reference, core"
            />
          </div>

          <div className="form-group">
            <label htmlFor="importance">Importance</label>
            <select
              id="importance"
              name="importance"
              value={formValues.importance}
              onChange={handleBasicFieldChange}
            >
              <option value="low">Low</option>
              <option value="medium">Medium</option>
              <option value="high">High</option>
            </select>
          </div>
        </div>
      )}

      {/* Template fields */}
      <div className="template-fields">
        {Object.entries(groupedFields).map(([groupName, fields]) => (
          <div key={groupName} className="field-group">
            <h3 className="group-title">{groupName}</h3>

            <div className="group-fields">
              {fields.map(field => (
                <div className="form-group" key={field.id}>
                  <label htmlFor={`custom-${field.id}`}>
                    {field.label}
                    {field.required && <span className="required-marker">*</span>}
                  </label>
                  {renderField(field)}
                  {field.description && (
                    <div className="field-description">{field.description}</div>
                  )}
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default TemplateFormGenerator;

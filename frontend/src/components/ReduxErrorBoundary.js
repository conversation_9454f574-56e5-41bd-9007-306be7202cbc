// src/components/ReduxErrorBoundary.js
import React, { Component } from 'react';
import { disableReduxForFeature } from '../utils/featureFlags';

/**
 * Error boundary component specifically for Redux migration
 * Provides fallback UI and rollback functionality
 */
class ReduxErrorBoundary extends Component {
  constructor(props) {
    super(props);
    this.state = { 
      hasError: false, 
      error: null, 
      errorInfo: null,
      errorCount: 0
    };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    // Update state with error details
    this.setState(prevState => ({
      errorInfo,
      errorCount: prevState.errorCount + 1
    }));
    
    // Log the error
    console.error('Redux Error caught by boundary:', error, errorInfo);
    
    // Report to monitoring service
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
    
    // Auto-rollback if error count exceeds threshold
    if (this.state.errorCount >= (this.props.errorThreshold || 3)) {
      this.handleRollback();
    }
  }
  
  handleRollback = () => {
    const { feature } = this.props;
    
    if (feature) {
      console.warn(`Rolling back Redux for feature: ${feature}`);
      disableReduxForFeature(feature);
      
      // Reload the application to apply changes
      if (this.props.reloadOnRollback !== false) {
        window.location.reload();
      }
    } else {
      console.warn('No feature specified for rollback');
    }
  };
  
  handleReset = () => {
    this.setState({ 
      hasError: false, 
      error: null, 
      errorInfo: null,
      errorCount: 0
    });
  };

  render() {
    const { hasError, error, errorInfo } = this.state;
    const { 
      feature, 
      fallback, 
      showDetails = true,
      showRollbackButton = true
    } = this.props;
    
    if (hasError) {
      // Use custom fallback if provided
      if (fallback) {
        return fallback(error, errorInfo, this.handleReset, this.handleRollback);
      }
      
      // Default fallback UI
      return (
        <div className="redux-error-boundary" style={styles.container}>
          <h2 style={styles.heading}>Redux Migration Error</h2>
          <p style={styles.message}>
            {feature 
              ? `There was an error in the Redux implementation of the ${feature} feature.` 
              : 'There was an error in the Redux implementation.'
            }
          </p>
          
          {showDetails && (
            <details style={styles.details}>
              <summary style={styles.summary}>Error Details</summary>
              <p style={styles.errorText}>{error && error.toString()}</p>
              <pre style={styles.stack}>
                {errorInfo && errorInfo.componentStack}
              </pre>
            </details>
          )}
          
          <div style={styles.buttonContainer}>
            <button 
              onClick={this.handleReset} 
              style={styles.resetButton}
            >
              Try Again
            </button>
            
            {showRollbackButton && feature && (
              <button 
                onClick={this.handleRollback} 
                style={styles.rollbackButton}
              >
                Rollback to Context API
              </button>
            )}
          </div>
        </div>
      );
    }

    // If no error, render children normally
    return this.props.children;
  }
}

// Inline styles for the error boundary
const styles = {
  container: {
    padding: '20px',
    margin: '20px 0',
    borderRadius: '8px',
    backgroundColor: '#fff8f8',
    border: '1px solid #ffcdd2',
    boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'
  },
  heading: {
    color: '#d32f2f',
    margin: '0 0 16px 0'
  },
  message: {
    fontSize: '16px',
    marginBottom: '16px'
  },
  details: {
    marginBottom: '16px'
  },
  summary: {
    cursor: 'pointer',
    fontWeight: 'bold'
  },
  errorText: {
    color: '#d32f2f',
    fontWeight: 'bold'
  },
  stack: {
    backgroundColor: '#f5f5f5',
    padding: '12px',
    borderRadius: '4px',
    overflow: 'auto',
    maxHeight: '200px',
    fontSize: '14px',
    whiteSpace: 'pre-wrap'
  },
  buttonContainer: {
    display: 'flex',
    gap: '12px'
  },
  resetButton: {
    padding: '8px 16px',
    backgroundColor: '#2196f3',
    color: 'white',
    border: 'none',
    borderRadius: '4px',
    cursor: 'pointer'
  },
  rollbackButton: {
    padding: '8px 16px',
    backgroundColor: '#ff9800',
    color: 'white',
    border: 'none',
    borderRadius: '4px',
    cursor: 'pointer'
  }
};

export default ReduxErrorBoundary;

// frontend/src/components/TemplateSuggestions.js
import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import './TemplateSuggestions.css';
import { getColorForCategory } from '../utils/colorMapping';
import { suggestTemplates, clearTemplateSuggestions } from '../redux/slices/semanticSearchSlice';

/**
 * Component for displaying template suggestions based on content
 * @param {Object} props - Component props
 * @returns {JSX.Element} TemplateSuggestions UI
 */
const TemplateSuggestions = ({ content, onTemplateSelect, limit = 5 }) => {
  const dispatch = useDispatch();
  
  // Get state from Redux
  const { templateSuggestions, isLoading, error } = useSelector(state => state.semanticSearch);
  
  // Local state
  const [isExpanded, setIsExpanded] = useState(false);
  
  // Get suggestions when content changes
  useEffect(() => {
    if (content && content.length > 50) {
      dispatch(suggestTemplates({ content, limit }));
    }
    
    // Clear suggestions when unmounting
    return () => {
      dispatch(clearTemplateSuggestions());
    };
  }, [dispatch, content, limit]);
  
  // Handle template selection
  const handleTemplateClick = (template) => {
    if (onTemplateSelect) {
      onTemplateSelect(template);
    }
  };
  
  // If no suggestions or loading, don't render
  if ((!templateSuggestions || templateSuggestions.length === 0) && !isLoading) {
    return null;
  }
  
  return (
    <div className={`template-suggestions ${isExpanded ? 'expanded' : ''}`}>
      <div className="suggestions-header" onClick={() => setIsExpanded(!isExpanded)}>
        <h3>
          {isLoading ? 'Finding relevant templates...' : 'Suggested Templates'}
          <span className="suggestion-count">{templateSuggestions.length}</span>
        </h3>
        <button className="expand-button">
          {isExpanded ? '▲' : '▼'}
        </button>
      </div>
      
      {isExpanded && (
        <div className="suggestions-content">
          {error && <div className="suggestions-error">{error}</div>}
          
          {isLoading ? (
            <div className="suggestions-loading">Analyzing content...</div>
          ) : templateSuggestions.length > 0 ? (
            <div className="suggestions-list">
              {templateSuggestions.map(template => (
                <div 
                  key={template.template_id} 
                  className="suggestion-card"
                  onClick={() => handleTemplateClick(template)}
                  style={{ borderLeftColor: getColorForCategory(template.category_id) }}
                >
                  <div className="suggestion-header">
                    <h4>{template.display_name}</h4>
                    <span className="suggestion-score">{Math.round(template.score * 100)}% match</span>
                  </div>
                  
                  <p className="suggestion-relevance">
                    {template.relevance_explanation || 'This template appears relevant to your content.'}
                  </p>
                  
                  <div className="suggestion-footer">
                    <span className="suggestion-category">{template.category_name}</span>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="no-suggestions">
              No relevant templates found for this content.
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default TemplateSuggestions;

import React, { useState, useEffect } from 'react';

/**
 * A simplified form for quickly creating world elements from other components
 *
 * @param {Object} props Component props
 * @param {Function} props.onSubmit Function to call when form is submitted
 * @param {Function} props.onCancel Function to call when form is cancelled
 * @param {Array} props.categories Available world building categories
 * @param {Array} props.parentElements Available parent elements for the selected category
 * @param {Function} props.onCategoryChange Function to call when category changes to fetch parent elements
 * @returns {JSX.Element} The QuickWorldElementForm component
 */
const QuickWorldElementForm = ({
  onSubmit,
  onCancel,
  categories = [],
  parentElements = [],
  onCategoryChange
}) => {
  // Form state
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    category_id: '',
    parent_id: '',
    has_children: false
  });

  // Error state
  const [errors, setErrors] = useState({});

  // Handle input changes
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    });

    // Clear error for this field
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: null
      });
    }
  };

  // Handle category change
  const handleCategoryChange = (e) => {
    const categoryId = e.target.value;
    setFormData({
      ...formData,
      category_id: categoryId,
      parent_id: '' // Reset parent when category changes
    });

    // Call the parent component's handler to fetch parent elements
    if (onCategoryChange) {
      onCategoryChange(categoryId);
    }
  };

  // Validate form
  const validateForm = () => {
    const newErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    }

    if (!formData.category_id) {
      newErrors.category_id = 'Category is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    e.stopPropagation();

    if (validateForm()) {
      try {
        await onSubmit(formData);
      } catch (error) {
        console.error('Error submitting form:', error);
        // Show error to user
        setErrors({
          ...errors,
          submit: error.message || 'Failed to create element. Please try again.'
        });
      }
    }

    return false; // Ensure no default form submission
  };

  // Get category display name
  const getCategoryDisplayName = (categoryId) => {
    if (!categoryId) return '';

    // Remove 'cat_' prefix and replace underscores with spaces
    return categoryId.replace('cat_', '').replace(/_/g, ' ');
  };

  return (
    <div className="quick-world-element-form">
      <h3>Create New World Element</h3>

      <div>
        {/* Name field */}
        <div className="form-group" style={{ marginBottom: '15px' }}>
          <label style={{ display: 'block', marginBottom: '5px', fontWeight: '500' }}>
            Name*
          </label>
          <input
            type="text"
            name="name"
            value={formData.name}
            onChange={handleChange}
            style={{
              width: '100%',
              padding: '8px 12px',
              borderRadius: '4px',
              border: errors.name ? '1px solid #dc3545' : '1px solid #ced4da',
              fontSize: '14px'
            }}
          />
          {errors.name && (
            <div style={{ color: '#dc3545', fontSize: '12px', marginTop: '4px' }}>
              {errors.name}
            </div>
          )}
        </div>

        {/* Category field */}
        <div className="form-group" style={{ marginBottom: '15px' }}>
          <label style={{ display: 'block', marginBottom: '5px', fontWeight: '500' }}>
            Category*
          </label>
          <select
            name="category_id"
            value={formData.category_id}
            onChange={handleCategoryChange}
            style={{
              width: '100%',
              padding: '8px 12px',
              borderRadius: '4px',
              border: errors.category_id ? '1px solid #dc3545' : '1px solid #ced4da',
              fontSize: '14px'
            }}
          >
            <option value="">Select a category</option>
            {categories.map(category => (
              <option key={category.category_id} value={category.category_id}>
                {getCategoryDisplayName(category.category_id)}
              </option>
            ))}
          </select>
          {errors.category_id && (
            <div style={{ color: '#dc3545', fontSize: '12px', marginTop: '4px' }}>
              {errors.category_id}
            </div>
          )}
        </div>

        {/* Parent element field (only show if category is selected) */}
        {formData.category_id && (
          <div className="form-group" style={{ marginBottom: '15px' }}>
            <label style={{ display: 'block', marginBottom: '5px', fontWeight: '500' }}>
              Parent Element (Optional)
            </label>
            <select
              name="parent_id"
              value={formData.parent_id}
              onChange={handleChange}
              style={{
                width: '100%',
                padding: '8px 12px',
                borderRadius: '4px',
                border: '1px solid #ced4da',
                fontSize: '14px'
              }}
            >
              <option value="">None (Top-level element)</option>
              {parentElements.map(element => (
                <option key={element.element_id} value={element.element_id}>
                  {element.name}
                </option>
              ))}
            </select>
          </div>
        )}

        {/* Description field */}
        <div className="form-group" style={{ marginBottom: '15px' }}>
          <label style={{ display: 'block', marginBottom: '5px', fontWeight: '500' }}>
            Description
          </label>
          <textarea
            name="description"
            value={formData.description}
            onChange={handleChange}
            rows={3}
            style={{
              width: '100%',
              padding: '8px 12px',
              borderRadius: '4px',
              border: '1px solid #ced4da',
              fontSize: '14px',
              resize: 'vertical'
            }}
          />
        </div>

        {/* Can have children checkbox */}
        <div className="form-group" style={{ marginBottom: '20px' }}>
          <label style={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}>
            <input
              type="checkbox"
              name="has_children"
              checked={formData.has_children}
              onChange={handleChange}
              style={{ marginRight: '8px', width: '16px', height: '16px' }}
            />
            <span>Can have sub-elements</span>
          </label>
        </div>

        {/* Form submission error */}
        {errors.submit && (
          <div style={{
            color: '#dc3545',
            padding: '10px',
            marginBottom: '15px',
            backgroundColor: '#f8d7da',
            borderRadius: '4px',
            textAlign: 'center'
          }}>
            {errors.submit}
          </div>
        )}

        {/* Form actions */}
        <div className="form-actions" style={{ display: 'flex', justifyContent: 'flex-end', gap: '10px' }}>
          <button
            type="button"
            onClick={onCancel}
            style={{
              padding: '8px 16px',
              borderRadius: '4px',
              border: 'none',
              background: '#6c757d',
              color: 'white',
              cursor: 'pointer',
              fontSize: '14px'
            }}
          >
            Cancel
          </button>
          <button
            type="button"
            onClick={handleSubmit}
            style={{
              padding: '8px 16px',
              borderRadius: '4px',
              border: 'none',
              background: '#0d6efd',
              color: 'white',
              cursor: 'pointer',
              fontSize: '14px'
            }}
          >
            Create Element
          </button>
        </div>
      </div>
    </div>
  );
};

export default QuickWorldElementForm;

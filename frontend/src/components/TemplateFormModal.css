/* frontend/src/components/TemplateFormModal.css */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.template-form-modal {
  background-color: var(--background-primary);
  border-radius: 8px;
  box-shadow: var(--shadow-lg);
  width: 90%;
  max-width: 700px;
  max-height: 90vh;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--background-secondary);
}

.modal-header h2 {
  margin: 0;
  font-size: 1.3rem;
  color: var(--text-primary);
}

.close-button {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 0;
  line-height: 1;
}

.close-button:hover {
  color: var(--text-primary);
}

.template-form-modal form {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.form-group label {
  font-weight: 500;
  color: var(--text-primary);
  font-size: 0.9rem;
}

.form-group input,
.form-group textarea,
.form-group select {
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--background-secondary);
  color: var(--text-primary);
  font-size: 0.9rem;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.1);
}

.template-fields,
.relationship-fields {
  border-top: 1px solid var(--border-color);
  padding-top: 15px;
  margin-top: 10px;
}

.template-fields h3,
.relationship-fields h3 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 1.1rem;
  color: var(--text-primary);
}

.relationship-description {
  margin: 5px 0 0;
  font-size: 0.8rem;
  color: var(--text-secondary);
  font-style: italic;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.create-button,
.cancel-button {
  padding: 8px 16px;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.create-button {
  background-color: var(--primary);
  color: white;
  border: none;
}

.create-button:hover {
  background-color: var(--primary-dark, #1976d2);
}

.cancel-button {
  background-color: transparent;
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
}

.cancel-button:hover {
  background-color: var(--background-tertiary);
  color: var(--text-primary);
}

.loading-indicator {
  padding: 15px;
  text-align: center;
  color: var(--text-secondary);
}

.error-message {
  padding: 10px 15px;
  margin: 10px 20px 0;
  background-color: rgba(244, 67, 54, 0.1);
  color: #f44336;
  border-radius: 4px;
  font-size: 0.9rem;
}

.required {
  color: #f44336;
  margin-left: 3px;
}

/* Dark theme adjustments */
.theme-dark .template-form-modal {
  background-color: var(--background-secondary);
}

.theme-dark .modal-header {
  background-color: var(--background-tertiary);
}

/* Responsive styles */
@media (max-width: 768px) {
  .template-form-modal {
    width: 95%;
    max-height: 95vh;
  }
}

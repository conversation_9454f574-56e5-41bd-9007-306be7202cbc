/* frontend/src/components/WorldElementList.css */

.world-element-list-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.category-tabs {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  margin-bottom: 20px;
  border-bottom: 1px solid #ddd;
  padding-bottom: 10px;
}

.category-tab {
  background: none;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  color: #666;
  transition: all 0.2s;
}

.category-tab:hover {
  background-color: #f5f5f5;
  color: #333;
}

.category-tab.active {
  background-color: #4caf50;
  color: white;
}

.element-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.element-list-header h2 {
  margin: 0;
  color: #333;
}

.element-actions {
  display: flex;
  gap: 10px;
}

.create-element-button,
.generate-element-button {
  background-color: #4caf50;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
}

.generate-element-button {
  background-color: #2196f3;
}

.create-element-button:disabled,
.generate-element-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.error-message {
  background-color: #ffebee;
  border: 1px solid #ffcdd2;
  border-radius: 4px;
  padding: 10px 15px;
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.error-message p {
  color: #c62828;
  margin: 0;
}

.error-message button {
  background: none;
  border: none;
  color: #c62828;
  cursor: pointer;
  font-weight: bold;
}

.loading-indicator {
  text-align: center;
  padding: 40px;
  color: #666;
  font-size: 16px;
}

.empty-state {
  text-align: center;
  padding: 40px;
  color: #666;
  background-color: #f5f5f5;
  border-radius: 8px;
  margin-top: 20px;
}

.empty-state p {
  margin: 5px 0;
}

/* Element Grid */
.element-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

/* Element Card */
.element-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
  transition: transform 0.2s, box-shadow 0.2s;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.element-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.element-card.selected {
  border: 2px solid #4caf50;
  box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.3);
}

.element-card-content {
  flex: 1;
}

.element-name {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 18px;
}

.element-description {
  margin: 0 0 15px 0;
  color: #555;
  font-size: 14px;
  line-height: 1.4;
}

.element-attributes {
  margin-top: 10px;
}

.attribute-item {
  margin-bottom: 5px;
  font-size: 13px;
}

.attribute-label {
  font-weight: 500;
  color: #666;
  margin-right: 5px;
}

.attribute-value {
  color: #333;
}

.element-card-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 15px;
  padding-top: 10px;
  border-top: 1px solid #eee;
}

.edit-button {
  background-color: #2196f3;
  color: white;
  border: none;
  padding: 5px 10px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.delete-button {
  background-color: #f44336;
  color: white;
  border: none;
  padding: 5px 10px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

/* Element Form */
.element-form-container {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  border: 1px solid #e0e0e0;
}

.element-form-container h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #333;
}

.element-form .form-group {
  margin-bottom: 15px;
}

.element-form label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #555;
}

.element-form input,
.element-form textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.element-form textarea {
  resize: vertical;
  min-height: 60px;
}

/* Count Selector Styles */
.count-selector {
  display: flex;
  align-items: center;
  gap: 15px;
}

.count-selector input[type="range"] {
  flex: 1;
  height: 5px;
  -webkit-appearance: none;
  appearance: none;
  background: #ddd;
  outline: none;
  border-radius: 5px;
}

.count-selector input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #2196f3;
  cursor: pointer;
}

.count-selector input[type="range"]::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #2196f3;
  cursor: pointer;
  border: none;
}

.count-display {
  font-size: 18px;
  font-weight: bold;
  color: #2196f3;
  min-width: 25px;
  text-align: center;
  background-color: #e3f2fd;
  padding: 5px 10px;
  border-radius: 4px;
}

.form-actions {
  display: flex;
  gap: 10px;
  margin-top: 20px;
}

.save-button {
  background-color: #4caf50;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
}

.save-button:disabled {
  background-color: #a5d6a7;
  cursor: not-allowed;
}

/* Cancel button styling is now in index.css */

/* Responsive Styles */
@media (max-width: 768px) {
  .element-grid {
    grid-template-columns: 1fr;
  }

  .element-list-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .element-actions {
    width: 100%;
  }

  .create-element-button,
  .generate-element-button {
    flex: 1;
  }

  .category-tabs {
    overflow-x: auto;
    white-space: nowrap;
    padding-bottom: 10px;
  }
}

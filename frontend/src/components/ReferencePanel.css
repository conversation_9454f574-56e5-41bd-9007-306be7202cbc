/* Removed .reference-panel class to avoid conflict with WritePage.css */
.reference-panel-content {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f9f9f9;
}

.reference-panel-header {
  padding: 15px;
  border-bottom: 1px solid #ddd;
  background-color: #f0f0f0;
}

.reference-panel-header h3 {
  margin: 0 0 10px 0;
  font-size: 16px;
  color: #333;
}

.reference-tabs {
  display: flex;
  margin-bottom: 10px;
  border-bottom: 1px solid #ddd;
}

.tab-button {
  padding: 8px 12px;
  background: none;
  border: none;
  border-bottom: 2px solid transparent;
  cursor: pointer;
  font-size: 14px;
  color: #666;
  flex: 1;
  text-align: center;
}

.tab-button:hover {
  background-color: #e9e9e9;
}

.tab-button.active {
  border-bottom: 2px solid #007bff;
  color: #007bff;
  font-weight: 500;
}

.reference-search-container {
  display: flex;
  padding: 10px;
  gap: 8px;
  align-items: center;
}

.reference-search {
  flex: 1;
}

.reference-search input {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.detailed-add-button {
  padding: 8px 12px;
  background-color: #0d6efd;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  white-space: nowrap;
}

.detailed-add-button:hover {
  background-color: #0b5ed7;
}

.reference-content {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
}

.reference-items {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.reference-card {
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 10px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.reference-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.reference-header h4 {
  margin: 0;
  font-size: 16px;
  color: #333;
}

.reference-actions {
  display: flex;
  gap: 5px;
}

.pin-button, .expand-button {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 16px;
  padding: 2px;
  color: #888;
}

.pin-button:hover, .expand-button:hover {
  color: #333;
}

.pin-button.pinned {
  color: #007bff;
}

.reference-details {
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px solid #eee;
  font-size: 14px;
}

.detail-item {
  margin-bottom: 8px;
}

.detail-item strong {
  color: #555;
}

.detail-item ul {
  margin: 5px 0;
  padding-left: 20px;
}

.no-items {
  padding: 20px;
  text-align: center;
  color: #888;
  font-style: italic;
}

.loading-indicator {
  padding: 20px;
  text-align: center;
  color: #888;
  font-style: italic;
}

/* New styles for character reference panel */
.reference-item {
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 15px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.reference-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  border-bottom: 1px solid #eee;
  padding-bottom: 8px;
}

.reference-item-header h4 {
  margin: 0;
  font-size: 18px;
  color: #333;
}

.character-role {
  background-color: #e9f5ff;
  color: #0066cc;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.character-headshot {
  float: right;
  margin: 0 0 10px 15px;
  max-width: 100px;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.character-headshot img {
  width: 100%;
  height: auto;
  display: block;
}

.character-basic-info {
  margin-bottom: 12px;
}

.info-item {
  margin-bottom: 4px;
  font-size: 14px;
}

.reference-item-description {
  margin-bottom: 15px;
  font-size: 14px;
  line-height: 1.5;
}

.reference-item-description strong {
  display: block;
  margin-bottom: 5px;
  color: #555;
  font-size: 15px;
}

.reference-item-description p {
  margin: 0;
  color: #333;
}

.character-traits,
.character-relationships,
.character-backstory,
.character-arc {
  margin-top: 15px;
  padding-top: 12px;
  border-top: 1px solid #eee;
  clear: both;
  position: relative;
  overflow: hidden;
  width: 100%;
  display: block;
}

.character-arc {
  margin-top: 20px;
}

.character-traits strong,
.character-relationships strong,
.character-backstory strong,
.character-arc strong {
  display: block;
  margin-bottom: 5px;
  color: #555;
  font-size: 15px;
}

.character-traits ul,
.character-relationships ul {
  margin: 5px 0;
  padding-left: 20px;
  list-style-type: disc;
}

.character-traits li,
.character-relationships li {
  margin-bottom: 8px;
  font-size: 14px;
}

.relationship-strength {
  margin-top: 4px;
  margin-bottom: 4px;
  display: flex;
  flex-direction: column;
  width: 100%;
}

.strength-label {
  font-size: 13px;
  color: #555;
  margin-bottom: 3px;
}

.strength-bar {
  height: 4px;
  background-color: #e2e8f0;
  border-radius: 2px;
  overflow: hidden;
  width: 100%;
}

.strength-fill {
  height: 100%;
  background-color: #4CAF50;
  border-radius: 2px;
  transition: width 0.3s ease;
}

.relationship-description {
  margin-top: 4px;
  font-size: 13px;
  color: #666;
  font-style: italic;
  padding-left: 10px;
  display: block;
  width: 100%;
  clear: both;
  line-height: 1.4;
}

.character-backstory p,
.character-arc p {
  margin: 5px 0;
  font-size: 14px;
  line-height: 1.5;
  color: #333;
  white-space: pre-wrap;
  overflow: visible;
  display: block;
  width: 100%;
  clear: both;
}

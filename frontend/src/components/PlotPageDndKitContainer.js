import React, { useState, useEffect } from 'react';
import { connect } from 'react-redux';
import PlotPageDndKit from './PlotPageDndKit';
import {
  fetchAllChapters as fetchChapters,
  fetchAllPlotEvents as fetchEvents,
  create<PERSON><PERSON><PERSON><PERSON> as addChapter,
  updateExisting<PERSON>hapter as updateChapter,
  remove<PERSON>hapter as deleteChapter,
  reorderChapters,
  assignEventToChapter,
  createPlotEvent as addEvent,
  updateExistingPlotEvent as updateEvent,
  removePlotEvent as deleteEvent,
  selectEvent,
  selectChapter,
  clearSelectedEvent,
  clearSelectedChapter,
  clearError,
  generateAIPlotEvent as generateAIEvent,
  selectAllPlotEvents,
  selectAllChapters,
  selectCurrentEvent,
  selectCurrentChapter,
  selectPlotLoading,
  selectPlotError
} from '../redux/slices/plotSlice';

import {
  selectCurrentBook,
  selectAllBooks,
  selectBooksLoading
} from '../redux/slices/bookSlice';

import { selectAllBrainstormCards, fetchAllBrainstormCards, createBrainstormCard } from '../redux/slices/brainstormSlice';

const PlotPageDndKitContainer = ({
  // Redux state
  bookId,
  bookTitle,
  chapters,
  events,
  selectedChapter,
  selectedEvent,
  error,
  isLoading,
  // Redux actions
  fetchChapters,
  fetchEvents,
  addChapter,
  updateChapter,
  deleteChapter,
  reorderChapters,
  assignEventToChapter,
  addEvent,
  updateEvent,
  deleteEvent,
  selectEvent,
  selectChapter,
  clearSelectedEvent,
  clearSelectedChapter,
  clearError,
  generateAIEvent
}) => {
  // Local state for forms
  const [showNewChapterForm, setShowNewChapterForm] = useState(false);
  const [newChapterTitle, setNewChapterTitle] = useState('');
  const [showNewEventForm, setShowNewEventForm] = useState(false);
  const [newEventTitle, setNewEventTitle] = useState('');
  const [newEventDescription, setNewEventDescription] = useState('');
  const [showAiPromptForm, setShowAiPromptForm] = useState(false);
  const [aiPrompt, setAiPrompt] = useState('');

  // Log when component mounts
  useEffect(() => {
    console.log('[DEBUG] PlotPageDndKitContainer: Component mounted');
  }, []);

  // Use effect to fetch data when component mounts or bookId changes
  useEffect(() => {
    console.log('[DEBUG] PlotPageDndKitContainer: bookId changed to:', bookId);
    if (bookId) {
      console.log('[DEBUG] PlotPageDndKitContainer: Fetching data for book:', bookId);
      fetchChapters(bookId);
      fetchEvents(bookId);
    } else {
      console.log('[DEBUG] PlotPageDndKitContainer: No bookId available');
    }
  }, [bookId, fetchChapters, fetchEvents]);

  // Helper function to get events for a specific chapter
  const getEventsByChapter = (chapterId) => {
    console.log('[DEBUG] getEventsByChapter called with chapterId:', chapterId);
    console.log('[DEBUG] All events:', events);
    console.log('[DEBUG] Events with matching chapter_id:', events.filter(event => event.chapter_id === chapterId));
    return events.filter(event => event.chapter_id === chapterId);
  };

  // Import from brainstorm function
  const importFromBrainstorm = async () => {
    console.log('[DEBUG] Import from brainstorm clicked');
    try {
      // Create some sample brainstorm cards for testing
      // In a real application, you would fetch these from the API
      const sampleCards = [
        {
          id: 'card-1',
          title: 'Character Introduction',
          content: 'Main character discovers a hidden artifact',
          type: 'character',
          characters: ['Main Character', 'Mentor'],
          locations: ['Ancient Temple']
        },
        {
          id: 'card-2',
          title: 'Plot Twist',
          content: 'The mentor is revealed to be the villain',
          type: 'plot',
          characters: ['Mentor', 'Main Character'],
          locations: ['Secret Lair']
        },
        {
          id: 'card-3',
          title: 'Climactic Battle',
          content: 'Final confrontation between hero and villain',
          type: 'action',
          characters: ['Main Character', 'Villain'],
          locations: ['Mountain Peak']
        }
      ];

      console.log('[DEBUG] Using sample brainstorm cards:', sampleCards);
      const brainstormCards = sampleCards;

      if (!brainstormCards || brainstormCards.length === 0) {
        console.log('[DEBUG] No brainstorm cards found to import');
        return;
      }

      // Convert brainstorm cards to plot events
      for (const card of brainstormCards) {
        // Skip cards that are already imported
        const alreadyImported = events.some(event =>
          event.title === card.title && event.description === card.content
        );

        if (alreadyImported) {
          console.log('[DEBUG] Card already imported, skipping:', card.title);
          continue;
        }

        // Create a new plot event from the brainstorm card
        const eventData = {
          title: card.title || 'Imported Event',
          description: card.content || '',
          event_type: card.type || 'general',
          characters: Array.isArray(card.characters) ? card.characters : [],
          locations: Array.isArray(card.locations) ? card.locations : [],
          is_in_bank: true, // Start in the unassigned bank
          chapter_id: null, // No chapter assigned initially
          // Add any additional fields needed for the event card
          sequence_number: 0,
          x: 0,
          y: 0,
          width: 200,
          height: 100,
          metadata: JSON.stringify({
            imported_from: 'brainstorm',
            original_id: card.id,
            import_date: new Date().toISOString(),
            card_type: card.type || 'general'
          })
        };

        console.log('[DEBUG] Creating plot event from brainstorm card:', eventData);
        await addEvent({ bookId, eventData });
      }

      // Refresh the events list
      fetchEvents(bookId);

    } catch (error) {
      console.error('[DEBUG] Error importing from brainstorm:', error);
    }
  };

  return (
    <PlotPageDndKit
      // Book data
      bookId={bookId}
      bookTitle={bookTitle}
      chapters={chapters}
      events={events}
      selectedChapter={selectedChapter}
      selectedEvent={selectedEvent}
      error={error}
      isLoading={isLoading}
      // Actions
      onFetchChapters={fetchChapters}
      onFetchEvents={fetchEvents}
      onAddChapter={addChapter}
      onUpdateChapter={updateChapter}
      onDeleteChapter={deleteChapter}
      onReorderChapters={reorderChapters}
      onAssignEventToChapter={assignEventToChapter}
      onAddEvent={addEvent}
      onUpdateEvent={updateEvent}
      onDeleteEvent={deleteEvent}
      onEventSelect={selectEvent}
      onChapterSelect={selectChapter}
      onClearSelectedEvent={clearSelectedEvent}
      onClearSelectedChapter={clearSelectedChapter}
      onClearError={clearError}
      onGenerateAIEvent={generateAIEvent}
      onImportFromBrainstorm={() => importFromBrainstorm()}
      // Form state
      showNewChapterForm={showNewChapterForm}
      setShowNewChapterForm={setShowNewChapterForm}
      newChapterTitle={newChapterTitle}
      setNewChapterTitle={setNewChapterTitle}
      showNewEventForm={showNewEventForm}
      setShowNewEventForm={setShowNewEventForm}
      newEventTitle={newEventTitle}
      setNewEventTitle={setNewEventTitle}
      newEventDescription={newEventDescription}
      setNewEventDescription={setNewEventDescription}
      showAiPromptForm={showAiPromptForm}
      setShowAiPromptForm={setShowAiPromptForm}
      aiPrompt={aiPrompt}
      setAiPrompt={setAiPrompt}
      // Helper functions
      getEventsByChapter={getEventsByChapter}
    />
  );
};

const mapStateToProps = (state) => {
  const currentBook = selectCurrentBook(state);
  return {
    bookId: currentBook?.book_id,
    bookTitle: currentBook?.title,
    chapters: selectAllChapters(state),
    events: selectAllPlotEvents(state),
    selectedChapter: selectCurrentChapter(state),
    selectedEvent: selectCurrentEvent(state),
    error: selectPlotError(state),
    isLoading: selectPlotLoading(state) || selectBooksLoading(state)
  };
};

const mapDispatchToProps = {
  fetchChapters,
  fetchEvents,
  addChapter,
  updateChapter,
  deleteChapter,
  reorderChapters,
  assignEventToChapter,
  addEvent,
  updateEvent,
  deleteEvent,
  selectEvent,
  selectChapter,
  clearSelectedEvent,
  clearSelectedChapter,
  clearError,
  generateAIEvent,
  fetchAllBrainstormCards,
  createBrainstormCard
  // We'll handle importFromBrainstorm in the component
};

export default connect(mapStateToProps, mapDispatchToProps)(PlotPageDndKitContainer);

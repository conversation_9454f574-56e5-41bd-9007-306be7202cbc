/* frontend/src/components/PlotSidebar.css */
.plot-sidebar {
  position: fixed;
  width: 20%;
  min-width: 200px;
  left: -20%;
  top: 0;
  transition: left 0.3s ease;
  z-index: 20;
}

.plot-sidebar.plot-open {
  left: 0;
}

.plot-sidebar-handle {
  position: absolute;
  width: 10px;
  height: 50px;
  background: #d0d0d0;
  right: -10px;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  z-index: 21;
}

.plot-sidebar-content {
  padding: 20px;
}

.form-group {
  margin-bottom: 15px;
}

.character-input {
  display: flex;
  gap: 5px;
}

.character-list {
  margin-top: 5px;
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.character-tag {
  padding: 2px 6px;
  border-radius: 12px;
  font-size: 12px;
}
/* PlotPage.css */
.plot-page {
  display: flex;
  flex-direction: column;
  min-height: calc(100vh - 60px);
  padding: 20px;
  overflow-y: auto;
}

.plot-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.plot-header-actions {
  display: flex;
  gap: 10px;
}

.plot-content {
  display: flex;
  flex: 1;
  gap: 20px;
  overflow-y: auto;
  min-height: 500px;
}

.chapters-container {
  flex: 3;
  overflow-y: auto;
  border: 1px solid #ddd;
  border-radius: 5px;
  padding: 10px;
  background-color: #f9f9f9;
  max-height: 70vh;
}

.chapters {
  display: flex;
  flex-direction: column;
  gap: 15px;
  min-height: 100%;
}

.chapter-container {
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 5px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
}

.chapter-container.selected {
  border-color: #4a90e2;
  box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.3);
}

.chapter-container.dragging {
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
}

.chapter-header {
  display: flex;
  align-items: center;
  padding: 10px 15px;
  background-color: #f0f0f0;
  border-bottom: 1px solid #ddd;
}

.chapter-header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex: 1;
  cursor: pointer;
}

.chapter-header h3 {
  margin: 0;
  font-size: 1.2rem;
}

.chapter-actions {
  display: flex;
  gap: 5px;
  align-items: center;
}

.chapter-events {
  padding: 10px;
  min-height: 50px;
  max-height: 300px;
  overflow-y: auto;
}

.chapter-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding: 10px;
  border-top: 1px solid #eee;
}

.event-card-wrapper {
  position: relative;
  margin-bottom: 10px;
  border-radius: 5px;
  transition: transform 0.2s ease;
}

.event-card-wrapper:hover {
  transform: translateY(-2px);
}

.event-card-wrapper.selected {
  border: 2px solid #007bff;
  border-radius: 6px;
  padding: 2px;
}

.event-card-wrapper .event-actions {
  display: flex;
  justify-content: flex-end;
  gap: 5px;
  padding: 5px;
  background-color: #f8f9fa;
  border-radius: 0 0 5px 5px;
  border: 1px solid #ddd;
  border-top: none;
}

.event-card-wrapper .event-actions button {
  padding: 3px 8px;
  font-size: 12px;
  border-radius: 3px;
  border: 1px solid #ccc;
  background-color: white;
  cursor: pointer;
}

.event-card-wrapper .event-actions button:hover {
  background-color: #e9ecef;
}

.event-card-wrapper .move-to-chapter {
  padding: 3px 8px;
  font-size: 12px;
  border-radius: 3px;
  border: 1px solid #ccc;
  background-color: white;
}

.event-card-wrapper .move-to-unassigned {
  background-color: #f8d7da;
  color: #721c24;
  border-color: #f5c6cb;
}

.event-header {
  display: flex;
  align-items: flex-start;
  margin-bottom: 10px;
}

/* Event header content styling is defined below */

.event-title-row {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 5px;
  margin-bottom: 5px;
}

.event-title-row h4 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: #333;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.event-header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex: 1;
  min-width: 0;
}

.event-header h4 {
  margin: 0;
  font-size: 1rem;
}

.event-actions {
  display: flex;
  gap: 5px;
  align-items: center;
  flex-wrap: wrap;
}

.move-to-chapter {
  padding: 4px 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: #f8f8f8;
  font-size: 0.8rem;
  cursor: pointer;
}

.move-to-unassigned {
  padding: 4px 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: #f8f8f8;
  font-size: 0.8rem;
  cursor: pointer;
}

.event-card p {
  margin: 0;
  font-size: 0.9rem;
  color: #555;
}

.unassigned-events-container {
  flex: 1;
  overflow-y: auto;
  border: 1px solid #ddd;
  border-radius: 5px;
  padding: 10px;
  background-color: #f9f9f9;
  display: flex;
  flex-direction: column;
  max-height: 70vh;
}

.unassigned-events-container h3 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 1.1rem;
}

.unassigned-events {
  flex: 1;
  overflow-y: auto;
  min-height: 100px;
  max-height: 69vh;
}

.unassigned-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding-top: 10px;
  border-top: 1px solid #eee;
  margin-top: 10px;
}

.empty-chapter,
.empty-unassigned,
.empty-chapters {
  padding: 20px;
  text-align: center;
  color: #888;
  font-style: italic;
}

.new-chapter-form,
.new-event-form,
.ai-prompt-form {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: white;
  border-radius: 5px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  padding: 20px;
  width: 400px;
  max-width: 90%;
  z-index: 1000;
}

.new-chapter-form h4,
.new-event-form h4,
.ai-prompt-form h4 {
  margin-top: 0;
  margin-bottom: 15px;
}

.new-chapter-form input,
.new-event-form input,
.new-event-form textarea,
.ai-prompt-form textarea,
.event-edit-form input,
.event-edit-form textarea,
.chapter-edit-form input {
  width: 100%;
  padding: 8px;
  margin-bottom: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.new-event-form textarea,
.ai-prompt-form textarea,
.event-edit-form textarea {
  min-height: 100px;
  resize: vertical;
}

.form-actions,
.event-edit-actions,
.chapter-edit-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.error-message {
  background-color: #ffebee;
  color: #c62828;
  padding: 10px 15px;
  border-radius: 4px;
  margin-bottom: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.error-message p {
  margin: 0;
}

.error-message button {
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  color: #c62828;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  font-style: italic;
  color: #666;
}

button {
  padding: 6px 12px;
  background-color: #4a90e2;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
}

button:hover {
  background-color: #3a7bc8;
}

button:disabled {
  background-color: #a0c4f0;
  cursor: not-allowed;
}

.event-actions button,
.chapter-actions button {
  padding: 3px 8px;
  font-size: 0.8rem;
}

.import-button {
  margin-left: 10px;
  background-color: #5cb85c;
}

.import-button:hover {
  background-color: #4cae4c;
}

.plot-header-actions {
  display: flex;
  gap: 10px;
}

.import-button {
  background-color: #5cb85c;
}

.import-button:hover {
  background-color: #4cae4c;
}

.plot-header-actions {
  display: flex;
  gap: 10px;
}

.import-button {
  background-color: #5cb85c;
}

.import-button:hover {
  background-color: #4cae4c;
}

/* Drag handle styles */
.chapter-drag-handle,
.event-drag-handle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 4px;
  background-color: #f0f0f0;
  cursor: grab;
  margin-right: 8px;
  touch-action: none;
}

.chapter-drag-handle:hover,
.event-drag-handle:hover {
  background-color: #e0e0e0;
}

.chapter-drag-handle:active,
.event-drag-handle:active {
  cursor: grabbing;
  background-color: #d0d0d0;
}

.drag-icon {
  font-size: 14px;
  color: #666;
}

.chapter-container.dragging {
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
  opacity: 0.8;
  z-index: 999;
  pointer-events: none;
}

.event-card {
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 5px;
  padding: 10px;
  margin-bottom: 10px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.event-card:hover {
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
}

.event-card.selected {
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.event-card.dragging {
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
  opacity: 0.8;
  z-index: 999;
  pointer-events: none;
}

/* Drop zone highlighting */
.chapter-events.drop-active,
.unassigned-events.drop-active {
  background-color: rgba(74, 144, 226, 0.1);
  border: 2px dashed #4a90e2;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.chapter-events.drop-over,
.unassigned-events.drop-over {
  background-color: rgba(74, 144, 226, 0.2);
  border: 2px solid #4a90e2;
}

/* Event metadata styles */
.event-metadata {
  margin-top: 10px;
  font-size: 0.9em;
  border-top: 1px solid #eee;
  padding-top: 8px;
}

.event-metadata > div {
  margin-bottom: 6px;
}

.event-metadata > div:last-child {
  margin-bottom: 0;
}

.event-characters strong,
.event-locations strong,
.event-type-tag strong {
  display: inline-block;
  min-width: 80px;
  font-weight: 600;
  color: #555;
}

.event-characters,
.event-locations,
.event-type-tag {
  margin-bottom: 5px;
}

.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  margin-top: 3px;
}

.character-tag {
  background-color: #e3f2fd;
  color: #0d47a1;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 0.85em;
  border: 1px solid rgba(13, 71, 161, 0.2);
  display: inline-block;
}

.location-tag {
  background-color: #e8f5e9;
  color: #1b5e20;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 0.85em;
  border: 1px solid rgba(27, 94, 32, 0.2);
  display: inline-block;
}

.event-type-badge {
  display: inline-block;
  border-radius: 4px;
  padding: 2px 6px;
  font-size: 0.8em;
  font-weight: bold;
  color: #333;
}

.event-type-tag {
  display: inline-block;
  background-color: #fff3e0;
  color: #e65100;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 0.85em;
  margin-top: 5px;
}

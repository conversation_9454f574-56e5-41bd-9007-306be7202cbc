// frontend/src/components/WorldConsistencyAnalyzer.js
import React, { useState } from 'react';
import { useSelector } from 'react-redux';
import { analyzeWorldConsistency } from '../services/worldBuildingApiService';
import { selectCurrentBookId } from '../redux/slices/worldBuildingSlice';
import './WorldConsistencyAnalyzer.css';

/**
 * Component for analyzing world consistency using AI
 * @param {Object} props - Component props
 * @returns {JSX.Element} WorldConsistencyAnalyzer UI
 */
const WorldConsistencyAnalyzer = ({ onAnalysisComplete }) => {
  const bookId = useSelector(selectCurrentBookId);
  
  // State for the analyzer
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysis, setAnalysis] = useState(null);
  const [error, setError] = useState(null);
  
  // Handle analysis
  const handleAnalyze = async () => {
    try {
      setIsAnalyzing(true);
      setError(null);
      setAnalysis(null);
      
      const consistencyAnalysis = await analyzeWorldConsistency(bookId);
      
      setAnalysis(consistencyAnalysis);
      
      if (onAnalysisComplete && typeof onAnalysisComplete === 'function') {
        onAnalysisComplete(consistencyAnalysis);
      }
    } catch (error) {
      console.error('Error analyzing world consistency:', error);
      setError(error.message || 'Failed to analyze world consistency');
    } finally {
      setIsAnalyzing(false);
    }
  };
  
  // Render analysis results
  const renderAnalysisResults = () => {
    if (!analysis) return null;
    
    return (
      <div className="analysis-results">
        <h4>Consistency Analysis</h4>
        
        {analysis.overall_score && (
          <div className="overall-score">
            <span className="score-label">Overall Consistency Score:</span>
            <div className="score-bar">
              <div 
                className="score-fill" 
                style={{ width: `${analysis.overall_score}%` }}
                data-score={`${analysis.overall_score}%`}
              />
            </div>
          </div>
        )}
        
        {analysis.strengths && analysis.strengths.length > 0 && (
          <div className="analysis-section strengths">
            <h5>Strengths</h5>
            <ul>
              {analysis.strengths.map((strength, index) => (
                <li key={`strength-${index}`}>{strength}</li>
              ))}
            </ul>
          </div>
        )}
        
        {analysis.inconsistencies && analysis.inconsistencies.length > 0 && (
          <div className="analysis-section inconsistencies">
            <h5>Potential Inconsistencies</h5>
            <ul>
              {analysis.inconsistencies.map((inconsistency, index) => (
                <li key={`inconsistency-${index}`}>{inconsistency}</li>
              ))}
            </ul>
          </div>
        )}
        
        {analysis.suggestions && analysis.suggestions.length > 0 && (
          <div className="analysis-section suggestions">
            <h5>Suggestions for Improvement</h5>
            <ul>
              {analysis.suggestions.map((suggestion, index) => (
                <li key={`suggestion-${index}`}>{suggestion}</li>
              ))}
            </ul>
          </div>
        )}
      </div>
    );
  };
  
  return (
    <div className="world-consistency-analyzer">
      <h3>World Consistency Analysis</h3>
      
      <div className="analyzer-description">
        <p>
          This tool uses AI to analyze your world elements and identify potential inconsistencies,
          strengths, and areas for improvement. It helps ensure your world is coherent and believable.
        </p>
      </div>
      
      {error && <div className="error-message">{error}</div>}
      
      <button
        className="analyze-button"
        onClick={handleAnalyze}
        disabled={isAnalyzing}
      >
        {isAnalyzing ? 'Analyzing...' : 'Analyze World Consistency'}
      </button>
      
      {renderAnalysisResults()}
    </div>
  );
};

export default WorldConsistencyAnalyzer;

// frontend/src/components/CharacterList.js
import React, { useState } from 'react';
import './CharacterList.css';

/**
 * CharacterList component for displaying and managing characters
 * @param {Object} props - Component props
 * @returns {JSX.Element} CharacterList UI
 */
const CharacterList = ({
  characters,
  selectedCharacter,
  isLoading,
  isSaving,
  error,
  onSelectCharacter,
  onCreateCharacter,
  onUpdateCharacter,
  onDeleteCharacter,
  onGenerateCharacter,
  onClearError
}) => {
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [showGenerateForm, setShowGenerateForm] = useState(false);
  const [editingCharacterId, setEditingCharacterId] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    role: '',
    description: '',
    traits: '',
    goals: '',
    backstory: ''
  });
  const [generatePrompt, setGeneratePrompt] = useState('');

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle create character form submission
  const handleCreateSubmit = (e) => {
    e.preventDefault();
    onCreateCharacter(formData);
    resetForm();
  };

  // Handle edit character form submission
  const handleEditSubmit = (e) => {
    e.preventDefault();
    onUpdateCharacter({ characterId: editingCharacterId, characterData: formData });
    resetForm();
  };

  // Handle generate character form submission
  const handleGenerateSubmit = (e) => {
    e.preventDefault();
    onGenerateCharacter({ prompt: generatePrompt });
    setGeneratePrompt('');
    setShowGenerateForm(false);
  };

  // Reset form state
  const resetForm = () => {
    setFormData({
      name: '',
      role: '',
      description: '',
      traits: '',
      goals: '',
      backstory: ''
    });
    setEditingCharacterId(null);
    setShowCreateForm(false);
  };

  // Start editing a character
  const handleStartEdit = (character) => {
    setEditingCharacterId(character.id);
    setFormData({
      name: character.name || '',
      role: character.role || '',
      description: character.description || '',
      traits: character.traits || '',
      goals: character.goals || '',
      backstory: character.backstory || ''
    });
    setShowCreateForm(false);
    setShowGenerateForm(false);
  };

  // Handle character deletion
  const handleDeleteCharacter = (characterId) => {
    if (window.confirm('Are you sure you want to delete this character? This action cannot be undone.')) {
      onDeleteCharacter(characterId);
    }
  };

  // Render create/edit character form
  const renderCharacterForm = () => {
    const isEditing = editingCharacterId !== null;
    const title = isEditing ? 'Edit Character' : 'Create New Character';
    const submitText = isEditing ? 'Save Changes' : 'Create Character';
    const submitAction = isEditing ? handleEditSubmit : handleCreateSubmit;
    
    return (
      <div className="character-form-container">
        <h3>{title}</h3>
        <form onSubmit={submitAction} className="character-form">
          <div className="form-group">
            <label htmlFor="name">Name</label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              required
            />
          </div>
          
          <div className="form-group">
            <label htmlFor="role">Role</label>
            <select
              id="role"
              name="role"
              value={formData.role}
              onChange={handleInputChange}
              required
            >
              <option value="">Select Role</option>
              <option value="protagonist">Protagonist</option>
              <option value="antagonist">Antagonist</option>
              <option value="supporting">Supporting Character</option>
              <option value="mentor">Mentor</option>
              <option value="sidekick">Sidekick</option>
              <option value="love_interest">Love Interest</option>
              <option value="foil">Foil</option>
              <option value="other">Other</option>
            </select>
          </div>
          
          <div className="form-group">
            <label htmlFor="description">Description</label>
            <textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              rows={3}
              placeholder="Physical appearance, mannerisms, etc."
            />
          </div>
          
          <div className="form-group">
            <label htmlFor="traits">Traits</label>
            <textarea
              id="traits"
              name="traits"
              value={formData.traits}
              onChange={handleInputChange}
              rows={2}
              placeholder="Personality traits, strengths, weaknesses"
            />
          </div>
          
          <div className="form-group">
            <label htmlFor="goals">Goals</label>
            <textarea
              id="goals"
              name="goals"
              value={formData.goals}
              onChange={handleInputChange}
              rows={2}
              placeholder="Character's motivations and objectives"
            />
          </div>
          
          <div className="form-group">
            <label htmlFor="backstory">Backstory</label>
            <textarea
              id="backstory"
              name="backstory"
              value={formData.backstory}
              onChange={handleInputChange}
              rows={4}
              placeholder="Character's history and background"
            />
          </div>
          
          <div className="form-actions">
            <button type="submit" className="save-button" disabled={isSaving}>
              {isSaving ? 'Saving...' : submitText}
            </button>
            <button 
              type="button" 
              className="cancel-button"
              onClick={resetForm}
              disabled={isSaving}
            >
              Cancel
            </button>
          </div>
        </form>
      </div>
    );
  };

  // Render generate character form
  const renderGenerateForm = () => {
    return (
      <div className="character-form-container">
        <h3>Generate Character with AI</h3>
        <form onSubmit={handleGenerateSubmit} className="character-form">
          <div className="form-group">
            <label htmlFor="generatePrompt">Character Prompt</label>
            <textarea
              id="generatePrompt"
              name="generatePrompt"
              value={generatePrompt}
              onChange={(e) => setGeneratePrompt(e.target.value)}
              rows={4}
              placeholder="Describe the character you want to generate (e.g., 'A wise old mentor with a mysterious past')"
              required
            />
          </div>
          
          <div className="form-actions">
            <button type="submit" className="save-button" disabled={isLoading}>
              {isLoading ? 'Generating...' : 'Generate Character'}
            </button>
            <button 
              type="button" 
              className="cancel-button"
              onClick={() => setShowGenerateForm(false)}
              disabled={isLoading}
            >
              Cancel
            </button>
          </div>
        </form>
      </div>
    );
  };

  // Render character card
  const renderCharacterCard = (character) => {
    const isSelected = selectedCharacter && selectedCharacter.id === character.id;
    
    return (
      <div 
        key={character.id} 
        className={`character-card ${isSelected ? 'selected' : ''}`}
        onClick={() => onSelectCharacter(character)}
      >
        <div className="character-card-content">
          <h3 className="character-name">{character.name}</h3>
          {character.role && <span className="character-role">{character.role}</span>}
          {character.description && <p className="character-description">{character.description}</p>}
          
          {(character.traits || character.goals) && (
            <div className="character-attributes">
              {character.traits && (
                <div className="character-traits">
                  <strong>Traits:</strong> {character.traits}
                </div>
              )}
              {character.goals && (
                <div className="character-goals">
                  <strong>Goals:</strong> {character.goals}
                </div>
              )}
            </div>
          )}
        </div>
        
        <div className="character-card-actions">
          <button 
            className="edit-button"
            onClick={(e) => {
              e.stopPropagation();
              handleStartEdit(character);
            }}
          >
            Edit
          </button>
          <button 
            className="delete-button"
            onClick={(e) => {
              e.stopPropagation();
              handleDeleteCharacter(character.id);
            }}
          >
            Delete
          </button>
        </div>
      </div>
    );
  };

  return (
    <div className="character-list-container">
      <div className="character-list-header">
        <h2>Characters</h2>
        <div className="character-actions">
          <button 
            className="create-character-button"
            onClick={() => {
              setShowCreateForm(true);
              setShowGenerateForm(false);
              setEditingCharacterId(null);
            }}
            disabled={showCreateForm || editingCharacterId !== null || showGenerateForm}
          >
            Create Character
          </button>
          <button 
            className="generate-character-button"
            onClick={() => {
              setShowGenerateForm(true);
              setShowCreateForm(false);
              setEditingCharacterId(null);
            }}
            disabled={showCreateForm || editingCharacterId !== null || showGenerateForm}
          >
            Generate with AI
          </button>
        </div>
      </div>
      
      {error && (
        <div className="error-message">
          <p>{error}</p>
          <button onClick={onClearError}>Dismiss</button>
        </div>
      )}
      
      {showCreateForm && renderCharacterForm()}
      {showGenerateForm && renderGenerateForm()}
      {editingCharacterId !== null && renderCharacterForm()}
      
      {isLoading && !showGenerateForm ? (
        <div className="loading-indicator">Loading characters...</div>
      ) : characters.length === 0 ? (
        <div className="empty-state">
          <p>You don't have any characters yet.</p>
          <p>Create your first character to get started!</p>
        </div>
      ) : (
        <div className="character-grid">
          {characters.map(character => renderCharacterCard(character))}
        </div>
      )}
    </div>
  );
};

export default CharacterList;

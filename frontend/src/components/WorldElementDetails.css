/* frontend/src/components/WorldElementDetails.css */

.world-element-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  background-color: var(--background-primary);
}

.world-element-details-container {
  flex: 1;
  min-width: 300px;
  padding: 20px;
  overflow-y: auto;
  background-color: var(--background-primary);
  border-left: 1px solid var(--border-color);
}

.element-content {
  display: flex;
  flex: 1;
  overflow: hidden;
  height: calc(100% - 60px); /* Adjust based on breadcrumb height */
}

.element-sidebar {
  width: 250px;
  overflow-y: auto;
  padding: 15px;
  border-right: 1px solid var(--border-color);
}

.element-main-content {
  flex: 1;
  overflow-y: auto;
  padding: 15px;
  display: flex;
  flex-direction: column;
}

.loading-indicator {
  text-align: center;
  padding: 40px;
  color: #666;
  font-size: 16px;
}

.no-element-selected {
  text-align: center;
  padding: 40px;
  background-color: var(--background-secondary);
  border-radius: 8px;
  margin: 20px;
}

.no-element-selected h2 {
  margin-top: 0;
  color: var(--text-primary);
}

.no-element-selected p {
  color: var(--text-secondary);
}

.error-message {
  background-color: #ffebee;
  border: 1px solid #ffcdd2;
  border-radius: 4px;
  padding: 10px 15px;
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.error-message p {
  color: #c62828;
  margin: 0;
}

.error-message button {
  background: none;
  border: none;
  color: #c62828;
  cursor: pointer;
  font-weight: bold;
}

/* Element Info */
.element-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.element-header h2 {
  margin: 0 0 5px 0;
  color: #333;
}

.element-type-badge {
  display: inline-block;
  padding: 3px 8px;
  border-radius: 12px;
  font-size: 0.7rem;
  text-transform: uppercase;
  font-weight: bold;
  background-color: var(--background-tertiary);
  color: var(--text-secondary);
}

.element-type-badge.organization {
  background-color: rgba(33, 150, 243, 0.2);
  color: #2196f3;
}

.element-type-badge.law {
  background-color: rgba(156, 39, 176, 0.2);
  color: #9c27b0;
}

.element-type-badge.law_clause {
  background-color: rgba(103, 58, 183, 0.2);
  color: #673ab7;
}

.element-type-badge.geography {
  background-color: rgba(76, 175, 80, 0.2);
  color: #4caf50;
}

.element-type-badge.location {
  background-color: rgba(255, 152, 0, 0.2);
  color: #ff9800;
}

.element-type-badge.magic_system {
  background-color: rgba(233, 30, 99, 0.2);
  color: #e91e63;
}

.edit-button {
  background-color: #2196f3;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
}

.back-to-parent-button {
  background-color: var(--background-tertiary);
  color: var(--text-primary);
  border: none;
  padding: 5px 10px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.85rem;
  margin-bottom: 10px;
  display: block;
  transition: background-color 0.2s;
}

.back-to-parent-button:hover {
  background-color: var(--background-secondary);
}

.info-section {
  margin-bottom: 20px;
}

.info-section h3 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 16px;
  border-bottom: 1px solid #eee;
  padding-bottom: 5px;
}

.info-section p {
  margin: 0;
  color: #555;
  line-height: 1.5;
}

.custom-fields-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
}

.custom-field-item {
  background-color: var(--background-tertiary);
  padding: 12px;
  border-radius: 6px;
  box-shadow: var(--shadow-sm);
}

.custom-field-item h4 {
  margin: 0 0 5px 0;
  color: var(--text-primary);
  font-size: 0.9rem;
  font-weight: 600;
}

.custom-field-item p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 0.9rem;
  line-height: 1.4;
}

/* Element Edit Form */
.element-edit-form {
  margin-top: 20px;
}

.element-edit-form .form-group {
  margin-bottom: 15px;
}

.element-edit-form label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #555;
}

.element-edit-form input,
.element-edit-form textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.element-edit-form textarea {
  resize: vertical;
  min-height: 60px;
}

.form-actions {
  display: flex;
  gap: 10px;
  margin-top: 20px;
}

.save-button {
  background-color: #4caf50;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
}

/* Cancel button styling is now in index.css */

/* Relationships section */
.relationships-section {
  margin-top: 20px;
}

.relationships-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.relationship-item {
  background-color: var(--background-tertiary);
  border-radius: 6px;
  padding: 12px;
  box-shadow: var(--shadow-sm);
}

.relationship-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 0.9rem;
}

.relationship-direction {
  font-weight: 600;
  color: var(--text-primary);
}

.relationship-type {
  background-color: var(--background-secondary);
  padding: 2px 8px;
  border-radius: 12px;
  color: var(--text-secondary);
  font-weight: 600;
}

.relationship-other {
  font-weight: 600;
  color: var(--text-primary);
}

/* Relationship type colors */
.relationship-type[data-type="located_in"] {
  background-color: rgba(76, 175, 80, 0.2);
  color: #4caf50;
}

.relationship-type[data-type="contains"] {
  background-color: rgba(33, 150, 243, 0.2);
  color: #2196f3;
}

.relationship-type[data-type="connected_to"] {
  background-color: rgba(255, 152, 0, 0.2);
  color: #ff9800;
}

.relationship-type[data-type="related_to"] {
  background-color: rgba(156, 39, 176, 0.2);
  color: #9c27b0;
}

.relationship-type[data-type="opposes"] {
  background-color: rgba(244, 67, 54, 0.2);
  color: #f44336;
}

.relationship-type[data-type="allies_with"] {
  background-color: rgba(0, 150, 136, 0.2);
  color: #009688;
}

.relationship-description {
  margin: 0 0 10px 0;
  font-size: 0.85rem;
  color: var(--text-secondary);
  line-height: 1.4;
}

.view-related-button {
  background-color: var(--background-secondary);
  color: var(--text-primary);
  border: none;
  padding: 5px 10px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.8rem;
  transition: background-color 0.2s;
}

.view-related-button:hover {
  background-color: var(--primary);
  color: white;
}

/* Magic System Edit Container */
.magic-system-edit-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.edit-magic-system-button {
  padding: 6px 12px;
  background-color: var(--primary);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  font-size: 0.9rem;
  transition: background-color 0.2s;
}

.edit-magic-system-button:hover {
  background-color: var(--button-primary-hover);
}

/* Sub-elements section */
.sub-elements-section {
  margin-top: 20px;
}

/* Magic system sub-elements */
.magic-system-sub-elements {
  margin-top: 30px;
}

.magic-system-sub-elements .sub-elements-header {
  background-color: var(--background-secondary);
  padding: 15px;
  border-radius: 8px 8px 0 0;
  border-bottom: 2px solid var(--primary);
  margin-bottom: 0;
}

.magic-system-sub-elements .sub-elements-header h3 {
  color: var(--text-primary);
  font-size: 1.2rem;
  margin: 0;
}

.sub-elements-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  padding-bottom: 5px;
  border-bottom: 1px solid var(--border-color);
}

.sub-elements-header h3 {
  margin: 0;
  color: var(--text-primary);
  font-size: 16px;
}

.sub-element-actions {
  display: flex;
  gap: 8px;
}

.create-sub-element-button {
  padding: 5px 10px;
  background-color: var(--primary);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.8rem;
  transition: background-color 0.2s;
}

.create-sub-element-button:hover {
  background-color: var(--button-primary-hover);
}

.tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.tag-item {
  font-size: 0.8rem;
  padding: 2px 8px;
  border-radius: 12px;
  background: var(--background-tertiary);
  color: var(--text-secondary);
}

/* Template information styles */
.template-info-section {
  border-top: 1px dashed var(--border-color);
  padding-top: 15px;
  margin-top: 20px;
}

.template-details {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
}

.template-detail {
  background-color: var(--background-tertiary);
  padding: 12px;
  border-radius: 6px;
  box-shadow: var(--shadow-sm);
  border-left: 3px solid var(--secondary);
}

.detail-label {
  display: block;
  margin: 0 0 5px 0;
  color: var(--text-primary);
  font-size: 0.9rem;
  font-weight: 600;
}

.detail-value {
  margin: 0;
  color: var(--text-secondary);
  font-size: 0.9rem;
  line-height: 1.4;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .attributes-list {
    grid-template-columns: 1fr;
  }

  .sub-elements-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
}

/* Tab system */
.element-tabs {
  display: flex;
  border-bottom: 1px solid var(--border-color);
  margin-bottom: 20px;
}

.element-tab {
  padding: 10px 16px;
  background: none;
  border: none;
  border-bottom: 2px solid transparent;
  cursor: pointer;
  font-size: 0.95rem;
  font-weight: 500;
  color: var(--text-secondary);
  transition: all 0.2s ease;
}

.element-tab:hover {
  color: var(--text-primary);
  background-color: var(--background-tertiary);
}

.element-tab.active {
  color: var(--primary);
  border-bottom: 2px solid var(--primary);
  background-color: var(--background-tertiary);
}

.element-tab-content {
  padding: 5px 0;
  overflow-y: auto;
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* Relationships tab content */
.relationships-tab-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
  overflow-y: auto;
}

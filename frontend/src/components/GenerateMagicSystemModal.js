// frontend/src/components/GenerateMagicSystemModal.js
import React, { useState } from 'react';
import './GenerateMagicSystemModal.css';

/**
 * Modal for generating magic systems using AI
 * @param {Object} props - Component props
 * @returns {JSX.Element} GenerateMagicSystemModal UI
 */
const GenerateMagicSystemModal = ({ onClose, onGenerate, isGenerating }) => {
  const [formData, setFormData] = useState({
    genre: 'fantasy',
    powerLevel: 'medium',
    complexity: 'medium',
    theme: '',
    constraints: '',
    quantity: 1
  });

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'quantity' ? Math.max(1, Math.min(20, parseInt(value) || 1)) : value
    }));
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();
    onGenerate(formData);
  };

  return (
    <div className="modal-overlay">
      <div className="generate-magic-system-modal">
        <div className="modal-header">
          <h2>Generate Magic System with AI</h2>
          <button className="close-button" onClick={onClose}>×</button>
        </div>

        <form onSubmit={handleSubmit} className="generate-magic-form">
          <div className="form-group">
            <label htmlFor="genre">Genre</label>
            <select
              id="genre"
              name="genre"
              value={formData.genre}
              onChange={handleInputChange}
              required
            >
              <option value="fantasy">Fantasy</option>
              <option value="sci-fi">Science Fiction</option>
              <option value="urban-fantasy">Urban Fantasy</option>
              <option value="steampunk">Steampunk</option>
              <option value="horror">Horror</option>
              <option value="mythological">Mythological</option>
              <option value="post-apocalyptic">Post-Apocalyptic</option>
            </select>
          </div>

          <div className="form-group">
            <label htmlFor="powerLevel">Power Level</label>
            <select
              id="powerLevel"
              name="powerLevel"
              value={formData.powerLevel}
              onChange={handleInputChange}
              required
            >
              <option value="low">Low (Subtle Magic)</option>
              <option value="medium">Medium (Balanced Magic)</option>
              <option value="high">High (Powerful Magic)</option>
              <option value="cosmic">Cosmic (Reality-Altering)</option>
            </select>
          </div>

          <div className="form-group">
            <label htmlFor="complexity">Complexity</label>
            <select
              id="complexity"
              name="complexity"
              value={formData.complexity}
              onChange={handleInputChange}
              required
            >
              <option value="simple">Simple (Easy to Understand)</option>
              <option value="medium">Medium (Moderate Rules)</option>
              <option value="complex">Complex (Intricate System)</option>
              <option value="very-complex">Very Complex (Deep Mechanics)</option>
            </select>
          </div>

          <div className="form-group">
            <label htmlFor="theme">Theme/Concept (Optional)</label>
            <input
              type="text"
              id="theme"
              name="theme"
              value={formData.theme}
              onChange={handleInputChange}
              placeholder="e.g. elemental, blood magic, rune-based, etc."
            />
          </div>

          <div className="form-group">
            <label htmlFor="constraints">Constraints/Limitations (Optional)</label>
            <textarea
              id="constraints"
              name="constraints"
              value={formData.constraints}
              onChange={handleInputChange}
              rows={3}
              placeholder="Describe any specific limitations or costs you want the magic system to have"
            />
          </div>

          <div className="form-group">
            <label htmlFor="quantity">Number of Systems to Generate (1-20)</label>
            <input
              type="number"
              id="quantity"
              name="quantity"
              value={formData.quantity}
              onChange={handleInputChange}
              min="1"
              max="20"
              required
            />
          </div>

          <div className="form-actions">
            <button
              type="submit"
              className="generate-button"
              disabled={isGenerating}
            >
              {isGenerating ? 'Generating...' : 'Generate Magic System'}
            </button>
            <button
              type="button"
              className="cancel-button"
              onClick={onClose}
              disabled={isGenerating}
            >
              Cancel
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default GenerateMagicSystemModal;

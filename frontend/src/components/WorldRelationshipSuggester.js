// frontend/src/components/WorldRelationshipSuggester.js
import React, { useState } from 'react';
import { useSelector } from 'react-redux';
import { suggestRelationships } from '../services/worldBuildingApiService';
import { selectCurrentBookId } from '../redux/slices/worldBuildingSlice';
import './WorldRelationshipSuggester.css';

/**
 * Component for suggesting relationships between world elements using AI
 * @param {Object} props - Component props
 * @returns {JSX.Element} WorldRelationshipSuggester UI
 */
const WorldRelationshipSuggester = ({ selectedElements = [], onRelationshipsSuggested }) => {
  const bookId = useSelector(selectCurrentBookId);
  
  // State for the suggester
  const [isSuggesting, setIsSuggesting] = useState(false);
  const [error, setError] = useState(null);
  
  // Handle suggestion
  const handleSuggestRelationships = async () => {
    if (selectedElements.length < 2) {
      setError('Please select at least 2 elements');
      return;
    }
    
    try {
      setIsSuggesting(true);
      setError(null);
      
      const elementIds = selectedElements.map(element => element.id);
      const suggestedRelationships = await suggestRelationships(bookId, elementIds);
      
      if (onRelationshipsSuggested && typeof onRelationshipsSuggested === 'function') {
        onRelationshipsSuggested(suggestedRelationships);
      }
    } catch (error) {
      console.error('Error suggesting relationships:', error);
      setError(error.message || 'Failed to suggest relationships');
    } finally {
      setIsSuggesting(false);
    }
  };
  
  return (
    <div className="world-relationship-suggester">
      <h3>AI Relationship Suggestions</h3>
      
      <div className="selected-elements">
        <h4>Selected Elements ({selectedElements.length}):</h4>
        
        {selectedElements.length > 0 ? (
          <ul className="element-list">
            {selectedElements.map(element => (
              <li key={element.id} className="element-item">
                <span className="element-name">{element.name}</span>
                <span className="element-category">{element.category}</span>
              </li>
            ))}
          </ul>
        ) : (
          <p className="no-elements">No elements selected</p>
        )}
      </div>
      
      {error && <div className="error-message">{error}</div>}
      
      <button
        className="suggest-button"
        onClick={handleSuggestRelationships}
        disabled={isSuggesting || selectedElements.length < 2}
      >
        {isSuggesting ? 'Suggesting...' : 'Suggest Relationships'}
      </button>
      
      <div className="suggester-info">
        <p>Select 2 or more elements to get AI-powered suggestions for meaningful relationships between them.</p>
        <p>These suggestions can help you build a more cohesive and interconnected world.</p>
      </div>
    </div>
  );
};

export default WorldRelationshipSuggester;

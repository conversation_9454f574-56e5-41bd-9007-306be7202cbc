// frontend/src/components/CreateElementModal.js
import React, { useState, useEffect } from 'react';
import './CreateElementModal.css';
import { getTemplateForElementType, getElementTypeLabel } from '../utils/templateUtils';
import { getCategoryElementType } from '../utils/categoryMapping';

/**
 * Modal for creating a new world element
 * @param {Object} props - Component props
 * @returns {JSX.Element} CreateElementModal UI
 */
const CreateElementModal = ({ categoryId, categoryName, onClose, onCreate }) => {
  // Get element type based on category
  const elementType = getCategoryElementType(categoryId);

  // Get template for this element type
  const template = getTemplateForElementType(elementType);

  // State for form data
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    tags: '',
    importance: 'medium'
  });

  // State for custom fields
  const [customFields, setCustomFields] = useState({});

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle custom field changes
  const handleCustomFieldChange = (fieldId, value) => {
    setCustomFields(prev => ({
      ...prev,
      [fieldId]: value
    }));
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();

    // Process tags
    const tags = formData.tags
      .split(',')
      .map(tag => tag.trim())
      .filter(tag => tag.length > 0);

    // Create element data
    const elementData = {
      name: formData.name,
      description: formData.description,
      tags,
      importance: formData.importance,
      element_type: elementType
    };

    // Call onCreate with the element data and custom fields
    onCreate(elementData, customFields);
  };

  // Log for debugging
  useEffect(() => {
    console.log('CreateElementModal - Category:', categoryId, categoryName);
    console.log('CreateElementModal - Element Type:', elementType);
    console.log('CreateElementModal - Template:', template);
  }, [categoryId, categoryName, elementType, template]);

  return (
    <div className="modal-overlay">
      <div className="create-element-modal">
        <div className="modal-header">
          <h2>Create New {getElementTypeLabel(elementType)}</h2>
          <button className="close-button" onClick={onClose}>×</button>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="form-group">
            <label htmlFor="name">Name</label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              placeholder="Enter element name"
              required
            />
          </div>

          <div className="form-group">
            <label htmlFor="description">Description</label>
            <textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              placeholder="Describe this element"
              rows={4}
              required
            />
          </div>

          {/* Custom fields based on element type */}
          {template.fields.map(field => (
            <div className="form-group" key={field.id}>
              <label htmlFor={`custom-${field.id}`}>{field.label}</label>
              {field.type === 'textarea' ? (
                <textarea
                  id={`custom-${field.id}`}
                  value={customFields[field.id] || ''}
                  onChange={(e) => handleCustomFieldChange(field.id, e.target.value)}
                  rows={3}
                  placeholder={field.placeholder}
                  required={field.required}
                />
              ) : field.type === 'select' ? (
                <select
                  id={`custom-${field.id}`}
                  value={customFields[field.id] || ''}
                  onChange={(e) => handleCustomFieldChange(field.id, e.target.value)}
                  required={field.required}
                >
                  <option value="">Select {field.label}</option>
                  {field.options.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              ) : (
                <input
                  type="text"
                  id={`custom-${field.id}`}
                  value={customFields[field.id] || ''}
                  onChange={(e) => handleCustomFieldChange(field.id, e.target.value)}
                  placeholder={field.placeholder}
                  required={field.required}
                />
              )}
            </div>
          ))}

          <div className="form-group">
            <label htmlFor="tags">Tags (comma-separated)</label>
            <input
              type="text"
              id="tags"
              name="tags"
              value={formData.tags}
              onChange={handleInputChange}
              placeholder="e.g. mountains, natural barrier, north"
            />
          </div>

          <div className="form-group">
            <label htmlFor="importance">Importance</label>
            <select
              id="importance"
              name="importance"
              value={formData.importance}
              onChange={handleInputChange}
            >
              <option value="low">Low</option>
              <option value="medium">Medium</option>
              <option value="high">High</option>
            </select>
          </div>

          <div className="form-actions">
            <button type="submit" className="create-button">Create {getElementTypeLabel(elementType)}</button>
            <button type="button" className="cancel-button" onClick={onClose}>Cancel</button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CreateElementModal;

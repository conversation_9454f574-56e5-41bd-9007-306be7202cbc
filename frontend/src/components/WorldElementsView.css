/* frontend/src/components/WorldElementsView.css */
.world-elements-view {
  width: 300px;
  border-right: 1px solid var(--border-color);
  background: var(--background-primary);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.world-elements-header {
  padding: 15px;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.world-elements-header h3 {
  margin: 0;
  font-size: 1.1rem;
  color: var(--text-primary);
}

.view-mode-selector {
  display: flex;
  background: var(--background-tertiary);
  border-radius: 4px;
  overflow: hidden;
}

.view-mode-option {
  padding: 6px 10px;
  border: none;
  background: transparent;
  cursor: pointer;
  color: var(--text-primary);
  transition: background 0.2s;
}

.view-mode-option.active {
  background: var(--primary);
  color: white;
}

.view-mode-option:hover:not(.active) {
  background: var(--background-secondary);
}

.categories-list {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
}

.category-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.2s;
  background: var(--background-secondary);
  position: relative;
}

.category-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.category-item.active {
  background: var(--primary);
  color: white;
}

.category-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 1.2rem;
}

.category-icon.physical {
  background: rgba(76, 175, 80, 0.2);
  color: #4caf50;
}

.category-icon.social {
  background: rgba(33, 150, 243, 0.2);
  color: #2196f3;
}

.category-icon.metaphysical {
  background: rgba(156, 39, 176, 0.2);
  color: #9c27b0;
}

.category-icon.technological {
  background: rgba(255, 152, 0, 0.2);
  color: #ff9800;
}

.category-item.active .category-icon {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.category-info {
  flex: 1;
  min-width: 0;
}

.category-info h4 {
  margin: 0 0 4px 0;
  font-size: 1rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.category-info p {
  margin: 0;
  font-size: 0.8rem;
  opacity: 0.8;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.category-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  font-size: 0.7rem;
  padding: 2px 6px;
  border-radius: 10px;
}

.category-badge.universal {
  background: rgba(33, 150, 243, 0.2);
  color: #2196f3;
}

.category-badge.genre-specific {
  background: rgba(156, 39, 176, 0.2);
  color: #9c27b0;
}

.category-item.active .category-badge {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.no-categories, .no-elements {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  text-align: center;
  color: var(--text-secondary);
  padding: 0 20px;
}

.create-element-button {
  margin-top: 15px;
  padding: 8px 16px;
  background: var(--primary);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: background 0.2s;
}

.create-element-button:hover {
  background: var(--button-primary-hover);
}

.header-with-back {
  display: flex;
  align-items: center;
  gap: 10px;
}

.back-button {
  padding: 5px 10px;
  background: var(--background-tertiary);
  border: none;
  border-radius: 4px;
  cursor: pointer;
  color: var(--text-primary);
  font-weight: 500;
  transition: background 0.2s;
}

.back-button:hover {
  background: var(--background-secondary);
}

.elements-list {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
}

.element-item {
  display: flex;
  flex-direction: column;
  padding: 15px;
  border-radius: 6px;
  margin-bottom: 10px;
  cursor: pointer;
  transition: all 0.2s;
  background: var(--background-secondary);
  border-left: 4px solid var(--primary);
}

.element-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.element-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.element-header h4 {
  margin: 0;
  font-size: 1.1rem;
  color: var(--text-primary);
}

.importance-badge {
  font-size: 0.7rem;
  padding: 2px 6px;
  border-radius: 10px;
  text-transform: uppercase;
  font-weight: bold;
}

.importance-badge.high {
  background: rgba(244, 67, 54, 0.2);
  color: #f44336;
}

.importance-badge.medium {
  background: rgba(255, 152, 0, 0.2);
  color: #ff9800;
}

.importance-badge.low {
  background: rgba(76, 175, 80, 0.2);
  color: #4caf50;
}

.element-description {
  margin: 0 0 10px 0;
  color: var(--text-secondary);
  font-size: 0.9rem;
  line-height: 1.4;
}

.element-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.element-tag {
  font-size: 0.8rem;
  padding: 2px 8px;
  border-radius: 12px;
  background: var(--background-tertiary);
  color: var(--text-secondary);
}

/* Responsive styles */
@media (max-width: 768px) {
  .world-elements-view {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid var(--border-color);
    max-height: 50%;
  }
}

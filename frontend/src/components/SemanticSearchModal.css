/* frontend/src/components/SemanticSearchModal.css */

.semantic-search-modal {
  width: 80%;
  max-width: 900px;
  max-height: 80vh;
  background-color: var(--background-color);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.semantic-search-tabs {
  display: flex;
  border-bottom: 1px solid var(--border-color);
}

.tab-button {
  padding: 10px 20px;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 14px;
  color: var(--text-color);
  transition: all 0.2s ease;
}

.tab-button.active {
  border-bottom: 2px solid var(--primary-color);
  color: var(--primary-color);
  font-weight: 600;
}

.search-mode-selector {
  display: flex;
  padding: 10px;
  justify-content: center;
  gap: 10px;
  border-bottom: 1px solid var(--border-color);
}

.mode-button {
  padding: 8px 16px;
  background-color: var(--background-secondary);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.mode-button.active {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.content-type-filters {
  display: flex;
  padding: 10px 20px;
  gap: 20px;
  flex-wrap: wrap;
  border-bottom: 1px solid var(--border-color);
}

.content-type-filters label {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 14px;
  cursor: pointer;
}

.search-form {
  display: flex;
  padding: 15px;
  gap: 10px;
}

.search-input {
  flex: 1;
  padding: 10px 15px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  font-size: 14px;
  background-color: var(--input-background);
  color: var(--text-color);
}

.search-button {
  padding: 10px 20px;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 600;
  transition: background-color 0.2s ease;
}

.search-button:hover {
  background-color: var(--primary-color-dark);
}

.search-button:disabled {
  background-color: var(--disabled-color);
  cursor: not-allowed;
}

.search-error {
  padding: 10px 15px;
  margin: 0 15px;
  background-color: var(--error-background);
  color: var(--error-color);
  border-radius: 4px;
  font-size: 14px;
}

.search-results {
  flex: 1;
  overflow-y: auto;
  padding: 15px;
}

.template-results, .hybrid-results {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 15px;
}

.template-result-card, .hybrid-result-card {
  background-color: var(--card-background);
  border-radius: 6px;
  border-left: 4px solid;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  padding: 15px;
  display: flex;
  flex-direction: column;
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.template-result-card:hover, .hybrid-result-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.template-result-header, .hybrid-result-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.template-result-header h4, .hybrid-result-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  flex: 1;
}

.template-score, .result-score {
  font-size: 12px;
  font-weight: 600;
  color: var(--success-color);
  background-color: var(--success-background);
  padding: 2px 6px;
  border-radius: 10px;
  white-space: nowrap;
}

.result-type {
  font-size: 12px;
  font-weight: 600;
  color: var(--primary-color);
  background-color: var(--primary-background);
  padding: 2px 6px;
  border-radius: 10px;
  margin-right: 5px;
}

.template-description, .result-description {
  font-size: 14px;
  color: var(--text-secondary);
  margin-bottom: 10px;
  flex: 1;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

.template-result-footer, .hybrid-result-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: var(--text-tertiary);
}

.template-category, .result-category {
  font-style: italic;
}

.no-results {
  text-align: center;
  padding: 30px;
  color: var(--text-secondary);
  font-style: italic;
}

.search-history {
  padding: 15px;
}

.search-history h3 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 16px;
  font-weight: 600;
}

.history-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.history-item {
  padding: 10px 15px;
  border-bottom: 1px solid var(--border-color);
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.history-item:hover {
  background-color: var(--hover-color);
}

.no-history {
  text-align: center;
  padding: 30px;
  color: var(--text-secondary);
  font-style: italic;
}

/* Specific styling for different result types */
.hybrid-result-card.template {
  border-left-color: var(--primary-color);
}

.hybrid-result-card.element {
  border-left-color: var(--success-color);
}

.hybrid-result-card.character {
  border-left-color: var(--warning-color);
}

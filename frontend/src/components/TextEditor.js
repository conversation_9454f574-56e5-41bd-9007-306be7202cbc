
// frontend/src/components/TextEditor.js
import React, { useEffect, useRef, useState, useCallback } from 'react';
import {
  Editor,
  EditorState,
  ContentState,
  RichUtils,
  convertToRaw,
  convertFromRaw,
  Modifier
} from 'draft-js';
// import { Map } from 'immutable';
import 'draft-js/dist/Draft.css';
import './TextEditor.css';
import TemplateSuggestions from './TemplateSuggestions';

function TextEditor({ content, onChange, readOnly = false }) {
  // State for template suggestions
  const [showTemplateSuggestions, setShowTemplateSuggestions] = useState(false);
  // Create a function to parse content and create editor state
  const createEditorState = (contentValue) => {
    console.log('[DEBUG TextEditor] Creating editor state from content');
    console.log('[DEBUG TextEditor] Content type:', typeof contentValue);
    console.log('[DEBUG TextEditor] Content value:', contentValue ? contentValue.substring(0, 100) + '...' : 'null or empty');

    if (!contentValue) {
      console.log('[DEBUG TextEditor] No content provided, creating empty editor state');
      return EditorState.createEmpty();
    }

    try {
      // Try to parse as rich text (JSON)
      let parsedContent;

      if (typeof contentValue === 'string') {
        try {
          parsedContent = JSON.parse(contentValue);
          console.log('[DEBUG TextEditor] Successfully parsed content as JSON');
        } catch (parseError) {
          console.log('[DEBUG TextEditor] Content is not valid JSON, treating as plain text');
          throw parseError; // This will be caught by the outer try/catch
        }
      } else {
        parsedContent = contentValue;
        console.log('[DEBUG TextEditor] Content is already an object');
      }

      // Validate that parsedContent has the expected structure
      if (!parsedContent.blocks || !Array.isArray(parsedContent.blocks)) {
        console.error('[DEBUG TextEditor] Invalid content structure, missing blocks array');
        throw new Error('Invalid content structure');
      }

      const contentState = convertFromRaw(parsedContent);
      return EditorState.createWithContent(contentState);
    } catch (e) {
      console.error('[DEBUG TextEditor] Error creating rich text editor state:', e);
      // Fall back to plain text
      console.log('[DEBUG TextEditor] Falling back to plain text content');
      return EditorState.createWithContent(
        ContentState.createFromText(typeof contentValue === 'string' ? contentValue : '')
      );
    }
  };

  // Initialize editor state only once on mount or when content changes significantly
  console.log('[DEBUG TextEditor] Initial content:', content);
  const [editorState, setEditorState] = useState(() => {
    console.log('[DEBUG TextEditor] Creating initial editor state');
    return createEditorState(content);
  });
  const [wordCount, setWordCount] = useState(0);
  const editorRef = useRef(null);

  // Track previous content for comparison
  const prevContentRef = useRef(content);
  console.log('[DEBUG TextEditor] Editor initialized with content type:', typeof content);

  // Update editor state when content prop changes
  useEffect(() => {
    console.log('[DEBUG TextEditor] useEffect triggered, content:', content);
    console.log('[DEBUG TextEditor] Previous content:', prevContentRef.current);
    console.log('[DEBUG TextEditor] Content equality check:', content === prevContentRef.current);

    // Always reinitialize if content has changed (even if empty)
    if (content !== prevContentRef.current) {
      console.log('[DEBUG TextEditor] Content changed, reinitializing editor state');
      console.log('[DEBUG TextEditor] New content type:', typeof content);

      try {
        if (content) {
          // If we have content, create editor state from it
          const newState = createEditorState(content);
          setEditorState(newState);
          console.log('[DEBUG TextEditor] Editor state updated with new content');
        } else {
          // If content is empty/null/undefined, create an empty editor state
          console.log('[DEBUG TextEditor] Creating empty editor state');
          setEditorState(EditorState.createEmpty());
        }

        // Update the previous content reference
        prevContentRef.current = content;
        console.log('[DEBUG TextEditor] Editor state successfully updated');
      } catch (error) {
        console.error('[DEBUG TextEditor] Error updating editor state:', error);
      }
    } else {
      console.log('[DEBUG TextEditor] Content unchanged, skipping reinitialization');
    }
  }, [content]); // Only depend on content

  // Update word count when editor state changes
  useEffect(() => {
    const contentState = editorState.getCurrentContent();
    const text = contentState.getPlainText();
    const words = text.match(/\S+/g) || [];
    setWordCount(words.length);

    // Show template suggestions if there's enough content
    setShowTemplateSuggestions(text.length > 100);
  }, [editorState]);

  // Get the current text for template suggestions
  const getCurrentText = useCallback(() => {
    const contentState = editorState.getCurrentContent();
    return contentState.getPlainText();
  }, [editorState]);

  const handleEditorChange = useCallback((state) => {
    console.log('[DEBUG TextEditor] Editor content changed');

    // Important: Update the editor state first to preserve cursor position
    setEditorState(state);

    try {
      // Convert to raw JSON for saving (preserves formatting)
      const contentState = state.getCurrentContent();
      const rawContent = convertToRaw(contentState);
      const jsonContent = JSON.stringify(rawContent);

      console.log('[DEBUG TextEditor] Converted to JSON, length:', jsonContent.length);
      console.log('[DEBUG TextEditor] First 100 chars:', jsonContent.substring(0, 100));

      // Call parent onChange handler with the JSON string
      if (onChange) {
        console.log('[DEBUG TextEditor] Calling parent onChange handler');
        onChange(jsonContent);
      } else {
        console.warn('[DEBUG TextEditor] No onChange handler provided');
      }
    } catch (error) {
      console.error('[DEBUG TextEditor] Error converting editor state to JSON:', error);
    }
  }, [onChange]);

  const handleKeyCommand = (command) => {
    const newState = RichUtils.handleKeyCommand(editorState, command);
    if (newState) {
      handleEditorChange(newState);
      return 'handled';
    }
    return 'not-handled';
  };

  // Handle formatting buttons
  const toggleInlineStyle = (style) => {
    handleEditorChange(RichUtils.toggleInlineStyle(editorState, style));
  };

  const toggleBlockType = (blockType) => {
    handleEditorChange(RichUtils.toggleBlockType(editorState, blockType));
  };

  // Font style functions
  const applyFontSize = (fontSize) => {
    const selection = editorState.getSelection();
    const currentContent = editorState.getCurrentContent();
    const currentStyle = editorState.getCurrentInlineStyle();

    // Remove any existing font size styles
    let nextContentState = currentContent;
    const fontSizes = ['FONT_SIZE_8', 'FONT_SIZE_10', 'FONT_SIZE_12', 'FONT_SIZE_14', 'FONT_SIZE_16', 'FONT_SIZE_18', 'FONT_SIZE_24', 'FONT_SIZE_30', 'FONT_SIZE_36', 'FONT_SIZE_48', 'FONT_SIZE_60', 'FONT_SIZE_72'];
    fontSizes.forEach(size => {
      if (currentStyle.has(size)) {
        nextContentState = Modifier.removeInlineStyle(
          nextContentState,
          selection,
          size
        );
      }
    });

    // Apply the new font size
    const newContentState = Modifier.applyInlineStyle(
      nextContentState,
      selection,
      fontSize
    );

    const newEditorState = EditorState.push(
      editorState,
      newContentState,
      'change-inline-style'
    );

    handleEditorChange(newEditorState);
  };

  const applyFontFamily = (fontFamily) => {
    const selection = editorState.getSelection();
    const currentContent = editorState.getCurrentContent();
    const currentStyle = editorState.getCurrentInlineStyle();

    // Remove any existing font family styles
    let nextContentState = currentContent;
    const fontFamilies = ['FONT_ARIAL', 'FONT_TIMES_NEW_ROMAN', 'FONT_GEORGIA', 'FONT_COURIER', 'FONT_VERDANA', 'FONT_TAHOMA'];
    fontFamilies.forEach(font => {
      if (currentStyle.has(font)) {
        nextContentState = Modifier.removeInlineStyle(
          nextContentState,
          selection,
          font
        );
      }
    });

    // Apply the new font family
    const newContentState = Modifier.applyInlineStyle(
      nextContentState,
      selection,
      fontFamily
    );

    const newEditorState = EditorState.push(
      editorState,
      newContentState,
      'change-inline-style'
    );

    handleEditorChange(newEditorState);
  };

  const applyFontColor = (color) => {
    const selection = editorState.getSelection();
    const currentContent = editorState.getCurrentContent();
    const currentStyle = editorState.getCurrentInlineStyle();

    // Remove any existing color styles
    let nextContentState = currentContent;
    const colors = ['COLOR_BLACK', 'COLOR_RED', 'COLOR_BLUE', 'COLOR_GREEN', 'COLOR_PURPLE', 'COLOR_ORANGE'];
    colors.forEach(c => {
      if (currentStyle.has(c)) {
        nextContentState = Modifier.removeInlineStyle(
          nextContentState,
          selection,
          c
        );
      }
    });

    // Apply the new color
    const newContentState = Modifier.applyInlineStyle(
      nextContentState,
      selection,
      color
    );

    const newEditorState = EditorState.push(
      editorState,
      newContentState,
      'change-inline-style'
    );

    handleEditorChange(newEditorState);
  };

  const focusEditor = () => {
    if (editorRef.current) {
      editorRef.current.focus();
      console.log('Editor focused');
    } else {
      console.log('Editor ref not available');
    }
  };

  // Handle template selection from suggestions
  const handleTemplateSelect = (template) => {
    console.log('Template selected:', template);

    // Here you would typically open a modal to create a new element
    // based on the selected template, or insert a reference to an
    // existing element

    // For now, we'll just insert a placeholder
    const selection = editorState.getSelection();
    const contentState = editorState.getCurrentContent();
    const newContentState = Modifier.insertText(
      contentState,
      selection,
      `[${template.display_name}]`
    );

    const newEditorState = EditorState.push(
      editorState,
      newContentState,
      'insert-characters'
    );

    handleEditorChange(newEditorState);
  };

  // Get current inline style and block type
  const currentInlineStyle = editorState.getCurrentInlineStyle();
  const currentBlockType = editorState
    .getCurrentContent()
    .getBlockForKey(editorState.getSelection().getStartKey())
    .getType();

  // Define custom style map for font sizes, families, and colors
  const styleMap = {
    // Font sizes
    'FONT_SIZE_8': { fontSize: '8px' },
    'FONT_SIZE_10': { fontSize: '10px' },
    'FONT_SIZE_12': { fontSize: '12px' },
    'FONT_SIZE_14': { fontSize: '14px' },
    'FONT_SIZE_16': { fontSize: '16px' },
    'FONT_SIZE_18': { fontSize: '18px' },
    'FONT_SIZE_24': { fontSize: '24px' },
    'FONT_SIZE_30': { fontSize: '30px' },
    'FONT_SIZE_36': { fontSize: '36px' },
    'FONT_SIZE_48': { fontSize: '48px' },
    'FONT_SIZE_60': { fontSize: '60px' },
    'FONT_SIZE_72': { fontSize: '72px' },

    // Font families
    'FONT_ARIAL': { fontFamily: 'Arial, sans-serif' },
    'FONT_TIMES_NEW_ROMAN': { fontFamily: '"Times New Roman", Times, serif' },
    'FONT_GEORGIA': { fontFamily: 'Georgia, serif' },
    'FONT_COURIER': { fontFamily: '"Courier New", Courier, monospace' },
    'FONT_VERDANA': { fontFamily: 'Verdana, Geneva, sans-serif' },
    'FONT_TAHOMA': { fontFamily: 'Tahoma, Geneva, sans-serif' },

    // Font colors
    'COLOR_BLACK': { color: '#000000' },
    'COLOR_RED': { color: '#FF0000' },
    'COLOR_BLUE': { color: '#0000FF' },
    'COLOR_GREEN': { color: '#008000' },
    'COLOR_PURPLE': { color: '#800080' },
    'COLOR_ORANGE': { color: '#FFA500' }
  };

  // Get current font size, family, and color
  const getCurrentFontSize = () => {
    const fontSizes = ['FONT_SIZE_8', 'FONT_SIZE_10', 'FONT_SIZE_12', 'FONT_SIZE_14', 'FONT_SIZE_16', 'FONT_SIZE_18', 'FONT_SIZE_24', 'FONT_SIZE_30', 'FONT_SIZE_36', 'FONT_SIZE_48', 'FONT_SIZE_60', 'FONT_SIZE_72'];
    for (const size of fontSizes) {
      if (currentInlineStyle.has(size)) {
        return size;
      }
    }
    return 'FONT_SIZE_16'; // Default font size
  };

  const getCurrentFontFamily = () => {
    const fontFamilies = ['FONT_ARIAL', 'FONT_TIMES_NEW_ROMAN', 'FONT_GEORGIA', 'FONT_COURIER', 'FONT_VERDANA', 'FONT_TAHOMA'];
    for (const font of fontFamilies) {
      if (currentInlineStyle.has(font)) {
        return font;
      }
    }
    return 'FONT_TIMES_NEW_ROMAN'; // Default font family
  };

  const getCurrentFontColor = () => {
    const colors = ['COLOR_BLACK', 'COLOR_RED', 'COLOR_BLUE', 'COLOR_GREEN', 'COLOR_PURPLE', 'COLOR_ORANGE'];
    for (const color of colors) {
      if (currentInlineStyle.has(color)) {
        return color;
      }
    }
    return 'COLOR_BLACK'; // Default color
  };

  const currentFontSize = getCurrentFontSize();
  const currentFontFamily = getCurrentFontFamily();
  const currentFontColor = getCurrentFontColor();

  // Initialize the editor when the component mounts
  useEffect(() => {
    // Small delay to ensure the editor is properly mounted
    setTimeout(() => {
      focusEditor();
    }, 100);
  }, []);

  return (
    <div className="text-editor-container">
      {!readOnly && (
        <div className="formatting-toolbar">
          <div className="toolbar-group">
            <button
              className={`toolbar-button ${currentInlineStyle.has('BOLD') ? 'active' : ''}`}
              onClick={() => toggleInlineStyle('BOLD')}
              title="Bold (Ctrl+B)"
            >
              <strong>B</strong>
            </button>
            <button
              className={`toolbar-button ${currentInlineStyle.has('ITALIC') ? 'active' : ''}`}
              onClick={() => toggleInlineStyle('ITALIC')}
              title="Italic (Ctrl+I)"
            >
              <em>I</em>
            </button>
            <button
              className={`toolbar-button ${currentInlineStyle.has('UNDERLINE') ? 'active' : ''}`}
              onClick={() => toggleInlineStyle('UNDERLINE')}
              title="Underline (Ctrl+U)"
            >
              <u>U</u>
            </button>
          </div>

          <div className="toolbar-group">
            <button
              className={`toolbar-button ${currentBlockType === 'header-one' ? 'active' : ''}`}
              onClick={() => toggleBlockType('header-one')}
              title="Heading 1"
            >
              H1
            </button>
            <button
              className={`toolbar-button ${currentBlockType === 'header-two' ? 'active' : ''}`}
              onClick={() => toggleBlockType('header-two')}
              title="Heading 2"
            >
              H2
            </button>
            <button
              className={`toolbar-button ${currentBlockType === 'header-three' ? 'active' : ''}`}
              onClick={() => toggleBlockType('header-three')}
              title="Heading 3"
            >
              H3
            </button>
          </div>

          <div className="toolbar-group">
            <button
              className={`toolbar-button ${currentBlockType === 'unordered-list-item' ? 'active' : ''}`}
              onClick={() => toggleBlockType('unordered-list-item')}
              title="Bullet List"
            >
              • List
            </button>
            <button
              className={`toolbar-button ${currentBlockType === 'ordered-list-item' ? 'active' : ''}`}
              onClick={() => toggleBlockType('ordered-list-item')}
              title="Numbered List"
            >
              1. List
            </button>
          </div>

          {/* Font Family Dropdown */}
          <div className="toolbar-group">
            <select
              className="font-select"
              value={currentFontFamily}
              onChange={(e) => applyFontFamily(e.target.value)}
              title="Font Family"
            >
              <option value="FONT_TIMES_NEW_ROMAN">Times New Roman</option>
              <option value="FONT_ARIAL">Arial</option>
              <option value="FONT_GEORGIA">Georgia</option>
              <option value="FONT_COURIER">Courier New</option>
              <option value="FONT_VERDANA">Verdana</option>
              <option value="FONT_TAHOMA">Tahoma</option>
            </select>
          </div>

          {/* Font Size Dropdown */}
          <div className="toolbar-group">
            <select
              className="font-select"
              value={currentFontSize}
              onChange={(e) => applyFontSize(e.target.value)}
              title="Font Size"
            >
              <option value="FONT_SIZE_8">8</option>
              <option value="FONT_SIZE_10">10</option>
              <option value="FONT_SIZE_12">12</option>
              <option value="FONT_SIZE_14">14</option>
              <option value="FONT_SIZE_16">16</option>
              <option value="FONT_SIZE_18">18</option>
              <option value="FONT_SIZE_24">24</option>
              <option value="FONT_SIZE_30">30</option>
              <option value="FONT_SIZE_36">36</option>
              <option value="FONT_SIZE_48">48</option>
              <option value="FONT_SIZE_60">60</option>
              <option value="FONT_SIZE_72">72</option>
            </select>
          </div>

          {/* Font Color Dropdown */}
          <div className="toolbar-group">
            <select
              className="font-select"
              value={currentFontColor}
              onChange={(e) => applyFontColor(e.target.value)}
              title="Font Color"
            >
              <option value="COLOR_BLACK">Black</option>
              <option value="COLOR_RED">Red</option>
              <option value="COLOR_BLUE">Blue</option>
              <option value="COLOR_GREEN">Green</option>
              <option value="COLOR_PURPLE">Purple</option>
              <option value="COLOR_ORANGE">Orange</option>
            </select>
          </div>
        </div>
      )}

      <div
        className="text-editor"
        onClick={focusEditor}
      >
        <Editor
          ref={editorRef}
          editorState={editorState}
          onChange={handleEditorChange}
          handleKeyCommand={handleKeyCommand}
          customStyleMap={styleMap}
          placeholder="Start writing..."
          readOnly={readOnly}
        />
      </div>

      <div className="text-editor-footer">
        <div className="word-count">{wordCount} words</div>
      </div>

      {/* Template Suggestions */}
      {showTemplateSuggestions && !readOnly && (
        <TemplateSuggestions
          content={getCurrentText()}
          onTemplateSelect={handleTemplateSelect}
          limit={5}
        />
      )}
    </div>
  );
}

export default TextEditor;

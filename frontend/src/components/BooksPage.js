// frontend/src/components/BooksPage.js
import React, { useState } from 'react';
import './BooksPage.css';
import BookListContainer from '../containers/BookListContainer';
import BookDetailsPageContainer from '../containers/BookDetailsPageContainer';

/**
 * BooksPage component that combines BookList and BookDetailsPage
 * @returns {JSX.Element} BooksPage UI
 */
const BooksPage = () => {
  const [showDetails, setShowDetails] = useState(false);
  
  return (
    <div className="books-page">
      <div className={`books-layout ${showDetails ? 'show-details' : ''}`}>
        <div className="books-sidebar">
          <BookListContainer />
        </div>
        
        <div className="books-details">
          <BookDetailsPageContainer />
        </div>
      </div>
      
      <button 
        className="toggle-details-button"
        onClick={() => setShowDetails(!showDetails)}
      >
        {showDetails ? 'Hide Details' : 'Show Details'}
      </button>
    </div>
  );
};

export default BooksPage;

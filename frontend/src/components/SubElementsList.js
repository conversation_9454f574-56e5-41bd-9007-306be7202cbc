// frontend/src/components/SubElementsList.js
import React, { useState } from 'react';
import './SubElementsList.css';
import { getColorForType } from '../utils/colorMapping';

/**
 * Component for displaying a list of sub-elements
 * @param {Object} props - Component props
 * @returns {JSX.Element} SubElementsList UI
 */
const SubElementsList = ({ elements = [], onElementSelect, onDeleteElement }) => {
  // State for confirmation dialog
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [elementToDelete, setElementToDelete] = useState(null);

  // Get element type label
  const getElementTypeLabel = (elementType) => {
    const labels = {
      'organization': 'Organization',
      'law': 'Law',
      'law_clause': 'Law Clause',
      'geography': 'Geography',
      'location': 'Location',
      'magic_system': 'Magic System',
      'magical_power': 'Magical Power',
      'magical_artifact': 'Magical Artifact',
      'spell': 'Spell',
      'generic': 'Element'
    };
    return labels[elementType] || 'Element';
  };

  // Handle delete button click
  const handleDeleteClick = (e, element) => {
    e.stopPropagation(); // Prevent triggering the parent onClick
    setElementToDelete(element);
    setShowConfirmation(true);
  };

  // Handle confirmation dialog close
  const handleConfirmationClose = () => {
    setShowConfirmation(false);
    setElementToDelete(null);
  };

  // Handle delete confirmation
  const handleDeleteConfirm = () => {
    if (elementToDelete && onDeleteElement) {
      onDeleteElement(elementToDelete.element_id);
    }
    setShowConfirmation(false);
    setElementToDelete(null);
  };

  if (elements.length === 0) {
    return (
      <div className="no-sub-elements">
        <p>No sub-elements found. Add some using the buttons above.</p>
      </div>
    );
  }

  return (
    <div className="sub-elements-list">
      {/* Confirmation Dialog */}
      {showConfirmation && elementToDelete && (
        <div className="delete-confirmation-overlay">
          <div className="delete-confirmation-dialog">
            <h3>Confirm Deletion</h3>
            <p>Are you sure you want to delete <strong>{elementToDelete.name}</strong>?</p>
            {elementToDelete.has_children && (
              <p className="warning">Warning: This will also delete all sub-elements!</p>
            )}
            <div className="confirmation-buttons">
              <button
                className="cancel-button"
                onClick={handleConfirmationClose}
              >
                Cancel
              </button>
              <button
                className="delete-button"
                onClick={handleDeleteConfirm}
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}

      {elements.map(element => (
        <div
          key={element.element_id}
          className={`sub-element-item ${element.has_children ? 'has-children' : ''}`}
          onClick={() => onElementSelect(element.element_id)}
          data-element-type={element.element_type}
          style={{ borderLeftColor: getColorForType(element.element_type) }}
        >
          {/* Delete button */}
          {onDeleteElement && (
            <button
              className="sub-element-delete-button"
              onClick={(e) => handleDeleteClick(e, element)}
              title="Delete element"
              aria-label="Delete element"
            >
              ×
            </button>
          )}

          <div className="sub-element-header">
            <div className="sub-element-title">
              {element.has_children && <span className="children-indicator">▶</span>}
              <h4>{element.name}</h4>
            </div>
            <span
              className="element-type-label"
              data-element-type={element.element_type}
              style={{
                backgroundColor: getColorForType(element.element_type),
                color: 'white'
              }}
            >
              {getElementTypeLabel(element.element_type)}
            </span>
          </div>

          {element.description && (
            <p className="sub-element-description">{element.description}</p>
          )}
        </div>
      ))}
    </div>
  );
};

export default SubElementsList;

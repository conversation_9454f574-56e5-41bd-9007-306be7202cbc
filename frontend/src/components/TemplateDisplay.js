// frontend/src/components/TemplateDisplay.js
import React, { useState } from 'react';
import './TemplateDisplay.css';
import { useTemplateRegistry } from '../utils/TemplateRegistry';
import { getElementTypeLabel } from '../utils/templateUtils';

/**
 * Component for displaying element data based on its template
 * @param {Object} props - Component props
 * @returns {JSX.Element} TemplateDisplay UI
 */
const TemplateDisplay = ({
  element,
  templateId,
  showBasicFields = true,
  groupFields = true,
  onEdit
}) => {
  // State for tracking expanded groups
  const [expandedGroups, setExpandedGroups] = useState({});

  // Use the template registry hook to get templates from Redux
  const templateRegistry = useTemplateRegistry();

  // Get template from registry or element type
  const template = templateRegistry.getTemplate(templateId || element?.element_type);

  // Toggle group expansion
  const toggleGroup = (groupName) => {
    setExpandedGroups(prev => ({
      ...prev,
      [groupName]: !prev[groupName]
    }));
  };

  // Group fields by category if enabled
  const getGroupedFields = () => {
    if (!template || !groupFields) return { 'Details': template?.fields || [] };

    const groups = {};

    template.fields.forEach(field => {
      const category = field.category || 'Details';
      if (!groups[category]) {
        groups[category] = [];
      }
      groups[category].push(field);
    });

    return groups;
  };

  // Render a field value based on its type
  const renderFieldValue = (field) => {
    if (!element || !element.custom_fields) return null;

    const value = element.custom_fields[field.id];
    if (value === undefined || value === null || value === '') return null;

    switch (field.type) {
      case 'checkbox':
        return (
          <span className="field-value checkbox-value">
            {value === true || value === 'true' ? 'Yes' : 'No'}
          </span>
        );

      case 'select':
        const option = field.options?.find(opt => opt.value === value);
        return (
          <span className="field-value">
            {option ? option.label : value}
          </span>
        );

      case 'textarea':
        return (
          <div className="field-value textarea-value">
            {value.split('\n').map((line, i) => (
              <React.Fragment key={i}>
                {line}
                {i < value.split('\n').length - 1 && <br />}
              </React.Fragment>
            ))}
          </div>
        );

      default:
        return <span className="field-value">{value}</span>;
    }
  };

  // If no element is provided
  if (!element) {
    return (
      <div className="template-display-error">
        No element data provided
      </div>
    );
  }

  // If no template is found
  if (!template) {
    return (
      <div className="template-display-error">
        Template not found: {templateId || element.element_type}
      </div>
    );
  }

  // Get grouped fields
  const groupedFields = getGroupedFields();

  return (
    <div className="template-display">
      {/* Element header */}
      <div className="element-header">
        <h2 className="element-name">{element.name}</h2>
        <div className="element-meta">
          <span className="element-type">
            {getElementTypeLabel(element.element_type)}
          </span>
          {template.version && (
            <span className="template-version">
              Template v{template.version}
            </span>
          )}
        </div>
      </div>

      {/* Basic fields */}
      {showBasicFields && (
        <div className="basic-fields">
          {element.description && (
            <div className="description-field">
              <h3>Description</h3>
              <div className="description-content">
                {element.description.split('\n').map((line, i) => (
                  <React.Fragment key={i}>
                    {line}
                    {i < element.description.split('\n').length - 1 && <br />}
                  </React.Fragment>
                ))}
              </div>
            </div>
          )}

          {element.tags && element.tags.length > 0 && (
            <div className="tags-field">
              <h3>Tags</h3>
              <div className="tags-list">
                {Array.isArray(element.tags)
                  ? element.tags.map(tag => (
                      <span key={tag} className="tag">{tag}</span>
                    ))
                  : element.tags.split(',').map(tag => (
                      <span key={tag.trim()} className="tag">{tag.trim()}</span>
                    ))
                }
              </div>
            </div>
          )}

          {element.importance && (
            <div className="importance-field">
              <h3>Importance</h3>
              <span className={`importance-badge ${element.importance}`}>
                {element.importance.charAt(0).toUpperCase() + element.importance.slice(1)}
              </span>
            </div>
          )}
        </div>
      )}

      {/* Template fields */}
      <div className="template-fields">
        {Object.entries(groupedFields).map(([groupName, fields]) => {
          // Check if any fields in this group have values
          const hasValues = fields.some(field =>
            element.custom_fields &&
            element.custom_fields[field.id] !== undefined &&
            element.custom_fields[field.id] !== null &&
            element.custom_fields[field.id] !== ''
          );

          // Skip empty groups
          if (!hasValues) return null;

          const isExpanded = expandedGroups[groupName] !== false; // Default to expanded

          return (
            <div key={groupName} className="field-group">
              <h3
                className={`group-title ${isExpanded ? 'expanded' : 'collapsed'}`}
                onClick={() => toggleGroup(groupName)}
              >
                {groupName}
                <span className="expand-icon">{isExpanded ? '▼' : '►'}</span>
              </h3>

              {isExpanded && (
                <div className="group-fields">
                  {fields.map(field => {
                    // Skip fields with no value
                    if (
                      !element.custom_fields ||
                      element.custom_fields[field.id] === undefined ||
                      element.custom_fields[field.id] === null ||
                      element.custom_fields[field.id] === ''
                    ) return null;

                    return (
                      <div className="field-item" key={field.id}>
                        <div className="field-label">{field.label}</div>
                        {renderFieldValue(field)}
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
          );
        })}
      </div>

      {/* Edit button */}
      {onEdit && (
        <button className="edit-element-button" onClick={onEdit}>
          Edit Element
        </button>
      )}
    </div>
  );
};

export default TemplateDisplay;

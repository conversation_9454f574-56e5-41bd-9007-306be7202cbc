/* frontend/src/components/ElementTreeView.css */
.element-tree-view {
  background-color: var(--background-secondary);
  border-radius: 6px;
  overflow: hidden;
  margin-bottom: 20px;
}

.tree-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: var(--background-tertiary);
  border-bottom: 1px solid var(--border-color);
}

.tree-header h3 {
  margin: 0;
  font-size: 1rem;
  color: var(--text-primary);
}

.expand-all-button {
  background: none;
  border: none;
  color: var(--primary);
  cursor: pointer;
  font-size: 0.8rem;
  padding: 4px 8px;
  border-radius: 4px;
}

.expand-all-button:hover {
  background-color: var(--background-hover);
}

.tree-content {
  padding: 8px 0;
  max-height: 400px;
  overflow-y: auto;
}

.tree-node-container {
  margin: 2px 0;
}

.tree-node {
  display: flex;
  align-items: center;
  padding: 6px 12px;
  cursor: pointer;
  border-radius: 4px;
  margin: 0 4px;
  transition: background-color 0.2s;
}

.tree-node:hover {
  background-color: var(--background-hover);
}

.tree-node.selected {
  background-color: var(--primary-light);
  color: var(--primary);
  font-weight: 500;
}

.expand-toggle {
  margin-right: 8px;
  font-size: 0.7rem;
  color: var(--text-tertiary);
  cursor: pointer;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.node-name {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.node-type {
  font-size: 0.7rem;
  background-color: var(--background-tertiary);
  color: var(--text-tertiary);
  padding: 2px 6px;
  border-radius: 10px;
  margin-left: 8px;
}

.tree-children {
  margin-left: 8px;
}

.empty-tree {
  padding: 16px;
  text-align: center;
  color: var(--text-tertiary);
  font-style: italic;
}

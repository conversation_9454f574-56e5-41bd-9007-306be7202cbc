// frontend/src/components/FloraFaunaSelectionModal.js
import React from 'react';
import './FloraFaunaSelectionModal.css';

/**
 * Modal for selecting between plant and animal types for Flora & Fauna
 * @param {Object} props - Component props
 * @returns {JSX.Element} FloraFaunaSelectionModal UI
 */
const FloraFaunaSelectionModal = ({ onSelect, onClose }) => {
  return (
    <div className="modal-overlay">
      <div className="flora-fauna-selection-modal">
        <div className="modal-header">
          <h2>Select Element Type</h2>
          <button className="close-button" onClick={onClose}>×</button>
        </div>

        <div className="selection-options">
          <div
            className="selection-option"
            onClick={() => onSelect('plant')}
          >
            <div className="option-icon">🌱</div>
            <h3>Plant</h3>
            <p>Create a plant, tree, flower, or other flora element</p>
          </div>

          <div
            className="selection-option"
            onClick={() => onSelect('animal')}
          >
            <div className="option-icon">🐕</div>
            <h3>Animal</h3>
            <p>Create a non-magical animal or wildlife</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FloraFaunaSelectionModal;

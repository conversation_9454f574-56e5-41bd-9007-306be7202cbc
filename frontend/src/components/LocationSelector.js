// frontend/src/components/LocationSelector.js
import React, { useState } from 'react';
import QuickWorldElementForm from './QuickWorldElementForm';

/**
 * Component for selecting world elements from a list or adding new ones
 * @param {Object} props - Component props
 * @param {Array} props.worldElements - List of world elements
 * @param {Array} props.selectedLocations - List of currently selected locations
 * @param {Function} props.onChange - Function to call when selection changes
 * @param {Function} props.onCreateLocation - Function to call when creating a new location
 * @param {Array} props.categories - Available world building categories
 * @returns {JSX.Element} World element selector component
 */
const LocationSelector = ({ worldElements, selectedLocations = [], onChange, onCreateLocation, categories = [] }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [showQuickForm, setShowQuickForm] = useState(false);

  // State for parent elements based on selected category
  const [availableParents, setAvailableParents] = useState([]);

  // State for selected category
  const [selectedCategory, setSelectedCategory] = useState('');

  console.log('LocationSelector - received worldElements:', worldElements);

  // Include all world element categories
  const locations = worldElements && worldElements.length > 0 ? worldElements
    .map(element => ({
      id: element.element_id || element.id, // Handle both element_id (from DB) and id (from Redux)
      name: element.name,
      description: element.description,
      category: element.category || ''
    })) : [];

  // Group elements by category for debugging
  const elementsByCategory = {};
  if (worldElements && worldElements.length > 0) {
    worldElements.forEach(element => {
      if (element) {
        const category = element.category || 'uncategorized';
        if (!elementsByCategory[category]) {
          elementsByCategory[category] = [];
        }
        elementsByCategory[category].push(element.name);
      }
    });
  }
  console.log('World elements by category:', elementsByCategory);

  console.log('Available world elements for selection:', locations);

  // Filter locations based on search term
  const filteredLocations = locations && locations.length > 0 ? locations.filter(location =>
    location && location.name && location.name.toLowerCase().includes(searchTerm.toLowerCase())
  ) : [];

  // Handle location selection
  const handleLocationSelect = (location) => {
    const locationName = typeof location === 'string' ? location : location.name;

    // Check if location is already selected
    if (selectedLocations.includes(locationName)) {
      // Remove location if already selected
      onChange(selectedLocations.filter(name => name !== locationName));
    } else {
      // Add location if not already selected
      onChange([...selectedLocations, locationName]);
    }
  };

  // We've removed the simple add location function in favor of the detailed add modal

  // Handle category change in quick form
  const handleCategoryChange = (categoryId) => {
    setSelectedCategory(categoryId);

    // Filter parent elements for this category
    const parentsForCategory = worldElements.filter(element =>
      element.category_id === categoryId && element.has_children
    );

    setAvailableParents(parentsForCategory);
  };

  // Handle quick form submission
  const handleQuickFormSubmit = async (formData) => {
    console.log('Creating new world element:', formData);

    try {
      // Call the parent component's handler and wait for it to complete
      if (typeof onCreateLocation === 'function') {
        const result = await onCreateLocation(formData);
        console.log('World element created result:', result);

        if (result) {
          // Add to selected locations
          const newSelectedLocations = [...selectedLocations, formData.name];
          onChange(newSelectedLocations);

          // Close the form
          setShowQuickForm(false);
        } else {
          console.error('Failed to create world element - no result returned');
          alert('Failed to create world element. Please try again.');
        }
      }
    } catch (error) {
      console.error('Error creating world element:', error);
      alert('Error creating world element: ' + (error.message || 'Unknown error'));
    }
  };

  return (
    <div className="world-element-selector">
      <div className="search-container" style={{ marginBottom: '10px' }}>
        <input
          type="text"
          placeholder="Search world elements..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          style={{
            width: '100%',
            padding: '8px 12px',
            borderRadius: '4px',
            border: '1px solid #ced4da',
            fontSize: '14px'
          }}
        />
      </div>

      {/* World elements list */}
      <div className="world-element-list" style={{
        maxHeight: '150px',
        overflowY: 'auto',
        border: '1px solid #ced4da',
        borderRadius: '4px',
        marginBottom: '10px'
      }}>
        {filteredLocations.length > 0 ? (
          filteredLocations.map(location => (
            <div
              key={location.id || location.name}
              className={`world-element-item ${selectedLocations.includes(location.name) ? 'selected' : ''}`}
              onClick={() => handleLocationSelect(location)}
              style={{
                padding: '6px 8px',
                cursor: 'pointer',
                borderBottom: '1px solid #eee',
                backgroundColor: selectedLocations.includes(location.name) ? '#e8f5e9' : 'transparent',
                display: 'flex',
                alignItems: 'center',
                width: '100%',
                justifyContent: 'flex-start'
              }}
            >
              <input
                type="checkbox"
                checked={selectedLocations.includes(location.name)}
                onChange={() => {}} // Handled by the div click
                style={{
                  marginRight: '8px',
                  flexShrink: 0,
                  width: '16px',
                  height: '16px'
                }}
              />
              <div style={{
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
                display: 'flex',
                flexDirection: 'column',
                flex: 1,
                textAlign: 'left'
              }}>
                <span style={{ fontWeight: '500' }}>{location.name}</span>
                {location.category && (
                  <span style={{ fontSize: '0.8em', color: '#6c757d' }}>
                    {location.category.replace('cat_', '').replace(/_/g, ' ')}
                  </span>
                )}
              </div>
            </div>
          ))
        ) : (
          <div style={{ padding: '8px', color: '#6c757d', textAlign: 'center', width: '100%' }}>
            {searchTerm ? 'No world elements found' : 'No world elements available'}
          </div>
        )}
      </div>

      {/* Selected world elements */}
      {selectedLocations.length > 0 && (
        <div className="selected-world-elements" style={{ marginBottom: '10px' }}>
          <div style={{ fontWeight: 'bold', marginBottom: '5px' }}>Selected:</div>
          <div style={{ display: 'flex', flexWrap: 'wrap', gap: '5px' }}>
            {selectedLocations.map(name => (
              <div
                key={name}
                className="selected-tag"
                style={{
                  backgroundColor: '#e8f5e9',
                  color: '#1b5e20',
                  padding: '4px 8px',
                  borderRadius: '4px',
                  fontSize: '0.85em',
                  display: 'flex',
                  alignItems: 'center',
                  border: '1px solid rgba(27, 94, 32, 0.2)',
                  margin: '2px'
                }}
              >
                <span style={{ marginRight: '4px' }}>{name}</span>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleLocationSelect(name);
                  }}
                  style={{
                    background: 'none',
                    border: 'none',
                    cursor: 'pointer',
                    marginLeft: '2px',
                    fontSize: '14px',
                    fontWeight: 'bold',
                    color: '#1b5e20',
                    padding: '0 2px',
                    lineHeight: '14px'
                  }}
                  aria-label="Remove"
                >
                  ×
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Add new world element option */}
      <div style={{ display: 'flex', justifyContent: 'center', marginTop: '10px' }}>
        {/* Detailed add button */}
        <button
          onClick={() => setShowQuickForm(true)}
          style={{
            width: '100%',
            padding: '8px 16px',
            borderRadius: '4px',
            border: 'none',
            background: '#0d6efd',
            color: 'white',
            cursor: 'pointer',
          }}
        >
          + Add World Element
        </button>
      </div>

      {/* We've removed the simple add form in favor of the detailed add modal */}

      {/* Quick World Element Form Modal */}
      {showQuickForm && (
        <div
          className="modal-overlay"
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: 'rgba(0, 0, 0, 0.5)',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            zIndex: 1100 // Higher than the parent modal
          }}
          onClick={(e) => {
            // Prevent clicks on the overlay from closing the modal
            e.stopPropagation();
          }}
        >
          <div
            className="modal-content"
            style={{
              background: 'white',
              padding: '20px',
              borderRadius: '8px',
              width: '500px',
              maxWidth: '90%',
              maxHeight: '90vh',
              overflowY: 'auto'
            }}
            onClick={(e) => {
              // Prevent clicks on the content from bubbling up to the overlay
              e.stopPropagation();
            }}
          >
            <QuickWorldElementForm
              onSubmit={handleQuickFormSubmit}
              onCancel={() => setShowQuickForm(false)}
              categories={categories}
              parentElements={availableParents}
              onCategoryChange={handleCategoryChange}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default LocationSelector;

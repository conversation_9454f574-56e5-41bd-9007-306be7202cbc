// frontend/src/components/Login.js
import React, { useState, useCallback, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link, useNavigate } from 'react-router-dom';
import { login, selectIsAuthenticated, selectAuthLoading, selectAuthError } from '../redux/slices/authSlice';
import './Login.css';

const Login = React.memo(({ onLogin = () => {} }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const isAuthenticated = useSelector(selectIsAuthenticated);
  const isLoading = useSelector(selectAuthLoading);
  const authError = useSelector(selectAuthError);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');

  // Redirect to Books page if already logged in
  // This will only run on initial render and when authentication state changes
  useEffect(() => {
    if (isAuthenticated) {
      console.debug('Login: Already authenticated, redirecting to /books');
      navigate('/books');
    }
  }, [isAuthenticated, navigate]);

  const handleSubmit = useCallback(async (e) => {
    e.preventDefault();
    try {
      console.debug('Login: Submitting login form');
      const resultAction = await dispatch(login({ email, password }));
      if (login.fulfilled.match(resultAction)) {
        console.debug('Login: Login successful');
        // Call onLogin callback if provided
        if (onLogin) onLogin();
        // We don't need to navigate here as the useEffect will handle it
        // when isAuthenticated changes to true
      }
    } catch (error) {
      console.error('Login error:', error);
    }
  }, [email, password, dispatch, onLogin]);

  return (
    <div className="login-container">
      <h2>Login</h2>
      <form onSubmit={handleSubmit} className="login-form">
        <div className="form-group">
          <label>Email:</label>
          <input type="email" value={email} onChange={(e) => setEmail(e.target.value)} required />
        </div>
        <div className="form-group">
          <label>Password:</label>
          <input type="password" value={password} onChange={(e) => setPassword(e.target.value)} required />
        </div>
        {authError && <p className="error-message">{authError}</p>}
        {isLoading && <p className="loading-message">Logging in...</p>}
        <button type="submit">Login</button>
      </form>
      <div className="register-link">
        <p>Don't have an account? <Link to="/register">Register</Link></p>
      </div>
    </div>
  );
});

export default Login;
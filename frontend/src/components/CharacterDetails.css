/* frontend/src/components/CharacterDetails.css */

.character-details-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.loading-indicator {
  text-align: center;
  padding: 40px;
  color: #666;
  font-size: 16px;
}

.no-character-selected {
  text-align: center;
  padding: 40px;
  background-color: #f5f5f5;
  border-radius: 8px;
}

.no-character-selected h2 {
  margin-top: 0;
  color: #333;
}

.error-message {
  background-color: #ffebee;
  border: 1px solid #ffcdd2;
  border-radius: 4px;
  padding: 10px 15px;
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.error-message p {
  color: #c62828;
  margin: 0;
}

.error-message button {
  background: none;
  border: none;
  color: #c62828;
  cursor: pointer;
  font-weight: bold;
}

/* Tabs */
.character-tabs {
  display: flex;
  border-bottom: 1px solid #ddd;
  margin-bottom: 20px;
}

.tab-button {
  padding: 10px 20px;
  background: none;
  border: none;
  border-bottom: 2px solid transparent;
  cursor: pointer;
  font-weight: 500;
  color: #666;
  transition: all 0.2s;
}

.tab-button:hover {
  color: #333;
}

.tab-button.active {
  color: #4caf50;
  border-bottom-color: #4caf50;
}

/* Character Info */
.character-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.character-header h2 {
  margin: 0 0 5px 0;
  color: #333;
}

.character-role {
  display: inline-block;
  background-color: #e0e0e0;
  padding: 3px 8px;
  border-radius: 12px;
  font-size: 12px;
}

.edit-button {
  background-color: #2196f3;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
}

.info-section {
  margin-bottom: 20px;
}

.info-section h3 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 16px;
}

.info-section p {
  margin: 0;
  color: #555;
  line-height: 1.5;
}

/* Character Edit Form */
.character-edit-form {
  margin-top: 20px;
}

.character-edit-form .form-group {
  margin-bottom: 15px;
}

.character-edit-form label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #555;
}

.character-edit-form input,
.character-edit-form select,
.character-edit-form textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.character-edit-form textarea {
  resize: vertical;
  min-height: 60px;
}

.form-actions {
  display: flex;
  gap: 10px;
  margin-top: 20px;
}

.save-button {
  background-color: #4caf50;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
}

/* Cancel button styling is now in index.css */

/* Relationships */
.character-relationships h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #333;
}

.add-relationship-form {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
  border: 1px solid #e0e0e0;
}

.form-row {
  display: flex;
  gap: 10px;
  align-items: flex-end;
}

.add-relationship-form .form-group {
  flex: 1;
}

.add-relationship-form label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #555;
}

.add-relationship-form select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.add-button {
  background-color: #4caf50;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  height: 36px;
}

.empty-relationships {
  text-align: center;
  padding: 20px;
  background-color: #f5f5f5;
  border-radius: 8px;
  color: #666;
}

.relationships-list {
  margin-top: 20px;
}

.relationship-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background-color: #f9f9f9;
  border-radius: 4px;
  margin-bottom: 10px;
}

.relationship-info {
  display: flex;
  flex-direction: column;
  gap: 10px;
  flex: 1;
}

.relationship-info > div:first-child {
  display: flex;
  align-items: center;
  gap: 10px;
}

.related-character {
  font-weight: 500;
  color: #333;
}

.relationship-type select {
  padding: 5px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.relationship-strength {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.relationship-strength label {
  font-size: 0.85rem;
  color: #555;
  margin-bottom: 5px;
}

.relationship-strength input[type="range"] {
  width: 100%;
  margin: 5px 0;
}

.strength-labels {
  display: flex;
  justify-content: space-between;
  font-size: 0.75rem;
  color: #777;
  margin-top: 2px;
}

.remove-button {
  background-color: #f44336;
  color: white;
  border: none;
  padding: 5px 10px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .form-row {
    flex-direction: column;
    gap: 15px;
  }

  .add-button {
    width: 100%;
  }
}

/* frontend/src/components/BookList.css */

.book-list-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.book-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.book-list-header h2 {
  margin: 0;
  color: #333;
}

.create-book-button {
  background-color: #4caf50;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
}

.create-book-button:disabled {
  background-color: #a5d6a7;
  cursor: not-allowed;
}

.error-message {
  background-color: #ffebee;
  border: 1px solid #ffcdd2;
  border-radius: 4px;
  padding: 10px 15px;
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.error-message p {
  color: #c62828;
  margin: 0;
}

.error-message button {
  background: none;
  border: none;
  color: #c62828;
  cursor: pointer;
  font-weight: bold;
}

.loading-indicator {
  text-align: center;
  padding: 40px;
  color: #666;
  font-size: 16px;
}

.empty-state {
  text-align: center;
  padding: 40px;
  color: #666;
  background-color: #f5f5f5;
  border-radius: 8px;
  margin-top: 20px;
}

.empty-state p {
  margin: 5px 0;
}

/* Book Grid */
.book-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

/* Book Card */
.book-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
  transition: transform 0.2s, box-shadow 0.2s;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.book-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.book-card.selected {
  border: 2px solid #4caf50;
  box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.3);
}

.book-card.loading-details {
  background-color: #f9f9f9;
}

.book-loading-details {
  padding: 10px 0;
  color: #888;
  font-style: italic;
}

.book-card-content {
  flex: 1;
}

.book-title {
  margin: 0 0 5px 0;
  color: #333;
  font-size: 18px;
}

.book-author {
  margin: 0 0 10px 0;
  color: #666;
  font-style: italic;
}

.book-genre {
  display: inline-block;
  background-color: #e0e0e0;
  padding: 3px 8px;
  border-radius: 12px;
  font-size: 12px;
  margin-bottom: 10px;
}

.book-description {
  margin: 10px 0 0 0;
  color: #555;
  font-size: 14px;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.book-card-actions {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  gap: 10px;
  margin-top: 15px;
  padding-top: 10px;
  border-top: 1px solid #eee;
}

.edit-button {
  background-color: #2196f3;
  color: white;
  border: none;
  padding: 5px 10px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.delete-button {
  background-color: #f44336;
  color: white;
  border: none;
  padding: 5px 10px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.select-button {
  background-color: #4caf50;
  color: white;
  border: none;
  padding: 5px 10px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  margin-left: auto;
}

/* Book Form */
.book-form-container {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  border: 1px solid #e0e0e0;
}

.book-form-container h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #333;
}

.book-form .form-group {
  margin-bottom: 15px;
}

.book-form label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #555;
}

.book-form input,
.book-form select,
.book-form textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.book-form textarea {
  resize: vertical;
  min-height: 80px;
}

.form-actions {
  display: flex;
  gap: 10px;
  margin-top: 20px;
}

.save-button {
  background-color: #4caf50;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
}

.save-button:disabled {
  background-color: #a5d6a7;
  cursor: not-allowed;
}

/* Cancel button styling is now in index.css */

/* Responsive Styles */
@media (max-width: 768px) {
  .book-grid {
    grid-template-columns: 1fr;
  }

  .book-list-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .create-book-button {
    width: 100%;
  }
}

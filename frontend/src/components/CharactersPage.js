// frontend/src/components/CharactersPage.js
import React, { useState } from 'react';
import CharacterCard from './CharacterCard';
import CharacterEditor from './CharacterEditor';
import Character3DRelationshipGraph from './Character3DRelationshipGraph';
import './CharactersPage.css';

/**
 * Presentation component for CharactersPage
 * @param {Object} props - Component props
 * @returns {JSX.Element} Characters page UI
 */
const CharactersPage = React.memo((props) => {
  const {
    characters,
    comparedCharacters,
    editingCharacter,
    worldSettingTypes,
    isLoading,
    isGeneratingCharacter, // New prop for AI character generation loading state
    setEditingCharacter,
    setDeleteHeadshotConfirm,
    handleEditCharacter,
    handleCreateCharacter,
    handleSaveCharacter,
    handleDeleteCharacter,
    handleCompareCharacter,
    handleGenerateCharacter,
    handleGenerateHeadshot,
    handleDeleteHeadshot,
    handleCancelEdit,
    handleGenerateName,
    handleGenerateAge,
    handleGenerateRace,
    handleGenerateDescription,
    handleGenerateTraits,
    handleGenerateBackstory,
    handleGenerateArc,
    handleGenerateRelationships,
    handleGenerateGenderIdentity,
    handleGenerateSexualOrientation
  } = props;

  // State for AI generator prompt
  const [generatorPrompt, setGeneratorPrompt] = useState('');
  // State for view mode (grid or graph)
  const [viewMode, setViewMode] = useState('grid');

  // Render character comparison
  const renderCharacterComparison = () => {
    if (comparedCharacters.length === 0) return null;

    return (
      <div className="character-comparison">
        <div className="comparison-header">
          <h3>Character Comparison</h3>
          <button
            className="clear-comparison-button"
            onClick={() => comparedCharacters.forEach(c => handleCompareCharacter(c))}
          >
            Clear Comparison
          </button>
        </div>

        <div className="comparison-container">
          {comparedCharacters.map(character => (
            <div key={character.id} className="comparison-character">
              <div className="comparison-character-header">
                <h4>{character.name}</h4>
                {character.role && <span className="comparison-role">{character.role}</span>}
              </div>

              <div className="comparison-character-content">
                <div className="comparison-main">
                  {character.headshot ? (
                    <div className="comparison-headshot">
                      <img src={character.headshot} alt={character.name} />
                    </div>
                  ) : (
                    <div className="comparison-headshot placeholder">
                      <span>{character.name.charAt(0)}</span>
                    </div>
                  )}

                  <div className="comparison-details">
                    {character.age && (
                      <div className="comparison-item">
                        <span className="comparison-label">Age:</span> {character.age}
                      </div>
                    )}

                    {character.race && (
                      <div className="comparison-item">
                        <span className="comparison-label">Race:</span> {character.race}
                      </div>
                    )}

                    {character.gender_identity ? (
                      <div className="comparison-item">
                        <span className="comparison-label">Gender Identity:</span> {character.gender_identity}
                      </div>
                    ) : null}

                    {character.sexual_orientation ? (
                      <div className="comparison-item">
                        <span className="comparison-label">Sexual Orientation:</span> {character.sexual_orientation}
                      </div>
                    ) : null}
                  </div>
                </div>

                {character.description && (
                  <div className="comparison-section">
                    <span className="comparison-label">Description:</span>
                    <p>{character.description}</p>
                  </div>
                )}

                {character.traits && character.traits.length > 0 && (
                  <div className="comparison-section">
                    <span className="comparison-label">Traits:</span>
                    <div className="comparison-traits">
                      {character.traits.map((trait, index) => (
                        <span key={index} className="comparison-trait">{trait}</span>
                      ))}
                    </div>
                  </div>
                )}

                {character.relationships && Array.isArray(character.relationships) && character.relationships.length > 0 && (
                  <div className="comparison-section">
                    <span className="comparison-label">Relationships:</span>
                    <ul className="comparison-relationships">
                      {character.relationships.map((rel, index) => {
                        const name = rel.name || rel.relatedCharacterName || 'Unknown';
                        const type = rel.type || rel.relationshipType || 'Unknown';
                        const description = rel.description || '';
                        const strength = rel.strength !== undefined ? rel.strength : 3; // Default to 3 if not specified

                        // Helper function to get strength label
                        const getStrengthLabel = (str) => {
                          switch (parseInt(str, 10)) {
                            case 0: return "Enemy";
                            case 1: return "Acquaintance";
                            case 2: return "Casual friend";
                            case 3: return "Close friend";
                            case 4: return "Deep bond";
                            case 5: return "Intimate connection";
                            default: return "Close friend";
                          }
                        };

                        const strengthLabel = getStrengthLabel(strength);

                        return (
                          <li key={index}>
                            <strong>{name}</strong>: {type}
                            <div className="comparison-relationship-strength">
                              <span className="strength-label">{strengthLabel}</span>
                              <div className="strength-bar">
                                <div
                                  className="strength-fill"
                                  style={{
                                    width: `${(parseInt(strength, 10) / 5) * 100}%`,
                                    backgroundColor: parseInt(strength, 10) === 0 ? '#F44336' : '#4CAF50'
                                  }}
                                ></div>
                              </div>
                            </div>
                            {description && <div className="comparison-relationship-description">{description}</div>}
                          </li>
                        );
                      })}
                    </ul>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  // Render AI character generator
  const renderAIGenerator = () => {
    // Calculate how many existing characters we have for relationships
    const existingCharacterCount = characters ? characters.length : 0;

    return (
      <div className="ai-character-generator">
        <h3>AI Character Generator</h3>
        <p>
          Describe the character you want to create. The AI will generate a complete character
          with relationships to your {existingCharacterCount} existing character{existingCharacterCount !== 1 ? 's' : ''}.
        </p>

        <div className="generator-form">
          <textarea
            value={generatorPrompt}
            onChange={(e) => setGeneratorPrompt(e.target.value)}
            placeholder="E.g., Create a wise old mentor character who has a dark secret and connections to the protagonist..."
            rows={4}
          />

          <div className="generator-info">
            <p><strong>The AI will automatically include:</strong></p>
            <ul>
              <li>Character name, age, and race</li>
              <li>Gender identity and sexual orientation</li>
              <li>Detailed description and personality traits</li>
              <li>Role in the story (protagonist, antagonist, etc.)</li>
              <li>Relationships with existing characters</li>
            </ul>
          </div>

          <div className="generator-suggestions">
            <p>Quick templates:</p>
            <div className="suggestion-buttons">
              {worldSettingTypes.map(type => (
                <button
                  key={type}
                  onClick={() => {
                    const suggestions = {
                      fantasy: 'Create a mysterious elven ranger who protects the ancient forest and has connections to other characters',
                      'sci-fi': 'Create a brilliant but eccentric scientist who discovered faster-than-light travel and has a rivalry with another character',
                      mystery: 'Create a sharp-witted detective with unconventional methods who has a complex relationship with the protagonist',
                      romance: 'Create a charming but guarded character with a painful past and romantic potential',
                      historical: 'Create a noble from the Victorian era with progressive ideas who mentors another character',
                      horror: 'Create a paranormal investigator who has seen too much and warns other characters of danger',
                      contemporary: 'Create a tech entrepreneur with a secret double life and connections to multiple characters'
                    };
                    setGeneratorPrompt(suggestions[type] || `Create a complex ${type} character with interesting motivations and connections to existing characters`);
                  }}
                >
                  {type.charAt(0).toUpperCase() + type.slice(1)}
                </button>
              ))}
            </div>
          </div>

          <button
            className="generate-button"
            onClick={() => handleGenerateCharacter(generatorPrompt)}
            disabled={!generatorPrompt.trim() || isGeneratingCharacter}
          >
            {isGeneratingCharacter ? (
              <>
                <span className="loading-spinner-small"></span>
                Generating Character...
              </>
            ) : (
              'Generate Character'
            )}
          </button>
        </div>
      </div>
    );
  };

  return (
    <div className="characters-page">
      <div className="characters-header">
        <h2>Characters</h2>
        <div className="characters-actions">
          <div className="view-toggle">
            <button
              className={`view-toggle-button ${viewMode === 'grid' ? 'active' : ''}`}
              onClick={() => setViewMode('grid')}
              title="Grid View"
            >
              <span className="view-icon grid-icon">☰</span>
            </button>
            <button
              className={`view-toggle-button ${viewMode === 'graph' ? 'active' : ''}`}
              onClick={() => setViewMode('graph')}
              title="Relationship Graph"
            >
              <span className="view-icon graph-icon">⋈</span>
            </button>
          </div>
          <button className="create-character-button" onClick={handleCreateCharacter}>
            Create New Character
          </button>
        </div>
      </div>

      {isLoading && !characters?.length ? (
        <div className="loading-indicator">
          <div className="spinner"></div>
          <p>Loading characters...</p>
        </div>
      ) : (
        <>
          {characters && characters.length > 0 ? (
            <>
              {viewMode === 'grid' ? (
                <div className="characters-grid">
                  {characters.map(character => (
                    <CharacterCard
                      key={character.id}
                      character={character}
                      isCompared={comparedCharacters.some(c => c.id === character.id)}
                      onEdit={handleEditCharacter}
                      onCompare={handleCompareCharacter}
                      onDelete={handleDeleteCharacter}
                    />
                  ))}
                </div>
              ) : (
                <Character3DRelationshipGraph
                  characters={characters}
                  onSelectCharacter={handleEditCharacter}
                />
              )}
            </>
          ) : (
            <div className="no-characters">
              <div className="no-characters-icon">👤</div>
              <h3>No Characters Yet</h3>
              <p>Create your first character to get started with your story.</p>
              <div className="no-characters-actions">
                <button onClick={handleCreateCharacter} className="create-button">
                  Create Character Manually
                </button>
                <span className="or-divider">or</span>
                {renderAIGenerator()}
              </div>
            </div>
          )}

          {comparedCharacters.length > 0 && renderCharacterComparison()}

          {/* Always show the editor when editingCharacter is set or when generating a headshot */}
          {editingCharacter && (
            <div className="character-editor-overlay">
              <CharacterEditor
                character={editingCharacter}
                characters={characters}
                onSave={handleSaveCharacter}
                onCancel={handleCancelEdit}
                onGenerateHeadshot={handleGenerateHeadshot}
                onDeleteHeadshot={handleDeleteHeadshot}
                onGenerateName={handleGenerateName}
                onGenerateAge={handleGenerateAge}
                onGenerateRace={handleGenerateRace}
                onGenerateDescription={handleGenerateDescription}
                onGenerateTraits={handleGenerateTraits}
                onGenerateBackstory={handleGenerateBackstory}
                onGenerateArc={handleGenerateArc}
                onGenerateRelationships={handleGenerateRelationships}
                onGenerateGenderIdentity={handleGenerateGenderIdentity}
                onGenerateSexualOrientation={handleGenerateSexualOrientation}
                isLoading={false}
              />
            </div>
          )}

          {!editingCharacter && characters && characters.length > 0 && (
            <div className="ai-generator-section">
              <h3>Need more characters?</h3>
              {renderAIGenerator()}
            </div>
          )}
        </>
      )}

      {/* AI Character Generation Loading Overlay */}
      {isGeneratingCharacter && !editingCharacter && (
        <div className="ai-generation-overlay">
          <div className="ai-generation-modal">
            <div className="spinner"></div>
            <h3>Generating Character</h3>
            <p>Creating a unique character with AI...</p>
            <p className="generation-tip">This may take 10-15 seconds</p>
          </div>
        </div>
      )}
    </div>
  );
});

export default CharactersPage;

// frontend/src/components/ClimateSelectionModal.js
import React from 'react';
import './ClimateSelectionModal.css';

/**
 * Modal for selecting between planetary and regional climate systems
 * @param {Object} props - Component props
 * @returns {JSX.Element} ClimateSelectionModal UI
 */
const ClimateSelectionModal = ({ onSelect, onClose }) => {
  return (
    <div className="modal-overlay">
      <div className="climate-selection-modal">
        <div className="modal-header">
          <h2>Select Climate System Type</h2>
          <button className="close-button" onClick={onClose}>×</button>
        </div>

        <div className="selection-options">
          <div 
            className="selection-option" 
            onClick={() => onSelect('planetary_climate_system')}
          >
            <div className="option-icon">🌍</div>
            <h3>Planetary Climate System</h3>
            <p>Create a global climate system that affects an entire world or planet</p>
          </div>

          <div 
            className="selection-option"
            onClick={() => onSelect('climate_system')}
          >
            <div className="option-icon">🌦️</div>
            <h3>Regional Climate System</h3>
            <p>Create a climate system for a specific region or area</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ClimateSelectionModal;

/* frontend/src/components/MagicSystemModal.css */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  overflow-y: auto;
  padding: 20px;
}

.magic-system-modal {
  background-color: var(--background-primary);
  border-radius: 8px;
  box-shadow: var(--shadow-lg);
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid var(--border-color);
  position: sticky;
  top: 0;
  background-color: var(--background-primary);
  z-index: 10;
}

.modal-header h2 {
  margin: 0;
  color: var(--text-primary);
  font-size: 1.5rem;
}

.close-button {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--text-secondary);
  transition: color 0.2s;
}

.close-button:hover {
  color: var(--text-primary);
}

.parent-info {
  padding: 10px 20px;
  background-color: var(--background-secondary);
  color: var(--text-secondary);
  font-size: 0.9rem;
  border-bottom: 1px solid var(--border-color);
}

.magic-system-form {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.form-section {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.form-section h3 {
  margin: 0 0 10px 0;
  color: var(--text-primary);
  font-size: 1.2rem;
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 10px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.form-group label {
  font-weight: 500;
  color: var(--text-primary);
  display: flex;
  align-items: center;
}

.required-indicator {
  color: var(--error);
  margin-left: 5px;
}

.form-group input,
.form-group textarea,
.form-group select {
  padding: 10px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--background-secondary);
  color: var(--text-primary);
  font-family: inherit;
  transition: border-color 0.2s;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  border-color: var(--primary);
  outline: none;
}

.form-group.has-error input,
.form-group.has-error textarea,
.form-group.has-error select {
  border-color: var(--error);
}

.error-message {
  color: var(--error);
  font-size: 0.85rem;
  margin-top: 5px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid var(--border-color);
}

.save-button {
  padding: 10px 20px;
  background-color: var(--primary);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
}

.save-button:hover {
  background-color: var(--primary-dark);
}

.cancel-button {
  padding: 10px 20px;
  background-color: var(--background-secondary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
}

.cancel-button:hover {
  background-color: var(--background-hover);
}

/* Dark theme adjustments */
.theme-dark .magic-system-modal {
  background-color: var(--background-primary-dark);
}

.theme-dark .modal-header {
  background-color: var(--background-primary-dark);
}

.theme-dark .parent-info {
  background-color: var(--background-secondary-dark);
}

.theme-dark .form-group input,
.theme-dark .form-group textarea,
.theme-dark .form-group select {
  background-color: var(--background-secondary-dark);
  color: var(--text-primary-dark);
}

.theme-dark .cancel-button {
  background-color: var(--background-secondary-dark);
  color: var(--text-primary-dark);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .magic-system-modal {
    width: 95%;
    max-height: 95vh;
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .save-button, .cancel-button {
    width: 100%;
  }
}

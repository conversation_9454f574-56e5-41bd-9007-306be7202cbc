/* frontend/src/components/WorldCategoryFilter.css */
.world-category-filter {
  margin-bottom: 15px;
}

.filter-buttons {
  display: flex;
  gap: 10px;
  margin-bottom: 10px;
}

.filter-toggle-button,
.order-toggle-button {
  padding: 8px 16px;
  background: var(--background-tertiary);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  color: var(--text-primary);
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.2s ease;
  flex: 1;
  text-align: center;
}

.filter-toggle-button:hover,
.order-toggle-button:hover {
  background: var(--background-secondary);
}

.filter-panel {
  margin-top: 10px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background: var(--background-primary);
  padding: 15px;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid var(--border-color);
}

.filter-header h3 {
  margin: 0;
  font-size: 1.1rem;
}

.reset-button {
  padding: 5px 10px;
  background: var(--background-tertiary);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  color: var(--text-primary);
  cursor: pointer;
  font-size: 0.8rem;
}

.reset-button:hover {
  background: var(--background-secondary);
}

.category-group {
  margin-bottom: 15px;
}

.category-title {
  margin: 0 0 8px 0;
  font-size: 1rem;
  padding-bottom: 5px;
  border-bottom: 1px solid var(--border-color);
}

/* Category-specific colors */
.category-title.cat_cosmology_physical {
  color: #4CAF50;
  border-bottom-color: #4CAF50;
}

.category-title.cat_cultural_social {
  color: #2196F3;
  border-bottom-color: #2196F3;
}

.category-title.cat_economic_material {
  color: #FFC107;
  border-bottom-color: #FFC107;
}

.category-title.cat_knowledge_technology {
  color: #9C27B0;
  border-bottom-color: #9C27B0;
}

.category-title.cat_temporal_historical {
  color: #FF5722;
  border-bottom-color: #FF5722;
}

.category-title.cat_magical_supernatural {
  color: #E91E63;
  border-bottom-color: #E91E63;
}

.category-title.cat_interactive_game {
  color: #00BCD4;
  border-bottom-color: #00BCD4;
}

.category-checkboxes {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 8px;
}

.category-checkbox {
  display: flex;
  align-items: center;
}

.category-checkbox label {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.category-checkbox input {
  margin-right: 8px;
}

/* Responsive styles */
@media (max-width: 768px) {
  .category-checkboxes {
    grid-template-columns: 1fr;
  }
}

/* frontend/src/components/WorldElementsColumn.css */
.world-elements-column {
  display: flex;
  flex-direction: column;
  width: 500px; /* Increased from 300px to 500px */
  height: 100%;
  border-left: 1px solid var(--border-color);
  border-right: 1px solid var(--border-color);
  background-color: var(--background-primary);
}

.elements-column-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--background-secondary);
}

.elements-column-header h3 {
  margin: 0;
  font-size: 1.1rem;
  color: var(--text-primary);
}

.elements-list {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
  max-height: calc(100vh - 150px); /* Adjust height to ensure scrolling */
  scrollbar-width: thin; /* For Firefox */
  scrollbar-color: var(--border-color) transparent; /* For Firefox */
}

/* Custom scrollbar styling for Webkit browsers */
.elements-list::-webkit-scrollbar {
  width: 8px;
}

.elements-list::-webkit-scrollbar-track {
  background: transparent;
}

.elements-list::-webkit-scrollbar-thumb {
  background-color: var(--border-color);
  border-radius: 4px;
  border: 2px solid transparent;
}

.elements-list::-webkit-scrollbar-thumb:hover {
  background-color: var(--text-secondary);
}

.element-item {
  display: flex;
  flex-direction: column;
  padding: 15px;
  border-radius: 6px;
  margin-bottom: 10px;
  cursor: pointer;
  transition: all 0.2s;
  background: var(--background-secondary);
  border-left: 4px solid var(--primary);
  position: relative; /* For absolute positioning of delete button */
}

.element-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

/* Delete button styling */
.element-delete-button {
  position: absolute;
  top: 5px;
  right: 5px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: rgba(244, 67, 54, 0.1);
  color: #f44336;
  border: none;
  font-size: 16px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  opacity: 0; /* Hidden by default */
  transition: opacity 0.2s, background-color 0.2s;
  z-index: 2;
}

.element-item:hover .element-delete-button {
  opacity: 1; /* Show on hover */
}

.element-delete-button:hover {
  background-color: rgba(244, 67, 54, 0.2);
}

.element-item.active {
  background: var(--background-tertiary);
  border-left-color: var(--secondary);
}

.element-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.element-badges {
  display: flex;
  gap: 5px;
  align-items: center;
}

.element-title {
  display: flex;
  align-items: center;
  gap: 5px;
}

.children-indicator {
  font-size: 0.7rem;
  color: var(--text-secondary);
  transition: transform 0.2s;
}

.element-item.active .children-indicator {
  transform: rotate(90deg);
}

.element-header h4 {
  margin: 0;
  font-size: 1.1rem;
  color: var(--text-primary);
}

.importance-badge {
  font-size: 0.7rem;
  padding: 2px 6px;
  border-radius: 10px;
  text-transform: uppercase;
  font-weight: bold;
}

.importance-badge.high {
  background: rgba(244, 67, 54, 0.2);
  color: #f44336;
}

.importance-badge.medium {
  background: rgba(255, 152, 0, 0.2);
  color: #ff9800;
}

.importance-badge.low {
  background: rgba(76, 175, 80, 0.2);
  color: #4caf50;
}

.element-description {
  margin: 0 0 10px 0;
  color: var(--text-secondary);
  font-size: 0.9rem;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.element-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.element-tag {
  font-size: 0.8rem;
  padding: 2px 8px;
  border-radius: 12px;
  background: var(--background-tertiary);
  color: var(--text-secondary);
}

.element-type-label {
  font-size: 0.7rem;
  padding: 2px 6px;
  border-radius: 10px;
  background-color: var(--background-tertiary);
  color: var(--text-secondary);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.no-elements, .no-category-selected {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  text-align: center;
  color: var(--text-secondary);
  padding: 0 20px;
}

.element-buttons {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.create-element-button, .create-magic-system-button, .toggle-templates-button {
  padding: 6px 12px;
  background: var(--primary);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  font-size: 0.9rem;
  transition: all 0.2s;
  white-space: nowrap;
  margin-left: 5px;
}

.create-element-button:hover, .create-magic-system-button:hover, .toggle-templates-button:hover {
  background: var(--button-primary-hover);
}

.toggle-templates-button {
  background: var(--background-tertiary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.toggle-templates-button:hover {
  background: var(--background-secondary);
  border-color: var(--primary);
}

.toggle-templates-button.active {
  background: var(--primary);
  color: white;
  border-color: var(--primary);
}

/* Column tabs */
.column-tabs {
  display: flex;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--background-secondary);
}

.column-tab {
  flex: 1;
  background: none;
  border: none;
  padding: 10px 15px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s;
  color: var(--text-secondary);
  position: relative;
  text-align: center;
}

.column-tab:hover {
  color: var(--text-primary);
  background-color: var(--background-hover);
}

.column-tab.active {
  color: var(--primary);
  font-weight: 600;
}

.column-tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: var(--primary);
}

/* Magic system button uses the same styling as other buttons */

/* Element type styling */
.element-item[data-element-type="organization"] {
  border-left-color: #2196f3;
}

.element-item[data-element-type="law"] {
  border-left-color: #9c27b0;
}

.element-item[data-element-type="law_clause"] {
  border-left-color: #673ab7;
}

.element-item[data-element-type="geography"] {
  border-left-color: #4caf50;
}

.element-item[data-element-type="location"] {
  border-left-color: #ff9800;
}

.element-item[data-element-type="magic_system"] {
  border-left-color: #e91e63;
}

/* Dark theme adjustments */
.theme-dark .world-elements-column {
  background-color: var(--background-secondary);
}

.theme-dark .elements-column-header {
  background-color: var(--background-tertiary);
}

/* Relationships styling */
.element-relationships {
  margin-top: 10px;
  border-top: 1px dashed rgba(0, 0, 0, 0.2);
  padding-top: 8px;
  width: 100%;
  font-size: 0.8rem;
}

.relationships-header {
  font-weight: 600;
  margin-bottom: 5px;
  font-size: 0.85rem;
  color: var(--text-secondary);
}

.relationships-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.relationship-item {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 0.8rem;
  line-height: 1.3;
  padding: 3px 0;
}

.relationship-direction {
  color: var(--primary);
  font-weight: bold;
  margin-right: 2px;
}

.relationship-name {
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 120px;
  color: var(--text-primary);
}

.relationship-type {
  color: var(--text-secondary);
  font-size: 0.75rem;
  white-space: nowrap;
  background-color: rgba(0, 0, 0, 0.05);
  padding: 1px 5px;
  border-radius: 3px;
  margin-left: 3px;
}

.relationship-more {
  font-size: 0.75rem;
  color: var(--text-secondary);
  font-style: italic;
  margin-top: 4px;
}

/* Dark theme adjustments for relationships */
.theme-dark .element-relationships {
  border-top: 1px dashed rgba(255, 255, 255, 0.2);
}

.theme-dark .relationship-direction {
  color: var(--primary-light);
}

/* Responsive styles */
@media (max-width: 768px) {
  .world-elements-column {
    width: 100%;
    border-left: none;
    border-right: none;
    border-bottom: 1px solid var(--border-color);
  }
}

// frontend/src/components/AIProviderSettings.js
import React, { useState, useEffect } from 'react';
import { fetchAIProviders, updateAIProviderPreferences, fetchUserPreferences } from '../services/apiService';
import './AIProviderSettings.css';

/**
 * Component for managing AI provider settings
 * @returns {JSX.Element} AIProviderSettings component
 */
const AIProviderSettings = () => {
  const [textProviders, setTextProviders] = useState([]);
  const [imageProviders, setImageProviders] = useState([]);
  const [imageStyles, setImageStyles] = useState([]);
  const [imageModels, setImageModels] = useState([]);
  const [imageQualities, setImageQualities] = useState([]);
  const [selectedTextProvider, setSelectedTextProvider] = useState('grok');
  const [selectedImageProvider, setSelectedImageProvider] = useState('grok');
  const [selectedImageStyle, setSelectedImageStyle] = useState('default');
  const [selectedImageQuality, setSelectedImageQuality] = useState('standard');
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [message, setMessage] = useState('');
  const [error, setError] = useState('');

  // Fetch available providers and user preferences on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError('');

        // Fetch available providers
        const providersData = await fetchAIProviders();
        setTextProviders(providersData.text_providers || []);
        setImageProviders(providersData.image_providers || []);
        setImageStyles(providersData.image_styles || []);
        setImageModels(providersData.image_models || []);
        setImageQualities(providersData.image_qualities || []);

        // Fetch user preferences
        const preferencesData = await fetchUserPreferences();
        setSelectedTextProvider(preferencesData.text_provider || 'grok');
        setSelectedImageProvider(preferencesData.image_provider || 'grok');
        setSelectedImageStyle(preferencesData.image_style || 'default');
        setSelectedImageQuality(preferencesData.image_quality || 'standard');

        // Store preferences in localStorage for client-side access
        localStorage.setItem('userPreferences', JSON.stringify({
          text_provider: preferencesData.text_provider || 'grok',
          image_provider: preferencesData.image_provider || 'grok',
          image_style: preferencesData.image_style || 'default',
          image_quality: preferencesData.image_quality || 'standard'
        }));
        console.log('User preferences stored in localStorage from fetch');
      } catch (err) {
        console.error('Error fetching AI provider data:', err);
        setError('Failed to load AI provider settings. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Update image quality when image provider changes
  useEffect(() => {
    // Set default quality based on selected provider
    if (selectedImageProvider === 'openai') {
      // For DALL-E 3, default to "standard" if current quality is not compatible
      if (!['standard', 'hd'].includes(selectedImageQuality)) {
        setSelectedImageQuality('standard');
      }
    } else if (selectedImageProvider === 'openai_gpt') {
      // For GPT Image 1, default to "high" if current quality is not compatible
      if (!['high', 'medium', 'low'].includes(selectedImageQuality)) {
        setSelectedImageQuality('high');
      }
    }
  }, [selectedImageProvider, selectedImageQuality]);

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      setSaving(true);
      setMessage('');
      setError('');

      const preferences = {
        text_provider: selectedTextProvider,
        image_provider: selectedImageProvider,
        image_style: selectedImageStyle,
        image_quality: selectedImageQuality
      };

      const response = await updateAIProviderPreferences(preferences);

      // Store preferences in localStorage for client-side access
      localStorage.setItem('userPreferences', JSON.stringify(preferences));
      console.log('User preferences stored in localStorage:', preferences);

      setMessage('AI provider settings saved successfully!');
      console.log('AI provider settings updated:', response);
    } catch (err) {
      console.error('Error updating AI provider settings:', err);
      setError('Failed to save AI provider settings. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return <div className="ai-provider-settings-loading">Loading AI provider settings...</div>;
  }

  return (
    <div className="ai-provider-settings">
      <h2>AI Provider Settings</h2>
      <p className="ai-provider-description">
        Choose which AI providers to use for text and image generation.
        <br />
        <strong>Note:</strong> To use OpenAI or Anthropic, you need to add your API keys to the backend/.env file.
        See the <code>backend/API_KEYS_GUIDE.md</code> file for instructions.
      </p>

      {error && <div className="ai-provider-error">{error}</div>}
      {message && <div className="ai-provider-success">{message}</div>}

      <form onSubmit={handleSubmit}>
        <div className="ai-provider-section">
          <h3>Text Generation</h3>
          <p>Select the AI provider to use for generating text content.</p>
          <div className="ai-provider-options">
            {textProviders.map(provider => (
              <div key={provider.id} className="ai-provider-option">
                <input
                  type="radio"
                  id={`text-${provider.id}`}
                  name="textProvider"
                  value={provider.id}
                  checked={selectedTextProvider === provider.id}
                  onChange={() => setSelectedTextProvider(provider.id)}
                />
                <label htmlFor={`text-${provider.id}`}>
                  <strong>{provider.name}</strong>
                  <span className="ai-provider-description">{provider.description}</span>
                </label>
              </div>
            ))}
          </div>
        </div>

        <div className="ai-provider-section">
          <h3>Image Generation</h3>
          <p>Select the AI provider to use for generating images.</p>
          <div className="ai-provider-options">
            {imageProviders.map(provider => (
              <div key={provider.id} className="ai-provider-option">
                <input
                  type="radio"
                  id={`image-${provider.id}`}
                  name="imageProvider"
                  value={provider.id}
                  checked={selectedImageProvider === provider.id}
                  onChange={() => setSelectedImageProvider(provider.id)}
                />
                <label htmlFor={`image-${provider.id}`}>
                  <strong>{provider.name}</strong>
                  <span className="ai-provider-description">{provider.description}</span>
                </label>
              </div>
            ))}
          </div>
        </div>

        <div className="ai-provider-section">
          <h3>Image Style</h3>
          <p>Select the style to use for generating character headshots and other images.</p>
          <div className="ai-provider-options">
            {imageStyles.map(style => (
              <div key={style.id} className="ai-provider-option">
                <input
                  type="radio"
                  id={`style-${style.id}`}
                  name="imageStyle"
                  value={style.id}
                  checked={selectedImageStyle === style.id}
                  onChange={() => setSelectedImageStyle(style.id)}
                />
                <label htmlFor={`style-${style.id}`}>
                  <strong>{style.name}</strong>
                  <span className="ai-provider-description">{style.description}</span>
                </label>
              </div>
            ))}
          </div>
        </div>



        <div className="ai-provider-section">
          <h3>Image Quality</h3>
          <p>Select the quality level for generated images.</p>
          <div className="ai-provider-options">
            {imageQualities
              .filter(quality => {
                // Filter quality options based on selected image provider
                if (selectedImageProvider === 'openai') {
                  // For DALL-E 3, only show "standard" and "hd"
                  return ['standard', 'hd'].includes(quality.id);
                } else if (selectedImageProvider === 'openai_gpt') {
                  // For GPT Image 1, only show "high", "medium", and "low"
                  return ['high', 'medium', 'low'].includes(quality.id);
                }
                // For Grok or other providers, show all options
                return true;
              })
              .map(quality => (
                <div key={quality.id} className="ai-provider-option">
                  <input
                    type="radio"
                    id={`quality-${quality.id}`}
                    name="imageQuality"
                    value={quality.id}
                    checked={selectedImageQuality === quality.id}
                    onChange={() => setSelectedImageQuality(quality.id)}
                  />
                  <label htmlFor={`quality-${quality.id}`}>
                    <strong>{quality.name}</strong>
                    <span className="ai-provider-description">{quality.description}</span>
                  </label>
                </div>
              ))
            }
          </div>
        </div>

        <div className="ai-provider-actions">
          <button type="submit" className="ai-provider-save-button" disabled={saving}>
            {saving ? 'Saving...' : 'Save Settings'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default AIProviderSettings;

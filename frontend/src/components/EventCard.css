/* frontend/src/components/EventCard.css */
.event-card {
  background-color: white;
  border: 1px solid #ddd;
  border-left-width: 5px;
  border-radius: 4px;
  padding: 10px;
  margin-bottom: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.2s ease;
  max-width: 100%;
  overflow: hidden;
}

.event-card:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.event-card.dragging {
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
  opacity: 0.8;
}

.event-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.event-type {
  color: white;
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 10px;
  font-weight: bold;
  text-transform: uppercase;
}

.event-id {
  color: #888;
  font-size: 10px;
}

.event-description {
  font-size: 14px;
  margin-bottom: 10px;
  line-height: 1.4;
  word-break: break-word;
}

.event-metadata {
  font-size: 12px;
  border-top: 1px solid #eee;
  padding-top: 8px;
}

.event-characters,
.event-locations {
  margin-bottom: 5px;
}

.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  margin-top: 3px;
}

.character-tag {
  background-color: #d1e7dd;
  color: #0f5132;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 11px;
}

.location-tag {
  background-color: #cfe2ff;
  color: #084298;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 11px;
}

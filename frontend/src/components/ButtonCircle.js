// frontend/src/components/ButtonCircle.js
import React, { useCallback } from 'react';
import './ButtonCircle.css';

/**
 * ButtonCircle component for navigation buttons
 * @param {Object} props - Component props
 * @param {string} props.label - Button label (e.g., "W", "B", "P", "Wo", "C")
 * @param {Function} props.onClick - Click handler for navigation
 * @returns {JSX.Element} Circular button UI
 */
const ButtonCircle = React.memo(({ label, onClick }) => {
  // Handle button click with debug logging
  const handleClick = useCallback(() => {
    console.debug('ButtonCircle clicked:', label);
    onClick();
  }, [label, onClick]);

  // Render debug log
  console.debug('ButtonCircle rendered with label:', label);

  return (
    <button className="button-circle" onClick={handleClick}>
      {label}
    </button>
  );
});

export default ButtonCircle;
/* frontend/src/components/BookDetailsPage.css */

.book-details-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.loading-indicator {
  text-align: center;
  padding: 40px;
  color: #666;
  font-size: 16px;
}

.no-book-selected {
  text-align: center;
  padding: 40px;
  background-color: #f5f5f5;
  border-radius: 8px;
}

.no-book-selected h2 {
  margin-top: 0;
  color: #333;
}

.no-book-selected button {
  background-color: #4caf50;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  margin-top: 15px;
}

.error-message {
  background-color: #ffebee;
  border: 1px solid #ffcdd2;
  border-radius: 4px;
  padding: 10px 15px;
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.error-message p {
  color: #c62828;
  margin: 0;
}

.error-message button {
  background: none;
  border: none;
  color: #c62828;
  cursor: pointer;
  font-weight: bold;
}

/* Book Header */
.book-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 30px;
}

.book-info h1 {
  margin: 0 0 5px 0;
  color: #333;
  font-size: 28px;
}

.book-author {
  margin: 0 0 10px 0;
  color: #666;
  font-style: italic;
  font-size: 16px;
}

.book-genre {
  display: inline-block;
  background-color: #e0e0e0;
  padding: 5px 10px;
  border-radius: 16px;
  font-size: 14px;
}

.book-actions {
  display: flex;
  gap: 10px;
}

.write-button {
  background-color: #4caf50;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  font-size: 16px;
}

.edit-button {
  background-color: #2196f3;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  font-size: 16px;
  margin-right: 10px;
}

/* Book Edit Form */
.book-edit-form {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 30px;
}

.book-edit-form .form-group {
  margin-bottom: 15px;
}

.book-edit-form label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #333;
}

.book-edit-form input,
.book-edit-form textarea {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px;
}

.book-edit-form textarea {
  resize: vertical;
  min-height: 100px;
}

.form-actions {
  display: flex;
  gap: 10px;
  margin-top: 20px;
}

.save-button {
  background-color: #4caf50;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  font-size: 16px;
}

/* Cancel button styling is now in index.css */

/* Book Description */
.book-description {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 30px;
}

.book-description h3 {
  margin-top: 0;
  margin-bottom: 10px;
  color: #333;
}

.book-description p {
  margin: 0;
  line-height: 1.6;
  color: #555;
}

/* Book Stats */
.book-stats {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 30px;
}

.book-stats h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #333;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
}

.stat-item {
  text-align: center;
  padding: 15px;
  background-color: #f5f5f5;
  border-radius: 8px;
}

.stat-value {
  display: block;
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin-bottom: 5px;
}

.stat-label {
  display: block;
  font-size: 14px;
  color: #666;
}

/* Book Navigation */
.book-navigation {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.book-navigation h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #333;
}

.navigation-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
}

.navigation-item {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
}

.navigation-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.navigation-icon {
  font-size: 32px;
  margin-bottom: 10px;
}

.navigation-item h4 {
  margin: 0 0 10px 0;
  color: #333;
}

.navigation-item p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .book-header {
    flex-direction: column;
    gap: 15px;
  }

  .book-actions {
    width: 100%;
  }

  .write-button {
    width: 100%;
  }

  .stats-grid,
  .navigation-grid {
    grid-template-columns: 1fr;
  }
}

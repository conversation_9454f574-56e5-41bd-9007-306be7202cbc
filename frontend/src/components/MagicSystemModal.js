// frontend/src/components/MagicSystemModal.js
import React, { useState, useEffect } from 'react';
import './MagicSystemModal.css';
import { getTemplateForElementType } from '../utils/templateUtils';

/**
 * Modal for creating or editing a magic system
 * @param {Object} props - Component props
 * @returns {JSX.Element} MagicSystemModal UI
 */
const MagicSystemModal = ({
  isEdit = false,
  magicSystem = null,
  parentElement = null,
  onClose,
  onSave
}) => {
  // Get template for magic system
  const template = getTemplateForElementType('magic_system');

  // State for form data
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    tags: '',
    importance: 'medium'
  });

  // State for custom fields
  const [customFields, setCustomFields] = useState({});

  // State for validation
  const [errors, setErrors] = useState({});

  // Initialize form data when editing an existing magic system
  useEffect(() => {
    if (isEdit && magicSystem) {
      console.log('MagicSystemModal - Initializing form with existing data:', magicSystem);

      setFormData({
        name: magicSystem.name || '',
        description: magicSystem.description || '',
        tags: magicSystem.tags ? magicSystem.tags.join(', ') : '',
        importance: magicSystem.importance || 'medium'
      });

      setCustomFields(magicSystem.custom_fields || {});
    }
  }, [isEdit, magicSystem]);

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear error for this field
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: null
      }));
    }
  };

  // Handle custom field changes
  const handleCustomFieldChange = (fieldId, value) => {
    setCustomFields(prev => ({
      ...prev,
      [fieldId]: value
    }));

    // Clear error for this field
    if (errors[`custom_${fieldId}`]) {
      setErrors(prev => ({
        ...prev,
        [`custom_${fieldId}`]: null
      }));
    }
  };

  // Validate form
  const validateForm = () => {
    const newErrors = {};

    // Validate required fields
    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Description is required';
    }

    // Validate required custom fields
    template.fields.forEach(field => {
      if (field.required && !customFields[field.id]) {
        newErrors[`custom_${field.id}`] = `${field.label} is required`;
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();

    // Validate form
    if (!validateForm()) {
      return;
    }

    // Process tags
    const tags = formData.tags
      .split(',')
      .map(tag => tag.trim())
      .filter(tag => tag.length > 0);

    // Create element data
    const elementData = {
      name: formData.name,
      description: formData.description,
      tags,
      importance: formData.importance,
      element_type: 'magic_system'
    };

    console.log('MagicSystemModal - Submitting form with data:', { elementData, customFields });

    // Call onSave with the element data and custom fields
    onSave(elementData, customFields);
  };

  return (
    <div className="modal-overlay">
      <div className="magic-system-modal">
        <div className="modal-header">
          <h2>{isEdit ? 'Edit Magic System' : 'Create New Magic System'}</h2>
          <button className="close-button" onClick={onClose}>×</button>
        </div>

        {parentElement && (
          <div className="parent-info">
            <span>Parent: {parentElement.name}</span>
          </div>
        )}

        <form onSubmit={handleSubmit} className="magic-system-form">
          <div className="form-section">
            <h3>Basic Information</h3>

            <div className={`form-group ${errors.name ? 'has-error' : ''}`}>
              <label htmlFor="name">Name</label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                placeholder="Enter magic system name"
                required
              />
              {errors.name && <div className="error-message">{errors.name}</div>}
            </div>

            <div className={`form-group ${errors.description ? 'has-error' : ''}`}>
              <label htmlFor="description">Description</label>
              <textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                placeholder="Describe this magic system"
                rows={4}
                required
              />
              {errors.description && <div className="error-message">{errors.description}</div>}
            </div>

            <div className="form-group">
              <label htmlFor="tags">Tags (comma-separated)</label>
              <input
                type="text"
                id="tags"
                name="tags"
                value={formData.tags}
                onChange={handleInputChange}
                placeholder="e.g. elemental, combat, healing"
              />
            </div>

            <div className="form-group">
              <label htmlFor="importance">Importance</label>
              <select
                id="importance"
                name="importance"
                value={formData.importance}
                onChange={handleInputChange}
              >
                <option value="low">Low</option>
                <option value="medium">Medium</option>
                <option value="high">High</option>
              </select>
            </div>
          </div>

          <div className="form-section">
            <h3>Magic System Details</h3>

            {/* Custom fields based on magic system template */}
            {template.fields.map(field => (
              <div
                className={`form-group ${errors[`custom_${field.id}`] ? 'has-error' : ''}`}
                key={field.id}
              >
                <label htmlFor={`custom-${field.id}`}>
                  {field.label}
                  {field.required && <span className="required-indicator">*</span>}
                </label>

                {field.type === 'textarea' ? (
                  <textarea
                    id={`custom-${field.id}`}
                    value={customFields[field.id] || ''}
                    onChange={(e) => handleCustomFieldChange(field.id, e.target.value)}
                    rows={3}
                    placeholder={field.placeholder}
                    required={field.required}
                  />
                ) : field.type === 'select' ? (
                  <select
                    id={`custom-${field.id}`}
                    value={customFields[field.id] || ''}
                    onChange={(e) => handleCustomFieldChange(field.id, e.target.value)}
                    required={field.required}
                  >
                    <option value="">Select {field.label}</option>
                    {field.options.map(option => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                ) : (
                  <input
                    type="text"
                    id={`custom-${field.id}`}
                    value={customFields[field.id] || ''}
                    onChange={(e) => handleCustomFieldChange(field.id, e.target.value)}
                    placeholder={field.placeholder}
                    required={field.required}
                  />
                )}

                {errors[`custom_${field.id}`] && (
                  <div className="error-message">{errors[`custom_${field.id}`]}</div>
                )}
              </div>
            ))}
          </div>

          <div className="form-actions">
            <button type="submit" className="save-button">
              {isEdit ? 'Save Changes' : 'Create Magic System'}
            </button>
            <button type="button" className="cancel-button" onClick={onClose}>
              Cancel
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default MagicSystemModal;

// frontend/src/components/BrainstormPageRedux.js
import React, { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useParams } from 'react-router-dom';
import {
  fetchAllBrainstormCards,
  selectAllBrainstormCards,
  selectCurrentPage,
  selectCardsByPage,
  selectAllPages,
  setCurrentPage,
  selectBrainstormLoading,
  selectBrainstormError,
  clearError
} from '../redux/slices/brainstormSlice';
import { selectCurrentBook } from '../redux/slices/bookSlice';
import './BrainstormPage.css';

/**
 * BrainstormPageRedux component for managing brainstorm cards
 * @param {Object} props - Component props
 * @param {Function} props.onPageChange - Function to handle page change
 * @returns {JSX.Element} BrainstormPage UI
 */
const BrainstormPageRedux = ({ onPageChange }) => {
  const { bookId, pageId } = useParams();
  const dispatch = useDispatch();

  // Select state from Redux store
  const allCards = useSelector(selectAllBrainstormCards);
  const currentPage = useSelector(selectCurrentPage);
  const pages = useSelector(selectAllPages);
  const currentBook = useSelector(selectCurrentBook);
  const isLoading = useSelector(selectBrainstormLoading);
  const error = useSelector(selectBrainstormError);

  // Use current book ID if not provided in URL
  const activeBookId = bookId || (currentBook && currentBook.book_id);

  // Set current page from URL if provided
  useEffect(() => {
    if (pageId && pages.includes(pageId)) {
      dispatch(setCurrentPage(pageId));
      if (onPageChange) {
        onPageChange(pageId);
      }
    }
  }, [dispatch, pageId, pages, onPageChange]);

  // Fetch brainstorm cards on component mount or when book ID changes
  useEffect(() => {
    if (activeBookId) {
      dispatch(fetchAllBrainstormCards(activeBookId));
    }
  }, [dispatch, activeBookId]);

  // Get cards for the current page
  const cards = useSelector(state => selectCardsByPage(state, currentPage));

  // Handle page change
  const handlePageChange = (page) => {
    dispatch(setCurrentPage(page));
    if (onPageChange) {
      onPageChange(page);
    }
  };

  // Handle clear error
  const handleClearError = () => {
    dispatch(clearError());
  };

  // If no book is selected, show a message
  if (!activeBookId) {
    return (
      <div className="no-book-selected">
        <h2>No Book Selected</h2>
        <p>Please select a book to manage brainstorm cards.</p>
      </div>
    );
  }

  return (
    <div className="brainstorm-page">
      <div className="brainstorm-page-header">
        <h2>Brainstorm</h2>
        <div className="page-tabs">
          {pages.map(page => (
            <button
              key={page}
              className={`page-tab ${page === currentPage ? 'active' : ''}`}
              onClick={() => handlePageChange(page)}
            >
              {page}
            </button>
          ))}
        </div>
      </div>

      {error && (
        <div className="error-message">
          <p>{error}</p>
          <button onClick={handleClearError}>Dismiss</button>
        </div>
      )}

      {isLoading ? (
        <div className="loading-indicator">Loading brainstorm data...</div>
      ) : (
        <div className="brainstorm-content">
          <div className="card-grid">
            {cards.length === 0 ? (
              <div className="empty-state">
                <p>No cards on this page yet.</p>
                <p>Create your first card to get started!</p>
              </div>
            ) : (
              cards.map(card => (
                <div key={card.id} className="brainstorm-card">
                  <h4>{card.title || 'Untitled Card'}</h4>
                  <p>{card.content}</p>
                  {card.type && <div className="card-type">{card.type}</div>}
                </div>
              ))
            )}
          </div>
        </div>
      )}

      <div className="brainstorm-footer">
        <p>Total Cards: {allCards.length} | Cards on this page: {cards.length}</p>
        <p>This is a simplified version of the Brainstorm Page using Redux. Full implementation coming soon.</p>
      </div>
    </div>
  );
};

export default BrainstormPageRedux;

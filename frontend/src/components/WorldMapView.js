// frontend/src/components/WorldMapView.js
import React, { useState, useRef, useEffect } from 'react';
import './WorldMapView.css';

/**
 * Component for displaying and managing world maps
 * @returns {JSX.Element} WorldMapView UI
 */
const WorldMapView = () => {
  // State for maps and map elements
  const [maps, setMaps] = useState([]);
  const [selectedMap, setSelectedMap] = useState(null);
  const [mapElements, setMapElements] = useState([]);
  const [isAddingElement, setIsAddingElement] = useState(false);
  const [elementType, setElementType] = useState('point');
  const [elementName, setElementName] = useState('');
  const [elementCategory, setElementCategory] = useState('places');

  // Refs for canvas
  const canvasRef = useRef(null);
  const canvasContainerRef = useRef(null);

  // Sample map data
  const sampleMaps = [
    { id: 'map1', name: 'Kingdom of Eldoria', description: 'Political map of the kingdom', image_url: null },
    { id: 'map2', name: 'City of Silverhold', description: 'Detailed city map', image_url: null },
    { id: 'map3', name: 'The Great Forest', description: 'Map of the enchanted forest', image_url: null }
  ];

  // Sample map elements
  const sampleMapElements = [
    { id: 'elem1', map_id: 'map1', name: 'Capital City', type: 'point', category: 'places', x: 150, y: 100, properties: { importance: 'high' } },
    { id: 'elem2', map_id: 'map1', name: 'Eastern Mountains', type: 'area', category: 'places', points: [[200, 50], [300, 50], [350, 150], [250, 200]], properties: { terrain: 'mountains' } },
    { id: 'elem3', map_id: 'map1', name: 'Trade Route', type: 'path', category: 'places', points: [[150, 100], [250, 150], [350, 100]], properties: { type: 'road' } }
  ];

  // Initialize with sample data
  useEffect(() => {
    setMaps(sampleMaps);
    if (sampleMaps.length > 0) {
      setSelectedMap(sampleMaps[0]);
    }
  }, []);

  // Load map elements when selected map changes
  useEffect(() => {
    if (selectedMap) {
      const filteredElements = sampleMapElements.filter(elem => elem.map_id === selectedMap.id);
      setMapElements(filteredElements);
    } else {
      setMapElements([]);
    }
  }, [selectedMap]);

  // Draw map and elements on canvas
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas || !selectedMap) return;

    const ctx = canvas.getContext('2d');
    const container = canvasContainerRef.current;

    // Set canvas size to match container
    canvas.width = container.clientWidth;
    canvas.height = container.clientHeight;

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Draw background grid
    ctx.strokeStyle = '#e0e0e0';
    ctx.lineWidth = 0.5;

    const gridSize = 20;
    for (let x = 0; x < canvas.width; x += gridSize) {
      ctx.beginPath();
      ctx.moveTo(x, 0);
      ctx.lineTo(x, canvas.height);
      ctx.stroke();
    }

    for (let y = 0; y < canvas.height; y += gridSize) {
      ctx.beginPath();
      ctx.moveTo(0, y);
      ctx.lineTo(canvas.width, y);
      ctx.stroke();
    }

    // Draw map elements
    mapElements.forEach(element => {
      // Set styles based on category
      switch (element.category) {
        case 'places':
          ctx.fillStyle = '#4caf50';
          ctx.strokeStyle = '#2e7d32';
          break;
        case 'magic_systems':
          ctx.fillStyle = '#9c27b0';
          ctx.strokeStyle = '#6a1b9a';
          break;
        case 'technology':
          ctx.fillStyle = '#ff9800';
          ctx.strokeStyle = '#e65100';
          break;
        case 'groups_societies':
          ctx.fillStyle = '#2196f3';
          ctx.strokeStyle = '#0d47a1';
          break;
        case 'species':
          ctx.fillStyle = '#e91e63';
          ctx.strokeStyle = '#880e4f';
          break;
        default:
          ctx.fillStyle = '#607d8b';
          ctx.strokeStyle = '#263238';
      }

      // Draw based on element type
      if (element.type === 'point') {
        ctx.beginPath();
        ctx.arc(element.x, element.y, 8, 0, Math.PI * 2);
        ctx.fill();
        ctx.stroke();

        // Draw label
        ctx.fillStyle = '#000';
        ctx.font = '12px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(element.name, element.x, element.y + 20);
      } else if (element.type === 'area' && element.points && element.points.length > 2) {
        ctx.beginPath();
        ctx.moveTo(element.points[0][0], element.points[0][1]);

        for (let i = 1; i < element.points.length; i++) {
          ctx.lineTo(element.points[i][0], element.points[i][1]);
        }

        ctx.closePath();
        ctx.globalAlpha = 0.3;
        ctx.fill();
        ctx.globalAlpha = 1;
        ctx.stroke();

        // Draw label at center of area
        const centerX = element.points.reduce((sum, point) => sum + point[0], 0) / element.points.length;
        const centerY = element.points.reduce((sum, point) => sum + point[1], 0) / element.points.length;

        ctx.fillStyle = '#000';
        ctx.font = '12px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(element.name, centerX, centerY);
      } else if (element.type === 'path' && element.points && element.points.length > 1) {
        ctx.beginPath();
        ctx.moveTo(element.points[0][0], element.points[0][1]);

        for (let i = 1; i < element.points.length; i++) {
          ctx.lineTo(element.points[i][0], element.points[i][1]);
        }

        ctx.lineWidth = 3;
        ctx.stroke();
        ctx.lineWidth = 1;

        // Draw label at middle point
        const middleIndex = Math.floor(element.points.length / 2);
        const middleX = element.points[middleIndex][0];
        const middleY = element.points[middleIndex][1];

        ctx.fillStyle = '#000';
        ctx.font = '12px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(element.name, middleX, middleY - 10);
      }
    });
  }, [selectedMap, mapElements]);

  // Handle map selection
  const handleMapSelect = (mapId) => {
    const map = maps.find(m => m.id === mapId);
    setSelectedMap(map);
  };

  // Handle adding new element
  const handleAddElement = () => {
    setIsAddingElement(true);
  };

  // Handle canvas click for adding elements
  const handleCanvasClick = (e) => {
    if (!isAddingElement || !selectedMap) return;

    const rect = canvasRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    // Create new element
    const newElement = {
      id: `elem${Date.now()}`,
      map_id: selectedMap.id,
      name: elementName || 'New Element',
      type: elementType,
      category: elementCategory,
      x,
      y,
      properties: {}
    };

    // For area and path types, we would need more complex interaction
    // This is a simplified version just for the point type
    if (elementType === 'point') {
      setMapElements([...mapElements, newElement]);
      setIsAddingElement(false);
      setElementName('');
    }
  };

  return (
    <div className="world-map-view">
      <div className="map-controls">
        <div className="map-selector">
          <h3>Maps</h3>
          <select
            value={selectedMap ? selectedMap.id : ''}
            onChange={(e) => handleMapSelect(e.target.value)}
            className="map-select"
          >
            {maps.map(map => (
              <option key={map.id} value={map.id}>{map.name}</option>
            ))}
          </select>
          <button className="create-map-button">Create New Map</button>
        </div>

        {selectedMap && (
          <div className="element-controls">
            {isAddingElement ? (
              <div className="add-element-form">
                <input
                  type="text"
                  placeholder="Element name"
                  value={elementName}
                  onChange={(e) => setElementName(e.target.value)}
                  className="element-name-input"
                />
                <select
                  value={elementType}
                  onChange={(e) => setElementType(e.target.value)}
                  className="element-type-select"
                >
                  <option value="point">Point</option>
                  <option value="area">Area</option>
                  <option value="path">Path</option>
                </select>
                <select
                  value={elementCategory}
                  onChange={(e) => setElementCategory(e.target.value)}
                  className="element-category-select"
                >
                  <option value="places">Places</option>
                  <option value="magic_systems">Magic Systems</option>
                  <option value="technology">Technology</option>
                  <option value="groups_societies">Groups & Societies</option>
                  <option value="species">Species</option>
                </select>
                <button
                  className="cancel-button"
                  onClick={() => setIsAddingElement(false)}
                >
                  Cancel
                </button>
                <div className="adding-instructions">
                  Click on the map to place the element
                </div>
              </div>
            ) : (
              <button
                className="add-element-button"
                onClick={handleAddElement}
              >
                Add Element
              </button>
            )}
          </div>
        )}
      </div>

      <div className="map-container" ref={canvasContainerRef}>
        {selectedMap ? (
          <canvas
            ref={canvasRef}
            className="map-canvas"
            onClick={handleCanvasClick}
          />
        ) : (
          <div className="no-map-selected">
            <p>Select a map or create a new one</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default WorldMapView;

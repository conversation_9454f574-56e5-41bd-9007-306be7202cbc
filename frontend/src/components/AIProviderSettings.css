/* frontend/src/components/AIProviderSettings.css */

.ai-provider-settings {
  background-color: var(--background-primary);
  border-radius: 8px;
  box-shadow: var(--shadow-md);
  padding: 24px;
  margin-bottom: 24px;
}

.ai-provider-settings h2 {
  margin-top: 0;
  margin-bottom: 16px;
  color: var(--text-primary);
  font-size: 1.5rem;
}

.ai-provider-description {
  color: var(--text-secondary);
  margin-bottom: 24px;
}

.ai-provider-section {
  margin-bottom: 24px;
}

.ai-provider-section h3 {
  margin-top: 0;
  margin-bottom: 8px;
  color: var(--text-primary);
  font-size: 1.2rem;
}

.ai-provider-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 16px;
}

.ai-provider-option {
  display: flex;
  align-items: flex-start;
  padding: 12px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  transition: all 0.2s ease;
}

.ai-provider-option:hover {
  background-color: var(--background-secondary);
}

.ai-provider-option input[type="radio"] {
  margin-top: 4px;
  margin-right: 12px;
}

.ai-provider-option label {
  display: flex;
  flex-direction: column;
  cursor: pointer;
  width: 100%;
}

.ai-provider-option strong {
  margin-bottom: 4px;
  color: var(--text-primary);
}

.ai-provider-option .ai-provider-description {
  font-size: 0.9rem;
  margin-bottom: 0;
}

.ai-provider-option input[type="radio"]:checked + label {
  font-weight: 500;
}

.ai-provider-option input[type="radio"]:checked + label strong {
  color: var(--primary);
}

.ai-provider-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 24px;
}

.ai-provider-save-button {
  background-color: var(--primary);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 10px 20px;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.ai-provider-save-button:hover {
  background-color: var(--button-primary-hover);
}

.ai-provider-save-button:disabled {
  background-color: var(--button-secondary-background);
  opacity: 0.6;
  cursor: not-allowed;
}

.ai-provider-error {
  background-color: rgba(220, 53, 69, 0.1);
  color: var(--error);
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 16px;
}

.ai-provider-success {
  background-color: rgba(40, 167, 69, 0.1);
  color: var(--accent);
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 16px;
}

.ai-provider-settings-loading {
  text-align: center;
  padding: 24px;
  color: var(--text-secondary);
}

/* Dark theme styles */
.theme-dark .ai-provider-settings {
  background-color: var(--background-tertiary);
  box-shadow: var(--shadow-md);
}

.theme-dark .ai-provider-settings h2,
.theme-dark .ai-provider-section h3 {
  color: var(--text-primary);
}

.theme-dark .ai-provider-description,
.theme-dark .ai-provider-option .ai-provider-description {
  color: var(--text-secondary);
}

.theme-dark .ai-provider-option {
  border-color: var(--border-color);
}

.theme-dark .ai-provider-option:hover {
  background-color: var(--background-secondary);
}

.theme-dark .ai-provider-option strong {
  color: var(--text-primary);
}

.theme-dark .ai-provider-option input[type="radio"]:checked + label strong {
  color: var(--primary);
}

/* Sepia theme styles */
.theme-sepia .ai-provider-settings {
  background-color: var(--background-primary);
  box-shadow: var(--shadow-md);
}

.theme-sepia .ai-provider-settings h2,
.theme-sepia .ai-provider-section h3 {
  color: var(--text-primary);
}

.theme-sepia .ai-provider-description,
.theme-sepia .ai-provider-option .ai-provider-description {
  color: var(--text-secondary);
}

.theme-sepia .ai-provider-option {
  border-color: var(--border-color);
}

.theme-sepia .ai-provider-option:hover {
  background-color: var(--background-secondary);
}

.theme-sepia .ai-provider-option strong {
  color: var(--text-primary);
}

.theme-sepia .ai-provider-option input[type="radio"]:checked + label strong {
  color: var(--primary);
}

.theme-sepia .ai-provider-save-button {
  background-color: var(--primary);
}

.theme-sepia .ai-provider-save-button:hover {
  background-color: var(--button-primary-hover);
}

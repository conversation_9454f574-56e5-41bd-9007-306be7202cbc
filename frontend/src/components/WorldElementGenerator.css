/* frontend/src/components/WorldElementGenerator.css */
.world-element-generator {
  background: var(--background-secondary);
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.world-element-generator h3 {
  margin: 0 0 15px 0;
  color: var(--text-primary);
  font-size: 1.2rem;
}

/* Category info styles */
.category-info {
  background: var(--background-primary);
  border-radius: 6px;
  padding: 12px 15px;
  margin-bottom: 0px;
  border-left: 4px solid var(--primary);
}

.category-info h4 {
  margin: 0 0 5px 0;
  color: var(--text-primary);
  font-size: 1.1rem;
}

.category-info p {
  margin: 0 0 8px 0;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.context-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
}

.context-badge {
  background: var(--primary);
  color: white;
  font-size: 0.75rem;
  padding: 2px 8px;
  border-radius: 12px;
  display: inline-block;
}

.context-note {
  font-size: 0.8rem;
  color: var(--text-secondary);
  font-style: italic;
}

.generator-form {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.form-group label {
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.form-group textarea,
.form-group input {
  padding: 10px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background: var(--background-primary);
  color: var(--text-primary);
  font-family: inherit;
  resize: vertical;
}

.form-group input[type="number"] {
  width: 60px;
}

/* Count row and batch mode toggle */
.count-row {
  display: flex;
  align-items: center;
  gap: 15px;
}

/* Mode toggles row */
.mode-toggles-row {
  margin-bottom: 15px;
}

.mode-toggles {
  display: flex;
  flex-direction: row;
  gap: 20px;
  flex-wrap: wrap;
}

.batch-mode-toggle,
.multi-level-mode-toggle,
.nested-mode-toggle {
  display: flex;
  align-items: center;
  gap: 5px;
}

.toggle-label {
  display: flex;
  align-items: center;
  gap: 5px;
  cursor: pointer;
}

.toggle-text {
  font-size: 0.85rem;
  color: var(--text-secondary);
}

.tooltip {
  position: relative;
  display: inline-block;
}

.tooltip-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: var(--background-tertiary);
  color: var(--text-secondary);
  font-size: 0.7rem;
  cursor: help;
}

.tooltip-text {
  visibility: hidden;
  width: 200px;
  background-color: var(--background-tertiary);
  color: var(--text-primary);
  text-align: center;
  border-radius: 6px;
  padding: 8px;
  position: absolute;
  z-index: 1;
  bottom: 125%;
  left: 50%;
  margin-left: -100px;
  opacity: 0;
  transition: opacity 0.3s;
  font-size: 0.8rem;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.tooltip:hover .tooltip-text {
  visibility: visible;
  opacity: 1;
}

/* Generation options */
.generation-options {
  display: flex;
  margin-bottom: 15px;
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid var(--border-color);
}

.option-button {
  flex: 1;
  padding: 8px 12px;
  background: var(--background-primary);
  border: none;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.2s;
  color: var(--text-secondary);
}

.option-button.active {
  background: var(--primary);
  color: white;
}

.option-button:hover:not(:disabled):not(.active) {
  background: var(--background-hover);
}

.option-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Template selector styles */
.template-selector {
  margin-bottom: 15px;
}

.template-selector label {
  display: block;
  margin-bottom: 8px;
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.template-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 10px;
  margin-bottom: 15px;
}

.template-card {
  background: var(--background-primary);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  padding: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.template-card:hover {
  border-color: var(--primary);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.template-card h4 {
  margin: 0 0 5px 0;
  font-size: 1rem;
  color: var(--text-primary);
}

.template-description {
  margin: 0 0 10px 0;
  font-size: 0.8rem;
  color: var(--text-secondary);
  line-height: 1.3;
}

.template-fields {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.template-field {
  font-size: 0.8rem;
  color: var(--text-secondary);
}

.field-label {
  display: inline-block;
  background: var(--background-tertiary);
  padding: 2px 6px;
  border-radius: 3px;
}

.template-field-more {
  font-size: 0.75rem;
  color: var(--text-secondary);
  margin-top: 4px;
  font-style: italic;
}

/* Prompt suggestions styles */
.prompt-suggestions {
  margin-top: 5px;
  margin-bottom: 10px;
}

.prompt-suggestions h5 {
  margin: 0 0 8px 0;
  color: var(--text-primary);
  font-size: 0.9rem;
  font-weight: 500;
}

.suggestion-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.suggestion-button {
  padding: 6px 12px;
  background: var(--background-tertiary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  cursor: pointer;
  font-size: 0.85rem;
  transition: all 0.2s;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}

.suggestion-button:hover:not(:disabled) {
  background: var(--background-hover);
  border-color: var(--primary);
}

.suggestion-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.error-message {
  color: var(--error);
  font-size: 0.9rem;
  padding: 5px 0;
}

.generate-button {
  padding: 10px 16px;
  background: var(--primary);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: background 0.2s;
  align-self: flex-start;
}

.generate-button:hover:not(:disabled) {
  background: var(--button-primary-hover);
}

.generate-button:disabled {
  background: var(--background-tertiary);
  color: var(--text-secondary);
  cursor: not-allowed;
}

.generator-help-section {
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.generator-tips {
  flex: 1;
}

.generator-tips h4 {
  margin: 0 0 10px 0;
  color: var(--text-primary);
  font-size: 1rem;
}

.generator-tips ul {
  margin: 0;
  padding-left: 20px;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.generator-tips li {
  margin-bottom: 5px;
}

/* Visual aids styles */
.visual-aids-section {
  flex: 1;
  border-top: 1px solid var(--border-color);
  padding-top: 15px;
}

.visual-aids-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.visual-aids-header h4 {
  margin: 0;
  color: var(--text-primary);
  font-size: 1rem;
}

.toggle-visual-aids {
  background: transparent;
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
  padding: 4px 10px;
  border-radius: 4px;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.2s;
}

.toggle-visual-aids:hover {
  background: var(--background-hover);
  color: var(--text-primary);
}

.visual-aids-gallery {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 15px;
  margin-top: 10px;
}

.visual-aid-card {
  background: var(--background-primary);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  overflow: hidden;
  transition: all 0.2s;
}

.visual-aid-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.visual-aid-image {
  width: 100%;
  height: 150px;
  object-fit: cover;
  display: block;
}

.visual-aid-info {
  padding: 10px;
}

.visual-aid-info h5 {
  margin: 0 0 5px 0;
  color: var(--text-primary);
  font-size: 0.9rem;
}

.visual-aid-info p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 0.8rem;
}

/* Dark theme adjustments */
.theme-dark .world-element-generator {
  background: var(--background-tertiary);
}

.theme-dark .category-info {
  background: rgba(0, 0, 0, 0.2);
}

.theme-dark .suggestion-button {
  background: rgba(0, 0, 0, 0.3);
}

.theme-dark .suggestion-button:hover:not(:disabled) {
  background: rgba(0, 0, 0, 0.4);
}

/* Preview styles */
.preview-container {
  background: var(--background-primary);
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
  border: 1px solid var(--border-color);
}

.preview-container h4 {
  margin: 0 0 8px 0;
  color: var(--text-primary);
  font-size: 1.1rem;
}

.preview-description {
  margin: 0 0 15px 0;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.preview-elements {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
  max-height: 400px;
  overflow-y: auto;
  padding-right: 10px;
}

.preview-element {
  background: var(--background-secondary);
  border-radius: 6px;
  padding: 12px;
  border: 1px solid var(--border-color);
}

.preview-element h5 {
  margin: 0 0 8px 0;
  color: var(--text-primary);
  font-size: 1rem;
}

.preview-element p {
  margin: 0 0 10px 0;
  color: var(--text-secondary);
  font-size: 0.9rem;
  line-height: 1.4;
}

/* Parent info in preview */
.parent-info {
  margin: 5px 0;
  font-size: 0.9rem;
  color: var(--text-secondary);
  background: var(--background-tertiary);
  padding: 4px 8px;
  border-radius: 4px;
  display: inline-block;
  margin-bottom: 8px;
}

.parent-label {
  font-weight: bold;
  margin-right: 5px;
}

.parent-value {
  font-style: italic;
}

.element-type {
  margin: 5px 0;
  font-size: 0.9rem;
  color: var(--text-secondary);
  display: inline-block;
  background: var(--background-tertiary);
  padding: 4px 8px;
  border-radius: 4px;
  margin-right: 8px;
}

.type-label {
  font-weight: bold;
  margin-right: 5px;
}

.element-custom-fields {
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px solid var(--border-color);
}

.custom-field {
  margin-bottom: 5px;
  font-size: 0.85rem;
}

.field-name {
  font-weight: 500;
  color: var(--text-primary);
  margin-right: 5px;
  text-transform: capitalize;
}

.field-value {
  color: var(--text-secondary);
}

.preview-actions {
  display: flex;
  gap: 10px;
  margin-top: 15px;
}

.add-button, .regenerate-button, .cancel-button {
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 0.9rem;
  cursor: pointer;
  border: none;
  transition: all 0.2s;
}

.add-button {
  background: var(--primary);
  color: white;
}

.add-button:hover {
  background: var(--button-primary-hover);
}

.regenerate-button {
  background: var(--background-tertiary);
  color: var(--text-primary);
}

.regenerate-button:hover {
  background: var(--background-hover);
}

.cancel-button {
  background: transparent;
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
}

.cancel-button:hover {
  background: var(--background-hover);
  color: var(--text-primary);
}

/* Parent selector styles */
.parent-selector {
  margin-top: 10px;
  padding: 10px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--background-primary);
}

.parent-selector label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: var(--text-primary);
}

.parent-selector select {
  width: 100%;
  padding: 8px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--background-primary);
  color: var(--text-primary);
  font-size: 0.9rem;
}

.no-parents-message {
  color: var(--error);
  font-size: 0.85rem;
  margin-top: 5px;
  font-style: italic;
}

.selected-parent-info {
  margin-top: 10px;
  padding: 8px;
  background-color: var(--background-tertiary);
  border-radius: 4px;
}

.selected-parent-info h5 {
  margin: 0 0 5px 0;
  font-size: 0.9rem;
  color: var(--text-primary);
}

.selected-parent-info p {
  margin: 0;
  font-weight: bold;
  color: var(--primary);
}

/* Sub-element count styles */
.sub-element-count {
  margin-top: 10px;
  padding: 10px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--background-primary);
}

.sub-element-count label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: var(--text-primary);
  font-size: 0.9rem;
}

.sub-element-count input {
  width: 60px;
  padding: 8px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--background-primary);
  color: var(--text-primary);
}

/* Multi-level mode styles */
.multi-level-mode-toggle {
  margin-bottom: 10px;
}

.nesting-configuration {
  margin-top: 15px;
  padding: 15px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--background-primary);
}

.nesting-level-control {
  margin-bottom: 15px;
}

.nesting-level-control label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: var(--text-primary);
  font-size: 0.9rem;
}

.nesting-level-control select {
  width: 100%;
  padding: 8px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--background-primary);
  color: var(--text-primary);
  font-size: 0.9rem;
}

.elements-per-level {
  margin-top: 15px;
}

.elements-per-level label {
  display: block;
  margin-bottom: 10px;
  font-weight: 500;
  color: var(--text-primary);
  font-size: 0.9rem;
}

.level-counts {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.level-count {
  display: flex;
  align-items: center;
  gap: 10px;
}

.level-count span {
  min-width: 70px;
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.level-count input {
  width: 60px;
  padding: 8px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--background-primary);
  color: var(--text-primary);
}

/* Parent selector container */
.parent-selector-container {
  margin-top: 10px;
  padding: 15px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--background-primary);
}

.parent-selector-container label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--text-primary);
  font-size: 0.9rem;
}

.parent-selector-container select {
  width: 100%;
  padding: 8px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--background-primary);
  color: var(--text-primary);
  font-size: 0.9rem;
  margin-bottom: 10px;
}

/* Hierarchical preview styles */
.hierarchical-preview {
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: 20px;
}

.hierarchy-level {
  border: 1px solid var(--border-color);
  border-radius: 8px;
  overflow: hidden;
}

.level-header {
  background-color: var(--primary);
  color: white;
  margin: 0;
  padding: 10px 15px;
  font-size: 1rem;
}

.level-elements {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 15px;
  padding: 15px;
  background-color: var(--background-secondary);
}

.level-0-element {
  border-left: 4px solid var(--primary);
}

.level-1-element {
  border-left: 4px solid var(--secondary);
}

.level-2-element {
  border-left: 4px solid var(--accent);
}

.children-indicator {
  margin-top: 10px;
  padding: 5px 10px;
  background-color: var(--background-tertiary);
  border-radius: 4px;
  display: inline-flex;
  align-items: center;
  gap: 5px;
}

.children-icon {
  font-size: 1.2rem;
  color: var(--primary);
}

.children-text {
  font-size: 0.8rem;
  color: var(--text-secondary);
}

/* Nested preview styles */
.nested-preview {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.parent-element {
  position: relative;
  border-left: 4px solid var(--primary);
  margin-bottom: 20px;
  background-color: var(--background-primary);
}

.element-badge {
  position: absolute;
  top: -10px;
  left: 10px;
  background-color: var(--primary);
  color: white;
  font-size: 0.7rem;
  padding: 2px 8px;
  border-radius: 10px;
  font-weight: bold;
}

.sub-elements-preview {
  margin-top: 15px;
  padding-top: 10px;
  border-top: 1px dashed var(--border-color);
}

.sub-elements-preview h6 {
  margin: 0 0 10px 0;
  color: var(--text-primary);
  font-size: 0.9rem;
}

.sub-elements-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 10px;
}

.sub-element {
  background-color: var(--background-tertiary);
  border-left: 3px solid var(--secondary);
}

/* Responsive styles */
@media (max-width: 768px) {
  .world-element-generator {
    padding: 15px;
  }

  .suggestion-buttons {
    flex-direction: column;
    gap: 6px;
  }

  .suggestion-button {
    max-width: 100%;
  }

  .visual-aids-gallery {
    grid-template-columns: 1fr;
  }

  .preview-elements {
    grid-template-columns: 1fr;
  }

  .template-cards {
    grid-template-columns: 1fr;
  }
}

// frontend/src/components/WorldElementsColumn.js
import React, { useState, useEffect } from 'react';
import './WorldElementsColumn.css';
import DeleteConfirmationModal from './DeleteConfirmationModal';
import ElementTypeSelectionModal from './ElementTypeSelectionModal';
import TemplateSelectionModal from './TemplateSelectionModal';
import TemplateBrowser from './TemplateBrowser';
import { getColorForType } from '../utils/colorMapping';
import { getElementTypeLabel } from '../utils/templateUtils';
import { hasMultipleElementOptions, getElementOptionsForCategory } from '../utils/categoryElementOptions';
import { getHeaders } from '../services/apiService';
import { BASE_URL } from '../utils/apiConfig';

/**
 * Component for displaying elements of a selected category
 * @param {Object} props - Component props
 * @returns {JSX.Element} WorldElementsColumn UI
 */
const WorldElementsColumn = ({
  activeCategory,
  elements = [],
  selectedElement,
  relationships = [],
  columnTab = 'templates', // Default to templates tab
  selectedTemplateId = null,
  onElementSelect,
  onCreateElement,
  onDeleteElement,
  onColumnTabChange,
  onTemplateSelect
}) => {
  // State for delete confirmation modal
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [elementToDelete, setElementToDelete] = useState(null);

  // State for element type selection modal
  const [selectionModalOpen, setSelectionModalOpen] = useState(false);

  // State for template selection modal
  const [templateModalOpen, setTemplateModalOpen] = useState(false);

  // Use the columnTab prop instead of local state
  const showTemplateBrowser = columnTab === 'templates';

  // State for templates
  const [templates, setTemplates] = useState([]);
  // Fetch templates when category changes
  useEffect(() => {
    if (activeCategory) {
      // Use getHeaders for authentication
      const headers = getHeaders();
      console.log('Fetching templates with headers:', headers);

      fetch(`${BASE_URL}/api/templates?category_id=${activeCategory.category_id}`, { headers })
        .then(res => {
          if (!res.ok) {
            throw new Error(`Failed to fetch templates: ${res.status} ${res.statusText}`);
          }
          return res.json();
        })
        .then(data => {
          console.log('Templates fetched in WorldElementsColumn:', data);
          setTemplates(data);
        })
        .catch(err => {
          console.error('Error fetching templates in WorldElementsColumn:', err);
          // Use mock data as fallback
          setTemplates([
            {
              template_id: 'mock_template_1',
              template_name: 'Mock Template 1',
              display_name: 'Mock Template 1',
              description: 'This is a mock template for testing',
              category_id: activeCategory.category_id,
              is_system_template: true,
              version: '1.0'
            },
            {
              template_id: 'mock_template_2',
              template_name: 'Mock Template 2',
              display_name: 'Mock Template 2',
              description: 'Another mock template for testing',
              category_id: activeCategory.category_id,
              is_system_template: true,
              version: '1.0'
            }
          ]);
        });
    }
  }, [activeCategory]);

  // Handle create element button click
  const handleCreateClick = () => {
    if (activeCategory && onCreateElement) {
      // Show the template selection modal instead of element type selection
      setTemplateModalOpen(true);
    }
  };

  // Handle template selection from the browser
  const handleTemplateSelect = (template, elementData, customFields) => {
    if (activeCategory && onCreateElement) {
      console.log('Template selected with form data:', template, elementData, customFields);

      // Create the element with the form data
      onCreateElement({
        ...elementData,
        // Ensure these fields are set correctly
        element_type: template.template_id,
        template_id: template.template_id,
        template_version: template.version || '1.0'
      }, null, template.template_id, customFields);

      // Switch to Elements tab after creating
      if (onColumnTabChange) {
        onColumnTabChange('elements');
      }
    }
  };

  // Handle element type selection
  const handleElementTypeSelect = (elementType) => {
    if (activeCategory && onCreateElement) {
      // Get the label for this element type
      const options = getElementOptionsForCategory(activeCategory.category_id);
      const selectedOption = options.find(opt => opt.value === elementType);
      const elementName = selectedOption ? selectedOption.label : 'Element';

      // Create a default element with the selected type
      onCreateElement({
        name: `New ${elementName}`,
        description: '',
        tags: [],
        importance: 'medium',
        element_type: elementType
      });

      // Close the modal
      setSelectionModalOpen(false);
    }
  };

  // Handle delete button click
  const handleDeleteClick = (e, element) => {
    e.stopPropagation(); // Prevent element selection when clicking delete
    setElementToDelete(element);
    setDeleteModalOpen(true);
  };

  // Handle delete confirmation
  const handleDeleteConfirm = () => {
    if (elementToDelete && onDeleteElement) {
      onDeleteElement(elementToDelete.element_id);
    }
    setDeleteModalOpen(false);
    setElementToDelete(null);
  };

  // Handle delete cancellation
  const handleDeleteCancel = () => {
    setDeleteModalOpen(false);
    setElementToDelete(null);
  };

  // Using imported getElementTypeLabel from elementTemplates.js

  // Get relationships for an element
  const getElementRelationships = (elementId) => {
    console.log('Getting relationships for element:', elementId);
    console.log('All relationships:', relationships);
    console.log('Element ID:', elementId);

    if (!relationships || !relationships.length) {
      console.log('No relationships found');
      return [];
    }

    const filteredRelationships = relationships.filter(rel => {
      console.log('Checking relationship:', rel);
      // Handle both source_id and source_element_id formats
      const sourceId = rel.source_id || rel.source_element_id;
      const targetId = rel.target_id || rel.target_element_id;
      console.log('Source ID:', sourceId, 'Target ID:', targetId);
      return sourceId === elementId || targetId === elementId;
    });

    console.log('Filtered relationships:', filteredRelationships);

    return filteredRelationships.map(rel => {
      // Handle both field formats
      const sourceId = rel.source_id || rel.source_element_id;
      const targetId = rel.target_id || rel.target_element_id;

      // Determine if this element is the source or target
      const isSource = sourceId === elementId;
      const otherElementId = isSource ? targetId : sourceId;

      // Find the other element's name
      const otherElement = elements.find(el => el.element_id === otherElementId);
      console.log('Other element:', otherElement);

      // Use source_name and target_name if available
      let otherElementName = 'Unknown Element';
      if (otherElement) {
        otherElementName = otherElement.name;
      } else if (isSource && rel.target_name) {
        otherElementName = rel.target_name;
      } else if (!isSource && rel.source_name) {
        otherElementName = rel.source_name;
      }

      return {
        relationshipId: rel.relationship_id,
        otherElementId,
        otherElementName,
        type: rel.relationship_type || rel.type, // Handle both formats
        direction: isSource ? 'outgoing' : 'incoming'
      };
    });
  };

  return (
    <div className="world-elements-column">
      {/* Delete confirmation modal */}
      <DeleteConfirmationModal
        isOpen={deleteModalOpen}
        elementName={elementToDelete ? elementToDelete.name : ''}
        onConfirm={handleDeleteConfirm}
        onCancel={handleDeleteCancel}
      />

      {/* Element type selection modal (legacy) */}
      {selectionModalOpen && activeCategory && (
        <ElementTypeSelectionModal
          title={`Select ${activeCategory.name} Type`}
          options={getElementOptionsForCategory(activeCategory.category_id)}
          onSelect={handleElementTypeSelect}
          onClose={() => setSelectionModalOpen(false)}
        />
      )}

      {/* Template selection modal */}
      {templateModalOpen && activeCategory && (
        <TemplateSelectionModal
          title={`Select ${activeCategory.name} Template`}
          categoryId={activeCategory.category_id}
          onSelect={(templateId) => {
            // Find the selected template
            const template = templates.find(t => t.template_id === templateId);
            if (template) {
              handleTemplateSelect(template);
            }
            setTemplateModalOpen(false);
          }}
          onClose={() => setTemplateModalOpen(false)}
        />
      )}

      <div className="elements-column-header">
        <h3>{activeCategory ? activeCategory.name : 'Select a Category'}</h3>
      </div>

      {/* Tabs for Templates and Elements */}
      {activeCategory && (
        <div className="column-tabs">
          <button
            className={`column-tab ${columnTab === 'elements' ? 'active' : ''}`}
            onClick={() => onColumnTabChange('elements')}
          >
            Elements
          </button>
          <button
            className={`column-tab ${columnTab === 'templates' ? 'active' : ''}`}
            onClick={() => onColumnTabChange('templates')}
          >
            Templates
          </button>
        </div>
      )}

      {/* Template browser */}
      {showTemplateBrowser && activeCategory && (
        <TemplateBrowser
          activeCategory={activeCategory}
          onTemplateSelect={handleTemplateSelect}
          onClose={() => onColumnTabChange('elements')}
        />
      )}

      {/* Only show elements list when in elements tab */}
      {!showTemplateBrowser && (
        <div className="elements-list" role="list" aria-label="World elements">
          {!activeCategory ? (
            <div className="no-category-selected">
              <p>Please select a category to view its elements</p>
            </div>
          ) : elements.length > 0 ? (
          elements.map(element => (
            <div
              key={element.element_id}
              className={`element-item ${element.has_children ? 'has-children' : ''} ${selectedElement && selectedElement.element_id === element.element_id ? 'active' : ''}`}
              onClick={() => onElementSelect(element.element_id)}
              data-element-type={element.element_type}
              style={{ borderLeftColor: getColorForType(element.element_type) }}
            >
              {/* Delete button in the top right corner */}
              {onDeleteElement && (
                <button
                  className="element-delete-button"
                  onClick={(e) => handleDeleteClick(e, element)}
                  title="Delete element"
                >
                  ×
                </button>
              )}

              <div className="element-header">
                <div className="element-title">
                  {element.has_children && <span className="children-indicator">▶</span>}
                  <h4>{element.name}</h4>
                </div>
                <div className="element-badges">
                  {element.importance && (
                    <span className={`importance-badge ${element.importance}`}>
                      {element.importance}
                    </span>
                  )}
                  <span
                    className="element-type-label"
                    style={{
                      backgroundColor: getColorForType(element.element_type),
                      color: 'white'
                    }}
                  >
                    {getElementTypeLabel(element.element_type)}
                  </span>
                </div>
              </div>

              <p className="element-description">{element.description}</p>

              {element.tags && element.tags.length > 0 && (
                <div className="element-tags">
                  {element.tags.map(tag => (
                    <span key={tag} className="element-tag">{tag}</span>
                  ))}
                </div>
              )}

              {/* Relationships section */}
              {(() => {
                const elementRelationships = getElementRelationships(element.element_id);
                console.log('Element relationships for', element.name, ':', elementRelationships);
                return elementRelationships.length > 0 ? (
                  <div className="element-relationships">
                    <div className="relationships-header">Connected to:</div>
                    <div className="relationships-list">
                      {elementRelationships.slice(0, 3).map((rel, index) => (
                        <div key={index} className="relationship-item">
                          <span className="relationship-direction">
                            {rel.direction === 'outgoing' ? '→' : '←'}
                          </span>
                          <span className="relationship-name">{rel.otherElementName}</span>
                          <span className="relationship-type">{rel.type}</span>
                        </div>
                      ))}
                      {elementRelationships.length > 3 && (
                        <div className="relationship-more">+{elementRelationships.length - 3} more...</div>
                      )}
                    </div>
                  </div>
                ) : null;
              })()}
            </div>
          ))
        ) : (
          <div className="no-elements">
            <p>No elements found in this category.</p>
            <p>Select a template from the Templates tab to create a new element.</p>
          </div>
        )}
      </div>
      )}
    </div>
  );
};

export default WorldElementsColumn;

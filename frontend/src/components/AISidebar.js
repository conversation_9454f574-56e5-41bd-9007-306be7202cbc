// frontend/src/components/AISidebar.js
import React, { useState, useCallback } from 'react';
import './AISidebar.css';

/**
 * AISidebar component for AI interactions as a floating bubble/chat window
 * @param {Object} props - Component props
 * @param {Array} props.responses - AI-generated responses
 * @param {boolean} props.isLoading - Loading state for AI requests
 * @param {boolean} props.isOpen - Whether the sidebar is open
 * @param {string} props.currentPage - Current app page
 * @param {string} props.selectedNodeId - Selected brainstorm node ID
 * @param {Array} props.selectedNodes - Selected brainstorm nodes for multi-select
 * @param {Function} props.onToggleSidebar - Callback to toggle sidebar
 * @param {Function} props.onPromptSubmit - Callback to submit prompt
 * Simplified to just handle basic text input and display responses
 * @returns {JSX.Element} AI sidebar UI
 */
const AISidebar = React.memo(({
  responses = [],
  isLoading = false,
  isOpen = false,
  onToggleSidebar,
  onPromptSubmit
}) => {
  const [prompt, setPrompt] = useState(''); // User input for AI prompt

  /**
   * Handles form submission for user prompt
   * @param {Object} e - Form submit event
   */
  const handleSubmit = useCallback((e) => {
    e.preventDefault();
    if (onPromptSubmit) {
      onPromptSubmit(prompt);
      setPrompt('');
    }
  }, [prompt, onPromptSubmit]);

  // Render debug log
  console.debug('AISidebar rendered with:', { isOpen, responses: responses.length });

  return (
    <div className={`ai-sidebar ${isOpen ? 'open' : 'closed'}`}>
      <div className="ai-toggle" onClick={onToggleSidebar}>AI</div>
      <div className="ai-content">
        <div className="ai-close" onClick={onToggleSidebar}>X</div> {/* Close button */}
        <div className="ai-responses">
          {responses.length > 0 ? (
            responses.map((resp, index) => (
              <div
                key={index}
                className="ai-response"
                style={{ cursor: 'default' }}
              >
                {resp}
              </div>
            ))
          ) : (
            <div className="ai-empty-state">
              Enter a prompt below to get AI assistance
            </div>
          )}
          {/* Action buttons removed - functionality now handled in the main interface */}
        </div>
        <div className="ai-prompt-container">
          <form onSubmit={handleSubmit}>
            <input
              type="text"
              placeholder={isLoading ? 'Generating...' : 'Ask the AI...'}
              className="ai-prompt"
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              disabled={isLoading}
            />
          </form>
        </div>
      </div>
    </div>
  );
});

export default AISidebar;
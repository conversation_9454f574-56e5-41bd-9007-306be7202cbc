/* frontend/src/components/WorldElementRelationshipManager.css */
.world-element-relationship-manager {
  margin-top: 1.5rem;
  padding: 1rem;
  background-color: var(--background-secondary);
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
  max-height: 100%;
}

.relationship-manager-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.relationship-manager-header h3 {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 500;
  color: var(--text-primary);
}

.add-relationship-button {
  padding: 0.5rem 1rem;
  background-color: var(--accent-color);
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.add-relationship-button:hover {
  background-color: var(--accent-color-dark);
}

/* Relationship List */
.relationships-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.relationship-item {
  padding: 0.75rem;
  background-color: var(--background-primary);
  border-radius: 6px;
  border: 1px solid var(--border-color);
}

.relationship-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.relationship-direction {
  font-weight: 500;
  color: var(--text-primary);
}

.relationship-type {
  padding: 0.25rem 0.5rem;
  background-color: var(--accent-color-light);
  color: var(--accent-color-dark);
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
}

.relationship-type[data-type="contains"],
.relationship-type[data-type="located_in"] {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.relationship-type[data-type="member_of"],
.relationship-type[data-type="belongs_to"] {
  background-color: #e3f2fd;
  color: #1565c0;
}

.relationship-type[data-type="created_by"],
.relationship-type[data-type="created"] {
  background-color: #fff3e0;
  color: #e65100;
}

.relationship-type[data-type="uses"],
.relationship-type[data-type="used_by"] {
  background-color: #f3e5f5;
  color: #6a1b9a;
}

.relationship-type[data-type="enemy_of"],
.relationship-type[data-type="opposes"] {
  background-color: #ffebee;
  color: #c62828;
}

.relationship-type[data-type="allied_with"],
.relationship-type[data-type="supports"] {
  background-color: #e8eaf6;
  color: #283593;
}

.relationship-other {
  font-weight: 500;
  color: var(--text-primary);
}

.relationship-description {
  margin: 0.5rem 0;
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.relationship-actions {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.view-related-button,
.delete-relationship-button {
  padding: 0.4rem 0.75rem;
  border: none;
  border-radius: 4px;
  font-size: 0.8rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.view-related-button {
  background-color: var(--accent-color-light);
  color: var(--accent-color-dark);
}

.view-related-button:hover {
  background-color: var(--accent-color-light-hover);
}

.delete-relationship-button {
  background-color: #ffebee;
  color: #c62828;
}

.delete-relationship-button:hover {
  background-color: #ffcdd2;
}

.no-relationships {
  padding: 1rem;
  text-align: center;
  color: var(--text-secondary);
  background-color: var(--background-primary);
  border-radius: 6px;
  border: 1px dashed var(--border-color);
}

/* Relationship Form */
.relationship-form {
  padding: 1rem;
  background-color: var(--background-primary);
  border-radius: 6px;
  border: 1px solid var(--border-color);
}

.relationship-form h4 {
  margin-top: 0;
  margin-bottom: 1rem;
  font-size: 1.1rem;
  font-weight: 500;
  color: var(--text-primary);
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 0.5rem;
}

.form-section {
  margin-bottom: 1.5rem;
  padding: 0.75rem;
  background-color: var(--background-secondary);
  border-radius: 6px;
}

.section-label {
  display: block;
  margin-bottom: 0.75rem;
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.95rem;
}

.form-row {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.form-group {
  margin-bottom: 1rem;
  flex: 1;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--text-primary);
}

.form-group select,
.form-group input,
.form-group textarea {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--background-primary);
  color: var(--text-primary);
  font-size: 0.9rem;
}

.direction-toggle {
  display: flex;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  overflow: hidden;
}

.direction-button {
  flex: 1;
  padding: 0.75rem;
  border: none;
  background-color: var(--background-primary);
  color: var(--text-primary);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.direction-button .direction-arrow {
  font-weight: bold;
  font-size: 1.1rem;
}

.direction-button:hover {
  background-color: var(--background-hover);
}

.direction-button.active {
  background-color: var(--accent-color);
  color: white;
  font-weight: 500;
}

.element-search {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
}

.search-input {
  flex: 2;
  padding: 0.6rem;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--background-primary);
  color: var(--text-primary);
}

.category-select {
  flex: 1;
  padding: 0.6rem;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--background-primary);
  color: var(--text-primary);
}

.element-list {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--background-primary);
}

.element-item {
  padding: 0.75rem;
  border-bottom: 1px solid var(--border-color);
  cursor: pointer;
  transition: background-color 0.2s;
  display: grid;
  grid-template-columns: 1fr auto;
  gap: 0.5rem;
}

.element-item:last-child {
  border-bottom: none;
}

.element-item:hover {
  background-color: var(--background-hover);
}

.element-item.selected {
  background-color: var(--accent-color-light);
  border-left: 3px solid var(--accent-color);
}

.element-name {
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 0.25rem;
}

.element-type {
  font-size: 0.8rem;
  color: var(--text-secondary);
  display: inline-block;
  padding: 0.2rem 0.4rem;
  background-color: var(--background-tertiary);
  border-radius: 3px;
  margin-right: 0.5rem;
}

.element-category {
  font-size: 0.8rem;
  color: var(--text-secondary);
  font-style: italic;
}

.no-elements {
  padding: 1rem;
  text-align: center;
  color: var(--text-secondary);
}

.error-message {
  margin: 0.5rem 0;
  padding: 0.5rem;
  background-color: #ffebee;
  color: #c62828;
  border-radius: 4px;
  font-size: 0.9rem;
}

/* Relationship types styling */
.relationship-categories {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 1rem;
}

.relationship-category {
  border: 1px solid var(--border-color);
  border-radius: 6px;
  overflow: hidden;
}

.category-header {
  margin: 0;
  padding: 0.5rem 1rem;
  background-color: var(--background-tertiary);
  color: var(--text-primary);
  font-size: 0.9rem;
  font-weight: 500;
}

.relationship-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  padding: 0.75rem;
  background-color: var(--background-primary);
}

.relationship-button {
  padding: 0.5rem 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--background-primary);
  color: var(--text-primary);
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.relationship-button:hover {
  background-color: var(--background-hover);
  transform: translateY(-1px);
}

.relationship-button.active {
  border-color: var(--accent-color);
  background-color: var(--accent-color-light);
  color: var(--accent-color-dark);
  font-weight: 500;
}

/* Category-specific colors */
.relationship-button.physical {
  border-color: #81c784;
  color: #2e7d32;
}

.relationship-button.physical.active {
  background-color: #e8f5e9;
}

.relationship-button.social {
  border-color: #64b5f6;
  color: #1565c0;
}

.relationship-button.social.active {
  background-color: #e3f2fd;
}

.relationship-button.causal {
  border-color: #ffb74d;
  color: #e65100;
}

.relationship-button.causal.active {
  background-color: #fff3e0;
}

.relationship-button.temporal {
  border-color: #ba68c8;
  color: #6a1b9a;
}

.relationship-button.temporal.active {
  background-color: #f3e5f5;
}

.relationship-button.conflict {
  border-color: #e57373;
  color: #c62828;
}

.relationship-button.conflict.active {
  background-color: #ffebee;
}

.relationship-button.alliance {
  border-color: #7986cb;
  color: #283593;
}

.relationship-button.alliance.active {
  background-color: #e8eaf6;
}

.relationship-button.suggested {
  border-width: 2px;
  border-style: dashed;
}

.relationship-button.template-based {
  border-width: 2px;
  border-style: solid;
  position: relative;
}

.relationship-button.template-based::after {
  content: "✓";
  position: absolute;
  top: -5px;
  right: -5px;
  background-color: var(--accent-color);
  color: white;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  font-size: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Relationship preview */
.relationship-preview {
  margin-top: 1rem;
  padding: 1rem;
  background-color: var(--background-tertiary);
  border-radius: 6px;
  border: 1px solid var(--border-color);
}

.relationship-preview h5 {
  margin-top: 0;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--text-primary);
}

.preview-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background-color: var(--background-primary);
  border-radius: 4px;
}

.preview-element {
  font-weight: 500;
  color: var(--text-primary);
  padding: 0.4rem 0.75rem;
  background-color: var(--accent-color-light);
  border-radius: 4px;
}

.preview-relationship {
  font-weight: 500;
  color: var(--accent-color-dark);
}

.preview-arrow {
  color: var(--text-secondary);
  font-size: 1.2rem;
}

/* Form actions */
.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  margin-top: 1.5rem;
  padding-top: 1rem;
  border-top: 1px solid var(--border-color);
}

.cancel-button,
.create-button {
  padding: 0.6rem 1.2rem;
  border: none;
  border-radius: 4px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancel-button {
  background-color: var(--background-tertiary);
  color: var(--text-primary);
}

.cancel-button:hover {
  background-color: var(--background-tertiary-hover);
}

.create-button {
  background-color: var(--accent-color);
  color: white;
}

.create-button:hover {
  background-color: var(--accent-color-dark);
  transform: translateY(-1px);
}

.create-button:disabled,
.cancel-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

// frontend/src/components/UserProfilePage.js
import React from 'react';
import AIProviderSettings from './AIProviderSettings';
import './UserProfilePage.css';

/**
 * User Profile Page component for displaying and editing user profile information
 * @param {Object} props - Component props
 * @returns {JSX.Element} User profile page UI
 */
const UserProfilePage = ({
  user,
  settings,
  isLoading,
  error,
  onUpdateProfile,
  onUpdateTheme,
  onUpdateFontSize,
  onUpdateAutoSave,
  onUpdateNotifications,
  onClearError
}) => {
  const [editMode, setEditMode] = React.useState(false);
  const [formData, setFormData] = React.useState({
    name: user?.name || '',
    email: user?.email || '',
    bio: user?.bio || '',
    avatar: user?.avatar || ''
  });

  // Update form data when user changes
  React.useEffect(() => {
    if (user) {
      setFormData({
        name: user.name || '',
        email: user.email || '',
        bio: user.bio || '',
        avatar: user.avatar || ''
      });
    }
  }, [user]);

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();
    onUpdateProfile(formData);
    setEditMode(false);
  };

  // Handle theme change
  const handleThemeChange = (e) => {
    onUpdateTheme(e.target.value);
  };

  // Handle font size change
  const handleFontSizeChange = (e) => {
    onUpdateFontSize(e.target.value);
  };

  // Handle auto save toggle
  const handleAutoSaveToggle = (e) => {
    onUpdateAutoSave(e.target.checked);
  };

  // Handle notifications toggle
  const handleNotificationsToggle = (e) => {
    onUpdateNotifications(e.target.checked);
  };

  return (
    <div className="user-profile-page">
      <h2>User Profile</h2>

      {error && (
        <div className="error-message">
          <p>{error}</p>
          <button onClick={onClearError}>Dismiss</button>
        </div>
      )}

      <div className="profile-container">
        <div className="profile-section">
          <h3>Profile Information</h3>

          {isLoading ? (
            <div className="loading-indicator">Loading profile...</div>
          ) : editMode ? (
            <form onSubmit={handleSubmit} className="profile-form">
              <div className="form-group">
                <label htmlFor="name">Name</label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  required
                />
              </div>

              <div className="form-group">
                <label htmlFor="email">Email</label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  required
                />
              </div>

              <div className="form-group">
                <label htmlFor="bio">Bio</label>
                <textarea
                  id="bio"
                  name="bio"
                  value={formData.bio}
                  onChange={handleInputChange}
                  rows={4}
                />
              </div>

              <div className="form-group">
                <label htmlFor="avatar">Avatar URL</label>
                <input
                  type="text"
                  id="avatar"
                  name="avatar"
                  value={formData.avatar}
                  onChange={handleInputChange}
                />
                {formData.avatar && (
                  <div className="avatar-preview">
                    <img src={formData.avatar} alt="Avatar preview" />
                  </div>
                )}
              </div>

              <div className="form-actions">
                <button type="submit" className="save-button">Save Changes</button>
                <button
                  type="button"
                  className="cancel-button"
                  onClick={() => setEditMode(false)}
                >
                  Cancel
                </button>
              </div>
            </form>
          ) : (
            <div className="profile-details">
              <div className="profile-header">
                {user?.avatar && (
                  <div className="avatar">
                    <img src={user.avatar} alt={user?.name || 'User'} />
                  </div>
                )}
                <div className="profile-info">
                  <h4>{user?.name || 'User'}</h4>
                  <p className="email">{user?.email || 'No email provided'}</p>
                </div>
                <button
                  className="edit-button"
                  onClick={() => setEditMode(true)}
                >
                  Edit Profile
                </button>
              </div>

              {user?.bio && (
                <div className="bio">
                  <h5>Bio</h5>
                  <p>{user.bio}</p>
                </div>
              )}
            </div>
          )}
        </div>

        <div className="settings-section">
          <h3>Application Settings</h3>

          <div className="settings-form">
            <div className="form-group">
              <label htmlFor="theme">Theme</label>
              <select
                id="theme"
                value={settings.theme}
                onChange={handleThemeChange}
              >
                <option value="light">Light</option>
                <option value="dark">Dark</option>
                <option value="sepia">Sepia</option>
              </select>
            </div>

            <div className="form-group">
              <label htmlFor="fontSize">Font Size</label>
              <select
                id="fontSize"
                value={settings.fontSize}
                onChange={handleFontSizeChange}
              >
                <option value="small">Small</option>
                <option value="medium">Medium</option>
                <option value="large">Large</option>
                <option value="x-large">Extra Large</option>
              </select>
            </div>

            <div className="form-group checkbox">
              <label htmlFor="autoSave">
                <input
                  type="checkbox"
                  id="autoSave"
                  checked={settings.autoSave}
                  onChange={handleAutoSaveToggle}
                />
                Enable Auto Save
              </label>
            </div>

            <div className="form-group checkbox">
              <label htmlFor="notifications">
                <input
                  type="checkbox"
                  id="notifications"
                  checked={settings.notifications}
                  onChange={handleNotificationsToggle}
                />
                Enable Notifications
              </label>
            </div>
          </div>
        </div>

        <div className="ai-settings-section">
          <AIProviderSettings />
        </div>
      </div>
    </div>
  );
};

export default UserProfilePage;

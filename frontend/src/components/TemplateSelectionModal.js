// frontend/src/components/TemplateSelectionModal.js
import React, { useState, useEffect } from 'react';
import './TemplateSelectionModal.css';
import { getHeaders } from '../services/apiService';
import { BASE_URL } from '../utils/apiConfig';

/**
 * Modal for selecting templates from the new template system
 * @param {Object} props - Component props
 * @returns {JSX.Element} TemplateSelectionModal UI
 */
const TemplateSelectionModal = ({
  title = 'Select Template',
  categoryId,
  onSelect,
  onClose
}) => {
  const [templates, setTemplates] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [filteredTemplates, setFilteredTemplates] = useState([]);
  const [popularTemplates, setPopularTemplates] = useState([]);

  // Fetch templates when the modal opens
  useEffect(() => {
    const fetchTemplates = async () => {
      try {
        setLoading(true);
        // Fetch templates for the selected category
        const url = categoryId
          ? `${BASE_URL}/api/templates?category_id=${categoryId}`
          : `${BASE_URL}/api/templates`;

        // Use getHeaders for authentication
        const headers = getHeaders();
        console.log('Fetching templates with headers in modal:', headers);

        const response = await fetch(url, { headers });
        if (!response.ok) {
          throw new Error(`Failed to fetch templates: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        setTemplates(data);
        setFilteredTemplates(data);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching templates:', err);
        setError(err.message || 'Failed to load templates');
        setLoading(false);
      }
    };

    const fetchPopularTemplates = async () => {
      try {
        // Fetch popular templates
        const headers = getHeaders();
        const response = await fetch(`${BASE_URL}/api/templates/popular?limit=5`, { headers });
        if (response.ok) {
          const data = await response.json();
          setPopularTemplates(data);
        } else {
          console.warn(`Failed to fetch popular templates: ${response.status} ${response.statusText}`);
        }
      } catch (err) {
        console.error('Error fetching popular templates:', err);
      }
    };

    fetchTemplates();
    fetchPopularTemplates();
  }, [categoryId]);

  // Filter templates based on search term
  useEffect(() => {
    if (!searchTerm.trim()) {
      setFilteredTemplates(templates);
      return;
    }

    const filtered = templates.filter(template =>
      template.display_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (template.description && template.description.toLowerCase().includes(searchTerm.toLowerCase()))
    );

    setFilteredTemplates(filtered);
  }, [searchTerm, templates]);

  // Handle template selection
  const handleTemplateSelect = (templateId) => {
    onSelect(templateId);
  };

  return (
    <div className="modal-overlay">
      <div className="template-selection-modal">
        <div className="modal-header">
          <h2>{title}</h2>
          <button className="close-button" onClick={onClose}>×</button>
        </div>

        <div className="template-search">
          <input
            type="text"
            placeholder="Search templates..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>

        {loading ? (
          <div className="template-loading">Loading templates...</div>
        ) : error ? (
          <div className="template-error">{error}</div>
        ) : (
          <>
            {popularTemplates.length > 0 && !searchTerm && (
              <div className="popular-templates">
                <h3>Popular Templates</h3>
                <div className="template-cards">
                  {popularTemplates.map(template => (
                    <TemplateCard
                      key={template.template_id}
                      template={template}
                      onSelect={() => handleTemplateSelect(template.template_id)}
                    />
                  ))}
                </div>
              </div>
            )}

            <div className="template-list">
              <h3>{searchTerm ? 'Search Results' : 'All Templates'}</h3>
              {filteredTemplates.length > 0 ? (
                <div className="template-cards">
                  {filteredTemplates.map(template => (
                    <TemplateCard
                      key={template.template_id}
                      template={template}
                      onSelect={() => handleTemplateSelect(template.template_id)}
                    />
                  ))}
                </div>
              ) : (
                <div className="no-templates">
                  <p>No templates found. Try a different search term.</p>
                </div>
              )}
            </div>
          </>
        )}
      </div>
    </div>
  );
};

/**
 * Card component for displaying template information
 * @param {Object} props - Component props
 * @returns {JSX.Element} TemplateCard UI
 */
const TemplateCard = ({ template, onSelect }) => {
  // Extract field definitions for display
  const fieldDefinitions = template.field_definitions || [];

  return (
    <div className="template-card" onClick={onSelect}>
      <div className={`template-card-header ${template.is_system_template ? 'system-template' : 'user-template'}`}>
        <h3>{template.display_name}</h3>
        {!template.is_system_template && <span className="custom-badge">Custom</span>}
      </div>

      <p className="template-description">{template.description || 'No description available'}</p>

      <div className="template-fields">
        {fieldDefinitions.slice(0, 3).map((field, index) => (
          <div key={index} className="template-field">
            <span className="field-label">{field.label}</span>
          </div>
        ))}
        {fieldDefinitions.length > 3 && (
          <div className="template-field-more">
            +{fieldDefinitions.length - 3} more fields
          </div>
        )}
      </div>

      <div className="template-footer">
        <span className="template-version">v{template.version || '1.0'}</span>
        {template.usage_count && (
          <span className="template-usage">Used {template.usage_count} times</span>
        )}
      </div>
    </div>
  );
};

export default TemplateSelectionModal;

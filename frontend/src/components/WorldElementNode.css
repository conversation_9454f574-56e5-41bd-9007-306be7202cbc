/* frontend/src/components/WorldElementNode.css */
.world-element-node {
  padding: 10px;
  border-radius: 8px;
  width: 150px;
  font-size: 12px;
  text-align: center;
  border-width: 2px;
  border-style: solid;
  background: var(--background-primary);
  color: var(--text-primary);
  box-shadow: var(--shadow-sm);
  transition: transform 0.2s, box-shadow 0.2s;
}

.world-element-node.selected {
  box-shadow: var(--shadow-md);
  transform: scale(1.05);
}

.world-element-node:hover {
  box-shadow: var(--shadow-md);
}

.node-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
}

.node-icon {
  font-size: 1.5rem;
  margin-bottom: 5px;
}

.node-label {
  font-weight: 600;
  font-size: 0.9rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
}

.node-type {
  font-size: 0.75rem;
  font-weight: 500;
  padding: 2px 6px;
  border-radius: 10px;
  background-color: rgba(0, 0, 0, 0.1);
  margin: 2px 0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.node-category {
  font-size: 0.7rem;
  opacity: 0.7;
}

.node-handle {
  width: 8px;
  height: 8px;
  background: var(--background-primary);
  border: 2px solid var(--border-color);
}

/* Category-specific styling */
/* Physical categories */
.world-element-node.cat_physical_geography,
.world-element-node.physical_geography {
  border-color: #4caf50;
}

.world-element-node.cat_physical_geography .node-icon,
.world-element-node.physical_geography .node-icon {
  color: #4caf50;
}

.world-element-node.cat_physical_locations,
.world-element-node.physical_locations {
  border-color: #8bc34a;
}

.world-element-node.cat_physical_locations .node-icon,
.world-element-node.physical_locations .node-icon {
  color: #8bc34a;
}

.world-element-node.cat_physical_climate,
.world-element-node.physical_climate {
  border-color: #00bcd4;
}

.world-element-node.cat_physical_climate .node-icon,
.world-element-node.physical_climate .node-icon {
  color: #00bcd4;
}

.world-element-node.cat_physical_flora_fauna,
.world-element-node.physical_flora_fauna {
  border-color: #009688;
}

.world-element-node.cat_physical_flora_fauna .node-icon,
.world-element-node.physical_flora_fauna .node-icon {
  color: #009688;
}

/* Social categories */
.world-element-node.cat_social_organizations,
.world-element-node.social_organizations {
  border-color: #2196f3;
}

.world-element-node.cat_social_organizations .node-icon,
.world-element-node.social_organizations .node-icon {
  color: #2196f3;
}

.world-element-node.cat_social_cultures,
.world-element-node.social_cultures {
  border-color: #3f51b5;
}

.world-element-node.cat_social_cultures .node-icon,
.world-element-node.social_cultures .node-icon {
  color: #3f51b5;
}

.world-element-node.cat_social_laws,
.world-element-node.social_laws {
  border-color: #673ab7;
}

.world-element-node.cat_social_laws .node-icon,
.world-element-node.social_laws .node-icon {
  color: #673ab7;
}

.world-element-node.cat_social_economics,
.world-element-node.social_economics {
  border-color: #607d8b;
}

.world-element-node.cat_social_economics .node-icon,
.world-element-node.social_economics .node-icon {
  color: #607d8b;
}

/* Metaphysical categories */
.world-element-node.cat_metaphysical_magic,
.world-element-node.metaphysical_magic {
  border-color: #9c27b0;
}

.world-element-node.cat_metaphysical_magic .node-icon,
.world-element-node.metaphysical_magic .node-icon {
  color: #9c27b0;
}

.world-element-node.cat_metaphysical_religion,
.world-element-node.metaphysical_religion {
  border-color: #e91e63;
}

.world-element-node.cat_metaphysical_religion .node-icon,
.world-element-node.metaphysical_religion .node-icon {
  color: #e91e63;
}

.world-element-node.cat_metaphysical_cosmology,
.world-element-node.metaphysical_cosmology {
  border-color: #ff5722;
}

.world-element-node.cat_metaphysical_cosmology .node-icon,
.world-element-node.metaphysical_cosmology .node-icon {
  color: #ff5722;
}

/* Technological categories */
.world-element-node.cat_technological_tools,
.world-element-node.technological_tools {
  border-color: #ff9800;
}

.world-element-node.cat_technological_tools .node-icon,
.world-element-node.technological_tools .node-icon {
  color: #ff9800;
}

.world-element-node.cat_technological_infrastructure,
.world-element-node.technological_infrastructure {
  border-color: #ffc107;
}

.world-element-node.cat_technological_infrastructure .node-icon,
.world-element-node.technological_infrastructure .node-icon {
  color: #ffc107;
}

.world-element-node.cat_technological_sciences,
.world-element-node.technological_sciences {
  border-color: #cddc39;
}

.world-element-node.cat_technological_sciences .node-icon,
.world-element-node.technological_sciences .node-icon {
  color: #cddc39;
}

/* Legacy categories */
.world-element-node.places {
  border-color: #4caf50;
}

.world-element-node.places .node-icon {
  color: #4caf50;
}

.world-element-node.magic_systems {
  border-color: #9c27b0;
}

.world-element-node.magic_systems .node-icon {
  color: #9c27b0;
}

.world-element-node.technology {
  border-color: #ff9800;
}

.world-element-node.technology .node-icon {
  color: #ff9800;
}

.world-element-node.groups_societies {
  border-color: #2196f3;
}

.world-element-node.groups_societies .node-icon {
  color: #2196f3;
}

.world-element-node.species {
  border-color: #607d8b;
}

.world-element-node.species .node-icon {
  color: #607d8b;
}

.world-element-node.species .node-type {
  background-color: rgba(96, 125, 139, 0.2);
  color: #607d8b;
}

.world-element-node.character_types,
.world-element-node.character {
  border-color: #795548;
}

.world-element-node.character_types .node-icon,
.world-element-node.character .node-icon {
  color: #795548;
}

.world-element-node.character .node-type {
  background-color: rgba(121, 85, 72, 0.2);
  color: #795548;
}

.world-element-node.world_name {
  border-color: #607d8b;
}

.world-element-node.world_name .node-icon {
  color: #607d8b;
}

/* Element type styling */
.world-element-node.organization {
  border-color: #2196f3;
}

.world-element-node.organization .node-icon {
  color: #2196f3;
}

.world-element-node.organization .node-type {
  background-color: rgba(33, 150, 243, 0.2);
  color: #2196f3;
}

.world-element-node.law {
  border-color: #9c27b0;
}

.world-element-node.law .node-icon {
  color: #9c27b0;
}

.world-element-node.law .node-type {
  background-color: rgba(156, 39, 176, 0.2);
  color: #9c27b0;
}

.world-element-node.law_clause {
  border-color: #673ab7;
}

.world-element-node.law_clause .node-icon {
  color: #673ab7;
}

.world-element-node.law_clause .node-type {
  background-color: rgba(103, 58, 183, 0.2);
  color: #673ab7;
}

.world-element-node.geography {
  border-color: #4caf50;
}

.world-element-node.geography .node-icon {
  color: #4caf50;
}

.world-element-node.geography .node-type {
  background-color: rgba(76, 175, 80, 0.2);
  color: #4caf50;
}

.world-element-node.location {
  border-color: #ff9800;
}

.world-element-node.location .node-icon {
  color: #ff9800;
}

.world-element-node.location .node-type {
  background-color: rgba(255, 152, 0, 0.2);
  color: #ff9800;
}

.world-element-node.magic_system {
  border-color: #e91e63;
}

.world-element-node.magic_system .node-icon {
  color: #e91e63;
}

.world-element-node.magic_system .node-type {
  background-color: rgba(233, 30, 99, 0.2);
  color: #e91e63;
}

/* Parent-child relationship styling */
.parent-node {
  border-width: 3px;
  box-shadow: 0 0 10px rgba(187, 134, 252, 0.3);
}

.child-node {
  border-style: dashed;
}

.parent-indicator {
  position: absolute;
  top: -5px;
  right: -5px;
  font-size: 12px;
  background-color: #BB86FC;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.child-indicator {
  font-size: 0.65rem;
  background-color: rgba(187, 134, 252, 0.2);
  color: #BB86FC;
  padding: 2px 6px;
  border-radius: 10px;
  margin-top: 2px;
}

/* Dark theme adjustments */
.theme-dark .world-element-node {
  background: var(--background-secondary);
}

.theme-dark .node-handle {
  background: var(--background-secondary);
}

.theme-dark .node-type {
  background-color: rgba(255, 255, 255, 0.1);
}

.theme-dark .child-indicator {
  background-color: rgba(187, 134, 252, 0.3);
}

/* Relationships styling */
.node-relationships {
  margin-top: 8px;
  border-top: 1px dashed rgba(0, 0, 0, 0.2);
  padding-top: 5px;
  width: 100%;
  font-size: 0.7rem;
}

.relationships-header {
  font-weight: 600;
  margin-bottom: 3px;
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.relationships-list {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.relationship-item {
  display: flex;
  align-items: center;
  gap: 3px;
  font-size: 0.7rem;
  line-height: 1.2;
  flex-wrap: wrap;
  justify-content: center;
}

.relationship-direction {
  color: var(--primary);
  font-weight: bold;
}

.relationship-name {
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100px;
}

.relationship-type {
  color: var(--text-secondary);
  font-size: 0.65rem;
  white-space: nowrap;
}

.relationship-more {
  font-size: 0.65rem;
  color: var(--text-secondary);
  font-style: italic;
  margin-top: 2px;
}

/* Dark theme adjustments for relationships */
.theme-dark .node-relationships {
  border-top: 1px dashed rgba(255, 255, 255, 0.2);
}

.theme-dark .relationship-direction {
  color: var(--primary-light);
}

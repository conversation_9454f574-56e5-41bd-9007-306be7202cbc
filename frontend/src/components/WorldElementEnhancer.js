// frontend/src/components/WorldElementEnhancer.js
import React, { useState } from 'react';
import { useSelector } from 'react-redux';
import { enhanceWorldElement } from '../services/worldBuildingApiService';
import { selectCurrentBookId } from '../redux/slices/worldBuildingSlice';
import './WorldElementEnhancer.css';

/**
 * Component for enhancing world elements using AI
 * @param {Object} props - Component props
 * @returns {JSX.Element} WorldElementEnhancer UI
 */
const WorldElementEnhancer = ({ elementId, elementName, onElementEnhanced }) => {
  const bookId = useSelector(selectCurrentBookId);
  
  // State for the enhancer
  const [prompt, setPrompt] = useState('');
  const [isEnhancing, setIsEnhancing] = useState(false);
  const [error, setError] = useState(null);
  
  // Enhancement types
  const enhancementTypes = [
    { id: 'description', label: 'Detailed Description', prompt: `Enhance the description of "${elementName}" with more vivid details.` },
    { id: 'history', label: 'Historical Background', prompt: `Create a historical background for "${elementName}".` },
    { id: 'culture', label: 'Cultural Aspects', prompt: `Describe the cultural aspects related to "${elementName}".` },
    { id: 'conflicts', label: 'Conflicts & Tensions', prompt: `Identify potential conflicts or tensions involving "${elementName}".` },
    { id: 'secrets', label: 'Secrets & Mysteries', prompt: `Create some secrets or mysteries related to "${elementName}".` }
  ];
  
  // Handle prompt change
  const handlePromptChange = (e) => {
    setPrompt(e.target.value);
  };
  
  // Handle enhancement type selection
  const handleEnhancementTypeSelect = (enhancementPrompt) => {
    setPrompt(enhancementPrompt);
  };
  
  // Handle enhancement
  const handleEnhance = async () => {
    if (!prompt.trim()) {
      setError('Please enter a prompt');
      return;
    }
    
    if (!elementId) {
      setError('No element selected');
      return;
    }
    
    try {
      setIsEnhancing(true);
      setError(null);
      
      const enhancedElement = await enhanceWorldElement(bookId, elementId, prompt);
      
      if (onElementEnhanced && typeof onElementEnhanced === 'function') {
        onElementEnhanced(enhancedElement);
      }
      
      // Clear the prompt after successful enhancement
      setPrompt('');
    } catch (error) {
      console.error('Error enhancing element:', error);
      setError(error.message || 'Failed to enhance element');
    } finally {
      setIsEnhancing(false);
    }
  };
  
  if (!elementId) {
    return (
      <div className="world-element-enhancer empty-state">
        <h3>Enhance Element with AI</h3>
        <p>Select an element to enhance it with AI</p>
      </div>
    );
  }
  
  return (
    <div className="world-element-enhancer">
      <h3>Enhance "{elementName}" with AI</h3>
      
      <div className="enhancement-types">
        {enhancementTypes.map(type => (
          <button
            key={type.id}
            className="enhancement-type-button"
            onClick={() => handleEnhancementTypeSelect(type.prompt)}
            disabled={isEnhancing}
          >
            {type.label}
          </button>
        ))}
      </div>
      
      <div className="enhancer-form">
        <div className="form-group">
          <label htmlFor="enhance-prompt">Enhancement Prompt:</label>
          <textarea
            id="enhance-prompt"
            value={prompt}
            onChange={handlePromptChange}
            placeholder="Describe how you want to enhance this element..."
            rows={3}
            disabled={isEnhancing}
          />
        </div>
        
        {error && <div className="error-message">{error}</div>}
        
        <button
          className="enhance-button"
          onClick={handleEnhance}
          disabled={isEnhancing || !prompt.trim() || !elementId}
        >
          {isEnhancing ? 'Enhancing...' : 'Enhance Element'}
        </button>
      </div>
    </div>
  );
};

export default WorldElementEnhancer;

// frontend/src/components/WorldBuildingTabs.js
import React from 'react';
import './WorldBuildingTabs.css';

/**
 * Tabs component for the World Building page
 * @param {Object} props - Component props
 * @returns {JSX.Element} WorldBuildingTabs UI
 */
const WorldBuildingTabs = ({
  activeTab,
  activeCategory,
  onTabChange,
  onCategoryChange,
  currentBook
}) => {
  // Define the new categories
  const categories = [
    { id: 'cat_cosmology_physical', name: 'Cosmology & Physical Environment', description: 'Stars, planets, continents, oceans, climate, species, and other physical aspects' },
    { id: 'cat_cultural_social', name: 'Cultural & Social Systems', description: 'Nations, governments, languages, religions, social structures, and other cultural aspects' },
    { id: 'cat_economic_material', name: 'Economic & Material Systems', description: 'Currencies, trade, resources, crafting, weapons, clothing, and other material aspects' },
    { id: 'cat_knowledge_technology', name: 'Knowledge & Technology Section', description: 'Scientific eras, inventions, education, energy, communication, transportation, and other technological aspects' },
    { id: 'cat_temporal_historical', name: 'Temporal & Historical Elements', description: 'Calendars, historical eras, events, patterns, wars, and other historical aspects' },
    { id: 'cat_magical_supernatural', name: 'Magical & Supernatural Systems', description: 'Magic sources, types, spells, magical creatures, realms, and other supernatural aspects' },
    { id: 'cat_interactive_game', name: 'Interactive Systems & Game Mechanics', description: 'Game rules, mechanics, character classes, abilities, quests, combat, and other interactive aspects' }
  ];

  // Set the active tab to 'all' when component mounts or if it's not already set
  React.useEffect(() => {
    if (activeTab !== 'all') {
      onTabChange('all');
    }
  }, [activeTab, onTabChange]);

  return (
    <div className="world-building-tabs">
      {/* Category buttons removed as requested */}
      {/* Genre info removed as it's no longer needed */}
    </div>
  );
};

export default WorldBuildingTabs;

.write-page {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 50px);
  overflow: hidden;
  background-color: var(--background-primary);
  color: var(--text-primary);
}

.write-page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px;
  border-bottom: 1px solid var(--write-page-header-border, var(--border-color));
  background-color: var(--write-page-header-background, var(--background-secondary));
  color: var(--text-primary);
}

.write-page-actions {
  display: flex;
  gap: 10px;
}

.write-page-actions button {
  padding: 8px 12px;
  border: none;
  border-radius: 4px;
  background-color: var(--button-primary-background);
  color: white;
  cursor: pointer;
  transition: all 0.2s ease;
}

.write-page-actions button:hover {
  background-color: var(--button-primary-hover);
  transform: scale(1.05);
}

.write-page-actions button.active {
  background-color: var(--button-primary-hover);
  color: white;
}

.write-page-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.editor-container {
  flex: 2;
  display: flex;
  flex-direction: column;
  padding: 20px;
  overflow-y: auto;
  border-left: 1px solid var(--editor-border, var(--border-color));
  border-right: 1px solid var(--editor-border, var(--border-color));
  background-color: var(--editor-background, var(--background-primary));
  color: var(--text-primary);
}

.editor-container .text-editor-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  margin-bottom: 20px;
}

.editor-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid var(--editor-border, var(--border-color));
  color: var(--text-primary);
}

.editor-header-left {
  flex: 1;
  display: flex;
  align-items: center;
}

.editor-header-center {
  flex: 2;
  display: flex;
  justify-content: center;
  align-items: center;
}

.editor-header-right {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.editor-header h2 {
  margin: 0;
  text-align: center;
}

.saving-indicator {
  color: #888;
  font-style: italic;
  margin-right: 10px;
}

.saved-indicator {
  color: #4caf50;
  font-size: 0.9em;
}

/* Auto-save notification */
.auto-save-notification {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background-color: rgba(76, 175, 80, 0.9);
  color: white;
  padding: 10px 15px;
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  transition: opacity 0.3s ease;
  z-index: 1000;
}

.auto-save-notification.fade-out {
  opacity: 0;
}

/* Sidebar styles */
.chapter-sidebar {
  flex: 1;
  overflow-y: auto;
  border-right: 1px solid var(--chapter-sidebar-border, var(--border-color));
  background-color: var(--chapter-sidebar-background, var(--background-secondary));
  max-width: 250px;
  color: var(--text-primary);
}

.event-sidebar {
  flex: 1;
  overflow-y: auto;
  border-left: 1px solid var(--event-sidebar-border, var(--border-color));
  background-color: var(--event-sidebar-background, var(--background-secondary));
  max-width: 250px;
  color: var(--text-primary);
}

/* No chapter selected guidance */
.no-chapter-selected {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: var(--background-secondary);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  padding: 20px;
  color: var(--text-primary);
}

.guidance-message {
  text-align: center;
  max-width: 500px;
  padding: 30px;
  background-color: var(--card-background);
  border-radius: 8px;
  box-shadow: var(--shadow-md);
  color: var(--text-primary);
}

.guidance-icon {
  font-size: 48px;
  display: block;
  margin-bottom: 20px;
}

.guidance-message h3 {
  margin: 0 0 15px 0;
  color: var(--text-primary);
  font-size: 24px;
}

.guidance-message p {
  margin: 0 0 25px 0;
  color: var(--text-secondary);
  line-height: 1.5;
}

.select-chapter-btn {
  background-color: var(--button-primary-background);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 10px 20px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.select-chapter-btn:hover {
  background-color: var(--button-primary-hover);
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  font-style: italic;
  color: #888;
}

/* Reference panel styles */
.reference-panel {
  width: 0;
  flex: 0;
  background-color: var(--card-background);
  border-left: 1px solid var(--border-color);
  box-shadow: var(--shadow-md);
  overflow-y: auto;
  transition: width 0.3s ease, flex 0.3s ease;
  color: var(--text-primary);
}

.reference-panel.visible {
  width: 300px;
  flex: 0 0 300px;
}

/* World context panel styles */
.world-context-panel {
  width: 300px;
  flex: 0 0 300px;
  background-color: var(--background-secondary);
  border-left: 1px solid var(--border-color);
  overflow-y: auto;
  max-height: calc(100vh - 120px);
}

.reference-panel-toggle {
  margin-left: auto;
  padding: 5px 10px;
  background-color: var(--background-tertiary);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  cursor: pointer;
  color: var(--text-primary);
}

.reference-panel-toggle:hover {
  background-color: var(--background-secondary);
}

/* frontend/src/components/CharacterEditor.css */
.character-editor {
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
  max-width: 900px;
  width: 100%;
  margin: 0 auto;
  overflow: hidden;
  position: relative;
  max-height: 85vh;
  display: flex;
  flex-direction: column;
}

.form-header, .preview-header {
  background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
  color: white;
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.form-header h3, .preview-header h3 {
  margin: 0;
  font-size: 1.3rem;
  font-weight: 600;
}

.form-header-actions {
  display: flex;
  gap: 10px;
}

.preview-button, .preview-toggle-button {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 4px;
  color: white;
  cursor: pointer;
  font-size: 0.9rem;
  padding: 5px 12px;
  transition: background 0.2s ease;
}

.preview-button:hover, .preview-toggle-button:hover {
  background: rgba(255, 255, 255, 0.4);
}

.close-button {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 4px;
  color: white;
  cursor: pointer;
  font-size: 1.2rem;
  height: 30px;
  width: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.2s ease;
}

.close-button:hover {
  background: rgba(255, 255, 255, 0.4);
}

.form-content, .preview-content {
  padding: 20px;
  overflow-y: auto;
  max-height: calc(85vh - 200px);
  scrollbar-width: thin;
}

.form-group {
  position: relative;
}

.generate-button-inline {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
  color: white;
  border: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-left: 8px;
  vertical-align: middle;
}

.form-group label {
  display: flex;
  align-items: center;
}

.generate-button-inline:hover {
  transform: scale(1.1);
}

.generate-button-inline:disabled {
  background: #cccccc;
  cursor: not-allowed;
}

.generate-button-inline .generate-icon {
  font-size: 16px;
}

.generate-button-inline .loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.form-columns {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.form-column {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.form-group.full-width {
  margin-bottom: 15px;
}

.form-group label {
  font-weight: 600;
  color: #4a5568;
  font-size: 0.9rem;
}

.form-group input, .form-group select, .form-group textarea {
  padding: 10px;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  font-size: 0.95rem;
  transition: border-color 0.2s ease;
}

.form-group input:focus, .form-group select:focus, .form-group textarea:focus {
  border-color: #4a90e2;
  outline: none;
  box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
}

.form-group input.error, .form-group textarea.error {
  border-color: #e53e3e;
}

.error-message {
  color: #e53e3e;
  font-size: 0.8rem;
  margin-top: 3px;
}

.helper-text {
  color: #718096;
  font-size: 0.8rem;
  margin-top: 3px;
}

.strength-legend {
  color: #718096;
  font-size: 0.75rem;
  margin-top: 5px;
  font-style: italic;
  line-height: 1.2;
}

.format-examples {
  margin-top: 8px;
  padding: 8px;
  background-color: #f8f9fa;
  border-radius: 4px;
  font-family: monospace;
  font-size: 0.75rem;
  line-height: 1.4;
}

.headshot-section {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 15px;
}

.headshot-preview {
  position: relative;
  width: 150px;
  height: 150px;
  border-radius: 8px;
  overflow: hidden;
  margin-top: 10px;
}

.headshot-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.headshot-generating {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
  color: white;
  text-align: center;
  padding: 10px;
}

.headshot-generating p {
  margin: 10px 0 0;
  font-size: 0.9rem;
}

.headshot-loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s linear infinite;
}

.delete-headshot-button {
  position: absolute;
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 5px 10px;
  font-size: 0.8rem;
  cursor: pointer;
  transition: background 0.2s ease;
}

.delete-headshot-button:hover {
  background: rgba(0, 0, 0, 0.9);
}

.delete-confirm {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 10px;
  text-align: center;
}

.delete-confirm p {
  color: white;
  margin: 0 0 10px;
  font-size: 0.9rem;
}

.confirm-buttons {
  display: flex;
  gap: 10px;
}

.confirm-buttons button {
  background: #4a90e2;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 5px 10px;
  font-size: 0.8rem;
  cursor: pointer;
  transition: background 0.2s ease;
}

.confirm-buttons button:first-child {
  background: #e53e3e;
}

.confirm-buttons button:hover {
  opacity: 0.9;
}

.generate-headshot {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.headshot-style-info {
  font-size: 0.8rem;
  color: var(--text-secondary, #666);
  font-style: italic;
  margin-top: 5px;
}

.headshot-placeholder {
  width: 150px;
  height: 150px;
  border-radius: 8px;
  background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 3rem;
  font-weight: bold;
}

.generate-headshot-button {
  background: #4a90e2;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 15px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: background 0.2s ease;
}

.generate-headshot-button:hover:not(:disabled) {
  background: #3a7bc8;
}

.generate-headshot-button:disabled {
  background: #a0aec0;
  cursor: not-allowed;
}

.loading-spinner-small {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 1s linear infinite;
  margin-right: 8px;
  vertical-align: middle;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.form-actions, .preview-actions {
  padding: 15px 20px;
  background-color: #f7fafc;
  border-top: 1px solid #e2e8f0;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.save-button {
  background: #4a90e2;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 10px 20px;
  font-size: 0.95rem;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.2s ease;
}

.save-button:hover {
  background: #3a7bc8;
}

/* Cancel button styling is now in index.css */

/* Dark theme support */
@media (prefers-color-scheme: dark) {
  .character-editor {
    background-color: #1a1a1a;
  }

  .form-content, .preview-content {
    background-color: #1a1a1a;
  }

  .form-group label {
    color: #e0e0e0;
  }

  .form-group input, .form-group select, .form-group textarea {
    background-color: #2a2a2a;
    border-color: #444;
    color: #e0e0e0;
  }

  .form-actions, .preview-actions {
    background-color: #2a2a2a;
    border-color: #444;
  }

  .ai-generation-section {
    background-color: #2a2a2a;
    border-color: #444;
  }

  .ai-generation-section h4 {
    color: #e0e0e0;
  }

  .ai-generation-section p {
    color: #b0b0b0;
  }

  .preview-trait {
    background-color: #2a2a2a;
    color: #e0e0e0;
  }

  .preview-info h2, .preview-section h4 {
    color: #e0e0e0;
  }

  .preview-detail, .preview-section p, .preview-relationships li {
    color: #b0b0b0;
  }

  .preview-relationship-description {
    color: #909090;
    border-color: #444;
  }
}

/* Preview styles */
.preview-content {
  /* display: flex; */
  flex-direction: column;
  gap: 20px;
}

.preview-main {
  display: flex;
  gap: 20px;
  margin-bottom: 10px;
}

.preview-headshot {
  width: 150px;
  height: 150px;
  border-radius: 8px;
  overflow: hidden;
  flex-shrink: 0;
}

.preview-headshot img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.preview-headshot.placeholder {
  background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 3rem;
  font-weight: bold;
}

.preview-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.preview-info h2 {
  margin: 0 0 10px;
  color: #2d3748;
}

.preview-detail {
  font-size: 0.95rem;
  color: #4a5568;
}

.preview-label {
  font-weight: 600;
  color: #4a5568;
}

.preview-section {
  border-top: 1px solid #e2e8f0;
  padding-top: 15px;
}

.preview-section h4 {
  margin: 0 0 10px;
  color: #2d3748;
  font-size: 1.1rem;
}

.preview-section p {
  margin: 0;
  color: #4a5568;
  line-height: 1.5;
}

.preview-traits {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.preview-trait {
  background-color: #f0f4f8;
  border-radius: 12px;
  color: #4a5568;
  font-size: 0.9rem;
  padding: 5px 12px;
}

.preview-relationships {
  margin: 0;
  padding-left: 20px;
}

.preview-relationships li {
  margin-bottom: 10px;
  color: #4a5568;
}

.relationship-strength {
  font-size: 0.85rem;
  color: #718096;
  margin-left: 5px;
}

.preview-relationship-description {
  margin-top: 4px;
  font-size: 0.85rem;
  color: #718096;
  font-style: italic;
  padding-left: 10px;
  border-left: 2px solid #e2e8f0;
  line-height: 1.4;
}

/* Headshot generating state */
.headshot-generating {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background-color: #f0f4f8;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
}

.headshot-loading-spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top: 4px solid #4a90e2;
  width: 30px;
  height: 30px;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

.headshot-generating p {
  margin: 0 0 15px;
  color: #4a5568;
  font-size: 0.9rem;
}

.cancel-generation-button {
  background-color: #e53e3e;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 0.8rem;
  cursor: pointer;
  margin-top: 10px;
  transition: background-color 0.2s;
}

.cancel-generation-button:hover {
  background-color: #c53030;
}

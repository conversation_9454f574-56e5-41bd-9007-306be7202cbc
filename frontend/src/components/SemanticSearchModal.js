// frontend/src/components/SemanticSearchModal.js
import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import './Modal.css';
import './SemanticSearchModal.css';
import { getColorForCategory } from '../utils/colorMapping';
import { 
  searchTemplates, 
  hybridSearch, 
  addToSearchHistory, 
  clearSearchResults 
} from '../redux/slices/semanticSearchSlice';

/**
 * Modal for semantic search across templates and world elements
 * @param {Object} props - Component props
 * @returns {JSX.Element} SemanticSearchModal UI
 */
const SemanticSearchModal = ({ onClose, onTemplateSelect, bookId }) => {
  const dispatch = useDispatch();
  
  // Get state from Redux
  const { 
    templateSearchResults, 
    hybridSearchResults, 
    isLoading, 
    error, 
    searchHistory 
  } = useSelector(state => state.semanticSearch);
  
  // Local state
  const [searchTerm, setSearchTerm] = useState('');
  const [searchMode, setSearchMode] = useState('templates'); // 'templates' or 'hybrid'
  const [contentTypes, setContentTypes] = useState(['templates', 'elements', 'characters']);
  const [limit, setLimit] = useState(10);
  const [activeTab, setActiveTab] = useState('search'); // 'search' or 'history'
  
  // Handle search submission
  const handleSearch = (e) => {
    e.preventDefault();
    
    if (!searchTerm.trim()) return;
    
    // Add to search history
    dispatch(addToSearchHistory(searchTerm));
    
    // Perform search based on mode
    if (searchMode === 'templates') {
      dispatch(searchTemplates({ query: searchTerm, limit }));
    } else {
      dispatch(hybridSearch({ 
        query: searchTerm, 
        bookId, 
        contentTypes, 
        limit 
      }));
    }
  };
  
  // Handle content type toggle
  const toggleContentType = (type) => {
    if (contentTypes.includes(type)) {
      setContentTypes(contentTypes.filter(t => t !== type));
    } else {
      setContentTypes([...contentTypes, type]);
    }
  };
  
  // Handle template selection
  const handleTemplateClick = (template) => {
    if (onTemplateSelect) {
      onTemplateSelect(template);
    }
    onClose();
  };
  
  // Handle history item click
  const handleHistoryItemClick = (term) => {
    setSearchTerm(term);
    setActiveTab('search');
    
    // Perform search
    if (searchMode === 'templates') {
      dispatch(searchTemplates({ query: term, limit }));
    } else {
      dispatch(hybridSearch({ 
        query: term, 
        bookId, 
        contentTypes, 
        limit 
      }));
    }
  };
  
  // Clear results when unmounting
  useEffect(() => {
    return () => {
      dispatch(clearSearchResults());
    };
  }, [dispatch]);
  
  return (
    <div className="modal-overlay">
      <div className="semantic-search-modal">
        <div className="modal-header">
          <h2>Semantic Search</h2>
          <button className="close-button" onClick={onClose}>×</button>
        </div>
        
        <div className="semantic-search-tabs">
          <button 
            className={`tab-button ${activeTab === 'search' ? 'active' : ''}`}
            onClick={() => setActiveTab('search')}
          >
            Search
          </button>
          <button 
            className={`tab-button ${activeTab === 'history' ? 'active' : ''}`}
            onClick={() => setActiveTab('history')}
          >
            History
          </button>
        </div>
        
        {activeTab === 'search' ? (
          <>
            <div className="search-mode-selector">
              <button 
                className={`mode-button ${searchMode === 'templates' ? 'active' : ''}`}
                onClick={() => setSearchMode('templates')}
              >
                Templates
              </button>
              <button 
                className={`mode-button ${searchMode === 'hybrid' ? 'active' : ''}`}
                onClick={() => setSearchMode('hybrid')}
              >
                All Content
              </button>
            </div>
            
            {searchMode === 'hybrid' && (
              <div className="content-type-filters">
                <label>
                  <input 
                    type="checkbox" 
                    checked={contentTypes.includes('templates')} 
                    onChange={() => toggleContentType('templates')} 
                  />
                  Templates
                </label>
                <label>
                  <input 
                    type="checkbox" 
                    checked={contentTypes.includes('elements')} 
                    onChange={() => toggleContentType('elements')} 
                  />
                  World Elements
                </label>
                <label>
                  <input 
                    type="checkbox" 
                    checked={contentTypes.includes('characters')} 
                    onChange={() => toggleContentType('characters')} 
                  />
                  Characters
                </label>
              </div>
            )}
            
            <form onSubmit={handleSearch} className="search-form">
              <input
                type="text"
                placeholder="Search by description, purpose, or related concepts..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="search-input"
              />
              <button type="submit" className="search-button" disabled={isLoading}>
                {isLoading ? 'Searching...' : 'Search'}
              </button>
            </form>
            
            {error && <div className="search-error">{error}</div>}
            
            <div className="search-results">
              {searchMode === 'templates' ? (
                // Template search results
                templateSearchResults.length > 0 ? (
                  <div className="template-results">
                    {templateSearchResults.map(template => (
                      <div 
                        key={template.template_id} 
                        className="template-result-card"
                        onClick={() => handleTemplateClick(template)}
                        style={{ borderLeftColor: getColorForCategory(template.category_id) }}
                      >
                        <div className="template-result-header">
                          <h4>{template.display_name}</h4>
                          <span className="template-score">{Math.round(template.score * 100)}% match</span>
                        </div>
                        <p className="template-description">
                          {template.description || 'No description available'}
                        </p>
                        <div className="template-result-footer">
                          <span className="template-category">{template.category_name}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : !isLoading && searchTerm && (
                  <div className="no-results">No templates found matching your search.</div>
                )
              ) : (
                // Hybrid search results
                hybridSearchResults.length > 0 ? (
                  <div className="hybrid-results">
                    {hybridSearchResults.map(result => (
                      <div 
                        key={result.id} 
                        className={`hybrid-result-card ${result.type}`}
                      >
                        <div className="hybrid-result-header">
                          <h4>{result.name}</h4>
                          <span className="result-type">{result.type}</span>
                          <span className="result-score">{Math.round(result.score * 100)}% match</span>
                        </div>
                        <p className="result-description">
                          {result.description || 'No description available'}
                        </p>
                        <div className="hybrid-result-footer">
                          <span className="result-category">{result.category}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : !isLoading && searchTerm && (
                  <div className="no-results">No content found matching your search.</div>
                )
              )}
            </div>
          </>
        ) : (
          // History tab
          <div className="search-history">
            <h3>Recent Searches</h3>
            {searchHistory.length > 0 ? (
              <ul className="history-list">
                {searchHistory.map((term, index) => (
                  <li key={index} className="history-item" onClick={() => handleHistoryItemClick(term)}>
                    {term}
                  </li>
                ))}
              </ul>
            ) : (
              <div className="no-history">No search history yet.</div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default SemanticSearchModal;

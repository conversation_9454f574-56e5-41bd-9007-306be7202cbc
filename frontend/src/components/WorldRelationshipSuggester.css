/* frontend/src/components/WorldRelationshipSuggester.css */
.world-relationship-suggester {
  background: var(--background-secondary);
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.world-relationship-suggester h3 {
  margin: 0 0 15px 0;
  color: var(--text-primary);
  font-size: 1.2rem;
}

.selected-elements {
  margin-bottom: 15px;
}

.selected-elements h4 {
  margin: 0 0 10px 0;
  color: var(--text-primary);
  font-size: 1rem;
}

.element-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.element-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: var(--background-primary);
  border-radius: 4px;
  border-left: 4px solid var(--primary);
}

.element-name {
  font-weight: 500;
  color: var(--text-primary);
}

.element-category {
  font-size: 0.8rem;
  color: var(--text-secondary);
  background: var(--background-tertiary);
  padding: 2px 6px;
  border-radius: 4px;
}

.no-elements {
  color: var(--text-secondary);
  font-style: italic;
  margin: 0;
}

.error-message {
  color: var(--error);
  font-size: 0.9rem;
  padding: 5px 0;
  margin-bottom: 10px;
}

.suggest-button {
  padding: 10px 16px;
  background: var(--primary);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: background 0.2s;
  margin-bottom: 15px;
}

.suggest-button:hover:not(:disabled) {
  background: var(--button-primary-hover);
}

.suggest-button:disabled {
  background: var(--background-tertiary);
  color: var(--text-secondary);
  cursor: not-allowed;
}

.suggester-info {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid var(--border-color);
}

.suggester-info p {
  margin: 0 0 8px 0;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

/* Element category colors */
.element-item[data-category="places"] {
  border-left-color: #4caf50;
}

.element-item[data-category="magic_systems"] {
  border-left-color: #9c27b0;
}

.element-item[data-category="technology"] {
  border-left-color: #ff9800;
}

.element-item[data-category="groups_societies"] {
  border-left-color: #2196f3;
}

.element-item[data-category="species"] {
  border-left-color: #e91e63;
}

/* Dark theme adjustments */
.theme-dark .world-relationship-suggester {
  background: var(--background-tertiary);
}

.theme-dark .element-item {
  background: var(--background-secondary);
}

/* Responsive styles */
@media (max-width: 768px) {
  .world-relationship-suggester {
    padding: 15px;
  }
}

// frontend/src/utils/TemplateRegistry.js
import { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { fetchElementTemplatesThunk } from '../redux/slices/worldBuildingSlice';

// Default templates to use as fallback when no templates are available
// This replaces the import from elementTemplates.js
const defaultTemplates = {
  // Basic templates for core element types
  region: {
    name: 'Region',
    fields: [
      { id: 'climate', label: 'Climate', type: 'text' },
      { id: 'geography', label: 'Geography', type: 'textarea' }
    ]
  },
  settlement: {
    name: 'Settlement',
    fields: [
      { id: 'population', label: 'Population', type: 'text' },
      { id: 'government', label: 'Government', type: 'text' }
    ]
  },
  character: {
    name: 'Character',
    fields: [
      { id: 'role', label: 'Role', type: 'text' },
      { id: 'personality', label: 'Personality', type: 'textarea' }
    ]
  },
  organization: {
    name: 'Organization',
    fields: [
      { id: 'purpose', label: 'Purpose', type: 'text' },
      { id: 'structure', label: 'Structure', type: 'textarea' }
    ]
  },
  magic_system: {
    name: 'Magic System',
    fields: [
      { id: 'rules', label: 'Rules', type: 'textarea' },
      { id: 'limitations', label: 'Limitations', type: 'textarea' }
    ]
  }
};

class TemplateRegistry {
  constructor() {
    this.templates = {};
    this.initialized = false;
    this.usingReduxStore = false;
    this.localStorageKey = 'template_registry_cache';
  }

  // Initialize with Redux store templates if available
  initializeWithReduxStore(reduxTemplates) {
    if (this.usingReduxStore) return;

    this.templates = {};

    // Convert database templates to the format expected by the frontend
    Object.values(reduxTemplates).forEach(dbTemplate => {
      const templateId = dbTemplate.template_id;

      // Convert the database template to the frontend format
      const frontendTemplate = {
        id: templateId,
        name: dbTemplate.display_name,
        category: dbTemplate.category_id,
        version: dbTemplate.version,
        description: dbTemplate.description,
        fields: this.convertFieldDefinitions(dbTemplate.field_definitions?.fields || [])
      };

      this.templates[templateId] = frontendTemplate;
    });

    this.initialized = true;
    this.usingReduxStore = true;

    // Cache templates in localStorage for offline use
    try {
      localStorage.setItem(this.localStorageKey, JSON.stringify(this.templates));
      console.log('Cached templates in localStorage');
    } catch (error) {
      console.warn('Failed to cache templates in localStorage:', error);
    }

    console.log('TemplateRegistry initialized with Redux templates:', Object.keys(this.templates).length);
  }

  // Convert database field definitions to frontend format
  convertFieldDefinitions(dbFields) {
    return dbFields.map(field => ({
      id: field.id,
      label: field.label,
      type: field.type || 'text',
      required: field.required || false,
      description: field.description || '',
      placeholder: field.description || '',
      options: field.options || [],
      category: field.category || 'Details'
    }));
  }

  // Initialize with fallback mechanisms
  initialize() {
    if (this.initialized) return;

    // Try to load templates from localStorage first
    try {
      const cachedTemplates = localStorage.getItem(this.localStorageKey);
      if (cachedTemplates) {
        this.templates = JSON.parse(cachedTemplates);
        this.initialized = true;
        console.log('TemplateRegistry initialized from localStorage cache:', Object.keys(this.templates).length);
        return;
      }
    } catch (error) {
      console.warn('Failed to load templates from localStorage:', error);
    }

    // If localStorage fails, load base templates from defaultTemplates as final fallback
    Object.entries(defaultTemplates).forEach(([id, template]) => {
      this.registerTemplate(id, template);
    });

    this.initialized = true;
    console.log('TemplateRegistry initialized with default templates:', Object.keys(this.templates).length);
  }

  // Register a new template or update an existing one
  registerTemplate(id, template, version = '1.0') {
    if (!id || !template) return null;

    // Add version information if not present
    const templateWithVersion = {
      ...template,
      version: template.version || version,
      id: id
    };

    // Handle inheritance if parent template is specified
    if (template.inheritsFrom && this.templates[template.inheritsFrom]) {
      const parentTemplate = this.templates[template.inheritsFrom];

      // Merge fields from parent template
      templateWithVersion.fields = [
        ...(parentTemplate.fields || []),
        ...(template.fields || [])
      ];
    }

    this.templates[id] = templateWithVersion;
    return templateWithVersion;
  }

  // Get a template by ID
  getTemplate(id) {
    if (!this.initialized) this.initialize();
    return this.templates[id] || null;
  }

  // Get all templates
  getAllTemplates() {
    if (!this.initialized) this.initialize();
    return Object.values(this.templates);
  }

  // Get templates by category
  getTemplatesByCategory(categoryId) {
    if (!this.initialized) this.initialize();
    return Object.values(this.templates).filter(
      template => template.category === categoryId
    );
  }

  // Validate an element against its template
  validateElement(element, templateId) {
    if (!this.initialized) this.initialize();

    const template = this.getTemplate(templateId || element.element_type);
    if (!template) return { valid: false, errors: ['Template not found'] };

    const errors = [];

    // Check required fields
    template.fields.forEach(field => {
      if (field.required &&
          (!element.custom_fields || !element.custom_fields[field.id])) {
        errors.push(`Required field ${field.label} is missing`);
      }
    });

    return {
      valid: errors.length === 0,
      errors
    };
  }
}

// Create a singleton instance
const templateRegistry = new TemplateRegistry();

export default templateRegistry;

// Hook to use the template registry with Redux
export const useTemplateRegistry = () => {
  const dispatch = useDispatch();
  // Will be used in future for book-specific template filtering
  // const currentBookId = useSelector(state => state.worldBuilding.currentBookId);
  const reduxTemplates = useSelector(state => state.worldBuilding.templates.byId);
  const lastFetched = useSelector(state => state.worldBuilding.templates.lastFetched);

  // Initialize the registry with Redux templates
  useEffect(() => {
    if (Object.keys(reduxTemplates).length > 0) {
      templateRegistry.initializeWithReduxStore(reduxTemplates);
    } else if (!lastFetched) {
      // Fetch templates if not already fetched
      dispatch(fetchElementTemplatesThunk());
    }
  }, [reduxTemplates, lastFetched, dispatch]);

  return templateRegistry;
};

// src/utils/apiConfig.js

/**
 * Determines the appropriate base URL for API requests
 * This function will use the window.location to determine the current host
 * and construct a backend URL that works both locally and on the network
 */
export const getApiBaseUrl = () => {
  // If an environment variable is set, use that
  if (process.env.REACT_APP_API_URL) {
    return process.env.REACT_APP_API_URL;
  }

  // If we're in a browser environment
  if (typeof window !== 'undefined') {
    // Get the current hostname (e.g., localhost, *************, etc.)
    const hostname = window.location.hostname;

    // Use the same hostname but with the backend port
    return `http://${hostname}:8000`;
  }

  // Fallback to localhost
  return 'http://localhost:8001';
};

// Export a pre-computed BASE_URL for immediate use
export const BASE_URL = getApiBaseUrl();

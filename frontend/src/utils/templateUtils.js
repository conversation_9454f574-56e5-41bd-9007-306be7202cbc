// frontend/src/utils/templateUtils.js
import templateRegistry from './TemplateRegistry';

/**
 * Get template for a specific element type
 * @param {string} elementType - The type of element
 * @returns {Object} The template for the element type
 */
export const getTemplateForElementType = (elementType) => {
  // Initialize the registry if needed
  if (!templateRegistry.initialized) {
    templateRegistry.initialize();
  }

  // Get the template from the registry
  const template = templateRegistry.getTemplate(elementType);

  // If no template is found, return a generic template
  if (!template) {
    return {
      name: 'Element',
      fields: [
        {
          id: 'notes',
          label: 'Notes',
          type: 'textarea',
          placeholder: 'Additional notes about this element',
          required: false
        }
      ]
    };
  }

  return template;
};

/**
 * Get element type label
 * @param {string} elementType - The type of element
 * @returns {string} The label for the element type
 */
export const getElementTypeLabel = (elementType) => {
  const template = getTemplateForElementType(elementType);
  return template?.name || 'Element';
};

/**
 * Get available sub-element types for a parent element type
 * @param {string} parentType - The parent element type
 * @returns {Array} Array of available sub-element types
 */
export const getAvailableSubElementTypes = (parentType) => {
  // Initialize the registry if needed
  if (!templateRegistry.initialized) {
    templateRegistry.initialize();
  }

  // Get all templates (not used in this simplified implementation)
  // const allTemplates = templateRegistry.getAllTemplates();

  // Filter templates that can be sub-elements of the parent type
  // This is a simplified implementation - in a real application, you would
  // use the relationship_definitions from the database templates

  // For now, we'll return a basic set of sub-element types based on parent type
  switch (parentType) {
    // Physical Environment
    case 'region':
      return ['settlement', 'landform', 'waterbody', 'landmark'];
    case 'settlement':
      return ['building', 'landmark', 'organization'];
    case 'landform':
      return ['natural_feature', 'landmark'];
    case 'waterbody':
      return ['natural_feature', 'landmark'];

    // Cultural & Social
    case 'culture':
      return ['cultural_practice', 'organization', 'religion'];
    case 'religion':
      return ['deity', 'cultural_practice', 'organization'];
    case 'organization':
      return ['organization', 'law', 'cultural_practice'];
    case 'law':
      return ['law_clause'];

    // Economic & Material
    case 'economic_system':
      return ['trade_route', 'market', 'currency'];
    case 'trade_route':
      return ['market'];
    case 'market':
      return ['organization'];

    // Knowledge & Technology
    case 'technology':
      return ['device'];

    // Magical & Supernatural
    case 'magic_system':
      return ['spell', 'magical_power', 'magical_artifact'];
    case 'spell':
      return ['magical_power'];
    case 'magical_artifact':
      return ['magical_power'];

    // Flora & Fauna
    case 'animal':
      return ['animal_variant'];
    case 'plant':
      return ['plant_variant'];
    case 'creature':
      return ['creature_variant'];

    // Cosmology
    case 'celestial_body':
      return ['celestial_body'];
    case 'star':
      return ['celestial_body'];
    case 'planetary_climate_system':
      return ['climate_system'];

    // Default case - generic sub-elements
    default:
      return ['generic'];
  }
};

// src/utils/localStorage.js

/**
 * Safely gets an item from localStorage with error handling
 * @param {string} key - The key to retrieve
 * @param {*} defaultValue - Default value if key doesn't exist or error occurs
 * @returns {*} The value from localStorage or defaultValue
 */
export const safeGetItem = (key, defaultValue = null) => {
  try {
    return localStorage.getItem(key) || defaultValue;
  } catch (error) {
    console.warn(`Error accessing localStorage for key ${key}:`, error);
    return defaultValue;
  }
};

/**
 * Safely sets an item in localStorage with error handling
 * @param {string} key - The key to set
 * @param {string} value - The value to store
 * @returns {boolean} True if successful, false if error
 */
export const safeSetItem = (key, value) => {
  try {
    localStorage.setItem(key, value);
    return true;
  } catch (error) {
    console.warn(`Error setting localStorage for key ${key}:`, error);
    return false;
  }
};

/**
 * Safely removes an item from localStorage with error handling
 * @param {string} key - The key to remove
 * @returns {boolean} True if successful, false if error
 */
export const safeRemoveItem = (key) => {
  try {
    localStorage.removeItem(key);
    return true;
  } catch (error) {
    console.warn(`Error removing localStorage for key ${key}:`, error);
    return false;
  }
};

/**
 * Safely checks if localStorage is available
 * @returns {boolean} True if localStorage is available, false otherwise
 */
export const isLocalStorageAvailable = () => {
  try {
    const testKey = '__test__';
    localStorage.setItem(testKey, testKey);
    localStorage.removeItem(testKey);
    return true;
  } catch (e) {
    return false;
  }
};

// frontend/src/utils/templateMigrationTest.js

/**
 * This file contains tests for the template migration.
 * It verifies that the new templateUtils.js functions work correctly
 * and that the TemplateRegistry fallback mechanisms are functioning.
 * 
 * This is for development testing only and can be removed after migration is complete.
 */

import templateRegistry from './TemplateRegistry';
import * as templateUtils from './templateUtils';
import * as oldTemplates from './elementTemplates';

// Test the template registry initialization
console.log('Testing TemplateRegistry initialization...');
templateRegistry.initialize();
console.log('TemplateRegistry initialized:', templateRegistry.initialized);
console.log('Number of templates:', Object.keys(templateRegistry.templates).length);

// Test the utility functions
console.log('\nTesting utility functions...');
const testElementType = 'settlement';

// Test getTemplateForElementType
const templateFromUtils = templateUtils.getTemplateForElementType(testElementType);
const templateFromOld = oldTemplates.getTemplateForElementType(testElementType);
console.log('Template from utils:', templateFromUtils.name);
console.log('Template from old:', templateFromOld.name);
console.log('Match:', templateFromUtils.name === templateFromOld.name);

// Test getElementTypeLabel
const labelFromUtils = templateUtils.getElementTypeLabel(testElementType);
const labelFromOld = oldTemplates.getElementTypeLabel(testElementType);
console.log('Label from utils:', labelFromUtils);
console.log('Label from old:', labelFromOld);
console.log('Match:', labelFromUtils === labelFromOld);

// Test getAvailableSubElementTypes
const subTypesFromUtils = templateUtils.getAvailableSubElementTypes(testElementType);
const subTypesFromOld = oldTemplates.getAvailableSubElementTypes(testElementType);
console.log('SubTypes from utils:', subTypesFromUtils);
console.log('SubTypes from old:', subTypesFromOld);
console.log('Match:', JSON.stringify(subTypesFromUtils) === JSON.stringify(subTypesFromOld));

console.log('\nAll tests completed.');

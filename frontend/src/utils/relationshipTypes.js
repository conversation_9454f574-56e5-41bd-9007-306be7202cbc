// frontend/src/utils/relationshipTypes.js

/**
 * Predefined relationship types for world elements
 * Each type has a bidirectional counterpart
 */
const RELATIONSHIP_TYPES = [
  // Physical relationships
  {
    id: 'contains',
    label: 'Contains',
    category: 'physical',
    inverse: 'located_in',
    description: 'This element physically contains the target element'
  },
  {
    id: 'located_in',
    label: 'Located In',
    category: 'physical',
    inverse: 'contains',
    description: 'This element is physically located within the target element'
  },
  {
    id: 'adjacent_to',
    label: 'Adjacent To',
    category: 'physical',
    inverse: 'adjacent_to',
    description: 'This element is physically adjacent to the target element'
  },
  {
    id: 'part_of',
    label: 'Part Of',
    category: 'physical',
    inverse: 'has_part',
    description: 'This element is a physical part of the target element'
  },
  {
    id: 'has_part',
    label: 'Has Part',
    category: 'physical',
    inverse: 'part_of',
    description: 'This element has the target element as a physical part'
  },
  {
    id: 'borders',
    label: 'Borders',
    category: 'physical',
    inverse: 'borders',
    description: 'This element shares a border with the target element'
  },

  // Social relationships
  {
    id: 'member_of',
    label: 'Member Of',
    category: 'social',
    inverse: 'has_member',
    description: 'This element is a member of the target group or organization'
  },
  {
    id: 'has_member',
    label: 'Has Member',
    category: 'social',
    inverse: 'member_of',
    description: 'This element has the target element as a member'
  },
  {
    id: 'allied_with',
    label: 'Allied With',
    category: 'social',
    inverse: 'allied_with',
    description: 'This element is allied with the target element'
  },
  {
    id: 'allies_with',
    label: 'Allies With',
    category: 'social',
    inverse: 'allies_with',
    description: 'This element is allied with the target element'
  },
  {
    id: 'enemy_of',
    label: 'Enemy Of',
    category: 'social',
    inverse: 'enemy_of',
    description: 'This element is an enemy of the target element'
  },
  {
    id: 'rules',
    label: 'Rules',
    category: 'social',
    inverse: 'ruled_by',
    description: 'This element rules or governs the target element'
  },
  {
    id: 'ruled_by',
    label: 'Ruled By',
    category: 'social',
    inverse: 'rules',
    description: 'This element is ruled or governed by the target element'
  },
  {
    id: 'leads',
    label: 'Leads',
    category: 'social',
    inverse: 'led_by',
    description: 'This element leads the target element'
  },
  {
    id: 'led_by',
    label: 'Led By',
    category: 'social',
    inverse: 'leads',
    description: 'This element is led by the target element'
  },
  {
    id: 'friend_of',
    label: 'Friend Of',
    category: 'social',
    inverse: 'friend_of',
    description: 'This element is a friend of the target element'
  },
  {
    id: 'family_of',
    label: 'Family Of',
    category: 'social',
    inverse: 'family_of',
    description: 'This element is family of the target element'
  },

  // Causal relationships
  {
    id: 'creates',
    label: 'Creates',
    category: 'causal',
    inverse: 'created_by',
    description: 'This element creates the target element'
  },
  {
    id: 'created',
    label: 'Created',
    category: 'causal',
    inverse: 'created_by',
    description: 'This element created the target element'
  },
  {
    id: 'created_by',
    label: 'Created By',
    category: 'causal',
    inverse: 'created',
    description: 'This element was created by the target element'
  },
  {
    id: 'destroyed',
    label: 'Destroyed',
    category: 'causal',
    inverse: 'destroyed_by',
    description: 'This element destroyed the target element'
  },
  {
    id: 'destroyed_by',
    label: 'Destroyed By',
    category: 'causal',
    inverse: 'destroyed',
    description: 'This element was destroyed by the target element'
  },
  {
    id: 'causes',
    label: 'Causes',
    category: 'causal',
    inverse: 'caused_by',
    description: 'This element causes the target element'
  },
  {
    id: 'caused_by',
    label: 'Caused By',
    category: 'causal',
    inverse: 'causes',
    description: 'This element is caused by the target element'
  },
  {
    id: 'produces',
    label: 'Produces',
    category: 'causal',
    inverse: 'produced_by',
    description: 'This element produces the target element'
  },
  {
    id: 'produced_by',
    label: 'Produced By',
    category: 'causal',
    inverse: 'produces',
    description: 'This element is produced by the target element'
  },

  // Functional relationships
  {
    id: 'uses',
    label: 'Uses',
    category: 'functional',
    inverse: 'used_by',
    description: 'This element uses the target element'
  },
  {
    id: 'used_by',
    label: 'Used By',
    category: 'functional',
    inverse: 'uses',
    description: 'This element is used by the target element'
  },
  {
    id: 'controls',
    label: 'Controls',
    category: 'functional',
    inverse: 'controlled_by',
    description: 'This element controls the target element'
  },
  {
    id: 'controlled_by',
    label: 'Controlled By',
    category: 'functional',
    inverse: 'controls',
    description: 'This element is controlled by the target element'
  },
  {
    id: 'owns',
    label: 'Owns',
    category: 'functional',
    inverse: 'owned_by',
    description: 'This element owns the target element'
  },
  {
    id: 'owned_by',
    label: 'Owned By',
    category: 'functional',
    inverse: 'owns',
    description: 'This element is owned by the target element'
  },
  {
    id: 'powered_by',
    label: 'Powered By',
    category: 'functional',
    inverse: 'powers',
    description: 'This element is powered by the target element'
  },
  {
    id: 'powers',
    label: 'Powers',
    category: 'functional',
    inverse: 'powered_by',
    description: 'This element powers the target element'
  },

  // Temporal relationships
  {
    id: 'precedes',
    label: 'Precedes',
    category: 'temporal',
    inverse: 'follows',
    description: 'This element precedes the target element in time'
  },
  {
    id: 'follows',
    label: 'Follows',
    category: 'temporal',
    inverse: 'precedes',
    description: 'This element follows the target element in time'
  },
  {
    id: 'during',
    label: 'During',
    category: 'temporal',
    inverse: 'contains_time',
    description: 'This element occurs during the target time period'
  },
  {
    id: 'contains_time',
    label: 'Contains (Time)',
    category: 'temporal',
    inverse: 'during',
    description: 'This time period contains the target element'
  },
  {
    id: 'cycles_through',
    label: 'Cycles Through',
    category: 'temporal',
    inverse: 'part_of_cycle',
    description: 'This element cycles through the target element'
  },
  {
    id: 'part_of_cycle',
    label: 'Part Of Cycle',
    category: 'temporal',
    inverse: 'cycles_through',
    description: 'This element is part of the target element\'s cycle'
  },

  // Conceptual relationships
  {
    id: 'represents',
    label: 'Represents',
    category: 'conceptual',
    inverse: 'represented_by',
    description: 'This element represents or symbolizes the target element'
  },
  {
    id: 'represented_by',
    label: 'Represented By',
    category: 'conceptual',
    inverse: 'represents',
    description: 'This element is represented or symbolized by the target element'
  },
  {
    id: 'opposes',
    label: 'Opposes',
    category: 'conceptual',
    inverse: 'opposed_by',
    description: 'This element conceptually opposes the target element'
  },
  {
    id: 'opposed_by',
    label: 'Opposed By',
    category: 'conceptual',
    inverse: 'opposes',
    description: 'This element is conceptually opposed by the target element'
  },
  {
    id: 'supports',
    label: 'Supports',
    category: 'conceptual',
    inverse: 'supported_by',
    description: 'This element conceptually supports the target element'
  },
  {
    id: 'supported_by',
    label: 'Supported By',
    category: 'conceptual',
    inverse: 'supports',
    description: 'This element is conceptually supported by the target element'
  },
  {
    id: 'influences',
    label: 'Influences',
    category: 'conceptual',
    inverse: 'influenced_by',
    description: 'This element influences the target element'
  },
  {
    id: 'influenced_by',
    label: 'Influenced By',
    category: 'conceptual',
    inverse: 'influences',
    description: 'This element is influenced by the target element'
  },
  {
    id: 'references',
    label: 'References',
    category: 'conceptual',
    inverse: 'referenced_by',
    description: 'This element references the target element'
  },
  {
    id: 'referenced_by',
    label: 'Referenced By',
    category: 'conceptual',
    inverse: 'references',
    description: 'This element is referenced by the target element'
  },

  // Domain-specific relationships
  {
    id: 'enforces',
    label: 'Enforces',
    category: 'legal',
    inverse: 'enforced_by',
    description: 'This element enforces the target element'
  },
  {
    id: 'enforced_by',
    label: 'Enforced By',
    category: 'legal',
    inverse: 'enforces',
    description: 'This element is enforced by the target element'
  },
  {
    id: 'amends',
    label: 'Amends',
    category: 'legal',
    inverse: 'amended_by',
    description: 'This element amends the target element'
  },
  {
    id: 'amended_by',
    label: 'Amended By',
    category: 'legal',
    inverse: 'amends',
    description: 'This element is amended by the target element'
  },
  {
    id: 'affects',
    label: 'Affects',
    category: 'environmental',
    inverse: 'affected_by',
    description: 'This element affects the target element'
  },
  {
    id: 'affected_by',
    label: 'Affected By',
    category: 'environmental',
    inverse: 'affects',
    description: 'This element is affected by the target element'
  },
  {
    id: 'climate_of',
    label: 'Climate Of',
    category: 'environmental',
    inverse: 'has_climate',
    description: 'This element is the climate of the target element'
  },
  {
    id: 'has_climate',
    label: 'Has Climate',
    category: 'environmental',
    inverse: 'climate_of',
    description: 'This element has the target element as its climate'
  },

  // Generic relationship
  {
    id: 'related_to',
    label: 'Related To',
    category: 'generic',
    inverse: 'related_to',
    description: 'This element is related to the target element in some way'
  },
  {
    id: 'connected_to',
    label: 'Connected To',
    category: 'generic',
    inverse: 'connected_to',
    description: 'This element is connected to the target element in some way'
  }
];

/**
 * Get all relationship types
 * @returns {Array} Array of relationship types
 */
export const getRelationshipTypes = () => {
  return RELATIONSHIP_TYPES;
};

/**
 * Get relationship types by category
 * @param {string} category - Category to filter by
 * @returns {Array} Filtered array of relationship types
 */
export const getRelationshipTypesByCategory = (category) => {
  return RELATIONSHIP_TYPES.filter(type => type.category === category);
};

/**
 * Get a relationship type by ID
 * @param {string} id - Relationship type ID
 * @returns {Object|null} Relationship type object or null if not found
 */
export const getRelationshipTypeById = (id) => {
  return RELATIONSHIP_TYPES.find(type => type.id === id) || null;
};

/**
 * Get the label for a relationship type
 * @param {string} relationshipType - The type of relationship
 * @returns {string} The label for the relationship type
 */
export const getRelationshipTypeLabel = (relationshipType) => {
  const type = getRelationshipTypeById(relationshipType);

  // If we found the type in our predefined list, return its label
  if (type) {
    return type.label;
  }

  // Format unknown relationship types by replacing underscores with spaces and capitalizing words
  if (relationshipType) {
    return relationshipType
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }

  return 'Connected To'; // Default fallback
};

/**
 * Get the inverse relationship type
 * @param {string} id - Relationship type ID
 * @returns {Object|null} Inverse relationship type or null if not found
 */
export const getInverseRelationshipType = (id) => {
  const type = getRelationshipTypeById(id);
  if (!type || !type.inverse) return null;

  return getRelationshipTypeById(type.inverse);
};

/**
 * Get available relationship types based on source and target element types
 * @param {string} sourceType - The type of the source element
 * @param {string} targetType - The type of the target element
 * @returns {Array} Array of available relationship types
 */
export const getAvailableRelationshipTypes = (sourceType, targetType) => {
  // Government -> Law
  if (sourceType === 'organization' && targetType === 'law') {
    return [
      getRelationshipTypeById('enforces'),
      getRelationshipTypeById('creates'),
      getRelationshipTypeById('amends')
    ].filter(Boolean);
  }

  // Law -> Law Clause
  if (sourceType === 'law' && targetType === 'law_clause') {
    return [
      getRelationshipTypeById('contains'),
      getRelationshipTypeById('references')
    ].filter(Boolean);
  }

  // Geography -> Location
  if (sourceType === 'geography' && targetType === 'location') {
    return [
      getRelationshipTypeById('contains'),
      getRelationshipTypeById('borders')
    ].filter(Boolean);
  }

  // Planetary Climate System -> Regional Climate System
  if (sourceType === 'planetary_climate_system' && targetType === 'climate_system') {
    return [
      getRelationshipTypeById('contains'),
      getRelationshipTypeById('influences')
    ].filter(Boolean);
  }

  // Regional Climate System -> Weather Phenomenon
  if (sourceType === 'climate_system' && targetType === 'weather_phenomenon') {
    return [
      getRelationshipTypeById('contains'),
      getRelationshipTypeById('produces')
    ].filter(Boolean);
  }

  // Regional Climate System -> Season
  if (sourceType === 'climate_system' && targetType === 'season') {
    return [
      getRelationshipTypeById('contains'),
      getRelationshipTypeById('cycles_through')
    ].filter(Boolean);
  }

  // Climate System -> Location/Geography
  if (sourceType === 'climate_system' && (targetType === 'location' || targetType === 'geography')) {
    return [
      getRelationshipTypeById('affects'),
      getRelationshipTypeById('climate_of')
    ].filter(Boolean);
  }

  // Location → Character
  if (sourceType === 'location' && targetType === 'character') {
    return [
      getRelationshipTypeById('contains'),
      getRelationshipTypeById('owned_by'),
      getRelationshipTypeById('ruled_by')
    ].filter(Boolean);
  }

  // Character → Location
  if (sourceType === 'character' && targetType === 'location') {
    return [
      getRelationshipTypeById('located_in'),
      getRelationshipTypeById('owns'),
      getRelationshipTypeById('rules')
    ].filter(Boolean);
  }

  // Character → Character
  if (sourceType === 'character' && targetType === 'character') {
    return [
      getRelationshipTypeById('friend_of'),
      getRelationshipTypeById('family_of'),
      getRelationshipTypeById('enemy_of'),
      getRelationshipTypeById('allied_with')
    ].filter(Boolean);
  }

  // Character → Item
  if (sourceType === 'character' && targetType === 'item') {
    return [
      getRelationshipTypeById('owns'),
      getRelationshipTypeById('created'),
      getRelationshipTypeById('uses')
    ].filter(Boolean);
  }

  // Item → Character
  if (sourceType === 'item' && targetType === 'character') {
    return [
      getRelationshipTypeById('owned_by'),
      getRelationshipTypeById('created_by'),
      getRelationshipTypeById('used_by')
    ].filter(Boolean);
  }

  // Faction → Character
  if (sourceType === 'faction' && targetType === 'character') {
    return [
      getRelationshipTypeById('has_member'),
      getRelationshipTypeById('rules'),
      getRelationshipTypeById('enemy_of')
    ].filter(Boolean);
  }

  // Character → Faction
  if (sourceType === 'character' && targetType === 'faction') {
    return [
      getRelationshipTypeById('member_of'),
      getRelationshipTypeById('ruled_by'),
      getRelationshipTypeById('enemy_of')
    ].filter(Boolean);
  }

  // Default relationships - return a subset of common relationships
  return [
    getRelationshipTypeById('related_to'),
    getRelationshipTypeById('influences'),
    getRelationshipTypeById('opposes'),
    getRelationshipTypeById('contains'),
    getRelationshipTypeById('located_in'),
    getRelationshipTypeById('connected_to')
  ].filter(Boolean);
};

/**
 * Extract relationship suggestions from a template's relationship_definitions field
 * @param {Object} sourceTemplate - The source element's template
 * @param {Object} targetTemplate - The target element's template
 * @returns {Array} Array of suggested relationship types based on template definitions
 */
export const getTemplateRelationshipSuggestions = (sourceTemplate, targetTemplate) => {
  if (!sourceTemplate || !targetTemplate) {
    return [];
  }

  const suggestions = [];

  // Check if the source template has relationship_definitions
  if (sourceTemplate.relationship_definitions &&
      sourceTemplate.relationship_definitions.valid_relationships) {

    // Find relationships where the target template matches
    sourceTemplate.relationship_definitions.valid_relationships.forEach(rel => {
      // Check if this relationship targets the target template
      if (rel.target_template === targetTemplate.template_id ||
          rel.target_template_id === targetTemplate.template_id) {

        // Get the relationship type
        const relationshipType = getRelationshipTypeById(rel.relationship_type || rel.relationship_type_id);

        if (relationshipType) {
          suggestions.push(relationshipType);
        }
      }
    });
  }

  return suggestions;
};

/**
 * Get suggested relationship types based on element types and templates
 * @param {string} sourceType - Source element type
 * @param {string} targetType - Target element type
 * @param {Object} sourceTemplate - The source element's template (optional)
 * @param {Object} targetTemplate - The target element's template (optional)
 * @returns {Array} Array of suggested relationship types
 */
export const getSuggestedRelationshipTypes = (sourceType, targetType, sourceTemplate, targetTemplate) => {
  // Get suggestions based on element types
  const typeBasedSuggestions = getAvailableRelationshipTypes(sourceType, targetType);

  // Get suggestions based on templates
  const templateBasedSuggestions = getTemplateRelationshipSuggestions(sourceTemplate, targetTemplate);

  // Mark template-based suggestions
  const markedTemplateSuggestions = templateBasedSuggestions.map(suggestion => ({
    ...suggestion,
    isFromTemplate: true
  }));

  // Combine suggestions, removing duplicates
  const allSuggestions = [...typeBasedSuggestions];

  // Add template suggestions if they're not already included
  markedTemplateSuggestions.forEach(suggestion => {
    const existingIndex = allSuggestions.findIndex(s => s.id === suggestion.id);
    if (existingIndex >= 0) {
      // If it exists, mark it as from template
      allSuggestions[existingIndex] = {
        ...allSuggestions[existingIndex],
        isFromTemplate: true
      };
    } else {
      // Otherwise add it
      allSuggestions.push(suggestion);
    }
  });

  return allSuggestions;
};

export default {
  getRelationshipTypes,
  getRelationshipTypesByCategory,
  getRelationshipTypeById,
  getRelationshipTypeLabel,
  getInverseRelationshipType,
  getSuggestedRelationshipTypes,
  getAvailableRelationshipTypes
};

// frontend/src/utils/categoryMapping.js

/**
 * Maps category IDs to element types
 * This mapping is used to determine which template to use for a given category
 */
const categoryToElementType = {
  // Cosmology & Physical Environment
  'cat_cosmology_physical': 'planet', // Default, but will be overridden by selection

  // Cultural & Social Systems
  'cat_cultural_social': 'culture', // Default, but will be overridden by selection

  // Economic & Material Systems
  'cat_economic_material': 'economic_system', // Default, but will be overridden by selection

  // Knowledge & Technology Section
  'cat_knowledge_technology': 'technology', // Default, but will be overridden by selection

  // Temporal & Historical Elements
  'cat_temporal_historical': 'historical_event', // Default, but will be overridden by selection

  // Magical & Supernatural Systems
  'cat_magical_supernatural': 'magic_system', // Default, but will be overridden by selection

  // Interactive Systems & Game Mechanics
  'cat_interactive_game': 'game_mechanic' // Default, but will be overridden by selection
};

/**
 * Get the element type for a given category ID
 * @param {string} categoryId - The category ID
 * @returns {string} The element type
 */
export const getCategoryElementType = (categoryId) => {
  if (!categoryId) return 'generic';

  // Check if we have a direct mapping
  if (categoryToElementType[categoryId]) {
    return categoryToElementType[categoryId];
  }

  // If no direct mapping, try to infer from the category ID
  if (categoryId.includes('cosmology_physical')) return 'planet';
  if (categoryId.includes('cultural_social')) return 'culture';
  if (categoryId.includes('economic_material')) return 'economic_system';
  if (categoryId.includes('knowledge_technology')) return 'technology';
  if (categoryId.includes('temporal_historical')) return 'historical_event';
  if (categoryId.includes('magical_supernatural')) return 'magic_system';
  if (categoryId.includes('interactive_game')) return 'game_mechanic';

  // Default to generic if no mapping is found
  return 'generic';
};

/**
 * Get the parent-child relationship mapping for element types
 * This mapping is used to determine which sub-element types are valid for a given parent element type
 * @returns {Object} The parent-child relationship mapping
 */
export const getParentChildMapping = () => {
  return {
    // Physical categories
    // Locations
    'settlement': ['district', 'building', 'point_of_interest', 'location'],
    'region': ['landmark', 'location', 'settlement'],

    // Geography
    'landform': ['natural_feature', 'boundary', 'ecological_zone'],
    'waterbody': ['natural_feature', 'boundary'],
    'natural_feature': ['natural_feature'],
    'ecological_zone': ['natural_feature'],

    // Climate
    'planetary_climate_system': ['climate_system'],
    'climate_system': ['weather_phenomenon', 'season', 'microclimate'],
    'weather_phenomenon': ['weather_phenomenon'],
    'season': ['season'],
    'microclimate': ['microclimate'],

    // Flora & Fauna
    'animal': ['animal_variant', 'subspecies', 'habitat', 'lifecycle'],
    'plant': ['plant_variant', 'subspecies', 'habitat', 'lifecycle'],

    // Creatures & Monsters
    'creature': ['creature_variant', 'ability', 'habitat'],
    'monster': ['creature_variant', 'ability', 'habitat'],

    // Alien Lifeforms
    'alien_species': ['subspecies', 'biological_trait', 'behavioral_pattern'],
    'alien_creature': ['biological_trait', 'habitat'],

    // Social categories
    // Organizations and governance
    'organization': ['division', 'role', 'resource', 'objective', 'law'],
    'law': ['law_clause'],

    // Magic systems
    'magic_system': ['spell', 'magical_power', 'magical_artifact'],
    'spell': ['spell'],
    'magical_power': ['magical_power'],
    'magical_artifact': ['magical_artifact'],

    // Religion and metaphysical
    'religion': ['deity'],
    'deity': ['deity'],

    // Technology
    'technology': ['device', 'technology'],
    'device': ['device'],

    // Culture
    'culture': ['cultural_practice'],
    'cultural_practice': ['cultural_practice'],

    // Economics and trade
    'economic_system': ['trade_route', 'market', 'currency'],
    'trade_route': ['trade_route'],
    'market': ['market'],
    'currency': ['currency'],

    // Cosmology
    'cosmic_structure': ['celestial_body', 'cosmic_phenomenon'],
    'celestial_body': ['celestial_body'],
    'cosmic_phenomenon': ['cosmic_phenomenon'],

    // Infrastructure
    'infrastructure_system': ['facility', 'route_network'],
    'facility': ['facility'],
    'route_network': ['route_network'],

    // Sciences & Knowledge
    'field_of_study': ['advanced_technology', 'knowledge_repository'],
    'advanced_technology': ['advanced_technology'],
    'knowledge_repository': ['knowledge_repository'],

    // Default - generic sub-elements
    'generic': ['generic']
  };
};

/**
 * Get valid sub-element types for a given parent element type
 * @param {string} parentType - The parent element type
 * @returns {Array} Array of valid sub-element types
 */
export const getValidSubElementTypes = (parentType) => {
  const mapping = getParentChildMapping();
  return mapping[parentType] || ['generic'];
};

export default {
  getCategoryElementType,
  getValidSubElementTypes,
  getParentChildMapping
};

// frontend/src/utils/categoryElementOptions.js

/**
 * Get element options for a specific category
 * @param {string} categoryId - Category identifier
 * @returns {Array} List of element options
 */
export const getElementOptionsForCategory = (categoryId) => {
  // Default options that apply to all categories
  const defaultOptions = [
    { value: 'generic', label: 'Generic Element' }
  ];
  
  // Category-specific options
  const categoryOptions = {
    // Cosmology & Physical Environment
    cat_cosmology_physical: [
      { value: 'planet', label: 'Planet' },
      { value: 'star', label: 'Star' },
      { value: 'celestial_body', label: 'Celestial Body' },
      { value: 'biome', label: 'Biome' },
      { value: 'terrain', label: 'Terrain' },
      { value: 'climate', label: 'Climate' },
      { value: 'weather', label: 'Weather Phenomenon' },
      { value: 'natural_resource', label: 'Natural Resource' },
      { value: 'ecosystem', label: 'Ecosystem' },
      { value: 'flora', label: 'Flora' },
      { value: 'fauna', label: 'Fauna' }
    ],
    
    // Cultural & Social Systems
    cat_cultural_social: [
      { value: 'culture', label: 'Culture' },
      { value: 'society', label: 'Society' },
      { value: 'government', label: 'Government' },
      { value: 'religion', label: 'Religion' },
      { value: 'language', label: 'Language' },
      { value: 'social_class', label: 'Social Class' },
      { value: 'organization', label: 'Organization' },
      { value: 'faction', label: 'Faction' },
      { value: 'tradition', label: 'Tradition' },
      { value: 'ritual', label: 'Ritual' }
    ],
    
    // Economic & Material Systems
    cat_economic_material: [
      { value: 'economy', label: 'Economy' },
      { value: 'currency', label: 'Currency' },
      { value: 'trade_route', label: 'Trade Route' },
      { value: 'resource', label: 'Resource' },
      { value: 'commodity', label: 'Commodity' },
      { value: 'industry', label: 'Industry' },
      { value: 'craft', label: 'Craft' },
      { value: 'material', label: 'Material' },
      { value: 'artifact', label: 'Artifact' }
    ],
    
    // Knowledge & Technology
    cat_knowledge_technology: [
      { value: 'technology', label: 'Technology' },
      { value: 'invention', label: 'Invention' },
      { value: 'science', label: 'Science' },
      { value: 'knowledge_system', label: 'Knowledge System' },
      { value: 'education', label: 'Education' },
      { value: 'communication', label: 'Communication' },
      { value: 'transportation', label: 'Transportation' },
      { value: 'weapon', label: 'Weapon' },
      { value: 'tool', label: 'Tool' }
    ],
    
    // Temporal & Historical Elements
    cat_temporal_historical: [
      { value: 'timeline', label: 'Timeline' },
      { value: 'era', label: 'Era' },
      { value: 'event', label: 'Event' },
      { value: 'conflict', label: 'Conflict' },
      { value: 'war', label: 'War' },
      { value: 'disaster', label: 'Disaster' },
      { value: 'discovery', label: 'Discovery' },
      { value: 'historical_figure', label: 'Historical Figure' },
      { value: 'legend', label: 'Legend' },
      { value: 'myth', label: 'Myth' }
    ],
    
    // Magical & Supernatural Systems
    cat_magical_supernatural: [
      { value: 'magic_system', label: 'Magic System' },
      { value: 'magical_element', label: 'Magical Element' },
      { value: 'spell', label: 'Spell' },
      { value: 'magical_creature', label: 'Magical Creature' },
      { value: 'magical_item', label: 'Magical Item' },
      { value: 'magical_location', label: 'Magical Location' },
      { value: 'deity', label: 'Deity' },
      { value: 'supernatural_being', label: 'Supernatural Being' },
      { value: 'supernatural_phenomenon', label: 'Supernatural Phenomenon' }
    ],
    
    // Interactive Systems & Game Mechanics
    cat_interactive_game: [
      { value: 'game_mechanic', label: 'Game Mechanic' },
      { value: 'rule', label: 'Rule' },
      { value: 'ability', label: 'Ability' },
      { value: 'skill', label: 'Skill' },
      { value: 'attribute', label: 'Attribute' },
      { value: 'class', label: 'Class' },
      { value: 'race', label: 'Race' },
      { value: 'item', label: 'Item' },
      { value: 'quest', label: 'Quest' },
      { value: 'challenge', label: 'Challenge' }
    ]
  };
  
  // Return category-specific options or default options
  return categoryOptions[categoryId] || defaultOptions;
};

/**
 * Check if a category has multiple element options
 * @param {string} categoryId - Category identifier
 * @returns {boolean} True if the category has multiple options
 */
export const hasMultipleElementOptions = (categoryId) => {
  const options = getElementOptionsForCategory(categoryId);
  return options.length > 1;
};

// frontend/src/utils/colorMapping.js

/**
 * Color mapping for categories and element types
 * Organized by the seven main categories with consistent colors across the application
 */

// Main category colors
export const CATEGORY_COLORS = {
  // New category colors
  cat_cosmology_physical: '#4CAF50',      // Green
  cat_cultural_social: '#2196F3',         // Blue
  cat_economic_material: '#FFC107',       // Amber
  cat_knowledge_technology: '#9C27B0',    // Purple
  cat_temporal_historical: '#FF5722',     // Deep Orange
  cat_magical_supernatural: '#E91E63',    // Pink
  cat_interactive_game: '#00BCD4',        // Cyan

  // Element type colors - Physical
  geography: '#03A9F4', // Lighter blue
  landform: '#03A9F4', // Lighter blue
  waterbody: '#00BCD4', // Blue-teal
  location: '#00BCD4', // Blue-teal
  settlement: '#00BCD4', // Blue-teal
  region: '#03A9F4', // Lighter blue
  species: '#4FC3F7', // Sky blue
  animal: '#29B6F6', // Light blue
  plant: '#26C6DA', // Cyan
  creature: '#4FC3F7', // Sky blue
  monster: '#2962FF', // Bright blue
  alien_species: '#4FC3F7', // Sky blue
  alien_creature: '#29B6F6', // Light blue
  planetary_climate_system: '#2196F3', // Blue
  climate_system: '#03A9F4', // Lighter blue
  weather_phenomenon: '#00BCD4', // Blue-teal
  season: '#4FC3F7', // Sky blue
  natural_feature: '#03A9F4', // Lighter blue
  landmark: '#00BCD4', // Blue-teal
  building: '#00BCD4', // Blue-teal
  creature_variant: '#4FC3F7', // Sky blue
  plant_variant: '#26C6DA', // Cyan
  animal_variant: '#29B6F6', // Light blue

  // Element type colors - Social
  organization: '#7E57C2', // Soft purple
  interplanetary_coalition: '#9575CD', // Light purple
  organizations: '#7E57C2', // Soft purple
  groups_societies: '#BA68C8', // Lavender
  government: '#E91E63', // Pink
  galactic_authority: '#D81B60', // Dark pink
  law: '#E91E63', // Pink
  law_clause: '#EC407A', // Light pink
  culture: '#9575CD', // Light purple
  cultural_practice: '#B39DDB', // Lighter purple
  economic_system: '#D81B60', // Dark pink
  interstellar_market: '#F06292', // Lighter pink
  trade_route: '#EC407A', // Light pink
  market: '#F06292', // Lighter pink
  currency: '#F48FB1', // Very light pink
  historical_period: '#BA68C8', // Lavender
  major_event: '#EC407A', // Light pink

  // Element type colors - Magical
  magic_systems: '#FF5722', // Deep orange
  magic_system: '#FF5722', // Deep orange (singular form)
  magical_tradition: '#FF7043', // Lighter orange-red
  magical_power: '#FF7043', // Lighter orange-red
  superpower: '#FF6E40', // Orange-red
  magical_artifact: '#FFAB40', // Amber
  spell: '#FF6E40', // Orange-red
  religion: '#FFA726', // Orange
  cosmic_cult: '#FFB74D', // Light orange
  deity: '#FFB74D', // Light orange
  cosmic_structure: '#F57C00', // Dark orange
  interdimensional_gateway: '#FB8C00', // Medium orange
  celestial_body: '#FB8C00', // Medium orange
  cosmic_phenomenon: '#FF9800', // Orange

  // Element type colors - Technological
  technology: '#66BB6A', // Light green
  advanced_device: '#81C784', // Pale green
  device: '#81C784', // Pale green
  infrastructure_system: '#2E7D32', // Dark green
  interstellar_network: '#43A047', // Light-medium green
  facility: '#388E3C', // Medium green
  route_network: '#43A047', // Light-medium green
  field_of_study: '#00897B', // Teal
  scientific_institution: '#26A69A', // Teal-green
  advanced_technology: '#26A69A', // Teal-green
  terraforming_system: '#388E3C', // Medium green
  knowledge_repository: '#4DB6AC', // Light teal
};

/**
 * Get color for a specific element type or category
 * @param {string} type - Element type or category ID
 * @returns {string} - Color hex code
 */
export const getColorForType = (type) => {
  return CATEGORY_COLORS[type] || CATEGORY_COLORS.physical; // Default to physical if not found
};

/**
 * Get the main category for an element type
 * @param {string} elementType - Element type
 * @returns {string} - Main category ID
 */
export const getMainCategoryForType = (elementType) => {
  // Cosmology & Physical Environment types
  if ([
    'geography', 'landform', 'waterbody', 'location', 'settlement', 'region',
    'species', 'animal', 'plant', 'creature', 'monster', 'alien_species', 'alien_creature',
    'planetary_climate_system', 'climate_system', 'weather_phenomenon', 'season',
    'natural_feature', 'landmark', 'building', 'creature_variant', 'plant_variant', 'animal_variant',
    'star', 'planet', 'moon', 'asteroid_comet', 'black_hole_exotic_object',
    'nebula', 'galaxy', 'star_system', 'continent', 'ocean_sea',
    'mountain_range', 'forest_jungle', 'desert', 'river_lake', 'plant_species',
    'animal_species', 'sapient_species', 'disease', 'climate_zone_weather_pattern',
    'weather_event', 'tectonic_activity', 'ecological_relationship', 'evolutionary_history'
  ].includes(elementType)) {
    return 'cat_cosmology_physical';
  }

  // Cultural & Social Systems types
  if ([
    'organization', 'interplanetary_coalition', 'organizations', 'groups_societies',
    'government', 'galactic_authority', 'law', 'law_clause',
    'culture', 'cultural_practice', 'nation_state', 'province_region', 'city_settlement', 'tribe_clan',
    'social_class', 'family_structure', 'government_type', 'government_position',
    'military_organization', 'military_unit', 'language', 'dialect',
    'art_form', 'music_style', 'festival_holiday', 'cuisine', 'clothing_style',
    'religion', 'religious_text', 'deity', 'philosophy', 'gender_role_sexual_norm',
    'age_group_generation', 'social_mobility_mechanism', 'legal_framework_justice_system',
    'law_element', 'diplomatic_relation', 'faction_system', 'faction_rank',
    'colonial_relationship', 'resistance_movement', 'international_organization',
    'sport_recreation', 'status_symbol', 'cultural_exchange'
  ].includes(elementType)) {
    return 'cat_cultural_social';
  }

  // Economic & Material Systems types
  if ([
    'economic_system', 'interstellar_market', 'trade_route', 'market', 'currency',
    'currency_denomination', 'trading_post', 'market_trading_center', 'banking_financial_institution',
    'resource', 'resource_node', 'taxation_system', 'specific_tax',
    'road_transport_network', 'communication_system', 'utility_system',
    'urban_plan', 'agricultural_system', 'manufacturing_center', 'tool_implement',
    'weapon_armor', 'clothing_item', 'furniture_item', 'luxury_good',
    'shelter_housing', 'art_decorative_object', 'public_facility',
    'public_transit_system', 'freight_transport'
  ].includes(elementType)) {
    return 'cat_economic_material';
  }

  // Knowledge & Technology Section types
  if ([
    'technology', 'advanced_device', 'device', 'infrastructure_system', 'interstellar_network',
    'facility', 'route_network', 'field_of_study', 'scientific_institution',
    'advanced_technology', 'terraforming_system', 'knowledge_repository',
    'scientific_technological_era', 'invention_innovation', 'scientific_paradigm',
    'knowledge_preservation_method', 'educational_system', 'educational_institution',
    'research_facility', 'energy_production_technology', 'medical_technology',
    'communication_technology', 'information_technology', 'manufacturing_technology',
    'weapons_technology', 'military_technology', 'biotechnology', 'space_technology',
    'land_vehicle', 'water_vessel', 'aircraft', 'space_vessel',
    'technological_disparity', 'technological_advancement_rate',
    'animal_based_transportation', 'personal_transportation_device'
  ].includes(elementType)) {
    return 'cat_knowledge_technology';
  }

  // Temporal & Historical Elements types
  if ([
    'historical_period', 'major_event',
    'calendar_system', 'holiday_observance', 'historical_era', 'historical_event',
    'creation_myth', 'apocalyptic_event', 'historical_pattern', 'migration_event',
    'rise_fall_of_civilization', 'technological_revolution', 'war_conflict',
    'military_operation', 'battle_campaign', 'natural_disaster',
    'historical_cultural_exchange', 'historical_figure', 'cult_fringe_belief',
    'supernatural_event', 'sacred_site', 'mythology_entry'
  ].includes(elementType)) {
    return 'cat_temporal_historical';
  }

  // Magical & Supernatural Systems types
  if ([
    'magic_systems', 'magic_system', 'magical_tradition', 'magical_power', 'superpower',
    'magical_artifact', 'spell', 'religion', 'cosmic_cult', 'deity',
    'cosmic_structure', 'interdimensional_gateway', 'celestial_body', 'cosmic_phenomenon',
    'magic_source', 'magic_type', 'spell_ability', 'magic_item',
    'enchantment_process', 'magical_creature', 'magical_location',
    'other_realm', 'spirit_entity', 'afterlife_system', 'prophecy_system',
    'paranormal_ability', 'mythical_artifact'
  ].includes(elementType)) {
    return 'cat_magical_supernatural';
  }

  // Interactive Systems & Game Mechanics types
  if ([
    'game_rule_system', 'game_mechanic', 'character_class_archetype',
    'ability_skill_system', 'ability_skill_tree', 'quest_mission_structure',
    'individual_quest', 'achievement_system', 'combat_system', 'combat_move',
    'crafting_system', 'item_crafting', 'progression_system',
    'npc_interaction_system', 'random_encounter', 'loot_reward_distribution',
    'loot_table', 'difficulty_scaling', 'player_choice_consequence',
    'game_session_structure', 'puzzle_challenge_design', 'tutorial_system',
    'dialogue_system', 'dialogue_option', 'dialogue_tree', 'inventory_system',
    'ai_behavior', 'ai_state', 'morality_alignment_system', 'moral_choice',
    'procedural_generation', 'generation_algorithm', 'multiplayer_interaction',
    'player_role', 'permadeath_roguelike_mechanics', 'stealth_mechanics'
  ].includes(elementType)) {
    return 'cat_interactive_game';
  }

  return 'cat_cosmology_physical'; // Default to cosmology & physical
};

/**
 * Get color for a category
 * @param {string} categoryId - Category ID
 * @returns {string} - Color hex code
 */
export const getColorForCategory = (categoryId) => {
  return CATEGORY_COLORS[categoryId] || CATEGORY_COLORS.cat_cosmology_physical;
};

/**
 * Get color for an aspect type (physical, social, metaphysical, technological)
 * @param {string} aspectType - Aspect type
 * @returns {string} - Color hex code
 */
export const getColorForAspectType = (aspectType) => {
  switch (aspectType) {
    case 'physical':
      return '#4CAF50'; // Green
    case 'social':
      return '#2196F3'; // Blue
    case 'metaphysical':
      return '#E91E63'; // Pink
    case 'technological':
      return '#9C27B0'; // Purple
    default:
      return '#2196F3'; // Default to blue
  }
};

export default {
  CATEGORY_COLORS,
  getColorForType,
  getMainCategoryForType,
  getColorForCategory,
  getColorForAspectType
};

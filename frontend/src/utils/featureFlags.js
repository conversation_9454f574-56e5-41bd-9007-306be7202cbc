// src/utils/featureFlags.js

/**
 * Feature flags for controlling Redux migration
 * These flags determine whether to use Redux or Context for each feature
 */
export const FLAGS = {
  USE_REDUX_AUTH: 'useReduxAuth',
  USE_REDUX_BOOKS: 'useReduxBooks',
  USE_REDUX_CHARACTERS: 'useReduxCharacters',
  USE_REDUX_WORLD: 'useReduxWorld',
  USE_REDUX_PLOT: 'useReduxPlot',
  USE_REDUX_BRAINSTORM: 'useReduxBrainstorm'
};

/**
 * Get the value of a feature flag
 * Checks localStorage first, then environment variables, then defaults
 * @param {string} flag - The feature flag to check
 * @returns {boolean} The value of the feature flag
 */
export const getFeatureFlag = (flag) => {
  // Check localStorage first (for user override)
  const localValue = localStorage.getItem(flag);
  if (localValue !== null) {
    return localValue === 'true';
  }
  
  // Then check environment variables
  const envKey = `REACT_APP_${flag.toUpperCase()}`;
  if (process.env[envKey] !== undefined) {
    return process.env[envKey] === 'true';
  }
  
  // Default values for each flag
  const defaults = {
    [FLAGS.USE_REDUX_AUTH]: false,
    [FLAGS.USE_REDUX_BOOKS]: false,
    [FLAGS.USE_REDUX_CHARACTERS]: false,
    [FLAGS.USE_REDUX_WORLD]: false,
    [FLAGS.USE_REDUX_PLOT]: false,
    [FLAGS.USE_REDUX_BRAINSTORM]: false
  };
  
  return defaults[flag] || false;
};

/**
 * Set the value of a feature flag in localStorage
 * @param {string} flag - The feature flag to set
 * @param {boolean} value - The value to set
 */
export const setFeatureFlag = (flag, value) => {
  localStorage.setItem(flag, value.toString());
};

/**
 * Reset all feature flags to their default values
 */
export const resetFeatureFlags = () => {
  Object.values(FLAGS).forEach(flag => {
    localStorage.removeItem(flag);
  });
};

/**
 * Enable Redux for a specific feature
 * @param {string} feature - The feature to enable Redux for
 */
export const enableReduxForFeature = (feature) => {
  const flagKey = `USE_REDUX_${feature.toUpperCase()}`;
  if (FLAGS[flagKey]) {
    setFeatureFlag(FLAGS[flagKey], true);
    return true;
  }
  return false;
};

/**
 * Disable Redux for a specific feature
 * @param {string} feature - The feature to disable Redux for
 */
export const disableReduxForFeature = (feature) => {
  const flagKey = `USE_REDUX_${feature.toUpperCase()}`;
  if (FLAGS[flagKey]) {
    setFeatureFlag(FLAGS[flagKey], false);
    return true;
  }
  return false;
};

/**
 * Get the status of all feature flags
 * @returns {Object} An object with the status of all feature flags
 */
export const getAllFeatureFlags = () => {
  return Object.values(FLAGS).reduce((acc, flag) => {
    acc[flag] = getFeatureFlag(flag);
    return acc;
  }, {});
};

export default FLAGS;

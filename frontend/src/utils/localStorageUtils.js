// frontend/src/utils/localStorageUtils.js

/**
 * Save world elements to localStorage for a specific book
 * @param {string} bookId - Book identifier
 * @param {Array} worldElements - World elements to save
 */
export const saveWorldElementsToStorage = (bookId, worldElements) => {
  try {
    if (bookId && Array.isArray(worldElements)) {
      localStorage.setItem(`worldElements_${bookId}`, JSON.stringify(worldElements));
      console.log('Saved world elements to localStorage:', worldElements.length);
    }
  } catch (error) {
    console.error('Error saving world elements to localStorage:', error);
  }
};

/**
 * Load world elements from localStorage for a specific book
 * @param {string} bookId - Book identifier
 * @returns {Array} World elements from localStorage or empty array
 */
export const loadWorldElementsFromStorage = (bookId) => {
  try {
    if (bookId) {
      const savedWorldElements = localStorage.getItem(`worldElements_${bookId}`);
      if (savedWorldElements) {
        const parsedWorldElements = JSON.parse(savedWorldElements);
        console.log('Loaded world elements from localStorage:', parsedWorldElements.length);
        return parsedWorldElements;
      }
    }
  } catch (error) {
    console.error('Error loading world elements from localStorage:', error);
  }
  return [];
};

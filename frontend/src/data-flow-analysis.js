// CURRENT DATA FLOW (LEGACY)

// Book Selection (App.js)
// User selects book → fetchBooksHandler → loadBookData → 
// Triggers multiple parallel API calls:
//   - fetchChaptersHandler
//   - fetchCharactersHandler
//   - fetchPlotHandler
//   - fetchBrainstormHandler
//   - fetchWorldHandler

// Plot Page (PlotPage.js)
// 3-second interval → fetchAndSyncPlotData → fetchPlot → 
// backend/app/api/plot_endpoints.py:get_plot → 
// backend/app/db/plot_repository.py:get_plot

// Character Management
// CharacterSidebar.js → fetchAndSyncCharacters → 
// fetchWorld + fetchCharacters → 
// backend character endpoints...

// NEW DATA FLOW (IMPLEMENTED)

// Book Selection (TitleBar.js)
// User selects book → handleBookSwitch → 
// ONLY loads basic book info, no automatic loading of all data

// Plot Management (PlotPage.js)
// Component mount → fetchPlotHandler → 
// backend/app/api/plot_endpoints.py:get_plot → 
// backend/app/db/plot_repository.py:get_plot
// No polling/interval needed

// Character Management (CharacterPage.js)
// Component mount → fetchCharactersHandler → 
// backend/app/api/character_endpoints.py:get_characters → 
// backend/app/db/character_repository.py:get_characters
// No automatic syncing with other components

// Brainstorm (BrainstormPage.js)
// Component mount → fetchBrainstormHandler → 
// backend/app/api/brainstorm_endpoints.py:get_brainstorm → 
// backend/app/db/brainstorm_repository.py:get_brainstorm
// Character/location tagging with local state

// REMAINING IMPLEMENTATION

// World Page (WorldPage.js)
// Component mount → fetchWorldElements → 
// backend/app/api/world_endpoints.py:get_world_elements → 
// backend/app/db/world_repository.py:get_world_elements
// Category management with local state

// Write Page (WritePage.js)
// Component mount → fetchChapterContent → 
// backend/app/api/chapter_endpoints.py:get_chapter_content → 
// backend/app/db/chapter_repository.py:get_chapter_content
// Auto-save with debounce

// AI Integration
// AI request → generateMemoryBlob → 
// backend/app/api/memory_endpoints.py:generate_memory_blob → 
// Combines data from all repositories

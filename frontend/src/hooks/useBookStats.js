// frontend/src/hooks/useBookStats.js
import { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { fetchAllChapters } from '../redux/slices/plotSlice';
import { fetchAllCharacters } from '../redux/slices/charactersSlice';

/**
 * Custom hook to calculate book statistics based on Redux state
 * @param {string} bookId - The ID of the book to get statistics for
 * @returns {Object} Book statistics
 */
export const useBookStats = (bookId) => {
  const dispatch = useDispatch();
  const [stats, setStats] = useState({
    wordCount: 0,
    chapterCount: 0,
    characterCount: 0,
    locationCount: 0,
    lastEdited: 'Never'
  });
  const [isCalculating, setIsCalculating] = useState(false);

  // Get data from Redux store
  const chapters = useSelector(state => state.plot.chapters);
  const chaptersLoading = useSelector(state => state.plot.isLoading);
  const characters = useSelector(state => state.characters.characters);
  const charactersLoading = useSelector(state => state.characters.isLoading);
  const worldElements = useSelector(state => state.worldBuilding.elements);
  const worldElementsLoading = useSelector(state => state.worldBuilding.isLoading);

  // Fetch chapters and characters when bookId changes
  useEffect(() => {
    if (!bookId) return;

    console.log('useBookStats: Fetching data for book', bookId);
    dispatch(fetchAllChapters(bookId));
    dispatch(fetchAllCharacters(bookId));
  }, [bookId, dispatch]);

  // Calculate stats whenever the data changes
  useEffect(() => {
    if (!bookId) return;
    if (chaptersLoading || charactersLoading || worldElementsLoading) return;

    setIsCalculating(true);
    console.log('useBookStats: Calculating stats with chapters:', chapters?.length);

    // Calculate word count directly from chapter content in Redux store
    let totalWordCount = 0;

    if (chapters && chapters.length > 0) {
      console.log('useBookStats: Calculating word count from', chapters.length, 'chapters');

      chapters.forEach(chapter => {
        const content = chapter.content;

        if (!content) {
          console.log(`useBookStats: No content found for chapter ${chapter.chapter_id || chapter.id}`);
          return;
        }

        try {
          // Try to parse as JSON (Draft.js format)
          if (typeof content === 'string' && content.trim().startsWith('{')) {
            const contentObj = JSON.parse(content);

            // Count words in each block
            if (contentObj && contentObj.blocks && Array.isArray(contentObj.blocks)) {
              let chapterWordCount = 0;

              contentObj.blocks.forEach(block => {
                if (block.text) {
                  // Simple word count by splitting on whitespace
                  const words = block.text.split(/\s+/).filter(Boolean);
                  chapterWordCount += words.length;
                }
              });

              console.log(`useBookStats: Chapter ${chapter.title} has ${chapterWordCount} words`);
              totalWordCount += chapterWordCount;
            }
          } else if (typeof content === 'string') {
            // Plain text content
            const words = content.split(/\s+/).filter(Boolean);
            console.log(`useBookStats: Chapter ${chapter.title} has ${words.length} words (plain text)`);
            totalWordCount += words.length;
          }
        } catch (error) {
          console.error(`useBookStats: Error counting words in chapter ${chapter.title}:`, error);
          // Try one more time with a simple split
          if (typeof content === 'string') {
            const words = content.split(/\s+/).filter(Boolean);
            console.log(`useBookStats: Chapter ${chapter.title} has ${words.length} words (fallback)`);
            totalWordCount += words.length;
          }
        }
      });

      console.log('useBookStats: Total word count:', totalWordCount);
    } else {
      console.log('useBookStats: No chapters available for word count calculation');
    }

    // Get chapter count
    const chapterCount = chapters ? chapters.length : 0;

    // Get character count
    const characterCount = characters ? characters.length : 0;

    // Get world element count
    const locationCount = worldElements ? Object.keys(worldElements).length : 0;

    // Get last edited date
    let lastEdited = 'Never';
    if (chapters && chapters.length > 0) {
      const dates = chapters
        .filter(chapter => chapter.updated_at)
        .map(chapter => new Date(chapter.updated_at));

      if (dates.length > 0) {
        const mostRecent = new Date(Math.max(...dates));
        lastEdited = mostRecent.toLocaleDateString();
      }
    }

    // Update stats
    const newStats = {
      wordCount: totalWordCount,
      chapterCount,
      characterCount,
      locationCount,
      lastEdited
    };

    console.log('useBookStats: Final stats:', newStats);
    setStats(newStats);
    setIsCalculating(false);
  }, [bookId, chapters, characters, worldElements, chaptersLoading, charactersLoading, worldElementsLoading]);

  return { ...stats, isLoading: isCalculating || chaptersLoading || charactersLoading || worldElementsLoading };
};

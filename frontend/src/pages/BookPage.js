// Inside the BookPage component
const BookPage = () => {
  const { bookId } = useParams();
  const { 
    selectedBook, 
    setSelectedBook, 
    chapters, 
    currentChapter, 
    setCurrentChapter,
    plotData,
    fetchBookData 
  } = useBookContext();
  const [activeTab, setActiveTab] = useState('write');
  const [selectedEvent, setSelectedEvent] = useState(null);

  // Fetch book data when component mounts or bookId changes
  useEffect(() => {
    if (bookId) {
      fetchBookData(bookId);
    }
  }, [bookId, fetchBookData]);

  // Set the first chapter as current if none is selected
  useEffect(() => {
    if (chapters && chapters.length > 0 && !currentChapter) {
      console.debug('Setting first chapter as current:', chapters[0]);
      setCurrentChapter(chapters[0]);
    }
  }, [chapters, currentChapter, setCurrentChapter]);

  // Log current state for debugging
  useEffect(() => {
    console.debug('BookPage state:', { 
      bookId, 
      selectedBook: selectedBook?.id, 
      chaptersCount: chapters?.length,
      currentChapter: currentChapter?.id,
      plotEvents: plotData?.bank?.length
    });
  }, [bookId, selectedBook, chapters, currentChapter, plotData]);

  // Render the appropriate tab content
  const renderTabContent = () => {
    switch (activeTab) {
      case 'write':
        return (
          <WritePage 
            currentChapter={currentChapter}
            plotData={plotData}
            selectedEvent={selectedEvent}
            setSelectedEvent={setSelectedEvent}
          />
        );
      // Other cases...
    }
  };
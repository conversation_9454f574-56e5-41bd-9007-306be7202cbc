{"name": "frontend", "version": "0.1.0", "private": true, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^7.0.2", "@dnd-kit/utilities": "^3.2.2", "@redux-devtools/extension": "^3.3.0", "@reduxjs/toolkit": "^2.6.1", "@sentry/react": "^9.8.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^14.2.1", "@testing-library/react-hooks": "^8.0.1", "@testing-library/user-event": "^13.5.0", "@types/react-beautiful-dnd": "^13.1.8", "axios": "^1.9.0", "bootstrap": "^5.3.5", "draft-js": "^0.11.7", "jest-fetch-mock": "^3.0.3", "konva": "^9.3.20", "lodash": "^4.17.21", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-bootstrap": "^2.10.9", "react-dom": "^18.2.0", "react-konva": "^18.2.10", "react-redux": "^9.2.0", "react-router-dom": "^6.22.3", "react-scripts": "5.0.1", "reactflow": "^11.11.4", "redux": "^5.0.1", "redux-devtools-extension": "^2.13.9", "redux-mock-store": "^1.5.5", "redux-thunk": "^3.1.0", "three": "^0.162.0", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}
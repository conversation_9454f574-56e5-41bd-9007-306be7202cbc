INFO:     127.0.0.1:54136 - "DELETE /books/772efdb4-fa04-446f-a61d-66b3911941b6/brainstorm/cards/node_64f25a64fac842a98f87cc46a0dcc312 HTTP/1.1" 200 OK
DEBUG:app.api.brainstorm_endpoints:Checking possible page_id formats: [\"772efdb4-fa04-446f-a61d-66b3911941b6_page1\", \"page_772efdb4-fa04-446f-a61d-66b3911941b6_1\", \"page_772efdb4_fa04_446f_a61d_66b3911941b6_1\"]
DEBUG:app.api.brainstorm_endpoints:Found existing page_id in database: page_772efdb4-fa04-446f-a61d-66b3911941b6_1
DEBUG:app.api.brainstorm_endpoints:Updating node node_64f25a64fac842a98f87cc46a0dcc312 of type event at position (859, -543)
DEBUG:app.api.brainstorm_endpoints:Created new card: node_64f25a64fac842a98f87cc46a0dcc312
DEBUG:app.api.brainstorm_endpoints:Successfully updated brainstorm card node_64f25a64fac842a98f87cc46a0dcc312 for book 772efdb4-fa04-446f-a61d-66b3911941b6
INFO:     127.0.0.1:54144 - "PUT /books/772efdb4-fa04-446f-a61d-66b3911941b6/brainstorm/cards/node_64f25a64fac842a98f87cc46a0dcc312 HTTP/1.1" 200 OK
INFO:     127.0.0.1:54136 - "GET /books/772efdb4-fa04-446f-a61d-66b3911941b6/world HTTP/1.1" 200 OK
INFO:     127.0.0.1:54144 - "GET /books/772efdb4-fa04-446f-a61d-66b3911941b6/characters HTTP/1.1" 200 OK
INFO:     127.0.0.1:54160 - "GET /books/772efdb4-fa04-446f-a61d-66b3911941b6/world HTTP/1.1" 200 OK
INFO:     127.0.0.1:54144 - "GET /books/772efdb4-fa04-446f-a61d-66b3911941b6/characters HTTP/1.1" 200 OK
DEBUG:app.api.brainstorm_endpoints:Checking possible page_id formats: [\"772efdb4-fa04-446f-a61d-66b3911941b6_page1\", \"page_772efdb4-fa04-446f-a61d-66b3911941b6_1\", \"page_772efdb4_fa04_446f_a61d_66b3911941b6_1\"]
DEBUG:app.api.brainstorm_endpoints:Found existing page_id in database: page_772efdb4-fa04-446f-a61d-66b3911941b6_1
DEBUG:app.api.brainstorm_endpoints:Updating node node_64f25a64fac842a98f87cc46a0dcc312 of type event at position (859, -543)
DEBUG:app.api.brainstorm_endpoints:Successfully deleted brainstorm card node_64f25a64fac842a98f87cc46a0dcc312 for book 772efdb4-fa04-446f-a61d-66b3911941b6
DEBUG:app.api.brainstorm_endpoints:Updated existing card: node_64f25a64fac842a98f87cc46a0dcc312
DEBUG:app.api.brainstorm_endpoints:Successfully updated brainstorm card node_64f25a64fac842a98f87cc46a0dcc312 for book 772efdb4-fa04-446f-a61d-66b3911941b6
INFO:     127.0.0.1:54136 - "DELETE /books/772efdb4-fa04-446f-a61d-66b3911941b6/brainstorm/cards/node_64f25a64fac842a98f87cc46a0dcc312 HTTP/1.1" 200 OK
INFO:     127.0.0.1:54144 - "PUT /books/772efdb4-fa04-446f-a61d-66b3911941b6/brainstorm/cards/node_64f25a64fac842a98f87cc46a0dcc312 HTTP/1.1" 200 OK
INFO:     127.0.0.1:54144 - "GET /books/772efdb4-fa04-446f-a61d-66b3911941b6/world HTTP/1.1" 200 OK
INFO:     127.0.0.1:54160 - "GET /books/772efdb4-fa04-446f-a61d-66b3911941b6/characters HTTP/1.1" 200 OK
INFO:     127.0.0.1:54160 - "GET /books/772efdb4-fa04-446f-a61d-66b3911941b6/characters HTTP/1.1" 200 OK
INFO:     127.0.0.1:54144 - "GET /books/772efdb4-fa04-446f-a61d-66b3911941b6/world HTTP/1.1" 200 OK
DEBUG:app.api.brainstorm_endpoints:Successfully deleted brainstorm card node_64f25a64fac842a98f87cc46a0dcc312 for book 772efdb4-fa04-446f-a61d-66b3911941b6
INFO:     127.0.0.1:44374 - "DELETE /books/772efdb4-fa04-446f-a61d-66b3911941b6/brainstorm/cards/node_64f25a64fac842a98f87cc46a0dcc312 HTTP/1.1" 200 OK
DEBUG:app.api.brainstorm_endpoints:Checking possible page_id formats: [\"772efdb4-fa04-446f-a61d-66b3911941b6_page1\", \"page_772efdb4-fa04-446f-a61d-66b3911941b6_1\", \"page_772efdb4_fa04_446f_a61d_66b3911941b6_1\"]
DEBUG:app.api.brainstorm_endpoints:Found existing page_id in database: page_772efdb4-fa04-446f-a61d-66b3911941b6_1
DEBUG:app.api.brainstorm_endpoints:Updating node node_64f25a64fac842a98f87cc46a0dcc312 of type event at position (859, -543)
DEBUG:app.api.brainstorm_endpoints:Created new card: node_64f25a64fac842a98f87cc46a0dcc312
DEBUG:app.api.brainstorm_endpoints:Successfully updated brainstorm card node_64f25a64fac842a98f87cc46a0dcc312 for book 772efdb4-fa04-446f-a61d-66b3911941b6
INFO:     127.0.0.1:44368 - "PUT /books/772efdb4-fa04-446f-a61d-66b3911941b6/brainstorm/cards/node_64f25a64fac842a98f87cc46a0dcc312 HTTP/1.1" 200 OK
INFO:     127.0.0.1:44368 - "GET /books/772efdb4-fa04-446f-a61d-66b3911941b6/world HTTP/1.1" 200 OK
INFO:     127.0.0.1:44374 - "GET /books/772efdb4-fa04-446f-a61d-66b3911941b6/characters HTTP/1.1" 200 OK

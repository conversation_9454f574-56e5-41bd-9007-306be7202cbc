# Unused Files in Project1

This file lists all files that were not being used in the application and have been moved to the old_files directory.

## Files Moved to old_files Directory

### Frontend Files
- frontend/src/old_files/* -> old_files/frontend/src/old_files/
- frontend/src/components/PlotPageRedux.js -> old_files/frontend/src/components/
- frontend/src/containers/BrainstormPageReduxContainer.js.bak -> old_files/frontend/src/containers/
- frontend/src/redux/ReduxProvider.js -> old_files/frontend/src/redux/

### Backend Files
- backend/app/db/migrations/migrate_from_book_memory.py -> old_files/backend/app/db/migrations/
- backend/app/db/migrations/drop_book_memory.py -> old_files/backend/app/db/migrations/
- backend/app/db/migrations/drop_book_memory_with_kill.py -> old_files/backend/app/db/migrations/
- backend/app/db/migrations/remove_book_memory.py -> old_files/backend/app/db/migrations/
- backend/app/db/migrations/remove_book_memory_table.py -> old_files/backend/app/db/migrations/
- backend/app/api/book_endpoints.py -> old_files/backend/app/api/
- backend/add_column.py -> old_files/backend/
- backend/add_columns_directly.py -> old_files/backend/
- backend/add_columns_script.py -> old_files/backend/
- backend/add_columns.sql -> old_files/backend/
- backend/check_db_schema.py -> old_files/backend/
- backend/check_schema.py -> old_files/backend/
- backend/reset_user_preferences.py -> old_files/backend/
- backend/run_migration_sudo.sh -> old_files/backend/
- backend/run_migration.sh -> old_files/backend/
- backend/update_importance_column.py -> old_files/backend/
- backend/update_schema.py -> old_files/backend/

### Documentation Files
- MovingForward.txt -> old_files/
- PROJECT_STATUS.md -> old_files/
- REDUX_STATUS.md -> old_files/
- STATE_MAP.md -> old_files/
- possibleRevisionsToWorldPage.txt -> old_files/
- redux_implementation_plan.md -> old_files/
- removedStates.md -> old_files/
- state_diagram.md -> old_files/
- state_management_breakdown.md -> old_files/
- state_management_diagrams.md -> old_files/
- state_management_summary.md -> old_files/
- state_migration_summary.md -> old_files/

### Directories
- _Reduxprep/* -> old_files/_Reduxprep/
- Testapps/* -> old_files/Testapps/

Note: These files have been preserved in the old_files directory for reference purposes but are no longer part of the active codebase.